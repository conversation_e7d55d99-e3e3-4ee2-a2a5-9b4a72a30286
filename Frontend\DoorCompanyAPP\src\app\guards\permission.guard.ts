import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const permissionGuard: CanActivateFn = (route, state) => {

  const authService = inject(AuthService);
  const router = inject(Router);
   
  const requiredPermissions = route.data['permissions'] as string[];
  if (!requiredPermissions) {
    return true;
  }

  const userPermissions = authService.viewUserPermissions().permissions;
  const hasPermission = requiredPermissions.some(p => userPermissions.includes(p)); 

  if (!hasPermission) {
    router.navigate(['/unauthorized']);
    return false;
  }


  return true;
};
