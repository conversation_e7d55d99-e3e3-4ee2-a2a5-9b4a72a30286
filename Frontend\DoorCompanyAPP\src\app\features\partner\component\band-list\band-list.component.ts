import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { PartnerBand } from '../../../../models/partner';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { PartnerService } from '../../../../services/partner.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { PartnerBandDialogComponent } from '../partner-band-dialog/partner-band-dialog.component';
import { PartnerDialogComponent } from '../partner-dialog/partner-dialog.component';

@Component({
  selector: 'app-band-list',
  standalone: false,
  templateUrl: './band-list.component.html',
  styleUrl: './band-list.component.css'
})
export class BandListComponent implements   OnInit ,AfterViewInit {
partnerBands: PartnerBand[] = [];
 dataSource: MatTableDataSource<PartnerBand>;
 loading = false;

  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = ['name', 'description', 'actions'];

  constructor(
    private partnerService: PartnerService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.dataSource = new MatTableDataSource(this.partnerBands);
   }


  ngAfterViewInit(): void {
    this.dataSource.sort = this.sort;
  }

  ngOnInit(): void {
    this.loadPartnerBands();
  }

  loadPartnerBands(): void {
    this.loading = true;
    this.partnerService.getPartnerBands().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.partnerBands = response.data;
          this.dataSource.data = this.partnerBands;
        }
        this.loading = false;
      },
      error: (error) => {
        this.loading = false;
      }
    });
  }


createPartnerBand(): void {

    const dialogRef = this.dialog.open(PartnerBandDialogComponent, {
      width: '600px',
      data: { mode: 'create' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadPartnerBands();
        this.snackBar.open('تم إنشاء بند بنجاح', 'إغلاق', { duration: 3000 });
      }
    });
  }

 editPartnerBand(partnerBand: PartnerBand): void {
    const dialogRef = this.dialog.open(PartnerBandDialogComponent, {
      width: '600px',
      data: { mode: 'edit', partnerBand }
    });   
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadPartnerBands();
        this.snackBar.open('تم تحديث بند بنجاح', 'إغلاق', { duration: 3000 });
      }
    });
  }

 deletePartnerband(partnerBand: PartnerBand): void {
    if (confirm(`هل أنت متأكد من حذف بند "${partnerBand.name}"؟`)) {
      this.partnerService.deletePartnerBand(partnerBand.id).subscribe({
        next: (response) => {
          if (response.succeeded && response.data != null) {
            this.loadPartnerBands();
            this.snackBar.open('تم حذف بند بنجاح', 'إغلاق', { duration: 3000 });
          }
        },
        error: (error) => {
         
          this.snackBar.open('حدث خطأ أثناء حذف بند', 'إغلاق', { duration: 3000 });
        }
      });
    }
  }




}
