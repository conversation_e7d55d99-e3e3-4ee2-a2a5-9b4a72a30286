import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { Partner } from '../../../../models/partner';
import { PartnerService } from '../../../../services/partner.service';
import { PartnerDialogComponent } from '../partner-dialog/partner-dialog.component';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
@Component({
  selector: 'app-partner-list',
  standalone: false,
  templateUrl: './partner-list.component.html',
  styleUrl: './partner-list.component.css'
})
export class PartnerListComponent implements  OnInit ,AfterViewInit  {
 Partners: Partner[] = [];
 dataSource: MatTableDataSource<Partner>;
 loading = false;

  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = ['name', 'description', 'initialCapital', 'createdAt', 'isDeleted','actions'];

  constructor(
    private partnerService: PartnerService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.dataSource = new MatTableDataSource(this.Partners);
   }


  ngAfterViewInit(): void {
    this.dataSource.sort = this.sort;
  }

  ngOnInit(): void {
    this.loadPartners();
  }

  loadPartners(): void {
    this.loading = true;
    this.partnerService.getPartners(true).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.Partners = response.data;
          this.dataSource.data = this.Partners;
        }
        this.loading = false;
      },
      error: (error) => {
        this.loading = false;
      }
    });
  }

createPartner(): void {

    const dialogRef = this.dialog.open(PartnerDialogComponent, {
      width: '600px',
      data: { mode: 'create' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadPartners();
        this.snackBar.open('تم إنشاء الشريك بنجاح', 'إغلاق', { duration: 3000 });
      }
    });
  }

 editPartner(Partner: Partner): void {
    const dialogRef = this.dialog.open(PartnerDialogComponent, {
      width: '600px',
      data: { mode: 'edit', Partner }   
    });   
        dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadPartners();
        this.snackBar.open('تم تحديث الشريك بنجاح', 'إغلاق', { duration: 3000 });
      }
    });
  }

 deletePartner(Partner: Partner): void {
    if (confirm(`هل أنت متأكد من حذف الشريك "${Partner.name}"؟`)) {
      this.partnerService.deletePartner(Partner.id).subscribe({
        next: (response) => {
          if (response.succeeded && response.data != null) {
            this.loadPartners();
            this.snackBar.open('تم حذف الشريك بنجاح', 'إغلاق', { duration: 3000 });
          }
        },
        error: (error) => {
         
          this.snackBar.open('حدث خطأ أثناء حذف الشريك', 'إغلاق', { duration: 3000 });
        }
      });
    }
  }

 }


