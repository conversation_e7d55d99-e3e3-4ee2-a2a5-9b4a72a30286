<div class="profile-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>جاري تحميل البيانات...</p>
  </div>

  <!-- Profile Content -->
  <div *ngIf="!isLoading && currentUser" class="profile-content">
    
    <!-- Header -->
    <div class="profile-header">
      <mat-icon class="header-icon">account_circle</mat-icon>
      <h1>الملف الشخصي</h1>
      <p>إدارة بيانات حسابك الشخصي</p>
    </div>

    <div class="profile-grid">
      
      <!-- Profile Image Section -->
      <mat-card class="image-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>photo_camera</mat-icon>
            الصورة الشخصية
          </mat-card-title>
        </mat-card-header>
          <!--Image Profile -->
        <mat-card-content>
          <div class="image-section">
            <div class="image-container">
              <img [src]="getProfileImageUrl()" 
                   alt="الصورة الشخصية" 
                   class="profile-image"
                   [class.default-image]="!hasProfileImage()">
              
              <div class="image-overlay" *ngIf="hasProfileImage()">
                <button mat-icon-button 
                        color="warn" 
                        (click)="deleteImage()"
                        matTooltip="حذف الصورة">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </div>

            <!-- File Upload -->
            <div class="upload-section" *ngIf="!selectedFile">
              <input type="file" 
                     #fileInput 
                     (change)="onFileSelected($event)"
                     accept="image/*"
                     style="display: none;">
              
              <button mat-raised-button 
                      color="primary" 
                      (click)="fileInput.click()"
                      class="upload-btn">
                <mat-icon>cloud_upload</mat-icon>
                اختيار صورة
              </button>
              
              <p class="upload-hint">
                الحد الأقصى: 5 ميجابايت<br>
                الأنواع المدعومة: JPG, PNG, GIF
              </p>
            </div>

            <!-- Image Preview and Upload -->
            <div class="preview-section" *ngIf="selectedFile">
              <div class="preview-container">
                <img [src]="imagePreview" alt="معاينة الصورة" class="preview-image">
              </div>
              
              <div class="preview-actions">
                <button mat-raised-button 
                        color="primary" 
                        (click)="uploadImage()"
                        [disabled]="isUploadingImage">
                  <mat-icon *ngIf="!isUploadingImage">save</mat-icon>
                  <mat-spinner *ngIf="isUploadingImage" diameter="20"></mat-spinner>
                  {{ isUploadingImage ? 'جاري الرفع...' : 'حفظ الصورة' }}
                </button>
                
                <button mat-stroked-button 
                        (click)="cancelImageUpload()"
                        [disabled]="isUploadingImage">
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Profile Information -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>person</mat-icon>
            البيانات الشخصية
          </mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="profileForm" (ngSubmit)="updateProfile()">
             <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>الاسم الكامل</mat-label>
                <input matInput formControlName="fullName" placeholder="أدخل الاسم الكامل">
                <mat-icon matSuffix>person</mat-icon>
                <mat-error>{{ getErrorMessage('fullName') }}</mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>رقم الهاتف</mat-label>
                <input matInput formControlName="phone"  placeholder="أدخل رقم الهاتف">
                <mat-icon matSuffix>phone</mat-icon>
                <mat-error>{{ getErrorMessage('phone') }}</mat-error>
              </mat-form-field>
            </div>

          



            <!-- Read-only fields -->
            <div class="readonly-section">
              <div class="readonly-field" *ngIf="currentUser.createdAt">
                <label>تاريخ الإنشاء:</label>
                <span>{{ currentUser.createdAt | date:'dd/MM/yyyy' }}</span>
              </div>
            </div>

            <div class="form-actions">
              <button mat-raised-button 
                      color="primary" 
                      type="submit"
                      [disabled]="profileForm.invalid || isUpdatingProfile">
                <mat-icon *ngIf="!isUpdatingProfile">save</mat-icon>
                <mat-spinner *ngIf="isUpdatingProfile" diameter="20"></mat-spinner>
                {{ isUpdatingProfile ? 'جاري الحفظ...' : 'حفظ التغييرات' }}
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Password Change Section -->
      <mat-card class="password-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>lock</mat-icon>
            تغيير كلمة المرور
          </mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div *ngIf="!showPasswordForm">
            <p class="password-description">
              لحماية حسابك، يُنصح بتغيير كلمة المرور بانتظام
            </p>
            <button mat-raised-button 
                    color="accent" 
                    (click)="togglePasswordForm()">
              <mat-icon>edit</mat-icon>
              تغيير كلمة المرور
            </button>
          </div>

          <form *ngIf="showPasswordForm" 
                [formGroup]="passwordForm" 
                (ngSubmit)="changePassword()">
            
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>كلمة المرور الحالية</mat-label>
                <input matInput 
                       formControlName="currentPassword" 
                       type="password"
                       placeholder="أدخل كلمة المرور الحالية">
                <mat-icon matSuffix>lock</mat-icon>
                <mat-error>{{ getErrorMessage('currentPassword', passwordForm) }}</mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>كلمة المرور الجديدة</mat-label>
                <input matInput 
                       formControlName="newPassword" 
                       type="password"
                       placeholder="أدخل كلمة المرور الجديدة">
                <mat-icon matSuffix>lock_outline</mat-icon>
                <mat-error>{{ getErrorMessage('newPassword', passwordForm) }}</mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>تأكيد كلمة المرور</mat-label>
                <input matInput 
                       formControlName="confirmPassword" 
                       type="password"
                       placeholder="أعد إدخال كلمة المرور الجديدة">
                <mat-icon matSuffix>lock_outline</mat-icon>
                <mat-error>{{ getErrorMessage('confirmPassword', passwordForm) }}</mat-error>
              </mat-form-field>
            </div>

            <div class="form-actions">
              <button mat-raised-button 
                      color="primary" 
                      type="submit"
                      [disabled]="passwordForm.invalid || isChangingPassword">
                <mat-icon *ngIf="!isChangingPassword">save</mat-icon>
                <mat-spinner *ngIf="isChangingPassword" diameter="20"></mat-spinner>
                {{ isChangingPassword ? 'جاري التغيير...' : 'تغيير كلمة المرور' }}
              </button>
              
              <button mat-stroked-button 
                      type="button"
                      (click)="togglePasswordForm()"
                      [disabled]="isChangingPassword">
                إلغاء
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

    </div>
  </div>
</div>

