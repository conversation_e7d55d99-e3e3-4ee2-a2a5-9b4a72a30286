﻿using DoorCompany.Service.Dtos.BasicDto;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos.PartnerDto
{
    public class ShareTransferResponseDto : BaseDto
    {
        public DateTime TransfersDate { get; set; }
        public int BuyerId { get; set; }
        public int SellerId { get; set; }
        public string BuyerName { get; set; } = string.Empty;
        public string SellerName { get; set; } = string.Empty;
        public int SharesCount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TransferAmount { get; set; }
        public string? Description { get; set; }
    }
    public class ShareTransferRequestDto
    {
        public int? partnerId { get; set; }
        public DateTime? fromDate { get; set; }
        public DateTime? toDate { get; set; }
    }

    public class CreateShareTransferDto : BaseCreateDto
    {
        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransfersDate { get; set; }

        [Required(ErrorMessage = "من فضلك اختار الشريك المشترى")]
        public int BuyerId { get; set; }

        [Required(ErrorMessage = "من  فضلك اختار الشريك البائع")]
        public int SellerId { get; set; }

        [Required(ErrorMessage = "عدد الاسهم مطلوب")]
        [Range(1, int.MaxValue, ErrorMessage = "عدد الاسهم يجب أن يكون أكبر من صفر")]
        public int SharesCount { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TransferAmount { get; set; }
        public string? Description { get; set; } = null;
    } 
    
    public class UpdateShareTransferDto : BaseUpdateDto
    {
        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransfersDate { get; set; }

        [Required(ErrorMessage = "من فضلك اختار الشريك المشترى")]
        public int BuyerId { get; set; }

        [Required(ErrorMessage = "من  فضلك اختار الشريك البائع")]
        public int SellerId { get; set; }

        [Required(ErrorMessage = "عدد الاسهم مطلوب")]
        [Range(1, int.MaxValue, ErrorMessage = "عدد الاسهم يجب أن يكون أكبر من صفر")]
        public int SharesCount { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TransferAmount { get; set; }
        public string? Description { get; set; } = null;
    }

}
