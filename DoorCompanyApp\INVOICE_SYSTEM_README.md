# نظام إدارة الفواتير والمخزون والحسابات المالية

## نظرة عامة
تم تطوير نظام شامل لإدارة الفواتير مع ربطه بالمخزون والحسابات المالية والخزينة. النظام يدعم فواتير البيع والشراء مع تتبع كامل للمخزون والمعاملات المالية.

## المميزات الرئيسية

### 1. إدارة الفواتير
- إنشاء فواتير البيع والشراء
- تعديل وحذف الفواتير
- تسديد الفواتير (كامل أو جزئي)
- إلغاء التسديد
- تقارير المبيعات والمشتريات
- ترقيم تلقائي للفواتير

### 2. إدارة المخزون
- تتبع حركات المخزون تلقائياً مع الفواتير
- حساب متوسط التكلفة
- تقارير المخزون
- تنبيهات الحد الأدنى والأقصى للمخزون
- التحقق من توفر الكميات قبل البيع

### 3. إدارة الحسابات المالية
- تسجيل المعاملات المالية تلقائياً
- إدارة الخزينة (نقدي وبنكي)
- حسابات العملاء والموردين
- تقارير الخزينة
- التحويل بين الحسابات

## البنية التقنية

### الخدمات (Services)
1. **InvoiceService**: إدارة الفواتير
2. **InventoryService**: إدارة المخزون
3. **FinancialService**: إدارة الحسابات المالية

### الكنترولرز (Controllers)
1. **InvoiceController**: API endpoints للفواتير
2. **InventoryController**: API endpoints للمخزون
3. **FinancialController**: API endpoints للحسابات المالية

### نماذج البيانات الرئيسية
- **InvoiceMaster**: الفاتورة الرئيسية
- **InvoiceItem**: أصناف الفاتورة
- **InventoryTransaction**: حركات المخزون
- **ProductInventory**: أرصدة المنتجات
- **FinancialTransaction**: المعاملات المالية

## API Endpoints

### الفواتير
```
POST   /api/Invoice                    - إنشاء فاتورة جديدة
PUT    /api/Invoice/{id}               - تحديث فاتورة
DELETE /api/Invoice/{id}               - حذف فاتورة
GET    /api/Invoice/{id}               - الحصول على فاتورة
GET    /api/Invoice                    - قائمة الفواتير مع التصفية
POST   /api/Invoice/{id}/pay           - تسديد فاتورة
POST   /api/Invoice/{id}/cancel-payment - إلغاء تسديد
GET    /api/Invoice/report             - تقرير الفواتير
GET    /api/Invoice/customer/{id}      - فواتير عميل
GET    /api/Invoice/supplier/{id}      - فواتير مورد
```

### المخزون
```
GET    /api/Inventory/product/{id}     - رصيد منتج
GET    /api/Inventory/report           - تقرير المخزون
GET    /api/Inventory/transactions     - حركات المخزون
GET    /api/Inventory/low-stock        - منتجات منخفضة المخزون
GET    /api/Inventory/over-stock       - منتجات مرتفعة المخزون
POST   /api/Inventory/check-availability - التحقق من توفر المنتجات
```

### الحسابات المالية
```
POST   /api/Financial/transactions     - إضافة معاملة مالية
GET    /api/Financial/transactions     - قائمة المعاملات
GET    /api/Financial/cash-balance     - رصيد الخزينة
GET    /api/Financial/cash-report      - تقرير الخزينة
POST   /api/Financial/supplier-payment - سداد لمورد
POST   /api/Financial/customer-receipt - تحصيل من عميل
GET    /api/Financial/customers-balances - أرصدة العملاء
GET    /api/Financial/suppliers-balances - أرصدة الموردين
```

## سير العمل

### إنشاء فاتورة بيع
1. التحقق من توفر المنتجات
2. إنشاء الفاتورة وأصنافها
3. تحديث المخزون (خروج)
4. تسجيل المعاملة المالية (إذا كان هناك دفع)

### إنشاء فاتورة شراء
1. إنشاء الفاتورة وأصنافها
2. تحديث المخزون (دخول)
3. تسجيل المعاملة المالية (إذا كان هناك دفع)

### تسديد فاتورة
1. تحديث بيانات الفاتورة
2. تسجيل المعاملة المالية في الخزينة

## المعاملات والأمان
- استخدام Database Transactions لضمان تماسك البيانات
- التحقق من صحة البيانات قبل المعالجة
- معالجة الأخطاء والاستثناءات
- Rollback تلقائي في حالة الفشل

## التكامل
النظام مدمج بالكامل مع:
- نظام إدارة المنتجات الموجود
- نظام إدارة العملاء والموردين
- نظام الصلاحيات والمستخدمين

## الاستخدام
1. تأكد من تسجيل الخدمات في DI Container
2. قم بتشغيل Migration لإنشاء الجداول
3. استخدم API Endpoints للتفاعل مع النظام
4. راجع التقارير لمتابعة الأداء

## ملاحظات مهمة
- يتم حساب متوسط التكلفة تلقائياً
- الفواتير مرقمة تلقائياً حسب النوع والتاريخ
- جميع المعاملات المالية مسجلة ومؤرخة
- يمكن إلغاء الفواتير مع عكس تأثيرها على المخزون والحسابات
