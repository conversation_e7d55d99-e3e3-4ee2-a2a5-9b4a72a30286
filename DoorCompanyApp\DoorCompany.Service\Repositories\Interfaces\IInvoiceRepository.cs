using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.ProductDto;

namespace DoorCompany.Service.Repositories.Interfaces
{
    public interface IInvoiceRepository
    {
        // إنشاء فاتورة جديدة
        Task<ApiResponse<InvoiceResponseDto>> CreateInvoiceAsync(CreateInvoiceMasterDto createDto);
        
        // تحديث فاتورة موجودة
        Task<ApiResponse<InvoiceResponseDto>> UpdateInvoiceAsync(int invoiceId, UpdateInvoiceMasterDto updateDto);
        
        // حذف فاتورة
        Task<ApiResponse<bool>> DeleteInvoiceAsync(int invoiceId);
        
        // الحصول على فاتورة بالمعرف
        Task<ApiResponse<InvoiceResponseDto>> GetInvoiceByIdAsync(int invoiceId);
        
        // الحصول على قائمة الفواتير مع التصفية والترقيم
        Task<ApiResponse<PagedResponse<InvoiceResponseDto>>> GetInvoicesAsync(InvoiceFilterDto filterDto);
        
        // الحصول على فواتير عميل معين
        Task<ApiResponse<PagedResponse<InvoiceResponseDto>>> GetCustomerInvoicesAsync(int customerId, InvoiceFilterDto filterDto);
        
        // الحصول على فواتير مورد معين
        Task<ApiResponse<PagedResponse<InvoiceResponseDto>>> GetSupplierInvoicesAsync(int supplierId, InvoiceFilterDto filterDto);
        
        // تسديد فاتورة (كامل أو جزئي)
        Task<ApiResponse<InvoiceResponseDto>> PayInvoiceAsync(int invoiceId, PayInvoiceDto paymentDto);
        
        // إلغاء تسديد فاتورة
        Task<ApiResponse<InvoiceResponseDto>> CancelPaymentAsync(int invoiceId);
        
        // الحصول على تقرير مبيعات/مشتريات
        Task<ApiResponse<InvoiceReportDto>> GetInvoiceReportAsync(InvoiceReportFilterDto filterDto);
        
        // الحصول على حساب عميل
        Task<ApiResponse<CustomerAccountDto>> GetCustomerAccountAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null);
        
        // الحصول على حساب مورد
        Task<ApiResponse<SupplierAccountDto>> GetSupplierAccountAsync(int supplierId, DateTime? fromDate = null, DateTime? toDate = null);
    }
}
