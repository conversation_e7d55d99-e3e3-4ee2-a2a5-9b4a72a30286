import{a as h}from"./chunk-PAYZX34A.js";import{ga as f}from"./chunk-5FTQYYYZ.js";import{Ba as p,Ca as r,O as i,Rb as l,Ta as c,Ua as d,hc as m,kb as u,wa as a}from"./chunk-KFKFGDWN.js";var s=class e{constructor(t,o){this.userService=t;this.authService=o}premissions=[];ngOnInit(){this.getUserPermissions(),console.log("User Permissions:",this.premissions)}date=new Date;expiredToken=null;getExparedToken(){return localStorage.getItem("tokenExpiry")}getUserPermissions(){return this.premissions=this.authService.viewUserPermissions().permissions.map(t=>t).sort()}hasPermission(t){return this.authService.hasPermission(t)}static \u0275fac=function(o){return new(o||e)(a(h),a(f))};static \u0275cmp=p({type:e,selectors:[["app-dashboard"]],standalone:!1,decls:2,vars:0,template:function(o,D){o&1&&(c(0,"p"),u(1,"dashboard works!"),d())},encapsulation:2})};var S=[{path:"",component:s}],n=class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=r({type:e});static \u0275inj=i({imports:[m.forChild(S),m]})};var v=class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=r({type:e});static \u0275inj=i({imports:[l,n]})};export{v as DashboardModule};
