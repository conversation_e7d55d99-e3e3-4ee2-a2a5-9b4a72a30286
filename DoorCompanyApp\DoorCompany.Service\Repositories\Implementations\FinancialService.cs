using AutoMapper;
using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.FinancialDto;
using DoorCompany.Service.Dtos.ProductDto;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.EntityFrameworkCore;

namespace DoorCompany.Service.Repositories.Implementations
{
    public class FinancialService :ResponseHandler, IFinancialRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;

        public FinancialService(IUnitOfWorkOfService unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<ApiResponse<FinancialTransactionDto>> AddFinancialTransactionAsync(CreateFinancialTransactionDto createDto)
        {
            try
            {
                var transaction = _mapper.Map<FinancialTransaction>(createDto);
                await _unitOfWork.FinancialTransactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                var result = _mapper.Map<FinancialTransactionDto>(transaction);
                return Success(result, "تم إضافة المعاملة المالية بنجاح");
            }
            catch (Exception ex)
            {
                return BadRequest<FinancialTransactionDto>($"خطأ في إضافة المعاملة المالية: {ex.Message}");
            }
        }

        public async Task<ApiResponse<FinancialTransactionDto>> UpdateFinancialTransactionAsync(int transactionId, UpdateFinancialTransactionDto updateDto)
        {
            try
            {
                var transaction = await _unitOfWork.FinancialTransactions.GetByIdAsync(transactionId);
                if (transaction == null)
                    return NotFound<FinancialTransactionDto>("المعاملة المالية غير موجودة");

                _mapper.Map(updateDto, transaction);
            await    _unitOfWork.FinancialTransactions.UpdateAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                var result = _mapper.Map<FinancialTransactionDto>(transaction);
                return Success(result, "تم تحديث المعاملة المالية بنجاح");
            }
            catch (Exception ex)
            {
                return BadRequest<FinancialTransactionDto>($"خطأ في تحديث المعاملة المالية: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> DeleteFinancialTransactionAsync(int transactionId)
        {
            try
            {
                var transaction = await _unitOfWork.FinancialTransactions.GetByIdAsync(transactionId);
                if (transaction == null)
                    return NotFound<bool>("المعاملة المالية غير موجودة");

            await    _unitOfWork.FinancialTransactions.DeleteAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم حذف المعاملة المالية بنجاح");
            }
            catch (Exception ex)
            {
                return BadRequest<bool>($"خطأ في حذف المعاملة المالية: {ex.Message}");
            }
        }

        public async Task<ApiResponse<FinancialTransactionDto>> GetFinancialTransactionAsync(int transactionId)
        {
            try
            {
                var transaction = await _unitOfWork.FinancialTransactions.GetByIdAsync(transactionId);
                if (transaction == null)
                    return NotFound<FinancialTransactionDto>("المعاملة المالية غير موجودة");

                var result = _mapper.Map<FinancialTransactionDto>(transaction);
                return Success(result);
            }
            catch (Exception ex)
            {
                return BadRequest<FinancialTransactionDto>($"خطأ في الحصول على المعاملة المالية: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResponse<FinancialTransactionDto>>> GetFinancialTransactionsAsync(FinancialFilterDto filterDto)
        {
            try
            {
                var query = _unitOfWork.FinancialTransactions.GetTableNoTracking();

                if (filterDto.TransactionType.HasValue)
                    query = query.Where(t => t.TransactionType == filterDto.TransactionType.Value);

                if (filterDto.AccountType.HasValue)
                    query = query.Where(t => t.AccountType == filterDto.AccountType.Value);

                if (filterDto.ReferenceType.HasValue)
                    query = query.Where(t => t.ReferenceType == filterDto.ReferenceType.Value);

                if (filterDto.FromDate.HasValue)
                    query = query.Where(t => t.TransactionDate >= filterDto.FromDate.Value);

                if (filterDto.ToDate.HasValue)
                    query = query.Where(t => t.TransactionDate <= filterDto.ToDate.Value);

                if (filterDto.MinAmount.HasValue)
                    query = query.Where(t => t.Amount >= filterDto.MinAmount.Value);

                if (filterDto.MaxAmount.HasValue)
                    query = query.Where(t => t.Amount <= filterDto.MaxAmount.Value);

                if (!string.IsNullOrEmpty(filterDto.SearchTerm))
                    query = query.Where(t => t.Description != null && t.Description.Contains(filterDto.SearchTerm));

                query = query.OrderByDescending(t => t.TransactionDate);

                var totalCount = await query.CountAsync();
                var items = await query
                    .Skip((filterDto.PageNumber - 1) * filterDto.PageSize)
                    .Take(filterDto.PageSize)
                    .Select(t => new FinancialTransactionDto
                    {
                        Id = t.Id,
                        TransactionDate = t.TransactionDate,
                        TransactionType = t.TransactionType,
                        TransactionTypeName = t.TransactionType.ToString(),
                        AccountType = t.AccountType,
                        AccountTypeName = t.AccountType.ToString(),
                        Amount = t.Amount,
                        ReferenceId = t.ReferenceId,
                        ReferenceType = t.ReferenceType,
                        ReferenceTypeName = t.ReferenceType.ToString(),
                        Description = t.Description
                    })
                    .ToListAsync();
              
                  var data = new PagedResponse<FinancialTransactionDto> { Items = items,PageNumber =  filterDto.PageNumber,PageSize = filterDto.PageSize,TotalCount = totalCount };
               
                  return Success(data);
            }
            catch (Exception ex)
            {
                return NotFound<PagedResponse<FinancialTransactionDto>>($"خطأ في الحصول على المعاملات المالية: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> RecordInvoicePaymentAsync(int invoiceId, decimal amount, PaymentType paymentType, string? notes = null)
        {
            try
            {
                var invoice = await _unitOfWork.InvoiceMasters.GetByIdAsync(invoiceId);
                if (invoice == null)
                    return NotFound<bool>("الفاتورة غير موجودة");

                var accountType = paymentType == PaymentType.Cash ? AccountType.Cash : AccountType.Bank;
                var transactionType = invoice.InvoiceType == InvoiceType.Sale ? TransactionType.Income : TransactionType.Expense;
                var referenceType = invoice.InvoiceType == InvoiceType.Sale ? ReferenceType.Sale : ReferenceType.Purchase;

                var transaction = new FinancialTransaction
                {
                    TransactionDate = DateTime.Now,
                    TransactionType = transactionType,
                    AccountType = accountType,
                    Amount = amount,
                    ReferenceId = invoiceId,
                    ReferenceType = referenceType,
                    Description = notes ?? $"تسديد فاتورة رقم {invoice.InvoiceNumber}"
                };

                await _unitOfWork.FinancialTransactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم تسجيل الدفع في الخزينة بنجاح");
            }
            catch (Exception ex)
            {
                return BadRequest<bool>($"خطأ في تسجيل الدفع: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> RecordSupplierPaymentAsync(int supplierId, decimal amount, PaymentType paymentType, string? notes = null)
        {
            try
            {
                var supplier = await _unitOfWork.Suppliers
                    .GetTableNoTracking()
                    .Include(s => s.Party)
                    .FirstOrDefaultAsync(s => s.Id == supplierId);

                if (supplier == null)
                    return NotFound<bool>("المورد غير موجود");

                var accountType = paymentType == PaymentType.Cash ? AccountType.Cash : AccountType.Bank;

                var transaction = new FinancialTransaction
                {
                    TransactionDate = DateTime.Now,
                    TransactionType = TransactionType.Expense,
                    AccountType = accountType,
                    Amount = amount,
                    ReferenceId = supplierId,
                    ReferenceType = ReferenceType.PaySupplier,
                    Description = notes ?? $"سداد للمورد {supplier.Party.Name}"
                };

                await _unitOfWork.FinancialTransactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم تسجيل السداد للمورد بنجاح");
            }
            catch (Exception ex)
            {
                return BadRequest<bool>($"خطأ في تسجيل السداد للمورد: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> RecordCustomerReceiptAsync(int customerId, decimal amount, PaymentType paymentType, string? notes = null)
        {
            try
            {
                var customer = await _unitOfWork.Customers
                    .GetTableNoTracking()
                    .Include(c => c.Party)
                    .FirstOrDefaultAsync(c => c.Id == customerId);

                if (customer == null)
                    return NotFound<bool>("العميل غير موجود");

                var accountType = paymentType == PaymentType.Cash ? AccountType.Cash : AccountType.Bank;

                var transaction = new FinancialTransaction
                {
                    TransactionDate = DateTime.Now,
                    TransactionType = TransactionType.Income,
                    AccountType = accountType,
                    Amount = amount,
                    ReferenceId = customerId,
                    ReferenceType = ReferenceType.ReceiveFromCustomer,
                    Description = notes ?? $"تحصيل من العميل {customer.Party.Name}"
                };

                await _unitOfWork.FinancialTransactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم تسجيل التحصيل من العميل بنجاح");
            }
            catch (Exception ex)
            {
                return BadRequest<bool>($"خطأ في تسجيل التحصيل من العميل: {ex.Message}");
            }
        }

        public async Task<ApiResponse<CashBalanceDto>> GetCashBalanceAsync()
        {
            try
            {
                var cashTransactions = await _unitOfWork.FinancialTransactions
                    .GetTableNoTracking()
                    .Where(t => t.AccountType == AccountType.Cash)
                    .ToListAsync();

                var bankTransactions = await _unitOfWork.FinancialTransactions
                    .GetTableNoTracking()
                    .Where(t => t.AccountType == AccountType.Bank)
                    .ToListAsync();

                var cashBalance = cashTransactions
                    .Sum(t => t.TransactionType == TransactionType.Income ? t.Amount : -t.Amount);

                var bankBalance = bankTransactions
                    .Sum(t => t.TransactionType == TransactionType.Income ? t.Amount : -t.Amount);

                var result = new CashBalanceDto
                {
                    CashBalance = cashBalance,
                    BankBalance = bankBalance,
                    TotalBalance = cashBalance + bankBalance,
                    LastUpdateDate = DateTime.Now
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                return BadRequest<CashBalanceDto>($"خطأ في الحصول على رصيد الخزينة: {ex.Message}");
            }
        }

        public async Task<ApiResponse<CashReportDto>> GetCashReportAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                fromDate ??= DateTime.Today.AddDays(-30);
                toDate ??= DateTime.Today;

                var transactions = await _unitOfWork.FinancialTransactions
                    .GetTableNoTracking()
                    .Where(t => t.TransactionDate >= fromDate && t.TransactionDate <= toDate)
                    .OrderBy(t => t.TransactionDate)
                    .ToListAsync();

                var openingTransactions = await _unitOfWork.FinancialTransactions
                    .GetTableNoTracking()
                    .Where(t => t.TransactionDate < fromDate)
                    .ToListAsync();

                var openingBalance = openingTransactions
                    .Sum(t => t.TransactionType == TransactionType.Income ? t.Amount : -t.Amount);

                var totalIncome = transactions
                    .Where(t => t.TransactionType == TransactionType.Income)
                    .Sum(t => t.Amount);

                var totalExpense = transactions
                    .Where(t => t.TransactionType == TransactionType.Expense)
                    .Sum(t => t.Amount);

                var netChange = totalIncome - totalExpense;
                var closingBalance = openingBalance + netChange;

                var transactionDtos = transactions.Select(t => new FinancialTransactionDto
                {
                    Id = t.Id,
                    TransactionDate = t.TransactionDate,
                    TransactionType = t.TransactionType,
                    TransactionTypeName = t.TransactionType.ToString(),
                    AccountType = t.AccountType,
                    AccountTypeName = t.AccountType.ToString(),
                    Amount = t.Amount,
                    ReferenceId = t.ReferenceId,
                    ReferenceType = t.ReferenceType,
                    ReferenceTypeName = t.ReferenceType.ToString(),
                    Description = t.Description
                }).ToList();

                var result = new CashReportDto
                {
                    OpeningBalance = openingBalance,
                    TotalIncome = totalIncome,
                    TotalExpense = totalExpense,
                    NetChange = netChange,
                    ClosingBalance = closingBalance,
                    Transactions = transactionDtos
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                return BadRequest<CashReportDto>($"خطأ في الحصول على تقرير الخزينة: {ex.Message}");
            }
        }

        public async Task<ApiResponse<CustomerAccountDto>> GetCustomerAccountAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var customer = await _unitOfWork.Customers
                    .GetTableNoTracking()
                    .Include(c => c.Party)
                    .FirstOrDefaultAsync(c => c.Id == customerId);

                if (customer == null)
                    return NotFound<CustomerAccountDto>("العميل غير موجود");

                fromDate ??= DateTime.Today.AddYears(-1);
                toDate ??= DateTime.Today;

                // فواتير العميل
                var invoices = await _unitOfWork.InvoiceMasters
                    .GetTableNoTracking()
                    .Where(i => i.Party != null && i.Party.Customer != null && i.Party.Customer.Id == customerId &&
                               i.InvoiceType == InvoiceType.Sale &&
                               i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                    .ToListAsync();

                // المدفوعات من العميل
                var payments = await _unitOfWork.FinancialTransactions
                    .GetTableNoTracking()
                    .Where(t => t.ReferenceType == ReferenceType.ReceiveFromCustomer &&
                               t.ReferenceId == customerId &&
                               t.TransactionDate >= fromDate && t.TransactionDate <= toDate)
                    .ToListAsync();

                var totalSales = invoices.Sum(i => i.TotalAmount);
                var totalPaid = payments.Sum(p => p.Amount);
                var totalRemaining = totalSales - totalPaid;

                var transactions = new List<CustomerTransactionDto>();

                // إضافة الفواتير
                foreach (var invoice in invoices)
                {
                    transactions.Add(new CustomerTransactionDto
                    {
                        Id = invoice.Id,
                        Date = invoice.InvoiceDate,
                        Type = "فاتورة بيع",
                        ReferenceNumber = invoice.InvoiceNumber,
                        Debit = invoice.TotalAmount,
                        Credit = 0,
                        Balance = 0, // سيتم حسابه لاحقاً
                        Notes = invoice.Notes
                    });
                }

                // إضافة المدفوعات
                foreach (var payment in payments)
                {
                    transactions.Add(new CustomerTransactionDto
                    {
                        Id = payment.Id,
                        Date = payment.TransactionDate,
                        Type = "تحصيل",
                        ReferenceNumber = payment.Id.ToString(),
                        Debit = 0,
                        Credit = payment.Amount,
                        Balance = 0, // سيتم حسابه لاحقاً
                        Notes = payment.Description
                    });
                }

                // ترتيب المعاملات وحساب الرصيد
                transactions = transactions.OrderBy(t => t.Date).ToList();
                decimal runningBalance = 0;
                foreach (var transaction in transactions)
                {
                    runningBalance += transaction.Debit - transaction.Credit;
                    transaction.Balance = runningBalance;
                }

                var result = new CustomerAccountDto
                {
                    CustomerId = customerId,
                    CustomerName = customer.Party.Name,
                    TotalSales = totalSales,
                    TotalPaid = totalPaid,
                    TotalRemaining = totalRemaining,
                    CreditLimit = customer.CreditLimit,
                    Transactions = transactions
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                return BadRequest<CustomerAccountDto>($"خطأ في الحصول على حساب العميل: {ex.Message}");
            }
        }

        public async Task<ApiResponse<SupplierAccountDto>> GetSupplierAccountAsync(int supplierId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var supplier = await _unitOfWork.Suppliers
                    .GetTableNoTracking()
                    .Include(s => s.Party)
                    .FirstOrDefaultAsync(s => s.Id == supplierId);

                if (supplier == null)
                    return NotFound<SupplierAccountDto>("المورد غير موجود");

                fromDate ??= DateTime.Today.AddYears(-1);
                toDate ??= DateTime.Today;

                // فواتير المورد
                var invoices = await _unitOfWork.InvoiceMasters
                    .GetTableNoTracking()
                    .Where(i => i.Party != null && i.Party.Supplier != null && i.Party.Supplier.Id == supplierId &&
                               i.InvoiceType == InvoiceType.Purchase &&
                               i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                    .ToListAsync();

                // المدفوعات للمورد
                var payments = await _unitOfWork.FinancialTransactions
                    .GetTableNoTracking()
                    .Where(t => t.ReferenceType == ReferenceType.PaySupplier &&
                               t.ReferenceId == supplierId &&
                               t.TransactionDate >= fromDate && t.TransactionDate <= toDate)
                    .ToListAsync();

                var totalPurchases = invoices.Sum(i => i.TotalAmount);
                var totalPaid = payments.Sum(p => p.Amount);
                var totalRemaining = totalPurchases - totalPaid;

                var transactions = new List<SupplierTransactionDto>();

                // إضافة الفواتير
                foreach (var invoice in invoices)
                {
                    transactions.Add(new SupplierTransactionDto
                    {
                        Id = invoice.Id,
                        Date = invoice.InvoiceDate,
                        Type = "فاتورة شراء",
                        ReferenceNumber = invoice.InvoiceNumber,
                        Debit = invoice.TotalAmount,
                        Credit = 0,
                        Balance = 0, // سيتم حسابه لاحقاً
                        Notes = invoice.Notes
                    });
                }

                // إضافة المدفوعات
                foreach (var payment in payments)
                {
                    transactions.Add(new SupplierTransactionDto
                    {
                        Id = payment.Id,
                        Date = payment.TransactionDate,
                        Type = "سداد",
                        ReferenceNumber = payment.Id.ToString(),
                        Debit = 0,
                        Credit = payment.Amount,
                        Balance = 0, // سيتم حسابه لاحقاً
                        Notes = payment.Description
                    });
                }

                // ترتيب المعاملات وحساب الرصيد
                transactions = transactions.OrderBy(t => t.Date).ToList();
                decimal runningBalance = 0;
                foreach (var transaction in transactions)
                {
                    runningBalance += transaction.Debit - transaction.Credit;
                    transaction.Balance = runningBalance;
                }

                var result = new SupplierAccountDto
                {
                    SupplierId = supplierId,
                    SupplierName = supplier.Party.Name,
                    TotalPurchases = totalPurchases,
                    TotalPaid = totalPaid,
                    TotalRemaining = totalRemaining,
                    Transactions = transactions
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                return BadRequest<SupplierAccountDto>($"خطأ في الحصول على حساب المورد: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResponse<CustomerBalanceDto>>> GetCustomersBalancesAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var customers = await _unitOfWork.Customers
                    .GetTableNoTracking()
                    .Include(c => c.Party)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var totalCount = await _unitOfWork.Customers.GetTableNoTracking().CountAsync();

                var result = new List<CustomerBalanceDto>();

                foreach (var customer in customers)
                {
                    var invoices = await _unitOfWork.InvoiceMasters
                        .GetTableNoTracking()
                        .Where(i => i.Party != null && i.Party.Customer != null && i.Party.Customer.Id == customer.Id && i.InvoiceType == InvoiceType.Sale)
                        .ToListAsync();

                    var payments = await _unitOfWork.FinancialTransactions
                        .GetTableNoTracking()
                        .Where(t => t.ReferenceType == ReferenceType.ReceiveFromCustomer && t.ReferenceId == customer.Id)
                        .ToListAsync();

                    var totalSales = invoices.Sum(i => i.TotalAmount);
                    var totalPaid = payments.Sum(p => p.Amount);
                    var balance = totalSales - totalPaid;

                    result.Add(new CustomerBalanceDto
                    {
                        CustomerId = customer.Id,
                        CustomerName = customer.Party.Name,
                        Phone = customer.Party.Phone,
                        TotalSales = totalSales,
                        TotalPaid = totalPaid,
                        Balance = balance,
                        CreditLimit = customer.CreditLimit,
                        AvailableCredit = customer.CreditLimit - balance
                    });
                }
   
                  var data = new PagedResponse<CustomerBalanceDto> { Items = result,PageNumber = pageNumber,PageSize = pageSize,TotalCount = totalCount };
               
                  return Success(data);
             //   return new PagedResponse<CustomerBalanceDto>(result, pageNumber, pageSize, totalCount);
            }
            catch (Exception ex)
            {
                return NotFound<PagedResponse<CustomerBalanceDto>>($"خطأ في الحصول على أرصدة العملاء: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResponse<SupplierBalanceDto>>> GetSuppliersBalancesAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var suppliers = await _unitOfWork.Suppliers
                    .GetTableNoTracking()
                    .Include(s => s.Party)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var totalCount = await _unitOfWork.Suppliers.GetTableNoTracking().CountAsync();

                var result = new List<SupplierBalanceDto>();

                foreach (var supplier in suppliers)
                {
                    var invoices = await _unitOfWork.InvoiceMasters
                        .GetTableNoTracking()
                        .Where(i => i.Party != null && i.Party.Supplier != null && i.Party.Supplier.Id == supplier.Id && i.InvoiceType == InvoiceType.Purchase)
                        .ToListAsync();

                    var payments = await _unitOfWork.FinancialTransactions
                        .GetTableNoTracking()
                        .Where(t => t.ReferenceType == ReferenceType.PaySupplier && t.ReferenceId == supplier.Id)
                        .ToListAsync();

                    var totalPurchases = invoices.Sum(i => i.TotalAmount);
                    var totalPaid = payments.Sum(p => p.Amount);
                    var balance = totalPurchases - totalPaid;

                    result.Add(new SupplierBalanceDto
                    {
                        SupplierId = supplier.Id,
                        SupplierName = supplier.Party.Name,
                        Phone = supplier.Party.Phone,
                        TotalPurchases = totalPurchases,
                        TotalPaid = totalPaid,
                        Balance = balance
                    });
                }
 var data = new PagedResponse<SupplierBalanceDto> { Items = result,PageNumber =  pageNumber,PageSize = pageSize,TotalCount = totalCount };
               
                  return Success(data);
               // return new PagedResponse<SupplierBalanceDto>(result, pageNumber, pageSize, totalCount);
            }
            catch (Exception ex)
            {
                return NotFound<PagedResponse<SupplierBalanceDto>>($"خطأ في الحصول على أرصدة الموردين: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> TransferBetweenAccountsAsync(TransferDto transferDto)
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // معاملة الخروج من الحساب الأول
                var outTransaction = new FinancialTransaction
                {
                    TransactionDate = transferDto.TransactionDate,
                    TransactionType = TransactionType.Expense,
                    AccountType = transferDto.FromAccount,
                    Amount = transferDto.Amount,
                    ReferenceType = ReferenceType.None,
                    Description = $"تحويل إلى {transferDto.ToAccount} - {transferDto.Description}"
                };

                // معاملة الدخول للحساب الثاني
                var inTransaction = new FinancialTransaction
                {
                    TransactionDate = transferDto.TransactionDate,
                    TransactionType = TransactionType.Income,
                    AccountType = transferDto.ToAccount,
                    Amount = transferDto.Amount,
                    ReferenceType = ReferenceType.None,
                    Description = $"تحويل من {transferDto.FromAccount} - {transferDto.Description}"
                };

                await _unitOfWork.FinancialTransactions.AddAsync(outTransaction);
                await _unitOfWork.FinancialTransactions.AddAsync(inTransaction);

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return Success(true, "تم التحويل بين الحسابات بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest<bool>($"خطأ في التحويل بين الحسابات: {ex.Message}");
            }
        }
    }
}
