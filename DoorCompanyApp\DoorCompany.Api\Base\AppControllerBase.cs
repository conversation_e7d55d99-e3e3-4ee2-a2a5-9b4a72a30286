﻿using Azure;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace DoorCompany.Api.Base
{
    [ApiController]
    public class AppControllerBase(IUnitOfWork work) : ControllerBase
    {
        protected readonly IUnitOfWork _work = work;
        
        #region Actions
        public ObjectResult NewResult<T>(ApiResponse<T> response)
        {
            return response.StatusCode switch
            {
                HttpStatusCode.OK => new OkObjectResult(response),
                HttpStatusCode.Created => new CreatedResult(string.Empty, response),
                HttpStatusCode.Unauthorized => new UnauthorizedObjectResult(response),
                HttpStatusCode.BadRequest => new BadRequestObjectResult(response),
                HttpStatusCode.NotFound => new NotFoundObjectResult(response),
                HttpStatusCode.Accepted => new AcceptedResult(string.Empty, response),
                HttpStatusCode.UnprocessableEntity => new UnprocessableEntityObjectResult(response),
                _ => new BadRequestObjectResult(response),
            };
        }
        #endregion
    }
}
