2025-07-19 14:22:40.578 +03:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 14:22:40.695 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 14:22:40.934 +03:00 [INF] Executed DbCommand (89ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 14:22:41.046 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 14:22:41.091 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 14:22:41.105 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 14:22:41.115 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 14:22:41.399 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 14:22:41.832 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 14:22:41.972 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 14:22:41.976 +03:00 [INF] Hosting environment: Development
2025-07-19 14:22:41.985 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 14:22:42.494 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 14:22:42.876 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 393.2837ms
2025-07-19 14:22:43.257 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 14:22:43.262 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 14:22:43.265 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.8639ms
2025-07-19 14:22:43.324 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 61.4102ms
2025-07-19 14:22:43.504 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 14:22:43.527 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 23.3892ms
2025-07-19 14:23:27.562 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:23:27.585 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 14:23:28.818 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:23:28.851 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 14:23:29.164 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 14:23:29.206 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:23:29.248 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 390.5632ms
2025-07-19 14:23:29.251 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:23:29.255 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 1696.2155ms
2025-07-19 14:25:13.335 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User - application/json 129
2025-07-19 14:25:13.347 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.CreateUser (DoorCompany.Api)'
2025-07-19 14:25:13.359 +03:00 [INF] Route matched with {action = "CreateUser", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateUser(DoorCompany.Service.Dtos.UserDto.CreateUserDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 14:25:13.507 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[@__userDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[UserName] = @__userDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-07-19 14:25:13.778 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([CreatedAt], [CreatedBy], [FullName], [IsActive], [IsDeleted], [Password], [Phone], [ProfileImage], [UpdatedAt], [UpdatedBy], [UserName])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-07-19 14:25:13.815 +03:00 [INF] User created successfully: GA
2025-07-19 14:25:13.844 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 14:25:13.854 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 14:25:13.857 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.CreateUser (DoorCompany.Api) in 494.924ms
2025-07-19 14:25:13.859 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.CreateUser (DoorCompany.Api)'
2025-07-19 14:25:13.860 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User - 200 null application/json; charset=utf-8 525.6742ms
2025-07-19 14:25:32.675 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:25:32.683 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:25:32.684 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 14:25:32.713 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 14:25:32.718 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:25:32.719 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 32.9199ms
2025-07-19 14:25:32.721 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:25:32.722 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 46.5515ms
2025-07-19 14:25:54.753 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 47
2025-07-19 14:25:54.767 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 14:25:54.773 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 14:25:54.798 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-19 14:25:54.840 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-19 14:25:55.043 +03:00 [INF] Access token generated for user 2
2025-07-19 14:25:55.092 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-19 14:25:55.099 +03:00 [INF] User logged in successfully: GA
2025-07-19 14:25:55.100 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 14:25:55.115 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 340.01ms
2025-07-19 14:25:55.118 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 14:25:55.119 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 365.735ms
2025-07-19 14:27:21.682 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User/change-password - application/json 111
2025-07-19 14:27:21.759 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.ChangePassword (DoorCompany.Api)'
2025-07-19 14:27:21.764 +03:00 [INF] Route matched with {action = "ChangePassword", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ChangePassword(DoorCompany.Service.Dtos.UserDto.ChangePasswordDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 14:27:21.785 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 14:27:22.000 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p9='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (DbType = Boolean), @p2='?' (DbType = Boolean), @p3='?' (Size = 4000), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FullName] = @p0, [IsActive] = @p1, [IsDeleted] = @p2, [Password] = @p3, [Phone] = @p4, [ProfileImage] = @p5, [UpdatedAt] = @p6, [UpdatedBy] = @p7, [UserName] = @p8
OUTPUT 1
WHERE [Id] = @p9;
2025-07-19 14:27:22.033 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId]
FROM [RefreshTokens] AS [r]
WHERE [r].[UserId] = @__userId_0 AND [r].[IsRevoked] = CAST(0 AS bit)
2025-07-19 14:27:22.053 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p12='?' (DbType = Int32), @p0='?' (DbType = DateTime2), @p1='?' (Size = 4000), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime2), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000), @p11='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p0, [IpAddress] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [IsRevoked] = @p4, [RevokedAt] = @p5, [RevokedReason] = @p6, [Token] = @p7, [UpdatedAt] = @p8, [UpdatedBy] = @p9, [UserAgent] = @p10, [UserId] = @p11
OUTPUT 1
WHERE [Id] = @p12;
2025-07-19 14:27:22.058 +03:00 [INF] All refresh tokens revoked for user 2
2025-07-19 14:27:22.059 +03:00 [INF] Password changed successfully for user: 2
2025-07-19 14:27:22.061 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:27:22.064 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.ChangePassword (DoorCompany.Api) in 297.214ms
2025-07-19 14:27:22.067 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.ChangePassword (DoorCompany.Api)'
2025-07-19 14:27:22.068 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User/change-password - 200 null application/json; charset=utf-8 386.5609ms
2025-07-19 14:28:19.419 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:28:19.440 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:28:19.442 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 14:28:19.470 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 14:28:19.474 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:28:19.475 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 27.2217ms
2025-07-19 14:28:19.477 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:28:19.478 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 58.9112ms
2025-07-19 14:29:16.487 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/logout - null 0
2025-07-19 14:29:16.498 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-19 14:29:16.504 +03:00 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 14:29:16.521 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId]
FROM [RefreshTokens] AS [r]
WHERE [r].[UserId] = @__userId_0 AND [r].[IsRevoked] = CAST(0 AS bit)
2025-07-19 14:29:16.523 +03:00 [INF] All refresh tokens revoked for user 2
2025-07-19 14:29:16.524 +03:00 [INF] User logged out successfully: 2
2025-07-19 14:29:16.525 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:29:16.526 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api) in 19.4698ms
2025-07-19 14:29:16.528 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-19 14:29:16.529 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/logout - 200 null application/json; charset=utf-8 42.2503ms
2025-07-19 14:30:18.426 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 14:30:18.499 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 14:30:18.646 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 14:30:18.738 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 14:30:18.779 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 14:30:18.792 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 14:30:18.800 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 14:30:18.937 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 14:30:19.107 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 14:30:19.265 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 14:30:19.266 +03:00 [INF] Hosting environment: Development
2025-07-19 14:30:19.268 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 14:30:19.673 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 14:30:19.901 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 237.4108ms
2025-07-19 14:30:19.932 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 14:30:19.937 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 14:30:19.962 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 29.9685ms
2025-07-19 14:30:19.993 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 55.9675ms
2025-07-19 14:30:20.150 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 14:30:20.165 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 15.7404ms
2025-07-19 14:30:27.333 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 14:30:27.345 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 12.7821ms
2025-07-19 14:30:27.390 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 14:30:27.390 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 14:30:27.393 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 3.3744ms
2025-07-19 14:30:27.414 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 23.2286ms
2025-07-19 14:30:27.579 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 14:30:27.601 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 22.0402ms
2025-07-19 14:30:33.015 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:30:33.026 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 14:30:33.105 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-19 14:30:33.112 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-19 14:30:33.113 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 401 0 null 98.4994ms
2025-07-19 14:30:36.125 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:30:36.133 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-19 14:30:36.135 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-19 14:30:36.137 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 401 0 null 12.5728ms
2025-07-19 14:30:56.410 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:30:56.497 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:30:56.518 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 14:30:56.752 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 14:30:56.795 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:30:56.830 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 304.3455ms
2025-07-19 14:30:56.832 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:30:56.836 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 425.7413ms
2025-07-19 14:31:10.630 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:31:10.636 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:31:10.638 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 14:31:10.726 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 14:31:10.732 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:31:10.733 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 92.9106ms
2025-07-19 14:31:10.735 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:31:10.736 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 106.0307ms
2025-07-19 14:31:23.463 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:31:23.467 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-19 14:31:23.468 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-19 14:31:23.469 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 401 0 null 6.2164ms
2025-07-19 14:31:42.018 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:31:42.032 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 11:31:42 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-19 14:31:42.099 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 11:31:42 ص'.
2025-07-19 14:31:42.101 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 11:31:42 ص'.
2025-07-19 14:31:42.102 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-19 14:31:42.105 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-19 14:31:42.107 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 401 0 null 89.0028ms
2025-07-19 14:31:58.602 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:31:58.617 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:31:58.619 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 14:31:58.630 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 14:31:58.644 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:31:58.646 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 25.4046ms
2025-07-19 14:31:58.647 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:31:58.649 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 47.4129ms
2025-07-19 14:32:17.368 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/logout - null 0
2025-07-19 14:32:17.373 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-19 14:32:17.379 +03:00 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 14:32:17.417 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId]
FROM [RefreshTokens] AS [r]
WHERE [r].[UserId] = @__userId_0 AND [r].[IsRevoked] = CAST(0 AS bit)
2025-07-19 14:32:17.436 +03:00 [INF] All refresh tokens revoked for user 2
2025-07-19 14:32:17.438 +03:00 [INF] User logged out successfully: 2
2025-07-19 14:32:17.440 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:32:17.449 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api) in 67.4599ms
2025-07-19 14:32:17.452 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-19 14:32:17.453 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/logout - 200 null application/json; charset=utf-8 84.9947ms
2025-07-19 14:32:19.227 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/logout - null 0
2025-07-19 14:32:19.242 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-19 14:32:19.244 +03:00 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 14:32:19.251 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId]
FROM [RefreshTokens] AS [r]
WHERE [r].[UserId] = @__userId_0 AND [r].[IsRevoked] = CAST(0 AS bit)
2025-07-19 14:32:19.254 +03:00 [INF] All refresh tokens revoked for user 2
2025-07-19 14:32:19.255 +03:00 [INF] User logged out successfully: 2
2025-07-19 14:32:19.256 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:32:19.257 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api) in 9.3355ms
2025-07-19 14:32:19.259 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-19 14:32:19.261 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/logout - 200 null application/json; charset=utf-8 33.1999ms
2025-07-19 14:32:27.172 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-19 14:32:27.186 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:32:27.191 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 14:32:27.206 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 14:32:27.210 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:32:27.212 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 18.6299ms
2025-07-19 14:32:27.213 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-19 14:32:27.215 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 42.5655ms
2025-07-19 14:33:11.218 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/logout - null 0
2025-07-19 14:33:11.229 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-19 14:33:11.230 +03:00 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 14:33:55.277 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId]
FROM [RefreshTokens] AS [r]
WHERE [r].[UserId] = @__userId_0 AND [r].[IsRevoked] = CAST(0 AS bit)
2025-07-19 14:34:04.318 +03:00 [INF] All refresh tokens revoked for user 2
2025-07-19 14:34:06.993 +03:00 [INF] User logged out successfully: 2
2025-07-19 14:34:10.044 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:34:10.046 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api) in 58813.8541ms
2025-07-19 14:34:10.048 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-19 14:34:10.050 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/logout - 200 null application/json; charset=utf-8 58833.2254ms
2025-07-19 14:36:02.508 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - application/json 112
2025-07-19 14:36:02.518 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-07-19 14:36:02.527 +03:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] RefreshToken(DoorCompany.Service.Dtos.UserDto.RefreshTokenDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 14:36:02.592 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-19 14:36:02.655 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@__refreshTokenDto_RefreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshTokenDto_RefreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-07-19 14:36:02.658 +03:00 [WRN] Invalid refresh token used
2025-07-19 14:36:02.660 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 14:36:02.666 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api) in 135.7583ms
2025-07-19 14:36:02.668 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-07-19 14:36:02.670 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - 401 null application/json; charset=utf-8 170.4838ms
2025-07-19 14:36:19.354 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/validate-token - null 0
2025-07-19 14:36:19.368 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.ValidateToken (DoorCompany.Api)'
2025-07-19 14:36:19.373 +03:00 [INF] Route matched with {action = "ValidateToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ValidateToken() on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 14:36:19.424 +03:00 [WRN] Token validation failed
Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException: IDX12709: CanReadToken() returned false. JWT is not well formed.
The token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EncodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'.
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ReadJwtToken(String token)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.GetJwtSecurityTokenFromToken(String token, TokenValidationParameters validationParameters)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateJWS(String token, TokenValidationParameters validationParameters, BaseConfiguration currentConfiguration, SecurityToken& signatureValidatedToken, ExceptionDispatchInfo& exceptionThrown)
--- End of stack trace from previous location ---
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateToken(String token, JwtSecurityToken outerToken, TokenValidationParameters validationParameters, SecurityToken& signatureValidatedToken)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateToken(String token, TokenValidationParameters validationParameters, SecurityToken& validatedToken)
   at DoorCompany.Service.Authentication.JwtTokenService.ValidateTokenAsync(String token) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Authentication\JwtTokenService.cs:line 101
2025-07-19 14:36:19.463 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 14:36:19.465 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.ValidateToken (DoorCompany.Api) in 89.4614ms
2025-07-19 14:36:19.466 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.ValidateToken (DoorCompany.Api)'
2025-07-19 14:36:19.467 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/validate-token - 200 null application/json; charset=utf-8 113.119ms
2025-07-19 14:36:47.540 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 47
2025-07-19 14:36:47.545 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 14:36:47.550 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 14:36:47.569 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-19 14:36:47.610 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-19 14:36:47.719 +03:00 [WRN] Login attempt with invalid password for user: GA
2025-07-19 14:36:47.720 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 14:36:47.722 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 168.5674ms
2025-07-19 14:36:47.723 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 14:36:47.724 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 401 null application/json; charset=utf-8 184.6502ms
2025-07-19 14:36:53.322 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 48
2025-07-19 14:36:53.327 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 14:36:53.329 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 14:36:53.337 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-19 14:36:53.460 +03:00 [INF] Access token generated for user 2
2025-07-19 14:36:53.616 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-19 14:36:53.650 +03:00 [INF] User logged in successfully: GA
2025-07-19 14:36:53.651 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 14:36:53.656 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 323.9991ms
2025-07-19 14:36:53.657 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 14:36:53.658 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 336.9208ms
2025-07-19 15:11:49.776 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:11:49.853 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:11:49.999 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 15:11:50.088 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:11:50.127 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 15:11:50.138 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:11:50.147 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:11:50.321 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 15:11:50.497 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 15:11:50.678 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 15:11:50.680 +03:00 [INF] Hosting environment: Development
2025-07-19 15:11:50.682 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 15:11:50.719 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 15:11:50.980 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 272.3289ms
2025-07-19 15:11:51.018 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 15:11:51.018 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 15:11:51.027 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 11.7579ms
2025-07-19 15:11:51.060 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.5761ms
2025-07-19 15:11:51.242 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 15:11:51.262 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.848ms
2025-07-19 15:11:59.261 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-19 15:11:59.271 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 15:11:59.347 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 15:11:59.375 +03:00 [INF] Route matched with {action = "GetUserRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 15:11:59.637 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 15:11:59.661 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:11:59.679 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 15:11:59.703 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api) in 319.9628ms
2025-07-19 15:11:59.705 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 15:11:59.710 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 200 null application/json; charset=utf-8 449.2ms
2025-07-19 15:12:12.701 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-19 15:12:12.706 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 15:12:12.708 +03:00 [INF] Route matched with {action = "GetUserRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 15:12:12.783 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 15:12:12.787 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:12:12.789 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 15:12:12.791 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api) in 79.8531ms
2025-07-19 15:12:12.793 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 15:12:12.794 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 200 null application/json; charset=utf-8 93.7855ms
2025-07-19 15:13:10.988 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-19 15:13:10.994 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 15:13:10.995 +03:00 [INF] Route matched with {action = "GetUserRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 15:13:11.010 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 15:13:11.016 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:13:11.018 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 15:13:11.020 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api) in 21.9211ms
2025-07-19 15:13:11.021 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 15:13:11.022 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 200 null application/json; charset=utf-8 34.6512ms
2025-07-19 15:16:25.059 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:16:25.141 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:16:25.295 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 15:16:25.385 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:16:25.426 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 15:16:25.437 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:16:25.444 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:16:25.601 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 15:16:25.774 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 15:16:25.996 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 15:16:25.997 +03:00 [INF] Hosting environment: Development
2025-07-19 15:16:25.998 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 15:16:26.207 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 15:16:26.431 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 234.0189ms
2025-07-19 15:16:26.448 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 15:16:26.454 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.7888ms
2025-07-19 15:16:26.466 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 15:16:26.505 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.5912ms
2025-07-19 15:16:26.675 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 15:16:26.694 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 18.7991ms
2025-07-19 15:16:34.767 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-19 15:16:34.777 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 15:16:34.851 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 15:16:34.878 +03:00 [INF] Route matched with {action = "GetUserRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 15:16:35.130 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 15:16:35.158 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:16:35.175 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 15:16:35.201 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api) in 315.3803ms
2025-07-19 15:16:35.203 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 15:16:35.207 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 200 null application/json; charset=utf-8 440.0898ms
2025-07-19 15:55:11.387 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:55:11.462 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:55:11.604 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 15:55:11.694 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:55:11.732 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 15:55:11.746 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:55:11.754 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:55:11.911 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 15:55:12.121 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 15:55:12.245 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 15:55:12.246 +03:00 [INF] Hosting environment: Development
2025-07-19 15:55:12.247 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 15:55:12.735 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 15:55:12.960 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 234.6449ms
2025-07-19 15:55:12.986 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 15:55:12.989 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 15:55:12.995 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.4514ms
2025-07-19 15:55:13.034 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 44.9079ms
2025-07-19 15:55:13.199 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 15:55:13.216 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.1417ms
2025-07-19 15:58:45.994 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:58:46.068 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:58:46.223 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 15:58:46.309 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:58:46.347 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 15:58:46.358 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:58:46.366 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 15:58:46.497 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 15:58:46.679 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 15:58:46.888 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 15:58:46.897 +03:00 [INF] Hosting environment: Development
2025-07-19 15:58:46.899 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 15:58:46.915 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 15:58:47.139 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 240.1936ms
2025-07-19 15:58:47.155 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 15:58:47.162 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.8671ms
2025-07-19 15:58:47.172 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 15:58:47.213 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 40.7308ms
2025-07-19 15:58:47.370 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 15:58:47.387 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 16.7685ms
2025-07-19 15:59:39.615 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-19 15:59:39.639 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 15:59:39.772 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 12:59:39 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-19 15:59:39.797 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 12:59:39 م'.
2025-07-19 15:59:39.804 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 12:59:39 م'.
2025-07-19 15:59:39.808 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 15:59:39.835 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 15:59:40.085 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 15:59:40.107 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:59:40.119 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 15:59:40.144 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 301.7855ms
2025-07-19 15:59:40.147 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 15:59:40.151 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 200 null application/json; charset=utf-8 535.8644ms
2025-07-19 15:59:47.014 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-19 15:59:47.021 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 12:59:47 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-19 15:59:47.024 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 12:59:47 م'.
2025-07-19 15:59:47.026 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 12:59:47 م'.
2025-07-19 15:59:47.028 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 15:59:47.030 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 15:59:47.101 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 15:59:47.105 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 15:59:47.108 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 15:59:47.110 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 77.3719ms
2025-07-19 15:59:47.112 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 15:59:47.114 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 200 null application/json; charset=utf-8 99.2687ms
2025-07-19 16:00:35.342 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - application/json 553
2025-07-19 16:00:35.352 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 01:00:35 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-19 16:00:35.356 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 01:00:35 م'.
2025-07-19 16:00:35.357 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '19/07/2025 01:00:35 م'.
2025-07-19 16:00:35.365 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-19 16:00:35.371 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-19 16:00:35.372 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - 401 0 null 30.229ms
2025-07-19 16:01:25.891 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - application/json 553
2025-07-19 16:01:25.899 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '19/07/2025 12:36:53 م', Current time (UTC): '19/07/2025 01:01:25 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-19 16:01:25.902 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '19/07/2025 12:36:53 م', Current time (UTC): '19/07/2025 01:01:25 م'.
2025-07-19 16:01:25.902 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '19/07/2025 12:36:53 م', Current time (UTC): '19/07/2025 01:01:25 م'.
2025-07-19 16:01:25.905 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-19 16:01:25.914 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-19 16:01:25.916 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - 401 0 null 26.2198ms
2025-07-19 16:01:53.248 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 52
2025-07-19 16:01:53.295 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '19/07/2025 12:36:53 م', Current time (UTC): '19/07/2025 01:01:53 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-19 16:01:53.298 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '19/07/2025 12:36:53 م', Current time (UTC): '19/07/2025 01:01:53 م'.
2025-07-19 16:01:53.300 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '19/07/2025 12:36:53 م', Current time (UTC): '19/07/2025 01:01:53 م'.
2025-07-19 16:01:53.302 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 16:01:53.309 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 16:01:53.363 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-19 16:01:53.403 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-19 16:01:53.527 +03:00 [WRN] Login attempt with invalid password for user: admin
2025-07-19 16:01:53.529 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 16:01:53.536 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 224.0876ms
2025-07-19 16:01:53.539 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 16:01:53.540 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 401 null application/json; charset=utf-8 291.8888ms
2025-07-19 16:02:03.860 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 52
2025-07-19 16:02:03.865 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '19/07/2025 12:36:53 م', Current time (UTC): '19/07/2025 01:02:03 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-19 16:02:03.869 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '19/07/2025 12:36:53 م', Current time (UTC): '19/07/2025 01:02:03 م'.
2025-07-19 16:02:03.870 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '19/07/2025 12:36:53 م', Current time (UTC): '19/07/2025 01:02:03 م'.
2025-07-19 16:02:03.871 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 16:02:03.873 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-19 16:02:03.882 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-19 16:02:04.019 +03:00 [INF] Access token generated for user 1
2025-07-19 16:02:04.185 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-19 16:02:04.214 +03:00 [INF] User logged in successfully: admin
2025-07-19 16:02:04.216 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 16:02:04.230 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 354.9253ms
2025-07-19 16:02:04.232 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-19 16:02:04.233 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 373.4304ms
2025-07-19 16:02:35.236 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - application/json 553
2025-07-19 16:02:35.262 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:02:35.267 +03:00 [INF] Route matched with {action = "AddAndRemoveUserRole", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveUserRole(DoorCompany.Service.Dtos.RoleDto.UpdateUserRolesDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:02:35.304 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__updateUserRolesDto_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__updateUserRolesDto_UserId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 16:02:35.332 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
DELETE FROM [UserRoles]
OUTPUT 1
WHERE [Id] = @p0;
2025-07-19 16:02:35.385 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [UserRoles] USING (
VALUES (@p0, @p1, 0),
(@p2, @p3, 1)) AS i ([RoleId], [UserId], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([RoleId], [UserId])
VALUES (i.[RoleId], i.[UserId])
OUTPUT INSERTED.[Id], i._Position;
2025-07-19 16:02:35.391 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 16:02:35.393 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api) in 123.5139ms
2025-07-19 16:02:35.394 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:02:35.396 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - 200 null application/json; charset=utf-8 159.9072ms
2025-07-19 16:03:19.817 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - application/json 548
2025-07-19 16:03:19.841 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:03:19.842 +03:00 [INF] Route matched with {action = "AddAndRemoveUserRole", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveUserRole(DoorCompany.Service.Dtos.RoleDto.UpdateUserRolesDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:03:19.858 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-19 16:03:19.877 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api) in 32.9576ms
2025-07-19 16:03:19.879 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:03:19.881 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - 400 null application/problem+json; charset=utf-8 63.9528ms
2025-07-19 16:03:52.420 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - application/json 549
2025-07-19 16:03:52.443 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:03:52.445 +03:00 [INF] Route matched with {action = "AddAndRemoveUserRole", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveUserRole(DoorCompany.Service.Dtos.RoleDto.UpdateUserRolesDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:03:52.451 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-19 16:03:52.453 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api) in 6.5268ms
2025-07-19 16:03:52.455 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:03:52.456 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - 400 null application/problem+json; charset=utf-8 36.1272ms
2025-07-19 16:04:11.009 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - application/json 553
2025-07-19 16:04:11.024 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:04:11.029 +03:00 [INF] Route matched with {action = "AddAndRemoveUserRole", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveUserRole(DoorCompany.Service.Dtos.RoleDto.UpdateUserRolesDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:04:11.032 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-19 16:04:11.035 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api) in 3.7803ms
2025-07-19 16:04:11.036 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:04:11.038 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - 400 null application/problem+json; charset=utf-8 29.2664ms
2025-07-19 16:04:23.846 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - application/json 553
2025-07-19 16:04:23.850 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:04:23.852 +03:00 [INF] Route matched with {action = "AddAndRemoveUserRole", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveUserRole(DoorCompany.Service.Dtos.RoleDto.UpdateUserRolesDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:04:23.865 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__updateUserRolesDto_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__updateUserRolesDto_UserId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 16:04:23.887 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
DELETE FROM [UserRoles]
OUTPUT 1
WHERE [Id] = @p0;
DELETE FROM [UserRoles]
OUTPUT 1
WHERE [Id] = @p1;
2025-07-19 16:04:23.910 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [UserRoles] USING (
VALUES (@p0, @p1, 0),
(@p2, @p3, 1)) AS i ([RoleId], [UserId], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([RoleId], [UserId])
VALUES (i.[RoleId], i.[UserId])
OUTPUT INSERTED.[Id], i._Position;
2025-07-19 16:04:23.913 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 16:04:23.914 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api) in 60.1307ms
2025-07-19 16:04:23.917 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.AddAndRemoveUserRole (DoorCompany.Api)'
2025-07-19 16:04:23.919 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User/add-Remove-user-Roles - 200 null application/json; charset=utf-8 72.2905ms
2025-07-19 16:24:51.030 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:24:51.114 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:24:51.285 +03:00 [INF] Executed DbCommand (39ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 16:24:51.373 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:24:51.412 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:24:51.423 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:24:51.430 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:24:51.645 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:24:51.882 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:24:52.048 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:24:52.093 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:24:52.094 +03:00 [INF] Hosting environment: Development
2025-07-19 16:24:52.096 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:24:52.293 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 256.9106ms
2025-07-19 16:24:52.324 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:24:52.324 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:24:52.332 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.347ms
2025-07-19 16:24:52.366 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.9237ms
2025-07-19 16:24:52.535 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:24:52.554 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.2105ms
2025-07-19 16:26:46.215 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:26:46.308 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:26:46.454 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 16:26:46.542 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:26:46.583 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:26:46.594 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:26:46.600 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:26:46.737 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:26:46.910 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:26:47.132 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:26:47.135 +03:00 [INF] Hosting environment: Development
2025-07-19 16:26:47.136 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:26:47.170 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:26:47.393 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 231.5848ms
2025-07-19 16:26:47.422 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:26:47.427 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:26:47.433 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.8137ms
2025-07-19 16:26:47.475 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 48.1059ms
2025-07-19 16:26:47.656 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:26:47.680 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 24.0599ms
2025-07-19 16:27:00.794 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - null null
2025-07-19 16:27:00.810 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 16:27:00.896 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-19 16:27:00.901 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-19 16:27:00.903 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - 401 0 null 109.3481ms
2025-07-19 16:27:20.986 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - null null
2025-07-19 16:27:21.072 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '25/06/2025 11:08:18 ص', Current time (UTC): '19/07/2025 01:27:21 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-19 16:27:21.096 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '25/06/2025 11:08:18 ص', Current time (UTC): '19/07/2025 01:27:21 م'.
2025-07-19 16:27:21.102 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '25/06/2025 11:08:18 ص', Current time (UTC): '19/07/2025 01:27:21 م'.
2025-07-19 16:27:21.105 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-19 16:27:21.108 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-19 16:27:21.110 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - 401 0 null 124.5248ms
2025-07-19 16:27:42.892 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:27:42.968 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:27:43.117 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 16:27:43.216 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:27:43.256 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:27:43.267 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:27:43.274 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:27:43.428 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:27:43.595 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:27:43.740 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:27:43.742 +03:00 [INF] Hosting environment: Development
2025-07-19 16:27:43.743 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:27:43.848 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:27:44.092 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 259.1314ms
2025-07-19 16:27:44.108 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:27:44.113 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 5.3138ms
2025-07-19 16:27:44.122 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:27:44.160 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 37.9252ms
2025-07-19 16:27:44.326 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:27:44.345 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.6618ms
2025-07-19 16:28:11.278 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - null null
2025-07-19 16:28:11.298 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 16:28:11.372 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:28:11.395 +03:00 [INF] Route matched with {action = "GetAllUserRoles", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUserRolesAsync() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:28:11.664 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 16:28:11.692 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 16:28:11.717 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api) in 316.2246ms
2025-07-19 16:28:11.719 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:28:11.722 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - 200 null application/json; charset=utf-8 454.6193ms
2025-07-19 16:33:07.118 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:33:07.194 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:33:07.343 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 16:33:07.437 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:33:07.476 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:33:07.487 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:33:07.494 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:33:07.689 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:33:07.886 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:33:08.012 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:33:08.013 +03:00 [INF] Hosting environment: Development
2025-07-19 16:33:08.014 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:33:08.500 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:33:08.733 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 244.0702ms
2025-07-19 16:33:08.751 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:33:08.757 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.823ms
2025-07-19 16:33:08.766 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:33:08.808 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.0013ms
2025-07-19 16:33:08.974 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:33:08.992 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.9057ms
2025-07-19 16:33:17.063 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - null null
2025-07-19 16:33:17.086 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 16:33:17.163 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:33:17.185 +03:00 [INF] Route matched with {action = "GetAllUserRoles", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUserRolesAsync() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:33:17.408 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 16:33:17.438 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 16:33:17.462 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api) in 270.4906ms
2025-07-19 16:33:17.465 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:33:17.469 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - 200 null application/json; charset=utf-8 406.1302ms
2025-07-19 16:33:49.154 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:33:49.225 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:33:49.369 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 16:33:49.464 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:33:49.504 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:33:49.514 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:33:49.522 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:33:49.656 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:33:49.845 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:33:49.997 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:33:49.999 +03:00 [INF] Hosting environment: Development
2025-07-19 16:33:50.000 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:33:50.315 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:33:50.540 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 232.3267ms
2025-07-19 16:33:50.572 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:33:50.572 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:33:50.588 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 17.0999ms
2025-07-19 16:33:50.624 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 51.9275ms
2025-07-19 16:33:50.776 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:33:50.793 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.1085ms
2025-07-19 16:33:59.819 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - null null
2025-07-19 16:33:59.829 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 16:33:59.904 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:33:59.925 +03:00 [INF] Route matched with {action = "GetAllUserRoles", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUserRolesAsync() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:34:00.171 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 16:34:00.206 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 16:34:00.231 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api) in 299.8524ms
2025-07-19 16:34:00.233 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:34:00.238 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - 200 null application/json; charset=utf-8 419.2147ms
2025-07-19 16:34:22.241 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:34:22.326 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:34:22.490 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 16:34:22.638 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:34:22.761 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:34:22.772 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:34:22.777 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:34:22.930 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:34:23.170 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:34:23.301 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:34:23.303 +03:00 [INF] Hosting environment: Development
2025-07-19 16:34:23.304 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:34:35.615 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:34:35.690 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:34:35.844 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 16:34:35.933 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:34:35.976 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:34:35.987 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:34:35.995 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:34:36.130 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:34:36.318 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:34:36.463 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:34:36.588 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:34:36.589 +03:00 [INF] Hosting environment: Development
2025-07-19 16:34:36.590 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:34:36.718 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 265.4482ms
2025-07-19 16:34:36.833 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:34:36.833 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:34:36.839 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:34:36.840 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.5843ms
2025-07-19 16:34:36.851 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 12.8384ms
2025-07-19 16:34:36.879 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:34:36.884 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 50.9015ms
2025-07-19 16:34:36.892 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 13.1625ms
2025-07-19 16:34:36.917 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:34:36.938 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 20.8403ms
2025-07-19 16:34:37.173 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:34:37.195 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 22.8029ms
2025-07-19 16:34:37.196 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:34:37.214 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.8812ms
2025-07-19 16:34:48.268 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - null null
2025-07-19 16:34:48.278 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 16:34:48.353 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:34:48.375 +03:00 [INF] Route matched with {action = "GetAllUserRoles", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUserRolesAsync() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:34:48.615 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 16:34:48.643 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 16:34:48.671 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api) in 288.0297ms
2025-07-19 16:34:48.674 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:34:48.679 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - 200 null application/json; charset=utf-8 410.9092ms
2025-07-19 16:42:18.121 +03:00 [INF] Executed DbCommand (503ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
CREATE DATABASE [DoorsCompanyDb];
2025-07-19 16:42:18.260 +03:00 [INF] Executed DbCommand (103ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
IF SERVERPROPERTY('EngineEdition') <> 5
BEGIN
    ALTER DATABASE [DoorsCompanyDb] SET READ_COMMITTED_SNAPSHOT ON;
END;
2025-07-19 16:42:18.277 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-19 16:42:18.281 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-19 16:42:18.305 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-07-19 16:42:18.383 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-07-19 16:42:18.394 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-19 16:42:18.396 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-19 16:42:18.413 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-19 16:42:18.425 +03:00 [INF] Applying migration '20250719134130_Inti-Database'.
2025-07-19 16:42:18.526 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ActionTypes] (
    [Id] int NOT NULL IDENTITY,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_ActionTypes] PRIMARY KEY ([Id])
);
2025-07-19 16:42:18.529 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Companies] (
    [Id] int NOT NULL IDENTITY,
    [CommercialRegister] nvarchar(max) NULL,
    [IndustrialRegister] nvarchar(max) NULL,
    [TaxRegister] nvarchar(max) NULL,
    [EstablishmentDate] datetime2 NULL,
    [TotalShares] int NOT NULL,
    [Code] nvarchar(max) NULL,
    [Symbol] nvarchar(max) NULL,
    [LogoPath] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Companies] PRIMARY KEY ([Id])
);
2025-07-19 16:42:18.531 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [FinancialTransactions] (
    [Id] int NOT NULL IDENTITY,
    [TransactionDate] datetime2 NOT NULL,
    [TransactionType] int NOT NULL,
    [AccountType] int NOT NULL,
    [Amount] decimal(18,2) NOT NULL,
    [ReferenceId] int NULL,
    [ReferenceType] int NOT NULL,
    [Description] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_FinancialTransactions] PRIMARY KEY ([Id])
);
2025-07-19 16:42:18.533 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [PartnerBands] (
    [Id] int NOT NULL IDENTITY,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_PartnerBands] PRIMARY KEY ([Id])
);
2025-07-19 16:42:18.535 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Partners] (
    [Id] int NOT NULL IDENTITY,
    [InitialCapital] decimal(18,2) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Partners] PRIMARY KEY ([Id])
);
2025-07-19 16:42:18.537 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Permissions] (
    [Id] int NOT NULL IDENTITY,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Permissions] PRIMARY KEY ([Id])
);
2025-07-19 16:42:18.539 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-19 16:42:18.545 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Users] (
    [Id] int NOT NULL IDENTITY,
    [UserName] nvarchar(max) NOT NULL,
    [FullName] nvarchar(max) NOT NULL,
    [Password] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NOT NULL,
    [ProfileImage] nvarchar(max) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY ([Id])
);
2025-07-19 16:42:19.234 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [MainActions] (
    [Id] int NOT NULL IDENTITY,
    [ParentActionId] int NULL,
    [ActionTypeId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_MainActions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_MainActions_ActionTypes_ActionTypeId] FOREIGN KEY ([ActionTypeId]) REFERENCES [ActionTypes] ([Id]),
    CONSTRAINT [FK_MainActions_MainActions_ParentActionId] FOREIGN KEY ([ParentActionId]) REFERENCES [MainActions] ([Id])
);
2025-07-19 16:42:19.241 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ShareDistributions] (
    [Id] int NOT NULL IDENTITY,
    [PartnerId] int NOT NULL,
    [SharesCount] int NOT NULL,
    [DistributionDate] datetime2 NOT NULL,
    [ShareValue] decimal(18,2) NOT NULL,
    [Description] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ShareDistributions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ShareDistributions_Partners_PartnerId] FOREIGN KEY ([PartnerId]) REFERENCES [Partners] ([Id]) ON DELETE CASCADE
);
2025-07-19 16:42:19.244 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ShareTransfers] (
    [Id] int NOT NULL IDENTITY,
    [TransfersDate] datetime2 NOT NULL,
    [FromPartnerId] int NOT NULL,
    [ToPartnerId] int NOT NULL,
    [SharesCount] int NOT NULL,
    [TransferAmount] decimal(18,2) NOT NULL,
    [Description] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ShareTransfers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ShareTransfers_Partners_FromPartnerId] FOREIGN KEY ([FromPartnerId]) REFERENCES [Partners] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_ShareTransfers_Partners_ToPartnerId] FOREIGN KEY ([ToPartnerId]) REFERENCES [Partners] ([Id]) ON DELETE NO ACTION
);
2025-07-19 16:42:19.246 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [RolePermissions] (
    [Id] int NOT NULL IDENTITY,
    [RoleId] int NOT NULL,
    [PermissionId] int NOT NULL,
    CONSTRAINT [PK_RolePermissions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_RolePermissions_Permissions_PermissionId] FOREIGN KEY ([PermissionId]) REFERENCES [Permissions] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_RolePermissions_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]) ON DELETE CASCADE
);
2025-07-19 16:42:19.248 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [RefreshTokens] (
    [Id] int NOT NULL IDENTITY,
    [Token] nvarchar(max) NOT NULL,
    [UserId] int NOT NULL,
    [ExpiryDate] datetime2 NOT NULL,
    [IsRevoked] bit NOT NULL,
    [RevokedReason] nvarchar(max) NULL,
    [RevokedAt] datetime2 NULL,
    [IpAddress] nvarchar(max) NULL,
    [UserAgent] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_RefreshTokens] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_RefreshTokens_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-07-19 16:42:22.857 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UserRoles] (
    [Id] int NOT NULL IDENTITY,
    [UserId] int NOT NULL,
    [RoleId] int NOT NULL,
    CONSTRAINT [PK_UserRoles] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_UserRoles_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_UserRoles_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-07-19 16:42:22.860 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [PartnerTransations] (
    [Id] int NOT NULL IDENTITY,
    [TransactionDate] datetime2 NOT NULL,
    [ActionDetailId] int NOT NULL,
    [PartnerId] int NOT NULL,
    [PartnerBandId] int NOT NULL,
    [Amount] decimal(18,2) NOT NULL,
    [Description] nvarchar(max) NULL,
    [Notes] nvarchar(max) NULL,
    [ImagePath] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_PartnerTransations] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_PartnerTransations_MainActions_ActionDetailId] FOREIGN KEY ([ActionDetailId]) REFERENCES [MainActions] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_PartnerTransations_PartnerBands_PartnerBandId] FOREIGN KEY ([PartnerBandId]) REFERENCES [PartnerBands] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_PartnerTransations_Partners_PartnerId] FOREIGN KEY ([PartnerId]) REFERENCES [Partners] ([Id]) ON DELETE CASCADE
);
2025-07-19 16:42:22.869 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ShareHistories] (
    [Id] int NOT NULL IDENTITY,
    [PartnerId] int NOT NULL,
    [SharesCount] int NOT NULL,
    [SharePercentage] decimal(5,4) NOT NULL,
    [RecordDate] datetime2 NOT NULL,
    [ChangeType] int NOT NULL,
    [Description] nvarchar(max) NULL,
    [TransactionId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ShareHistories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ShareHistories_Partners_PartnerId] FOREIGN KEY ([PartnerId]) REFERENCES [Partners] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_ShareHistories_ShareTransfers_TransactionId] FOREIGN KEY ([TransactionId]) REFERENCES [ShareTransfers] ([Id])
);
2025-07-19 16:42:22.873 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_MainActions_ActionTypeId] ON [MainActions] ([ActionTypeId]);
2025-07-19 16:42:22.874 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_MainActions_ParentActionId] ON [MainActions] ([ParentActionId]);
2025-07-19 16:42:22.875 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_PartnerTransations_ActionDetailId] ON [PartnerTransations] ([ActionDetailId]);
2025-07-19 16:42:22.876 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_PartnerTransations_PartnerBandId] ON [PartnerTransations] ([PartnerBandId]);
2025-07-19 16:42:22.877 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_PartnerTransations_PartnerId] ON [PartnerTransations] ([PartnerId]);
2025-07-19 16:42:22.878 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RefreshTokens_UserId] ON [RefreshTokens] ([UserId]);
2025-07-19 16:42:22.878 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RolePermissions_PermissionId] ON [RolePermissions] ([PermissionId]);
2025-07-19 16:42:22.879 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RolePermissions_RoleId] ON [RolePermissions] ([RoleId]);
2025-07-19 16:42:25.852 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareDistributions_PartnerId] ON [ShareDistributions] ([PartnerId]);
2025-07-19 16:42:25.853 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareHistories_PartnerId] ON [ShareHistories] ([PartnerId]);
2025-07-19 16:42:25.854 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareHistories_TransactionId] ON [ShareHistories] ([TransactionId]);
2025-07-19 16:42:25.855 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareTransfers_FromPartnerId] ON [ShareTransfers] ([FromPartnerId]);
2025-07-19 16:42:25.856 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareTransfers_ToPartnerId] ON [ShareTransfers] ([ToPartnerId]);
2025-07-19 16:42:25.857 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_UserRoles_RoleId] ON [UserRoles] ([RoleId]);
2025-07-19 16:42:25.858 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_UserRoles_UserId] ON [UserRoles] ([UserId]);
2025-07-19 16:42:25.865 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250719134130_Inti-Database', N'9.0.7');
2025-07-19 16:42:25.872 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-07-19 16:42:47.198 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:42:47.292 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]
        WHERE [u].[UserName] = N'Admin') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:42:47.514 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([CreatedAt], [CreatedBy], [FullName], [IsActive], [IsDeleted], [Password], [Phone], [ProfileImage], [UpdatedAt], [UpdatedBy], [UserName])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-07-19 16:42:47.553 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:42:47.603 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000), @p11='?' (DbType = Boolean), @p12='?' (DbType = Boolean), @p13='?' (Size = 4000), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32), @p18='?' (Size = 4000), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?' (Size = 4000), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = DateTime2), @p25='?' (DbType = Int32), @p26='?' (Size = 4000), @p27='?' (DbType = Boolean), @p28='?' (DbType = Boolean), @p29='?' (Size = 4000), @p30='?' (DbType = DateTime2), @p31='?' (DbType = Int32), @p32='?' (DbType = DateTime2), @p33='?' (DbType = Int32), @p34='?' (Size = 4000), @p35='?' (DbType = Boolean), @p36='?' (DbType = Boolean), @p37='?' (Size = 4000), @p38='?' (DbType = DateTime2), @p39='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [Roles] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, 1),
(@p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, 2),
(@p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, 3),
(@p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, 4)) AS i ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsActive], i.[IsDeleted], i.[Name], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-07-19 16:42:47.652 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:42:47.681 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:42:47.719 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [UserRoles] USING (
VALUES (@p0, @p1, 0),
(@p2, @p3, 1),
(@p4, @p5, 2),
(@p6, @p7, 3),
(@p8, @p9, 4)) AS i ([RoleId], [UserId], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([RoleId], [UserId])
VALUES (i.[RoleId], i.[UserId])
OUTPUT INSERTED.[Id], i._Position;
2025-07-19 16:42:47.730 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:42:47.770 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000), @p11='?' (DbType = Boolean), @p12='?' (DbType = Boolean), @p13='?' (Size = 4000), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32), @p18='?' (Size = 4000), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?' (Size = 4000), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = DateTime2), @p25='?' (DbType = Int32), @p26='?' (Size = 4000), @p27='?' (DbType = Boolean), @p28='?' (DbType = Boolean), @p29='?' (Size = 4000), @p30='?' (DbType = DateTime2), @p31='?' (DbType = Int32), @p32='?' (DbType = DateTime2), @p33='?' (DbType = Int32), @p34='?' (Size = 4000), @p35='?' (DbType = Boolean), @p36='?' (DbType = Boolean), @p37='?' (Size = 4000), @p38='?' (DbType = DateTime2), @p39='?' (DbType = Int32), @p40='?' (DbType = DateTime2), @p41='?' (DbType = Int32), @p42='?' (Size = 4000), @p43='?' (DbType = Boolean), @p44='?' (DbType = Boolean), @p45='?' (Size = 4000), @p46='?' (DbType = DateTime2), @p47='?' (DbType = Int32), @p48='?' (DbType = DateTime2), @p49='?' (DbType = Int32), @p50='?' (Size = 4000), @p51='?' (DbType = Boolean), @p52='?' (DbType = Boolean), @p53='?' (Size = 4000), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (DbType = DateTime2), @p57='?' (DbType = Int32), @p58='?' (Size = 4000), @p59='?' (DbType = Boolean), @p60='?' (DbType = Boolean), @p61='?' (Size = 4000), @p62='?' (DbType = DateTime2), @p63='?' (DbType = Int32), @p64='?' (DbType = DateTime2), @p65='?' (DbType = Int32), @p66='?' (Size = 4000), @p67='?' (DbType = Boolean), @p68='?' (DbType = Boolean), @p69='?' (Size = 4000), @p70='?' (DbType = DateTime2), @p71='?' (DbType = Int32), @p72='?' (DbType = DateTime2), @p73='?' (DbType = Int32), @p74='?' (Size = 4000), @p75='?' (DbType = Boolean), @p76='?' (DbType = Boolean), @p77='?' (Size = 4000), @p78='?' (DbType = DateTime2), @p79='?' (DbType = Int32), @p80='?' (DbType = DateTime2), @p81='?' (DbType = Int32), @p82='?' (Size = 4000), @p83='?' (DbType = Boolean), @p84='?' (DbType = Boolean), @p85='?' (Size = 4000), @p86='?' (DbType = DateTime2), @p87='?' (DbType = Int32), @p88='?' (DbType = DateTime2), @p89='?' (DbType = Int32), @p90='?' (Size = 4000), @p91='?' (DbType = Boolean), @p92='?' (DbType = Boolean), @p93='?' (Size = 4000), @p94='?' (DbType = DateTime2), @p95='?' (DbType = Int32), @p96='?' (DbType = DateTime2), @p97='?' (DbType = Int32), @p98='?' (Size = 4000), @p99='?' (DbType = Boolean), @p100='?' (DbType = Boolean), @p101='?' (Size = 4000), @p102='?' (DbType = DateTime2), @p103='?' (DbType = Int32), @p104='?' (DbType = DateTime2), @p105='?' (DbType = Int32), @p106='?' (Size = 4000), @p107='?' (DbType = Boolean), @p108='?' (DbType = Boolean), @p109='?' (Size = 4000), @p110='?' (DbType = DateTime2), @p111='?' (DbType = Int32), @p112='?' (DbType = DateTime2), @p113='?' (DbType = Int32), @p114='?' (Size = 4000), @p115='?' (DbType = Boolean), @p116='?' (DbType = Boolean), @p117='?' (Size = 4000), @p118='?' (DbType = DateTime2), @p119='?' (DbType = Int32), @p120='?' (DbType = DateTime2), @p121='?' (DbType = Int32), @p122='?' (Size = 4000), @p123='?' (DbType = Boolean), @p124='?' (DbType = Boolean), @p125='?' (Size = 4000), @p126='?' (DbType = DateTime2), @p127='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [Permissions] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, 1),
(@p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, 2),
(@p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, 3),
(@p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, 4),
(@p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, 5),
(@p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, 6),
(@p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, 7),
(@p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, 8),
(@p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79, 9),
(@p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, 10),
(@p88, @p89, @p90, @p91, @p92, @p93, @p94, @p95, 11),
(@p96, @p97, @p98, @p99, @p100, @p101, @p102, @p103, 12),
(@p104, @p105, @p106, @p107, @p108, @p109, @p110, @p111, 13),
(@p112, @p113, @p114, @p115, @p116, @p117, @p118, @p119, 14),
(@p120, @p121, @p122, @p123, @p124, @p125, @p126, @p127, 15)) AS i ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsActive], i.[IsDeleted], i.[Name], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-07-19 16:42:47.810 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[Name] = N'Admin'
2025-07-19 16:42:47.840 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = Int32), @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (DbType = Int32), @p18='?' (DbType = Int32), @p19='?' (DbType = Int32), @p20='?' (DbType = Int32), @p21='?' (DbType = Int32), @p22='?' (DbType = Int32), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = Int32), @p29='?' (DbType = Int32), @p30='?' (DbType = Int32), @p31='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [RolePermissions] USING (
VALUES (@p0, @p1, 0),
(@p2, @p3, 1),
(@p4, @p5, 2),
(@p6, @p7, 3),
(@p8, @p9, 4),
(@p10, @p11, 5),
(@p12, @p13, 6),
(@p14, @p15, 7),
(@p16, @p17, 8),
(@p18, @p19, 9),
(@p20, @p21, 10),
(@p22, @p23, 11),
(@p24, @p25, 12),
(@p26, @p27, 13),
(@p28, @p29, 14),
(@p30, @p31, 15)) AS i ([PermissionId], [RoleId], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([PermissionId], [RoleId])
VALUES (i.[PermissionId], i.[RoleId])
OUTPUT INSERTED.[Id], i._Position;
2025-07-19 16:42:47.979 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:42:48.147 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:42:48.351 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:42:48.353 +03:00 [INF] Hosting environment: Development
2025-07-19 16:42:48.354 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:42:48.695 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:42:48.917 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 230.4855ms
2025-07-19 16:42:48.934 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:42:48.940 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.3116ms
2025-07-19 16:42:48.947 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:42:48.989 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.5584ms
2025-07-19 16:42:49.149 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:42:49.165 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 16.0512ms
2025-07-19 16:42:57.489 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - null null
2025-07-19 16:42:57.501 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 16:42:57.565 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:42:57.587 +03:00 [INF] Route matched with {action = "GetAllUserRoles", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUserRolesAsync() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:42:57.834 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-19 16:42:57.860 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 16:42:57.882 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api) in 289.2232ms
2025-07-19 16:42:57.885 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-19 16:42:57.890 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - 200 null application/json; charset=utf-8 400.8127ms
2025-07-19 16:43:12.786 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/1/user-Roles - null null
2025-07-19 16:43:12.795 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 16:43:12.808 +03:00 [INF] Route matched with {action = "GetUserRoleById", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserRoleById(Int32) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-19 16:43:12.944 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-19 16:43:12.957 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:43:12.961 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 16:43:12.966 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetUserRoleById (DoorCompany.Api) in 155.4624ms
2025-07-19 16:43:12.968 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetUserRoleById (DoorCompany.Api)'
2025-07-19 16:43:12.971 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/1/user-Roles - 200 null application/json; charset=utf-8 185.0184ms
2025-07-19 16:53:14.308 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:53:14.382 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:53:14.528 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 16:53:14.617 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:53:14.660 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:53:14.672 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:53:14.680 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:53:14.850 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:53:15.055 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:53:15.212 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:53:15.213 +03:00 [INF] Hosting environment: Development
2025-07-19 16:53:15.214 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:53:15.612 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:53:15.836 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 231.6212ms
2025-07-19 16:53:15.866 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:53:15.867 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:53:15.880 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 14.7534ms
2025-07-19 16:53:15.913 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 45.2497ms
2025-07-19 16:53:16.073 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:53:16.094 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 20.4313ms
2025-07-19 16:53:25.606 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-19 16:53:25.632 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 16:53:25.695 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 16:53:25.723 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 16:53:25.955 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [r0].[Id], [r0].[PermissionId], [r0].[RoleId]
FROM [Roles] AS [r]
LEFT JOIN [RolePermissions] AS [r0] ON [r].[Id] = [r0].[RoleId]
ORDER BY [r].[Id]
2025-07-19 16:55:42.874 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:55:42.949 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:55:43.092 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 16:55:43.188 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 16:55:43.233 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 16:55:43.244 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:55:43.252 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 16:55:43.388 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 16:55:43.573 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 16:55:43.686 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 16:55:43.688 +03:00 [INF] Hosting environment: Development
2025-07-19 16:55:43.690 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 16:55:43.993 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 16:55:44.225 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 241.508ms
2025-07-19 16:55:44.240 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 16:55:44.246 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.3463ms
2025-07-19 16:55:44.255 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 16:55:44.295 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 40.6117ms
2025-07-19 16:55:44.466 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 16:55:44.483 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 16.9288ms
2025-07-19 16:55:49.982 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-19 16:55:49.993 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 16:55:50.073 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 16:55:50.096 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 16:55:56.690 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [r0].[Id], [r0].[PermissionId], [r0].[RoleId]
FROM [Roles] AS [r]
LEFT JOIN [RolePermissions] AS [r0] ON [r].[Id] = [r0].[RoleId]
ORDER BY [r].[Id]
2025-07-19 16:56:22.016 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api) in 31913.5011ms
2025-07-19 16:56:22.020 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 16:56:22.025 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.NullReferenceException: Object reference not set to an instance of an object.
   at DoorCompany.Service.Repositories.Implementations.UserService.<>c.<GetAllRolesAsync>b__20_2(RolePermission ur) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 518
   at System.Linq.Enumerable.SelectEnumerableIterator`2.MoveNext()
   at System.String.Join(String separator, IEnumerable`1 values)
   at DoorCompany.Service.Repositories.Implementations.UserService.<>c.<GetAllRolesAsync>b__20_0(Role role) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 514
   at System.Linq.Enumerable.SelectListIterator`2.Fill(ReadOnlySpan`1 source, Span`1 destination, Func`2 func)
   at System.Linq.Enumerable.SelectListIterator`2.ToList()
   at DoorCompany.Service.Repositories.Implementations.UserService.GetAllRolesAsync() in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 514
   at DoorCompany.Api.Controllers.RoleController.GetAllRoles() in D:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\RoleController.cs:line 24
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-19 16:56:22.087 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - 500 null text/plain; charset=utf-8 32105.5736ms
2025-07-19 16:56:27.530 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-19 16:56:27.536 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 16:56:27.539 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 16:56:32.573 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [r0].[Id], [r0].[PermissionId], [r0].[RoleId]
FROM [Roles] AS [r]
LEFT JOIN [RolePermissions] AS [r0] ON [r].[Id] = [r0].[RoleId]
ORDER BY [r].[Id]
2025-07-19 17:00:00.231 +03:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:00:00.304 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:00:00.448 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 17:00:00.564 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 17:00:00.628 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 17:00:00.640 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:00:00.651 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:00:00.812 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 17:00:01.034 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 17:00:01.218 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 17:00:01.222 +03:00 [INF] Hosting environment: Development
2025-07-19 17:00:01.223 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 17:00:01.224 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 17:00:01.498 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 305.7414ms
2025-07-19 17:00:01.526 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 17:00:01.528 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 17:00:01.536 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.3618ms
2025-07-19 17:00:01.576 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.9208ms
2025-07-19 17:00:01.750 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 17:00:01.773 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 23.404ms
2025-07-19 17:00:06.598 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-19 17:00:06.609 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 17:00:06.684 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 17:00:06.705 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:00:09.198 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Roles] AS [r]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r].[Id] = [s].[RoleId]
ORDER BY [r].[Id], [s].[Id]
2025-07-19 17:00:13.373 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.RoleResponseWithPermissionListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 17:00:14.932 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api) in 8219.4886ms
2025-07-19 17:00:14.936 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 17:00:14.942 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - 200 null application/json; charset=utf-8 8343.3467ms
2025-07-19 17:08:53.859 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:08:53.931 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:08:54.086 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 17:08:54.178 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 17:08:54.221 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 17:08:54.233 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:08:54.241 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:08:54.382 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 17:08:54.586 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 17:08:54.861 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 17:08:54.921 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 17:08:54.923 +03:00 [INF] Hosting environment: Development
2025-07-19 17:08:54.925 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 17:08:55.089 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 241.3495ms
2025-07-19 17:08:55.107 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 17:08:55.114 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.2816ms
2025-07-19 17:08:55.119 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 17:08:55.159 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 40.3038ms
2025-07-19 17:08:55.322 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 17:08:55.374 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 51.8949ms
2025-07-19 17:09:02.560 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-19 17:09:02.571 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 17:09:02.636 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:09:02.664 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:09:02.948 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:09:02.974 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 17:09:03.074 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 403.3956ms
2025-07-19 17:09:03.077 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:09:03.081 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.ArgumentNullException: Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.Any[TSource](IEnumerable`1 source, Func`2 predicate)
   at DoorCompany.Service.Repositories.Implementations.UserService.<>c.<GetRolePermissionsByIdAsync>b__21_3(Permission role) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 544
   at System.Linq.Enumerable.SelectListIterator`2.Fill(ReadOnlySpan`1 source, Span`1 destination, Func`2 func)
   at System.Linq.Enumerable.SelectListIterator`2.ToList()
   at DoorCompany.Service.Repositories.Implementations.UserService.GetRolePermissionsByIdAsync(Int32 roleId) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 540
   at DoorCompany.Api.Controllers.RoleController.GetRoleById(Int32 id) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\RoleController.cs:line 35
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-19 17:09:03.137 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 500 null text/plain; charset=utf-8 577.0688ms
2025-07-19 17:09:24.411 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-19 17:09:24.417 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:09:24.419 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:09:24.500 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:09:24.506 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 17:09:24.571 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 149.378ms
2025-07-19 17:09:24.573 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:09:24.575 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.ArgumentNullException: Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.Any[TSource](IEnumerable`1 source, Func`2 predicate)
   at DoorCompany.Service.Repositories.Implementations.UserService.<>c.<GetRolePermissionsByIdAsync>b__21_3(Permission role) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 544
   at System.Linq.Enumerable.SelectListIterator`2.Fill(ReadOnlySpan`1 source, Span`1 destination, Func`2 func)
   at System.Linq.Enumerable.SelectListIterator`2.ToList()
   at DoorCompany.Service.Repositories.Implementations.UserService.GetRolePermissionsByIdAsync(Int32 roleId) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 540
   at DoorCompany.Api.Controllers.RoleController.GetRoleById(Int32 id) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\RoleController.cs:line 35
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-19 17:09:24.584 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 500 null text/plain; charset=utf-8 172.5352ms
2025-07-19 17:09:46.297 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-19 17:09:46.303 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:09:46.305 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:09:52.804 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:10:13.417 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 17:10:15.599 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 29291.7271ms
2025-07-19 17:10:15.601 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:10:15.603 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.ArgumentNullException: Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.Any[TSource](IEnumerable`1 source, Func`2 predicate)
   at DoorCompany.Service.Repositories.Implementations.UserService.<>c.<GetRolePermissionsByIdAsync>b__21_3(Permission role) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 544
   at System.Linq.Enumerable.SelectListIterator`2.Fill(ReadOnlySpan`1 source, Span`1 destination, Func`2 func)
   at System.Linq.Enumerable.SelectListIterator`2.ToList()
   at DoorCompany.Service.Repositories.Implementations.UserService.GetRolePermissionsByIdAsync(Int32 roleId) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 540
   at DoorCompany.Api.Controllers.RoleController.GetRoleById(Int32 id) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\RoleController.cs:line 35
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-19 17:10:15.611 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 500 null text/plain; charset=utf-8 29313.5125ms
2025-07-19 17:10:26.562 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-19 17:10:26.567 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:10:26.569 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:10:28.462 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:10:35.253 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 17:10:44.049 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 17477.0125ms
2025-07-19 17:10:44.051 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:10:44.053 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.ArgumentNullException: Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.Any[TSource](IEnumerable`1 source, Func`2 predicate)
   at DoorCompany.Service.Repositories.Implementations.UserService.<>c.<GetRolePermissionsByIdAsync>b__21_3(Permission role) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 544
   at System.Linq.Enumerable.SelectListIterator`2.Fill(ReadOnlySpan`1 source, Span`1 destination, Func`2 func)
   at System.Linq.Enumerable.SelectListIterator`2.ToList()
   at DoorCompany.Service.Repositories.Implementations.UserService.GetRolePermissionsByIdAsync(Int32 roleId) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 540
   at DoorCompany.Api.Controllers.RoleController.GetRoleById(Int32 id) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\RoleController.cs:line 35
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-19 17:10:44.066 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 500 null text/plain; charset=utf-8 17503.7307ms
2025-07-19 17:13:44.309 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:13:44.400 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:13:44.553 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 17:13:44.642 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 17:13:44.683 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 17:13:44.698 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:13:44.706 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:13:44.843 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 17:13:45.014 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 17:13:45.176 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 17:13:45.177 +03:00 [INF] Hosting environment: Development
2025-07-19 17:13:45.178 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 17:13:45.563 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 17:13:45.789 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 234.5247ms
2025-07-19 17:13:45.815 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 17:13:45.817 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 17:13:45.825 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.1604ms
2025-07-19 17:13:45.858 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.3037ms
2025-07-19 17:13:46.020 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 17:13:46.037 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.236ms
2025-07-19 17:13:54.065 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-19 17:13:54.086 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 17:13:54.161 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:13:54.188 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:13:58.499 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:13:59.691 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 17:14:01.049 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 6854.8734ms
2025-07-19 17:14:01.052 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:14:01.058 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.ArgumentNullException: Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.Any[TSource](IEnumerable`1 source, Func`2 predicate)
   at DoorCompany.Service.Repositories.Implementations.UserService.<>c.<GetRolePermissionsByIdAsync>b__21_3(Permission role) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 544
   at System.Linq.Enumerable.SelectListIterator`2.Fill(ReadOnlySpan`1 source, Span`1 destination, Func`2 func)
   at System.Linq.Enumerable.SelectListIterator`2.ToList()
   at DoorCompany.Service.Repositories.Implementations.UserService.GetRolePermissionsByIdAsync(Int32 roleId) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 540
   at DoorCompany.Api.Controllers.RoleController.GetRoleById(Int32 id) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\RoleController.cs:line 35
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-19 17:14:01.108 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 500 null text/plain; charset=utf-8 7042.3258ms
2025-07-19 17:14:13.032 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-19 17:14:13.038 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:14:13.040 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:14:18.046 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:17:34.085 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:17:34.164 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:17:34.329 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 17:17:34.417 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 17:17:34.460 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 17:17:34.470 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:17:34.479 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:17:34.614 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 17:17:34.804 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 17:17:34.936 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 17:17:34.937 +03:00 [INF] Hosting environment: Development
2025-07-19 17:17:34.938 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 17:17:35.323 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 17:17:35.562 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 249.7156ms
2025-07-19 17:17:35.586 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 17:17:35.589 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 17:17:35.595 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.7268ms
2025-07-19 17:17:35.631 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.6174ms
2025-07-19 17:17:35.796 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 17:17:35.814 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.8963ms
2025-07-19 17:17:42.567 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-19 17:17:42.588 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 17:17:42.661 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:17:42.687 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:17:48.852 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:17:49.687 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 17:17:52.463 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 17:17:52.491 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 9795.983ms
2025-07-19 17:17:52.494 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:17:52.498 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 200 null application/json; charset=utf-8 9930.9031ms
2025-07-19 17:18:13.894 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-19 17:18:13.901 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:18:13.902 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:18:17.741 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:18:17.746 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 17:18:17.749 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 17:18:17.751 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 3846.2928ms
2025-07-19 17:18:17.757 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:18:17.759 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 200 null application/json; charset=utf-8 3865.0135ms
2025-07-19 17:34:27.626 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:34:27.700 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:34:27.844 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 17:34:27.931 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 17:34:27.974 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 17:34:27.985 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:34:27.993 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:36:29.933 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:36:30.007 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:36:30.150 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 17:36:30.243 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 17:36:30.284 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 17:36:30.297 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:36:30.304 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 17:36:30.458 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 17:36:30.642 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 17:36:30.833 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 17:36:30.836 +03:00 [INF] Hosting environment: Development
2025-07-19 17:36:30.837 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 17:36:30.914 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 17:36:31.142 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 238.7297ms
2025-07-19 17:36:31.168 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 17:36:31.173 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 17:36:31.176 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.2426ms
2025-07-19 17:36:31.213 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.9961ms
2025-07-19 17:36:31.394 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 17:36:31.416 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 23.9422ms
2025-07-19 17:36:43.954 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-19 17:36:43.966 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 17:36:44.041 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:36:44.072 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:36:44.322 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:36:44.345 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 17:36:44.360 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 17:36:44.386 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 307.048ms
2025-07-19 17:36:44.389 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:36:44.395 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 200 null application/json; charset=utf-8 441.708ms
2025-07-19 17:37:48.136 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - application/json 1866
2025-07-19 17:37:48.143 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-19 17:37:48.154 +03:00 [INF] Route matched with {action = "AddAndRemoveRolePermission", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveRolePermission(DoorCompany.Service.Dtos.RoleDto.UpdateRolePermissionsDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:37:48.280 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__updateRolePermissionsDto_RoleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__updateRolePermissionsDto_RoleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:37:48.287 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 17:37:48.289 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api) in 132.211ms
2025-07-19 17:37:48.296 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-19 17:37:48.298 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - 200 null application/json; charset=utf-8 162.0672ms
2025-07-19 17:38:43.589 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-19 17:38:43.598 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:38:43.599 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:38:43.617 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:38:43.622 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 17:38:43.624 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 17:38:43.627 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 25.7286ms
2025-07-19 17:38:43.635 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 17:38:43.636 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 200 null application/json; charset=utf-8 46.7992ms
2025-07-19 17:41:03.741 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - application/json 1871
2025-07-19 17:41:03.749 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-19 17:41:03.750 +03:00 [INF] Route matched with {action = "AddAndRemoveRolePermission", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveRolePermission(DoorCompany.Service.Dtos.RoleDto.UpdateRolePermissionsDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 17:41:03.769 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__updateRolePermissionsDto_RoleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__updateRolePermissionsDto_RoleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 17:41:03.901 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RolePermissions] ([PermissionId], [RoleId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1);
2025-07-19 17:41:03.937 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 17:41:03.939 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api) in 187.3437ms
2025-07-19 17:41:03.941 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-19 17:41:03.943 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - 200 null application/json; charset=utf-8 204.9309ms
2025-07-19 18:18:28.543 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:18:28.619 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:18:28.764 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 18:18:28.857 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 18:18:28.897 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 18:18:28.908 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:18:28.918 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:18:29.181 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 18:18:29.369 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 18:18:29.573 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 18:18:29.705 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 18:18:29.707 +03:00 [INF] Hosting environment: Development
2025-07-19 18:18:29.708 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 18:18:29.819 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 254.1507ms
2025-07-19 18:18:29.850 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 18:18:29.850 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 18:18:29.862 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 12.7874ms
2025-07-19 18:18:29.904 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 53.9859ms
2025-07-19 18:18:30.055 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 18:18:30.073 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.7537ms
2025-07-19 18:19:06.817 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Role - application/json 31
2025-07-19 18:19:06.827 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 18:19:06.906 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.CreateRoleAsync (DoorCompany.Api)'
2025-07-19 18:19:06.936 +03:00 [INF] Route matched with {action = "CreateRole", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateRoleAsync(DoorCompany.Service.Dtos.RoleDto.CreateRoleDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:19:07.132 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__model_RoleName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[Name] = @__model_RoleName_0 AND [r].[IsDeleted] = CAST(0 AS bit)
2025-07-19 18:19:07.293 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Roles] ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-07-19 18:19:07.324 +03:00 [INF] Role created successfully: SystemAdmin
2025-07-19 18:19:07.337 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 18:19:07.362 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.CreateRoleAsync (DoorCompany.Api) in 419.8229ms
2025-07-19 18:19:07.365 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.CreateRoleAsync (DoorCompany.Api)'
2025-07-19 18:19:07.369 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Role - 200 null application/json; charset=utf-8 552.4976ms
2025-07-19 18:19:15.500 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-19 18:19:15.506 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 18:19:15.513 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:19:15.674 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Roles] AS [r]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r].[Id] = [s].[RoleId]
ORDER BY [r].[Id], [s].[Id]
2025-07-19 18:19:15.692 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.RoleResponseWithPermissionListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 18:19:15.698 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api) in 181.7463ms
2025-07-19 18:19:15.704 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 18:19:15.705 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - 200 null application/json; charset=utf-8 205.2456ms
2025-07-19 18:19:55.745 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/5 - null null
2025-07-19 18:19:55.753 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 18:19:55.760 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:19:55.801 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 18:19:55.814 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 18:19:55.821 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 18:19:55.824 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 59.6416ms
2025-07-19 18:19:55.825 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 18:19:55.827 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/5 - 200 null application/json; charset=utf-8 81.324ms
2025-07-19 18:20:01.832 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/6 - null null
2025-07-19 18:20:01.843 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 18:20:01.849 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:20:01.865 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-19 18:20:01.870 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 18:20:01.873 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 18:20:01.874 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 19.8328ms
2025-07-19 18:20:01.876 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-19 18:20:01.877 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/6 - 200 null application/json; charset=utf-8 44.834ms
2025-07-19 18:20:24.295 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 18:20:24.329 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 33.1427ms
2025-07-19 18:20:24.612 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 18:20:24.629 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.2631ms
2025-07-19 18:32:13.912 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:32:13.994 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:32:14.147 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 18:32:14.249 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 18:32:14.294 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 18:32:14.457 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [UserRoles] ([RoleId], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1);
2025-07-19 18:32:14.494 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:32:14.504 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:32:14.676 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 18:32:14.864 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 18:32:15.026 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 18:32:15.029 +03:00 [INF] Hosting environment: Development
2025-07-19 18:32:15.034 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 18:32:15.046 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 18:32:15.276 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 240.362ms
2025-07-19 18:32:15.299 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 18:32:15.303 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 18:32:15.307 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.9882ms
2025-07-19 18:32:15.346 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.5726ms
2025-07-19 18:32:15.641 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 18:32:15.666 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 25.6426ms
2025-07-19 18:32:23.415 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-19 18:32:23.440 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 18:32:23.511 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 18:32:23.533 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:32:23.763 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Roles] AS [r]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r].[Id] = [s].[RoleId]
ORDER BY [r].[Id], [s].[Id]
2025-07-19 18:32:23.789 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.RoleResponseWithPermissionListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 18:32:23.813 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api) in 272.9547ms
2025-07-19 18:32:23.815 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 18:32:23.820 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - 200 null application/json; charset=utf-8 405.0858ms
2025-07-19 18:32:42.423 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5184/api/Role - application/json 38
2025-07-19 18:32:42.429 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api)'
2025-07-19 18:32:42.441 +03:00 [INF] Route matched with {action = "UpdateRole", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateRoleAsync(DoorCompany.Service.Dtos.RoleDto.UpdateRoleDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:32:42.563 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[Id] = @__p_0
2025-07-19 18:32:42.566 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 18:32:42.571 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api) in 126.7284ms
2025-07-19 18:32:42.577 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api)'
2025-07-19 18:32:42.579 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5184/api/Role - 404 null application/json; charset=utf-8 155.5036ms
2025-07-19 18:33:15.446 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5184/api/Role - application/json 41
2025-07-19 18:33:15.469 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api)'
2025-07-19 18:33:15.470 +03:00 [INF] Route matched with {action = "UpdateRole", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateRoleAsync(DoorCompany.Service.Dtos.RoleDto.UpdateRoleDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:33:15.486 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[Id] = @__p_0
2025-07-19 18:33:15.488 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 18:33:15.490 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api) in 17.9368ms
2025-07-19 18:33:15.494 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api)'
2025-07-19 18:33:15.495 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5184/api/Role - 404 null application/json; charset=utf-8 52.2366ms
2025-07-19 18:33:20.627 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5184/api/Role - application/json 41
2025-07-19 18:33:20.630 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api)'
2025-07-19 18:33:20.632 +03:00 [INF] Route matched with {action = "UpdateRole", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateRoleAsync(DoorCompany.Service.Dtos.RoleDto.UpdateRoleDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:33:20.640 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[Id] = @__p_0
2025-07-19 18:33:20.644 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 18:33:20.647 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api) in 12.0424ms
2025-07-19 18:33:20.648 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api)'
2025-07-19 18:33:20.650 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5184/api/Role - 404 null application/json; charset=utf-8 23.2221ms
2025-07-19 18:33:30.379 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5184/api/Role - application/json 41
2025-07-19 18:33:30.383 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api)'
2025-07-19 18:33:30.384 +03:00 [INF] Route matched with {action = "UpdateRole", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateRoleAsync(DoorCompany.Service.Dtos.RoleDto.UpdateRoleDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:33:30.410 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[Id] = @__p_0
2025-07-19 18:33:30.431 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__model_RoleName_0='?' (Size = 4000), @__model_Id_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[Name] = @__model_RoleName_0 AND [r].[Id] <> @__model_Id_1 AND [r].[IsDeleted] = CAST(0 AS bit)
2025-07-19 18:33:30.448 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p6='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (DbType = Boolean), @p2='?' (DbType = Boolean), @p3='?' (Size = 4000), @p4='?' (DbType = DateTime2), @p5='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Roles] SET [Description] = @p0, [IsActive] = @p1, [IsDeleted] = @p2, [Name] = @p3, [UpdatedAt] = @p4, [UpdatedBy] = @p5
OUTPUT 1
WHERE [Id] = @p6;
2025-07-19 18:33:30.455 +03:00 [INF] Role updated successfully: 6
2025-07-19 18:33:30.459 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-19 18:33:30.462 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api) in 61.7346ms
2025-07-19 18:33:30.464 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.UpdateRoleAsync (DoorCompany.Api)'
2025-07-19 18:33:30.465 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5184/api/Role - 200 null application/json; charset=utf-8 86.2242ms
2025-07-19 18:33:36.241 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-19 18:33:36.246 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 18:33:36.248 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-19 18:33:36.260 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Roles] AS [r]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r].[Id] = [s].[RoleId]
ORDER BY [r].[Id], [s].[Id]
2025-07-19 18:33:36.264 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.RoleResponseWithPermissionListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 18:33:36.270 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api) in 20.2921ms
2025-07-19 18:33:36.272 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-19 18:33:36.274 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - 200 null application/json; charset=utf-8 32.7666ms
2025-07-19 18:48:08.189 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:48:08.260 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:48:08.414 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 18:48:08.503 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 18:48:08.543 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 18:48:08.554 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:48:08.565 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 18:48:08.743 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 18:48:08.960 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 18:48:09.125 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 18:48:09.138 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 18:48:09.147 +03:00 [INF] Hosting environment: Development
2025-07-19 18:48:09.151 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 18:48:09.373 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 256.6731ms
2025-07-19 18:48:09.398 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 18:48:09.403 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 18:48:09.406 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.5897ms
2025-07-19 18:48:09.447 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.5025ms
2025-07-19 18:48:09.622 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 18:48:09.644 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 22.7965ms
2025-07-19 18:48:15.650 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - null null
2025-07-19 18:48:15.661 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 18:48:15.743 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-19 18:48:15.765 +03:00 [INF] Route matched with {action = "GetAllPermissons", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPermissons() on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-19 18:48:15.921 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 18:48:15.939 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PermissionDto.PermissionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 18:48:15.963 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api) in 191.636ms
2025-07-19 18:48:15.967 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-19 18:48:15.972 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - 200 null application/json; charset=utf-8 322.0011ms
2025-07-19 19:15:25.081 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 19:15:25.156 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 19:15:25.340 +03:00 [INF] Executed DbCommand (42ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-19 19:15:25.423 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-19 19:15:25.485 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-19 19:15:25.497 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 19:15:25.504 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-19 19:15:25.657 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-19 19:15:25.853 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-19 19:15:25.972 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-19 19:15:25.977 +03:00 [INF] Hosting environment: Development
2025-07-19 19:15:25.978 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-19 19:15:26.384 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-19 19:15:26.610 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 234.5422ms
2025-07-19 19:15:26.639 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-19 19:15:26.642 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-19 19:15:26.646 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.8885ms
2025-07-19 19:15:26.684 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.7486ms
2025-07-19 19:15:26.859 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-19 19:15:26.877 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 18.4399ms
2025-07-19 19:15:56.037 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - null null
2025-07-19 19:15:56.060 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-19 19:15:56.127 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-19 19:15:56.149 +03:00 [INF] Route matched with {action = "GetAllPermissons", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPermissons() on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-19 19:15:56.294 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 19:15:56.314 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PermissionDto.PermissionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 19:15:56.338 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api) in 182.7642ms
2025-07-19 19:15:56.340 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-19 19:15:56.344 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - 200 null application/json; charset=utf-8 307.2363ms
2025-07-19 19:16:12.282 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Permission - application/json 48
2025-07-19 19:16:12.302 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-19 19:16:12.315 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(DoorCompany.Service.Dtos.PermissionDto.CreatePermissionDto) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-19 19:16:12.501 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[@__generatedPermissions_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name]
FROM [Permissions] AS [p]
WHERE [p].[Name] IN (
    SELECT [g].[value]
    FROM OPENJSON(@__generatedPermissions_0) WITH ([value] nvarchar(max) '$') AS [g]
)
2025-07-19 19:16:12.506 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PermissionDto.PermissionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 19:16:12.508 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 190.4105ms
2025-07-19 19:16:12.510 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-19 19:16:12.512 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Permission - 400 null application/json; charset=utf-8 230.3894ms
2025-07-19 19:17:00.487 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Permission - application/json 51
2025-07-19 19:17:00.499 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-19 19:17:00.501 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(DoorCompany.Service.Dtos.PermissionDto.CreatePermissionDto) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-19 19:17:00.518 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__generatedPermissions_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name]
FROM [Permissions] AS [p]
WHERE [p].[Name] IN (
    SELECT [g].[value]
    FROM OPENJSON(@__generatedPermissions_0) WITH ([value] nvarchar(max) '$') AS [g]
)
2025-07-19 19:17:00.689 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000), @p11='?' (DbType = Boolean), @p12='?' (DbType = Boolean), @p13='?' (Size = 4000), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32), @p18='?' (Size = 4000), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?' (Size = 4000), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = DateTime2), @p25='?' (DbType = Int32), @p26='?' (Size = 4000), @p27='?' (DbType = Boolean), @p28='?' (DbType = Boolean), @p29='?' (Size = 4000), @p30='?' (DbType = DateTime2), @p31='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [Permissions] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, 1),
(@p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, 2),
(@p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, 3)) AS i ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsActive], i.[IsDeleted], i.[Name], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-07-19 19:17:00.719 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PermissionDto.PermissionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 19:17:00.721 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 218.3603ms
2025-07-19 19:17:00.723 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-19 19:17:00.724 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Permission - 200 null application/json; charset=utf-8 237.2242ms
2025-07-19 19:17:11.894 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - null null
2025-07-19 19:17:11.898 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-19 19:17:11.903 +03:00 [INF] Route matched with {action = "GetAllPermissons", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPermissons() on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-19 19:17:11.923 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-19 19:17:11.926 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PermissionDto.PermissionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-19 19:17:11.928 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api) in 10.2467ms
2025-07-19 19:17:11.933 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-19 19:17:11.935 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - 200 null application/json; charset=utf-8 40.6802ms
