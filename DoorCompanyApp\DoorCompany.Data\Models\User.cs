﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Data.Models
{
    public class User : BaseEntity
    {
        public string UserName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string ProfileImage { get; set; } = string.Empty;
        public ICollection<UserRole>? UserRoles { get; set; } 
    }

    public class Role : BaseName
    {
        public ICollection<RolePermission>? RolePermissions { get; set; }
        public ICollection<UserRole>? UserRoles { get; set; }
    }

    public class Permission : BaseName 
    {
        public ICollection<RolePermission>? RolePermissions { get; set; }
    }

    public class RolePermission
    {
        [Key]
        public int Id { get; set; }
        public int RoleId { get; set; }
        public int PermissionId { get; set; }

        [ForeignKey(nameof(RoleId))]
        public virtual Role Roles { get; set; } = null!;

        [ForeignKey(nameof(PermissionId))]
        public virtual Permission Permissions { get; set; } = null!;
    }

    public class UserRole
    {
        [Key]
        public int Id { get; set; }
        public int UserId { get; set; }
        public int RoleId { get; set; }
        public virtual User Users { get; set; } = null!;
        public virtual Role Roles { get; set; } =null!;
    }
    public class RefreshToken : BaseEntity
    {
        public string Token { get; set; } = string.Empty;
        public int UserId { get; set; }
        public DateTime ExpiryDate { get; set; }
        public bool IsRevoked { get; set; } = false;
        public string? RevokedReason { get; set; }
        public DateTime? RevokedAt { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }

        [ForeignKey(nameof(UserId))]
        public virtual User User { get; set; } = null!;
    }

}
