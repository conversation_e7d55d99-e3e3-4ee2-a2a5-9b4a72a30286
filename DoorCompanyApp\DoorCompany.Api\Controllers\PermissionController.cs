﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos.ImageDto;
using DoorCompany.Service.Dtos.PermissionDto;
using DoorCompany.Service.Dtos.RoleDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using DoorCompany.Service.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PermissionController : AppControllerBase
    {
        private readonly IFileUploadService _fileUploadService;
        public PermissionController(IUnitOfWork work, IFileUploadService fileUploadService) : base(work)
        {
            _fileUploadService = fileUploadService;    
        }

        
        /// <summary>
        /// Get all Paermissions
        /// </summary>
        [HttpGet("all-Permission")]
        public async Task<IActionResult> GetAllPermissons()
        {
            var response = await _work.UserRepository.GetAllPermissionAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Create new Permission
        /// </summary>
        [HttpPost()]
        //[AllowAnonymous]
        public async Task<IActionResult> CreatePermissionAsync(CreatePermissionDto model)
        {
            var response = await _work.UserRepository.CreatePermissonAsync(model);
            return NewResult(response);
        }

      
    }
}
