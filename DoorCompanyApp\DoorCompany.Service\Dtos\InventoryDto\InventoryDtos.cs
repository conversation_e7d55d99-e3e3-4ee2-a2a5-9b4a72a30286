using DoorCompany.Data.Models;

namespace DoorCompany.Service.Dtos.InventoryDto
{
    // DTOs للمخزون
    public class ProductInventoryDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public decimal Balance { get; set; }
        public decimal AverageCost { get; set; }
        public decimal TotalValue { get; set; }
        public decimal? MinimumStock { get; set; }
        public decimal? MaximumStock { get; set; }
        public bool IsLowStock { get; set; }
        public bool IsOverStock { get; set; }
        public DateTime LastTransactionDate { get; set; }
    }

    public class InventoryTransactionDto
    {
        public int Id { get; set; }
        public DateTime TransactionDate { get; set; }
        public InventoryTransactionType Type { get; set; }
        public string TypeName { get; set; } = string.Empty;
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal TotalCost { get; set; }
        public decimal BalanceAfter { get; set; }
        public decimal AverageCost { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? Description { get; set; }
    }

    public class InventoryFilterDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int? ProductId { get; set; }
        public InventoryTransactionType? TransactionType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SearchTerm { get; set; }
        public bool? IsLowStock { get; set; }
        public bool? IsOverStock { get; set; }
    }

    public class ProductAvailabilityDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal RequiredQuantity { get; set; }
        public decimal AvailableQuantity { get; set; }
        public bool IsAvailable { get; set; }
        public decimal ShortageQuantity { get; set; }
    }

    public class ProductQuantityDto
    {
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
    }
}
