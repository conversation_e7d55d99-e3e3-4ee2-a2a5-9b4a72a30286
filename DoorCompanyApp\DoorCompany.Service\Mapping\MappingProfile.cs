﻿using AutoMapper;
using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Dtos.ProductDto;
using DoorCompany.Service.Dtos.ShareDto;
using DoorCompany.Service.Dtos.UserDto;
using Microsoft.AspNetCore.Http.HttpResults;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {

            //Partner Mapping
          
            CreateMap<Partner, CreatePartnerDto>().ReverseMap();
            CreateMap<Partner, UpdatePartnerDto>().ReverseMap();
            CreateMap<Partner, PartnerResponseDto>()
                .ForMember(dest => dest.CurrentCapital, opt => opt.Ignore())
                .ForMember(dest => dest.TotalInvestments, opt => opt.Ignore())
                .ForMember(dest => dest.TotalWithdrawals, opt => opt.Ignore())
                .ForMember(dest => dest.InitialShareValue, opt => opt.Ignore())
                .ForMember(dest => dest.CurrentShare, opt => opt.Ignore())
                .ForMember(dest => dest.InitialShareCount, opt => opt.Ignore());

            //Partner Band Mapping
            CreateMap<PartnerBand, PartnerBandResponseDto>();
            CreateMap<PartnerBand, CreatePartnerBandDto>().ReverseMap();
            CreateMap<PartnerBand, UpdatePartnerBandDto>().ReverseMap();

            //Partner Transaction Mapping
            CreateMap<PartnerTransation, CreatePartnerTransactionDto>().ReverseMap();
            // CreateMap<PartnerTransation, UpdatePartnerTransactionDto>().ReverseMap();
            CreateMap<UpdatePartnerTransactionDto, PartnerTransation>()
                .ForMember(dest => dest.ImagePath, opt => opt.Ignore());

            CreateMap<PartnerTransation, PartnerTransactionResponseDto>()
              .ForMember(dest => dest.ActionDetailName, opt => opt.MapFrom(src => src.MainAction.Name))
              .ForMember(dest => dest.PartnerName, opt => opt.MapFrom(src => src.Partners.Name))
              .ForMember(dest => dest.PartnerBandName, opt => opt.MapFrom(src => src.PartnerBand.Name))
              .ForMember(dest => dest.ImagePath, opt => opt.MapFrom((src, dest, destMember, context) =>
               {
                   var baseUrl = context.Items["baseUrl"]?.ToString();
                   return string.IsNullOrWhiteSpace(src.ImagePath)
                       ? null
                       : $"{baseUrl}/{src.ImagePath.Replace("\\", "/")}";
               }));

            //Share Distribution Mapping
            CreateMap<ShareDistribution, CreateShareDto>().ReverseMap();
            CreateMap<ShareDistribution, UpdateShareDto>().ReverseMap();
            CreateMap<ShareDistribution, ShareResponseDto>()
                  .ForMember(dest => dest.PartnerName, opt => opt.MapFrom(src => src.Partners.Name));

            //Share Transfer Mapping
            CreateMap<ShareTransfer, CreateShareTransferDto>().ReverseMap();
            CreateMap<ShareTransfer, UpdateShareTransferDto>().ReverseMap();
            CreateMap<ShareTransfer, ShareTransferResponseDto>()
                 .ForMember(dest => dest.BuyerName, opt => opt.MapFrom(src => src.Buyer.Name))
                 .ForMember(dest => dest.SellerName, opt => opt.MapFrom(src => src.Seller.Name));


            //Product Mapping 
            CreateMap<Product, CreateProductDto>().ReverseMap();                
            CreateMap<Product, ProductResponseDto>()
                   .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.ItemCategory.Name))
                   .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.ItemUnit.Name))
                   .ForMember(dest => dest.OpeningBalance, opt => opt.MapFrom(src => src.InventoryTransactions.Where(t => t.Type == InventoryTransactionType.OpeningBalance).Sum(t => t.Quantity)))
                   .ForMember(dest => dest.Balance,  opt => opt.Ignore())
                   .ForMember(dest => dest.ImagePath, opt => opt.MapFrom((src, dest, destMember, context) =>
                   {
                       var baseUrl = context.Items["baseUrl"]?.ToString();
                       return string.IsNullOrWhiteSpace(src.ImagePath)
                           ? null
                           : $"{baseUrl}/{src.ImagePath.Replace("\\", "/")}";
                   }));

            //Category Mapping
            CreateMap<ItemCategory, CreateCategoryDto>().ReverseMap();
            CreateMap<ItemCategory, CategoryResponseDto>()
                 .ForMember(dest => dest.ParentCategoryName,opt => opt.MapFrom(src => src.ParentCategory != null ? src.ParentCategory.Name : string.Empty))
                 .ForMember(dest => dest.ImageUrl, opt => opt.MapFrom((src, dest, destMember, context) =>
                 {
                    var baseUrl = context.Items["baseUrl"]?.ToString();
                    return string.IsNullOrWhiteSpace(src.ImageUrl)
                        ? null
                        : $"{baseUrl}/{src.ImageUrl.Replace("\\", "/")}";
                  }))
                 .ForMember(dest => dest.HasChildren,opt => opt.MapFrom(src => src.ChildCategories != null && src.ChildCategories.Any(c => !c.IsDeleted)))
                 .ForMember(dest => dest.Children,opt => opt.Ignore());


            //User Mapping
            CreateMap<User, CreateUserDto>().ReverseMap();
            CreateMap<User, UpdateUserDto>().ReverseMap();
            CreateMap<User, UserResponseDto>()
                .ForMember(dest => dest.ProfileImage, opt => opt.MapFrom((src, dest, destMember, context) =>
                                                {
                                                    var baseUrl = context.Items["baseUrl"]?.ToString();
                                                    return string.IsNullOrWhiteSpace(src.ProfileImage)
                                                        ? null
                                                        : $"{baseUrl}/{src.ProfileImage.Replace("\\", "/")}";
                                                }))
                .ForMember(dest => dest.Permissions, opt => opt.MapFrom(src => src.UserRoles));

            //User Role Mapping
            CreateMap<UserRole, UserRoleResponseDto>()
            .ForMember(dest => dest.UserRole, opt => opt.MapFrom(src => src.Roles.Name))
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.Users.FullName));

            //Main Action Mapping
            CreateMap<MainAction, BasicActionResponse>();

        }
    }
}
