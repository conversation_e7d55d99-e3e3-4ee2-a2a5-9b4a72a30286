﻿using AutoMapper;
using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Dtos.ProductDto;
using DoorCompany.Service.Dtos.ShareDto;
using DoorCompany.Service.Dtos.UserDto;
using DoorCompany.Service.Dtos.FinancialDto;
using DoorCompany.Service.Dtos.InventoryDto;
using Microsoft.AspNetCore.Http.HttpResults;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {

            //Partner Mapping
          
            CreateMap<Partner, CreatePartnerDto>().ReverseMap();
            CreateMap<Partner, UpdatePartnerDto>().ReverseMap();
            CreateMap<Partner, PartnerResponseDto>()
                .ForMember(dest => dest.CurrentCapital, opt => opt.Ignore())
                .ForMember(dest => dest.TotalInvestments, opt => opt.Ignore())
                .ForMember(dest => dest.TotalWithdrawals, opt => opt.Ignore())
                .ForMember(dest => dest.InitialShareValue, opt => opt.Ignore())
                .ForMember(dest => dest.CurrentShare, opt => opt.Ignore())
                .ForMember(dest => dest.InitialShareCount, opt => opt.Ignore());

            //Partner Band Mapping
            CreateMap<PartnerBand, PartnerBandResponseDto>();
            CreateMap<PartnerBand, CreatePartnerBandDto>().ReverseMap();
            CreateMap<PartnerBand, UpdatePartnerBandDto>().ReverseMap();

            //Partner Transaction Mapping
            CreateMap<PartnerTransation, CreatePartnerTransactionDto>().ReverseMap();
            // CreateMap<PartnerTransation, UpdatePartnerTransactionDto>().ReverseMap();
            CreateMap<UpdatePartnerTransactionDto, PartnerTransation>()
                .ForMember(dest => dest.ImagePath, opt => opt.Ignore());

            CreateMap<PartnerTransation, PartnerTransactionResponseDto>()
              .ForMember(dest => dest.ActionDetailName, opt => opt.MapFrom(src => src.MainAction.Name))
              .ForMember(dest => dest.PartnerName, opt => opt.MapFrom(src => src.Partners.Name))
              .ForMember(dest => dest.PartnerBandName, opt => opt.MapFrom(src => src.PartnerBand.Name))
              .ForMember(dest => dest.ImagePath, opt => opt.MapFrom((src, dest, destMember, context) =>
               {
                   var baseUrl = context.Items["baseUrl"]?.ToString();
                   return string.IsNullOrWhiteSpace(src.ImagePath)
                       ? null
                       : $"{baseUrl}/{src.ImagePath.Replace("\\", "/")}";
               }));

            //Share Distribution Mapping
            CreateMap<ShareDistribution, CreateShareDto>().ReverseMap();
            CreateMap<ShareDistribution, UpdateShareDto>().ReverseMap();
            CreateMap<ShareDistribution, ShareResponseDto>()
                  .ForMember(dest => dest.PartnerName, opt => opt.MapFrom(src => src.Partners.Name));

            //Share Transfer Mapping
            CreateMap<ShareTransfer, CreateShareTransferDto>().ReverseMap();
            CreateMap<ShareTransfer, UpdateShareTransferDto>().ReverseMap();
            CreateMap<ShareTransfer, ShareTransferResponseDto>()
                 .ForMember(dest => dest.BuyerName, opt => opt.MapFrom(src => src.Buyer.Name))
                 .ForMember(dest => dest.SellerName, opt => opt.MapFrom(src => src.Seller.Name));


            //Product Mapping 
            CreateMap<Product, CreateProductDto>().ReverseMap();                
            CreateMap<Product, ProductResponseDto>()
                   .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.ItemCategory.Name))
                   .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.ItemUnit.Name))
                   .ForMember(dest => dest.OpeningBalance, opt => opt.MapFrom(src => src.InventoryTransactions.Where(t => t.Type == InventoryTransactionType.OpeningBalance).Sum(t => t.Quantity)))
                   .ForMember(dest => dest.Balance,  opt => opt.Ignore())
                   .ForMember(dest => dest.ImagePath, opt => opt.MapFrom((src, dest, destMember, context) =>
                   {
                       var baseUrl = context.Items["baseUrl"]?.ToString();
                       return string.IsNullOrWhiteSpace(src.ImagePath)
                           ? null
                           : $"{baseUrl}/{src.ImagePath.Replace("\\", "/")}";
                   }));

            //Category Mapping
            CreateMap<ItemCategory, CreateCategoryDto>().ReverseMap();
            CreateMap<ItemCategory, CategoryResponseDto>()
                 .ForMember(dest => dest.ParentCategoryName,opt => opt.MapFrom(src => src.ParentCategory != null ? src.ParentCategory.Name : string.Empty))
                 .ForMember(dest => dest.ImageUrl, opt => opt.MapFrom((src, dest, destMember, context) =>
                 {
                    var baseUrl = context.Items["baseUrl"]?.ToString();
                    return string.IsNullOrWhiteSpace(src.ImageUrl)
                        ? null
                        : $"{baseUrl}/{src.ImageUrl.Replace("\\", "/")}";
                  }))
                 .ForMember(dest => dest.HasChildren,opt => opt.MapFrom(src => src.ChildCategories != null && src.ChildCategories.Any(c => !c.IsDeleted)))
                 .ForMember(dest => dest.Children,opt => opt.Ignore());


            //User Mapping
            CreateMap<User, CreateUserDto>().ReverseMap();
            CreateMap<User, UpdateUserDto>().ReverseMap();
            CreateMap<User, UserResponseDto>()
                .ForMember(dest => dest.ProfileImage, opt => opt.MapFrom((src, dest, destMember, context) =>
                                                {
                                                    var baseUrl = context.Items["baseUrl"]?.ToString();
                                                    return string.IsNullOrWhiteSpace(src.ProfileImage)
                                                        ? null
                                                        : $"{baseUrl}/{src.ProfileImage.Replace("\\", "/")}";
                                                }))
                .ForMember(dest => dest.Permissions, opt => opt.MapFrom(src => src.UserRoles));

            //User Role Mapping
            CreateMap<UserRole, UserRoleResponseDto>()
            .ForMember(dest => dest.UserRole, opt => opt.MapFrom(src => src.Roles.Name))
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.Users.FullName));

            //Main Action Mapping
            CreateMap<MainAction, BasicActionResponse>();

            // Invoice Mapping
            CreateMap<InvoiceMaster, CreateInvoiceMasterDto>().ReverseMap();
            CreateMap<InvoiceMaster, UpdateInvoiceMasterDto>().ReverseMap();
            CreateMap<InvoiceMaster, InvoiceResponseDto>()
                .ForMember(dest => dest.PartyName, opt => opt.MapFrom(src => src.Party.Name))
                .ForMember(dest => dest.Items, opt => opt.MapFrom(src => src.Items));

            CreateMap<InvoiceItem, CreateInvoiceItemDto>().ReverseMap();
            CreateMap<InvoiceItem, UpdateInvoiceItemDto>().ReverseMap();
            CreateMap<InvoiceItem, InvoiceItemResponseDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name));

            // Financial Transaction Mapping
            CreateMap<FinancialTransaction, CreateFinancialTransactionDto>().ReverseMap();
            CreateMap<FinancialTransaction, UpdateFinancialTransactionDto>().ReverseMap();
            CreateMap<FinancialTransaction, FinancialTransactionDto>()
                .ForMember(dest => dest.TransactionTypeName, opt => opt.MapFrom(src => src.TransactionType.ToString()))
                .ForMember(dest => dest.AccountTypeName, opt => opt.MapFrom(src => src.AccountType.ToString()))
                .ForMember(dest => dest.ReferenceTypeName, opt => opt.MapFrom(src => src.ReferenceType.ToString()));

            // Inventory Transaction Mapping
            CreateMap<InventoryTransaction, InventoryTransactionDto>()
                .ForMember(dest => dest.TypeName, opt => opt.MapFrom(src => src.Type.ToString()))
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name));

            // Product Inventory Mapping
            CreateMap<ProductInventory, ProductInventoryDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
                .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
                .ForMember(dest => dest.TotalValue, opt => opt.MapFrom(src => src.Balance * src.AverageCost))
                .ForMember(dest => dest.MinimumStock, opt => opt.MapFrom(src => src.Product.MinimumStock))
                .ForMember(dest => dest.MaximumStock, opt => opt.MapFrom(src => src.Product.MaximumStock))
                .ForMember(dest => dest.IsLowStock, opt => opt.MapFrom(src => src.Product.MinimumStock.HasValue && src.Balance <= src.Product.MinimumStock.Value))
                .ForMember(dest => dest.IsOverStock, opt => opt.MapFrom(src => src.Product.MaximumStock.HasValue && src.Balance >= src.Product.MaximumStock.Value))
                .ForMember(dest => dest.LastTransactionDate, opt => opt.Ignore());

        }
    }
}
