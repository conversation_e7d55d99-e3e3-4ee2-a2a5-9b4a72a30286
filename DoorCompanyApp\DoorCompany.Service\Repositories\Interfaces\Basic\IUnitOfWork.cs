﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Interfaces.Basic
{
    public interface IUnitOfWork
    {
        IPartnerRepository PartnerRepository { get; }
        IUserRepository UserRepository { get; }
        IMainActionRepository MainActionRepository { get; }
        IShareRepository ShareRepository { get; }
        IProductRepository ProductRepository { get; }
    }
}
