import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  <PERSON><PERSON><PERSON>r,
  MatFormField,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>l,
  Mat<PERSON>refix,
  MatSuffix
} from "./chunk-GCZAWPPH.js";
import {
  MatCommonModule,
  ObserversModule
} from "./chunk-QYGT4RVM.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-SBQGIAH2.js";

// ../../node_modules/@angular/material/fesm2022/module-BXZhw7pQ.mjs
var MatFormFieldModule = class _MatFormFieldModule {
  static ɵfac = function MatFormFieldModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MatFormFieldModule)();
  };
  static ɵmod = ɵɵdefineNgModule({
    type: _MatFormFieldModule,
    imports: [MatCommonModule, ObserversModule, MatFormField, MatL<PERSON>l, Mat<PERSON>rror, <PERSON><PERSON><PERSON>, MatPrefix, MatSuffix],
    exports: [MatFormField, MatLabel, Mat<PERSON>int, MatError, MatPrefix, MatSuffix, MatCommonModule]
  });
  static ɵinj = ɵɵdefineInjector({
    imports: [MatCommonModule, ObserversModule, MatCommonModule]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatFormFieldModule, [{
    type: NgModule,
    args: [{
      imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],
      exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]
    }]
  }], null, null);
})();

export {
  MatFormFieldModule
};
//# sourceMappingURL=chunk-VOS4RXAJ.js.map
