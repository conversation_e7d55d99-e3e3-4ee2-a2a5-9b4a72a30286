import { ChangeDetectorRef, Component } from '@angular/core';
import { UpdateProfileRequest, User } from '../../models/user.model';
import { UserService } from '../../services/user.service';
import {  FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.css'
})
export class ProfileComponent {
  profileForm: FormGroup;
  passwordForm: FormGroup;
  currentUser: User | null = null;
  isLoading = false;
  isUpdatingProfile = false;
  isChangingPassword = false;
  isUploadingImage = false;
  selectedFile: File | null = null;
  imagePreview: string | null = null;
  showPasswordForm = false;

constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.profileForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      phone: ['', [Validators.required, Validators.minLength(7)]]
         
    });

    this.passwordForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    this.loadUserProfile();
  }





  loadUserProfile(): void {
    this.isLoading = true;
    this.userService.getCurrentUser().subscribe({
      next: (response) => {
        if (response.statusCode == 200 && response.data ) {
          this.currentUser = response.data;
          this.profileForm.patchValue({
            fullName: this.currentUser.fullName,
            phone: this.currentUser.phone
          });
          this.imagePreview = this.currentUser.profileImage || null;
        }
      },
      error: (error) => {
        this.snackBar.open('فشل في تحميل الملف الشخصي', 'إغلاق', { duration: 3000 });
      },
      complete: () => {
        this.isLoading = false;
        this.cdr.markForCheck();
      }
    });
  }

 private passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');

    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }
togglePasswordForm(): void {
    this.showPasswordForm = !this.showPasswordForm;
    if (!this.showPasswordForm) {
      this.passwordForm.reset();
    }
  }

getErrorMessage(fieldName: string, formGroup: FormGroup = this.profileForm): string {
    const control = formGroup.get(fieldName);
    if (control?.hasError('required')) {
      return 'هذا الحقل مطلوب';
    }
    if (control?.hasError('fullName')) {
      return 'اسم المستخدم غير صحيح';
    }
    if (control?.hasError('minlength')) {
      const minLength = control.errors?.['minlength']?.requiredLength;
      return `يجب أن يكون على الأقل ${minLength} أحرف`;
    }
    if (control?.hasError('passwordMismatch')) {
      return 'كلمة المرور غير متطابقة';
    }
    return '';
  }

 changePassword(): void {
    if (this.passwordForm.invalid) {
      this.markFormGroupTouched(this.passwordForm);
      return;
    }

    this.isChangingPassword = true;
    const passwordData = this.passwordForm.value;

    this.userService.changePassword(passwordData).subscribe({
      next: () => {

        this.snackBar.open('تم تغيير كلمة المرور بنجاح', 'إغلاق', { duration: 3000 });
        this.passwordForm.reset();
        this.showPasswordForm = false;
        this.isChangingPassword = false;
      },
      error: (error) => {
        // console.error('Error changing password:', error);
        // const message = error.error?.message; //|| 'حدث خطأ أثناء تغيير كلمة المرور'
        // this.snackBar.open(message, 'إغلاق', { duration: 3000 });
        this.isChangingPassword = false;
      }
    });
  }


 updateProfile(): void {
    if (this.profileForm.invalid) {
      this.markFormGroupTouched(this.profileForm);
      return;
    }

    this.isUpdatingProfile = true;
    const updateData: UpdateProfileRequest = this.profileForm.value;

    this.userService.updateProfile(updateData).subscribe({
      next: () => {
        this.snackBar.open('تم تحديث البيانات بنجاح', 'إغلاق', { duration: 3000 });
        this.loadUserProfile(); // Reload to get updated data
        this.isUpdatingProfile = false;
      },
      error: (error) => {
        const message = error.error?.message || 'حدث خطأ أثناء تحديث البيانات';
        this.snackBar.open(message, 'إغلاق', { duration: 3000 });
        this.isUpdatingProfile = false;
      }
    });
  }



 private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

 getProfileImageUrl(): string {
    if (this.imagePreview) {
      return this.imagePreview;
    }
    if (this.currentUser?.profileImage) {
      // Add timestamp to prevent caching issues
      const timestamp = new Date().getTime();
      return `${this.currentUser.profileImage}?t=${timestamp}`;
    }
    // Return default avatar from API server
    return `no-image.png`;
  }


 hasProfileImage(): boolean {
    return !!(this.currentUser?.profileImage || this.imagePreview);
  }

 onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type ".jpg", ".jpeg", ".png", ".gif", ".bmp"
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'];
      if (!allowedTypes.includes(file.type)) {
        this.snackBar.open('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)', 'إغلاق', { duration: 4000 });
        return;
      }

      // Validate file size (5MB max)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        this.snackBar.open('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'إغلاق', { duration: 4000 });
        return;
      }

      this.selectedFile = file;

      // Create image preview
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.imagePreview = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }

  uploadImage(): void {
    if (!this.selectedFile) {
      this.snackBar.open('يرجى اختيار صورة أولاً', 'إغلاق', { duration: 3000 });
      return;
    }
       this.isUploadingImage = true;    
       this.userService.uploadProfileImage(this.selectedFile).subscribe({
       next: (response) => {
        this.snackBar.open('تم رفع الصورة بنجاح', 'إغلاق', { duration: 3000 });
        this.selectedFile = null;
        this.imagePreview = null;
        this.isUploadingImage = false;

        // Force reload current user to update image everywhere
        this.userService.getCurrentUser().subscribe({
          next: (response) => {
            this.currentUser = response.data;
            this.imagePreview = this.currentUser.profileImage || null;
            
            // Force change detection
            this.cdr.detectChanges();
          }
        });
      },
      error: (error) => {
        this.snackBar.open('حدث خطأ أثناء رفع الصورة', 'إغلاق', { duration: 3000 });
        this.isUploadingImage = false;
      }
    });
  }



   deleteImage(): void {
    if (!this.currentUser?.profileImage) {
      return;
    }
  }
cancelImageUpload(): void {
    this.selectedFile = null;
    this.imagePreview = null;
  }
}
