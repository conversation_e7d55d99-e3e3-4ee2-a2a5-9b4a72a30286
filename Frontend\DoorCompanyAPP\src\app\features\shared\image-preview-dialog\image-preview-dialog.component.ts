import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-image-preview-dialog',
  standalone: false,
  templateUrl: './image-preview-dialog.component.html',
  styleUrl: './image-preview-dialog.component.css'
})
export class ImagePreviewDialogComponent {
 constructor(
    public dialogRef: MatDialogRef<ImagePreviewDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { imageUrl: string; imageAlt: string }
  ) {}

  close(): void {
    this.dialogRef.close();
  }
}
