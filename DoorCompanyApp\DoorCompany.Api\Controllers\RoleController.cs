﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos.RoleDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RoleController : AppControllerBase
    {
        public RoleController(IUnitOfWork work) : base(work)
        {
        }



        /// <summary>
        /// Get Role by ID
        /// </summary>
        [HttpGet("All-Roles")]
        public async Task<IActionResult> GetAllRoles()
        {
            var response = await _work.UserRepository.GetAllRolesAsync();
            return NewResult(response);
        }


        /// <summary>
        /// Get RolePermission by Role Id
        /// </summary>
        [HttpGet("{id}")]
        [Authorize("Permissions.Test.Delete")]
        public async Task<IActionResult> GetRoleById(int id)
        {
            var response = await _work.UserRepository.GetRolePermissionsByIdAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// Create new Roles
        /// </summary>
        [HttpPost()]
        //[AllowAnonymous]
        public async Task<IActionResult> CreateRoleAsync(CreateRoleDto model)
        {
            var response = await _work.UserRepository.CreateRoleAsync(model);
            return NewResult(response);
        }

         /// <summary>
        /// Update  Role
        /// </summary>
        [HttpPut()]
        //[AllowAnonymous]
        public async Task<IActionResult> UpdateRoleAsync(UpdateRoleDto model)
        {
            var response = await _work.UserRepository.UpdateRoleAsync(model);
            return NewResult(response);
        }



        /// <summary>
        /// Create new UserRoles
        /// </summary>
        [HttpPost("add-Remove-Role-Permmision")]
        //[AllowAnonymous]
       
        public async Task<IActionResult> AddAndRemoveRolePermission(UpdateRolePermissionsDto updateRolePermissionsDto)
        {
            var response = await _work.UserRepository.UpdateRolePermiissionAsync(updateRolePermissionsDto);
            return NewResult(response);
        }

    }
}
