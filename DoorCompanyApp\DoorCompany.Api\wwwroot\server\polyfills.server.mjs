import { createRequire } from 'node:module';
globalThis['require'] ??= createRequire(import.meta.url);
var Gr=(_=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(_,{get:(k,h)=>(typeof require<"u"?require:k)[h]}):_)(function(_){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+_+'" is not supported')});var ct=globalThis;function lt(_){return(ct.__Zone_symbol_prefix||"__zone_symbol__")+_}function Oi(){let _=ct.performance;function k(N){_&&_.mark&&_.mark(N)}function h(N,g){_&&_.measure&&_.measure(N,g)}k("Zone");class c{static __symbol__=lt;static assertZonePatched(){if(ct.Promise!==D.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let g=c.current;for(;g.parent;)g=g.parent;return g}static get current(){return K.zone}static get currentTask(){return e}static __load_patch(g,S,C=!1){if(D.hasOwnProperty(g)){let G=ct[lt("forceDuplicateZoneCheck")]===!0;if(!C&&G)throw Error("Already loaded patch: "+g)}else if(!ct["__Zone_disable_"+g]){let G="Zone:"+g;k(G),D[g]=S(ct,c,B),h(G,G)}}get parent(){return this._parent}get name(){return this._name}_parent;_name;_properties;_zoneDelegate;constructor(g,S){this._parent=g,this._name=S?S.name||"unnamed":"<root>",this._properties=S&&S.properties||{},this._zoneDelegate=new i(this,this._parent&&this._parent._zoneDelegate,S)}get(g){let S=this.getZoneWith(g);if(S)return S._properties[g]}getZoneWith(g){let S=this;for(;S;){if(S._properties.hasOwnProperty(g))return S;S=S._parent}return null}fork(g){if(!g)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,g)}wrap(g,S){if(typeof g!="function")throw new Error("Expecting function got: "+g);let C=this._zoneDelegate.intercept(this,g,S),G=this;return function(){return G.runGuarded(C,this,arguments,S)}}run(g,S,C,G){K={parent:K,zone:this};try{return this._zoneDelegate.invoke(this,g,S,C,G)}finally{K=K.parent}}runGuarded(g,S=null,C,G){K={parent:K,zone:this};try{try{return this._zoneDelegate.invoke(this,g,S,C,G)}catch(ee){if(this._zoneDelegate.handleError(this,ee))throw ee}}finally{K=K.parent}}runTask(g,S,C){if(g.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(g.zone||J).name+"; Execution: "+this.name+")");let G=g,{type:ee,data:{isPeriodic:y=!1,isRefreshable:M=!1}={}}=g;if(g.state===P&&(ee===V||ee===Y))return;let X=g.state!=w;X&&G._transitionTo(w,d);let de=e;e=G,K={parent:K,zone:this};try{ee==Y&&g.data&&!y&&!M&&(g.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,G,S,C)}catch(xe){if(this._zoneDelegate.handleError(this,xe))throw xe}}finally{let xe=g.state;if(xe!==P&&xe!==se)if(ee==V||y||M&&xe===E)X&&G._transitionTo(d,w,E);else{let bt=G._zoneDelegates;this._updateTaskCount(G,-1),X&&G._transitionTo(P,w,P),M&&(G._zoneDelegates=bt)}K=K.parent,e=de}}scheduleTask(g){if(g.zone&&g.zone!==this){let C=this;for(;C;){if(C===g.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${g.zone.name}`);C=C.parent}}g._transitionTo(E,P);let S=[];g._zoneDelegates=S,g._zone=this;try{g=this._zoneDelegate.scheduleTask(this,g)}catch(C){throw g._transitionTo(se,E,P),this._zoneDelegate.handleError(this,C),C}return g._zoneDelegates===S&&this._updateTaskCount(g,1),g.state==E&&g._transitionTo(d,E),g}scheduleMicroTask(g,S,C,G){return this.scheduleTask(new u(ne,g,S,C,G,void 0))}scheduleMacroTask(g,S,C,G,ee){return this.scheduleTask(new u(Y,g,S,C,G,ee))}scheduleEventTask(g,S,C,G,ee){return this.scheduleTask(new u(V,g,S,C,G,ee))}cancelTask(g){if(g.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(g.zone||J).name+"; Execution: "+this.name+")");if(!(g.state!==d&&g.state!==w)){g._transitionTo(m,d,w);try{this._zoneDelegate.cancelTask(this,g)}catch(S){throw g._transitionTo(se,m),this._zoneDelegate.handleError(this,S),S}return this._updateTaskCount(g,-1),g._transitionTo(P,m),g.runCount=-1,g}}_updateTaskCount(g,S){let C=g._zoneDelegates;S==-1&&(g._zoneDelegates=null);for(let G=0;G<C.length;G++)C[G]._updateTaskCount(g.type,S)}}let n={name:"",onHasTask:(N,g,S,C)=>N.hasTask(S,C),onScheduleTask:(N,g,S,C)=>N.scheduleTask(S,C),onInvokeTask:(N,g,S,C,G,ee)=>N.invokeTask(S,C,G,ee),onCancelTask:(N,g,S,C)=>N.cancelTask(S,C)};class i{get zone(){return this._zone}_zone;_taskCounts={microTask:0,macroTask:0,eventTask:0};_parentDelegate;_forkDlgt;_forkZS;_forkCurrZone;_interceptDlgt;_interceptZS;_interceptCurrZone;_invokeDlgt;_invokeZS;_invokeCurrZone;_handleErrorDlgt;_handleErrorZS;_handleErrorCurrZone;_scheduleTaskDlgt;_scheduleTaskZS;_scheduleTaskCurrZone;_invokeTaskDlgt;_invokeTaskZS;_invokeTaskCurrZone;_cancelTaskDlgt;_cancelTaskZS;_cancelTaskCurrZone;_hasTaskDlgt;_hasTaskDlgtOwner;_hasTaskZS;_hasTaskCurrZone;constructor(g,S,C){this._zone=g,this._parentDelegate=S,this._forkZS=C&&(C&&C.onFork?C:S._forkZS),this._forkDlgt=C&&(C.onFork?S:S._forkDlgt),this._forkCurrZone=C&&(C.onFork?this._zone:S._forkCurrZone),this._interceptZS=C&&(C.onIntercept?C:S._interceptZS),this._interceptDlgt=C&&(C.onIntercept?S:S._interceptDlgt),this._interceptCurrZone=C&&(C.onIntercept?this._zone:S._interceptCurrZone),this._invokeZS=C&&(C.onInvoke?C:S._invokeZS),this._invokeDlgt=C&&(C.onInvoke?S:S._invokeDlgt),this._invokeCurrZone=C&&(C.onInvoke?this._zone:S._invokeCurrZone),this._handleErrorZS=C&&(C.onHandleError?C:S._handleErrorZS),this._handleErrorDlgt=C&&(C.onHandleError?S:S._handleErrorDlgt),this._handleErrorCurrZone=C&&(C.onHandleError?this._zone:S._handleErrorCurrZone),this._scheduleTaskZS=C&&(C.onScheduleTask?C:S._scheduleTaskZS),this._scheduleTaskDlgt=C&&(C.onScheduleTask?S:S._scheduleTaskDlgt),this._scheduleTaskCurrZone=C&&(C.onScheduleTask?this._zone:S._scheduleTaskCurrZone),this._invokeTaskZS=C&&(C.onInvokeTask?C:S._invokeTaskZS),this._invokeTaskDlgt=C&&(C.onInvokeTask?S:S._invokeTaskDlgt),this._invokeTaskCurrZone=C&&(C.onInvokeTask?this._zone:S._invokeTaskCurrZone),this._cancelTaskZS=C&&(C.onCancelTask?C:S._cancelTaskZS),this._cancelTaskDlgt=C&&(C.onCancelTask?S:S._cancelTaskDlgt),this._cancelTaskCurrZone=C&&(C.onCancelTask?this._zone:S._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;let G=C&&C.onHasTask,ee=S&&S._hasTaskZS;(G||ee)&&(this._hasTaskZS=G?C:n,this._hasTaskDlgt=S,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,C.onScheduleTask||(this._scheduleTaskZS=n,this._scheduleTaskDlgt=S,this._scheduleTaskCurrZone=this._zone),C.onInvokeTask||(this._invokeTaskZS=n,this._invokeTaskDlgt=S,this._invokeTaskCurrZone=this._zone),C.onCancelTask||(this._cancelTaskZS=n,this._cancelTaskDlgt=S,this._cancelTaskCurrZone=this._zone))}fork(g,S){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,g,S):new c(g,S)}intercept(g,S,C){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,g,S,C):S}invoke(g,S,C,G,ee){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,g,S,C,G,ee):S.apply(C,G)}handleError(g,S){return this._handleErrorZS?this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,g,S):!0}scheduleTask(g,S){let C=S;if(this._scheduleTaskZS)this._hasTaskZS&&C._zoneDelegates.push(this._hasTaskDlgtOwner),C=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,g,S),C||(C=S);else if(S.scheduleFn)S.scheduleFn(S);else if(S.type==ne)R(S);else throw new Error("Task is missing scheduleFn.");return C}invokeTask(g,S,C,G){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,g,S,C,G):S.callback.apply(C,G)}cancelTask(g,S){let C;if(this._cancelTaskZS)C=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,g,S);else{if(!S.cancelFn)throw Error("Task is not cancelable");C=S.cancelFn(S)}return C}hasTask(g,S){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,g,S)}catch(C){this.handleError(g,C)}}_updateTaskCount(g,S){let C=this._taskCounts,G=C[g],ee=C[g]=G+S;if(ee<0)throw new Error("More tasks executed then were scheduled.");if(G==0||ee==0){let y={microTask:C.microTask>0,macroTask:C.macroTask>0,eventTask:C.eventTask>0,change:g};this.hasTask(this._zone,y)}}}class u{type;source;invoke;callback;data;scheduleFn;cancelFn;_zone=null;runCount=0;_zoneDelegates=null;_state="notScheduled";constructor(g,S,C,G,ee,y){if(this.type=g,this.source=S,this.data=G,this.scheduleFn=ee,this.cancelFn=y,!C)throw new Error("callback is not defined");this.callback=C;let M=this;g===V&&G&&G.useG?this.invoke=u.invokeTask:this.invoke=function(){return u.invokeTask.call(ct,M,this,arguments)}}static invokeTask(g,S,C){g||(g=this),r++;try{return g.runCount++,g.zone.runTask(g,S,C)}finally{r==1&&I(),r--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(P,E)}_transitionTo(g,S,C){if(this._state===S||this._state===C)this._state=g,g==P&&(this._zoneDelegates=null);else throw new Error(`${this.type} '${this.source}': can not transition to '${g}', expecting state '${S}'${C?" or '"+C+"'":""}, was '${this._state}'.`)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}let o=lt("setTimeout"),f=lt("Promise"),s=lt("then"),b=[],p=!1,T;function O(N){if(T||ct[f]&&(T=ct[f].resolve(0)),T){let g=T[s];g||(g=T.then),g.call(T,N)}else ct[o](N,0)}function R(N){r===0&&b.length===0&&O(I),N&&b.push(N)}function I(){if(!p){for(p=!0;b.length;){let N=b;b=[];for(let g=0;g<N.length;g++){let S=N[g];try{S.zone.runTask(S,null,null)}catch(C){B.onUnhandledError(C)}}}B.microtaskDrainDone(),p=!1}}let J={name:"NO ZONE"},P="notScheduled",E="scheduling",d="scheduled",w="running",m="canceling",se="unknown",ne="microTask",Y="macroTask",V="eventTask",D={},B={symbol:lt,currentZoneFrame:()=>K,onUnhandledError:l,microtaskDrainDone:l,scheduleMicroTask:R,showUncaughtError:()=>!c[lt("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:l,patchMethod:()=>l,bindArguments:()=>[],patchThen:()=>l,patchMacroTask:()=>l,patchEventPrototype:()=>l,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>l,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>l,wrapWithCurrentZone:()=>l,filterProperties:()=>[],attachOriginToPatched:()=>l,_redefineProperty:()=>l,patchCallbacks:()=>l,nativeScheduleMicroTask:O},K={parent:null,zone:new c(null,null)},e=null,r=0;function l(){}return h("Zone","Zone"),c}var vn=Object.getOwnPropertyDescriptor,Ii=Object.defineProperty,ta=Object.getPrototypeOf,Hi=Array.prototype.slice,qi="addEventListener",Ri="removeEventListener",It="true",Ht="false",Zr=lt("");function Pi(_,k){return Zone.current.wrap(_,k)}function ra(_,k,h,c,n){return Zone.current.scheduleMacroTask(_,k,h,c,n)}var je=lt,Xr=typeof window<"u",Qr=Xr?window:void 0,$e=Xr&&Qr||globalThis,Bi="removeAttribute";function Fi(_,k){for(let h=_.length-1;h>=0;h--)typeof _[h]=="function"&&(_[h]=Pi(_[h],k+"_"+h));return _}function Ui(_){return _?_.writable===!1?!1:!(typeof _.get=="function"&&typeof _.set>"u"):!0}var na=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,aa=!("nw"in $e)&&typeof $e.process<"u"&&$e.process.toString()==="[object process]",ji=!aa&&!na&&!!(Xr&&Qr.HTMLElement),Qn=typeof $e.process<"u"&&$e.process.toString()==="[object process]"&&!na&&!!(Xr&&Qr.HTMLElement),Wr={},Vi=je("enable_beforeunload"),Yn=function(_){if(_=_||$e.event,!_)return;let k=Wr[_.type];k||(k=Wr[_.type]=je("ON_PROPERTY"+_.type));let h=this||_.target||$e,c=h[k],n;if(ji&&h===Qr&&_.type==="error"){let i=_;n=c&&c.call(this,i.message,i.filename,i.lineno,i.colno,i.error),n===!0&&_.preventDefault()}else n=c&&c.apply(this,arguments),_.type==="beforeunload"&&$e[Vi]&&typeof n=="string"?_.returnValue=n:n!=null&&!n&&_.preventDefault();return n};function $n(_,k,h){let c=vn(_,k);if(!c&&h&&vn(h,k)&&(c={enumerable:!0,configurable:!0}),!c||!c.configurable)return;let n=je("on"+k+"patched");if(_.hasOwnProperty(n)&&_[n])return;delete c.writable,delete c.value;let i=c.get,u=c.set,o=k.slice(2),f=Wr[o];f||(f=Wr[o]=je("ON_PROPERTY"+o)),c.set=function(s){let b=this;if(!b&&_===$e&&(b=$e),!b)return;typeof b[f]=="function"&&b.removeEventListener(o,Yn),u?.call(b,null),b[f]=s,typeof s=="function"&&b.addEventListener(o,Yn,!1)},c.get=function(){let s=this;if(!s&&_===$e&&(s=$e),!s)return null;let b=s[f];if(b)return b;if(i){let p=i.call(this);if(p)return c.set.call(this,p),typeof s[Bi]=="function"&&s.removeAttribute(k),p}return null},Ii(_,k,c),_[n]=!0}function Gi(_,k,h){if(k)for(let c=0;c<k.length;c++)$n(_,"on"+k[c],h);else{let c=[];for(let n in _)n.slice(0,2)=="on"&&c.push(n);for(let n=0;n<c.length;n++)$n(_,c[n],h)}}function zi(_,k){if(typeof Object.getOwnPropertySymbols!="function")return;Object.getOwnPropertySymbols(_).forEach(c=>{let n=Object.getOwnPropertyDescriptor(_,c);Object.defineProperty(k,c,{get:function(){return _[c]},set:function(i){n&&(!n.writable||typeof n.set!="function")||(_[c]=i)},enumerable:n?n.enumerable:!0,configurable:n?n.configurable:!0})})}var ia=!1;function Zi(_){ia=_}function tr(_,k,h){let c=_;for(;c&&!c.hasOwnProperty(k);)c=ta(c);!c&&_[k]&&(c=_);let n=je(k),i=null;if(c&&(!(i=c[n])||!c.hasOwnProperty(n))){i=c[n]=c[k];let u=c&&vn(c,k);if(Ui(u)){let o=h(i,n,k);c[k]=function(){return o(this,arguments)},gr(c[k],i),ia&&zi(i,c[k])}}return i}function Kr(_,k,h){let c=null;function n(i){let u=i.data;return u.args[u.cbIdx]=function(){i.invoke.apply(this,arguments)},c.apply(u.target,u.args),i}c=tr(_,k,i=>function(u,o){let f=h(u,o);return f.cbIdx>=0&&typeof o[f.cbIdx]=="function"?ra(f.name,o[f.cbIdx],f,n):i.apply(u,o)})}function Wi(_,k,h){let c=null;function n(i){let u=i.data;return u.args[u.cbIdx]=function(){i.invoke.apply(this,arguments)},c.apply(u.target,u.args),i}c=tr(_,k,i=>function(u,o){let f=h(u,o);return f.cbIdx>=0&&typeof o[f.cbIdx]=="function"?Zone.current.scheduleMicroTask(f.name,o[f.cbIdx],f,n):i.apply(u,o)})}function gr(_,k){_[je("OriginalDelegate")]=k}function Jn(_){return typeof _=="function"}function ea(_){return typeof _=="number"}function Ki(_){_.__load_patch("ZoneAwarePromise",(k,h,c)=>{let n=Object.getOwnPropertyDescriptor,i=Object.defineProperty;function u(Q){if(Q&&Q.toString===Object.prototype.toString){let z=Q.constructor&&Q.constructor.name;return(z||"")+": "+JSON.stringify(Q)}return Q?Q.toString():Object.prototype.toString.call(Q)}let o=c.symbol,f=[],s=k[o("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")]!==!1,b=o("Promise"),p=o("then"),T="__creationTrace__";c.onUnhandledError=Q=>{if(c.showUncaughtError()){let z=Q&&Q.rejection;z?console.error("Unhandled Promise rejection:",z instanceof Error?z.message:z,"; Zone:",Q.zone.name,"; Task:",Q.task&&Q.task.source,"; Value:",z,z instanceof Error?z.stack:void 0):console.error(Q)}},c.microtaskDrainDone=()=>{for(;f.length;){let Q=f.shift();try{Q.zone.runGuarded(()=>{throw Q.throwOriginal?Q.rejection:Q})}catch(z){R(z)}}};let O=o("unhandledPromiseRejectionHandler");function R(Q){c.onUnhandledError(Q);try{let z=h[O];typeof z=="function"&&z.call(this,Q)}catch{}}function I(Q){return Q&&typeof Q.then=="function"}function J(Q){return Q}function P(Q){return M.reject(Q)}let E=o("state"),d=o("value"),w=o("finally"),m=o("parentPromiseValue"),se=o("parentPromiseState"),ne="Promise.then",Y=null,V=!0,D=!1,B=0;function K(Q,z){return H=>{try{N(Q,z,H)}catch(Z){N(Q,!1,Z)}}}let e=function(){let Q=!1;return function(H){return function(){Q||(Q=!0,H.apply(null,arguments))}}},r="Promise resolved with itself",l=o("currentTaskTrace");function N(Q,z,H){let Z=e();if(Q===H)throw new TypeError(r);if(Q[E]===Y){let ce=null;try{(typeof H=="object"||typeof H=="function")&&(ce=H&&H.then)}catch(he){return Z(()=>{N(Q,!1,he)})(),Q}if(z!==D&&H instanceof M&&H.hasOwnProperty(E)&&H.hasOwnProperty(d)&&H[E]!==Y)S(H),N(Q,H[E],H[d]);else if(z!==D&&typeof ce=="function")try{ce.call(H,Z(K(Q,z)),Z(K(Q,!1)))}catch(he){Z(()=>{N(Q,!1,he)})()}else{Q[E]=z;let he=Q[d];if(Q[d]=H,Q[w]===w&&z===V&&(Q[E]=Q[se],Q[d]=Q[m]),z===D&&H instanceof Error){let ae=h.currentTask&&h.currentTask.data&&h.currentTask.data[T];ae&&i(H,l,{configurable:!0,enumerable:!1,writable:!0,value:ae})}for(let ae=0;ae<he.length;)C(Q,he[ae++],he[ae++],he[ae++],he[ae++]);if(he.length==0&&z==D){Q[E]=B;let ae=H;try{throw new Error("Uncaught (in promise): "+u(H)+(H&&H.stack?`
`+H.stack:""))}catch(me){ae=me}s&&(ae.throwOriginal=!0),ae.rejection=H,ae.promise=Q,ae.zone=h.current,ae.task=h.currentTask,f.push(ae),c.scheduleMicroTask()}}}return Q}let g=o("rejectionHandledHandler");function S(Q){if(Q[E]===B){try{let z=h[g];z&&typeof z=="function"&&z.call(this,{rejection:Q[d],promise:Q})}catch{}Q[E]=D;for(let z=0;z<f.length;z++)Q===f[z].promise&&f.splice(z,1)}}function C(Q,z,H,Z,ce){S(Q);let he=Q[E],ae=he?typeof Z=="function"?Z:J:typeof ce=="function"?ce:P;z.scheduleMicroTask(ne,()=>{try{let me=Q[d],ge=!!H&&w===H[w];ge&&(H[m]=me,H[se]=he);let be=z.run(ae,void 0,ge&&ae!==P&&ae!==J?[]:[me]);N(H,!0,be)}catch(me){N(H,!1,me)}},H)}let G="function ZoneAwarePromise() { [native code] }",ee=function(){},y=k.AggregateError;class M{static toString(){return G}static resolve(z){return z instanceof M?z:N(new this(null),V,z)}static reject(z){return N(new this(null),D,z)}static withResolvers(){let z={};return z.promise=new M((H,Z)=>{z.resolve=H,z.reject=Z}),z}static any(z){if(!z||typeof z[Symbol.iterator]!="function")return Promise.reject(new y([],"All promises were rejected"));let H=[],Z=0;try{for(let ae of z)Z++,H.push(M.resolve(ae))}catch{return Promise.reject(new y([],"All promises were rejected"))}if(Z===0)return Promise.reject(new y([],"All promises were rejected"));let ce=!1,he=[];return new M((ae,me)=>{for(let ge=0;ge<H.length;ge++)H[ge].then(be=>{ce||(ce=!0,ae(be))},be=>{he.push(be),Z--,Z===0&&(ce=!0,me(new y(he,"All promises were rejected")))})})}static race(z){let H,Z,ce=new this((me,ge)=>{H=me,Z=ge});function he(me){H(me)}function ae(me){Z(me)}for(let me of z)I(me)||(me=this.resolve(me)),me.then(he,ae);return ce}static all(z){return M.allWithCallback(z)}static allSettled(z){return(this&&this.prototype instanceof M?this:M).allWithCallback(z,{thenCallback:Z=>({status:"fulfilled",value:Z}),errorCallback:Z=>({status:"rejected",reason:Z})})}static allWithCallback(z,H){let Z,ce,he=new this((be,Se)=>{Z=be,ce=Se}),ae=2,me=0,ge=[];for(let be of z){I(be)||(be=this.resolve(be));let Se=me;try{be.then(Ce=>{ge[Se]=H?H.thenCallback(Ce):Ce,ae--,ae===0&&Z(ge)},Ce=>{H?(ge[Se]=H.errorCallback(Ce),ae--,ae===0&&Z(ge)):ce(Ce)})}catch(Ce){ce(Ce)}ae++,me++}return ae-=2,ae===0&&Z(ge),he}constructor(z){let H=this;if(!(H instanceof M))throw new Error("Must be an instanceof Promise.");H[E]=Y,H[d]=[];try{let Z=e();z&&z(Z(K(H,V)),Z(K(H,D)))}catch(Z){N(H,!1,Z)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return M}then(z,H){let Z=this.constructor?.[Symbol.species];(!Z||typeof Z!="function")&&(Z=this.constructor||M);let ce=new Z(ee),he=h.current;return this[E]==Y?this[d].push(he,ce,z,H):C(this,he,ce,z,H),ce}catch(z){return this.then(null,z)}finally(z){let H=this.constructor?.[Symbol.species];(!H||typeof H!="function")&&(H=M);let Z=new H(ee);Z[w]=w;let ce=h.current;return this[E]==Y?this[d].push(ce,Z,z,z):C(this,ce,Z,z,z),Z}}M.resolve=M.resolve,M.reject=M.reject,M.race=M.race,M.all=M.all;let X=k[b]=k.Promise;k.Promise=M;let de=o("thenPatched");function xe(Q){let z=Q.prototype,H=n(z,"then");if(H&&(H.writable===!1||!H.configurable))return;let Z=z.then;z[p]=Z,Q.prototype.then=function(ce,he){return new M((me,ge)=>{Z.call(this,me,ge)}).then(ce,he)},Q[de]=!0}c.patchThen=xe;function bt(Q){return function(z,H){let Z=Q.apply(z,H);if(Z instanceof M)return Z;let ce=Z.constructor;return ce[de]||xe(ce),Z}}return X&&(xe(X),tr(k,"fetch",Q=>bt(Q))),Promise[h.__symbol__("uncaughtPromiseErrors")]=f,M})}function Xi(_){_.__load_patch("toString",k=>{let h=Function.prototype.toString,c=je("OriginalDelegate"),n=je("Promise"),i=je("Error"),u=function(){if(typeof this=="function"){let b=this[c];if(b)return typeof b=="function"?h.call(b):Object.prototype.toString.call(b);if(this===Promise){let p=k[n];if(p)return h.call(p)}if(this===Error){let p=k[i];if(p)return h.call(p)}}return h.call(this)};u[c]=h,Function.prototype.toString=u;let o=Object.prototype.toString,f="[object Promise]";Object.prototype.toString=function(){return typeof Promise=="function"&&this instanceof Promise?f:o.call(this)}})}function Qi(){let _=globalThis,k=_[lt("forceDuplicateZoneCheck")]===!0;if(_.Zone&&(k||typeof _.Zone.__symbol__!="function"))throw new Error("Zone already loaded.");return _.Zone??=Oi(),_.Zone}var Yi={useG:!0},rt={},$i={},sa=new RegExp("^"+Zr+"(\\w+)(true|false)$"),Ji=je("propagationStopped");function oa(_,k){let h=(k?k(_):_)+Ht,c=(k?k(_):_)+It,n=Zr+h,i=Zr+c;rt[_]={},rt[_][Ht]=n,rt[_][It]=i}function es(_,k,h,c){let n=c&&c.add||qi,i=c&&c.rm||Ri,u=c&&c.listeners||"eventListeners",o=c&&c.rmAll||"removeAllListeners",f=je(n),s="."+n+":",b="prependListener",p="."+b+":",T=function(E,d,w){if(E.isRemoved)return;let m=E.callback;typeof m=="object"&&m.handleEvent&&(E.callback=Y=>m.handleEvent(Y),E.originalDelegate=m);let se;try{E.invoke(E,d,[w])}catch(Y){se=Y}let ne=E.options;if(ne&&typeof ne=="object"&&ne.once){let Y=E.originalDelegate?E.originalDelegate:E.callback;d[i].call(d,w.type,Y,ne)}return se};function O(E,d,w){if(d=d||_.event,!d)return;let m=E||d.target||_,se=m[rt[d.type][w?It:Ht]];if(se){let ne=[];if(se.length===1){let Y=T(se[0],m,d);Y&&ne.push(Y)}else{let Y=se.slice();for(let V=0;V<Y.length&&!(d&&d[Ji]===!0);V++){let D=T(Y[V],m,d);D&&ne.push(D)}}if(ne.length===1)throw ne[0];for(let Y=0;Y<ne.length;Y++){let V=ne[Y];k.nativeScheduleMicroTask(()=>{throw V})}}}let R=function(E){return O(this,E,!1)},I=function(E){return O(this,E,!0)};function J(E,d){if(!E)return!1;let w=!0;d&&d.useG!==void 0&&(w=d.useG);let m=d&&d.vh,se=!0;d&&d.chkDup!==void 0&&(se=d.chkDup);let ne=!1;d&&d.rt!==void 0&&(ne=d.rt);let Y=E;for(;Y&&!Y.hasOwnProperty(n);)Y=ta(Y);if(!Y&&E[n]&&(Y=E),!Y||Y[f])return!1;let V=d&&d.eventNameToString,D={},B=Y[f]=Y[n],K=Y[je(i)]=Y[i],e=Y[je(u)]=Y[u],r=Y[je(o)]=Y[o],l;d&&d.prepend&&(l=Y[je(d.prepend)]=Y[d.prepend]);function N(H,Z){return Z?typeof H=="boolean"?{capture:H,passive:!0}:H?typeof H=="object"&&H.passive!==!1?{...H,passive:!0}:H:{passive:!0}:H}let g=function(H){if(!D.isExisting)return B.call(D.target,D.eventName,D.capture?I:R,D.options)},S=function(H){if(!H.isRemoved){let Z=rt[H.eventName],ce;Z&&(ce=Z[H.capture?It:Ht]);let he=ce&&H.target[ce];if(he){for(let ae=0;ae<he.length;ae++)if(he[ae]===H){he.splice(ae,1),H.isRemoved=!0,H.removeAbortListener&&(H.removeAbortListener(),H.removeAbortListener=null),he.length===0&&(H.allRemoved=!0,H.target[ce]=null);break}}}if(H.allRemoved)return K.call(H.target,H.eventName,H.capture?I:R,H.options)},C=function(H){return B.call(D.target,D.eventName,H.invoke,D.options)},G=function(H){return l.call(D.target,D.eventName,H.invoke,D.options)},ee=function(H){return K.call(H.target,H.eventName,H.invoke,H.options)},y=w?g:C,M=w?S:ee,X=function(H,Z){let ce=typeof Z;return ce==="function"&&H.callback===Z||ce==="object"&&H.originalDelegate===Z},de=d?.diff||X,xe=Zone[je("UNPATCHED_EVENTS")],bt=_[je("PASSIVE_EVENTS")];function Q(H){if(typeof H=="object"&&H!==null){let Z={...H};return H.signal&&(Z.signal=H.signal),Z}return H}let z=function(H,Z,ce,he,ae=!1,me=!1){return function(){let ge=this||_,be=arguments[0];d&&d.transferEventName&&(be=d.transferEventName(be));let Se=arguments[1];if(!Se)return H.apply(this,arguments);if(aa&&be==="uncaughtException")return H.apply(this,arguments);let Ce=!1;if(typeof Se!="function"){if(!Se.handleEvent)return H.apply(this,arguments);Ce=!0}if(m&&!m(H,Se,ge,arguments))return;let nt=!!bt&&bt.indexOf(be)!==-1,Ue=Q(N(arguments[2],nt)),Je=Ue?.signal;if(Je?.aborted)return;if(xe){for(let U=0;U<xe.length;U++)if(be===xe[U])return nt?H.call(ge,be,Se,Ue):H.apply(this,arguments)}let Fe=Ue?typeof Ue=="boolean"?!0:Ue.capture:!1,vr=Ue&&typeof Ue=="object"?Ue.once:!1,we=Zone.current,Wt=rt[be];Wt||(oa(be,V),Wt=rt[be]);let nr=Wt[Fe?It:Ht],_t=ge[nr],ar=!1;if(_t){if(ar=!0,se){for(let U=0;U<_t.length;U++)if(de(_t[U],Se))return}}else _t=ge[nr]=[];let qt,Kt=ge.constructor.name,ir=$i[Kt];ir&&(qt=ir[be]),qt||(qt=Kt+Z+(V?V(be):be)),D.options=Ue,vr&&(D.options.once=!1),D.target=ge,D.capture=Fe,D.eventName=be,D.isExisting=ar;let ve=w?Yi:void 0;ve&&(ve.taskData=D),Je&&(D.options.signal=void 0);let F=we.scheduleEventTask(qt,Se,ve,ce,he);if(Je){D.options.signal=Je;let U=()=>F.zone.cancelTask(F);H.call(Je,"abort",U,{once:!0}),F.removeAbortListener=()=>Je.removeEventListener("abort",U)}if(D.target=null,ve&&(ve.taskData=null),vr&&(D.options.once=!0),typeof F.options!="boolean"&&(F.options=Ue),F.target=ge,F.capture=Fe,F.eventName=be,Ce&&(F.originalDelegate=Se),me?_t.unshift(F):_t.push(F),ae)return ge}};return Y[n]=z(B,s,y,M,ne),l&&(Y[b]=z(l,p,G,M,ne,!0)),Y[i]=function(){let H=this||_,Z=arguments[0];d&&d.transferEventName&&(Z=d.transferEventName(Z));let ce=arguments[2],he=ce?typeof ce=="boolean"?!0:ce.capture:!1,ae=arguments[1];if(!ae)return K.apply(this,arguments);if(m&&!m(K,ae,H,arguments))return;let me=rt[Z],ge;me&&(ge=me[he?It:Ht]);let be=ge&&H[ge];if(be)for(let Se=0;Se<be.length;Se++){let Ce=be[Se];if(de(Ce,ae)){if(be.splice(Se,1),Ce.isRemoved=!0,be.length===0&&(Ce.allRemoved=!0,H[ge]=null,!he&&typeof Z=="string")){let nt=Zr+"ON_PROPERTY"+Z;H[nt]=null}return Ce.zone.cancelTask(Ce),ne?H:void 0}}return K.apply(this,arguments)},Y[u]=function(){let H=this||_,Z=arguments[0];d&&d.transferEventName&&(Z=d.transferEventName(Z));let ce=[],he=ca(H,V?V(Z):Z);for(let ae=0;ae<he.length;ae++){let me=he[ae],ge=me.originalDelegate?me.originalDelegate:me.callback;ce.push(ge)}return ce},Y[o]=function(){let H=this||_,Z=arguments[0];if(Z){d&&d.transferEventName&&(Z=d.transferEventName(Z));let ce=rt[Z];if(ce){let he=ce[Ht],ae=ce[It],me=H[he],ge=H[ae];if(me){let be=me.slice();for(let Se=0;Se<be.length;Se++){let Ce=be[Se],nt=Ce.originalDelegate?Ce.originalDelegate:Ce.callback;this[i].call(this,Z,nt,Ce.options)}}if(ge){let be=ge.slice();for(let Se=0;Se<be.length;Se++){let Ce=be[Se],nt=Ce.originalDelegate?Ce.originalDelegate:Ce.callback;this[i].call(this,Z,nt,Ce.options)}}}}else{let ce=Object.keys(H);for(let he=0;he<ce.length;he++){let ae=ce[he],me=sa.exec(ae),ge=me&&me[1];ge&&ge!=="removeListener"&&this[o].call(this,ge)}this[o].call(this,"removeListener")}if(ne)return this},gr(Y[n],B),gr(Y[i],K),r&&gr(Y[o],r),e&&gr(Y[u],e),!0}let P=[];for(let E=0;E<h.length;E++)P[E]=J(h[E],c);return P}function ca(_,k){if(!k){let i=[];for(let u in _){let o=sa.exec(u),f=o&&o[1];if(f&&(!k||f===k)){let s=_[u];if(s)for(let b=0;b<s.length;b++)i.push(s[b])}}return i}let h=rt[k];h||(oa(k),h=rt[k]);let c=_[h[Ht]],n=_[h[It]];return c?n?c.concat(n):c.slice():n?n.slice():[]}function ts(_,k){k.patchMethod(_,"queueMicrotask",h=>function(c,n){Zone.current.scheduleMicroTask("queueMicrotask",n[0])})}var zr=je("zoneTask");function $t(_,k,h,c){let n=null,i=null;k+=c,h+=c;let u={};function o(s){let b=s.data;b.args[0]=function(){return s.invoke.apply(this,arguments)};let p=n.apply(_,b.args);return ea(p)?b.handleId=p:(b.handle=p,b.isRefreshable=Jn(p.refresh)),s}function f(s){let{handle:b,handleId:p}=s.data;return i.call(_,b??p)}n=tr(_,k,s=>function(b,p){if(Jn(p[0])){let T={isRefreshable:!1,isPeriodic:c==="Interval",delay:c==="Timeout"||c==="Interval"?p[1]||0:void 0,args:p},O=p[0];p[0]=function(){try{return O.apply(this,arguments)}finally{let{handle:w,handleId:m,isPeriodic:se,isRefreshable:ne}=T;!se&&!ne&&(m?delete u[m]:w&&(w[zr]=null))}};let R=ra(k,p[0],T,o,f);if(!R)return R;let{handleId:I,handle:J,isRefreshable:P,isPeriodic:E}=R.data;if(I)u[I]=R;else if(J&&(J[zr]=R,P&&!E)){let d=J.refresh;J.refresh=function(){let{zone:w,state:m}=R;return m==="notScheduled"?(R._state="scheduled",w._updateTaskCount(R,1)):m==="running"&&(R._state="scheduling"),d.call(this)}}return J??I??R}else return s.apply(_,p)}),i=tr(_,h,s=>function(b,p){let T=p[0],O;ea(T)?(O=u[T],delete u[T]):(O=T?.[zr],O?T[zr]=null:O=T),O?.type?O.cancelFn&&O.zone.cancelTask(O):s.apply(_,p)})}function rs(_){_.__load_patch("EventEmitter",(k,h,c)=>{let n="addListener",i="prependListener",u="removeListener",o="removeAllListeners",f="listeners",s="on",b="off",p=function(I,J){return I.callback===J||I.callback.listener===J},T=function(I){return typeof I=="string"?I:I?I.toString().replace("(","_").replace(")","_"):""};function O(I){let J=es(k,c,[I],{useG:!1,add:n,rm:u,prepend:i,rmAll:o,listeners:f,chkDup:!1,rt:!0,diff:p,eventNameToString:T});J&&J[0]&&(I[s]=I[n],I[b]=I[u])}let R;try{R=Gr("events")}catch{}R&&R.EventEmitter&&O(R.EventEmitter.prototype)})}function ns(_){_.__load_patch("fs",(k,h,c)=>{let n;try{n=Gr("fs")}catch{}if(!n)return;["access","appendFile","chmod","chown","close","exists","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","lutimes","link","lstat","mkdir","mkdtemp","open","opendir","read","readdir","readFile","readlink","realpath","rename","rmdir","stat","symlink","truncate","unlink","utimes","write","writeFile","writev"].filter(o=>!!n[o]&&typeof n[o]=="function").forEach(o=>{Kr(n,o,(f,s)=>({name:"fs."+o,args:s,cbIdx:s.length>0?s.length-1:-1,target:f}))});let u=n.realpath?.[c.symbol("OriginalDelegate")];u?.native&&(n.realpath.native=u.native,Kr(n.realpath,"native",(o,f)=>({args:f,target:o,cbIdx:f.length>0?f.length-1:-1,name:"fs.realpath.native"})))})}function as(_){_.__load_patch("node_util",(k,h,c)=>{c.patchOnProperties=Gi,c.patchMethod=tr,c.bindArguments=Fi,c.patchMacroTask=Kr,Zi(!0)})}var Jt="set",er="clear";function is(_){as(_),rs(_),ns(_),_.__load_patch("node_timers",(k,h)=>{let c=!1;try{let n=Gr("timers");if(!(k.setTimeout===n.setTimeout)&&!Qn){let u=n.setTimeout;n.setTimeout=function(){return c=!0,u.apply(this,arguments)};let o=k.setTimeout(()=>{},100);clearTimeout(o),n.setTimeout=u}$t(n,Jt,er,"Timeout"),$t(n,Jt,er,"Interval"),$t(n,Jt,er,"Immediate")}catch{}Qn||(c?(k[h.__symbol__("setTimeout")]=k.setTimeout,k[h.__symbol__("setInterval")]=k.setInterval,k[h.__symbol__("setImmediate")]=k.setImmediate):($t(k,Jt,er,"Timeout"),$t(k,Jt,er,"Interval"),$t(k,Jt,er,"Immediate")))}),_.__load_patch("nextTick",()=>{Wi(process,"nextTick",(k,h)=>({name:"process.nextTick",args:h,cbIdx:h.length>0&&typeof h[0]=="function"?0:-1,target:process}))}),_.__load_patch("handleUnhandledPromiseRejection",(k,h,c)=>{h[c.symbol("unhandledPromiseRejectionHandler")]=n("unhandledRejection"),h[c.symbol("rejectionHandledHandler")]=n("rejectionHandled");function n(i){return function(u){ca(process,i).forEach(f=>{i==="unhandledRejection"?f.invoke(u.rejection,u.promise):i==="rejectionHandled"&&f.invoke(u.promise)})}}}),_.__load_patch("crypto",()=>{let k;try{k=Gr("crypto")}catch{}k&&["randomBytes","pbkdf2"].forEach(c=>{Kr(k,c,(n,i)=>({name:"crypto."+c,args:i,cbIdx:i.length>0&&typeof i[i.length-1]=="function"?i.length-1:-1,target:k}))})}),_.__load_patch("console",(k,h)=>{["dir","log","info","error","warn","assert","debug","timeEnd","trace"].forEach(n=>{let i=console[h.__symbol__(n)]=console[n];i&&(console[n]=function(){let u=Hi.call(arguments);return h.current===h.root?i.apply(this,u):h.root.run(i,this,u)})})}),_.__load_patch("queueMicrotask",(k,h,c)=>{ts(k,c)})}function ss(){let _=Qi();return is(_),Ki(_),Xi(_),_}ss();var os=Object.getOwnPropertyNames,le=(_,k)=>function(){return k||(0,_[os(_)[0]])((k={exports:{}}).exports,k),k.exports},br=le({"external/npm/node_modules/domino/lib/Event.js"(_,k){k.exports=h,h.CAPTURING_PHASE=1,h.AT_TARGET=2,h.BUBBLING_PHASE=3;function h(c,n){if(this.type="",this.target=null,this.currentTarget=null,this.eventPhase=h.AT_TARGET,this.bubbles=!1,this.cancelable=!1,this.isTrusted=!1,this.defaultPrevented=!1,this.timeStamp=Date.now(),this._propagationStopped=!1,this._immediatePropagationStopped=!1,this._initialized=!0,this._dispatching=!1,c&&(this.type=c),n)for(var i in n)this[i]=n[i]}h.prototype=Object.create(Object.prototype,{constructor:{value:h},stopPropagation:{value:function(){this._propagationStopped=!0}},stopImmediatePropagation:{value:function(){this._propagationStopped=!0,this._immediatePropagationStopped=!0}},preventDefault:{value:function(){this.cancelable&&(this.defaultPrevented=!0)}},initEvent:{value:function(n,i,u){this._initialized=!0,!this._dispatching&&(this._propagationStopped=!1,this._immediatePropagationStopped=!1,this.defaultPrevented=!1,this.isTrusted=!1,this.target=null,this.type=n,this.bubbles=i,this.cancelable=u)}}})}}),ua=le({"external/npm/node_modules/domino/lib/UIEvent.js"(_,k){var h=br();k.exports=c;function c(){h.call(this),this.view=null,this.detail=0}c.prototype=Object.create(h.prototype,{constructor:{value:c},initUIEvent:{value:function(n,i,u,o,f){this.initEvent(n,i,u),this.view=o,this.detail=f}}})}}),fa=le({"external/npm/node_modules/domino/lib/MouseEvent.js"(_,k){var h=ua();k.exports=c;function c(){h.call(this),this.screenX=this.screenY=this.clientX=this.clientY=0,this.ctrlKey=this.altKey=this.shiftKey=this.metaKey=!1,this.button=0,this.buttons=1,this.relatedTarget=null}c.prototype=Object.create(h.prototype,{constructor:{value:c},initMouseEvent:{value:function(n,i,u,o,f,s,b,p,T,O,R,I,J,P,E){switch(this.initEvent(n,i,u,o,f),this.screenX=s,this.screenY=b,this.clientX=p,this.clientY=T,this.ctrlKey=O,this.altKey=R,this.shiftKey=I,this.metaKey=J,this.button=P,P){case 0:this.buttons=1;break;case 1:this.buttons=4;break;case 2:this.buttons=2;break;default:this.buttons=0;break}this.relatedTarget=E}},getModifierState:{value:function(n){switch(n){case"Alt":return this.altKey;case"Control":return this.ctrlKey;case"Shift":return this.shiftKey;case"Meta":return this.metaKey;default:return!1}}}})}}),En=le({"external/npm/node_modules/domino/lib/config.js"(_){_.isApiWritable=!globalThis.__domino_frozen__}}),Be=le({"external/npm/node_modules/domino/lib/utils.js"(_){var k=En().isApiWritable;_.NAMESPACE={HTML:"http://www.w3.org/1999/xhtml",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/",MATHML:"http://www.w3.org/1998/Math/MathML",SVG:"http://www.w3.org/2000/svg",XLINK:"http://www.w3.org/1999/xlink"},_.IndexSizeError=()=>{throw new DOMException("The index is not in the allowed range","IndexSizeError")},_.HierarchyRequestError=()=>{throw new DOMException("The node tree hierarchy is not correct","HierarchyRequestError")},_.WrongDocumentError=()=>{throw new DOMException("The object is in the wrong Document","WrongDocumentError")},_.InvalidCharacterError=()=>{throw new DOMException("The string contains invalid characters","InvalidCharacterError")},_.NoModificationAllowedError=()=>{throw new DOMException("The object cannot be modified","NoModificationAllowedError")},_.NotFoundError=()=>{throw new DOMException("The object can not be found here","NotFoundError")},_.NotSupportedError=()=>{throw new DOMException("The operation is not supported","NotSupportedError")},_.InvalidStateError=()=>{throw new DOMException("The object is in an invalid state","InvalidStateError")},_.SyntaxError=()=>{throw new DOMException("The string did not match the expected pattern","SyntaxError")},_.InvalidModificationError=()=>{throw new DOMException("The object can not be modified in this way","InvalidModificationError")},_.NamespaceError=()=>{throw new DOMException("The operation is not allowed by Namespaces in XML","NamespaceError")},_.InvalidAccessError=()=>{throw new DOMException("The object does not support the operation or argument","InvalidAccessError")},_.TypeMismatchError=()=>{throw new DOMException("The type of the object does not match the expected type","TypeMismatchError")},_.SecurityError=()=>{throw new DOMException("The operation is insecure","SecurityError")},_.NetworkError=()=>{throw new DOMException("A network error occurred","NetworkError")},_.AbortError=()=>{throw new DOMException("The operation was aborted","AbortError")},_.UrlMismatchError=()=>{throw new DOMException("The given URL does not match another URL","URLMismatchError")},_.QuotaExceededError=()=>{throw new DOMException("The quota has been exceeded","QuotaExceededError")},_.TimeoutError=()=>{throw new DOMException("The operation timed out","TimeoutError")},_.InvalidNodeTypeError=()=>{throw new DOMException("The node is of an invalid type","InvalidNodeTypeError")},_.DataCloneError=()=>{throw new DOMException("The object can not be cloned","DataCloneError")},_.InUseAttributeError=()=>{throw new DOMException("The attribute is already in use","InUseAttributeError")},_.nyi=function(){throw new Error("NotYetImplemented")},_.shouldOverride=function(){throw new Error("Abstract function; should be overriding in subclass.")},_.assert=function(h,c){if(!h)throw new Error("Assertion failed: "+(c||"")+`
`+new Error().stack)},_.expose=function(h,c){for(var n in h)Object.defineProperty(c.prototype,n,{value:h[n],writable:k})},_.merge=function(h,c){for(var n in c)h[n]=c[n]},_.documentOrder=function(h,c){return 3-(h.compareDocumentPosition(c)&6)},_.toASCIILowerCase=function(h){return h.replace(/[A-Z]+/g,function(c){return c.toLowerCase()})},_.toASCIIUpperCase=function(h){return h.replace(/[a-z]+/g,function(c){return c.toUpperCase()})}}}),ha=le({"external/npm/node_modules/domino/lib/EventTarget.js"(_,k){var h=br(),c=fa(),n=Be();k.exports=i;function i(){}i.prototype={addEventListener:function(o,f,s){if(f){s===void 0&&(s=!1),this._listeners||(this._listeners=Object.create(null)),this._listeners[o]||(this._listeners[o]=[]);for(var b=this._listeners[o],p=0,T=b.length;p<T;p++){var O=b[p];if(O.listener===f&&O.capture===s)return}var R={listener:f,capture:s};typeof f=="function"&&(R.f=f),b.push(R)}},removeEventListener:function(o,f,s){if(s===void 0&&(s=!1),this._listeners){var b=this._listeners[o];if(b)for(var p=0,T=b.length;p<T;p++){var O=b[p];if(O.listener===f&&O.capture===s){b.length===1?this._listeners[o]=void 0:b.splice(p,1);return}}}},dispatchEvent:function(o){return this._dispatchEvent(o,!1)},_dispatchEvent:function(o,f){typeof f!="boolean"&&(f=!1);function s(I,J){var P=J.type,E=J.eventPhase;if(J.currentTarget=I,E!==h.CAPTURING_PHASE&&I._handlers&&I._handlers[P]){var d=I._handlers[P],w;if(typeof d=="function")w=d.call(J.currentTarget,J);else{var m=d.handleEvent;if(typeof m!="function")throw new TypeError("handleEvent property of event handler object isnot a function.");w=m.call(d,J)}switch(J.type){case"mouseover":w===!0&&J.preventDefault();break;case"beforeunload":default:w===!1&&J.preventDefault();break}}var se=I._listeners&&I._listeners[P];if(se){se=se.slice();for(var ne=0,Y=se.length;ne<Y;ne++){if(J._immediatePropagationStopped)return;var V=se[ne];if(!(E===h.CAPTURING_PHASE&&!V.capture||E===h.BUBBLING_PHASE&&V.capture))if(V.f)V.f.call(J.currentTarget,J);else{var D=V.listener.handleEvent;if(typeof D!="function")throw new TypeError("handleEvent property of event listener object is not a function.");D.call(V.listener,J)}}}}(!o._initialized||o._dispatching)&&n.InvalidStateError(),o.isTrusted=f,o._dispatching=!0,o.target=this;for(var b=[],p=this.parentNode;p;p=p.parentNode)b.push(p);o.eventPhase=h.CAPTURING_PHASE;for(var T=b.length-1;T>=0&&(s(b[T],o),!o._propagationStopped);T--);if(o._propagationStopped||(o.eventPhase=h.AT_TARGET,s(this,o)),o.bubbles&&!o._propagationStopped){o.eventPhase=h.BUBBLING_PHASE;for(var O=0,R=b.length;O<R&&(s(b[O],o),!o._propagationStopped);O++);}if(o._dispatching=!1,o.eventPhase=h.AT_TARGET,o.currentTarget=null,f&&!o.defaultPrevented&&o instanceof c)switch(o.type){case"mousedown":this._armed={x:o.clientX,y:o.clientY,t:o.timeStamp};break;case"mouseout":case"mouseover":this._armed=null;break;case"mouseup":this._isClick(o)&&this._doClick(o),this._armed=null;break}return!o.defaultPrevented},_isClick:function(u){return this._armed!==null&&u.type==="mouseup"&&u.isTrusted&&u.button===0&&u.timeStamp-this._armed.t<1e3&&Math.abs(u.clientX-this._armed.x)<10&&Math.abs(u.clientY-this._armed.Y)<10},_doClick:function(u){if(!this._click_in_progress){this._click_in_progress=!0;for(var o=this;o&&!o._post_click_activation_steps;)o=o.parentNode;o&&o._pre_click_activation_steps&&o._pre_click_activation_steps();var f=this.ownerDocument.createEvent("MouseEvent");f.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,u.screenX,u.screenY,u.clientX,u.clientY,u.ctrlKey,u.altKey,u.shiftKey,u.metaKey,u.button,null);var s=this._dispatchEvent(f,!0);o&&(s?o._post_click_activation_steps&&o._post_click_activation_steps(f):o._cancelled_activation_steps&&o._cancelled_activation_steps())}},_setEventHandler:function(o,f){this._handlers||(this._handlers=Object.create(null)),this._handlers[o]=f},_getEventHandler:function(o){return this._handlers&&this._handlers[o]||null}}}}),pa=le({"external/npm/node_modules/domino/lib/LinkedList.js"(_,k){var h=Be(),c=k.exports={valid:function(n){return h.assert(n,"list falsy"),h.assert(n._previousSibling,"previous falsy"),h.assert(n._nextSibling,"next falsy"),!0},insertBefore:function(n,i){h.assert(c.valid(n)&&c.valid(i));var u=n,o=n._previousSibling,f=i,s=i._previousSibling;u._previousSibling=s,o._nextSibling=f,s._nextSibling=u,f._previousSibling=o,h.assert(c.valid(n)&&c.valid(i))},replace:function(n,i){h.assert(c.valid(n)&&(i===null||c.valid(i))),i!==null&&c.insertBefore(i,n),c.remove(n),h.assert(c.valid(n)&&(i===null||c.valid(i)))},remove:function(n){h.assert(c.valid(n));var i=n._previousSibling;if(i!==n){var u=n._nextSibling;i._nextSibling=u,u._previousSibling=i,n._previousSibling=n._nextSibling=n,h.assert(c.valid(n))}}}}}),da=le({"external/npm/node_modules/domino/lib/NodeUtils.js"(_,k){k.exports={serializeOne:J,\u0275escapeMatchingClosingTag:T,\u0275escapeClosingCommentTag:R,\u0275escapeProcessingInstructionContent:I};var h=Be(),c=h.NAMESPACE,n={STYLE:!0,SCRIPT:!0,XMP:!0,IFRAME:!0,NOEMBED:!0,NOFRAMES:!0,PLAINTEXT:!0},i={area:!0,base:!0,basefont:!0,bgsound:!0,br:!0,col:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},u={},o=/[&<>\u00A0]/g,f=/[&"<>\u00A0]/g;function s(P){return o.test(P)?P.replace(o,E=>{switch(E){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case"\xA0":return"&nbsp;"}}):P}function b(P){return f.test(P)?P.replace(f,E=>{switch(E){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case'"':return"&quot;";case"\xA0":return"&nbsp;"}}):P}function p(P){var E=P.namespaceURI;return E?E===c.XML?"xml:"+P.localName:E===c.XLINK?"xlink:"+P.localName:E===c.XMLNS?P.localName==="xmlns"?"xmlns":"xmlns:"+P.localName:P.name:P.localName}function T(P,E){let d="</"+E;if(!P.toLowerCase().includes(d))return P;let w=[...P],m=P.matchAll(new RegExp(d,"ig"));for(let se of m)w[se.index]="&lt;";return w.join("")}var O=/--!?>/;function R(P){return O.test(P)?P.replace(/(--\!?)>/g,"$1&gt;"):P}function I(P){return P.includes(">")?P.replaceAll(">","&gt;"):P}function J(P,E){var d="";switch(P.nodeType){case 1:var w=P.namespaceURI,m=w===c.HTML,se=m||w===c.SVG||w===c.MATHML?P.localName:P.tagName;d+="<"+se;for(var ne=0,Y=P._numattrs;ne<Y;ne++){var V=P._attr(ne);d+=" "+p(V),V.value!==void 0&&(d+='="'+b(V.value)+'"')}if(d+=">",!(m&&i[se])){var D=P.serialize();n[se.toUpperCase()]&&(D=T(D,se)),m&&u[se]&&D.charAt(0)===`
`&&(d+=`
`),d+=D,d+="</"+se+">"}break;case 3:case 4:var B;E.nodeType===1&&E.namespaceURI===c.HTML?B=E.tagName:B="",n[B]||B==="NOSCRIPT"&&E.ownerDocument._scripting_enabled?d+=P.data:d+=s(P.data);break;case 8:d+="<!--"+R(P.data)+"-->";break;case 7:let K=I(P.data);d+="<?"+P.target+" "+K+"?>";break;case 10:d+="<!DOCTYPE "+P.name,d+=">";break;default:h.InvalidStateError()}return d}}}),Ge=le({"external/npm/node_modules/domino/lib/Node.js"(_,k){k.exports=u;var h=ha(),c=pa(),n=da(),i=Be();function u(){h.call(this),this.parentNode=null,this._nextSibling=this._previousSibling=this,this._index=void 0}var o=u.ELEMENT_NODE=1,f=u.ATTRIBUTE_NODE=2,s=u.TEXT_NODE=3,b=u.CDATA_SECTION_NODE=4,p=u.ENTITY_REFERENCE_NODE=5,T=u.ENTITY_NODE=6,O=u.PROCESSING_INSTRUCTION_NODE=7,R=u.COMMENT_NODE=8,I=u.DOCUMENT_NODE=9,J=u.DOCUMENT_TYPE_NODE=10,P=u.DOCUMENT_FRAGMENT_NODE=11,E=u.NOTATION_NODE=12,d=u.DOCUMENT_POSITION_DISCONNECTED=1,w=u.DOCUMENT_POSITION_PRECEDING=2,m=u.DOCUMENT_POSITION_FOLLOWING=4,se=u.DOCUMENT_POSITION_CONTAINS=8,ne=u.DOCUMENT_POSITION_CONTAINED_BY=16,Y=u.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC=32;u.prototype=Object.create(h.prototype,{baseURI:{get:i.nyi},parentElement:{get:function(){return this.parentNode&&this.parentNode.nodeType===o?this.parentNode:null}},hasChildNodes:{value:i.shouldOverride},firstChild:{get:i.shouldOverride},lastChild:{get:i.shouldOverride},isConnected:{get:function(){let V=this;for(;V!=null;){if(V.nodeType===u.DOCUMENT_NODE)return!0;V=V.parentNode,V!=null&&V.nodeType===u.DOCUMENT_FRAGMENT_NODE&&(V=V.host)}return!1}},previousSibling:{get:function(){var V=this.parentNode;return!V||this===V.firstChild?null:this._previousSibling}},nextSibling:{get:function(){var V=this.parentNode,D=this._nextSibling;return!V||D===V.firstChild?null:D}},textContent:{get:function(){return null},set:function(V){}},innerText:{get:function(){return null},set:function(V){}},_countChildrenOfType:{value:function(V){for(var D=0,B=this.firstChild;B!==null;B=B.nextSibling)B.nodeType===V&&D++;return D}},_ensureInsertValid:{value:function(D,B,K){var e=this,r,l;if(!D.nodeType)throw new TypeError("not a node");switch(e.nodeType){case I:case P:case o:break;default:i.HierarchyRequestError()}switch(D.isAncestor(e)&&i.HierarchyRequestError(),(B!==null||!K)&&B.parentNode!==e&&i.NotFoundError(),D.nodeType){case P:case J:case o:case s:case O:case R:break;default:i.HierarchyRequestError()}if(e.nodeType===I)switch(D.nodeType){case s:i.HierarchyRequestError();break;case P:switch(D._countChildrenOfType(s)>0&&i.HierarchyRequestError(),D._countChildrenOfType(o)){case 0:break;case 1:if(B!==null)for(K&&B.nodeType===J&&i.HierarchyRequestError(),l=B.nextSibling;l!==null;l=l.nextSibling)l.nodeType===J&&i.HierarchyRequestError();r=e._countChildrenOfType(o),K?r>0&&i.HierarchyRequestError():(r>1||r===1&&B.nodeType!==o)&&i.HierarchyRequestError();break;default:i.HierarchyRequestError()}break;case o:if(B!==null)for(K&&B.nodeType===J&&i.HierarchyRequestError(),l=B.nextSibling;l!==null;l=l.nextSibling)l.nodeType===J&&i.HierarchyRequestError();r=e._countChildrenOfType(o),K?r>0&&i.HierarchyRequestError():(r>1||r===1&&B.nodeType!==o)&&i.HierarchyRequestError();break;case J:if(B===null)e._countChildrenOfType(o)&&i.HierarchyRequestError();else for(l=e.firstChild;l!==null&&l!==B;l=l.nextSibling)l.nodeType===o&&i.HierarchyRequestError();r=e._countChildrenOfType(J),K?r>0&&i.HierarchyRequestError():(r>1||r===1&&B.nodeType!==J)&&i.HierarchyRequestError();break}else D.nodeType===J&&i.HierarchyRequestError()}},insertBefore:{value:function(D,B){var K=this;K._ensureInsertValid(D,B,!0);var e=B;return e===D&&(e=D.nextSibling),K.doc.adoptNode(D),D._insertOrReplace(K,e,!1),D}},appendChild:{value:function(V){return this.insertBefore(V,null)}},_appendChild:{value:function(V){V._insertOrReplace(this,null,!1)}},removeChild:{value:function(D){var B=this;if(!D.nodeType)throw new TypeError("not a node");return D.parentNode!==B&&i.NotFoundError(),D.remove(),D}},replaceChild:{value:function(D,B){var K=this;return K._ensureInsertValid(D,B,!1),D.doc!==K.doc&&K.doc.adoptNode(D),D._insertOrReplace(K,B,!0),B}},contains:{value:function(D){return D===null?!1:this===D?!0:(this.compareDocumentPosition(D)&ne)!==0}},compareDocumentPosition:{value:function(D){if(this===D)return 0;if(this.doc!==D.doc||this.rooted!==D.rooted)return d+Y;for(var B=[],K=[],e=this;e!==null;e=e.parentNode)B.push(e);for(e=D;e!==null;e=e.parentNode)K.push(e);if(B.reverse(),K.reverse(),B[0]!==K[0])return d+Y;e=Math.min(B.length,K.length);for(var r=1;r<e;r++)if(B[r]!==K[r])return B[r].index<K[r].index?m:w;return B.length<K.length?m+ne:w+se}},isSameNode:{value:function(D){return this===D}},isEqualNode:{value:function(D){if(!D||D.nodeType!==this.nodeType||!this.isEqual(D))return!1;for(var B=this.firstChild,K=D.firstChild;B&&K;B=B.nextSibling,K=K.nextSibling)if(!B.isEqualNode(K))return!1;return B===null&&K===null}},cloneNode:{value:function(V){var D=this.clone();if(V)for(var B=this.firstChild;B!==null;B=B.nextSibling)D._appendChild(B.cloneNode(!0));return D}},lookupPrefix:{value:function(D){var B;if(D===""||D===null||D===void 0)return null;switch(this.nodeType){case o:return this._lookupNamespacePrefix(D,this);case I:return B=this.documentElement,B?B.lookupPrefix(D):null;case T:case E:case P:case J:return null;case f:return B=this.ownerElement,B?B.lookupPrefix(D):null;default:return B=this.parentElement,B?B.lookupPrefix(D):null}}},lookupNamespaceURI:{value:function(D){(D===""||D===void 0)&&(D=null);var B;switch(this.nodeType){case o:return i.shouldOverride();case I:return B=this.documentElement,B?B.lookupNamespaceURI(D):null;case T:case E:case J:case P:return null;case f:return B=this.ownerElement,B?B.lookupNamespaceURI(D):null;default:return B=this.parentElement,B?B.lookupNamespaceURI(D):null}}},isDefaultNamespace:{value:function(D){(D===""||D===void 0)&&(D=null);var B=this.lookupNamespaceURI(null);return B===D}},index:{get:function(){var V=this.parentNode;if(this===V.firstChild)return 0;var D=V.childNodes;if(this._index===void 0||D[this._index]!==this){for(var B=0;B<D.length;B++)D[B]._index=B;i.assert(D[this._index]===this)}return this._index}},isAncestor:{value:function(V){if(this.doc!==V.doc||this.rooted!==V.rooted)return!1;for(var D=V;D;D=D.parentNode)if(D===this)return!0;return!1}},ensureSameDoc:{value:function(V){V.ownerDocument===null?V.ownerDocument=this.doc:V.ownerDocument!==this.doc&&i.WrongDocumentError()}},removeChildren:{value:i.shouldOverride},_insertOrReplace:{value:function(D,B,K){var e=this,r,l;if(e.nodeType===P&&e.rooted&&i.HierarchyRequestError(),D._childNodes&&(r=B===null?D._childNodes.length:B.index,e.parentNode===D)){var N=e.index;N<r&&r--}K&&(B.rooted&&B.doc.mutateRemove(B),B.parentNode=null);var g=B;g===null&&(g=D.firstChild);var S=e.rooted&&D.rooted;if(e.nodeType===P){for(var C=[0,K?1:0],G,ee=e.firstChild;ee!==null;ee=G)G=ee.nextSibling,C.push(ee),ee.parentNode=D;var y=C.length;if(K?c.replace(g,y>2?C[2]:null):y>2&&g!==null&&c.insertBefore(C[2],g),D._childNodes)for(C[0]=B===null?D._childNodes.length:B._index,D._childNodes.splice.apply(D._childNodes,C),l=2;l<y;l++)C[l]._index=C[0]+(l-2);else D._firstChild===B&&(y>2?D._firstChild=C[2]:K&&(D._firstChild=null));if(e._childNodes?e._childNodes.length=0:e._firstChild=null,D.rooted)for(D.modify(),l=2;l<y;l++)D.doc.mutateInsert(C[l])}else{if(B===e)return;S?e._remove():e.parentNode&&e.remove(),e.parentNode=D,K?(c.replace(g,e),D._childNodes?(e._index=r,D._childNodes[r]=e):D._firstChild===B&&(D._firstChild=e)):(g!==null&&c.insertBefore(e,g),D._childNodes?(e._index=r,D._childNodes.splice(r,0,e)):D._firstChild===B&&(D._firstChild=e)),S?(D.modify(),D.doc.mutateMove(e)):D.rooted&&(D.modify(),D.doc.mutateInsert(e))}}},lastModTime:{get:function(){return this._lastModTime||(this._lastModTime=this.doc.modclock),this._lastModTime}},modify:{value:function(){if(this.doc.modclock)for(var V=++this.doc.modclock,D=this;D;D=D.parentElement)D._lastModTime&&(D._lastModTime=V)}},doc:{get:function(){return this.ownerDocument||this}},rooted:{get:function(){return!!this._nid}},normalize:{value:function(){for(var V,D=this.firstChild;D!==null;D=V)if(V=D.nextSibling,D.normalize&&D.normalize(),D.nodeType===u.TEXT_NODE){if(D.nodeValue===""){this.removeChild(D);continue}var B=D.previousSibling;B!==null&&B.nodeType===u.TEXT_NODE&&(B.appendData(D.nodeValue),this.removeChild(D))}}},serialize:{value:function(){if(this._innerHTML)return this._innerHTML;for(var V="",D=this.firstChild;D!==null;D=D.nextSibling)V+=n.serializeOne(D,this);return V}},outerHTML:{get:function(){return n.serializeOne(this,{nodeType:0})},set:i.nyi},ELEMENT_NODE:{value:o},ATTRIBUTE_NODE:{value:f},TEXT_NODE:{value:s},CDATA_SECTION_NODE:{value:b},ENTITY_REFERENCE_NODE:{value:p},ENTITY_NODE:{value:T},PROCESSING_INSTRUCTION_NODE:{value:O},COMMENT_NODE:{value:R},DOCUMENT_NODE:{value:I},DOCUMENT_TYPE_NODE:{value:J},DOCUMENT_FRAGMENT_NODE:{value:P},NOTATION_NODE:{value:E},DOCUMENT_POSITION_DISCONNECTED:{value:d},DOCUMENT_POSITION_PRECEDING:{value:w},DOCUMENT_POSITION_FOLLOWING:{value:m},DOCUMENT_POSITION_CONTAINS:{value:se},DOCUMENT_POSITION_CONTAINED_BY:{value:ne},DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:{value:Y}})}}),cs=le({"external/npm/node_modules/domino/lib/NodeList.es6.js"(_,k){k.exports=class extends Array{constructor(c){if(super(c&&c.length||0),c)for(var n in c)this[n]=c[n]}item(c){return this[c]||null}}}}),ls=le({"external/npm/node_modules/domino/lib/NodeList.es5.js"(_,k){function h(n){return this[n]||null}function c(n){return n||(n=[]),n.item=h,n}k.exports=c}}),rr=le({"external/npm/node_modules/domino/lib/NodeList.js"(_,k){var h;try{h=cs()}catch{h=ls()}k.exports=h}}),Tn=le({"external/npm/node_modules/domino/lib/ContainerNode.js"(_,k){k.exports=n;var h=Ge(),c=rr();function n(){h.call(this),this._firstChild=this._childNodes=null}n.prototype=Object.create(h.prototype,{hasChildNodes:{value:function(){return this._childNodes?this._childNodes.length>0:this._firstChild!==null}},childNodes:{get:function(){return this._ensureChildNodes(),this._childNodes}},firstChild:{get:function(){return this._childNodes?this._childNodes.length===0?null:this._childNodes[0]:this._firstChild}},lastChild:{get:function(){var i=this._childNodes,u;return i?i.length===0?null:i[i.length-1]:(u=this._firstChild,u===null?null:u._previousSibling)}},_ensureChildNodes:{value:function(){if(!this._childNodes){var i=this._firstChild,u=i,o=this._childNodes=new c;if(i)do o.push(u),u=u._nextSibling;while(u!==i);this._firstChild=null}}},removeChildren:{value:function(){for(var u=this.rooted?this.ownerDocument:null,o=this.firstChild,f;o!==null;)f=o,o=f.nextSibling,u&&u.mutateRemove(f),f.parentNode=null;this._childNodes?this._childNodes.length=0:this._firstChild=null,this.modify()}}})}}),yn=le({"external/npm/node_modules/domino/lib/xmlnames.js"(_){_.isValidName=I,_.isValidQName=J;var k=/^[_:A-Za-z][-.:\w]+$/,h=/^([_A-Za-z][-.\w]+|[_A-Za-z][-.\w]+:[_A-Za-z][-.\w]+)$/,c="_A-Za-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",n="-._A-Za-z0-9\xB7\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0300-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",i="["+c+"]["+n+"]*",u=c+":",o=n+":",f=new RegExp("^["+u+"]["+o+"]*$"),s=new RegExp("^("+i+"|"+i+":"+i+")$"),b=/[\uD800-\uDB7F\uDC00-\uDFFF]/,p=/[\uD800-\uDB7F\uDC00-\uDFFF]/g,T=/[\uD800-\uDB7F][\uDC00-\uDFFF]/g;c+="\uD800-\u{EFC00}-\uDFFF",n+="\uD800-\u{EFC00}-\uDFFF",i="["+c+"]["+n+"]*",u=c+":",o=n+":";var O=new RegExp("^["+u+"]["+o+"]*$"),R=new RegExp("^("+i+"|"+i+":"+i+")$");function I(P){if(k.test(P)||f.test(P))return!0;if(!b.test(P)||!O.test(P))return!1;var E=P.match(p),d=P.match(T);return d!==null&&2*d.length===E.length}function J(P){if(h.test(P)||s.test(P))return!0;if(!b.test(P)||!R.test(P))return!1;var E=P.match(p),d=P.match(T);return d!==null&&2*d.length===E.length}}}),ma=le({"external/npm/node_modules/domino/lib/attributes.js"(_){var k=Be();_.property=function(c){if(Array.isArray(c.type)){var n=Object.create(null);c.type.forEach(function(o){n[o.value||o]=o.alias||o});var i=c.missing;i===void 0&&(i=null);var u=c.invalid;return u===void 0&&(u=i),{get:function(){var o=this._getattr(c.name);return o===null?i:(o=n[o.toLowerCase()],o!==void 0?o:u!==null?u:o)},set:function(o){this._setattr(c.name,o)}}}else{if(c.type===Boolean)return{get:function(){return this.hasAttribute(c.name)},set:function(o){o?this._setattr(c.name,""):this.removeAttribute(c.name)}};if(c.type===Number||c.type==="long"||c.type==="unsigned long"||c.type==="limited unsigned long with fallback")return h(c);if(!c.type||c.type===String)return{get:function(){return this._getattr(c.name)||""},set:function(o){c.treatNullAsEmptyString&&o===null&&(o=""),this._setattr(c.name,o)}};if(typeof c.type=="function")return c.type(c.name,c)}throw new Error("Invalid attribute definition")};function h(c){var n;typeof c.default=="function"?n=c.default:typeof c.default=="number"?n=function(){return c.default}:n=function(){k.assert(!1,typeof c.default)};var i=c.type==="unsigned long",u=c.type==="long",o=c.type==="limited unsigned long with fallback",f=c.min,s=c.max,b=c.setmin;return f===void 0&&(i&&(f=0),u&&(f=-2147483648),o&&(f=1)),s===void 0&&(i||u||o)&&(s=2147483647),{get:function(){var p=this._getattr(c.name),T=c.float?parseFloat(p):parseInt(p,10);if(p===null||!isFinite(T)||f!==void 0&&T<f||s!==void 0&&T>s)return n.call(this);if(i||u||o){if(!/^[ \t\n\f\r]*[-+]?[0-9]/.test(p))return n.call(this);T=T|0}return T},set:function(p){c.float||(p=Math.floor(p)),b!==void 0&&p<b&&k.IndexSizeError(c.name+" set to "+p),i?p=p<0||p>2147483647?n.call(this):p|0:o?p=p<1||p>2147483647?n.call(this):p|0:u&&(p=p<-2147483648||p>2147483647?n.call(this):p|0),this._setattr(c.name,String(p))}}}_.registerChangeHandler=function(c,n,i){var u=c.prototype;Object.prototype.hasOwnProperty.call(u,"_attributeChangeHandlers")||(u._attributeChangeHandlers=Object.create(u._attributeChangeHandlers||null)),u._attributeChangeHandlers[n]=i}}}),us=le({"external/npm/node_modules/domino/lib/FilteredElementList.js"(_,k){k.exports=c;var h=Ge();function c(n,i){this.root=n,this.filter=i,this.lastModTime=n.lastModTime,this.done=!1,this.cache=[],this.traverse()}c.prototype=Object.create(Object.prototype,{length:{get:function(){return this.checkcache(),this.done||this.traverse(),this.cache.length}},item:{value:function(n){return this.checkcache(),!this.done&&n>=this.cache.length&&this.traverse(),this.cache[n]}},checkcache:{value:function(){if(this.lastModTime!==this.root.lastModTime){for(var n=this.cache.length-1;n>=0;n--)this[n]=void 0;this.cache.length=0,this.done=!1,this.lastModTime=this.root.lastModTime}}},traverse:{value:function(n){n!==void 0&&n++;for(var i;(i=this.next())!==null;)if(this[this.cache.length]=i,this.cache.push(i),n&&this.cache.length===n)return;this.done=!0}},next:{value:function(){var n=this.cache.length===0?this.root:this.cache[this.cache.length-1],i;for(n.nodeType===h.DOCUMENT_NODE?i=n.documentElement:i=n.nextElement(this.root);i;){if(this.filter(i))return i;i=i.nextElement(this.root)}return null}}})}}),ga=le({"external/npm/node_modules/domino/lib/DOMTokenList.js"(_,k){var h=Be();k.exports=c;function c(f,s){this._getString=f,this._setString=s,this._length=0,this._lastStringValue="",this._update()}Object.defineProperties(c.prototype,{length:{get:function(){return this._length}},item:{value:function(f){var s=o(this);return f<0||f>=s.length?null:s[f]}},contains:{value:function(f){f=String(f);var s=o(this);return s.indexOf(f)>-1}},add:{value:function(){for(var f=o(this),s=0,b=arguments.length;s<b;s++){var p=i(arguments[s]);f.indexOf(p)<0&&f.push(p)}this._update(f)}},remove:{value:function(){for(var f=o(this),s=0,b=arguments.length;s<b;s++){var p=i(arguments[s]),T=f.indexOf(p);T>-1&&f.splice(T,1)}this._update(f)}},toggle:{value:function(s,b){return s=i(s),this.contains(s)?b===void 0||b===!1?(this.remove(s),!1):!0:b===void 0||b===!0?(this.add(s),!0):!1}},replace:{value:function(s,b){String(b)===""&&h.SyntaxError(),s=i(s),b=i(b);var p=o(this),T=p.indexOf(s);if(T<0)return!1;var O=p.indexOf(b);return O<0?p[T]=b:T<O?(p[T]=b,p.splice(O,1)):p.splice(T,1),this._update(p),!0}},toString:{value:function(){return this._getString()}},value:{get:function(){return this._getString()},set:function(f){this._setString(f),this._update()}},_update:{value:function(f){f?(n(this,f),this._setString(f.join(" ").trim())):n(this,o(this)),this._lastStringValue=this._getString()}}});function n(f,s){var b=f._length,p;for(f._length=s.length,p=0;p<s.length;p++)f[p]=s[p];for(;p<b;p++)f[p]=void 0}function i(f){return f=String(f),f===""&&h.SyntaxError(),/[ \t\r\n\f]/.test(f)&&h.InvalidCharacterError(),f}function u(f){for(var s=f._length,b=Array(s),p=0;p<s;p++)b[p]=f[p];return b}function o(f){var s=f._getString();if(s===f._lastStringValue)return u(f);var b=s.replace(/(^[ \t\r\n\f]+)|([ \t\r\n\f]+$)/g,"");if(b==="")return[];var p=Object.create(null);return b.split(/[ \t\r\n\f]+/g).filter(function(T){var O="$"+T;return p[O]?!1:(p[O]=!0,!0)})}}}),wn=le({"external/npm/node_modules/domino/lib/select.js"(_,k){var h=Object.create(null,{location:{get:function(){throw new Error("window.location is not supported.")}}}),c=function(e,r){return e.compareDocumentPosition(r)},n=function(e,r){return c(e,r)&2?1:-1},i=function(e){for(;(e=e.nextSibling)&&e.nodeType!==1;);return e},u=function(e){for(;(e=e.previousSibling)&&e.nodeType!==1;);return e},o=function(e){if(e=e.firstChild)for(;e.nodeType!==1&&(e=e.nextSibling););return e},f=function(e){if(e=e.lastChild)for(;e.nodeType!==1&&(e=e.previousSibling););return e},s=function(e){if(!e.parentNode)return!1;var r=e.parentNode.nodeType;return r===1||r===9},b=function(e){if(!e)return e;var r=e[0];return r==='"'||r==="'"?(e[e.length-1]===r?e=e.slice(1,-1):e=e.slice(1),e.replace(m.str_escape,function(l){var N=/^\\(?:([0-9A-Fa-f]+)|([\r\n\f]+))/.exec(l);if(!N)return l.slice(1);if(N[2])return"";var g=parseInt(N[1],16);return String.fromCodePoint?String.fromCodePoint(g):String.fromCharCode(g)})):m.ident.test(e)?p(e):e},p=function(e){return e.replace(m.escape,function(r){var l=/^\\([0-9A-Fa-f]+)/.exec(r);if(!l)return r[1];var N=parseInt(l[1],16);return String.fromCodePoint?String.fromCodePoint(N):String.fromCharCode(N)})},T=function(){return Array.prototype.indexOf?Array.prototype.indexOf:function(e,r){for(var l=this.length;l--;)if(this[l]===r)return l;return-1}}(),O=function(e,r){var l=m.inside.source.replace(/</g,e).replace(/>/g,r);return new RegExp(l)},R=function(e,r,l){return e=e.source,e=e.replace(r,l.source||l),new RegExp(e)},I=function(e,r){return e.replace(/^(?:\w+:\/\/|\/+)/,"").replace(/(?:\/+|\/*#.*?)$/,"").split("/",r).join("/")},J=function(e,r){var l=e.replace(/\s+/g,""),N;return l==="even"?l="2n+0":l==="odd"?l="2n+1":l.indexOf("n")===-1&&(l="0n"+l),N=/^([+-])?(\d+)?n([+-])?(\d+)?$/.exec(l),{group:N[1]==="-"?-(N[2]||1):+(N[2]||1),offset:N[4]?N[3]==="-"?-N[4]:+N[4]:0}},P=function(e,r,l){var N=J(e),g=N.group,S=N.offset,C=l?f:o,G=l?u:i;return function(ee){if(s(ee))for(var y=C(ee.parentNode),M=0;y;){if(r(y,ee)&&M++,y===ee)return M-=S,g&&M?M%g===0&&M<0==g<0:!M;y=G(y)}}},E={"*":function(){return function(){return!0}}(),type:function(e){return e=e.toLowerCase(),function(r){return r.nodeName.toLowerCase()===e}},attr:function(e,r,l,N){return r=d[r],function(g){var S;switch(e){case"for":S=g.htmlFor;break;case"class":S=g.className,S===""&&g.getAttribute("class")==null&&(S=null);break;case"href":case"src":S=g.getAttribute(e,2);break;case"title":S=g.getAttribute("title")||null;break;case"id":case"lang":case"dir":case"accessKey":case"hidden":case"tabIndex":case"style":if(g.getAttribute){S=g.getAttribute(e);break}default:if(g.hasAttribute&&!g.hasAttribute(e))break;S=g[e]!=null?g[e]:g.getAttribute&&g.getAttribute(e);break}if(S!=null)return S=S+"",N&&(S=S.toLowerCase(),l=l.toLowerCase()),r(S,l)}},":first-child":function(e){return!u(e)&&s(e)},":last-child":function(e){return!i(e)&&s(e)},":only-child":function(e){return!u(e)&&!i(e)&&s(e)},":nth-child":function(e,r){return P(e,function(){return!0},r)},":nth-last-child":function(e){return E[":nth-child"](e,!0)},":root":function(e){return e.ownerDocument.documentElement===e},":empty":function(e){return!e.firstChild},":not":function(e){var r=B(e);return function(l){return!r(l)}},":first-of-type":function(e){if(s(e)){for(var r=e.nodeName;e=u(e);)if(e.nodeName===r)return;return!0}},":last-of-type":function(e){if(s(e)){for(var r=e.nodeName;e=i(e);)if(e.nodeName===r)return;return!0}},":only-of-type":function(e){return E[":first-of-type"](e)&&E[":last-of-type"](e)},":nth-of-type":function(e,r){return P(e,function(l,N){return l.nodeName===N.nodeName},r)},":nth-last-of-type":function(e){return E[":nth-of-type"](e,!0)},":checked":function(e){return!!(e.checked||e.selected)},":indeterminate":function(e){return!E[":checked"](e)},":enabled":function(e){return!e.disabled&&e.type!=="hidden"},":disabled":function(e){return!!e.disabled},":target":function(e){return e.id===h.location.hash.substring(1)},":focus":function(e){return e===e.ownerDocument.activeElement},":is":function(e){return B(e)},":matches":function(e){return E[":is"](e)},":nth-match":function(e,r){var l=e.split(/\s*,\s*/),N=l.shift(),g=B(l.join(","));return P(N,g,r)},":nth-last-match":function(e){return E[":nth-match"](e,!0)},":links-here":function(e){return e+""==h.location+""},":lang":function(e){return function(r){for(;r;){if(r.lang)return r.lang.indexOf(e)===0;r=r.parentNode}}},":dir":function(e){return function(r){for(;r;){if(r.dir)return r.dir===e;r=r.parentNode}}},":scope":function(e,r){var l=r||e.ownerDocument;return l.nodeType===9?e===l.documentElement:e===l},":any-link":function(e){return typeof e.href=="string"},":local-link":function(e){if(e.nodeName)return e.href&&e.host===h.location.host;var r=+e+1;return function(l){if(l.href){var N=h.location+"",g=l+"";return I(N,r)===I(g,r)}}},":default":function(e){return!!e.defaultSelected},":valid":function(e){return e.willValidate||e.validity&&e.validity.valid},":invalid":function(e){return!E[":valid"](e)},":in-range":function(e){return e.value>e.min&&e.value<=e.max},":out-of-range":function(e){return!E[":in-range"](e)},":required":function(e){return!!e.required},":optional":function(e){return!e.required},":read-only":function(e){if(e.readOnly)return!0;var r=e.getAttribute("contenteditable"),l=e.contentEditable,N=e.nodeName.toLowerCase();return N=N!=="input"&&N!=="textarea",(N||e.disabled)&&r==null&&l!=="true"},":read-write":function(e){return!E[":read-only"](e)},":hover":function(){throw new Error(":hover is not supported.")},":active":function(){throw new Error(":active is not supported.")},":link":function(){throw new Error(":link is not supported.")},":visited":function(){throw new Error(":visited is not supported.")},":column":function(){throw new Error(":column is not supported.")},":nth-column":function(){throw new Error(":nth-column is not supported.")},":nth-last-column":function(){throw new Error(":nth-last-column is not supported.")},":current":function(){throw new Error(":current is not supported.")},":past":function(){throw new Error(":past is not supported.")},":future":function(){throw new Error(":future is not supported.")},":contains":function(e){return function(r){var l=r.innerText||r.textContent||r.value||"";return l.indexOf(e)!==-1}},":has":function(e){return function(r){return K(e,r).length>0}}},d={"-":function(){return!0},"=":function(e,r){return e===r},"*=":function(e,r){return e.indexOf(r)!==-1},"~=":function(e,r){var l,N,g,S;for(N=0;;N=l+1){if(l=e.indexOf(r,N),l===-1)return!1;if(g=e[l-1],S=e[l+r.length],(!g||g===" ")&&(!S||S===" "))return!0}},"|=":function(e,r){var l=e.indexOf(r),N;if(l===0)return N=e[l+r.length],N==="-"||!N},"^=":function(e,r){return e.indexOf(r)===0},"$=":function(e,r){var l=e.lastIndexOf(r);return l!==-1&&l+r.length===e.length},"!=":function(e,r){return e!==r}},w={" ":function(e){return function(r){for(;r=r.parentNode;)if(e(r))return r}},">":function(e){return function(r){if(r=r.parentNode)return e(r)&&r}},"+":function(e){return function(r){if(r=u(r))return e(r)&&r}},"~":function(e){return function(r){for(;r=u(r);)if(e(r))return r}},noop:function(e){return function(r){return e(r)&&r}},ref:function(e,r){var l;function N(g){for(var S=g.ownerDocument,C=S.getElementsByTagName("*"),G=C.length;G--;)if(l=C[G],N.test(g))return l=null,!0;l=null}return N.combinator=function(g){if(!(!l||!l.getAttribute)){var S=l.getAttribute(r)||"";if(S[0]==="#"&&(S=S.substring(1)),S===g.id&&e(l))return l}},N}},m={escape:/\\(?:[^0-9A-Fa-f\r\n]|[0-9A-Fa-f]{1,6}[\r\n\t ]?)/g,str_escape:/(escape)|\\(\n|\r\n?|\f)/g,nonascii:/[\u00A0-\uFFFF]/,cssid:/(?:(?!-?[0-9])(?:escape|nonascii|[-_a-zA-Z0-9])+)/,qname:/^ *(cssid|\*)/,simple:/^(?:([.#]cssid)|pseudo|attr)/,ref:/^ *\/(cssid)\/ */,combinator:/^(?: +([^ \w*.#\\]) +|( )+|([^ \w*.#\\]))(?! *$)/,attr:/^\[(cssid)(?:([^\w]?=)(inside))?\]/,pseudo:/^(:cssid)(?:\((inside)\))?/,inside:/(?:"(?:\\"|[^"])*"|'(?:\\'|[^'])*'|<[^"'>]*>|\\["'>]|[^"'>])*/,ident:/^(cssid)$/};m.cssid=R(m.cssid,"nonascii",m.nonascii),m.cssid=R(m.cssid,"escape",m.escape),m.qname=R(m.qname,"cssid",m.cssid),m.simple=R(m.simple,"cssid",m.cssid),m.ref=R(m.ref,"cssid",m.cssid),m.attr=R(m.attr,"cssid",m.cssid),m.pseudo=R(m.pseudo,"cssid",m.cssid),m.inside=R(m.inside,`[^"'>]*`,m.inside),m.attr=R(m.attr,"inside",O("\\[","\\]")),m.pseudo=R(m.pseudo,"inside",O("\\(","\\)")),m.simple=R(m.simple,"pseudo",m.pseudo),m.simple=R(m.simple,"attr",m.attr),m.ident=R(m.ident,"cssid",m.cssid),m.str_escape=R(m.str_escape,"escape",m.escape);var se=function(e){for(var r=e.replace(/^\s+|\s+$/g,""),l,N=[],g=[],S,C,G,ee,y;r;){if(G=m.qname.exec(r))r=r.substring(G[0].length),C=p(G[1]),g.push(ne(C,!0));else if(G=m.simple.exec(r))r=r.substring(G[0].length),C="*",g.push(ne(C,!0)),g.push(ne(G));else throw new SyntaxError("Invalid selector.");for(;G=m.simple.exec(r);)r=r.substring(G[0].length),g.push(ne(G));if(r[0]==="!"&&(r=r.substring(1),S=D(),S.qname=C,g.push(S.simple)),G=m.ref.exec(r)){r=r.substring(G[0].length),y=w.ref(Y(g),p(G[1])),N.push(y.combinator),g=[];continue}if(G=m.combinator.exec(r)){if(r=r.substring(G[0].length),ee=G[1]||G[2]||G[3],ee===","){N.push(w.noop(Y(g)));break}}else ee="noop";if(!w[ee])throw new SyntaxError("Bad combinator.");N.push(w[ee](Y(g))),g=[]}return l=V(N),l.qname=C,l.sel=r,S&&(S.lname=l.qname,S.test=l,S.qname=S.qname,S.sel=l.sel,l=S),y&&(y.test=l,y.qname=l.qname,y.sel=l.sel,l=y),l},ne=function(e,r){if(r)return e==="*"?E["*"]:E.type(e);if(e[1])return e[1][0]==="."?E.attr("class","~=",p(e[1].substring(1)),!1):E.attr("id","=",p(e[1].substring(1)),!1);if(e[2])return e[3]?E[p(e[2])](b(e[3])):E[p(e[2])];if(e[4]){var l=e[6],N=/["'\s]\s*I$/i.test(l);return N&&(l=l.replace(/\s*I$/i,"")),E.attr(p(e[4]),e[5]||"-",b(l),N)}throw new SyntaxError("Unknown Selector.")},Y=function(e){var r=e.length,l;return r<2?e[0]:function(N){if(N){for(l=0;l<r;l++)if(!e[l](N))return;return!0}}},V=function(e){return e.length<2?function(r){return!!e[0](r)}:function(r){for(var l=e.length;l--;)if(!(r=e[l](r)))return;return!0}},D=function(){var e;function r(l){for(var N=l.ownerDocument,g=N.getElementsByTagName(r.lname),S=g.length;S--;)if(r.test(g[S])&&e===l)return e=null,!0;e=null}return r.simple=function(l){return e=l,!0},r},B=function(e){for(var r=se(e),l=[r];r.sel;)r=se(r.sel),l.push(r);return l.length<2?r:function(N){for(var g=l.length,S=0;S<g;S++)if(l[S](N))return!0}},K=function(e,r){for(var l=[],N=se(e),g=r.getElementsByTagName(N.qname),S=0,C;C=g[S++];)N(C)&&l.push(C);if(N.sel){for(;N.sel;)for(N=se(N.sel),g=r.getElementsByTagName(N.qname),S=0;C=g[S++];)N(C)&&T.call(l,C)===-1&&l.push(C);l.sort(n)}return l};k.exports=_=function(e,r){var l,N;if(r.nodeType!==11&&e.indexOf(" ")===-1){if(e[0]==="#"&&r.rooted&&/^#[A-Z_][-A-Z0-9_]*$/i.test(e)&&r.doc._hasMultipleElementsWithId&&(l=e.substring(1),!r.doc._hasMultipleElementsWithId(l)))return N=r.doc.getElementById(l),N?[N]:[];if(e[0]==="."&&/^\.\w+$/.test(e))return r.getElementsByClassName(e.substring(1));if(/^\w+$/.test(e))return r.getElementsByTagName(e)}return K(e,r)},_.selectors=E,_.operators=d,_.combinators=w,_.matches=function(e,r){var l={sel:r};do if(l=se(l.sel),l(e))return!0;while(l.sel);return!1}}}),Nn=le({"external/npm/node_modules/domino/lib/ChildNode.js"(_,k){var h=Ge(),c=pa(),n=function(u,o){for(var f=u.createDocumentFragment(),s=0;s<o.length;s++){var b=o[s],p=b instanceof h;f.appendChild(p?b:u.createTextNode(String(b)))}return f},i={after:{value:function(){var o=Array.prototype.slice.call(arguments),f=this.parentNode,s=this.nextSibling;if(f!==null){for(;s&&o.some(function(p){return p===s});)s=s.nextSibling;var b=n(this.doc,o);f.insertBefore(b,s)}}},before:{value:function(){var o=Array.prototype.slice.call(arguments),f=this.parentNode,s=this.previousSibling;if(f!==null){for(;s&&o.some(function(T){return T===s});)s=s.previousSibling;var b=n(this.doc,o),p=s?s.nextSibling:f.firstChild;f.insertBefore(b,p)}}},remove:{value:function(){this.parentNode!==null&&(this.doc&&(this.doc._preremoveNodeIterators(this),this.rooted&&this.doc.mutateRemove(this)),this._remove(),this.parentNode=null)}},_remove:{value:function(){var o=this.parentNode;o!==null&&(o._childNodes?o._childNodes.splice(this.index,1):o._firstChild===this&&(this._nextSibling===this?o._firstChild=null:o._firstChild=this._nextSibling),c.remove(this),o.modify())}},replaceWith:{value:function(){var o=Array.prototype.slice.call(arguments),f=this.parentNode,s=this.nextSibling;if(f!==null){for(;s&&o.some(function(p){return p===s});)s=s.nextSibling;var b=n(this.doc,o);this.parentNode===f?f.replaceChild(b,this):f.insertBefore(b,s)}}}};k.exports=i}}),ba=le({"external/npm/node_modules/domino/lib/NonDocumentTypeChildNode.js"(_,k){var h=Ge(),c={nextElementSibling:{get:function(){if(this.parentNode){for(var n=this.nextSibling;n!==null;n=n.nextSibling)if(n.nodeType===h.ELEMENT_NODE)return n}return null}},previousElementSibling:{get:function(){if(this.parentNode){for(var n=this.previousSibling;n!==null;n=n.previousSibling)if(n.nodeType===h.ELEMENT_NODE)return n}return null}}};k.exports=c}}),_a=le({"external/npm/node_modules/domino/lib/NamedNodeMap.js"(_,k){k.exports=c;var h=Be();function c(n){this.element=n}Object.defineProperties(c.prototype,{length:{get:h.shouldOverride},item:{value:h.shouldOverride},getNamedItem:{value:function(i){return this.element.getAttributeNode(i)}},getNamedItemNS:{value:function(i,u){return this.element.getAttributeNodeNS(i,u)}},setNamedItem:{value:h.nyi},setNamedItemNS:{value:h.nyi},removeNamedItem:{value:function(i){var u=this.element.getAttributeNode(i);if(u)return this.element.removeAttribute(i),u;h.NotFoundError()}},removeNamedItemNS:{value:function(i,u){var o=this.element.getAttributeNodeNS(i,u);if(o)return this.element.removeAttributeNS(i,u),o;h.NotFoundError()}}})}}),_r=le({"external/npm/node_modules/domino/lib/Element.js"(_,k){k.exports=P;var h=yn(),c=Be(),n=c.NAMESPACE,i=ma(),u=Ge(),o=rr(),f=da(),s=us(),b=ga(),p=wn(),T=Tn(),O=Nn(),R=ba(),I=_a(),J=Object.create(null);function P(e,r,l,N){T.call(this),this.nodeType=u.ELEMENT_NODE,this.ownerDocument=e,this.localName=r,this.namespaceURI=l,this.prefix=N,this._tagName=void 0,this._attrsByQName=Object.create(null),this._attrsByLName=Object.create(null),this._attrKeys=[]}function E(e,r){if(e.nodeType===u.TEXT_NODE)r.push(e._data);else for(var l=0,N=e.childNodes.length;l<N;l++)E(e.childNodes[l],r)}P.prototype=Object.create(T.prototype,{isHTML:{get:function(){return this.namespaceURI===n.HTML&&this.ownerDocument.isHTML}},tagName:{get:function(){if(this._tagName===void 0){var r;if(this.prefix===null?r=this.localName:r=this.prefix+":"+this.localName,this.isHTML){var l=J[r];l||(J[r]=l=c.toASCIIUpperCase(r)),r=l}this._tagName=r}return this._tagName}},nodeName:{get:function(){return this.tagName}},nodeValue:{get:function(){return null},set:function(){}},textContent:{get:function(){var e=[];return E(this,e),e.join("")},set:function(e){this.removeChildren(),e!=null&&e!==""&&this._appendChild(this.ownerDocument.createTextNode(e))}},innerText:{get:function(){var e=[];return E(this,e),e.join("").replace(/[ \t\n\f\r]+/g," ").trim()},set:function(e){this.removeChildren(),e!=null&&e!==""&&this._appendChild(this.ownerDocument.createTextNode(e))}},innerHTML:{get:function(){return this.serialize()},set:c.nyi},outerHTML:{get:function(){return f.serializeOne(this,{nodeType:0})},set:function(e){var r=this.ownerDocument,l=this.parentNode;if(l!==null){l.nodeType===u.DOCUMENT_NODE&&c.NoModificationAllowedError(),l.nodeType===u.DOCUMENT_FRAGMENT_NODE&&(l=l.ownerDocument.createElement("body"));var N=r.implementation.mozHTMLParser(r._address,l);N.parse(e===null?"":String(e),!0),this.replaceWith(N._asDocumentFragment())}}},_insertAdjacent:{value:function(r,l){var N=!1;switch(r){case"beforebegin":N=!0;case"afterend":var g=this.parentNode;return g===null?null:g.insertBefore(l,N?this:this.nextSibling);case"afterbegin":N=!0;case"beforeend":return this.insertBefore(l,N?this.firstChild:null);default:return c.SyntaxError()}}},insertAdjacentElement:{value:function(r,l){if(l.nodeType!==u.ELEMENT_NODE)throw new TypeError("not an element");return r=c.toASCIILowerCase(String(r)),this._insertAdjacent(r,l)}},insertAdjacentText:{value:function(r,l){var N=this.ownerDocument.createTextNode(l);r=c.toASCIILowerCase(String(r)),this._insertAdjacent(r,N)}},insertAdjacentHTML:{value:function(r,l){r=c.toASCIILowerCase(String(r)),l=String(l);var N;switch(r){case"beforebegin":case"afterend":N=this.parentNode,(N===null||N.nodeType===u.DOCUMENT_NODE)&&c.NoModificationAllowedError();break;case"afterbegin":case"beforeend":N=this;break;default:c.SyntaxError()}(!(N instanceof P)||N.ownerDocument.isHTML&&N.localName==="html"&&N.namespaceURI===n.HTML)&&(N=N.ownerDocument.createElementNS(n.HTML,"body"));var g=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,N);g.parse(l,!0),this._insertAdjacent(r,g._asDocumentFragment())}},children:{get:function(){return this._children||(this._children=new se(this)),this._children}},attributes:{get:function(){return this._attributes||(this._attributes=new w(this)),this._attributes}},firstElementChild:{get:function(){for(var e=this.firstChild;e!==null;e=e.nextSibling)if(e.nodeType===u.ELEMENT_NODE)return e;return null}},lastElementChild:{get:function(){for(var e=this.lastChild;e!==null;e=e.previousSibling)if(e.nodeType===u.ELEMENT_NODE)return e;return null}},childElementCount:{get:function(){return this.children.length}},nextElement:{value:function(e){e||(e=this.ownerDocument.documentElement);var r=this.firstElementChild;if(!r){if(this===e)return null;r=this.nextElementSibling}if(r)return r;for(var l=this.parentElement;l&&l!==e;l=l.parentElement)if(r=l.nextElementSibling,r)return r;return null}},getElementsByTagName:{value:function(r){var l;return r?(r==="*"?l=function(){return!0}:this.isHTML?l=Y(r):l=ne(r),new s(this,l)):new o}},getElementsByTagNameNS:{value:function(r,l){var N;return r==="*"&&l==="*"?N=function(){return!0}:r==="*"?N=ne(l):l==="*"?N=V(r):N=D(r,l),new s(this,N)}},getElementsByClassName:{value:function(r){if(r=String(r).trim(),r===""){var l=new o;return l}return r=r.split(/[ \t\r\n\f]+/),new s(this,B(r))}},getElementsByName:{value:function(r){return new s(this,K(String(r)))}},clone:{value:function(){var r;this.namespaceURI!==n.HTML||this.prefix||!this.ownerDocument.isHTML?r=this.ownerDocument.createElementNS(this.namespaceURI,this.prefix!==null?this.prefix+":"+this.localName:this.localName):r=this.ownerDocument.createElement(this.localName);for(var l=0,N=this._attrKeys.length;l<N;l++){var g=this._attrKeys[l],S=this._attrsByLName[g],C=S.cloneNode();C._setOwnerElement(r),r._attrsByLName[g]=C,r._addQName(C)}return r._attrKeys=this._attrKeys.concat(),r}},isEqual:{value:function(r){if(this.localName!==r.localName||this.namespaceURI!==r.namespaceURI||this.prefix!==r.prefix||this._numattrs!==r._numattrs)return!1;for(var l=0,N=this._numattrs;l<N;l++){var g=this._attr(l);if(!r.hasAttributeNS(g.namespaceURI,g.localName)||r.getAttributeNS(g.namespaceURI,g.localName)!==g.value)return!1}return!0}},_lookupNamespacePrefix:{value:function(r,l){if(this.namespaceURI&&this.namespaceURI===r&&this.prefix!==null&&l.lookupNamespaceURI(this.prefix)===r)return this.prefix;for(var N=0,g=this._numattrs;N<g;N++){var S=this._attr(N);if(S.prefix==="xmlns"&&S.value===r&&l.lookupNamespaceURI(S.localName)===r)return S.localName}var C=this.parentElement;return C?C._lookupNamespacePrefix(r,l):null}},lookupNamespaceURI:{value:function(r){if((r===""||r===void 0)&&(r=null),this.namespaceURI!==null&&this.prefix===r)return this.namespaceURI;for(var l=0,N=this._numattrs;l<N;l++){var g=this._attr(l);if(g.namespaceURI===n.XMLNS&&(g.prefix==="xmlns"&&g.localName===r||r===null&&g.prefix===null&&g.localName==="xmlns"))return g.value||null}var S=this.parentElement;return S?S.lookupNamespaceURI(r):null}},getAttribute:{value:function(r){var l=this.getAttributeNode(r);return l?l.value:null}},getAttributeNS:{value:function(r,l){var N=this.getAttributeNodeNS(r,l);return N?N.value:null}},getAttributeNode:{value:function(r){r=String(r),/[A-Z]/.test(r)&&this.isHTML&&(r=c.toASCIILowerCase(r));var l=this._attrsByQName[r];return l?(Array.isArray(l)&&(l=l[0]),l):null}},getAttributeNodeNS:{value:function(r,l){r=r==null?"":String(r),l=String(l);var N=this._attrsByLName[r+"|"+l];return N||null}},hasAttribute:{value:function(r){return r=String(r),/[A-Z]/.test(r)&&this.isHTML&&(r=c.toASCIILowerCase(r)),this._attrsByQName[r]!==void 0}},hasAttributeNS:{value:function(r,l){r=r==null?"":String(r),l=String(l);var N=r+"|"+l;return this._attrsByLName[N]!==void 0}},hasAttributes:{value:function(){return this._numattrs>0}},toggleAttribute:{value:function(r,l){r=String(r),h.isValidName(r)||c.InvalidCharacterError(),/[A-Z]/.test(r)&&this.isHTML&&(r=c.toASCIILowerCase(r));var N=this._attrsByQName[r];return N===void 0?l===void 0||l===!0?(this._setAttribute(r,""),!0):!1:l===void 0||l===!1?(this.removeAttribute(r),!1):!0}},_setAttribute:{value:function(r,l){var N=this._attrsByQName[r],g;N?Array.isArray(N)&&(N=N[0]):(N=this._newattr(r),g=!0),N.value=l,this._attributes&&(this._attributes[r]=N),g&&this._newattrhook&&this._newattrhook(r,l)}},setAttribute:{value:function(r,l){r=String(r),h.isValidName(r)||c.InvalidCharacterError(),/[A-Z]/.test(r)&&this.isHTML&&(r=c.toASCIILowerCase(r)),this._setAttribute(r,String(l))}},_setAttributeNS:{value:function(r,l,N){var g=l.indexOf(":"),S,C;g<0?(S=null,C=l):(S=l.substring(0,g),C=l.substring(g+1)),(r===""||r===void 0)&&(r=null);var G=(r===null?"":r)+"|"+C,ee=this._attrsByLName[G],y;ee||(ee=new d(this,C,S,r),y=!0,this._attrsByLName[G]=ee,this._attributes&&(this._attributes[this._attrKeys.length]=ee),this._attrKeys.push(G),this._addQName(ee)),ee.value=N,y&&this._newattrhook&&this._newattrhook(l,N)}},setAttributeNS:{value:function(r,l,N){r=r==null||r===""?null:String(r),l=String(l),h.isValidQName(l)||c.InvalidCharacterError();var g=l.indexOf(":"),S=g<0?null:l.substring(0,g);(S!==null&&r===null||S==="xml"&&r!==n.XML||(l==="xmlns"||S==="xmlns")&&r!==n.XMLNS||r===n.XMLNS&&!(l==="xmlns"||S==="xmlns"))&&c.NamespaceError(),this._setAttributeNS(r,l,String(N))}},setAttributeNode:{value:function(r){r.ownerElement!==null&&r.ownerElement!==this&&c.InUseAttributeError();var l=null,N=this._attrsByQName[r.name];if(N){if(Array.isArray(N)||(N=[N]),N.some(function(g){return g===r}))return r;r.ownerElement!==null&&c.InUseAttributeError(),N.forEach(function(g){this.removeAttributeNode(g)},this),l=N[0]}return this.setAttributeNodeNS(r),l}},setAttributeNodeNS:{value:function(r){r.ownerElement!==null&&c.InUseAttributeError();var l=r.namespaceURI,N=(l===null?"":l)+"|"+r.localName,g=this._attrsByLName[N];return g&&this.removeAttributeNode(g),r._setOwnerElement(this),this._attrsByLName[N]=r,this._attributes&&(this._attributes[this._attrKeys.length]=r),this._attrKeys.push(N),this._addQName(r),this._newattrhook&&this._newattrhook(r.name,r.value),g||null}},removeAttribute:{value:function(r){r=String(r),/[A-Z]/.test(r)&&this.isHTML&&(r=c.toASCIILowerCase(r));var l=this._attrsByQName[r];if(l){Array.isArray(l)?l.length>2?l=l.shift():(this._attrsByQName[r]=l[1],l=l[0]):this._attrsByQName[r]=void 0;var N=l.namespaceURI,g=(N===null?"":N)+"|"+l.localName;this._attrsByLName[g]=void 0;var S=this._attrKeys.indexOf(g);this._attributes&&(Array.prototype.splice.call(this._attributes,S,1),this._attributes[r]=void 0),this._attrKeys.splice(S,1);var C=l.onchange;l._setOwnerElement(null),C&&C.call(l,this,l.localName,l.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(l)}}},removeAttributeNS:{value:function(r,l){r=r==null?"":String(r),l=String(l);var N=r+"|"+l,g=this._attrsByLName[N];if(g){this._attrsByLName[N]=void 0;var S=this._attrKeys.indexOf(N);this._attributes&&Array.prototype.splice.call(this._attributes,S,1),this._attrKeys.splice(S,1),this._removeQName(g);var C=g.onchange;g._setOwnerElement(null),C&&C.call(g,this,g.localName,g.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(g)}}},removeAttributeNode:{value:function(r){var l=r.namespaceURI,N=(l===null?"":l)+"|"+r.localName;return this._attrsByLName[N]!==r&&c.NotFoundError(),this.removeAttributeNS(l,r.localName),r}},getAttributeNames:{value:function(){var r=this;return this._attrKeys.map(function(l){return r._attrsByLName[l].name})}},_getattr:{value:function(r){var l=this._attrsByQName[r];return l?l.value:null}},_setattr:{value:function(r,l){var N=this._attrsByQName[r],g;N||(N=this._newattr(r),g=!0),N.value=String(l),this._attributes&&(this._attributes[r]=N),g&&this._newattrhook&&this._newattrhook(r,l)}},_newattr:{value:function(r){var l=new d(this,r,null,null),N="|"+r;return this._attrsByQName[r]=l,this._attrsByLName[N]=l,this._attributes&&(this._attributes[this._attrKeys.length]=l),this._attrKeys.push(N),l}},_addQName:{value:function(e){var r=e.name,l=this._attrsByQName[r];l?Array.isArray(l)?l.push(e):this._attrsByQName[r]=[l,e]:this._attrsByQName[r]=e,this._attributes&&(this._attributes[r]=e)}},_removeQName:{value:function(e){var r=e.name,l=this._attrsByQName[r];if(Array.isArray(l)){var N=l.indexOf(e);c.assert(N!==-1),l.length===2?(this._attrsByQName[r]=l[1-N],this._attributes&&(this._attributes[r]=this._attrsByQName[r])):(l.splice(N,1),this._attributes&&this._attributes[r]===e&&(this._attributes[r]=l[0]))}else c.assert(l===e),this._attrsByQName[r]=void 0,this._attributes&&(this._attributes[r]=void 0)}},_numattrs:{get:function(){return this._attrKeys.length}},_attr:{value:function(e){return this._attrsByLName[this._attrKeys[e]]}},id:i.property({name:"id"}),className:i.property({name:"class"}),classList:{get:function(){var e=this;if(this._classList)return this._classList;var r=new b(function(){return e.className||""},function(l){e.className=l});return this._classList=r,r},set:function(e){this.className=e}},matches:{value:function(e){return p.matches(this,e)}},closest:{value:function(e){var r=this;do{if(r.matches&&r.matches(e))return r;r=r.parentElement||r.parentNode}while(r!==null&&r.nodeType===u.ELEMENT_NODE);return null}},querySelector:{value:function(e){return p(e,this)[0]}},querySelectorAll:{value:function(e){var r=p(e,this);return r.item?r:new o(r)}}}),Object.defineProperties(P.prototype,O),Object.defineProperties(P.prototype,R),i.registerChangeHandler(P,"id",function(e,r,l,N){e.rooted&&(l&&e.ownerDocument.delId(l,e),N&&e.ownerDocument.addId(N,e))}),i.registerChangeHandler(P,"class",function(e,r,l,N){e._classList&&e._classList._update()});function d(e,r,l,N,g){this.localName=r,this.prefix=l===null||l===""?null:""+l,this.namespaceURI=N===null||N===""?null:""+N,this.data=g,this._setOwnerElement(e)}d.prototype=Object.create(Object.prototype,{ownerElement:{get:function(){return this._ownerElement}},_setOwnerElement:{value:function(r){this._ownerElement=r,this.prefix===null&&this.namespaceURI===null&&r?this.onchange=r._attributeChangeHandlers[this.localName]:this.onchange=null}},name:{get:function(){return this.prefix?this.prefix+":"+this.localName:this.localName}},specified:{get:function(){return!0}},value:{get:function(){return this.data},set:function(e){var r=this.data;e=e===void 0?"":e+"",e!==r&&(this.data=e,this.ownerElement&&(this.onchange&&this.onchange(this.ownerElement,this.localName,r,e),this.ownerElement.rooted&&this.ownerElement.ownerDocument.mutateAttr(this,r)))}},cloneNode:{value:function(r){return new d(null,this.localName,this.prefix,this.namespaceURI,this.data)}},nodeType:{get:function(){return u.ATTRIBUTE_NODE}},nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return this.value},set:function(e){this.value=e}},textContent:{get:function(){return this.value},set:function(e){e==null&&(e=""),this.value=e}},innerText:{get:function(){return this.value},set:function(e){e==null&&(e=""),this.value=e}}}),P._Attr=d;function w(e){I.call(this,e);for(var r in e._attrsByQName)this[r]=e._attrsByQName[r];for(var l=0;l<e._attrKeys.length;l++)this[l]=e._attrsByLName[e._attrKeys[l]]}w.prototype=Object.create(I.prototype,{length:{get:function(){return this.element._attrKeys.length},set:function(){}},item:{value:function(e){return e=e>>>0,e>=this.length?null:this.element._attrsByLName[this.element._attrKeys[e]]}}});var m;(m=globalThis.Symbol)!=null&&m.iterator&&(w.prototype[globalThis.Symbol.iterator]=function(){var e=0,r=this.length,l=this;return{next:function(){return e<r?{value:l.item(e++)}:{done:!0}}}});function se(e){this.element=e,this.updateCache()}se.prototype=Object.create(Object.prototype,{length:{get:function(){return this.updateCache(),this.childrenByNumber.length}},item:{value:function(r){return this.updateCache(),this.childrenByNumber[r]||null}},namedItem:{value:function(r){return this.updateCache(),this.childrenByName[r]||null}},namedItems:{get:function(){return this.updateCache(),this.childrenByName}},updateCache:{value:function(){var r=/^(a|applet|area|embed|form|frame|frameset|iframe|img|object)$/;if(this.lastModTime!==this.element.lastModTime){this.lastModTime=this.element.lastModTime;for(var l=this.childrenByNumber&&this.childrenByNumber.length||0,N=0;N<l;N++)this[N]=void 0;this.childrenByNumber=[],this.childrenByName=Object.create(null);for(var g=this.element.firstChild;g!==null;g=g.nextSibling)if(g.nodeType===u.ELEMENT_NODE){this[this.childrenByNumber.length]=g,this.childrenByNumber.push(g);var S=g.getAttribute("id");S&&!this.childrenByName[S]&&(this.childrenByName[S]=g);var C=g.getAttribute("name");C&&this.element.namespaceURI===n.HTML&&r.test(this.element.localName)&&!this.childrenByName[C]&&(this.childrenByName[S]=g)}}}}});function ne(e){return function(r){return r.localName===e}}function Y(e){var r=c.toASCIILowerCase(e);return r===e?ne(e):function(l){return l.isHTML?l.localName===r:l.localName===e}}function V(e){return function(r){return r.namespaceURI===e}}function D(e,r){return function(l){return l.namespaceURI===e&&l.localName===r}}function B(e){return function(r){return e.every(function(l){return r.classList.contains(l)})}}function K(e){return function(r){return r.namespaceURI!==n.HTML?!1:r.getAttribute("name")===e}}}}),va=le({"external/npm/node_modules/domino/lib/Leaf.js"(_,k){k.exports=o;var h=Ge(),c=rr(),n=Be(),i=n.HierarchyRequestError,u=n.NotFoundError;function o(){h.call(this)}o.prototype=Object.create(h.prototype,{hasChildNodes:{value:function(){return!1}},firstChild:{value:null},lastChild:{value:null},insertBefore:{value:function(f,s){if(!f.nodeType)throw new TypeError("not a node");i()}},replaceChild:{value:function(f,s){if(!f.nodeType)throw new TypeError("not a node");i()}},removeChild:{value:function(f){if(!f.nodeType)throw new TypeError("not a node");u()}},removeChildren:{value:function(){}},childNodes:{get:function(){return this._childNodes||(this._childNodes=new c),this._childNodes}}})}}),Yr=le({"external/npm/node_modules/domino/lib/CharacterData.js"(_,k){k.exports=u;var h=va(),c=Be(),n=Nn(),i=ba();function u(){h.call(this)}u.prototype=Object.create(h.prototype,{substringData:{value:function(f,s){if(arguments.length<2)throw new TypeError("Not enough arguments");return f=f>>>0,s=s>>>0,(f>this.data.length||f<0||s<0)&&c.IndexSizeError(),this.data.substring(f,f+s)}},appendData:{value:function(f){if(arguments.length<1)throw new TypeError("Not enough arguments");this.data+=String(f)}},insertData:{value:function(f,s){return this.replaceData(f,0,s)}},deleteData:{value:function(f,s){return this.replaceData(f,s,"")}},replaceData:{value:function(f,s,b){var p=this.data,T=p.length;f=f>>>0,s=s>>>0,b=String(b),(f>T||f<0)&&c.IndexSizeError(),f+s>T&&(s=T-f);var O=p.substring(0,f),R=p.substring(f+s);this.data=O+b+R}},isEqual:{value:function(f){return this._data===f._data}},length:{get:function(){return this.data.length}}}),Object.defineProperties(u.prototype,n),Object.defineProperties(u.prototype,i)}}),Ea=le({"external/npm/node_modules/domino/lib/Text.js"(_,k){k.exports=i;var h=Be(),c=Ge(),n=Yr();function i(o,f){n.call(this),this.nodeType=c.TEXT_NODE,this.ownerDocument=o,this._data=f,this._index=void 0}var u={get:function(){return this._data},set:function(o){o==null?o="":o=String(o),o!==this._data&&(this._data=o,this.rooted&&this.ownerDocument.mutateValue(this),this.parentNode&&this.parentNode._textchangehook&&this.parentNode._textchangehook(this))}};i.prototype=Object.create(n.prototype,{nodeName:{value:"#text"},nodeValue:u,textContent:u,innerText:u,data:{get:u.get,set:function(o){u.set.call(this,o===null?"":String(o))}},splitText:{value:function(f){(f>this._data.length||f<0)&&h.IndexSizeError();var s=this._data.substring(f),b=this.ownerDocument.createTextNode(s);this.data=this.data.substring(0,f);var p=this.parentNode;return p!==null&&p.insertBefore(b,this.nextSibling),b}},wholeText:{get:function(){for(var f=this.textContent,s=this.nextSibling;s&&s.nodeType===c.TEXT_NODE;s=s.nextSibling)f+=s.textContent;return f}},replaceWholeText:{value:h.nyi},clone:{value:function(){return new i(this.ownerDocument,this._data)}}})}}),Ta=le({"external/npm/node_modules/domino/lib/Comment.js"(_,k){k.exports=n;var h=Ge(),c=Yr();function n(u,o){c.call(this),this.nodeType=h.COMMENT_NODE,this.ownerDocument=u,this._data=o}var i={get:function(){return this._data},set:function(u){u==null?u="":u=String(u),this._data=u,this.rooted&&this.ownerDocument.mutateValue(this)}};n.prototype=Object.create(c.prototype,{nodeName:{value:"#comment"},nodeValue:i,textContent:i,innerText:i,data:{get:i.get,set:function(u){i.set.call(this,u===null?"":String(u))}},clone:{value:function(){return new n(this.ownerDocument,this._data)}}})}}),ya=le({"external/npm/node_modules/domino/lib/DocumentFragment.js"(_,k){k.exports=f;var h=Ge(),c=rr(),n=Tn(),i=_r(),u=wn(),o=Be();function f(s){n.call(this),this.nodeType=h.DOCUMENT_FRAGMENT_NODE,this.ownerDocument=s}f.prototype=Object.create(n.prototype,{nodeName:{value:"#document-fragment"},nodeValue:{get:function(){return null},set:function(){}},textContent:Object.getOwnPropertyDescriptor(i.prototype,"textContent"),innerText:Object.getOwnPropertyDescriptor(i.prototype,"innerText"),querySelector:{value:function(s){var b=this.querySelectorAll(s);return b.length?b[0]:null}},querySelectorAll:{value:function(s){var b=Object.create(this);b.isHTML=!0,b.getElementsByTagName=i.prototype.getElementsByTagName,b.nextElement=Object.getOwnPropertyDescriptor(i.prototype,"firstElementChild").get;var p=u(s,b);return p.item?p:new c(p)}},clone:{value:function(){return new f(this.ownerDocument)}},isEqual:{value:function(b){return!0}},innerHTML:{get:function(){return this.serialize()},set:o.nyi},outerHTML:{get:function(){return this.serialize()},set:o.nyi}})}}),wa=le({"external/npm/node_modules/domino/lib/ProcessingInstruction.js"(_,k){k.exports=n;var h=Ge(),c=Yr();function n(u,o,f){c.call(this),this.nodeType=h.PROCESSING_INSTRUCTION_NODE,this.ownerDocument=u,this.target=o,this._data=f}var i={get:function(){return this._data},set:function(u){u==null?u="":u=String(u),this._data=u,this.rooted&&this.ownerDocument.mutateValue(this)}};n.prototype=Object.create(c.prototype,{nodeName:{get:function(){return this.target}},nodeValue:i,textContent:i,innerText:i,data:{get:i.get,set:function(u){i.set.call(this,u===null?"":String(u))}},clone:{value:function(){return new n(this.ownerDocument,this.target,this._data)}},isEqual:{value:function(o){return this.target===o.target&&this._data===o._data}}})}}),$r=le({"external/npm/node_modules/domino/lib/NodeFilter.js"(_,k){var h={FILTER_ACCEPT:1,FILTER_REJECT:2,FILTER_SKIP:3,SHOW_ALL:4294967295,SHOW_ELEMENT:1,SHOW_ATTRIBUTE:2,SHOW_TEXT:4,SHOW_CDATA_SECTION:8,SHOW_ENTITY_REFERENCE:16,SHOW_ENTITY:32,SHOW_PROCESSING_INSTRUCTION:64,SHOW_COMMENT:128,SHOW_DOCUMENT:256,SHOW_DOCUMENT_TYPE:512,SHOW_DOCUMENT_FRAGMENT:1024,SHOW_NOTATION:2048};k.exports=h.constructor=h.prototype=h}}),Na=le({"external/npm/node_modules/domino/lib/NodeTraversal.js"(_,k){k.exports={nextSkippingChildren:h,nextAncestorSibling:c,next:n,previous:u,deepLastChild:i};function h(o,f){return o===f?null:o.nextSibling!==null?o.nextSibling:c(o,f)}function c(o,f){for(o=o.parentNode;o!==null;o=o.parentNode){if(o===f)return null;if(o.nextSibling!==null)return o.nextSibling}return null}function n(o,f){var s;return s=o.firstChild,s!==null?s:o===f?null:(s=o.nextSibling,s!==null?s:c(o,f))}function i(o){for(;o.lastChild;)o=o.lastChild;return o}function u(o,f){var s;return s=o.previousSibling,s!==null?i(s):(s=o.parentNode,s===f?null:s)}}}),fs=le({"external/npm/node_modules/domino/lib/TreeWalker.js"(_,k){k.exports=b;var h=Ge(),c=$r(),n=Na(),i=Be(),u={first:"firstChild",last:"lastChild",next:"firstChild",previous:"lastChild"},o={first:"nextSibling",last:"previousSibling",next:"nextSibling",previous:"previousSibling"};function f(p,T){var O,R,I,J,P;for(R=p._currentNode[u[T]];R!==null;){if(J=p._internalFilter(R),J===c.FILTER_ACCEPT)return p._currentNode=R,R;if(J===c.FILTER_SKIP&&(O=R[u[T]],O!==null)){R=O;continue}for(;R!==null;){if(P=R[o[T]],P!==null){R=P;break}if(I=R.parentNode,I===null||I===p.root||I===p._currentNode)return null;R=I}}return null}function s(p,T){var O,R,I;if(O=p._currentNode,O===p.root)return null;for(;;){for(I=O[o[T]];I!==null;){if(O=I,R=p._internalFilter(O),R===c.FILTER_ACCEPT)return p._currentNode=O,O;I=O[u[T]],(R===c.FILTER_REJECT||I===null)&&(I=O[o[T]])}if(O=O.parentNode,O===null||O===p.root||p._internalFilter(O)===c.FILTER_ACCEPT)return null}}function b(p,T,O){(!p||!p.nodeType)&&i.NotSupportedError(),this._root=p,this._whatToShow=Number(T)||0,this._filter=O||null,this._active=!1,this._currentNode=p}Object.defineProperties(b.prototype,{root:{get:function(){return this._root}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},currentNode:{get:function(){return this._currentNode},set:function(T){if(!(T instanceof h))throw new TypeError("Not a Node");this._currentNode=T}},_internalFilter:{value:function(T){var O,R;if(this._active&&i.InvalidStateError(),!(1<<T.nodeType-1&this._whatToShow))return c.FILTER_SKIP;if(R=this._filter,R===null)O=c.FILTER_ACCEPT;else{this._active=!0;try{typeof R=="function"?O=R(T):O=R.acceptNode(T)}finally{this._active=!1}}return+O}},parentNode:{value:function(){for(var T=this._currentNode;T!==this.root;){if(T=T.parentNode,T===null)return null;if(this._internalFilter(T)===c.FILTER_ACCEPT)return this._currentNode=T,T}return null}},firstChild:{value:function(){return f(this,"first")}},lastChild:{value:function(){return f(this,"last")}},previousSibling:{value:function(){return s(this,"previous")}},nextSibling:{value:function(){return s(this,"next")}},previousNode:{value:function(){var T,O,R,I;for(T=this._currentNode;T!==this._root;){for(R=T.previousSibling;R;R=T.previousSibling)if(T=R,O=this._internalFilter(T),O!==c.FILTER_REJECT){for(I=T.lastChild;I&&(T=I,O=this._internalFilter(T),O!==c.FILTER_REJECT);I=T.lastChild);if(O===c.FILTER_ACCEPT)return this._currentNode=T,T}if(T===this.root||T.parentNode===null)return null;if(T=T.parentNode,this._internalFilter(T)===c.FILTER_ACCEPT)return this._currentNode=T,T}return null}},nextNode:{value:function(){var T,O,R,I;T=this._currentNode,O=c.FILTER_ACCEPT;e:for(;;){for(R=T.firstChild;R;R=T.firstChild){if(T=R,O=this._internalFilter(T),O===c.FILTER_ACCEPT)return this._currentNode=T,T;if(O===c.FILTER_REJECT)break}for(I=n.nextSkippingChildren(T,this.root);I;I=n.nextSkippingChildren(T,this.root)){if(T=I,O=this._internalFilter(T),O===c.FILTER_ACCEPT)return this._currentNode=T,T;if(O===c.FILTER_SKIP)continue e}return null}}},toString:{value:function(){return"[object TreeWalker]"}}})}}),hs=le({"external/npm/node_modules/domino/lib/NodeIterator.js"(_,k){k.exports=f;var h=$r(),c=Na(),n=Be();function i(s,b,p){return p?c.next(s,b):s===b?null:c.previous(s,null)}function u(s,b){for(;b;b=b.parentNode)if(s===b)return!0;return!1}function o(s,b){var p,T;for(p=s._referenceNode,T=s._pointerBeforeReferenceNode;;){if(T===b)T=!T;else if(p=i(p,s._root,b),p===null)return null;var O=s._internalFilter(p);if(O===h.FILTER_ACCEPT)break}return s._referenceNode=p,s._pointerBeforeReferenceNode=T,p}function f(s,b,p){(!s||!s.nodeType)&&n.NotSupportedError(),this._root=s,this._referenceNode=s,this._pointerBeforeReferenceNode=!0,this._whatToShow=Number(b)||0,this._filter=p||null,this._active=!1,s.doc._attachNodeIterator(this)}Object.defineProperties(f.prototype,{root:{get:function(){return this._root}},referenceNode:{get:function(){return this._referenceNode}},pointerBeforeReferenceNode:{get:function(){return this._pointerBeforeReferenceNode}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},_internalFilter:{value:function(b){var p,T;if(this._active&&n.InvalidStateError(),!(1<<b.nodeType-1&this._whatToShow))return h.FILTER_SKIP;if(T=this._filter,T===null)p=h.FILTER_ACCEPT;else{this._active=!0;try{typeof T=="function"?p=T(b):p=T.acceptNode(b)}finally{this._active=!1}}return+p}},_preremove:{value:function(b){if(!u(b,this._root)&&u(b,this._referenceNode)){if(this._pointerBeforeReferenceNode){for(var p=b;p.lastChild;)p=p.lastChild;if(p=c.next(p,this.root),p){this._referenceNode=p;return}this._pointerBeforeReferenceNode=!1}if(b.previousSibling===null)this._referenceNode=b.parentNode;else{this._referenceNode=b.previousSibling;var T;for(T=this._referenceNode.lastChild;T;T=this._referenceNode.lastChild)this._referenceNode=T}}}},nextNode:{value:function(){return o(this,!0)}},previousNode:{value:function(){return o(this,!1)}},detach:{value:function(){}},toString:{value:function(){return"[object NodeIterator]"}}})}}),Sn=le({"external/npm/node_modules/domino/lib/URL.js"(_,k){k.exports=h;function h(c){if(!c)return Object.create(h.prototype);this.url=c.replace(/^[ \t\n\r\f]+|[ \t\n\r\f]+$/g,"");var n=h.pattern.exec(this.url);if(n){if(n[2]&&(this.scheme=n[2]),n[4]){var i=n[4].match(h.userinfoPattern);if(i&&(this.username=i[1],this.password=i[3],n[4]=n[4].substring(i[0].length)),n[4].match(h.portPattern)){var u=n[4].lastIndexOf(":");this.host=n[4].substring(0,u),this.port=n[4].substring(u+1)}else this.host=n[4]}n[5]&&(this.path=n[5]),n[6]&&(this.query=n[7]),n[8]&&(this.fragment=n[9])}}h.pattern=/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/,h.userinfoPattern=/^([^@:]*)(:([^@]*))?@/,h.portPattern=/:\d+$/,h.authorityPattern=/^[^:\/?#]+:\/\//,h.hierarchyPattern=/^[^:\/?#]+:\//,h.percentEncode=function(n){var i=n.charCodeAt(0);if(i<256)return"%"+i.toString(16);throw Error("can't percent-encode codepoints > 255 yet")},h.prototype={constructor:h,isAbsolute:function(){return!!this.scheme},isAuthorityBased:function(){return h.authorityPattern.test(this.url)},isHierarchical:function(){return h.hierarchyPattern.test(this.url)},toString:function(){var c="";return this.scheme!==void 0&&(c+=this.scheme+":"),this.isAbsolute()&&(c+="//",(this.username||this.password)&&(c+=this.username||"",this.password&&(c+=":"+this.password),c+="@"),this.host&&(c+=this.host)),this.port!==void 0&&(c+=":"+this.port),this.path!==void 0&&(c+=this.path),this.query!==void 0&&(c+="?"+this.query),this.fragment!==void 0&&(c+="#"+this.fragment),c},resolve:function(c){var n=this,i=new h(c),u=new h;return i.scheme!==void 0?(u.scheme=i.scheme,u.username=i.username,u.password=i.password,u.host=i.host,u.port=i.port,u.path=f(i.path),u.query=i.query):(u.scheme=n.scheme,i.host!==void 0?(u.username=i.username,u.password=i.password,u.host=i.host,u.port=i.port,u.path=f(i.path),u.query=i.query):(u.username=n.username,u.password=n.password,u.host=n.host,u.port=n.port,i.path?(i.path.charAt(0)==="/"?u.path=f(i.path):(u.path=o(n.path,i.path),u.path=f(u.path)),u.query=i.query):(u.path=n.path,i.query!==void 0?u.query=i.query:u.query=n.query))),u.fragment=i.fragment,u.toString();function o(s,b){if(n.host!==void 0&&!n.path)return"/"+b;var p=s.lastIndexOf("/");return p===-1?b:s.substring(0,p+1)+b}function f(s){if(!s)return s;for(var b="";s.length>0;){if(s==="."||s===".."){s="";break}var p=s.substring(0,2),T=s.substring(0,3),O=s.substring(0,4);if(T==="../")s=s.substring(3);else if(p==="./")s=s.substring(2);else if(T==="/./")s="/"+s.substring(3);else if(p==="/."&&s.length===2)s="/";else if(O==="/../"||T==="/.."&&s.length===3)s="/"+s.substring(4),b=b.replace(/\/?[^\/]*$/,"");else{var R=s.match(/(\/?([^\/]*))/)[0];b+=R,s=s.substring(R.length)}}return b}}}}}),ps=le({"external/npm/node_modules/domino/lib/CustomEvent.js"(_,k){k.exports=c;var h=br();function c(n,i){h.call(this,n,i)}c.prototype=Object.create(h.prototype,{constructor:{value:c}})}}),Sa=le({"external/npm/node_modules/domino/lib/events.js"(_,k){k.exports={Event:br(),UIEvent:ua(),MouseEvent:fa(),CustomEvent:ps()}}}),ds=le({"external/npm/node_modules/domino/lib/style_parser.js"(_){Object.defineProperty(_,"__esModule",{value:!0}),_.hyphenate=_.parse=void 0;function k(c){let n=[],i=0,u=0,o=0,f=0,s=0,b=null;for(;i<c.length;)switch(c.charCodeAt(i++)){case 40:u++;break;case 41:u--;break;case 39:o===0?o=39:o===39&&c.charCodeAt(i-1)!==92&&(o=0);break;case 34:o===0?o=34:o===34&&c.charCodeAt(i-1)!==92&&(o=0);break;case 58:!b&&u===0&&o===0&&(b=h(c.substring(s,i-1).trim()),f=i);break;case 59:if(b&&f>0&&u===0&&o===0){let T=c.substring(f,i-1).trim();n.push(b,T),s=i,f=0,b=null}break}if(b&&f){let p=c.slice(f).trim();n.push(b,p)}return n}_.parse=k;function h(c){return c.replace(/[a-z][A-Z]/g,n=>n.charAt(0)+"-"+n.charAt(1)).toLowerCase()}_.hyphenate=h}}),kn=le({"external/npm/node_modules/domino/lib/CSSStyleDeclaration.js"(_,k){var{parse:h}=ds();k.exports=function(f){let s=new n(f),b={get:function(p,T){return T in p?p[T]:p.getPropertyValue(c(T))},has:function(p,T){return!0},set:function(p,T,O){return T in p?p[T]=O:p.setProperty(c(T),O??void 0),!0}};return new Proxy(s,b)};function c(f){return f.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function n(f){this._element=f}var i="!important";function u(f){let s={property:{},priority:{}};if(!f)return s;let b=h(f);if(b.length<2)return s;for(let p=0;p<b.length;p+=2){let T=b[p],O=b[p+1];O.endsWith(i)&&(s.priority[T]="important",O=O.slice(0,-i.length).trim()),s.property[T]=O}return s}var o={};n.prototype=Object.create(Object.prototype,{_parsed:{get:function(){if(!this._parsedStyles||this.cssText!==this._lastParsedText){var f=this.cssText;this._parsedStyles=u(f),this._lastParsedText=f,delete this._names}return this._parsedStyles}},_serialize:{value:function(){var f=this._parsed,s="";for(var b in f.property)s&&(s+=" "),s+=b+": "+f.property[b],f.priority[b]&&(s+=" !"+f.priority[b]),s+=";";this.cssText=s,this._lastParsedText=s,delete this._names}},cssText:{get:function(){return this._element.getAttribute("style")},set:function(f){this._element.setAttribute("style",f)}},length:{get:function(){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names.length}},item:{value:function(f){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names[f]}},getPropertyValue:{value:function(f){return f=f.toLowerCase(),this._parsed.property[f]||""}},getPropertyPriority:{value:function(f){return f=f.toLowerCase(),this._parsed.priority[f]||""}},setProperty:{value:function(f,s,b){if(f=f.toLowerCase(),s==null&&(s=""),b==null&&(b=""),s!==o&&(s=""+s),s=s.trim(),s===""){this.removeProperty(f);return}if(!(b!==""&&b!==o&&!/^important$/i.test(b))){var p=this._parsed;if(s===o){if(!p.property[f])return;b!==""?p.priority[f]="important":delete p.priority[f]}else{if(s.includes(";")&&!s.includes("data:"))return;var T=u(f+":"+s);if(Object.getOwnPropertyNames(T.property).length===0||Object.getOwnPropertyNames(T.priority).length!==0)return;for(var O in T.property)p.property[O]=T.property[O],b!==o&&(b!==""?p.priority[O]="important":p.priority[O]&&delete p.priority[O])}this._serialize()}}},setPropertyValue:{value:function(f,s){return this.setProperty(f,s,o)}},setPropertyPriority:{value:function(f,s){return this.setProperty(f,o,s)}},removeProperty:{value:function(f){f=f.toLowerCase();var s=this._parsed;f in s.property&&(delete s.property[f],delete s.priority[f],this._serialize())}}})}}),ka=le({"external/npm/node_modules/domino/lib/URLUtils.js"(_,k){var h=Sn();k.exports=c;function c(){}c.prototype=Object.create(Object.prototype,{_url:{get:function(){return new h(this.href)}},protocol:{get:function(){var n=this._url;return n&&n.scheme?n.scheme+":":":"},set:function(n){var i=this.href,u=new h(i);u.isAbsolute()&&(n=n.replace(/:+$/,""),n=n.replace(/[^-+\.a-zA-Z0-9]/g,h.percentEncode),n.length>0&&(u.scheme=n,i=u.toString())),this.href=i}},host:{get:function(){var n=this._url;return n.isAbsolute()&&n.isAuthorityBased()?n.host+(n.port?":"+n.port:""):""},set:function(n){var i=this.href,u=new h(i);u.isAbsolute()&&u.isAuthorityBased()&&(n=n.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,h.percentEncode),n.length>0&&(u.host=n,delete u.port,i=u.toString())),this.href=i}},hostname:{get:function(){var n=this._url;return n.isAbsolute()&&n.isAuthorityBased()?n.host:""},set:function(n){var i=this.href,u=new h(i);u.isAbsolute()&&u.isAuthorityBased()&&(n=n.replace(/^\/+/,""),n=n.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,h.percentEncode),n.length>0&&(u.host=n,i=u.toString())),this.href=i}},port:{get:function(){var n=this._url;return n.isAbsolute()&&n.isAuthorityBased()&&n.port!==void 0?n.port:""},set:function(n){var i=this.href,u=new h(i);u.isAbsolute()&&u.isAuthorityBased()&&(n=""+n,n=n.replace(/[^0-9].*$/,""),n=n.replace(/^0+/,""),n.length===0&&(n="0"),parseInt(n,10)<=65535&&(u.port=n,i=u.toString())),this.href=i}},pathname:{get:function(){var n=this._url;return n.isAbsolute()&&n.isHierarchical()?n.path:""},set:function(n){var i=this.href,u=new h(i);u.isAbsolute()&&u.isHierarchical()&&(n.charAt(0)!=="/"&&(n="/"+n),n=n.replace(/[^-+\._~!$&'()*,;:=@\/a-zA-Z0-9]/g,h.percentEncode),u.path=n,i=u.toString()),this.href=i}},search:{get:function(){var n=this._url;return n.isAbsolute()&&n.isHierarchical()&&n.query!==void 0?"?"+n.query:""},set:function(n){var i=this.href,u=new h(i);u.isAbsolute()&&u.isHierarchical()&&(n.charAt(0)==="?"&&(n=n.substring(1)),n=n.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,h.percentEncode),u.query=n,i=u.toString()),this.href=i}},hash:{get:function(){var n=this._url;return n==null||n.fragment==null||n.fragment===""?"":"#"+n.fragment},set:function(n){var i=this.href,u=new h(i);n.charAt(0)==="#"&&(n=n.substring(1)),n=n.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,h.percentEncode),u.fragment=n,i=u.toString(),this.href=i}},username:{get:function(){var n=this._url;return n.username||""},set:function(n){var i=this.href,u=new h(i);u.isAbsolute()&&(n=n.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\:]/g,h.percentEncode),u.username=n,i=u.toString()),this.href=i}},password:{get:function(){var n=this._url;return n.password||""},set:function(n){var i=this.href,u=new h(i);u.isAbsolute()&&(n===""?u.password=null:(n=n.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\]/g,h.percentEncode),u.password=n),i=u.toString()),this.href=i}},origin:{get:function(){var n=this._url;if(n==null)return"";var i=function(u){var o=[n.scheme,n.host,+n.port||u];return o[0]+"://"+o[1]+(o[2]===u?"":":"+o[2])};switch(n.scheme){case"ftp":return i(21);case"gopher":return i(70);case"http":case"ws":return i(80);case"https":case"wss":return i(443);default:return n.scheme+"://"}}}}),c._inherit=function(n){Object.getOwnPropertyNames(c.prototype).forEach(function(i){if(!(i==="constructor"||i==="href")){var u=Object.getOwnPropertyDescriptor(c.prototype,i);Object.defineProperty(n,i,u)}})}}}),La=le({"external/npm/node_modules/domino/lib/defineElement.js"(_,k){var h=ma(),c=En().isApiWritable;k.exports=function(o,f,s,b){var p=o.ctor;if(p){var T=o.props||{};if(o.attributes)for(var O in o.attributes){var R=o.attributes[O];(typeof R!="object"||Array.isArray(R))&&(R={type:R}),R.name||(R.name=O.toLowerCase()),T[O]=h.property(R)}T.constructor={value:p,writable:c},p.prototype=Object.create((o.superclass||f).prototype,T),o.events&&u(p,o.events),s[o.name]=p}else p=f;return(o.tags||o.tag&&[o.tag]||[]).forEach(function(I){b[I]=p}),p};function n(o,f,s,b){this.body=o,this.document=f,this.form=s,this.element=b}n.prototype.build=function(){return()=>{}};function i(o,f,s,b){var p=o.ownerDocument||Object.create(null),T=o.form||Object.create(null);o[f]=new n(b,p,T,o).build()}function u(o,f){var s=o.prototype;f.forEach(function(b){Object.defineProperty(s,"on"+b,{get:function(){return this._getEventHandler(b)},set:function(p){this._setEventHandler(b,p)}}),h.registerChangeHandler(o,"on"+b,i)})}}}),Ln=le({"external/npm/node_modules/domino/lib/htmlelts.js"(_){var k=Ge(),h=_r(),c=kn(),n=Be(),i=ka(),u=La(),o=_.elements={},f=Object.create(null);_.createElement=function(E,d,w){var m=f[d]||J;return new m(E,d,w)};function s(E){return u(E,I,o,f)}function b(E){return{get:function(){var d=this._getattr(E);if(d===null)return"";var w=this.doc._resolve(d);return w===null?d:w},set:function(d){this._setattr(E,d)}}}function p(E){return{get:function(){var d=this._getattr(E);return d===null?null:d.toLowerCase()==="use-credentials"?"use-credentials":"anonymous"},set:function(d){d==null?this.removeAttribute(E):this._setattr(E,d)}}}var T={type:["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"],missing:""},O={A:!0,LINK:!0,BUTTON:!0,INPUT:!0,SELECT:!0,TEXTAREA:!0,COMMAND:!0},R=function(E,d,w){I.call(this,E,d,w),this._form=null},I=_.HTMLElement=s({superclass:h,name:"HTMLElement",ctor:function(d,w,m){h.call(this,d,w,n.NAMESPACE.HTML,m)},props:{dangerouslySetInnerHTML:{set:function(E){this._innerHTML=E}},innerHTML:{get:function(){return this.serialize()},set:function(E){var d=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,this);d.parse(E===null?"":String(E),!0);for(var w=this instanceof f.template?this.content:this;w.hasChildNodes();)w.removeChild(w.firstChild);w.appendChild(d._asDocumentFragment())}},style:{get:function(){return this._style||(this._style=new c(this)),this._style},set:function(E){E==null&&(E=""),this._setattr("style",String(E))}},blur:{value:function(){}},focus:{value:function(){}},forceSpellCheck:{value:function(){}},click:{value:function(){if(!this._click_in_progress){this._click_in_progress=!0;try{this._pre_click_activation_steps&&this._pre_click_activation_steps();var E=this.ownerDocument.createEvent("MouseEvent");E.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,0,0,0,0,!1,!1,!1,!1,0,null);var d=this.dispatchEvent(E);d?this._post_click_activation_steps&&this._post_click_activation_steps(E):this._cancelled_activation_steps&&this._cancelled_activation_steps()}finally{this._click_in_progress=!1}}}},submit:{value:n.nyi}},attributes:{title:String,lang:String,dir:{type:["ltr","rtl","auto"],missing:""},draggable:{type:["true","false"],treatNullAsEmptyString:!0},spellcheck:{type:["true","false"],missing:""},enterKeyHint:{type:["enter","done","go","next","previous","search","send"],missing:""},autoCapitalize:{type:["off","on","none","sentences","words","characters"],missing:""},autoFocus:Boolean,accessKey:String,nonce:String,hidden:Boolean,translate:{type:["no","yes"],missing:""},tabIndex:{type:"long",default:function(){return this.tagName in O||this.contentEditable?0:-1}}},events:["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"]}),J=s({name:"HTMLUnknownElement",ctor:function(d,w,m){I.call(this,d,w,m)}}),P={form:{get:function(){return this._form}}};s({tag:"a",name:"HTMLAnchorElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{_post_click_activation_steps:{value:function(E){this.href&&(this.ownerDocument.defaultView.location=this.href)}}},attributes:{href:b,ping:String,download:String,target:String,rel:String,media:String,hreflang:String,type:String,referrerPolicy:T,coords:String,charset:String,name:String,rev:String,shape:String}}),i._inherit(f.a.prototype),s({tag:"area",name:"HTMLAreaElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{alt:String,target:String,download:String,rel:String,media:String,href:b,hreflang:String,type:String,shape:String,coords:String,ping:String,referrerPolicy:T,noHref:Boolean}}),i._inherit(f.area.prototype),s({tag:"br",name:"HTMLBRElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{clear:String}}),s({tag:"base",name:"HTMLBaseElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{target:String}}),s({tag:"body",name:"HTMLBodyElement",ctor:function(d,w,m){I.call(this,d,w,m)},events:["afterprint","beforeprint","beforeunload","blur","error","focus","hashchange","load","message","offline","online","pagehide","pageshow","popstate","resize","scroll","storage","unload"],attributes:{text:{type:String,treatNullAsEmptyString:!0},link:{type:String,treatNullAsEmptyString:!0},vLink:{type:String,treatNullAsEmptyString:!0},aLink:{type:String,treatNullAsEmptyString:!0},bgColor:{type:String,treatNullAsEmptyString:!0},background:String}}),s({tag:"button",name:"HTMLButtonElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:P,attributes:{name:String,value:String,disabled:Boolean,autofocus:Boolean,type:{type:["submit","reset","button","menu"],missing:"submit"},formTarget:String,formAction:b,formNoValidate:Boolean,formMethod:{type:["get","post","dialog"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""}}}),s({tag:"dl",name:"HTMLDListElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{compact:Boolean}}),s({tag:"data",name:"HTMLDataElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{value:String}}),s({tag:"datalist",name:"HTMLDataListElement",ctor:function(d,w,m){I.call(this,d,w,m)}}),s({tag:"details",name:"HTMLDetailsElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{open:Boolean}}),s({tag:"div",name:"HTMLDivElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{align:String}}),s({tag:"embed",name:"HTMLEmbedElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{src:b,type:String,width:String,height:String,align:String,name:String}}),s({tag:"fieldset",name:"HTMLFieldSetElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:P,attributes:{disabled:Boolean,name:String}}),s({tag:"form",name:"HTMLFormElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{action:String,autocomplete:{type:["on","off"],missing:"on"},name:String,acceptCharset:{name:"accept-charset"},target:String,noValidate:Boolean,method:{type:["get","post","dialog"],invalid:"get",missing:"get"},enctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"},encoding:{name:"enctype",type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"}}}),s({tag:"hr",name:"HTMLHRElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{align:String,color:String,noShade:Boolean,size:String,width:String}}),s({tag:"head",name:"HTMLHeadElement",ctor:function(d,w,m){I.call(this,d,w,m)}}),s({tags:["h1","h2","h3","h4","h5","h6"],name:"HTMLHeadingElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{align:String}}),s({tag:"html",name:"HTMLHtmlElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{xmlns:b,version:String}}),s({tag:"iframe",name:"HTMLIFrameElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{src:b,srcdoc:String,name:String,width:String,height:String,seamless:Boolean,allow:Boolean,allowFullscreen:Boolean,allowUserMedia:Boolean,allowPaymentRequest:Boolean,referrerPolicy:T,loading:{type:["eager","lazy"],treatNullAsEmptyString:!0},align:String,scrolling:String,frameBorder:String,longDesc:b,marginHeight:{type:String,treatNullAsEmptyString:!0},marginWidth:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"img",name:"HTMLImageElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{alt:String,src:b,srcset:String,crossOrigin:p,useMap:String,isMap:Boolean,sizes:String,height:{type:"unsigned long",default:0},width:{type:"unsigned long",default:0},referrerPolicy:T,loading:{type:["eager","lazy"],missing:""},name:String,lowsrc:b,align:String,hspace:{type:"unsigned long",default:0},vspace:{type:"unsigned long",default:0},longDesc:b,border:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"input",name:"HTMLInputElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:{form:P.form,_post_click_activation_steps:{value:function(E){if(this.type==="checkbox")this.checked=!this.checked;else if(this.type==="radio")for(var d=this.form.getElementsByName(this.name),w=d.length-1;w>=0;w--){var m=d[w];m.checked=m===this}}}},attributes:{name:String,disabled:Boolean,autofocus:Boolean,accept:String,alt:String,max:String,min:String,pattern:String,placeholder:String,step:String,dirName:String,defaultValue:{name:"value"},multiple:Boolean,required:Boolean,readOnly:Boolean,checked:Boolean,value:String,src:b,defaultChecked:{name:"checked",type:Boolean},size:{type:"unsigned long",default:20,min:1,setmin:1},width:{type:"unsigned long",min:0,setmin:0,default:0},height:{type:"unsigned long",min:0,setmin:0,default:0},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},autocomplete:String,type:{type:["text","hidden","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"],missing:"text"},formTarget:String,formNoValidate:Boolean,formMethod:{type:["get","post"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""},align:String,useMap:String}}),s({tag:"keygen",name:"HTMLKeygenElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:P,attributes:{name:String,disabled:Boolean,autofocus:Boolean,challenge:String,keytype:{type:["rsa"],missing:""}}}),s({tag:"li",name:"HTMLLIElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{value:{type:"long",default:0},type:String}}),s({tag:"label",name:"HTMLLabelElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:P,attributes:{htmlFor:{name:"for",type:String}}}),s({tag:"legend",name:"HTMLLegendElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{align:String}}),s({tag:"link",name:"HTMLLinkElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{href:b,rel:String,media:String,hreflang:String,type:String,crossOrigin:p,nonce:String,integrity:String,referrerPolicy:T,imageSizes:String,imageSrcset:String,charset:String,rev:String,target:String}}),s({tag:"map",name:"HTMLMapElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{name:String}}),s({tag:"menu",name:"HTMLMenuElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{type:{type:["context","popup","toolbar"],missing:"toolbar"},label:String,compact:Boolean}}),s({tag:"meta",name:"HTMLMetaElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{name:String,content:String,httpEquiv:{name:"http-equiv",type:String},scheme:String}}),s({tag:"meter",name:"HTMLMeterElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:P}),s({tags:["ins","del"],name:"HTMLModElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{cite:b,dateTime:String}}),s({tag:"ol",name:"HTMLOListElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{_numitems:{get:function(){var E=0;return this.childNodes.forEach(function(d){d.nodeType===k.ELEMENT_NODE&&d.tagName==="LI"&&E++}),E}}},attributes:{type:String,reversed:Boolean,start:{type:"long",default:function(){return this.reversed?this._numitems:1}},compact:Boolean}}),s({tag:"object",name:"HTMLObjectElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:P,attributes:{data:b,type:String,name:String,useMap:String,typeMustMatch:Boolean,width:String,height:String,align:String,archive:String,code:String,declare:Boolean,hspace:{type:"unsigned long",default:0},standby:String,vspace:{type:"unsigned long",default:0},codeBase:b,codeType:String,border:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"optgroup",name:"HTMLOptGroupElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{disabled:Boolean,label:String}}),s({tag:"option",name:"HTMLOptionElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{form:{get:function(){for(var E=this.parentNode;E&&E.nodeType===k.ELEMENT_NODE;){if(E.localName==="select")return E.form;E=E.parentNode}}},value:{get:function(){return this._getattr("value")||this.text},set:function(E){this._setattr("value",E)}},text:{get:function(){return this.textContent.replace(/[ \t\n\f\r]+/g," ").trim()},set:function(E){this.textContent=E}}},attributes:{disabled:Boolean,defaultSelected:{name:"selected",type:Boolean},label:String}}),s({tag:"output",name:"HTMLOutputElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:P,attributes:{name:String}}),s({tag:"p",name:"HTMLParagraphElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{align:String}}),s({tag:"param",name:"HTMLParamElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{name:String,value:String,type:String,valueType:String}}),s({tags:["pre","listing","xmp"],name:"HTMLPreElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{width:{type:"long",default:0}}}),s({tag:"progress",name:"HTMLProgressElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:P,attributes:{max:{type:Number,float:!0,default:1,min:0}}}),s({tags:["q","blockquote"],name:"HTMLQuoteElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{cite:b}}),s({tag:"script",name:"HTMLScriptElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{text:{get:function(){for(var E="",d=0,w=this.childNodes.length;d<w;d++){var m=this.childNodes[d];m.nodeType===k.TEXT_NODE&&(E+=m._data)}return E},set:function(E){this.removeChildren(),E!==null&&E!==""&&this.appendChild(this.ownerDocument.createTextNode(E))}}},attributes:{src:b,type:String,charset:String,referrerPolicy:T,defer:Boolean,async:Boolean,nomodule:Boolean,crossOrigin:p,nonce:String,integrity:String}}),s({tag:"select",name:"HTMLSelectElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:{form:P.form,options:{get:function(){return this.getElementsByTagName("option")}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,multiple:Boolean,required:Boolean,size:{type:"unsigned long",default:0}}}),s({tag:"span",name:"HTMLSpanElement",ctor:function(d,w,m){I.call(this,d,w,m)}}),s({tag:"style",name:"HTMLStyleElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{media:String,type:String,scoped:Boolean}}),s({tag:"caption",name:"HTMLTableCaptionElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{align:String}}),s({name:"HTMLTableCellElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{colSpan:{type:"unsigned long",default:1},rowSpan:{type:"unsigned long",default:1},scope:{type:["row","col","rowgroup","colgroup"],missing:""},abbr:String,align:String,axis:String,height:String,width:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},noWrap:Boolean,vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),s({tags:["col","colgroup"],name:"HTMLTableColElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{span:{type:"limited unsigned long with fallback",default:1,min:1},align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,width:String}}),s({tag:"table",name:"HTMLTableElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,border:String,frame:String,rules:String,summary:String,width:String,bgColor:{type:String,treatNullAsEmptyString:!0},cellPadding:{type:String,treatNullAsEmptyString:!0},cellSpacing:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"template",name:"HTMLTemplateElement",ctor:function(d,w,m){I.call(this,d,w,m),this._contentFragment=d._templateDoc.createDocumentFragment()},props:{content:{get:function(){return this._contentFragment}},serialize:{value:function(){return this.content.serialize()}}}}),s({tag:"tr",name:"HTMLTableRowElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{cells:{get:function(){return this.querySelectorAll("td,th")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),s({tags:["thead","tfoot","tbody"],name:"HTMLTableSectionElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String}}),s({tag:"textarea",name:"HTMLTextAreaElement",ctor:function(d,w,m){R.call(this,d,w,m)},props:{form:P.form,type:{get:function(){return"textarea"}},defaultValue:{get:function(){return this.textContent},set:function(E){this.textContent=E}},value:{get:function(){return this.defaultValue},set:function(E){this.defaultValue=E}},textLength:{get:function(){return this.value.length}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,placeholder:String,wrap:String,dirName:String,required:Boolean,readOnly:Boolean,rows:{type:"limited unsigned long with fallback",default:2},cols:{type:"limited unsigned long with fallback",default:20},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""}}}),s({tag:"time",name:"HTMLTimeElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{dateTime:String,pubDate:Boolean}}),s({tag:"title",name:"HTMLTitleElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{text:{get:function(){return this.textContent}}}}),s({tag:"ul",name:"HTMLUListElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{type:String,compact:Boolean}}),s({name:"HTMLMediaElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{src:b,crossOrigin:p,preload:{type:["metadata","none","auto",{value:"",alias:"auto"}],missing:"auto"},loop:Boolean,autoplay:Boolean,mediaGroup:String,controls:Boolean,defaultMuted:{name:"muted",type:Boolean}}}),s({name:"HTMLAudioElement",tag:"audio",superclass:o.HTMLMediaElement,ctor:function(d,w,m){o.HTMLMediaElement.call(this,d,w,m)}}),s({name:"HTMLVideoElement",tag:"video",superclass:o.HTMLMediaElement,ctor:function(d,w,m){o.HTMLMediaElement.call(this,d,w,m)},attributes:{poster:b,width:{type:"unsigned long",min:0,default:0},height:{type:"unsigned long",min:0,default:0}}}),s({tag:"td",name:"HTMLTableDataCellElement",superclass:o.HTMLTableCellElement,ctor:function(d,w,m){o.HTMLTableCellElement.call(this,d,w,m)}}),s({tag:"th",name:"HTMLTableHeaderCellElement",superclass:o.HTMLTableCellElement,ctor:function(d,w,m){o.HTMLTableCellElement.call(this,d,w,m)}}),s({tag:"frameset",name:"HTMLFrameSetElement",ctor:function(d,w,m){I.call(this,d,w,m)}}),s({tag:"frame",name:"HTMLFrameElement",ctor:function(d,w,m){I.call(this,d,w,m)}}),s({tag:"canvas",name:"HTMLCanvasElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{getContext:{value:n.nyi},probablySupportsContext:{value:n.nyi},setContext:{value:n.nyi},transferControlToProxy:{value:n.nyi},toDataURL:{value:n.nyi},toBlob:{value:n.nyi}},attributes:{width:{type:"unsigned long",default:300},height:{type:"unsigned long",default:150}}}),s({tag:"dialog",name:"HTMLDialogElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{show:{value:n.nyi},showModal:{value:n.nyi},close:{value:n.nyi}},attributes:{open:Boolean,returnValue:String}}),s({tag:"menuitem",name:"HTMLMenuItemElement",ctor:function(d,w,m){I.call(this,d,w,m)},props:{_label:{get:function(){var E=this._getattr("label");return E!==null&&E!==""?E:(E=this.textContent,E.replace(/[ \t\n\f\r]+/g," ").trim())}},label:{get:function(){var E=this._getattr("label");return E!==null?E:this._label},set:function(E){this._setattr("label",E)}}},attributes:{type:{type:["command","checkbox","radio"],missing:"command"},icon:b,disabled:Boolean,checked:Boolean,radiogroup:String,default:Boolean}}),s({tag:"source",name:"HTMLSourceElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{srcset:String,sizes:String,media:String,src:b,type:String,width:String,height:String}}),s({tag:"track",name:"HTMLTrackElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{src:b,srclang:String,label:String,default:Boolean,kind:{type:["subtitles","captions","descriptions","chapters","metadata"],missing:"subtitles",invalid:"metadata"}},props:{NONE:{get:function(){return 0}},LOADING:{get:function(){return 1}},LOADED:{get:function(){return 2}},ERROR:{get:function(){return 3}},readyState:{get:n.nyi},track:{get:n.nyi}}}),s({tag:"font",name:"HTMLFontElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{color:{type:String,treatNullAsEmptyString:!0},face:{type:String},size:{type:String}}}),s({tag:"dir",name:"HTMLDirectoryElement",ctor:function(d,w,m){I.call(this,d,w,m)},attributes:{compact:Boolean}}),s({tags:["abbr","address","article","aside","b","bdi","bdo","cite","content","code","dd","dfn","dt","em","figcaption","figure","footer","header","hgroup","i","kbd","main","mark","nav","noscript","rb","rp","rt","rtc","ruby","s","samp","section","small","strong","sub","summary","sup","u","var","wbr","acronym","basefont","big","center","nobr","noembed","noframes","plaintext","strike","tt"]})}}),Ca=le({"external/npm/node_modules/domino/lib/svg.js"(_){var k=_r(),h=La(),c=Be(),n=kn(),i=_.elements={},u=Object.create(null);_.createElement=function(s,b,p){var T=u[b]||f;return new T(s,b,p)};function o(s){return h(s,f,i,u)}var f=o({superclass:k,name:"SVGElement",ctor:function(b,p,T){k.call(this,b,p,c.NAMESPACE.SVG,T)},props:{style:{get:function(){return this._style||(this._style=new n(this)),this._style}}}});o({name:"SVGSVGElement",ctor:function(b,p,T){f.call(this,b,p,T)},tag:"svg",props:{createSVGRect:{value:function(){return _.createElement(this.ownerDocument,"rect",null)}}}}),o({tags:["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","hkern","image","line","linearGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"]})}}),ms=le({"external/npm/node_modules/domino/lib/MutationConstants.js"(_,k){k.exports={VALUE:1,ATTR:2,REMOVE_ATTR:3,REMOVE:4,MOVE:5,INSERT:6}}}),Cn=le({"external/npm/node_modules/domino/lib/Document.js"(_,k){k.exports=V;var h=Ge(),c=rr(),n=Tn(),i=_r(),u=Ea(),o=Ta(),f=br(),s=ya(),b=wa(),p=Jr(),T=fs(),O=hs(),R=$r(),I=Sn(),J=wn(),P=Sa(),E=yn(),d=Ln(),w=Ca(),m=Be(),se=ms(),ne=m.NAMESPACE,Y=En().isApiWritable;function V(y,M){n.call(this),this.nodeType=h.DOCUMENT_NODE,this.isHTML=y,this._address=M||"about:blank",this.readyState="loading",this.implementation=new p(this),this.ownerDocument=null,this._contentType=y?"text/html":"application/xml",this.doctype=null,this.documentElement=null,this._templateDocCache=null,this._nodeIterators=null,this._nid=1,this._nextnid=2,this._nodes=[null,this],this.byId=Object.create(null),this.modclock=0}var D={event:"Event",customevent:"CustomEvent",uievent:"UIEvent",mouseevent:"MouseEvent"},B={events:"event",htmlevents:"event",mouseevents:"mouseevent",mutationevents:"mutationevent",uievents:"uievent"},K=function(y,M,X){return{get:function(){var de=y.call(this);return de?de[M]:X},set:function(de){var xe=y.call(this);xe&&(xe[M]=de)}}};function e(y,M){var X,de,xe;return y===""&&(y=null),E.isValidQName(M)||m.InvalidCharacterError(),X=null,de=M,xe=M.indexOf(":"),xe>=0&&(X=M.substring(0,xe),de=M.substring(xe+1)),X!==null&&y===null&&m.NamespaceError(),X==="xml"&&y!==ne.XML&&m.NamespaceError(),(X==="xmlns"||M==="xmlns")&&y!==ne.XMLNS&&m.NamespaceError(),y===ne.XMLNS&&!(X==="xmlns"||M==="xmlns")&&m.NamespaceError(),{namespace:y,prefix:X,localName:de}}V.prototype=Object.create(n.prototype,{_setMutationHandler:{value:function(y){this.mutationHandler=y}},_dispatchRendererEvent:{value:function(y,M,X){var de=this._nodes[y];de&&de._dispatchEvent(new f(M,X),!0)}},nodeName:{value:"#document"},nodeValue:{get:function(){return null},set:function(){}},documentURI:{get:function(){return this._address},set:m.nyi},compatMode:{get:function(){return this._quirks?"BackCompat":"CSS1Compat"}},createTextNode:{value:function(y){return new u(this,String(y))}},createComment:{value:function(y){return new o(this,y)}},createDocumentFragment:{value:function(){return new s(this)}},createProcessingInstruction:{value:function(y,M){return(!E.isValidName(y)||M.indexOf("?>")!==-1)&&m.InvalidCharacterError(),new b(this,y,M)}},createAttribute:{value:function(y){return y=String(y),E.isValidName(y)||m.InvalidCharacterError(),this.isHTML&&(y=m.toASCIILowerCase(y)),new i._Attr(null,y,null,null,"")}},createAttributeNS:{value:function(y,M){y=y==null||y===""?null:String(y),M=String(M);var X=e(y,M);return new i._Attr(null,X.localName,X.prefix,X.namespace,"")}},createElement:{value:function(y){return y=String(y),E.isValidName(y)||m.InvalidCharacterError(),this.isHTML?(/[A-Z]/.test(y)&&(y=m.toASCIILowerCase(y)),d.createElement(this,y,null)):this.contentType==="application/xhtml+xml"?d.createElement(this,y,null):new i(this,y,null,null)},writable:Y},createElementNS:{value:function(y,M){y=y==null||y===""?null:String(y),M=String(M);var X=e(y,M);return this._createElementNS(X.localName,X.namespace,X.prefix)},writable:Y},_createElementNS:{value:function(y,M,X){return M===ne.HTML?d.createElement(this,y,X):M===ne.SVG?w.createElement(this,y,X):new i(this,y,M,X)}},createEvent:{value:function(M){M=M.toLowerCase();var X=B[M]||M,de=P[D[X]];if(de){var xe=new de;return xe._initialized=!1,xe}else m.NotSupportedError()}},createTreeWalker:{value:function(y,M,X){if(!y)throw new TypeError("root argument is required");if(!(y instanceof h))throw new TypeError("root not a node");return M=M===void 0?R.SHOW_ALL:+M,X=X===void 0?null:X,new T(y,M,X)}},createNodeIterator:{value:function(y,M,X){if(!y)throw new TypeError("root argument is required");if(!(y instanceof h))throw new TypeError("root not a node");return M=M===void 0?R.SHOW_ALL:+M,X=X===void 0?null:X,new O(y,M,X)}},_attachNodeIterator:{value:function(y){this._nodeIterators||(this._nodeIterators=[]),this._nodeIterators.push(y)}},_detachNodeIterator:{value:function(y){var M=this._nodeIterators.indexOf(y);this._nodeIterators.splice(M,1)}},_preremoveNodeIterators:{value:function(y){this._nodeIterators&&this._nodeIterators.forEach(function(M){M._preremove(y)})}},_updateDocTypeElement:{value:function(){this.doctype=this.documentElement=null;for(var M=this.firstChild;M!==null;M=M.nextSibling)M.nodeType===h.DOCUMENT_TYPE_NODE?this.doctype=M:M.nodeType===h.ELEMENT_NODE&&(this.documentElement=M)}},insertBefore:{value:function(M,X){return h.prototype.insertBefore.call(this,M,X),this._updateDocTypeElement(),M}},replaceChild:{value:function(M,X){return h.prototype.replaceChild.call(this,M,X),this._updateDocTypeElement(),X}},removeChild:{value:function(M){return h.prototype.removeChild.call(this,M),this._updateDocTypeElement(),M}},getElementById:{value:function(y){var M=this.byId[y];return M?M instanceof ee?M.getFirst():M:null}},_hasMultipleElementsWithId:{value:function(y){return this.byId[y]instanceof ee}},getElementsByName:{value:i.prototype.getElementsByName},getElementsByTagName:{value:i.prototype.getElementsByTagName},getElementsByTagNameNS:{value:i.prototype.getElementsByTagNameNS},getElementsByClassName:{value:i.prototype.getElementsByClassName},adoptNode:{value:function(M){return M.nodeType===h.DOCUMENT_NODE&&m.NotSupportedError(),M.nodeType===h.ATTRIBUTE_NODE||(M.parentNode&&M.parentNode.removeChild(M),M.ownerDocument!==this&&G(M,this)),M}},importNode:{value:function(M,X){return this.adoptNode(M.cloneNode(X))},writable:Y},origin:{get:function(){return null}},characterSet:{get:function(){return"UTF-8"}},contentType:{get:function(){return this._contentType}},URL:{get:function(){return this._address}},domain:{get:m.nyi,set:m.nyi},referrer:{get:m.nyi},cookie:{get:m.nyi,set:m.nyi},lastModified:{get:m.nyi},location:{get:function(){return this.defaultView?this.defaultView.location:null},set:m.nyi},_titleElement:{get:function(){return this.getElementsByTagName("title").item(0)||null}},title:{get:function(){var y=this._titleElement,M=y?y.textContent:"";return M.replace(/[ \t\n\r\f]+/g," ").replace(/(^ )|( $)/g,"")},set:function(y){var M=this._titleElement,X=this.head;!M&&!X||(M||(M=this.createElement("title"),X.appendChild(M)),M.textContent=y)}},dir:K(function(){var y=this.documentElement;if(y&&y.tagName==="HTML")return y},"dir",""),fgColor:K(function(){return this.body},"text",""),linkColor:K(function(){return this.body},"link",""),vlinkColor:K(function(){return this.body},"vLink",""),alinkColor:K(function(){return this.body},"aLink",""),bgColor:K(function(){return this.body},"bgColor",""),charset:{get:function(){return this.characterSet}},inputEncoding:{get:function(){return this.characterSet}},scrollingElement:{get:function(){return this._quirks?this.body:this.documentElement}},body:{get:function(){return l(this.documentElement,"body")},set:m.nyi},head:{get:function(){return l(this.documentElement,"head")}},images:{get:m.nyi},embeds:{get:m.nyi},plugins:{get:m.nyi},links:{get:m.nyi},forms:{get:m.nyi},scripts:{get:m.nyi},applets:{get:function(){return[]}},activeElement:{get:function(){return null}},innerHTML:{get:function(){return this.serialize()},set:m.nyi},outerHTML:{get:function(){return this.serialize()},set:m.nyi},write:{value:function(y){if(this.isHTML||m.InvalidStateError(),!!this._parser){var M=arguments.join("");this._parser.parse(M)}}},writeln:{value:function(M){this.write(Array.prototype.join.call(arguments,"")+`
`)}},open:{value:function(){this.documentElement=null}},close:{value:function(){this.readyState="interactive",this._dispatchEvent(new f("readystatechange"),!0),this._dispatchEvent(new f("DOMContentLoaded"),!0),this.readyState="complete",this._dispatchEvent(new f("readystatechange"),!0),this.defaultView&&this.defaultView._dispatchEvent(new f("load"),!0)}},clone:{value:function(){var M=new V(this.isHTML,this._address);return M._quirks=this._quirks,M._contentType=this._contentType,M}},cloneNode:{value:function(M){var X=h.prototype.cloneNode.call(this,!1);if(M)for(var de=this.firstChild;de!==null;de=de.nextSibling)X._appendChild(X.importNode(de,!0));return X._updateDocTypeElement(),X}},isEqual:{value:function(M){return!0}},mutateValue:{value:function(y){this.mutationHandler&&this.mutationHandler({type:se.VALUE,target:y,data:y.data})}},mutateAttr:{value:function(y,M){this.mutationHandler&&this.mutationHandler({type:se.ATTR,target:y.ownerElement,attr:y})}},mutateRemoveAttr:{value:function(y){this.mutationHandler&&this.mutationHandler({type:se.REMOVE_ATTR,target:y.ownerElement,attr:y})}},mutateRemove:{value:function(y){this.mutationHandler&&this.mutationHandler({type:se.REMOVE,target:y.parentNode,node:y}),C(y)}},mutateInsert:{value:function(y){S(y),this.mutationHandler&&this.mutationHandler({type:se.INSERT,target:y.parentNode,node:y})}},mutateMove:{value:function(y){this.mutationHandler&&this.mutationHandler({type:se.MOVE,target:y})}},addId:{value:function(M,X){var de=this.byId[M];de?(de instanceof ee||(de=new ee(de),this.byId[M]=de),de.add(X)):this.byId[M]=X}},delId:{value:function(M,X){var de=this.byId[M];m.assert(de),de instanceof ee?(de.del(X),de.length===1&&(this.byId[M]=de.downgrade())):this.byId[M]=void 0}},_resolve:{value:function(y){return new I(this._documentBaseURL).resolve(y)}},_documentBaseURL:{get:function(){var y=this._address;y==="about:blank"&&(y="/");var M=this.querySelector("base[href]");return M?new I(y).resolve(M.getAttribute("href")):y}},_templateDoc:{get:function(){if(!this._templateDocCache){var y=new V(this.isHTML,this._address);this._templateDocCache=y._templateDocCache=y}return this._templateDocCache}},querySelector:{value:function(y){return J(y,this)[0]}},querySelectorAll:{value:function(y){var M=J(y,this);return M.item?M:new c(M)}}});var r=["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"];r.forEach(function(y){Object.defineProperty(V.prototype,"on"+y,{get:function(){return this._getEventHandler(y)},set:function(M){this._setEventHandler(y,M)}})});function l(y,M){if(y&&y.isHTML){for(var X=y.firstChild;X!==null;X=X.nextSibling)if(X.nodeType===h.ELEMENT_NODE&&X.localName===M&&X.namespaceURI===ne.HTML)return X}return null}function N(y){if(y._nid=y.ownerDocument._nextnid++,y.ownerDocument._nodes[y._nid]=y,y.nodeType===h.ELEMENT_NODE){var M=y.getAttribute("id");M&&y.ownerDocument.addId(M,y),y._roothook&&y._roothook()}}function g(y){if(y.nodeType===h.ELEMENT_NODE){var M=y.getAttribute("id");M&&y.ownerDocument.delId(M,y)}y.ownerDocument._nodes[y._nid]=void 0,y._nid=void 0}function S(y){if(N(y),y.nodeType===h.ELEMENT_NODE)for(var M=y.firstChild;M!==null;M=M.nextSibling)S(M)}function C(y){g(y);for(var M=y.firstChild;M!==null;M=M.nextSibling)C(M)}function G(y,M){y.ownerDocument=M,y._lastModTime=void 0,Object.prototype.hasOwnProperty.call(y,"_tagName")&&(y._tagName=void 0);for(var X=y.firstChild;X!==null;X=X.nextSibling)G(X,M)}function ee(y){this.nodes=Object.create(null),this.nodes[y._nid]=y,this.length=1,this.firstNode=void 0}ee.prototype.add=function(y){this.nodes[y._nid]||(this.nodes[y._nid]=y,this.length++,this.firstNode=void 0)},ee.prototype.del=function(y){this.nodes[y._nid]&&(delete this.nodes[y._nid],this.length--,this.firstNode=void 0)},ee.prototype.getFirst=function(){if(!this.firstNode){var y;for(y in this.nodes)(this.firstNode===void 0||this.firstNode.compareDocumentPosition(this.nodes[y])&h.DOCUMENT_POSITION_PRECEDING)&&(this.firstNode=this.nodes[y])}return this.firstNode},ee.prototype.downgrade=function(){if(this.length===1){var y;for(y in this.nodes)return this.nodes[y]}return this}}}),Dn=le({"external/npm/node_modules/domino/lib/DocumentType.js"(_,k){k.exports=i;var h=Ge(),c=va(),n=Nn();function i(u,o,f,s){c.call(this),this.nodeType=h.DOCUMENT_TYPE_NODE,this.ownerDocument=u||null,this.name=o,this.publicId=f||"",this.systemId=s||""}i.prototype=Object.create(c.prototype,{nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return null},set:function(){}},clone:{value:function(){return new i(this.ownerDocument,this.name,this.publicId,this.systemId)}},isEqual:{value:function(o){return this.name===o.name&&this.publicId===o.publicId&&this.systemId===o.systemId}}}),Object.defineProperties(i.prototype,n)}}),Mn=le({"external/npm/node_modules/domino/lib/HTMLParser.js"(_,k){k.exports=ve;var h=Cn(),c=Dn(),n=Ge(),i=Be().NAMESPACE,u=Ln(),o=u.elements,f=Function.prototype.apply.bind(Array.prototype.push),s=-1,b=1,p=2,T=3,O=4,R=5,I=[],J=/^HTML$|^-\/\/W3O\/\/DTD W3 HTML Strict 3\.0\/\/EN\/\/$|^-\/W3C\/DTD HTML 4\.0 Transitional\/EN$|^\+\/\/Silmaril\/\/dtd html Pro v0r11 19970101\/\/|^-\/\/AdvaSoft Ltd\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/AS\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict\/\/|^-\/\/IETF\/\/DTD HTML 2\.0\/\/|^-\/\/IETF\/\/DTD HTML 2\.1E\/\/|^-\/\/IETF\/\/DTD HTML 3\.0\/\/|^-\/\/IETF\/\/DTD HTML 3\.2 Final\/\/|^-\/\/IETF\/\/DTD HTML 3\.2\/\/|^-\/\/IETF\/\/DTD HTML 3\/\/|^-\/\/IETF\/\/DTD HTML Level 0\/\/|^-\/\/IETF\/\/DTD HTML Level 1\/\/|^-\/\/IETF\/\/DTD HTML Level 2\/\/|^-\/\/IETF\/\/DTD HTML Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 0\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict\/\/|^-\/\/IETF\/\/DTD HTML\/\/|^-\/\/Metrius\/\/DTD Metrius Presentational\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 Tables\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 Tables\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD HTML\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD Strict HTML\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML 2\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended 1\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended Relaxed 1\.0\/\/|^-\/\/SoftQuad Software\/\/DTD HoTMetaL PRO 6\.0::19990601::extensions to HTML 4\.0\/\/|^-\/\/SoftQuad\/\/DTD HoTMetaL PRO 4\.0::19971010::extensions to HTML 4\.0\/\/|^-\/\/Spyglass\/\/DTD HTML 2\.0 Extended\/\/|^-\/\/SQ\/\/DTD HTML 2\.0 HoTMetaL \+ extensions\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava HTML\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava Strict HTML\/\/|^-\/\/W3C\/\/DTD HTML 3 1995-03-24\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Draft\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Final\/\/|^-\/\/W3C\/\/DTD HTML 3\.2\/\/|^-\/\/W3C\/\/DTD HTML 3\.2S Draft\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Transitional\/\/|^-\/\/W3C\/\/DTD HTML Experimental 19960712\/\/|^-\/\/W3C\/\/DTD HTML Experimental 970421\/\/|^-\/\/W3C\/\/DTD W3 HTML\/\/|^-\/\/W3O\/\/DTD W3 HTML 3\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML 2\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML\/\//i,P="http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd",E=/^-\/\/W3C\/\/DTD HTML 4\.01 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.01 Transitional\/\//i,d=/^-\/\/W3C\/\/DTD XHTML 1\.0 Frameset\/\/|^-\/\/W3C\/\/DTD XHTML 1\.0 Transitional\/\//i,w=Object.create(null);w[i.HTML]={__proto__:null,address:!0,applet:!0,area:!0,article:!0,aside:!0,base:!0,basefont:!0,bgsound:!0,blockquote:!0,body:!0,br:!0,button:!0,caption:!0,center:!0,col:!0,colgroup:!0,dd:!0,details:!0,dir:!0,div:!0,dl:!0,dt:!0,embed:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,frame:!0,frameset:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,head:!0,header:!0,hgroup:!0,hr:!0,html:!0,iframe:!0,img:!0,input:!0,li:!0,link:!0,listing:!0,main:!0,marquee:!0,menu:!0,meta:!0,nav:!0,noembed:!0,noframes:!0,noscript:!0,object:!0,ol:!0,p:!0,param:!0,plaintext:!0,pre:!0,script:!0,section:!0,select:!0,source:!0,style:!0,summary:!0,table:!0,tbody:!0,td:!0,template:!0,textarea:!0,tfoot:!0,th:!0,thead:!0,title:!0,tr:!0,track:!0,ul:!0,wbr:!0,xmp:!0},w[i.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0},w[i.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0};var m=Object.create(null);m[i.HTML]={__proto__:null,address:!0,div:!0,p:!0};var se=Object.create(null);se[i.HTML]={__proto__:null,dd:!0,dt:!0};var ne=Object.create(null);ne[i.HTML]={__proto__:null,table:!0,thead:!0,tbody:!0,tfoot:!0,tr:!0};var Y=Object.create(null);Y[i.HTML]={__proto__:null,dd:!0,dt:!0,li:!0,menuitem:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0};var V=Object.create(null);V[i.HTML]={__proto__:null,caption:!0,colgroup:!0,dd:!0,dt:!0,li:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0,tbody:!0,td:!0,tfoot:!0,th:!0,thead:!0,tr:!0};var D=Object.create(null);D[i.HTML]={__proto__:null,table:!0,template:!0,html:!0};var B=Object.create(null);B[i.HTML]={__proto__:null,tbody:!0,tfoot:!0,thead:!0,template:!0,html:!0};var K=Object.create(null);K[i.HTML]={__proto__:null,tr:!0,template:!0,html:!0};var e=Object.create(null);e[i.HTML]={__proto__:null,button:!0,fieldset:!0,input:!0,keygen:!0,object:!0,output:!0,select:!0,textarea:!0,img:!0};var r=Object.create(null);r[i.HTML]={__proto__:null,applet:!0,caption:!0,html:!0,table:!0,td:!0,th:!0,marquee:!0,object:!0,template:!0},r[i.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0},r[i.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var l=Object.create(r);l[i.HTML]=Object.create(r[i.HTML]),l[i.HTML].ol=!0,l[i.HTML].ul=!0;var N=Object.create(r);N[i.HTML]=Object.create(r[i.HTML]),N[i.HTML].button=!0;var g=Object.create(null);g[i.HTML]={__proto__:null,html:!0,table:!0,template:!0};var S=Object.create(null);S[i.HTML]={__proto__:null,optgroup:!0,option:!0};var C=Object.create(null);C[i.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0};var G=Object.create(null);G[i.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var ee={__proto__:null,"xlink:actuate":i.XLINK,"xlink:arcrole":i.XLINK,"xlink:href":i.XLINK,"xlink:role":i.XLINK,"xlink:show":i.XLINK,"xlink:title":i.XLINK,"xlink:type":i.XLINK,"xml:base":i.XML,"xml:lang":i.XML,"xml:space":i.XML,xmlns:i.XMLNS,"xmlns:xlink":i.XMLNS},y={__proto__:null,attributename:"attributeName",attributetype:"attributeType",basefrequency:"baseFrequency",baseprofile:"baseProfile",calcmode:"calcMode",clippathunits:"clipPathUnits",diffuseconstant:"diffuseConstant",edgemode:"edgeMode",filterunits:"filterUnits",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",limitingconeangle:"limitingConeAngle",markerheight:"markerHeight",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textlength:"textLength",viewbox:"viewBox",viewtarget:"viewTarget",xchannelselector:"xChannelSelector",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"},M={__proto__:null,altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient",textpath:"textPath"},X={__proto__:null,0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376},de={__proto__:null,AElig:198,"AElig;":198,AMP:38,"AMP;":38,Aacute:193,"Aacute;":193,"Abreve;":258,Acirc:194,"Acirc;":194,"Acy;":1040,"Afr;":[55349,56580],Agrave:192,"Agrave;":192,"Alpha;":913,"Amacr;":256,"And;":10835,"Aogon;":260,"Aopf;":[55349,56632],"ApplyFunction;":8289,Aring:197,"Aring;":197,"Ascr;":[55349,56476],"Assign;":8788,Atilde:195,"Atilde;":195,Auml:196,"Auml;":196,"Backslash;":8726,"Barv;":10983,"Barwed;":8966,"Bcy;":1041,"Because;":8757,"Bernoullis;":8492,"Beta;":914,"Bfr;":[55349,56581],"Bopf;":[55349,56633],"Breve;":728,"Bscr;":8492,"Bumpeq;":8782,"CHcy;":1063,COPY:169,"COPY;":169,"Cacute;":262,"Cap;":8914,"CapitalDifferentialD;":8517,"Cayleys;":8493,"Ccaron;":268,Ccedil:199,"Ccedil;":199,"Ccirc;":264,"Cconint;":8752,"Cdot;":266,"Cedilla;":184,"CenterDot;":183,"Cfr;":8493,"Chi;":935,"CircleDot;":8857,"CircleMinus;":8854,"CirclePlus;":8853,"CircleTimes;":8855,"ClockwiseContourIntegral;":8754,"CloseCurlyDoubleQuote;":8221,"CloseCurlyQuote;":8217,"Colon;":8759,"Colone;":10868,"Congruent;":8801,"Conint;":8751,"ContourIntegral;":8750,"Copf;":8450,"Coproduct;":8720,"CounterClockwiseContourIntegral;":8755,"Cross;":10799,"Cscr;":[55349,56478],"Cup;":8915,"CupCap;":8781,"DD;":8517,"DDotrahd;":10513,"DJcy;":1026,"DScy;":1029,"DZcy;":1039,"Dagger;":8225,"Darr;":8609,"Dashv;":10980,"Dcaron;":270,"Dcy;":1044,"Del;":8711,"Delta;":916,"Dfr;":[55349,56583],"DiacriticalAcute;":180,"DiacriticalDot;":729,"DiacriticalDoubleAcute;":733,"DiacriticalGrave;":96,"DiacriticalTilde;":732,"Diamond;":8900,"DifferentialD;":8518,"Dopf;":[55349,56635],"Dot;":168,"DotDot;":8412,"DotEqual;":8784,"DoubleContourIntegral;":8751,"DoubleDot;":168,"DoubleDownArrow;":8659,"DoubleLeftArrow;":8656,"DoubleLeftRightArrow;":8660,"DoubleLeftTee;":10980,"DoubleLongLeftArrow;":10232,"DoubleLongLeftRightArrow;":10234,"DoubleLongRightArrow;":10233,"DoubleRightArrow;":8658,"DoubleRightTee;":8872,"DoubleUpArrow;":8657,"DoubleUpDownArrow;":8661,"DoubleVerticalBar;":8741,"DownArrow;":8595,"DownArrowBar;":10515,"DownArrowUpArrow;":8693,"DownBreve;":785,"DownLeftRightVector;":10576,"DownLeftTeeVector;":10590,"DownLeftVector;":8637,"DownLeftVectorBar;":10582,"DownRightTeeVector;":10591,"DownRightVector;":8641,"DownRightVectorBar;":10583,"DownTee;":8868,"DownTeeArrow;":8615,"Downarrow;":8659,"Dscr;":[55349,56479],"Dstrok;":272,"ENG;":330,ETH:208,"ETH;":208,Eacute:201,"Eacute;":201,"Ecaron;":282,Ecirc:202,"Ecirc;":202,"Ecy;":1069,"Edot;":278,"Efr;":[55349,56584],Egrave:200,"Egrave;":200,"Element;":8712,"Emacr;":274,"EmptySmallSquare;":9723,"EmptyVerySmallSquare;":9643,"Eogon;":280,"Eopf;":[55349,56636],"Epsilon;":917,"Equal;":10869,"EqualTilde;":8770,"Equilibrium;":8652,"Escr;":8496,"Esim;":10867,"Eta;":919,Euml:203,"Euml;":203,"Exists;":8707,"ExponentialE;":8519,"Fcy;":1060,"Ffr;":[55349,56585],"FilledSmallSquare;":9724,"FilledVerySmallSquare;":9642,"Fopf;":[55349,56637],"ForAll;":8704,"Fouriertrf;":8497,"Fscr;":8497,"GJcy;":1027,GT:62,"GT;":62,"Gamma;":915,"Gammad;":988,"Gbreve;":286,"Gcedil;":290,"Gcirc;":284,"Gcy;":1043,"Gdot;":288,"Gfr;":[55349,56586],"Gg;":8921,"Gopf;":[55349,56638],"GreaterEqual;":8805,"GreaterEqualLess;":8923,"GreaterFullEqual;":8807,"GreaterGreater;":10914,"GreaterLess;":8823,"GreaterSlantEqual;":10878,"GreaterTilde;":8819,"Gscr;":[55349,56482],"Gt;":8811,"HARDcy;":1066,"Hacek;":711,"Hat;":94,"Hcirc;":292,"Hfr;":8460,"HilbertSpace;":8459,"Hopf;":8461,"HorizontalLine;":9472,"Hscr;":8459,"Hstrok;":294,"HumpDownHump;":8782,"HumpEqual;":8783,"IEcy;":1045,"IJlig;":306,"IOcy;":1025,Iacute:205,"Iacute;":205,Icirc:206,"Icirc;":206,"Icy;":1048,"Idot;":304,"Ifr;":8465,Igrave:204,"Igrave;":204,"Im;":8465,"Imacr;":298,"ImaginaryI;":8520,"Implies;":8658,"Int;":8748,"Integral;":8747,"Intersection;":8898,"InvisibleComma;":8291,"InvisibleTimes;":8290,"Iogon;":302,"Iopf;":[55349,56640],"Iota;":921,"Iscr;":8464,"Itilde;":296,"Iukcy;":1030,Iuml:207,"Iuml;":207,"Jcirc;":308,"Jcy;":1049,"Jfr;":[55349,56589],"Jopf;":[55349,56641],"Jscr;":[55349,56485],"Jsercy;":1032,"Jukcy;":1028,"KHcy;":1061,"KJcy;":1036,"Kappa;":922,"Kcedil;":310,"Kcy;":1050,"Kfr;":[55349,56590],"Kopf;":[55349,56642],"Kscr;":[55349,56486],"LJcy;":1033,LT:60,"LT;":60,"Lacute;":313,"Lambda;":923,"Lang;":10218,"Laplacetrf;":8466,"Larr;":8606,"Lcaron;":317,"Lcedil;":315,"Lcy;":1051,"LeftAngleBracket;":10216,"LeftArrow;":8592,"LeftArrowBar;":8676,"LeftArrowRightArrow;":8646,"LeftCeiling;":8968,"LeftDoubleBracket;":10214,"LeftDownTeeVector;":10593,"LeftDownVector;":8643,"LeftDownVectorBar;":10585,"LeftFloor;":8970,"LeftRightArrow;":8596,"LeftRightVector;":10574,"LeftTee;":8867,"LeftTeeArrow;":8612,"LeftTeeVector;":10586,"LeftTriangle;":8882,"LeftTriangleBar;":10703,"LeftTriangleEqual;":8884,"LeftUpDownVector;":10577,"LeftUpTeeVector;":10592,"LeftUpVector;":8639,"LeftUpVectorBar;":10584,"LeftVector;":8636,"LeftVectorBar;":10578,"Leftarrow;":8656,"Leftrightarrow;":8660,"LessEqualGreater;":8922,"LessFullEqual;":8806,"LessGreater;":8822,"LessLess;":10913,"LessSlantEqual;":10877,"LessTilde;":8818,"Lfr;":[55349,56591],"Ll;":8920,"Lleftarrow;":8666,"Lmidot;":319,"LongLeftArrow;":10229,"LongLeftRightArrow;":10231,"LongRightArrow;":10230,"Longleftarrow;":10232,"Longleftrightarrow;":10234,"Longrightarrow;":10233,"Lopf;":[55349,56643],"LowerLeftArrow;":8601,"LowerRightArrow;":8600,"Lscr;":8466,"Lsh;":8624,"Lstrok;":321,"Lt;":8810,"Map;":10501,"Mcy;":1052,"MediumSpace;":8287,"Mellintrf;":8499,"Mfr;":[55349,56592],"MinusPlus;":8723,"Mopf;":[55349,56644],"Mscr;":8499,"Mu;":924,"NJcy;":1034,"Nacute;":323,"Ncaron;":327,"Ncedil;":325,"Ncy;":1053,"NegativeMediumSpace;":8203,"NegativeThickSpace;":8203,"NegativeThinSpace;":8203,"NegativeVeryThinSpace;":8203,"NestedGreaterGreater;":8811,"NestedLessLess;":8810,"NewLine;":10,"Nfr;":[55349,56593],"NoBreak;":8288,"NonBreakingSpace;":160,"Nopf;":8469,"Not;":10988,"NotCongruent;":8802,"NotCupCap;":8813,"NotDoubleVerticalBar;":8742,"NotElement;":8713,"NotEqual;":8800,"NotEqualTilde;":[8770,824],"NotExists;":8708,"NotGreater;":8815,"NotGreaterEqual;":8817,"NotGreaterFullEqual;":[8807,824],"NotGreaterGreater;":[8811,824],"NotGreaterLess;":8825,"NotGreaterSlantEqual;":[10878,824],"NotGreaterTilde;":8821,"NotHumpDownHump;":[8782,824],"NotHumpEqual;":[8783,824],"NotLeftTriangle;":8938,"NotLeftTriangleBar;":[10703,824],"NotLeftTriangleEqual;":8940,"NotLess;":8814,"NotLessEqual;":8816,"NotLessGreater;":8824,"NotLessLess;":[8810,824],"NotLessSlantEqual;":[10877,824],"NotLessTilde;":8820,"NotNestedGreaterGreater;":[10914,824],"NotNestedLessLess;":[10913,824],"NotPrecedes;":8832,"NotPrecedesEqual;":[10927,824],"NotPrecedesSlantEqual;":8928,"NotReverseElement;":8716,"NotRightTriangle;":8939,"NotRightTriangleBar;":[10704,824],"NotRightTriangleEqual;":8941,"NotSquareSubset;":[8847,824],"NotSquareSubsetEqual;":8930,"NotSquareSuperset;":[8848,824],"NotSquareSupersetEqual;":8931,"NotSubset;":[8834,8402],"NotSubsetEqual;":8840,"NotSucceeds;":8833,"NotSucceedsEqual;":[10928,824],"NotSucceedsSlantEqual;":8929,"NotSucceedsTilde;":[8831,824],"NotSuperset;":[8835,8402],"NotSupersetEqual;":8841,"NotTilde;":8769,"NotTildeEqual;":8772,"NotTildeFullEqual;":8775,"NotTildeTilde;":8777,"NotVerticalBar;":8740,"Nscr;":[55349,56489],Ntilde:209,"Ntilde;":209,"Nu;":925,"OElig;":338,Oacute:211,"Oacute;":211,Ocirc:212,"Ocirc;":212,"Ocy;":1054,"Odblac;":336,"Ofr;":[55349,56594],Ograve:210,"Ograve;":210,"Omacr;":332,"Omega;":937,"Omicron;":927,"Oopf;":[55349,56646],"OpenCurlyDoubleQuote;":8220,"OpenCurlyQuote;":8216,"Or;":10836,"Oscr;":[55349,56490],Oslash:216,"Oslash;":216,Otilde:213,"Otilde;":213,"Otimes;":10807,Ouml:214,"Ouml;":214,"OverBar;":8254,"OverBrace;":9182,"OverBracket;":9140,"OverParenthesis;":9180,"PartialD;":8706,"Pcy;":1055,"Pfr;":[55349,56595],"Phi;":934,"Pi;":928,"PlusMinus;":177,"Poincareplane;":8460,"Popf;":8473,"Pr;":10939,"Precedes;":8826,"PrecedesEqual;":10927,"PrecedesSlantEqual;":8828,"PrecedesTilde;":8830,"Prime;":8243,"Product;":8719,"Proportion;":8759,"Proportional;":8733,"Pscr;":[55349,56491],"Psi;":936,QUOT:34,"QUOT;":34,"Qfr;":[55349,56596],"Qopf;":8474,"Qscr;":[55349,56492],"RBarr;":10512,REG:174,"REG;":174,"Racute;":340,"Rang;":10219,"Rarr;":8608,"Rarrtl;":10518,"Rcaron;":344,"Rcedil;":342,"Rcy;":1056,"Re;":8476,"ReverseElement;":8715,"ReverseEquilibrium;":8651,"ReverseUpEquilibrium;":10607,"Rfr;":8476,"Rho;":929,"RightAngleBracket;":10217,"RightArrow;":8594,"RightArrowBar;":8677,"RightArrowLeftArrow;":8644,"RightCeiling;":8969,"RightDoubleBracket;":10215,"RightDownTeeVector;":10589,"RightDownVector;":8642,"RightDownVectorBar;":10581,"RightFloor;":8971,"RightTee;":8866,"RightTeeArrow;":8614,"RightTeeVector;":10587,"RightTriangle;":8883,"RightTriangleBar;":10704,"RightTriangleEqual;":8885,"RightUpDownVector;":10575,"RightUpTeeVector;":10588,"RightUpVector;":8638,"RightUpVectorBar;":10580,"RightVector;":8640,"RightVectorBar;":10579,"Rightarrow;":8658,"Ropf;":8477,"RoundImplies;":10608,"Rrightarrow;":8667,"Rscr;":8475,"Rsh;":8625,"RuleDelayed;":10740,"SHCHcy;":1065,"SHcy;":1064,"SOFTcy;":1068,"Sacute;":346,"Sc;":10940,"Scaron;":352,"Scedil;":350,"Scirc;":348,"Scy;":1057,"Sfr;":[55349,56598],"ShortDownArrow;":8595,"ShortLeftArrow;":8592,"ShortRightArrow;":8594,"ShortUpArrow;":8593,"Sigma;":931,"SmallCircle;":8728,"Sopf;":[55349,56650],"Sqrt;":8730,"Square;":9633,"SquareIntersection;":8851,"SquareSubset;":8847,"SquareSubsetEqual;":8849,"SquareSuperset;":8848,"SquareSupersetEqual;":8850,"SquareUnion;":8852,"Sscr;":[55349,56494],"Star;":8902,"Sub;":8912,"Subset;":8912,"SubsetEqual;":8838,"Succeeds;":8827,"SucceedsEqual;":10928,"SucceedsSlantEqual;":8829,"SucceedsTilde;":8831,"SuchThat;":8715,"Sum;":8721,"Sup;":8913,"Superset;":8835,"SupersetEqual;":8839,"Supset;":8913,THORN:222,"THORN;":222,"TRADE;":8482,"TSHcy;":1035,"TScy;":1062,"Tab;":9,"Tau;":932,"Tcaron;":356,"Tcedil;":354,"Tcy;":1058,"Tfr;":[55349,56599],"Therefore;":8756,"Theta;":920,"ThickSpace;":[8287,8202],"ThinSpace;":8201,"Tilde;":8764,"TildeEqual;":8771,"TildeFullEqual;":8773,"TildeTilde;":8776,"Topf;":[55349,56651],"TripleDot;":8411,"Tscr;":[55349,56495],"Tstrok;":358,Uacute:218,"Uacute;":218,"Uarr;":8607,"Uarrocir;":10569,"Ubrcy;":1038,"Ubreve;":364,Ucirc:219,"Ucirc;":219,"Ucy;":1059,"Udblac;":368,"Ufr;":[55349,56600],Ugrave:217,"Ugrave;":217,"Umacr;":362,"UnderBar;":95,"UnderBrace;":9183,"UnderBracket;":9141,"UnderParenthesis;":9181,"Union;":8899,"UnionPlus;":8846,"Uogon;":370,"Uopf;":[55349,56652],"UpArrow;":8593,"UpArrowBar;":10514,"UpArrowDownArrow;":8645,"UpDownArrow;":8597,"UpEquilibrium;":10606,"UpTee;":8869,"UpTeeArrow;":8613,"Uparrow;":8657,"Updownarrow;":8661,"UpperLeftArrow;":8598,"UpperRightArrow;":8599,"Upsi;":978,"Upsilon;":933,"Uring;":366,"Uscr;":[55349,56496],"Utilde;":360,Uuml:220,"Uuml;":220,"VDash;":8875,"Vbar;":10987,"Vcy;":1042,"Vdash;":8873,"Vdashl;":10982,"Vee;":8897,"Verbar;":8214,"Vert;":8214,"VerticalBar;":8739,"VerticalLine;":124,"VerticalSeparator;":10072,"VerticalTilde;":8768,"VeryThinSpace;":8202,"Vfr;":[55349,56601],"Vopf;":[55349,56653],"Vscr;":[55349,56497],"Vvdash;":8874,"Wcirc;":372,"Wedge;":8896,"Wfr;":[55349,56602],"Wopf;":[55349,56654],"Wscr;":[55349,56498],"Xfr;":[55349,56603],"Xi;":926,"Xopf;":[55349,56655],"Xscr;":[55349,56499],"YAcy;":1071,"YIcy;":1031,"YUcy;":1070,Yacute:221,"Yacute;":221,"Ycirc;":374,"Ycy;":1067,"Yfr;":[55349,56604],"Yopf;":[55349,56656],"Yscr;":[55349,56500],"Yuml;":376,"ZHcy;":1046,"Zacute;":377,"Zcaron;":381,"Zcy;":1047,"Zdot;":379,"ZeroWidthSpace;":8203,"Zeta;":918,"Zfr;":8488,"Zopf;":8484,"Zscr;":[55349,56501],aacute:225,"aacute;":225,"abreve;":259,"ac;":8766,"acE;":[8766,819],"acd;":8767,acirc:226,"acirc;":226,acute:180,"acute;":180,"acy;":1072,aelig:230,"aelig;":230,"af;":8289,"afr;":[55349,56606],agrave:224,"agrave;":224,"alefsym;":8501,"aleph;":8501,"alpha;":945,"amacr;":257,"amalg;":10815,amp:38,"amp;":38,"and;":8743,"andand;":10837,"andd;":10844,"andslope;":10840,"andv;":10842,"ang;":8736,"ange;":10660,"angle;":8736,"angmsd;":8737,"angmsdaa;":10664,"angmsdab;":10665,"angmsdac;":10666,"angmsdad;":10667,"angmsdae;":10668,"angmsdaf;":10669,"angmsdag;":10670,"angmsdah;":10671,"angrt;":8735,"angrtvb;":8894,"angrtvbd;":10653,"angsph;":8738,"angst;":197,"angzarr;":9084,"aogon;":261,"aopf;":[55349,56658],"ap;":8776,"apE;":10864,"apacir;":10863,"ape;":8778,"apid;":8779,"apos;":39,"approx;":8776,"approxeq;":8778,aring:229,"aring;":229,"ascr;":[55349,56502],"ast;":42,"asymp;":8776,"asympeq;":8781,atilde:227,"atilde;":227,auml:228,"auml;":228,"awconint;":8755,"awint;":10769,"bNot;":10989,"backcong;":8780,"backepsilon;":1014,"backprime;":8245,"backsim;":8765,"backsimeq;":8909,"barvee;":8893,"barwed;":8965,"barwedge;":8965,"bbrk;":9141,"bbrktbrk;":9142,"bcong;":8780,"bcy;":1073,"bdquo;":8222,"becaus;":8757,"because;":8757,"bemptyv;":10672,"bepsi;":1014,"bernou;":8492,"beta;":946,"beth;":8502,"between;":8812,"bfr;":[55349,56607],"bigcap;":8898,"bigcirc;":9711,"bigcup;":8899,"bigodot;":10752,"bigoplus;":10753,"bigotimes;":10754,"bigsqcup;":10758,"bigstar;":9733,"bigtriangledown;":9661,"bigtriangleup;":9651,"biguplus;":10756,"bigvee;":8897,"bigwedge;":8896,"bkarow;":10509,"blacklozenge;":10731,"blacksquare;":9642,"blacktriangle;":9652,"blacktriangledown;":9662,"blacktriangleleft;":9666,"blacktriangleright;":9656,"blank;":9251,"blk12;":9618,"blk14;":9617,"blk34;":9619,"block;":9608,"bne;":[61,8421],"bnequiv;":[8801,8421],"bnot;":8976,"bopf;":[55349,56659],"bot;":8869,"bottom;":8869,"bowtie;":8904,"boxDL;":9559,"boxDR;":9556,"boxDl;":9558,"boxDr;":9555,"boxH;":9552,"boxHD;":9574,"boxHU;":9577,"boxHd;":9572,"boxHu;":9575,"boxUL;":9565,"boxUR;":9562,"boxUl;":9564,"boxUr;":9561,"boxV;":9553,"boxVH;":9580,"boxVL;":9571,"boxVR;":9568,"boxVh;":9579,"boxVl;":9570,"boxVr;":9567,"boxbox;":10697,"boxdL;":9557,"boxdR;":9554,"boxdl;":9488,"boxdr;":9484,"boxh;":9472,"boxhD;":9573,"boxhU;":9576,"boxhd;":9516,"boxhu;":9524,"boxminus;":8863,"boxplus;":8862,"boxtimes;":8864,"boxuL;":9563,"boxuR;":9560,"boxul;":9496,"boxur;":9492,"boxv;":9474,"boxvH;":9578,"boxvL;":9569,"boxvR;":9566,"boxvh;":9532,"boxvl;":9508,"boxvr;":9500,"bprime;":8245,"breve;":728,brvbar:166,"brvbar;":166,"bscr;":[55349,56503],"bsemi;":8271,"bsim;":8765,"bsime;":8909,"bsol;":92,"bsolb;":10693,"bsolhsub;":10184,"bull;":8226,"bullet;":8226,"bump;":8782,"bumpE;":10926,"bumpe;":8783,"bumpeq;":8783,"cacute;":263,"cap;":8745,"capand;":10820,"capbrcup;":10825,"capcap;":10827,"capcup;":10823,"capdot;":10816,"caps;":[8745,65024],"caret;":8257,"caron;":711,"ccaps;":10829,"ccaron;":269,ccedil:231,"ccedil;":231,"ccirc;":265,"ccups;":10828,"ccupssm;":10832,"cdot;":267,cedil:184,"cedil;":184,"cemptyv;":10674,cent:162,"cent;":162,"centerdot;":183,"cfr;":[55349,56608],"chcy;":1095,"check;":10003,"checkmark;":10003,"chi;":967,"cir;":9675,"cirE;":10691,"circ;":710,"circeq;":8791,"circlearrowleft;":8634,"circlearrowright;":8635,"circledR;":174,"circledS;":9416,"circledast;":8859,"circledcirc;":8858,"circleddash;":8861,"cire;":8791,"cirfnint;":10768,"cirmid;":10991,"cirscir;":10690,"clubs;":9827,"clubsuit;":9827,"colon;":58,"colone;":8788,"coloneq;":8788,"comma;":44,"commat;":64,"comp;":8705,"compfn;":8728,"complement;":8705,"complexes;":8450,"cong;":8773,"congdot;":10861,"conint;":8750,"copf;":[55349,56660],"coprod;":8720,copy:169,"copy;":169,"copysr;":8471,"crarr;":8629,"cross;":10007,"cscr;":[55349,56504],"csub;":10959,"csube;":10961,"csup;":10960,"csupe;":10962,"ctdot;":8943,"cudarrl;":10552,"cudarrr;":10549,"cuepr;":8926,"cuesc;":8927,"cularr;":8630,"cularrp;":10557,"cup;":8746,"cupbrcap;":10824,"cupcap;":10822,"cupcup;":10826,"cupdot;":8845,"cupor;":10821,"cups;":[8746,65024],"curarr;":8631,"curarrm;":10556,"curlyeqprec;":8926,"curlyeqsucc;":8927,"curlyvee;":8910,"curlywedge;":8911,curren:164,"curren;":164,"curvearrowleft;":8630,"curvearrowright;":8631,"cuvee;":8910,"cuwed;":8911,"cwconint;":8754,"cwint;":8753,"cylcty;":9005,"dArr;":8659,"dHar;":10597,"dagger;":8224,"daleth;":8504,"darr;":8595,"dash;":8208,"dashv;":8867,"dbkarow;":10511,"dblac;":733,"dcaron;":271,"dcy;":1076,"dd;":8518,"ddagger;":8225,"ddarr;":8650,"ddotseq;":10871,deg:176,"deg;":176,"delta;":948,"demptyv;":10673,"dfisht;":10623,"dfr;":[55349,56609],"dharl;":8643,"dharr;":8642,"diam;":8900,"diamond;":8900,"diamondsuit;":9830,"diams;":9830,"die;":168,"digamma;":989,"disin;":8946,"div;":247,divide:247,"divide;":247,"divideontimes;":8903,"divonx;":8903,"djcy;":1106,"dlcorn;":8990,"dlcrop;":8973,"dollar;":36,"dopf;":[55349,56661],"dot;":729,"doteq;":8784,"doteqdot;":8785,"dotminus;":8760,"dotplus;":8724,"dotsquare;":8865,"doublebarwedge;":8966,"downarrow;":8595,"downdownarrows;":8650,"downharpoonleft;":8643,"downharpoonright;":8642,"drbkarow;":10512,"drcorn;":8991,"drcrop;":8972,"dscr;":[55349,56505],"dscy;":1109,"dsol;":10742,"dstrok;":273,"dtdot;":8945,"dtri;":9663,"dtrif;":9662,"duarr;":8693,"duhar;":10607,"dwangle;":10662,"dzcy;":1119,"dzigrarr;":10239,"eDDot;":10871,"eDot;":8785,eacute:233,"eacute;":233,"easter;":10862,"ecaron;":283,"ecir;":8790,ecirc:234,"ecirc;":234,"ecolon;":8789,"ecy;":1101,"edot;":279,"ee;":8519,"efDot;":8786,"efr;":[55349,56610],"eg;":10906,egrave:232,"egrave;":232,"egs;":10902,"egsdot;":10904,"el;":10905,"elinters;":9191,"ell;":8467,"els;":10901,"elsdot;":10903,"emacr;":275,"empty;":8709,"emptyset;":8709,"emptyv;":8709,"emsp13;":8196,"emsp14;":8197,"emsp;":8195,"eng;":331,"ensp;":8194,"eogon;":281,"eopf;":[55349,56662],"epar;":8917,"eparsl;":10723,"eplus;":10865,"epsi;":949,"epsilon;":949,"epsiv;":1013,"eqcirc;":8790,"eqcolon;":8789,"eqsim;":8770,"eqslantgtr;":10902,"eqslantless;":10901,"equals;":61,"equest;":8799,"equiv;":8801,"equivDD;":10872,"eqvparsl;":10725,"erDot;":8787,"erarr;":10609,"escr;":8495,"esdot;":8784,"esim;":8770,"eta;":951,eth:240,"eth;":240,euml:235,"euml;":235,"euro;":8364,"excl;":33,"exist;":8707,"expectation;":8496,"exponentiale;":8519,"fallingdotseq;":8786,"fcy;":1092,"female;":9792,"ffilig;":64259,"fflig;":64256,"ffllig;":64260,"ffr;":[55349,56611],"filig;":64257,"fjlig;":[102,106],"flat;":9837,"fllig;":64258,"fltns;":9649,"fnof;":402,"fopf;":[55349,56663],"forall;":8704,"fork;":8916,"forkv;":10969,"fpartint;":10765,frac12:189,"frac12;":189,"frac13;":8531,frac14:188,"frac14;":188,"frac15;":8533,"frac16;":8537,"frac18;":8539,"frac23;":8532,"frac25;":8534,frac34:190,"frac34;":190,"frac35;":8535,"frac38;":8540,"frac45;":8536,"frac56;":8538,"frac58;":8541,"frac78;":8542,"frasl;":8260,"frown;":8994,"fscr;":[55349,56507],"gE;":8807,"gEl;":10892,"gacute;":501,"gamma;":947,"gammad;":989,"gap;":10886,"gbreve;":287,"gcirc;":285,"gcy;":1075,"gdot;":289,"ge;":8805,"gel;":8923,"geq;":8805,"geqq;":8807,"geqslant;":10878,"ges;":10878,"gescc;":10921,"gesdot;":10880,"gesdoto;":10882,"gesdotol;":10884,"gesl;":[8923,65024],"gesles;":10900,"gfr;":[55349,56612],"gg;":8811,"ggg;":8921,"gimel;":8503,"gjcy;":1107,"gl;":8823,"glE;":10898,"gla;":10917,"glj;":10916,"gnE;":8809,"gnap;":10890,"gnapprox;":10890,"gne;":10888,"gneq;":10888,"gneqq;":8809,"gnsim;":8935,"gopf;":[55349,56664],"grave;":96,"gscr;":8458,"gsim;":8819,"gsime;":10894,"gsiml;":10896,gt:62,"gt;":62,"gtcc;":10919,"gtcir;":10874,"gtdot;":8919,"gtlPar;":10645,"gtquest;":10876,"gtrapprox;":10886,"gtrarr;":10616,"gtrdot;":8919,"gtreqless;":8923,"gtreqqless;":10892,"gtrless;":8823,"gtrsim;":8819,"gvertneqq;":[8809,65024],"gvnE;":[8809,65024],"hArr;":8660,"hairsp;":8202,"half;":189,"hamilt;":8459,"hardcy;":1098,"harr;":8596,"harrcir;":10568,"harrw;":8621,"hbar;":8463,"hcirc;":293,"hearts;":9829,"heartsuit;":9829,"hellip;":8230,"hercon;":8889,"hfr;":[55349,56613],"hksearow;":10533,"hkswarow;":10534,"hoarr;":8703,"homtht;":8763,"hookleftarrow;":8617,"hookrightarrow;":8618,"hopf;":[55349,56665],"horbar;":8213,"hscr;":[55349,56509],"hslash;":8463,"hstrok;":295,"hybull;":8259,"hyphen;":8208,iacute:237,"iacute;":237,"ic;":8291,icirc:238,"icirc;":238,"icy;":1080,"iecy;":1077,iexcl:161,"iexcl;":161,"iff;":8660,"ifr;":[55349,56614],igrave:236,"igrave;":236,"ii;":8520,"iiiint;":10764,"iiint;":8749,"iinfin;":10716,"iiota;":8489,"ijlig;":307,"imacr;":299,"image;":8465,"imagline;":8464,"imagpart;":8465,"imath;":305,"imof;":8887,"imped;":437,"in;":8712,"incare;":8453,"infin;":8734,"infintie;":10717,"inodot;":305,"int;":8747,"intcal;":8890,"integers;":8484,"intercal;":8890,"intlarhk;":10775,"intprod;":10812,"iocy;":1105,"iogon;":303,"iopf;":[55349,56666],"iota;":953,"iprod;":10812,iquest:191,"iquest;":191,"iscr;":[55349,56510],"isin;":8712,"isinE;":8953,"isindot;":8949,"isins;":8948,"isinsv;":8947,"isinv;":8712,"it;":8290,"itilde;":297,"iukcy;":1110,iuml:239,"iuml;":239,"jcirc;":309,"jcy;":1081,"jfr;":[55349,56615],"jmath;":567,"jopf;":[55349,56667],"jscr;":[55349,56511],"jsercy;":1112,"jukcy;":1108,"kappa;":954,"kappav;":1008,"kcedil;":311,"kcy;":1082,"kfr;":[55349,56616],"kgreen;":312,"khcy;":1093,"kjcy;":1116,"kopf;":[55349,56668],"kscr;":[55349,56512],"lAarr;":8666,"lArr;":8656,"lAtail;":10523,"lBarr;":10510,"lE;":8806,"lEg;":10891,"lHar;":10594,"lacute;":314,"laemptyv;":10676,"lagran;":8466,"lambda;":955,"lang;":10216,"langd;":10641,"langle;":10216,"lap;":10885,laquo:171,"laquo;":171,"larr;":8592,"larrb;":8676,"larrbfs;":10527,"larrfs;":10525,"larrhk;":8617,"larrlp;":8619,"larrpl;":10553,"larrsim;":10611,"larrtl;":8610,"lat;":10923,"latail;":10521,"late;":10925,"lates;":[10925,65024],"lbarr;":10508,"lbbrk;":10098,"lbrace;":123,"lbrack;":91,"lbrke;":10635,"lbrksld;":10639,"lbrkslu;":10637,"lcaron;":318,"lcedil;":316,"lceil;":8968,"lcub;":123,"lcy;":1083,"ldca;":10550,"ldquo;":8220,"ldquor;":8222,"ldrdhar;":10599,"ldrushar;":10571,"ldsh;":8626,"le;":8804,"leftarrow;":8592,"leftarrowtail;":8610,"leftharpoondown;":8637,"leftharpoonup;":8636,"leftleftarrows;":8647,"leftrightarrow;":8596,"leftrightarrows;":8646,"leftrightharpoons;":8651,"leftrightsquigarrow;":8621,"leftthreetimes;":8907,"leg;":8922,"leq;":8804,"leqq;":8806,"leqslant;":10877,"les;":10877,"lescc;":10920,"lesdot;":10879,"lesdoto;":10881,"lesdotor;":10883,"lesg;":[8922,65024],"lesges;":10899,"lessapprox;":10885,"lessdot;":8918,"lesseqgtr;":8922,"lesseqqgtr;":10891,"lessgtr;":8822,"lesssim;":8818,"lfisht;":10620,"lfloor;":8970,"lfr;":[55349,56617],"lg;":8822,"lgE;":10897,"lhard;":8637,"lharu;":8636,"lharul;":10602,"lhblk;":9604,"ljcy;":1113,"ll;":8810,"llarr;":8647,"llcorner;":8990,"llhard;":10603,"lltri;":9722,"lmidot;":320,"lmoust;":9136,"lmoustache;":9136,"lnE;":8808,"lnap;":10889,"lnapprox;":10889,"lne;":10887,"lneq;":10887,"lneqq;":8808,"lnsim;":8934,"loang;":10220,"loarr;":8701,"lobrk;":10214,"longleftarrow;":10229,"longleftrightarrow;":10231,"longmapsto;":10236,"longrightarrow;":10230,"looparrowleft;":8619,"looparrowright;":8620,"lopar;":10629,"lopf;":[55349,56669],"loplus;":10797,"lotimes;":10804,"lowast;":8727,"lowbar;":95,"loz;":9674,"lozenge;":9674,"lozf;":10731,"lpar;":40,"lparlt;":10643,"lrarr;":8646,"lrcorner;":8991,"lrhar;":8651,"lrhard;":10605,"lrm;":8206,"lrtri;":8895,"lsaquo;":8249,"lscr;":[55349,56513],"lsh;":8624,"lsim;":8818,"lsime;":10893,"lsimg;":10895,"lsqb;":91,"lsquo;":8216,"lsquor;":8218,"lstrok;":322,lt:60,"lt;":60,"ltcc;":10918,"ltcir;":10873,"ltdot;":8918,"lthree;":8907,"ltimes;":8905,"ltlarr;":10614,"ltquest;":10875,"ltrPar;":10646,"ltri;":9667,"ltrie;":8884,"ltrif;":9666,"lurdshar;":10570,"luruhar;":10598,"lvertneqq;":[8808,65024],"lvnE;":[8808,65024],"mDDot;":8762,macr:175,"macr;":175,"male;":9794,"malt;":10016,"maltese;":10016,"map;":8614,"mapsto;":8614,"mapstodown;":8615,"mapstoleft;":8612,"mapstoup;":8613,"marker;":9646,"mcomma;":10793,"mcy;":1084,"mdash;":8212,"measuredangle;":8737,"mfr;":[55349,56618],"mho;":8487,micro:181,"micro;":181,"mid;":8739,"midast;":42,"midcir;":10992,middot:183,"middot;":183,"minus;":8722,"minusb;":8863,"minusd;":8760,"minusdu;":10794,"mlcp;":10971,"mldr;":8230,"mnplus;":8723,"models;":8871,"mopf;":[55349,56670],"mp;":8723,"mscr;":[55349,56514],"mstpos;":8766,"mu;":956,"multimap;":8888,"mumap;":8888,"nGg;":[8921,824],"nGt;":[8811,8402],"nGtv;":[8811,824],"nLeftarrow;":8653,"nLeftrightarrow;":8654,"nLl;":[8920,824],"nLt;":[8810,8402],"nLtv;":[8810,824],"nRightarrow;":8655,"nVDash;":8879,"nVdash;":8878,"nabla;":8711,"nacute;":324,"nang;":[8736,8402],"nap;":8777,"napE;":[10864,824],"napid;":[8779,824],"napos;":329,"napprox;":8777,"natur;":9838,"natural;":9838,"naturals;":8469,nbsp:160,"nbsp;":160,"nbump;":[8782,824],"nbumpe;":[8783,824],"ncap;":10819,"ncaron;":328,"ncedil;":326,"ncong;":8775,"ncongdot;":[10861,824],"ncup;":10818,"ncy;":1085,"ndash;":8211,"ne;":8800,"neArr;":8663,"nearhk;":10532,"nearr;":8599,"nearrow;":8599,"nedot;":[8784,824],"nequiv;":8802,"nesear;":10536,"nesim;":[8770,824],"nexist;":8708,"nexists;":8708,"nfr;":[55349,56619],"ngE;":[8807,824],"nge;":8817,"ngeq;":8817,"ngeqq;":[8807,824],"ngeqslant;":[10878,824],"nges;":[10878,824],"ngsim;":8821,"ngt;":8815,"ngtr;":8815,"nhArr;":8654,"nharr;":8622,"nhpar;":10994,"ni;":8715,"nis;":8956,"nisd;":8954,"niv;":8715,"njcy;":1114,"nlArr;":8653,"nlE;":[8806,824],"nlarr;":8602,"nldr;":8229,"nle;":8816,"nleftarrow;":8602,"nleftrightarrow;":8622,"nleq;":8816,"nleqq;":[8806,824],"nleqslant;":[10877,824],"nles;":[10877,824],"nless;":8814,"nlsim;":8820,"nlt;":8814,"nltri;":8938,"nltrie;":8940,"nmid;":8740,"nopf;":[55349,56671],not:172,"not;":172,"notin;":8713,"notinE;":[8953,824],"notindot;":[8949,824],"notinva;":8713,"notinvb;":8951,"notinvc;":8950,"notni;":8716,"notniva;":8716,"notnivb;":8958,"notnivc;":8957,"npar;":8742,"nparallel;":8742,"nparsl;":[11005,8421],"npart;":[8706,824],"npolint;":10772,"npr;":8832,"nprcue;":8928,"npre;":[10927,824],"nprec;":8832,"npreceq;":[10927,824],"nrArr;":8655,"nrarr;":8603,"nrarrc;":[10547,824],"nrarrw;":[8605,824],"nrightarrow;":8603,"nrtri;":8939,"nrtrie;":8941,"nsc;":8833,"nsccue;":8929,"nsce;":[10928,824],"nscr;":[55349,56515],"nshortmid;":8740,"nshortparallel;":8742,"nsim;":8769,"nsime;":8772,"nsimeq;":8772,"nsmid;":8740,"nspar;":8742,"nsqsube;":8930,"nsqsupe;":8931,"nsub;":8836,"nsubE;":[10949,824],"nsube;":8840,"nsubset;":[8834,8402],"nsubseteq;":8840,"nsubseteqq;":[10949,824],"nsucc;":8833,"nsucceq;":[10928,824],"nsup;":8837,"nsupE;":[10950,824],"nsupe;":8841,"nsupset;":[8835,8402],"nsupseteq;":8841,"nsupseteqq;":[10950,824],"ntgl;":8825,ntilde:241,"ntilde;":241,"ntlg;":8824,"ntriangleleft;":8938,"ntrianglelefteq;":8940,"ntriangleright;":8939,"ntrianglerighteq;":8941,"nu;":957,"num;":35,"numero;":8470,"numsp;":8199,"nvDash;":8877,"nvHarr;":10500,"nvap;":[8781,8402],"nvdash;":8876,"nvge;":[8805,8402],"nvgt;":[62,8402],"nvinfin;":10718,"nvlArr;":10498,"nvle;":[8804,8402],"nvlt;":[60,8402],"nvltrie;":[8884,8402],"nvrArr;":10499,"nvrtrie;":[8885,8402],"nvsim;":[8764,8402],"nwArr;":8662,"nwarhk;":10531,"nwarr;":8598,"nwarrow;":8598,"nwnear;":10535,"oS;":9416,oacute:243,"oacute;":243,"oast;":8859,"ocir;":8858,ocirc:244,"ocirc;":244,"ocy;":1086,"odash;":8861,"odblac;":337,"odiv;":10808,"odot;":8857,"odsold;":10684,"oelig;":339,"ofcir;":10687,"ofr;":[55349,56620],"ogon;":731,ograve:242,"ograve;":242,"ogt;":10689,"ohbar;":10677,"ohm;":937,"oint;":8750,"olarr;":8634,"olcir;":10686,"olcross;":10683,"oline;":8254,"olt;":10688,"omacr;":333,"omega;":969,"omicron;":959,"omid;":10678,"ominus;":8854,"oopf;":[55349,56672],"opar;":10679,"operp;":10681,"oplus;":8853,"or;":8744,"orarr;":8635,"ord;":10845,"order;":8500,"orderof;":8500,ordf:170,"ordf;":170,ordm:186,"ordm;":186,"origof;":8886,"oror;":10838,"orslope;":10839,"orv;":10843,"oscr;":8500,oslash:248,"oslash;":248,"osol;":8856,otilde:245,"otilde;":245,"otimes;":8855,"otimesas;":10806,ouml:246,"ouml;":246,"ovbar;":9021,"par;":8741,para:182,"para;":182,"parallel;":8741,"parsim;":10995,"parsl;":11005,"part;":8706,"pcy;":1087,"percnt;":37,"period;":46,"permil;":8240,"perp;":8869,"pertenk;":8241,"pfr;":[55349,56621],"phi;":966,"phiv;":981,"phmmat;":8499,"phone;":9742,"pi;":960,"pitchfork;":8916,"piv;":982,"planck;":8463,"planckh;":8462,"plankv;":8463,"plus;":43,"plusacir;":10787,"plusb;":8862,"pluscir;":10786,"plusdo;":8724,"plusdu;":10789,"pluse;":10866,plusmn:177,"plusmn;":177,"plussim;":10790,"plustwo;":10791,"pm;":177,"pointint;":10773,"popf;":[55349,56673],pound:163,"pound;":163,"pr;":8826,"prE;":10931,"prap;":10935,"prcue;":8828,"pre;":10927,"prec;":8826,"precapprox;":10935,"preccurlyeq;":8828,"preceq;":10927,"precnapprox;":10937,"precneqq;":10933,"precnsim;":8936,"precsim;":8830,"prime;":8242,"primes;":8473,"prnE;":10933,"prnap;":10937,"prnsim;":8936,"prod;":8719,"profalar;":9006,"profline;":8978,"profsurf;":8979,"prop;":8733,"propto;":8733,"prsim;":8830,"prurel;":8880,"pscr;":[55349,56517],"psi;":968,"puncsp;":8200,"qfr;":[55349,56622],"qint;":10764,"qopf;":[55349,56674],"qprime;":8279,"qscr;":[55349,56518],"quaternions;":8461,"quatint;":10774,"quest;":63,"questeq;":8799,quot:34,"quot;":34,"rAarr;":8667,"rArr;":8658,"rAtail;":10524,"rBarr;":10511,"rHar;":10596,"race;":[8765,817],"racute;":341,"radic;":8730,"raemptyv;":10675,"rang;":10217,"rangd;":10642,"range;":10661,"rangle;":10217,raquo:187,"raquo;":187,"rarr;":8594,"rarrap;":10613,"rarrb;":8677,"rarrbfs;":10528,"rarrc;":10547,"rarrfs;":10526,"rarrhk;":8618,"rarrlp;":8620,"rarrpl;":10565,"rarrsim;":10612,"rarrtl;":8611,"rarrw;":8605,"ratail;":10522,"ratio;":8758,"rationals;":8474,"rbarr;":10509,"rbbrk;":10099,"rbrace;":125,"rbrack;":93,"rbrke;":10636,"rbrksld;":10638,"rbrkslu;":10640,"rcaron;":345,"rcedil;":343,"rceil;":8969,"rcub;":125,"rcy;":1088,"rdca;":10551,"rdldhar;":10601,"rdquo;":8221,"rdquor;":8221,"rdsh;":8627,"real;":8476,"realine;":8475,"realpart;":8476,"reals;":8477,"rect;":9645,reg:174,"reg;":174,"rfisht;":10621,"rfloor;":8971,"rfr;":[55349,56623],"rhard;":8641,"rharu;":8640,"rharul;":10604,"rho;":961,"rhov;":1009,"rightarrow;":8594,"rightarrowtail;":8611,"rightharpoondown;":8641,"rightharpoonup;":8640,"rightleftarrows;":8644,"rightleftharpoons;":8652,"rightrightarrows;":8649,"rightsquigarrow;":8605,"rightthreetimes;":8908,"ring;":730,"risingdotseq;":8787,"rlarr;":8644,"rlhar;":8652,"rlm;":8207,"rmoust;":9137,"rmoustache;":9137,"rnmid;":10990,"roang;":10221,"roarr;":8702,"robrk;":10215,"ropar;":10630,"ropf;":[55349,56675],"roplus;":10798,"rotimes;":10805,"rpar;":41,"rpargt;":10644,"rppolint;":10770,"rrarr;":8649,"rsaquo;":8250,"rscr;":[55349,56519],"rsh;":8625,"rsqb;":93,"rsquo;":8217,"rsquor;":8217,"rthree;":8908,"rtimes;":8906,"rtri;":9657,"rtrie;":8885,"rtrif;":9656,"rtriltri;":10702,"ruluhar;":10600,"rx;":8478,"sacute;":347,"sbquo;":8218,"sc;":8827,"scE;":10932,"scap;":10936,"scaron;":353,"sccue;":8829,"sce;":10928,"scedil;":351,"scirc;":349,"scnE;":10934,"scnap;":10938,"scnsim;":8937,"scpolint;":10771,"scsim;":8831,"scy;":1089,"sdot;":8901,"sdotb;":8865,"sdote;":10854,"seArr;":8664,"searhk;":10533,"searr;":8600,"searrow;":8600,sect:167,"sect;":167,"semi;":59,"seswar;":10537,"setminus;":8726,"setmn;":8726,"sext;":10038,"sfr;":[55349,56624],"sfrown;":8994,"sharp;":9839,"shchcy;":1097,"shcy;":1096,"shortmid;":8739,"shortparallel;":8741,shy:173,"shy;":173,"sigma;":963,"sigmaf;":962,"sigmav;":962,"sim;":8764,"simdot;":10858,"sime;":8771,"simeq;":8771,"simg;":10910,"simgE;":10912,"siml;":10909,"simlE;":10911,"simne;":8774,"simplus;":10788,"simrarr;":10610,"slarr;":8592,"smallsetminus;":8726,"smashp;":10803,"smeparsl;":10724,"smid;":8739,"smile;":8995,"smt;":10922,"smte;":10924,"smtes;":[10924,65024],"softcy;":1100,"sol;":47,"solb;":10692,"solbar;":9023,"sopf;":[55349,56676],"spades;":9824,"spadesuit;":9824,"spar;":8741,"sqcap;":8851,"sqcaps;":[8851,65024],"sqcup;":8852,"sqcups;":[8852,65024],"sqsub;":8847,"sqsube;":8849,"sqsubset;":8847,"sqsubseteq;":8849,"sqsup;":8848,"sqsupe;":8850,"sqsupset;":8848,"sqsupseteq;":8850,"squ;":9633,"square;":9633,"squarf;":9642,"squf;":9642,"srarr;":8594,"sscr;":[55349,56520],"ssetmn;":8726,"ssmile;":8995,"sstarf;":8902,"star;":9734,"starf;":9733,"straightepsilon;":1013,"straightphi;":981,"strns;":175,"sub;":8834,"subE;":10949,"subdot;":10941,"sube;":8838,"subedot;":10947,"submult;":10945,"subnE;":10955,"subne;":8842,"subplus;":10943,"subrarr;":10617,"subset;":8834,"subseteq;":8838,"subseteqq;":10949,"subsetneq;":8842,"subsetneqq;":10955,"subsim;":10951,"subsub;":10965,"subsup;":10963,"succ;":8827,"succapprox;":10936,"succcurlyeq;":8829,"succeq;":10928,"succnapprox;":10938,"succneqq;":10934,"succnsim;":8937,"succsim;":8831,"sum;":8721,"sung;":9834,sup1:185,"sup1;":185,sup2:178,"sup2;":178,sup3:179,"sup3;":179,"sup;":8835,"supE;":10950,"supdot;":10942,"supdsub;":10968,"supe;":8839,"supedot;":10948,"suphsol;":10185,"suphsub;":10967,"suplarr;":10619,"supmult;":10946,"supnE;":10956,"supne;":8843,"supplus;":10944,"supset;":8835,"supseteq;":8839,"supseteqq;":10950,"supsetneq;":8843,"supsetneqq;":10956,"supsim;":10952,"supsub;":10964,"supsup;":10966,"swArr;":8665,"swarhk;":10534,"swarr;":8601,"swarrow;":8601,"swnwar;":10538,szlig:223,"szlig;":223,"target;":8982,"tau;":964,"tbrk;":9140,"tcaron;":357,"tcedil;":355,"tcy;":1090,"tdot;":8411,"telrec;":8981,"tfr;":[55349,56625],"there4;":8756,"therefore;":8756,"theta;":952,"thetasym;":977,"thetav;":977,"thickapprox;":8776,"thicksim;":8764,"thinsp;":8201,"thkap;":8776,"thksim;":8764,thorn:254,"thorn;":254,"tilde;":732,times:215,"times;":215,"timesb;":8864,"timesbar;":10801,"timesd;":10800,"tint;":8749,"toea;":10536,"top;":8868,"topbot;":9014,"topcir;":10993,"topf;":[55349,56677],"topfork;":10970,"tosa;":10537,"tprime;":8244,"trade;":8482,"triangle;":9653,"triangledown;":9663,"triangleleft;":9667,"trianglelefteq;":8884,"triangleq;":8796,"triangleright;":9657,"trianglerighteq;":8885,"tridot;":9708,"trie;":8796,"triminus;":10810,"triplus;":10809,"trisb;":10701,"tritime;":10811,"trpezium;":9186,"tscr;":[55349,56521],"tscy;":1094,"tshcy;":1115,"tstrok;":359,"twixt;":8812,"twoheadleftarrow;":8606,"twoheadrightarrow;":8608,"uArr;":8657,"uHar;":10595,uacute:250,"uacute;":250,"uarr;":8593,"ubrcy;":1118,"ubreve;":365,ucirc:251,"ucirc;":251,"ucy;":1091,"udarr;":8645,"udblac;":369,"udhar;":10606,"ufisht;":10622,"ufr;":[55349,56626],ugrave:249,"ugrave;":249,"uharl;":8639,"uharr;":8638,"uhblk;":9600,"ulcorn;":8988,"ulcorner;":8988,"ulcrop;":8975,"ultri;":9720,"umacr;":363,uml:168,"uml;":168,"uogon;":371,"uopf;":[55349,56678],"uparrow;":8593,"updownarrow;":8597,"upharpoonleft;":8639,"upharpoonright;":8638,"uplus;":8846,"upsi;":965,"upsih;":978,"upsilon;":965,"upuparrows;":8648,"urcorn;":8989,"urcorner;":8989,"urcrop;":8974,"uring;":367,"urtri;":9721,"uscr;":[55349,56522],"utdot;":8944,"utilde;":361,"utri;":9653,"utrif;":9652,"uuarr;":8648,uuml:252,"uuml;":252,"uwangle;":10663,"vArr;":8661,"vBar;":10984,"vBarv;":10985,"vDash;":8872,"vangrt;":10652,"varepsilon;":1013,"varkappa;":1008,"varnothing;":8709,"varphi;":981,"varpi;":982,"varpropto;":8733,"varr;":8597,"varrho;":1009,"varsigma;":962,"varsubsetneq;":[8842,65024],"varsubsetneqq;":[10955,65024],"varsupsetneq;":[8843,65024],"varsupsetneqq;":[10956,65024],"vartheta;":977,"vartriangleleft;":8882,"vartriangleright;":8883,"vcy;":1074,"vdash;":8866,"vee;":8744,"veebar;":8891,"veeeq;":8794,"vellip;":8942,"verbar;":124,"vert;":124,"vfr;":[55349,56627],"vltri;":8882,"vnsub;":[8834,8402],"vnsup;":[8835,8402],"vopf;":[55349,56679],"vprop;":8733,"vrtri;":8883,"vscr;":[55349,56523],"vsubnE;":[10955,65024],"vsubne;":[8842,65024],"vsupnE;":[10956,65024],"vsupne;":[8843,65024],"vzigzag;":10650,"wcirc;":373,"wedbar;":10847,"wedge;":8743,"wedgeq;":8793,"weierp;":8472,"wfr;":[55349,56628],"wopf;":[55349,56680],"wp;":8472,"wr;":8768,"wreath;":8768,"wscr;":[55349,56524],"xcap;":8898,"xcirc;":9711,"xcup;":8899,"xdtri;":9661,"xfr;":[55349,56629],"xhArr;":10234,"xharr;":10231,"xi;":958,"xlArr;":10232,"xlarr;":10229,"xmap;":10236,"xnis;":8955,"xodot;":10752,"xopf;":[55349,56681],"xoplus;":10753,"xotime;":10754,"xrArr;":10233,"xrarr;":10230,"xscr;":[55349,56525],"xsqcup;":10758,"xuplus;":10756,"xutri;":9651,"xvee;":8897,"xwedge;":8896,yacute:253,"yacute;":253,"yacy;":1103,"ycirc;":375,"ycy;":1099,yen:165,"yen;":165,"yfr;":[55349,56630],"yicy;":1111,"yopf;":[55349,56682],"yscr;":[55349,56526],"yucy;":1102,yuml:255,"yuml;":255,"zacute;":378,"zcaron;":382,"zcy;":1079,"zdot;":380,"zeetrf;":8488,"zeta;":950,"zfr;":[55349,56631],"zhcy;":1078,"zigrarr;":8669,"zopf;":[55349,56683],"zscr;":[55349,56527],"zwj;":8205,"zwnj;":8204},xe=/(A(?:Elig;?|MP;?|acute;?|breve;|c(?:irc;?|y;)|fr;|grave;?|lpha;|macr;|nd;|o(?:gon;|pf;)|pplyFunction;|ring;?|s(?:cr;|sign;)|tilde;?|uml;?)|B(?:a(?:ckslash;|r(?:v;|wed;))|cy;|e(?:cause;|rnoullis;|ta;)|fr;|opf;|reve;|scr;|umpeq;)|C(?:Hcy;|OPY;?|a(?:cute;|p(?:;|italDifferentialD;)|yleys;)|c(?:aron;|edil;?|irc;|onint;)|dot;|e(?:dilla;|nterDot;)|fr;|hi;|ircle(?:Dot;|Minus;|Plus;|Times;)|lo(?:ckwiseContourIntegral;|seCurly(?:DoubleQuote;|Quote;))|o(?:lon(?:;|e;)|n(?:gruent;|int;|tourIntegral;)|p(?:f;|roduct;)|unterClockwiseContourIntegral;)|ross;|scr;|up(?:;|Cap;))|D(?:D(?:;|otrahd;)|Jcy;|Scy;|Zcy;|a(?:gger;|rr;|shv;)|c(?:aron;|y;)|el(?:;|ta;)|fr;|i(?:a(?:critical(?:Acute;|Do(?:t;|ubleAcute;)|Grave;|Tilde;)|mond;)|fferentialD;)|o(?:pf;|t(?:;|Dot;|Equal;)|uble(?:ContourIntegral;|Do(?:t;|wnArrow;)|L(?:eft(?:Arrow;|RightArrow;|Tee;)|ong(?:Left(?:Arrow;|RightArrow;)|RightArrow;))|Right(?:Arrow;|Tee;)|Up(?:Arrow;|DownArrow;)|VerticalBar;)|wn(?:Arrow(?:;|Bar;|UpArrow;)|Breve;|Left(?:RightVector;|TeeVector;|Vector(?:;|Bar;))|Right(?:TeeVector;|Vector(?:;|Bar;))|Tee(?:;|Arrow;)|arrow;))|s(?:cr;|trok;))|E(?:NG;|TH;?|acute;?|c(?:aron;|irc;?|y;)|dot;|fr;|grave;?|lement;|m(?:acr;|pty(?:SmallSquare;|VerySmallSquare;))|o(?:gon;|pf;)|psilon;|qu(?:al(?:;|Tilde;)|ilibrium;)|s(?:cr;|im;)|ta;|uml;?|x(?:ists;|ponentialE;))|F(?:cy;|fr;|illed(?:SmallSquare;|VerySmallSquare;)|o(?:pf;|rAll;|uriertrf;)|scr;)|G(?:Jcy;|T;?|amma(?:;|d;)|breve;|c(?:edil;|irc;|y;)|dot;|fr;|g;|opf;|reater(?:Equal(?:;|Less;)|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|scr;|t;)|H(?:ARDcy;|a(?:cek;|t;)|circ;|fr;|ilbertSpace;|o(?:pf;|rizontalLine;)|s(?:cr;|trok;)|ump(?:DownHump;|Equal;))|I(?:Ecy;|Jlig;|Ocy;|acute;?|c(?:irc;?|y;)|dot;|fr;|grave;?|m(?:;|a(?:cr;|ginaryI;)|plies;)|n(?:t(?:;|e(?:gral;|rsection;))|visible(?:Comma;|Times;))|o(?:gon;|pf;|ta;)|scr;|tilde;|u(?:kcy;|ml;?))|J(?:c(?:irc;|y;)|fr;|opf;|s(?:cr;|ercy;)|ukcy;)|K(?:Hcy;|Jcy;|appa;|c(?:edil;|y;)|fr;|opf;|scr;)|L(?:Jcy;|T;?|a(?:cute;|mbda;|ng;|placetrf;|rr;)|c(?:aron;|edil;|y;)|e(?:ft(?:A(?:ngleBracket;|rrow(?:;|Bar;|RightArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|Right(?:Arrow;|Vector;)|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;|rightarrow;)|ss(?:EqualGreater;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;))|fr;|l(?:;|eftarrow;)|midot;|o(?:ng(?:Left(?:Arrow;|RightArrow;)|RightArrow;|left(?:arrow;|rightarrow;)|rightarrow;)|pf;|wer(?:LeftArrow;|RightArrow;))|s(?:cr;|h;|trok;)|t;)|M(?:ap;|cy;|e(?:diumSpace;|llintrf;)|fr;|inusPlus;|opf;|scr;|u;)|N(?:Jcy;|acute;|c(?:aron;|edil;|y;)|e(?:gative(?:MediumSpace;|Thi(?:ckSpace;|nSpace;)|VeryThinSpace;)|sted(?:GreaterGreater;|LessLess;)|wLine;)|fr;|o(?:Break;|nBreakingSpace;|pf;|t(?:;|C(?:ongruent;|upCap;)|DoubleVerticalBar;|E(?:lement;|qual(?:;|Tilde;)|xists;)|Greater(?:;|Equal;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|Hump(?:DownHump;|Equal;)|Le(?:ftTriangle(?:;|Bar;|Equal;)|ss(?:;|Equal;|Greater;|Less;|SlantEqual;|Tilde;))|Nested(?:GreaterGreater;|LessLess;)|Precedes(?:;|Equal;|SlantEqual;)|R(?:everseElement;|ightTriangle(?:;|Bar;|Equal;))|S(?:quareSu(?:bset(?:;|Equal;)|perset(?:;|Equal;))|u(?:bset(?:;|Equal;)|cceeds(?:;|Equal;|SlantEqual;|Tilde;)|perset(?:;|Equal;)))|Tilde(?:;|Equal;|FullEqual;|Tilde;)|VerticalBar;))|scr;|tilde;?|u;)|O(?:Elig;|acute;?|c(?:irc;?|y;)|dblac;|fr;|grave;?|m(?:acr;|ega;|icron;)|opf;|penCurly(?:DoubleQuote;|Quote;)|r;|s(?:cr;|lash;?)|ti(?:lde;?|mes;)|uml;?|ver(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;))|P(?:artialD;|cy;|fr;|hi;|i;|lusMinus;|o(?:incareplane;|pf;)|r(?:;|ecedes(?:;|Equal;|SlantEqual;|Tilde;)|ime;|o(?:duct;|portion(?:;|al;)))|s(?:cr;|i;))|Q(?:UOT;?|fr;|opf;|scr;)|R(?:Barr;|EG;?|a(?:cute;|ng;|rr(?:;|tl;))|c(?:aron;|edil;|y;)|e(?:;|verse(?:E(?:lement;|quilibrium;)|UpEquilibrium;))|fr;|ho;|ight(?:A(?:ngleBracket;|rrow(?:;|Bar;|LeftArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;)|o(?:pf;|undImplies;)|rightarrow;|s(?:cr;|h;)|uleDelayed;)|S(?:H(?:CHcy;|cy;)|OFTcy;|acute;|c(?:;|aron;|edil;|irc;|y;)|fr;|hort(?:DownArrow;|LeftArrow;|RightArrow;|UpArrow;)|igma;|mallCircle;|opf;|q(?:rt;|uare(?:;|Intersection;|Su(?:bset(?:;|Equal;)|perset(?:;|Equal;))|Union;))|scr;|tar;|u(?:b(?:;|set(?:;|Equal;))|c(?:ceeds(?:;|Equal;|SlantEqual;|Tilde;)|hThat;)|m;|p(?:;|erset(?:;|Equal;)|set;)))|T(?:HORN;?|RADE;|S(?:Hcy;|cy;)|a(?:b;|u;)|c(?:aron;|edil;|y;)|fr;|h(?:e(?:refore;|ta;)|i(?:ckSpace;|nSpace;))|ilde(?:;|Equal;|FullEqual;|Tilde;)|opf;|ripleDot;|s(?:cr;|trok;))|U(?:a(?:cute;?|rr(?:;|ocir;))|br(?:cy;|eve;)|c(?:irc;?|y;)|dblac;|fr;|grave;?|macr;|n(?:der(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;)|ion(?:;|Plus;))|o(?:gon;|pf;)|p(?:Arrow(?:;|Bar;|DownArrow;)|DownArrow;|Equilibrium;|Tee(?:;|Arrow;)|arrow;|downarrow;|per(?:LeftArrow;|RightArrow;)|si(?:;|lon;))|ring;|scr;|tilde;|uml;?)|V(?:Dash;|bar;|cy;|dash(?:;|l;)|e(?:e;|r(?:bar;|t(?:;|ical(?:Bar;|Line;|Separator;|Tilde;))|yThinSpace;))|fr;|opf;|scr;|vdash;)|W(?:circ;|edge;|fr;|opf;|scr;)|X(?:fr;|i;|opf;|scr;)|Y(?:Acy;|Icy;|Ucy;|acute;?|c(?:irc;|y;)|fr;|opf;|scr;|uml;)|Z(?:Hcy;|acute;|c(?:aron;|y;)|dot;|e(?:roWidthSpace;|ta;)|fr;|opf;|scr;)|a(?:acute;?|breve;|c(?:;|E;|d;|irc;?|ute;?|y;)|elig;?|f(?:;|r;)|grave;?|l(?:e(?:fsym;|ph;)|pha;)|m(?:a(?:cr;|lg;)|p;?)|n(?:d(?:;|and;|d;|slope;|v;)|g(?:;|e;|le;|msd(?:;|a(?:a;|b;|c;|d;|e;|f;|g;|h;))|rt(?:;|vb(?:;|d;))|s(?:ph;|t;)|zarr;))|o(?:gon;|pf;)|p(?:;|E;|acir;|e;|id;|os;|prox(?:;|eq;))|ring;?|s(?:cr;|t;|ymp(?:;|eq;))|tilde;?|uml;?|w(?:conint;|int;))|b(?:Not;|a(?:ck(?:cong;|epsilon;|prime;|sim(?:;|eq;))|r(?:vee;|wed(?:;|ge;)))|brk(?:;|tbrk;)|c(?:ong;|y;)|dquo;|e(?:caus(?:;|e;)|mptyv;|psi;|rnou;|t(?:a;|h;|ween;))|fr;|ig(?:c(?:ap;|irc;|up;)|o(?:dot;|plus;|times;)|s(?:qcup;|tar;)|triangle(?:down;|up;)|uplus;|vee;|wedge;)|karow;|l(?:a(?:ck(?:lozenge;|square;|triangle(?:;|down;|left;|right;))|nk;)|k(?:1(?:2;|4;)|34;)|ock;)|n(?:e(?:;|quiv;)|ot;)|o(?:pf;|t(?:;|tom;)|wtie;|x(?:D(?:L;|R;|l;|r;)|H(?:;|D;|U;|d;|u;)|U(?:L;|R;|l;|r;)|V(?:;|H;|L;|R;|h;|l;|r;)|box;|d(?:L;|R;|l;|r;)|h(?:;|D;|U;|d;|u;)|minus;|plus;|times;|u(?:L;|R;|l;|r;)|v(?:;|H;|L;|R;|h;|l;|r;)))|prime;|r(?:eve;|vbar;?)|s(?:cr;|emi;|im(?:;|e;)|ol(?:;|b;|hsub;))|u(?:ll(?:;|et;)|mp(?:;|E;|e(?:;|q;))))|c(?:a(?:cute;|p(?:;|and;|brcup;|c(?:ap;|up;)|dot;|s;)|r(?:et;|on;))|c(?:a(?:ps;|ron;)|edil;?|irc;|ups(?:;|sm;))|dot;|e(?:dil;?|mptyv;|nt(?:;|erdot;|))|fr;|h(?:cy;|eck(?:;|mark;)|i;)|ir(?:;|E;|c(?:;|eq;|le(?:arrow(?:left;|right;)|d(?:R;|S;|ast;|circ;|dash;)))|e;|fnint;|mid;|scir;)|lubs(?:;|uit;)|o(?:lon(?:;|e(?:;|q;))|m(?:ma(?:;|t;)|p(?:;|fn;|le(?:ment;|xes;)))|n(?:g(?:;|dot;)|int;)|p(?:f;|rod;|y(?:;|sr;|)))|r(?:arr;|oss;)|s(?:cr;|u(?:b(?:;|e;)|p(?:;|e;)))|tdot;|u(?:darr(?:l;|r;)|e(?:pr;|sc;)|larr(?:;|p;)|p(?:;|brcap;|c(?:ap;|up;)|dot;|or;|s;)|r(?:arr(?:;|m;)|ly(?:eq(?:prec;|succ;)|vee;|wedge;)|ren;?|vearrow(?:left;|right;))|vee;|wed;)|w(?:conint;|int;)|ylcty;)|d(?:Arr;|Har;|a(?:gger;|leth;|rr;|sh(?:;|v;))|b(?:karow;|lac;)|c(?:aron;|y;)|d(?:;|a(?:gger;|rr;)|otseq;)|e(?:g;?|lta;|mptyv;)|f(?:isht;|r;)|har(?:l;|r;)|i(?:am(?:;|ond(?:;|suit;)|s;)|e;|gamma;|sin;|v(?:;|ide(?:;|ontimes;|)|onx;))|jcy;|lc(?:orn;|rop;)|o(?:llar;|pf;|t(?:;|eq(?:;|dot;)|minus;|plus;|square;)|ublebarwedge;|wn(?:arrow;|downarrows;|harpoon(?:left;|right;)))|r(?:bkarow;|c(?:orn;|rop;))|s(?:c(?:r;|y;)|ol;|trok;)|t(?:dot;|ri(?:;|f;))|u(?:arr;|har;)|wangle;|z(?:cy;|igrarr;))|e(?:D(?:Dot;|ot;)|a(?:cute;?|ster;)|c(?:aron;|ir(?:;|c;?)|olon;|y;)|dot;|e;|f(?:Dot;|r;)|g(?:;|rave;?|s(?:;|dot;))|l(?:;|inters;|l;|s(?:;|dot;))|m(?:acr;|pty(?:;|set;|v;)|sp(?:1(?:3;|4;)|;))|n(?:g;|sp;)|o(?:gon;|pf;)|p(?:ar(?:;|sl;)|lus;|si(?:;|lon;|v;))|q(?:c(?:irc;|olon;)|s(?:im;|lant(?:gtr;|less;))|u(?:als;|est;|iv(?:;|DD;))|vparsl;)|r(?:Dot;|arr;)|s(?:cr;|dot;|im;)|t(?:a;|h;?)|u(?:ml;?|ro;)|x(?:cl;|ist;|p(?:ectation;|onentiale;)))|f(?:allingdotseq;|cy;|emale;|f(?:ilig;|l(?:ig;|lig;)|r;)|ilig;|jlig;|l(?:at;|lig;|tns;)|nof;|o(?:pf;|r(?:all;|k(?:;|v;)))|partint;|r(?:a(?:c(?:1(?:2;?|3;|4;?|5;|6;|8;)|2(?:3;|5;)|3(?:4;?|5;|8;)|45;|5(?:6;|8;)|78;)|sl;)|own;)|scr;)|g(?:E(?:;|l;)|a(?:cute;|mma(?:;|d;)|p;)|breve;|c(?:irc;|y;)|dot;|e(?:;|l;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|l;))|l(?:;|es;)))|fr;|g(?:;|g;)|imel;|jcy;|l(?:;|E;|a;|j;)|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|opf;|rave;|s(?:cr;|im(?:;|e;|l;))|t(?:;|c(?:c;|ir;)|dot;|lPar;|quest;|r(?:a(?:pprox;|rr;)|dot;|eq(?:less;|qless;)|less;|sim;)|)|v(?:ertneqq;|nE;))|h(?:Arr;|a(?:irsp;|lf;|milt;|r(?:dcy;|r(?:;|cir;|w;)))|bar;|circ;|e(?:arts(?:;|uit;)|llip;|rcon;)|fr;|ks(?:earow;|warow;)|o(?:arr;|mtht;|ok(?:leftarrow;|rightarrow;)|pf;|rbar;)|s(?:cr;|lash;|trok;)|y(?:bull;|phen;))|i(?:acute;?|c(?:;|irc;?|y;)|e(?:cy;|xcl;?)|f(?:f;|r;)|grave;?|i(?:;|i(?:int;|nt;)|nfin;|ota;)|jlig;|m(?:a(?:cr;|g(?:e;|line;|part;)|th;)|of;|ped;)|n(?:;|care;|fin(?:;|tie;)|odot;|t(?:;|cal;|e(?:gers;|rcal;)|larhk;|prod;))|o(?:cy;|gon;|pf;|ta;)|prod;|quest;?|s(?:cr;|in(?:;|E;|dot;|s(?:;|v;)|v;))|t(?:;|ilde;)|u(?:kcy;|ml;?))|j(?:c(?:irc;|y;)|fr;|math;|opf;|s(?:cr;|ercy;)|ukcy;)|k(?:appa(?:;|v;)|c(?:edil;|y;)|fr;|green;|hcy;|jcy;|opf;|scr;)|l(?:A(?:arr;|rr;|tail;)|Barr;|E(?:;|g;)|Har;|a(?:cute;|emptyv;|gran;|mbda;|ng(?:;|d;|le;)|p;|quo;?|rr(?:;|b(?:;|fs;)|fs;|hk;|lp;|pl;|sim;|tl;)|t(?:;|ail;|e(?:;|s;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|quo(?:;|r;)|r(?:dhar;|ushar;)|sh;)|e(?:;|ft(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|leftarrows;|right(?:arrow(?:;|s;)|harpoons;|squigarrow;)|threetimes;)|g;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|r;))|g(?:;|es;)|s(?:approx;|dot;|eq(?:gtr;|qgtr;)|gtr;|sim;)))|f(?:isht;|loor;|r;)|g(?:;|E;)|h(?:ar(?:d;|u(?:;|l;))|blk;)|jcy;|l(?:;|arr;|corner;|hard;|tri;)|m(?:idot;|oust(?:;|ache;))|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|o(?:a(?:ng;|rr;)|brk;|ng(?:left(?:arrow;|rightarrow;)|mapsto;|rightarrow;)|oparrow(?:left;|right;)|p(?:ar;|f;|lus;)|times;|w(?:ast;|bar;)|z(?:;|enge;|f;))|par(?:;|lt;)|r(?:arr;|corner;|har(?:;|d;)|m;|tri;)|s(?:aquo;|cr;|h;|im(?:;|e;|g;)|q(?:b;|uo(?:;|r;))|trok;)|t(?:;|c(?:c;|ir;)|dot;|hree;|imes;|larr;|quest;|r(?:Par;|i(?:;|e;|f;))|)|ur(?:dshar;|uhar;)|v(?:ertneqq;|nE;))|m(?:DDot;|a(?:cr;?|l(?:e;|t(?:;|ese;))|p(?:;|sto(?:;|down;|left;|up;))|rker;)|c(?:omma;|y;)|dash;|easuredangle;|fr;|ho;|i(?:cro;?|d(?:;|ast;|cir;|dot;?)|nus(?:;|b;|d(?:;|u;)))|l(?:cp;|dr;)|nplus;|o(?:dels;|pf;)|p;|s(?:cr;|tpos;)|u(?:;|ltimap;|map;))|n(?:G(?:g;|t(?:;|v;))|L(?:eft(?:arrow;|rightarrow;)|l;|t(?:;|v;))|Rightarrow;|V(?:Dash;|dash;)|a(?:bla;|cute;|ng;|p(?:;|E;|id;|os;|prox;)|tur(?:;|al(?:;|s;)))|b(?:sp;?|ump(?:;|e;))|c(?:a(?:p;|ron;)|edil;|ong(?:;|dot;)|up;|y;)|dash;|e(?:;|Arr;|ar(?:hk;|r(?:;|ow;))|dot;|quiv;|s(?:ear;|im;)|xist(?:;|s;))|fr;|g(?:E;|e(?:;|q(?:;|q;|slant;)|s;)|sim;|t(?:;|r;))|h(?:Arr;|arr;|par;)|i(?:;|s(?:;|d;)|v;)|jcy;|l(?:Arr;|E;|arr;|dr;|e(?:;|ft(?:arrow;|rightarrow;)|q(?:;|q;|slant;)|s(?:;|s;))|sim;|t(?:;|ri(?:;|e;)))|mid;|o(?:pf;|t(?:;|in(?:;|E;|dot;|v(?:a;|b;|c;))|ni(?:;|v(?:a;|b;|c;))|))|p(?:ar(?:;|allel;|sl;|t;)|olint;|r(?:;|cue;|e(?:;|c(?:;|eq;))))|r(?:Arr;|arr(?:;|c;|w;)|ightarrow;|tri(?:;|e;))|s(?:c(?:;|cue;|e;|r;)|hort(?:mid;|parallel;)|im(?:;|e(?:;|q;))|mid;|par;|qsu(?:be;|pe;)|u(?:b(?:;|E;|e;|set(?:;|eq(?:;|q;)))|cc(?:;|eq;)|p(?:;|E;|e;|set(?:;|eq(?:;|q;)))))|t(?:gl;|ilde;?|lg;|riangle(?:left(?:;|eq;)|right(?:;|eq;)))|u(?:;|m(?:;|ero;|sp;))|v(?:Dash;|Harr;|ap;|dash;|g(?:e;|t;)|infin;|l(?:Arr;|e;|t(?:;|rie;))|r(?:Arr;|trie;)|sim;)|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|near;))|o(?:S;|a(?:cute;?|st;)|c(?:ir(?:;|c;?)|y;)|d(?:ash;|blac;|iv;|ot;|sold;)|elig;|f(?:cir;|r;)|g(?:on;|rave;?|t;)|h(?:bar;|m;)|int;|l(?:arr;|c(?:ir;|ross;)|ine;|t;)|m(?:acr;|ega;|i(?:cron;|d;|nus;))|opf;|p(?:ar;|erp;|lus;)|r(?:;|arr;|d(?:;|er(?:;|of;)|f;?|m;?)|igof;|or;|slope;|v;)|s(?:cr;|lash;?|ol;)|ti(?:lde;?|mes(?:;|as;))|uml;?|vbar;)|p(?:ar(?:;|a(?:;|llel;|)|s(?:im;|l;)|t;)|cy;|er(?:cnt;|iod;|mil;|p;|tenk;)|fr;|h(?:i(?:;|v;)|mmat;|one;)|i(?:;|tchfork;|v;)|l(?:an(?:ck(?:;|h;)|kv;)|us(?:;|acir;|b;|cir;|d(?:o;|u;)|e;|mn;?|sim;|two;))|m;|o(?:intint;|pf;|und;?)|r(?:;|E;|ap;|cue;|e(?:;|c(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;))|ime(?:;|s;)|n(?:E;|ap;|sim;)|o(?:d;|f(?:alar;|line;|surf;)|p(?:;|to;))|sim;|urel;)|s(?:cr;|i;)|uncsp;)|q(?:fr;|int;|opf;|prime;|scr;|u(?:at(?:ernions;|int;)|est(?:;|eq;)|ot;?))|r(?:A(?:arr;|rr;|tail;)|Barr;|Har;|a(?:c(?:e;|ute;)|dic;|emptyv;|ng(?:;|d;|e;|le;)|quo;?|rr(?:;|ap;|b(?:;|fs;)|c;|fs;|hk;|lp;|pl;|sim;|tl;|w;)|t(?:ail;|io(?:;|nals;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|ldhar;|quo(?:;|r;)|sh;)|e(?:al(?:;|ine;|part;|s;)|ct;|g;?)|f(?:isht;|loor;|r;)|h(?:ar(?:d;|u(?:;|l;))|o(?:;|v;))|i(?:ght(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|left(?:arrows;|harpoons;)|rightarrows;|squigarrow;|threetimes;)|ng;|singdotseq;)|l(?:arr;|har;|m;)|moust(?:;|ache;)|nmid;|o(?:a(?:ng;|rr;)|brk;|p(?:ar;|f;|lus;)|times;)|p(?:ar(?:;|gt;)|polint;)|rarr;|s(?:aquo;|cr;|h;|q(?:b;|uo(?:;|r;)))|t(?:hree;|imes;|ri(?:;|e;|f;|ltri;))|uluhar;|x;)|s(?:acute;|bquo;|c(?:;|E;|a(?:p;|ron;)|cue;|e(?:;|dil;)|irc;|n(?:E;|ap;|sim;)|polint;|sim;|y;)|dot(?:;|b;|e;)|e(?:Arr;|ar(?:hk;|r(?:;|ow;))|ct;?|mi;|swar;|tm(?:inus;|n;)|xt;)|fr(?:;|own;)|h(?:arp;|c(?:hcy;|y;)|ort(?:mid;|parallel;)|y;?)|i(?:gma(?:;|f;|v;)|m(?:;|dot;|e(?:;|q;)|g(?:;|E;)|l(?:;|E;)|ne;|plus;|rarr;))|larr;|m(?:a(?:llsetminus;|shp;)|eparsl;|i(?:d;|le;)|t(?:;|e(?:;|s;)))|o(?:ftcy;|l(?:;|b(?:;|ar;))|pf;)|pa(?:des(?:;|uit;)|r;)|q(?:c(?:ap(?:;|s;)|up(?:;|s;))|su(?:b(?:;|e;|set(?:;|eq;))|p(?:;|e;|set(?:;|eq;)))|u(?:;|ar(?:e;|f;)|f;))|rarr;|s(?:cr;|etmn;|mile;|tarf;)|t(?:ar(?:;|f;)|r(?:aight(?:epsilon;|phi;)|ns;))|u(?:b(?:;|E;|dot;|e(?:;|dot;)|mult;|n(?:E;|e;)|plus;|rarr;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;)))|cc(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;)|m;|ng;|p(?:1;?|2;?|3;?|;|E;|d(?:ot;|sub;)|e(?:;|dot;)|hs(?:ol;|ub;)|larr;|mult;|n(?:E;|e;)|plus;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;))))|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|nwar;)|zlig;?)|t(?:a(?:rget;|u;)|brk;|c(?:aron;|edil;|y;)|dot;|elrec;|fr;|h(?:e(?:re(?:4;|fore;)|ta(?:;|sym;|v;))|i(?:ck(?:approx;|sim;)|nsp;)|k(?:ap;|sim;)|orn;?)|i(?:lde;|mes(?:;|b(?:;|ar;)|d;|)|nt;)|o(?:ea;|p(?:;|bot;|cir;|f(?:;|ork;))|sa;)|prime;|r(?:ade;|i(?:angle(?:;|down;|left(?:;|eq;)|q;|right(?:;|eq;))|dot;|e;|minus;|plus;|sb;|time;)|pezium;)|s(?:c(?:r;|y;)|hcy;|trok;)|w(?:ixt;|ohead(?:leftarrow;|rightarrow;)))|u(?:Arr;|Har;|a(?:cute;?|rr;)|br(?:cy;|eve;)|c(?:irc;?|y;)|d(?:arr;|blac;|har;)|f(?:isht;|r;)|grave;?|h(?:ar(?:l;|r;)|blk;)|l(?:c(?:orn(?:;|er;)|rop;)|tri;)|m(?:acr;|l;?)|o(?:gon;|pf;)|p(?:arrow;|downarrow;|harpoon(?:left;|right;)|lus;|si(?:;|h;|lon;)|uparrows;)|r(?:c(?:orn(?:;|er;)|rop;)|ing;|tri;)|scr;|t(?:dot;|ilde;|ri(?:;|f;))|u(?:arr;|ml;?)|wangle;)|v(?:Arr;|Bar(?:;|v;)|Dash;|a(?:ngrt;|r(?:epsilon;|kappa;|nothing;|p(?:hi;|i;|ropto;)|r(?:;|ho;)|s(?:igma;|u(?:bsetneq(?:;|q;)|psetneq(?:;|q;)))|t(?:heta;|riangle(?:left;|right;))))|cy;|dash;|e(?:e(?:;|bar;|eq;)|llip;|r(?:bar;|t;))|fr;|ltri;|nsu(?:b;|p;)|opf;|prop;|rtri;|s(?:cr;|u(?:bn(?:E;|e;)|pn(?:E;|e;)))|zigzag;)|w(?:circ;|e(?:d(?:bar;|ge(?:;|q;))|ierp;)|fr;|opf;|p;|r(?:;|eath;)|scr;)|x(?:c(?:ap;|irc;|up;)|dtri;|fr;|h(?:Arr;|arr;)|i;|l(?:Arr;|arr;)|map;|nis;|o(?:dot;|p(?:f;|lus;)|time;)|r(?:Arr;|arr;)|s(?:cr;|qcup;)|u(?:plus;|tri;)|vee;|wedge;)|y(?:ac(?:ute;?|y;)|c(?:irc;|y;)|en;?|fr;|icy;|opf;|scr;|u(?:cy;|ml;?))|z(?:acute;|c(?:aron;|y;)|dot;|e(?:etrf;|ta;)|fr;|hcy;|igrarr;|opf;|scr;|w(?:j;|nj;)))|[\s\S]/g,bt=/[^\r"&\u0000]+/g,Q=/[^\r'&\u0000]+/g,z=/[^\r\t\n\f &>\u0000]+/g,H=/[^\r\t\n\f \/>A-Z\u0000]+/g,Z=/[^\r\t\n\f \/=>A-Z\u0000]+/g,ce=/[^\]\r\u0000\uffff]*/g,he=/[^&<\r\u0000\uffff]*/g,ae=/[^<\r\u0000\uffff]*/g,me=/[^\r\u0000\uffff]*/g,ge=/(?:(\/)?([a-z]+)>)|[\s\S]/g,be=/(?:([-a-z]+)[ \t\n\f]*=[ \t\n\f]*('[^'&\r\u0000]*'|"[^"&\r\u0000]*"|[^\t\n\r\f "&'\u0000>][^&> \t\n\r\f\u0000]*[ \t\n\f]))|[\s\S]/g,Se=/[^\x09\x0A\x0C\x0D\x20]/,Ce=/[^\x09\x0A\x0C\x0D\x20]/g,nt=/[^\x00\x09\x0A\x0C\x0D\x20]/,Ue=/^[\x09\x0A\x0C\x0D\x20]+/,Je=/\x00/g;function Fe(F){var U=16384;if(F.length<U)return String.fromCharCode.apply(String,F);for(var oe="",te=0;te<F.length;te+=U)oe+=String.fromCharCode.apply(String,F.slice(te,te+U));return oe}function vr(F){for(var U=[],oe=0;oe<F.length;oe++)U[oe]=F.charCodeAt(oe);return U}function we(F,U){if(typeof U=="string")return F.namespaceURI===i.HTML&&F.localName===U;var oe=U[F.namespaceURI];return oe&&oe[F.localName]}function Wt(F){return we(F,C)}function nr(F){if(we(F,G))return!0;if(F.namespaceURI===i.MATHML&&F.localName==="annotation-xml"){var U=F.getAttribute("encoding");if(U&&(U=U.toLowerCase()),U==="text/html"||U==="application/xhtml+xml")return!0}return!1}function _t(F){return F in M?M[F]:F}function ar(F){for(var U=0,oe=F.length;U<oe;U++)F[U][0]in y&&(F[U][0]=y[F[U][0]])}function qt(F){for(var U=0,oe=F.length;U<oe;U++)if(F[U][0]==="definitionurl"){F[U][0]="definitionURL";break}}function Kt(F){for(var U=0,oe=F.length;U<oe;U++)F[U][0]in ee&&F[U].push(ee[F[U][0]])}function ir(F,U){for(var oe=0,te=F.length;oe<te;oe++){var Oe=F[oe][0],re=F[oe][1];U.hasAttribute(Oe)||U._setAttribute(Oe,re)}}ve.ElementStack=function(){this.elements=[],this.top=null},ve.ElementStack.prototype.push=function(F){this.elements.push(F),this.top=F},ve.ElementStack.prototype.pop=function(F){this.elements.pop(),this.top=this.elements[this.elements.length-1]},ve.ElementStack.prototype.popTag=function(F){for(var U=this.elements.length-1;U>0;U--){var oe=this.elements[U];if(we(oe,F))break}this.elements.length=U,this.top=this.elements[U-1]},ve.ElementStack.prototype.popElementType=function(F){for(var U=this.elements.length-1;U>0&&!(this.elements[U]instanceof F);U--);this.elements.length=U,this.top=this.elements[U-1]},ve.ElementStack.prototype.popElement=function(F){for(var U=this.elements.length-1;U>0&&this.elements[U]!==F;U--);this.elements.length=U,this.top=this.elements[U-1]},ve.ElementStack.prototype.removeElement=function(F){if(this.top===F)this.pop();else{var U=this.elements.lastIndexOf(F);U!==-1&&this.elements.splice(U,1)}},ve.ElementStack.prototype.clearToContext=function(F){for(var U=this.elements.length-1;U>0&&!we(this.elements[U],F);U--);this.elements.length=U+1,this.top=this.elements[U]},ve.ElementStack.prototype.contains=function(F){return this.inSpecificScope(F,Object.create(null))},ve.ElementStack.prototype.inSpecificScope=function(F,U){for(var oe=this.elements.length-1;oe>=0;oe--){var te=this.elements[oe];if(we(te,F))return!0;if(we(te,U))return!1}return!1},ve.ElementStack.prototype.elementInSpecificScope=function(F,U){for(var oe=this.elements.length-1;oe>=0;oe--){var te=this.elements[oe];if(te===F)return!0;if(we(te,U))return!1}return!1},ve.ElementStack.prototype.elementTypeInSpecificScope=function(F,U){for(var oe=this.elements.length-1;oe>=0;oe--){var te=this.elements[oe];if(te instanceof F)return!0;if(we(te,U))return!1}return!1},ve.ElementStack.prototype.inScope=function(F){return this.inSpecificScope(F,r)},ve.ElementStack.prototype.elementInScope=function(F){return this.elementInSpecificScope(F,r)},ve.ElementStack.prototype.elementTypeInScope=function(F){return this.elementTypeInSpecificScope(F,r)},ve.ElementStack.prototype.inButtonScope=function(F){return this.inSpecificScope(F,N)},ve.ElementStack.prototype.inListItemScope=function(F){return this.inSpecificScope(F,l)},ve.ElementStack.prototype.inTableScope=function(F){return this.inSpecificScope(F,g)},ve.ElementStack.prototype.inSelectScope=function(F){for(var U=this.elements.length-1;U>=0;U--){var oe=this.elements[U];if(oe.namespaceURI!==i.HTML)return!1;var te=oe.localName;if(te===F)return!0;if(te!=="optgroup"&&te!=="option")return!1}return!1},ve.ElementStack.prototype.generateImpliedEndTags=function(F,U){for(var oe=U?V:Y,te=this.elements.length-1;te>=0;te--){var Oe=this.elements[te];if(F&&we(Oe,F)||!we(this.elements[te],oe))break}this.elements.length=te+1,this.top=this.elements[te]},ve.ActiveFormattingElements=function(){this.list=[],this.attrs=[]},ve.ActiveFormattingElements.prototype.MARKER={localName:"|"},ve.ActiveFormattingElements.prototype.insertMarker=function(){this.list.push(this.MARKER),this.attrs.push(this.MARKER)},ve.ActiveFormattingElements.prototype.push=function(F,U){for(var oe=0,te=this.list.length-1;te>=0&&this.list[te]!==this.MARKER;te--)if(Rt(F,this.list[te],this.attrs[te])&&(oe++,oe===3)){this.list.splice(te,1),this.attrs.splice(te,1);break}this.list.push(F);for(var Oe=[],re=0;re<U.length;re++)Oe[re]=U[re];this.attrs.push(Oe);function Rt(vt,Pt,ut){if(vt.localName!==Pt.localName||vt._numattrs!==ut.length)return!1;for(var Ze=0,Er=ut.length;Ze<Er;Ze++){var Bt=ut[Ze][0],x=ut[Ze][1];if(!vt.hasAttribute(Bt)||vt.getAttribute(Bt)!==x)return!1}return!0}},ve.ActiveFormattingElements.prototype.clearToMarker=function(){for(var F=this.list.length-1;F>=0&&this.list[F]!==this.MARKER;F--);F<0&&(F=0),this.list.length=F,this.attrs.length=F},ve.ActiveFormattingElements.prototype.findElementByTag=function(F){for(var U=this.list.length-1;U>=0;U--){var oe=this.list[U];if(oe===this.MARKER)break;if(oe.localName===F)return oe}return null},ve.ActiveFormattingElements.prototype.indexOf=function(F){return this.list.lastIndexOf(F)},ve.ActiveFormattingElements.prototype.remove=function(F){var U=this.list.lastIndexOf(F);U!==-1&&(this.list.splice(U,1),this.attrs.splice(U,1))},ve.ActiveFormattingElements.prototype.replace=function(F,U,oe){var te=this.list.lastIndexOf(F);te!==-1&&(this.list[te]=U,this.attrs[te]=oe)},ve.ActiveFormattingElements.prototype.insertAfter=function(F,U){var oe=this.list.lastIndexOf(F);oe!==-1&&(this.list.splice(oe,0,U),this.attrs.splice(oe,0,U))};function ve(F,U,oe){var te=null,Oe=0,re=0,Rt=!1,vt=!1,Pt=0,ut=[],Ze="",Er=!0,Bt=0,x=Te,Et,He,De="",Tr="",Me=[],Ke="",We="",Ae=[],Tt=[],yt=[],wt=[],et=[],yr=!1,j=Li,ft=null,ht=[],L=new ve.ElementStack,Ee=new ve.ActiveFormattingElements,Ft=U!==void 0,wr=null,pt=null,Nr=!0;U&&(Nr=U.ownerDocument._scripting_enabled),oe&&oe.scripting_enabled===!1&&(Nr=!1);var qe=!0,en=!1,Sr,tn,W=[],Nt=!1,Ut=!1,kr={document:function(){return Ne},_asDocumentFragment:function(){for(var t=Ne.createDocumentFragment(),a=Ne.firstChild;a.hasChildNodes();)t.appendChild(a.firstChild);return t},pause:function(){Bt++},resume:function(){Bt--,this.parse("")},parse:function(t,a,v){var A;return Bt>0?(Ze+=t,!0):(Pt===0?(Ze&&(t=Ze+t,Ze=""),a&&(t+="\uFFFF",Rt=!0),te=t,Oe=t.length,re=0,Er&&(Er=!1,te.charCodeAt(0)===65279&&(re=1)),Pt++,A=An(v),Ze=te.substring(re,Oe),Pt--):(Pt++,ut.push(te,Oe,re),te=t,Oe=t.length,re=0,An(),A=!1,Ze=te.substring(re,Oe),re=ut.pop(),Oe=ut.pop(),te=ut.pop(),Ze&&(te=Ze+te.substring(re),Oe=te.length,re=0,Ze=""),Pt--),A)}},Ne=new h(!0,F);if(Ne._parser=kr,Ne._scripting_enabled=Nr,U){if(U.ownerDocument._quirks&&(Ne._quirks=!0),U.ownerDocument._limitedQuirks&&(Ne._limitedQuirks=!0),U.namespaceURI===i.HTML)switch(U.localName){case"title":case"textarea":x=Ct;break;case"style":case"xmp":case"iframe":case"noembed":case"noframes":case"script":case"plaintext":x=on;break}var xn=Ne.createElement("html");Ne._appendChild(xn),L.push(xn),U instanceof o.HTMLTemplateElement&&ht.push(bn),fr();for(var sr=U;sr!==null;sr=sr.parentElement)if(sr instanceof o.HTMLFormElement){pt=sr;break}}function An(t){for(var a,v,A,q;re<Oe;){if(Bt>0||t&&t())return!0;switch(typeof x.lookahead){case"undefined":if(a=te.charCodeAt(re++),vt&&(vt=!1,a===10)){re++;continue}switch(a){case 13:re<Oe?te.charCodeAt(re)===10&&re++:vt=!0,x(10);break;case 65535:if(Rt&&re===Oe){x(s);break}default:x(a);break}break;case"number":a=te.charCodeAt(re);var $=x.lookahead,fe=!0;if($<0&&(fe=!1,$=-$),$<Oe-re)v=fe?te.substring(re,re+$):null,q=!1;else if(Rt)v=fe?te.substring(re,Oe):null,q=!0,a===65535&&re===Oe-1&&(a=s);else return!0;x(a,v,q);break;case"string":a=te.charCodeAt(re),A=x.lookahead;var ye=te.indexOf(A,re);if(ye!==-1)v=te.substring(re,ye+A.length),q=!1;else{if(!Rt)return!0;v=te.substring(re,Oe),a===65535&&re===Oe-1&&(a=s),q=!0}x(a,v,q);break}}return!1}function St(t,a){for(var v=0;v<et.length;v++)if(et[v][0]===t)return;a!==void 0?et.push([t,a]):et.push([t])}function xa(){be.lastIndex=re-1;var t=be.exec(te);if(!t)throw new Error("should never happen");var a=t[1];if(!a)return!1;var v=t[2],A=v.length;switch(v[0]){case'"':case"'":v=v.substring(1,A-1),re+=t[0].length-1,x=fn;break;default:x=ot,re+=t[0].length-1,v=v.substring(0,A-1);break}for(var q=0;q<et.length;q++)if(et[q][0]===a)return!0;return et.push([a,v]),!0}function Aa(){yr=!1,De="",et.length=0}function or(){yr=!0,De="",et.length=0}function dt(){Me.length=0}function rn(){Ke=""}function nn(){We=""}function On(){Ae.length=0}function Xt(){Tt.length=0,yt=null,wt=null}function Lr(){yt=[]}function kt(){wt=[]}function ke(){en=!0}function Oa(){return L.top&&L.top.namespaceURI!=="http://www.w3.org/1999/xhtml"}function Xe(t){return Tr===t}function Qt(){if(W.length>0){var t=Fe(W);if(W.length=0,Ut&&(Ut=!1,t[0]===`
`&&(t=t.substring(1)),t.length===0))return;Pe(b,t),Nt=!1}Ut=!1}function cr(t){t.lastIndex=re-1;var a=t.exec(te);if(a&&a.index===re-1)return a=a[0],re+=a.length-1,Rt&&re===Oe&&(a=a.slice(0,-1),re--),a;throw new Error("should never happen")}function lr(t){t.lastIndex=re-1;var a=t.exec(te)[0];return a?(Ia(a),re+=a.length-1,!0):!1}function Ia(t){W.length>0&&Qt(),!(Ut&&(Ut=!1,t[0]===`
`&&(t=t.substring(1)),t.length===0))&&Pe(b,t)}function mt(){if(yr)Pe(T,De);else{var t=De;De="",Tr=t,Pe(p,t,et)}}function Ha(){if(re===Oe)return!1;ge.lastIndex=re;var t=ge.exec(te);if(!t)throw new Error("should never happen");var a=t[2];if(!a)return!1;var v=t[1];return v?(re+=a.length+2,Pe(T,a)):(re+=a.length+1,Tr=a,Pe(p,a,I)),!0}function qa(){yr?Pe(T,De,null,!0):Pe(p,De,et,!0)}function Le(){Pe(R,Fe(Tt),yt?Fe(yt):void 0,wt?Fe(wt):void 0)}function _e(){Qt(),j(s),Ne.modclock=1}var Pe=kr.insertToken=function(a,v,A,q){Qt();var $=L.top;!$||$.namespaceURI===i.HTML?j(a,v,A,q):a!==p&&a!==b?Xn(a,v,A,q):Wt($)&&(a===b||a===p&&v!=="mglyph"&&v!=="malignmark")||a===p&&v==="svg"&&$.namespaceURI===i.MATHML&&$.localName==="annotation-xml"||nr($)?(tn=!0,j(a,v,A,q),tn=!1):Xn(a,v,A,q)};function at(t){var a=L.top;Lt&&we(a,ne)?Dr(function(v){return v.createComment(t)}):(a instanceof o.HTMLTemplateElement&&(a=a.content),a._appendChild(a.ownerDocument.createComment(t)))}function it(t){var a=L.top;if(Lt&&we(a,ne))Dr(function(A){return A.createTextNode(t)});else{a instanceof o.HTMLTemplateElement&&(a=a.content);var v=a.lastChild;v&&v.nodeType===n.TEXT_NODE?v.appendData(t):a._appendChild(a.ownerDocument.createTextNode(t))}}function ur(t,a,v){var A=u.createElement(t,a,null);if(v)for(var q=0,$=v.length;q<$;q++)A._setAttribute(v[q][0],v[q][1]);return A}var Lt=!1;function pe(t,a){var v=Cr(function(A){return ur(A,t,a)});return we(v,e)&&(v._form=pt),v}function Cr(t){var a;return Lt&&we(L.top,ne)?a=Dr(t):L.top instanceof o.HTMLTemplateElement?(a=t(L.top.content.ownerDocument),L.top.content._appendChild(a)):(a=t(L.top.ownerDocument),L.top._appendChild(a)),L.push(a),a}function an(t,a,v){return Cr(function(A){var q=A._createElementNS(t,v,null);if(a)for(var $=0,fe=a.length;$<fe;$++){var ye=a[$];ye.length===2?q._setAttribute(ye[0],ye[1]):q._setAttributeNS(ye[2],ye[0],ye[1])}return q})}function In(t){for(var a=L.elements.length-1;a>=0;a--)if(L.elements[a]instanceof t)return a;return-1}function Dr(t){var a,v,A=-1,q=-1,$;if(A=In(o.HTMLTableElement),q=In(o.HTMLTemplateElement),q>=0&&(A<0||q>A)?a=L.elements[q]:A>=0&&(a=L.elements[A].parentNode,a?v=L.elements[A]:a=L.elements[A-1]),a||(a=L.elements[0]),a instanceof o.HTMLTemplateElement&&(a=a.content),$=t(a.ownerDocument),$.nodeType===n.TEXT_NODE){var fe;if(v?fe=v.previousSibling:fe=a.lastChild,fe&&fe.nodeType===n.TEXT_NODE)return fe.appendData($.data),$}return v?a.insertBefore($,v):a._appendChild($),$}function fr(){for(var t=!1,a=L.elements.length-1;a>=0;a--){var v=L.elements[a];if(a===0&&(t=!0,Ft&&(v=U)),v.namespaceURI===i.HTML){var A=v.localName;switch(A){case"select":for(var q=a;q>0;){var $=L.elements[--q];if($ instanceof o.HTMLTemplateElement)break;if($ instanceof o.HTMLTableElement){j=Vr;return}}j=gt;return;case"tr":j=dr;return;case"tbody":case"tfoot":case"thead":j=zt;return;case"caption":j=gn;return;case"colgroup":j=jr;return;case"table":j=Qe;return;case"template":j=ht[ht.length-1];return;case"body":j=ue;return;case"frameset":j=_n;return;case"html":wr===null?j=Fr:j=mn;return;default:if(!t){if(A==="head"){j=Re;return}if(A==="td"||A==="th"){j=Yt;return}}}}if(t){j=ue;return}}}function Mr(t,a){pe(t,a),x=hr,ft=j,j=Ur}function Ra(t,a){pe(t,a),x=Ct,ft=j,j=Ur}function sn(t,a){return{elt:ur(t,Ee.list[a].localName,Ee.attrs[a]),attrs:Ee.attrs[a]}}function ze(){if(Ee.list.length!==0){var t=Ee.list[Ee.list.length-1];if(t!==Ee.MARKER&&L.elements.lastIndexOf(t)===-1){for(var a=Ee.list.length-2;a>=0&&(t=Ee.list[a],!(t===Ee.MARKER||L.elements.lastIndexOf(t)!==-1));a--);for(a=a+1;a<Ee.list.length;a++){var v=Cr(function(A){return sn(A,a).elt});Ee.list[a]=v}}}}var xr={localName:"BM"};function Pa(t){if(we(L.top,t)&&Ee.indexOf(L.top)===-1)return L.pop(),!0;for(var a=0;a<8;){a++;var v=Ee.findElementByTag(t);if(!v)return!1;var A=L.elements.lastIndexOf(v);if(A===-1)return Ee.remove(v),!0;if(!L.elementInScope(v))return!0;for(var q=null,$,fe=A+1;fe<L.elements.length;fe++)if(we(L.elements[fe],w)){q=L.elements[fe],$=fe;break}if(q){var ye=L.elements[A-1];Ee.insertAfter(v,xr);for(var Ie=q,Ve=q,Ye=$,tt,Zt=0;Zt++,Ie=L.elements[--Ye],Ie!==v;){if(tt=Ee.indexOf(Ie),Zt>3&&tt!==-1&&(Ee.remove(Ie),tt=-1),tt===-1){L.removeElement(Ie);continue}var Ot=sn(ye.ownerDocument,tt);Ee.replace(Ie,Ot.elt,Ot.attrs),L.elements[Ye]=Ot.elt,Ie=Ot.elt,Ve===q&&(Ee.remove(xr),Ee.insertAfter(Ot.elt,xr)),Ie._appendChild(Ve),Ve=Ie}Lt&&we(ye,ne)?Dr(function(){return Ve}):ye instanceof o.HTMLTemplateElement?ye.content._appendChild(Ve):ye._appendChild(Ve);for(var mr=sn(q.ownerDocument,Ee.indexOf(v));q.hasChildNodes();)mr.elt._appendChild(q.firstChild);q._appendChild(mr.elt),Ee.remove(v),Ee.replace(xr,mr.elt,mr.attrs),L.removeElement(v);var Ai=L.elements.lastIndexOf(q);L.elements.splice(Ai+1,0,mr.elt)}else return L.popElement(v),Ee.remove(v),!0}return!0}function Ba(){L.pop(),j=ft}function jt(){delete Ne._parser,L.elements.length=0,Ne.defaultView&&Ne.defaultView.dispatchEvent(new o.Event("load",{}))}function ie(t,a){x=a,re--}function Te(t){switch(t){case 38:Et=Te,x=pr;break;case 60:if(Ha())break;x=Fa;break;case 0:W.push(t),Nt=!0;break;case-1:_e();break;default:lr(he)||W.push(t);break}}function Ct(t){switch(t){case 38:Et=Ct,x=pr;break;case 60:x=ja;break;case 0:W.push(65533),Nt=!0;break;case-1:_e();break;default:W.push(t);break}}function hr(t){switch(t){case 60:x=za;break;case 0:W.push(65533);break;case-1:_e();break;default:lr(ae)||W.push(t);break}}function Dt(t){switch(t){case 60:x=Ka;break;case 0:W.push(65533);break;case-1:_e();break;default:lr(ae)||W.push(t);break}}function on(t){switch(t){case 0:W.push(65533);break;case-1:_e();break;default:lr(me)||W.push(t);break}}function Fa(t){switch(t){case 33:x=Pn;break;case 47:x=Ua;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Aa(),ie(t,Hn);break;case 63:ie(t,Hr);break;default:W.push(60),ie(t,Te);break}}function Ua(t){switch(t){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:or(),ie(t,Hn);break;case 62:x=Te;break;case-1:W.push(60),W.push(47),_e();break;default:ie(t,Hr);break}}function Hn(t){switch(t){case 9:case 10:case 12:case 32:x=ot;break;case 47:x=xt;break;case 62:x=Te,mt();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:De+=String.fromCharCode(t+32);break;case 0:De+="\uFFFD";break;case-1:_e();break;default:De+=cr(H);break}}function ja(t){t===47?(dt(),x=Va):(W.push(60),ie(t,Ct))}function Va(t){switch(t){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:or(),ie(t,Ga);break;default:W.push(60),W.push(47),ie(t,Ct);break}}function Ga(t){switch(t){case 9:case 10:case 12:case 32:if(Xe(De)){x=ot;return}break;case 47:if(Xe(De)){x=xt;return}break;case 62:if(Xe(De)){x=Te,mt();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:De+=String.fromCharCode(t+32),Me.push(t);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:De+=String.fromCharCode(t),Me.push(t);return}W.push(60),W.push(47),f(W,Me),ie(t,Ct)}function za(t){t===47?(dt(),x=Za):(W.push(60),ie(t,hr))}function Za(t){switch(t){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:or(),ie(t,Wa);break;default:W.push(60),W.push(47),ie(t,hr);break}}function Wa(t){switch(t){case 9:case 10:case 12:case 32:if(Xe(De)){x=ot;return}break;case 47:if(Xe(De)){x=xt;return}break;case 62:if(Xe(De)){x=Te,mt();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:De+=String.fromCharCode(t+32),Me.push(t);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:De+=String.fromCharCode(t),Me.push(t);return}W.push(60),W.push(47),f(W,Me),ie(t,hr)}function Ka(t){switch(t){case 47:dt(),x=Xa;break;case 33:x=Ya,W.push(60),W.push(33);break;default:W.push(60),ie(t,Dt);break}}function Xa(t){switch(t){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:or(),ie(t,Qa);break;default:W.push(60),W.push(47),ie(t,Dt);break}}function Qa(t){switch(t){case 9:case 10:case 12:case 32:if(Xe(De)){x=ot;return}break;case 47:if(Xe(De)){x=xt;return}break;case 62:if(Xe(De)){x=Te,mt();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:De+=String.fromCharCode(t+32),Me.push(t);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:De+=String.fromCharCode(t),Me.push(t);return}W.push(60),W.push(47),f(W,Me),ie(t,Dt)}function Ya(t){t===45?(x=$a,W.push(45)):ie(t,Dt)}function $a(t){t===45?(x=qn,W.push(45)):ie(t,Dt)}function st(t){switch(t){case 45:x=Ja,W.push(45);break;case 60:x=cn;break;case 0:W.push(65533);break;case-1:_e();break;default:W.push(t);break}}function Ja(t){switch(t){case 45:x=qn,W.push(45);break;case 60:x=cn;break;case 0:x=st,W.push(65533);break;case-1:_e();break;default:x=st,W.push(t);break}}function qn(t){switch(t){case 45:W.push(45);break;case 60:x=cn;break;case 62:x=Dt,W.push(62);break;case 0:x=st,W.push(65533);break;case-1:_e();break;default:x=st,W.push(t);break}}function cn(t){switch(t){case 47:dt(),x=ei;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:dt(),W.push(60),ie(t,ri);break;default:W.push(60),ie(t,st);break}}function ei(t){switch(t){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:or(),ie(t,ti);break;default:W.push(60),W.push(47),ie(t,st);break}}function ti(t){switch(t){case 9:case 10:case 12:case 32:if(Xe(De)){x=ot;return}break;case 47:if(Xe(De)){x=xt;return}break;case 62:if(Xe(De)){x=Te,mt();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:De+=String.fromCharCode(t+32),Me.push(t);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:De+=String.fromCharCode(t),Me.push(t);return}W.push(60),W.push(47),f(W,Me),ie(t,st)}function ri(t){switch(t){case 9:case 10:case 12:case 32:case 47:case 62:Fe(Me)==="script"?x=Mt:x=st,W.push(t);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Me.push(t+32),W.push(t);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Me.push(t),W.push(t);break;default:ie(t,st);break}}function Mt(t){switch(t){case 45:x=ni,W.push(45);break;case 60:x=ln,W.push(60);break;case 0:W.push(65533);break;case-1:_e();break;default:W.push(t);break}}function ni(t){switch(t){case 45:x=ai,W.push(45);break;case 60:x=ln,W.push(60);break;case 0:x=Mt,W.push(65533);break;case-1:_e();break;default:x=Mt,W.push(t);break}}function ai(t){switch(t){case 45:W.push(45);break;case 60:x=ln,W.push(60);break;case 62:x=Dt,W.push(62);break;case 0:x=Mt,W.push(65533);break;case-1:_e();break;default:x=Mt,W.push(t);break}}function ln(t){t===47?(dt(),x=ii,W.push(47)):ie(t,Mt)}function ii(t){switch(t){case 9:case 10:case 12:case 32:case 47:case 62:Fe(Me)==="script"?x=st:x=Mt,W.push(t);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Me.push(t+32),W.push(t);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Me.push(t),W.push(t);break;default:ie(t,Mt);break}}function ot(t){switch(t){case 9:case 10:case 12:case 32:break;case 47:x=xt;break;case 62:x=Te,mt();break;case-1:_e();break;case 61:rn(),Ke+=String.fromCharCode(t),x=un;break;default:if(xa())break;rn(),ie(t,un);break}}function un(t){switch(t){case 9:case 10:case 12:case 32:case 47:case 62:case-1:ie(t,si);break;case 61:x=Rn;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ke+=String.fromCharCode(t+32);break;case 0:Ke+="\uFFFD";break;case 34:case 39:case 60:default:Ke+=cr(Z);break}}function si(t){switch(t){case 9:case 10:case 12:case 32:break;case 47:St(Ke),x=xt;break;case 61:x=Rn;break;case 62:x=Te,St(Ke),mt();break;case-1:St(Ke),_e();break;default:St(Ke),rn(),ie(t,un);break}}function Rn(t){switch(t){case 9:case 10:case 12:case 32:break;case 34:nn(),x=Ar;break;case 39:nn(),x=Or;break;case 62:default:nn(),ie(t,Ir);break}}function Ar(t){switch(t){case 34:St(Ke,We),x=fn;break;case 38:Et=Ar,x=pr;break;case 0:We+="\uFFFD";break;case-1:_e();break;case 10:We+=String.fromCharCode(t);break;default:We+=cr(bt);break}}function Or(t){switch(t){case 39:St(Ke,We),x=fn;break;case 38:Et=Or,x=pr;break;case 0:We+="\uFFFD";break;case-1:_e();break;case 10:We+=String.fromCharCode(t);break;default:We+=cr(Q);break}}function Ir(t){switch(t){case 9:case 10:case 12:case 32:St(Ke,We),x=ot;break;case 38:Et=Ir,x=pr;break;case 62:St(Ke,We),x=Te,mt();break;case 0:We+="\uFFFD";break;case-1:re--,x=Te;break;case 34:case 39:case 60:case 61:case 96:default:We+=cr(z);break}}function fn(t){switch(t){case 9:case 10:case 12:case 32:x=ot;break;case 47:x=xt;break;case 62:x=Te,mt();break;case-1:_e();break;default:ie(t,ot);break}}function xt(t){switch(t){case 62:x=Te,qa();break;case-1:_e();break;default:ie(t,ot);break}}function Hr(t,a,v){var A=a.length;v?re+=A-1:re+=A;var q=a.substring(0,A-1);q=q.replace(/\u0000/g,"\uFFFD"),q=q.replace(/\u000D\u000A/g,`
`),q=q.replace(/\u000D/g,`
`),Pe(O,q),x=Te}Hr.lookahead=">";function Pn(t,a,v){if(a[0]==="-"&&a[1]==="-"){re+=2,On(),x=oi;return}a.toUpperCase()==="DOCTYPE"?(re+=7,x=di):a==="[CDATA["&&Oa()?(re+=7,x=dn):x=Hr}Pn.lookahead=7;function oi(t){switch(On(),t){case 45:x=ci;break;case 62:x=Te,Pe(O,Fe(Ae));break;default:ie(t,Vt);break}}function ci(t){switch(t){case 45:x=qr;break;case 62:x=Te,Pe(O,Fe(Ae));break;case-1:Pe(O,Fe(Ae)),_e();break;default:Ae.push(45),ie(t,Vt);break}}function Vt(t){switch(t){case 60:Ae.push(t),x=li;break;case 45:x=hn;break;case 0:Ae.push(65533);break;case-1:Pe(O,Fe(Ae)),_e();break;default:Ae.push(t);break}}function li(t){switch(t){case 33:Ae.push(t),x=ui;break;case 60:Ae.push(t);break;default:ie(t,Vt);break}}function ui(t){switch(t){case 45:x=fi;break;default:ie(t,Vt);break}}function fi(t){switch(t){case 45:x=hi;break;default:ie(t,hn);break}}function hi(t){switch(t){case 62:case-1:ie(t,qr);break;default:ie(t,qr);break}}function hn(t){switch(t){case 45:x=qr;break;case-1:Pe(O,Fe(Ae)),_e();break;default:Ae.push(45),ie(t,Vt);break}}function qr(t){switch(t){case 62:x=Te,Pe(O,Fe(Ae));break;case 33:x=pi;break;case 45:Ae.push(45);break;case-1:Pe(O,Fe(Ae)),_e();break;default:Ae.push(45),Ae.push(45),ie(t,Vt);break}}function pi(t){switch(t){case 45:Ae.push(45),Ae.push(45),Ae.push(33),x=hn;break;case 62:x=Te,Pe(O,Fe(Ae));break;case-1:Pe(O,Fe(Ae)),_e();break;default:Ae.push(45),Ae.push(45),Ae.push(33),ie(t,Vt);break}}function di(t){switch(t){case 9:case 10:case 12:case 32:x=Bn;break;case-1:Xt(),ke(),Le(),_e();break;default:ie(t,Bn);break}}function Bn(t){switch(t){case 9:case 10:case 12:case 32:break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Xt(),Tt.push(t+32),x=pn;break;case 0:Xt(),Tt.push(65533),x=pn;break;case 62:Xt(),ke(),x=Te,Le();break;case-1:Xt(),ke(),Le(),_e();break;default:Xt(),Tt.push(t),x=pn;break}}function pn(t){switch(t){case 9:case 10:case 12:case 32:x=Fn;break;case 62:x=Te,Le();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Tt.push(t+32);break;case 0:Tt.push(65533);break;case-1:ke(),Le(),_e();break;default:Tt.push(t);break}}function Fn(t,a,v){switch(t){case 9:case 10:case 12:case 32:re+=1;break;case 62:x=Te,re+=1,Le();break;case-1:ke(),Le(),_e();break;default:a=a.toUpperCase(),a==="PUBLIC"?(re+=6,x=mi):a==="SYSTEM"?(re+=6,x=_i):(ke(),x=At);break}}Fn.lookahead=6;function mi(t){switch(t){case 9:case 10:case 12:case 32:x=gi;break;case 34:Lr(),x=Un;break;case 39:Lr(),x=jn;break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),_e();break;default:ke(),x=At;break}}function gi(t){switch(t){case 9:case 10:case 12:case 32:break;case 34:Lr(),x=Un;break;case 39:Lr(),x=jn;break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),_e();break;default:ke(),x=At;break}}function Un(t){switch(t){case 34:x=Vn;break;case 0:yt.push(65533);break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),_e();break;default:yt.push(t);break}}function jn(t){switch(t){case 39:x=Vn;break;case 0:yt.push(65533);break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),_e();break;default:yt.push(t);break}}function Vn(t){switch(t){case 9:case 10:case 12:case 32:x=bi;break;case 62:x=Te,Le();break;case 34:kt(),x=Rr;break;case 39:kt(),x=Pr;break;case-1:ke(),Le(),_e();break;default:ke(),x=At;break}}function bi(t){switch(t){case 9:case 10:case 12:case 32:break;case 62:x=Te,Le();break;case 34:kt(),x=Rr;break;case 39:kt(),x=Pr;break;case-1:ke(),Le(),_e();break;default:ke(),x=At;break}}function _i(t){switch(t){case 9:case 10:case 12:case 32:x=vi;break;case 34:kt(),x=Rr;break;case 39:kt(),x=Pr;break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),_e();break;default:ke(),x=At;break}}function vi(t){switch(t){case 9:case 10:case 12:case 32:break;case 34:kt(),x=Rr;break;case 39:kt(),x=Pr;break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),_e();break;default:ke(),x=At;break}}function Rr(t){switch(t){case 34:x=Gn;break;case 0:wt.push(65533);break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),_e();break;default:wt.push(t);break}}function Pr(t){switch(t){case 39:x=Gn;break;case 0:wt.push(65533);break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),_e();break;default:wt.push(t);break}}function Gn(t){switch(t){case 9:case 10:case 12:case 32:break;case 62:x=Te,Le();break;case-1:ke(),Le(),_e();break;default:x=At;break}}function At(t){switch(t){case 62:x=Te,Le();break;case-1:Le(),_e();break}}function dn(t){switch(t){case 93:x=Ei;break;case-1:_e();break;case 0:Nt=!0;default:lr(ce)||W.push(t);break}}function Ei(t){switch(t){case 93:x=Ti;break;default:W.push(93),ie(t,dn);break}}function Ti(t){switch(t){case 93:W.push(93);break;case 62:Qt(),x=Te;break;default:W.push(93),W.push(93),ie(t,dn);break}}function pr(t){switch(dt(),Me.push(38),t){case 9:case 10:case 12:case 32:case 60:case 38:case-1:ie(t,Gt);break;case 35:Me.push(t),x=yi;break;default:ie(t,zn);break}}function zn(t){xe.lastIndex=re;var a=xe.exec(te);if(!a)throw new Error("should never happen");var v=a[1];if(!v){x=Gt;return}switch(re+=v.length,f(Me,vr(v)),Et){case Ar:case Or:case Ir:if(v[v.length-1]!==";"&&/[=A-Za-z0-9]/.test(te[re])){x=Gt;return}break}dt();var A=de[v];typeof A=="number"?Me.push(A):f(Me,A),x=Gt}zn.lookahead=-32;function yi(t){switch(He=0,t){case 120:case 88:Me.push(t),x=wi;break;default:ie(t,Ni);break}}function wi(t){switch(t){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 65:case 66:case 67:case 68:case 69:case 70:case 97:case 98:case 99:case 100:case 101:case 102:ie(t,Si);break;default:ie(t,Gt);break}}function Ni(t){switch(t){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:ie(t,ki);break;default:ie(t,Gt);break}}function Si(t){switch(t){case 65:case 66:case 67:case 68:case 69:case 70:He*=16,He+=t-55;break;case 97:case 98:case 99:case 100:case 101:case 102:He*=16,He+=t-87;break;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:He*=16,He+=t-48;break;case 59:x=Br;break;default:ie(t,Br);break}}function ki(t){switch(t){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:He*=10,He+=t-48;break;case 59:x=Br;break;default:ie(t,Br);break}}function Br(t){He in X?He=X[He]:(He>1114111||He>=55296&&He<57344)&&(He=65533),dt(),He<=65535?Me.push(He):(He=He-65536,Me.push(55296+(He>>10)),Me.push(56320+(He&1023))),ie(t,Gt)}function Gt(t){switch(Et){case Ar:case Or:case Ir:We+=Fe(Me);break;default:f(W,Me);break}ie(t,Et)}function Li(t,a,v,A){switch(t){case 1:if(a=a.replace(Ue,""),a.length===0)return;break;case 4:Ne._appendChild(Ne.createComment(a));return;case 5:var q=a,$=v,fe=A;Ne.appendChild(new c(Ne,q,$,fe)),en||q.toLowerCase()!=="html"||J.test($)||fe&&fe.toLowerCase()===P||fe===void 0&&E.test($)?Ne._quirks=!0:(d.test($)||fe!==void 0&&E.test($))&&(Ne._limitedQuirks=!0),j=Zn;return}Ne._quirks=!0,j=Zn,j(t,a,v,A)}function Zn(t,a,v,A){var q;switch(t){case 1:if(a=a.replace(Ue,""),a.length===0)return;break;case 5:return;case 4:Ne._appendChild(Ne.createComment(a));return;case 2:if(a==="html"){q=ur(Ne,a,v),L.push(q),Ne.appendChild(q),j=Fr;return}break;case 3:switch(a){case"html":case"head":case"body":case"br":break;default:return}}q=ur(Ne,"html",null),L.push(q),Ne.appendChild(q),j=Fr,j(t,a,v,A)}function Fr(t,a,v,A){switch(t){case 1:if(a=a.replace(Ue,""),a.length===0)return;break;case 5:return;case 4:at(a);return;case 2:switch(a){case"html":ue(t,a,v,A);return;case"head":var q=pe(a,v);wr=q,j=Re;return}break;case 3:switch(a){case"html":case"head":case"body":case"br":break;default:return}}Fr(p,"head",null),j(t,a,v,A)}function Re(t,a,v,A){switch(t){case 1:var q=a.match(Ue);if(q&&(it(q[0]),a=a.substring(q[0].length)),a.length===0)return;break;case 4:at(a);return;case 5:return;case 2:switch(a){case"html":ue(t,a,v,A);return;case"meta":case"base":case"basefont":case"bgsound":case"link":pe(a,v),L.pop();return;case"title":Ra(a,v);return;case"noscript":if(!Nr){pe(a,v),j=Wn;return}case"noframes":case"style":Mr(a,v);return;case"script":Cr(function($){var fe=ur($,a,v);return fe._parser_inserted=!0,fe._force_async=!1,Ft&&(fe._already_started=!0),Qt(),fe}),x=Dt,ft=j,j=Ur;return;case"template":pe(a,v),Ee.insertMarker(),qe=!1,j=bn,ht.push(j);return;case"head":return}break;case 3:switch(a){case"head":L.pop(),j=mn;return;case"body":case"html":case"br":break;case"template":if(!L.contains("template"))return;L.generateImpliedEndTags(null,"thorough"),L.popTag("template"),Ee.clearToMarker(),ht.pop(),fr();return;default:return}break}Re(T,"head",null),j(t,a,v,A)}function Wn(t,a,v,A){switch(t){case 5:return;case 4:Re(t,a);return;case 1:var q=a.match(Ue);if(q&&(Re(t,q[0]),a=a.substring(q[0].length)),a.length===0)return;break;case 2:switch(a){case"html":ue(t,a,v,A);return;case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"style":Re(t,a,v);return;case"head":case"noscript":return}break;case 3:switch(a){case"noscript":L.pop(),j=Re;return;case"br":break;default:return}break}Wn(T,"noscript",null),j(t,a,v,A)}function mn(t,a,v,A){switch(t){case 1:var q=a.match(Ue);if(q&&(it(q[0]),a=a.substring(q[0].length)),a.length===0)return;break;case 4:at(a);return;case 5:return;case 2:switch(a){case"html":ue(t,a,v,A);return;case"body":pe(a,v),qe=!1,j=ue;return;case"frameset":pe(a,v),j=_n;return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":L.push(wr),Re(p,a,v),L.removeElement(wr);return;case"head":return}break;case 3:switch(a){case"template":return Re(t,a,v,A);case"body":case"html":case"br":break;default:return}break}mn(p,"body",null),qe=!0,j(t,a,v,A)}function ue(t,a,v,A){var q,$,fe,ye;switch(t){case 1:if(Nt&&(a=a.replace(Je,""),a.length===0))return;qe&&Se.test(a)&&(qe=!1),ze(),it(a);return;case 5:return;case 4:at(a);return;case-1:if(ht.length)return bn(t);jt();return;case 2:switch(a){case"html":if(L.contains("template"))return;ir(v,L.elements[0]);return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":Re(p,a,v);return;case"body":if(q=L.elements[1],!q||!(q instanceof o.HTMLBodyElement)||L.contains("template"))return;qe=!1,ir(v,q);return;case"frameset":if(!qe||(q=L.elements[1],!q||!(q instanceof o.HTMLBodyElement)))return;for(q.parentNode&&q.parentNode.removeChild(q);!(L.top instanceof o.HTMLHtmlElement);)L.pop();pe(a,v),j=_n;return;case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"nav":case"ol":case"p":case"section":case"summary":case"ul":L.inButtonScope("p")&&ue(T,"p"),pe(a,v);return;case"menu":L.inButtonScope("p")&&ue(T,"p"),we(L.top,"menuitem")&&L.pop(),pe(a,v);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":L.inButtonScope("p")&&ue(T,"p"),L.top instanceof o.HTMLHeadingElement&&L.pop(),pe(a,v);return;case"pre":case"listing":L.inButtonScope("p")&&ue(T,"p"),pe(a,v),Ut=!0,qe=!1;return;case"form":if(pt&&!L.contains("template"))return;L.inButtonScope("p")&&ue(T,"p"),ye=pe(a,v),L.contains("template")||(pt=ye);return;case"li":for(qe=!1,$=L.elements.length-1;$>=0;$--){if(fe=L.elements[$],fe instanceof o.HTMLLIElement){ue(T,"li");break}if(we(fe,w)&&!we(fe,m))break}L.inButtonScope("p")&&ue(T,"p"),pe(a,v);return;case"dd":case"dt":for(qe=!1,$=L.elements.length-1;$>=0;$--){if(fe=L.elements[$],we(fe,se)){ue(T,fe.localName);break}if(we(fe,w)&&!we(fe,m))break}L.inButtonScope("p")&&ue(T,"p"),pe(a,v);return;case"plaintext":L.inButtonScope("p")&&ue(T,"p"),pe(a,v),x=on;return;case"button":L.inScope("button")?(ue(T,"button"),j(t,a,v,A)):(ze(),pe(a,v),qe=!1);return;case"a":var Ie=Ee.findElementByTag("a");Ie&&(ue(T,a),Ee.remove(Ie),L.removeElement(Ie));case"b":case"big":case"code":case"em":case"font":case"i":case"s":case"small":case"strike":case"strong":case"tt":case"u":ze(),Ee.push(pe(a,v),v);return;case"nobr":ze(),L.inScope(a)&&(ue(T,a),ze()),Ee.push(pe(a,v),v);return;case"applet":case"marquee":case"object":ze(),pe(a,v),Ee.insertMarker(),qe=!1;return;case"table":!Ne._quirks&&L.inButtonScope("p")&&ue(T,"p"),pe(a,v),qe=!1,j=Qe;return;case"area":case"br":case"embed":case"img":case"keygen":case"wbr":ze(),pe(a,v),L.pop(),qe=!1;return;case"input":ze(),ye=pe(a,v),L.pop();var Ve=ye.getAttribute("type");(!Ve||Ve.toLowerCase()!=="hidden")&&(qe=!1);return;case"param":case"source":case"track":pe(a,v),L.pop();return;case"hr":L.inButtonScope("p")&&ue(T,"p"),we(L.top,"menuitem")&&L.pop(),pe(a,v),L.pop(),qe=!1;return;case"image":ue(p,"img",v,A);return;case"textarea":pe(a,v),Ut=!0,qe=!1,x=Ct,ft=j,j=Ur;return;case"xmp":L.inButtonScope("p")&&ue(T,"p"),ze(),qe=!1,Mr(a,v);return;case"iframe":qe=!1,Mr(a,v);return;case"noembed":Mr(a,v);return;case"select":ze(),pe(a,v),qe=!1,j===Qe||j===gn||j===zt||j===dr||j===Yt?j=Vr:j=gt;return;case"optgroup":case"option":L.top instanceof o.HTMLOptionElement&&ue(T,"option"),ze(),pe(a,v);return;case"menuitem":we(L.top,"menuitem")&&L.pop(),ze(),pe(a,v);return;case"rb":case"rtc":L.inScope("ruby")&&L.generateImpliedEndTags(),pe(a,v);return;case"rp":case"rt":L.inScope("ruby")&&L.generateImpliedEndTags("rtc"),pe(a,v);return;case"math":ze(),qt(v),Kt(v),an(a,v,i.MATHML),A&&L.pop();return;case"svg":ze(),ar(v),Kt(v),an(a,v,i.SVG),A&&L.pop();return;case"caption":case"col":case"colgroup":case"frame":case"head":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}ze(),pe(a,v);return;case 3:switch(a){case"template":Re(T,a,v);return;case"body":if(!L.inScope("body"))return;j=Kn;return;case"html":if(!L.inScope("body"))return;j=Kn,j(t,a,v);return;case"address":case"article":case"aside":case"blockquote":case"button":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"listing":case"main":case"menu":case"nav":case"ol":case"pre":case"section":case"summary":case"ul":if(!L.inScope(a))return;L.generateImpliedEndTags(),L.popTag(a);return;case"form":if(L.contains("template")){if(!L.inScope("form"))return;L.generateImpliedEndTags(),L.popTag("form")}else{var Ye=pt;if(pt=null,!Ye||!L.elementInScope(Ye))return;L.generateImpliedEndTags(),L.removeElement(Ye)}return;case"p":L.inButtonScope(a)?(L.generateImpliedEndTags(a),L.popTag(a)):(ue(p,a,null),j(t,a,v,A));return;case"li":if(!L.inListItemScope(a))return;L.generateImpliedEndTags(a),L.popTag(a);return;case"dd":case"dt":if(!L.inScope(a))return;L.generateImpliedEndTags(a),L.popTag(a);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":if(!L.elementTypeInScope(o.HTMLHeadingElement))return;L.generateImpliedEndTags(),L.popElementType(o.HTMLHeadingElement);return;case"sarcasm":break;case"a":case"b":case"big":case"code":case"em":case"font":case"i":case"nobr":case"s":case"small":case"strike":case"strong":case"tt":case"u":var tt=Pa(a);if(tt)return;break;case"applet":case"marquee":case"object":if(!L.inScope(a))return;L.generateImpliedEndTags(),L.popTag(a),Ee.clearToMarker();return;case"br":ue(p,a,null);return}for($=L.elements.length-1;$>=0;$--)if(fe=L.elements[$],we(fe,a)){L.generateImpliedEndTags(a),L.popElement(fe);break}else if(we(fe,w))return;return}}function Ur(t,a,v,A){switch(t){case 1:it(a);return;case-1:L.top instanceof o.HTMLScriptElement&&(L.top._already_started=!0),L.pop(),j=ft,j(t);return;case 3:a==="script"?Ba():(L.pop(),j=ft);return;default:return}}function Qe(t,a,v,A){function q(fe){for(var ye=0,Ie=fe.length;ye<Ie;ye++)if(fe[ye][0]==="type")return fe[ye][1].toLowerCase();return null}switch(t){case 1:if(tn){ue(t,a,v,A);return}else if(we(L.top,ne)){Sr=[],ft=j,j=Ci,j(t,a,v,A);return}break;case 4:at(a);return;case 5:return;case 2:switch(a){case"caption":L.clearToContext(D),Ee.insertMarker(),pe(a,v),j=gn;return;case"colgroup":L.clearToContext(D),pe(a,v),j=jr;return;case"col":Qe(p,"colgroup",null),j(t,a,v,A);return;case"tbody":case"tfoot":case"thead":L.clearToContext(D),pe(a,v),j=zt;return;case"td":case"th":case"tr":Qe(p,"tbody",null),j(t,a,v,A);return;case"table":if(!L.inTableScope(a))return;Qe(T,a),j(t,a,v,A);return;case"style":case"script":case"template":Re(t,a,v,A);return;case"input":var $=q(v);if($!=="hidden")break;pe(a,v),L.pop();return;case"form":if(pt||L.contains("template"))return;pt=pe(a,v),L.popElement(pt);return}break;case 3:switch(a){case"table":if(!L.inTableScope(a))return;L.popTag(a),fr();return;case"body":case"caption":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return;case"template":Re(t,a,v,A);return}break;case-1:ue(t,a,v,A);return}Lt=!0,ue(t,a,v,A),Lt=!1}function Ci(t,a,v,A){if(t===b){if(Nt&&(a=a.replace(Je,""),a.length===0))return;Sr.push(a)}else{var q=Sr.join("");Sr.length=0,Se.test(q)?(Lt=!0,ue(b,q),Lt=!1):it(q),j=ft,j(t,a,v,A)}}function gn(t,a,v,A){function q(){return L.inTableScope("caption")?(L.generateImpliedEndTags(),L.popTag("caption"),Ee.clearToMarker(),j=Qe,!0):!1}switch(t){case 2:switch(a){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":q()&&j(t,a,v,A);return}break;case 3:switch(a){case"caption":q();return;case"table":q()&&j(t,a,v,A);return;case"body":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}break}ue(t,a,v,A)}function jr(t,a,v,A){switch(t){case 1:var q=a.match(Ue);if(q&&(it(q[0]),a=a.substring(q[0].length)),a.length===0)return;break;case 4:at(a);return;case 5:return;case 2:switch(a){case"html":ue(t,a,v,A);return;case"col":pe(a,v),L.pop();return;case"template":Re(t,a,v,A);return}break;case 3:switch(a){case"colgroup":if(!we(L.top,"colgroup"))return;L.pop(),j=Qe;return;case"col":return;case"template":Re(t,a,v,A);return}break;case-1:ue(t,a,v,A);return}we(L.top,"colgroup")&&(jr(T,"colgroup"),j(t,a,v,A))}function zt(t,a,v,A){function q(){!L.inTableScope("tbody")&&!L.inTableScope("thead")&&!L.inTableScope("tfoot")||(L.clearToContext(B),zt(T,L.top.localName,null),j(t,a,v,A))}switch(t){case 2:switch(a){case"tr":L.clearToContext(B),pe(a,v),j=dr;return;case"th":case"td":zt(p,"tr",null),j(t,a,v,A);return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":q();return}break;case 3:switch(a){case"table":q();return;case"tbody":case"tfoot":case"thead":L.inTableScope(a)&&(L.clearToContext(B),L.pop(),j=Qe);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":case"tr":return}break}Qe(t,a,v,A)}function dr(t,a,v,A){function q(){return L.inTableScope("tr")?(L.clearToContext(K),L.pop(),j=zt,!0):!1}switch(t){case 2:switch(a){case"th":case"td":L.clearToContext(K),pe(a,v),j=Yt,Ee.insertMarker();return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":case"tr":q()&&j(t,a,v,A);return}break;case 3:switch(a){case"tr":q();return;case"table":q()&&j(t,a,v,A);return;case"tbody":case"tfoot":case"thead":L.inTableScope(a)&&q()&&j(t,a,v,A);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":return}break}Qe(t,a,v,A)}function Yt(t,a,v,A){switch(t){case 2:switch(a){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":L.inTableScope("td")?(Yt(T,"td"),j(t,a,v,A)):L.inTableScope("th")&&(Yt(T,"th"),j(t,a,v,A));return}break;case 3:switch(a){case"td":case"th":if(!L.inTableScope(a))return;L.generateImpliedEndTags(),L.popTag(a),Ee.clearToMarker(),j=dr;return;case"body":case"caption":case"col":case"colgroup":case"html":return;case"table":case"tbody":case"tfoot":case"thead":case"tr":if(!L.inTableScope(a))return;Yt(T,L.inTableScope("td")?"td":"th"),j(t,a,v,A);return}break}ue(t,a,v,A)}function gt(t,a,v,A){switch(t){case 1:if(Nt&&(a=a.replace(Je,""),a.length===0))return;it(a);return;case 4:at(a);return;case 5:return;case-1:ue(t,a,v,A);return;case 2:switch(a){case"html":ue(t,a,v,A);return;case"option":L.top instanceof o.HTMLOptionElement&&gt(T,a),pe(a,v);return;case"optgroup":L.top instanceof o.HTMLOptionElement&&gt(T,"option"),L.top instanceof o.HTMLOptGroupElement&&gt(T,a),pe(a,v);return;case"select":gt(T,a);return;case"input":case"keygen":case"textarea":if(!L.inSelectScope("select"))return;gt(T,"select"),j(t,a,v,A);return;case"script":case"template":Re(t,a,v,A);return}break;case 3:switch(a){case"optgroup":L.top instanceof o.HTMLOptionElement&&L.elements[L.elements.length-2]instanceof o.HTMLOptGroupElement&&gt(T,"option"),L.top instanceof o.HTMLOptGroupElement&&L.pop();return;case"option":L.top instanceof o.HTMLOptionElement&&L.pop();return;case"select":if(!L.inSelectScope(a))return;L.popTag(a),fr();return;case"template":Re(t,a,v,A);return}break}}function Vr(t,a,v,A){switch(a){case"caption":case"table":case"tbody":case"tfoot":case"thead":case"tr":case"td":case"th":switch(t){case 2:Vr(T,"select"),j(t,a,v,A);return;case 3:L.inTableScope(a)&&(Vr(T,"select"),j(t,a,v,A));return}}gt(t,a,v,A)}function bn(t,a,v,A){function q($){j=$,ht[ht.length-1]=j,j(t,a,v,A)}switch(t){case 1:case 4:case 5:ue(t,a,v,A);return;case-1:L.contains("template")?(L.popTag("template"),Ee.clearToMarker(),ht.pop(),fr(),j(t,a,v,A)):jt();return;case 2:switch(a){case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":Re(t,a,v,A);return;case"caption":case"colgroup":case"tbody":case"tfoot":case"thead":q(Qe);return;case"col":q(jr);return;case"tr":q(zt);return;case"td":case"th":q(dr);return}q(ue);return;case 3:switch(a){case"template":Re(t,a,v,A);return;default:return}}}function Kn(t,a,v,A){switch(t){case 1:if(Se.test(a))break;ue(t,a);return;case 4:L.elements[0]._appendChild(Ne.createComment(a));return;case 5:return;case-1:jt();return;case 2:if(a==="html"){ue(t,a,v,A);return}break;case 3:if(a==="html"){if(Ft)return;j=Mi;return}break}j=ue,j(t,a,v,A)}function _n(t,a,v,A){switch(t){case 1:a=a.replace(Ce,""),a.length>0&&it(a);return;case 4:at(a);return;case 5:return;case-1:jt();return;case 2:switch(a){case"html":ue(t,a,v,A);return;case"frameset":pe(a,v);return;case"frame":pe(a,v),L.pop();return;case"noframes":Re(t,a,v,A);return}break;case 3:if(a==="frameset"){if(Ft&&L.top instanceof o.HTMLHtmlElement)return;L.pop(),!Ft&&!(L.top instanceof o.HTMLFrameSetElement)&&(j=Di);return}break}}function Di(t,a,v,A){switch(t){case 1:a=a.replace(Ce,""),a.length>0&&it(a);return;case 4:at(a);return;case 5:return;case-1:jt();return;case 2:switch(a){case"html":ue(t,a,v,A);return;case"noframes":Re(t,a,v,A);return}break;case 3:if(a==="html"){j=xi;return}break}}function Mi(t,a,v,A){switch(t){case 1:if(Se.test(a))break;ue(t,a,v,A);return;case 4:Ne._appendChild(Ne.createComment(a));return;case 5:ue(t,a,v,A);return;case-1:jt();return;case 2:if(a==="html"){ue(t,a,v,A);return}break}j=ue,j(t,a,v,A)}function xi(t,a,v,A){switch(t){case 1:a=a.replace(Ce,""),a.length>0&&ue(t,a,v,A);return;case 4:Ne._appendChild(Ne.createComment(a));return;case 5:ue(t,a,v,A);return;case-1:jt();return;case 2:switch(a){case"html":ue(t,a,v,A);return;case"noframes":Re(t,a,v,A);return}break}}function Xn(t,a,v,A){function q(Ie){for(var Ve=0,Ye=Ie.length;Ve<Ye;Ve++)switch(Ie[Ve][0]){case"color":case"face":case"size":return!0}return!1}var $;switch(t){case 1:qe&&nt.test(a)&&(qe=!1),Nt&&(a=a.replace(Je,"\uFFFD")),it(a);return;case 4:at(a);return;case 5:return;case 2:switch(a){case"font":if(!q(v))break;case"b":case"big":case"blockquote":case"body":case"br":case"center":case"code":case"dd":case"div":case"dl":case"dt":case"em":case"embed":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":case"head":case"hr":case"i":case"img":case"li":case"listing":case"menu":case"meta":case"nobr":case"ol":case"p":case"pre":case"ruby":case"s":case"small":case"span":case"strong":case"strike":case"sub":case"sup":case"table":case"tt":case"u":case"ul":case"var":if(Ft)break;do L.pop(),$=L.top;while($.namespaceURI!==i.HTML&&!Wt($)&&!nr($));Pe(t,a,v,A);return}$=L.elements.length===1&&Ft?U:L.top,$.namespaceURI===i.MATHML?qt(v):$.namespaceURI===i.SVG&&(a=_t(a),ar(v)),Kt(v),an(a,v,$.namespaceURI),A&&L.pop();return;case 3:if($=L.top,a==="script"&&$.namespaceURI===i.SVG&&$.localName==="script")L.pop();else for(var fe=L.elements.length-1,ye=L.elements[fe];;){if(ye.localName.toLowerCase()===a){L.popElement(ye);break}if(ye=L.elements[--fe],ye.namespaceURI===i.HTML){j(t,a,v,A);break}}return}}return kr.testTokenizer=function(t,a,v,A){var q=[];switch(a){case"PCDATA state":x=Te;break;case"RCDATA state":x=Ct;break;case"RAWTEXT state":x=hr;break;case"PLAINTEXT state":x=on;break}if(v&&(Tr=v),Pe=function(fe,ye,Ie,Ve){switch(Qt(),fe){case 1:q.length>0&&q[q.length-1][0]==="Character"?q[q.length-1][1]+=ye:q.push(["Character",ye]);break;case 4:q.push(["Comment",ye]);break;case 5:q.push(["DOCTYPE",ye,Ie===void 0?null:Ie,Ve===void 0?null:Ve,!en]);break;case 2:for(var Ye=Object.create(null),tt=0;tt<Ie.length;tt++){var Zt=Ie[tt];Zt.length===1?Ye[Zt[0]]="":Ye[Zt[0]]=Zt[1]}var Ot=["StartTag",ye,Ye];Ve&&Ot.push(!0),q.push(Ot);break;case 3:q.push(["EndTag",ye]);break}},!A)this.parse(t,!0);else{for(var $=0;$<t.length;$++)this.parse(t[$]);this.parse("",!0)}return q},kr}}}),Jr=le({"external/npm/node_modules/domino/lib/DOMImplementation.js"(_,k){k.exports=o;var h=Cn(),c=Dn(),n=Mn(),i=Be(),u=yn();function o(s){this.contextObject=s}var f={xml:{"":!0,"1.0":!0,"2.0":!0},core:{"":!0,"2.0":!0},html:{"":!0,"1.0":!0,"2.0":!0},xhtml:{"":!0,"1.0":!0,"2.0":!0}};o.prototype={hasFeature:function(b,p){var T=f[(b||"").toLowerCase()];return T&&T[p||""]||!1},createDocumentType:function(b,p,T){return u.isValidQName(b)||i.InvalidCharacterError(),new c(this.contextObject,b,p,T)},createDocument:function(b,p,T){var O=new h(!1,null),R;return p?R=O.createElementNS(b,p):R=null,T&&O.appendChild(T),R&&O.appendChild(R),b===i.NAMESPACE.HTML?O._contentType="application/xhtml+xml":b===i.NAMESPACE.SVG?O._contentType="image/svg+xml":O._contentType="application/xml",O},createHTMLDocument:function(b){var p=new h(!0,null);p.appendChild(new c(p,"html"));var T=p.createElement("html");p.appendChild(T);var O=p.createElement("head");if(T.appendChild(O),b!==void 0){var R=p.createElement("title");O.appendChild(R),R.appendChild(p.createTextNode(b))}return T.appendChild(p.createElement("body")),p.modclock=1,p},mozSetOutputMutationHandler:function(s,b){s.mutationHandler=b},mozGetInputMutationHandler:function(s){i.nyi()},mozHTMLParser:n}}}),gs=le({"external/npm/node_modules/domino/lib/Location.js"(_,k){var h=Sn(),c=ka();k.exports=n;function n(i,u){this._window=i,this._href=u}n.prototype=Object.create(c.prototype,{constructor:{value:n},href:{get:function(){return this._href},set:function(i){this.assign(i)}},assign:{value:function(i){var u=new h(this._href),o=u.resolve(i);this._href=o}},replace:{value:function(i){this.assign(i)}},reload:{value:function(){this.assign(this.href)}},toString:{value:function(){return this.href}}})}}),bs=le({"external/npm/node_modules/domino/lib/NavigatorID.js"(_,k){var h=Object.create(null,{appCodeName:{value:"Mozilla"},appName:{value:"Netscape"},appVersion:{value:"4.0"},platform:{value:""},product:{value:"Gecko"},productSub:{value:"20100101"},userAgent:{value:""},vendor:{value:""},vendorSub:{value:""},taintEnabled:{value:function(){return!1}}});k.exports=h}}),_s=le({"external/npm/node_modules/domino/lib/WindowTimers.js"(_,k){var h={setTimeout,clearTimeout,setInterval,clearInterval};k.exports=h}}),Da=le({"external/npm/node_modules/domino/lib/impl.js"(_,k){var h=Be();_=k.exports={CSSStyleDeclaration:kn(),CharacterData:Yr(),Comment:Ta(),DOMImplementation:Jr(),DOMTokenList:ga(),Document:Cn(),DocumentFragment:ya(),DocumentType:Dn(),Element:_r(),HTMLParser:Mn(),NamedNodeMap:_a(),Node:Ge(),NodeList:rr(),NodeFilter:$r(),ProcessingInstruction:wa(),Text:Ea(),Window:Ma()},h.merge(_,Sa()),h.merge(_,Ln().elements),h.merge(_,Ca().elements)}}),Ma=le({"external/npm/node_modules/domino/lib/Window.js"(_,k){var h=Jr(),c=ha(),n=gs(),i=Be();k.exports=u;function u(o){this.document=o||new h(null).createHTMLDocument(""),this.document._scripting_enabled=!0,this.document.defaultView=this,this.location=new n(this,this.document._address||"about:blank")}u.prototype=Object.create(c.prototype,{console:{value:console},history:{value:{back:i.nyi,forward:i.nyi,go:i.nyi}},navigator:{value:bs()},window:{get:function(){return this}},self:{get:function(){return this}},frames:{get:function(){return this}},parent:{get:function(){return this}},top:{get:function(){return this}},length:{value:0},frameElement:{value:null},opener:{value:null},onload:{get:function(){return this._getEventHandler("load")},set:function(o){this._setEventHandler("load",o)}},getComputedStyle:{value:function(f){return f.style}}}),i.expose(_s(),u),i.expose(Da(),u)}}),vs=le({"external/npm/node_modules/domino/lib/index.js"(_){var k=Jr(),h=Mn();Ma();var c=Da();_.createDOMImplementation=function(){return new k(null)},_.createDocument=function(n,i){if(n||i){var u=new h;return u.parse(n||"",!0),u.document()}return new k(null).createHTMLDocument("")},_.createIncrementalHTMLParser=function(){var n=new h;return{write:function(i){i.length>0&&n.parse(i,!1,function(){return!0})},end:function(i){n.parse(i||"",!0,function(){return!0})},process:function(i){return n.parse("",!1,i)},document:function(){return n.document()}}},_.createWindow=function(n,i){var u=_.createDocument(n);return i!==void 0&&(u._address=i),new c.Window(u)},_.impl=c}}),la=vs();function Es(){Object.assign(globalThis,la.impl),globalThis.KeyboardEvent=la.impl.Event}Es();
