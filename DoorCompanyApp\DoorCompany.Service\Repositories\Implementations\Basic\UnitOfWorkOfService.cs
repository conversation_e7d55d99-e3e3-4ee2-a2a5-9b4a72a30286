﻿using DoorCompany.Data.Context;
using DoorCompany.Data.Models;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Implementations.Basic
{
    public class UnitOfWorkOfService(ApplicationDbContext context) : IUnitOfWorkOfService
    {
        private readonly ApplicationDbContext _context = context;
        private IDbContextTransaction? _transaction;



        // Company Management
        private IGenericRepository<Company>? _companies;

        // Action Management
        private IGenericRepository<ActionType>? _actionTypes;
        private IGenericRepository<MainAction>? _mainActions;

        // Partner Management
        private IGenericRepository<Partner>? _partners;
        private IGenericRepository<PartnerTransation>? _partnerTransations;
        private IGenericRepository<PartnerBand>? _partnerBand;
      
        //User Management
        private IGenericRepository<User>? _user;
        private IGenericRepository<Role>? _role;
        private IGenericRepository<Permission>? _permission;
        private IGenericRepository<RolePermission>? _rolePermission;
        private IGenericRepository<UserRole>? _userRole;
        private IGenericRepository<RefreshToken>? _refreshToken;

        //ShareDistribution Management
        private IGenericRepository<ShareDistribution>? _shareDistribution;
        private IGenericRepository<ShareTransfer>? _shareTransfer;
        private IGenericRepository<ShareHistory>? _shareHistorie;

        private IGenericRepository<FinancialTransaction>? _financialTransaction;
        private IGenericRepository<Product>? _product;
        private IGenericRepository<ItemCategory>? _itemCategory;
        private IGenericRepository<Unit>? _unit;
        private IGenericRepository<InventoryTransaction>? _inventoryTransaction;
        private IGenericRepository<Party>? _partie;
        private IGenericRepository<Customer>? _Customer;
        private IGenericRepository<Supplier>? _suppler;
        private IGenericRepository<ProductionUnit>? _productUnite;
        private IGenericRepository<InvoiceMaster>? _invoiceMaster;
        private IGenericRepository<InvoiceItem>? _invoiceItem;
        private IGenericRepository<ProductInventory>? _productInventory;

        // Company Management
        public IGenericRepository<Company> Companies => _companies ??= new GenericRepository<Company>(_context);

        // Action Management
        public IGenericRepository<ActionType> ActionTypes => _actionTypes ??= new GenericRepository<ActionType>(_context);
        public IGenericRepository<MainAction> MainActions => _mainActions ??= new GenericRepository<MainAction>(_context);



        // Partner Management
        public IGenericRepository<Partner> Partners => _partners ??= new GenericRepository<Partner>(_context);
        public IGenericRepository<PartnerTransation> PartnerTransations => _partnerTransations ??= new GenericRepository<PartnerTransation>(_context);
        public IGenericRepository<PartnerBand> PartnerBands => _partnerBand ??= new GenericRepository<PartnerBand>(_context);



        //User Management
        public IGenericRepository<User> Users => _user ??= new GenericRepository<User>(_context);

        public IGenericRepository<Role> Roles => _role ??= new GenericRepository<Role>(_context);

        public IGenericRepository<Permission> Permissions => _permission ??= new GenericRepository<Permission>(_context);

        public IGenericRepository<RolePermission> RolePermissions => _rolePermission ??= new GenericRepository<RolePermission>(_context);

        public IGenericRepository<UserRole> UserRoles => _userRole ??= new GenericRepository<UserRole>(_context);

        public IGenericRepository<RefreshToken> RefreshTokens => _refreshToken ??= new GenericRepository<RefreshToken>(_context);


        //ShareDistribution Management
        public IGenericRepository<ShareDistribution> ShareDistributions => _shareDistribution ??= new GenericRepository<ShareDistribution>(_context);
        public IGenericRepository<ShareTransfer> ShareTransfers => _shareTransfer ??= new GenericRepository<ShareTransfer>(_context);
        public IGenericRepository<ShareHistory> ShareHistories => _shareHistorie ??= new GenericRepository<ShareHistory>(_context);

        public IGenericRepository<FinancialTransaction> FinancialTransactions => _financialTransaction ??= new GenericRepository<FinancialTransaction>(_context);

        public IGenericRepository<Product> Products => _product ??= new GenericRepository<Product>(_context);

        public IGenericRepository<ItemCategory> ItemCategories => _itemCategory ??= new GenericRepository<ItemCategory>(_context);
        public IGenericRepository<Unit> Units => _unit ??= new GenericRepository<Unit>(_context);

        public IGenericRepository<InventoryTransaction> InventoryTransactions => _inventoryTransaction ??= new GenericRepository<InventoryTransaction>(_context);

        public IGenericRepository<Party> Parties => _partie ??= new GenericRepository<Party>(_context);

        public IGenericRepository<Customer> Customers => _Customer ??= new GenericRepository<Customer>(_context);

        public IGenericRepository<Supplier> Suppliers => _suppler ??= new GenericRepository<Supplier>(_context);

        public IGenericRepository<ProductionUnit> ProductionUnits => _productUnite ??= new GenericRepository<ProductionUnit>(_context);

        public IGenericRepository<InvoiceMaster> InvoiceMasters => _invoiceMaster ??= new GenericRepository<InvoiceMaster>(_context);

        public IGenericRepository<InvoiceItem> InvoiceItems => _invoiceItem ??= new GenericRepository<InvoiceItem>(_context);

        public IGenericRepository<ProductInventory> ProductInventorys => _productInventory ??= new GenericRepository<ProductInventory>(_context);

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }
        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }




    }
}
