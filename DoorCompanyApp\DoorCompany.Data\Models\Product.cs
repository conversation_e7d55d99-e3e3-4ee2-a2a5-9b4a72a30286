﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Data.Models
{
    public class Product : BaseName
    {
        public string Code { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty;
        public int CategoryId { get; set; }
        public int UnitId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal StandardCost { get; set; }//التكلفة القياسية

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinimumStock { get; set; }//الحد الأدنى للمخزون

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaximumStock { get; set; }// الحد الأقصى للمخزون

        public int? SortOrder { get; set; } = 1;
        public int? ItemType { get; set; } = 1;
        public string? ImagePath { get; set; } = string.Empty;


        [ForeignKey(nameof(CategoryId))]
        public virtual ItemCategory ItemCategory { get; set; } = null!;

        [ForeignKey(nameof(UnitId))]
        public virtual Unit ItemUnit { get; set; } = null!;
        public ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
    }
    public class ItemCategory : BaseName
    {
        public int? ParentCategoryId { get; set; }
        public string? Code { get; set; }
        public string? Symbol { get; set; }
        public int? CategoryTypeId { get; set; } //item,Product,RawMaterial,Component,FinishedProduct,Packaging
        public string? ImageUrl { get; set; }
        public int? SortOrder { get; set; }

        [ForeignKey(nameof(ParentCategoryId))]
        public ItemCategory? ParentCategory { get; set; }
        public ICollection<ItemCategory>? ChildCategories { get; set; }
    }
    public class Unit : BaseName
    {
        public string? Symbol { get; set; }
    }
    public class InventoryTransaction : BaseEntity
    {
        public InventoryTransactionType Type { get; set; }
        public DateTime TransactionDate { get; set; }
        public int ProductId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } // تكلفة الوحدة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; } // إجمالي التكلفة
        [Column(TypeName = "decimal(18,2)")]
        public decimal BalanceAfter { get; set; } // الرصيد بعد الحركة
        [Column(TypeName = "decimal(18,2)")]
        public decimal AverageCost { get; set; } // متوسط التكلفة بعد الحركة
        public string? ReferenceNumber { get; set; }
        public string? Description { get; set; }

        [ForeignKey(nameof(ProductId))]
        public virtual Product Product { get; set; } = null!;
    }
    public enum InventoryTransactionType
    {
        [Description("مشتريات")]
        Purchase = 1,

        [Description("مبيعات")]
        Sale = 2,

        [Description("إدخال إنتاج")]
        ProductionInput = 3,

        [Description("إخراج إنتاج")]
        ProductionOutput = 4,

        [Description("تسوية مخزون")]
        Adjustment = 5,

        [Description("تحويل مخزون")]
        Transfer = 6,

        [Description("إرجاع")]
        Return = 7,

        [Description("مرتجع مبيعات")]
        SalesReturn = 8,

        [Description("مرتجع مشتريات")]
        PurchaseReturn = 9,

        [Description("الرصيد الافتتاحي")]
        OpeningBalance = 10
    }
    public class Party : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? TaxNumber { get; set; }

        // الأدوار (Roles)
        public virtual Customer? Customer { get; set; }
        public virtual Supplier? Supplier { get; set; }
        public virtual ProductionUnit? ProductionUnit { get; set; }
    }
    public class Customer : BaseEntity 
    {
        public int PartyId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditLimit { get; set; } = 0; //حد الائتمان 
        public string? SalesRepresentative { get; set; } //اسم المندوب

        [ForeignKey(nameof(PartyId))]
        public virtual Party Party { get; set; } = null!;
    }
    public class Supplier : BaseEntity
    {
        public int PartyId { get; set; }
        public string? BankAccount { get; set; }
        public string? Website { get; set; }

        [ForeignKey(nameof(PartyId))]
        public virtual Party Party { get; set; } = null!;
    }
    public class ProductionUnit : BaseEntity
    {
        public int PartyId { get; set; }
        public string? ManagerName { get; set; }
        public int? CapacityUnitsPerDay { get; set; }
        public string? ProductionType { get; set; }

        [ForeignKey(nameof(PartyId))]
        public virtual Party Party { get; set; } = null!;
    }
    public class InvoiceMaster : BaseEntity
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public InvoiceType InvoiceType { get; set; }
        public DateTime InvoiceDate { get; set; }
        public int PartyId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal ItemDiscountAmount { get; set; } // خصم على مستوى الأصناف
        [Column(TypeName = "decimal(18,2)")]
        public decimal InvoiceDiscountAmount { get; set; } // خصم على مستوى الفاتورة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }  //مبلغ الضريبة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }  //المبلغ الاجمالي
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } // المبلغ المدفوع
        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } //المبلغ المتبقي
        public PaymentType PaymentType { get; set; } = PaymentType.Cash;
        public bool IsPaid { get; set; } = false;
        public string? Notes { get; set; }


        [ForeignKey(nameof(PartyId))]
        public virtual Party Party { get; set; } = null!;
        public virtual ICollection<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();
        public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
       
    }
    public enum InvoiceType
    {
        [Description("مشتريات")]
        Purchase = 1,

        [Description("مبيعات")]
        Sale = 2,

        [Description("إدخال إنتاج")]
        ProductionInput = 3,

        [Description("إخراج إنتاج")]
        ProductionOutput = 4,

        [Description("مرتجع انتاج")]
        Return = 5,

        [Description("مرتجع مبيعات")]
        SalesReturn = 6,

        [Description("مرتجع مشتريات")]
        PurchaseReturn = 7
    }
    public enum PaymentType
    {
        Cash = 1,           // نقدي
        Credit = 2,         // آجل
        Check = 3,          // شيك
        BankTransfer = 4,   // تحويل بنكي
        Mixed = 5           // مختلط
    }
    public class InvoiceItem : BaseEntity
    {
        public int InvoiceMasterId { get; set; }
        public int ProductId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountPercentage { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual InvoiceMaster InvoiceMaster { get; set; } = null!;

        [ForeignKey(nameof(ProductId))]
        public virtual Product Product { get; set; } = null!;

    }
    public class ProductInventory : BaseEntity
    {
        public int ProductId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal AverageCost { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; }



        [ForeignKey(nameof(ProductId))]
        public virtual Product Product { get; set; } =null!;
    }
}
