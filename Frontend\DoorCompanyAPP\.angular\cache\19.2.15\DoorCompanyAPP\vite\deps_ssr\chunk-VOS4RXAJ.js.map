{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/module-BXZhw7pQ.mjs"], "sourcesContent": ["import { ObserversModule } from '@angular/cdk/observers';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { j as <PERSON><PERSON><PERSON><PERSON>ield, M as <PERSON><PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON><PERSON>, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-DqPi4knt.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nclass MatFormFieldModule {\n  static ɵfac = function MatFormFieldModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatFormFieldModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatFormFieldModule,\n    imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],\n    exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, ObserversModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],\n      exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatFormFieldModule as M };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB,cAAc,UAAU,UAAU,SAAS,WAAW,SAAS;AAAA,IAC3G,SAAS,CAAC,cAAc,UAAU,SAAS,UAAU,WAAW,WAAW,eAAe;AAAA,EAC5F,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,iBAAiB,eAAe;AAAA,EAC7D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,cAAc,UAAU,UAAU,SAAS,WAAW,SAAS;AAAA,MAC3G,SAAS,CAAC,cAAc,UAAU,SAAS,UAAU,WAAW,WAAW,eAAe;AAAA,IAC5F,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}