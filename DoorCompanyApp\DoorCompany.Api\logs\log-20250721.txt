2025-07-21 10:46:30.283 +03:00 [INF] Executed DbCommand (60ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:46:30.391 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:46:30.633 +03:00 [INF] Executed DbCommand (102ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 10:46:30.753 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 10:46:30.825 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-21 10:46:30.852 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:46:30.863 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:46:31.094 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-21 10:46:31.443 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-21 10:46:31.589 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 10:46:31.590 +03:00 [INF] Hosting environment: Development
2025-07-21 10:46:31.593 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-21 10:46:32.084 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-21 10:46:32.388 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 321.2678ms
2025-07-21 10:46:32.679 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-21 10:46:32.686 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-21 10:46:32.687 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.6704ms
2025-07-21 10:46:32.751 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 64.7815ms
2025-07-21 10:46:32.951 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-21 10:46:32.980 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 28.6243ms
2025-07-21 10:46:52.732 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:46:52.749 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:46:52.804 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 73.0338ms
2025-07-21 10:47:01.021 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:01.035 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:01.043 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 22.0495ms
2025-07-21 10:47:38.647 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:38.656 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:38.660 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 12.859ms
2025-07-21 10:47:41.999 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:42.003 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:42.007 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.9229ms
2025-07-21 10:47:42.528 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:42.532 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:42.546 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 17.4458ms
2025-07-21 10:47:42.714 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:42.717 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:42.731 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrlUploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 17.3961ms
2025-07-21 10:47:46.262 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:46.265 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:46.270 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.8053ms
2025-07-21 10:47:46.853 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:46.858 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:46.862 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 9.458ms
2025-07-21 10:47:47.060 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:47.064 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:47.068 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.9144ms
2025-07-21 10:47:47.248 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:47.252 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:47.255 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.0098ms
2025-07-21 10:47:47.436 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:47.449 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:47.454 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 17.8319ms
2025-07-21 10:47:47.600 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:47.610 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:47.619 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 18.961ms
2025-07-21 10:47:47.783 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:47.787 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:47.791 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 8.6122ms
2025-07-21 10:47:47.972 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:47.976 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:47.980 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 8.0965ms
2025-07-21 10:47:48.171 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:48.174 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:48.178 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.651ms
2025-07-21 10:47:48.341 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:48.345 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:48.350 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 8.439ms
2025-07-21 10:47:48.559 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:48.563 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:48.567 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.4758ms
2025-07-21 10:47:48.742 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:48.747 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:48.751 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 9.1618ms
2025-07-21 10:47:48.931 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:48.935 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:48.938 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.038ms
2025-07-21 10:47:49.140 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:49.144 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:49.149 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 8.3273ms
2025-07-21 10:47:49.335 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:49.338 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:49.341 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 6.6224ms
2025-07-21 10:47:49.516 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:49.519 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:49.534 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 18.2666ms
2025-07-21 10:47:49.713 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:49.716 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:49.720 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 6.8726ms
2025-07-21 10:47:54.390 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:54.394 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:54.398 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.7301ms
2025-07-21 10:47:54.602 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:54.605 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:54.609 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.3601ms
2025-07-21 10:47:54.804 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:54.808 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:54.812 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.4913ms
2025-07-21 10:47:54.991 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:54.994 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:54.998 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.188ms
2025-07-21 10:47:55.161 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:55.165 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:55.168 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.0748ms
2025-07-21 10:47:55.343 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:55.347 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:55.350 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.2431ms
2025-07-21 10:47:55.532 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:55.545 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:55.550 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 17.6649ms
2025-07-21 10:47:55.717 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:55.720 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:55.723 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 6.457ms
2025-07-21 10:47:55.906 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:55.910 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:55.915 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 8.888ms
2025-07-21 10:47:56.089 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:56.092 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:56.096 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 6.9146ms
2025-07-21 10:47:56.268 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:56.271 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:56.275 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 6.9065ms
2025-07-21 10:47:56.480 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:56.483 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:56.486 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 6.1565ms
2025-07-21 10:47:56.656 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:56.660 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:56.664 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.9664ms
2025-07-21 10:47:56.843 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:56.846 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:56.849 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 6.9626ms
2025-07-21 10:47:57.029 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:57.032 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:57.036 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 6.6864ms
2025-07-21 10:47:57.200 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:57.204 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:57.209 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 8.7435ms
2025-07-21 10:47:57.393 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:57.396 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:57.400 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.0356ms
2025-07-21 10:47:57.572 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:57.576 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:57.579 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.5387ms
2025-07-21 10:47:57.769 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:57.774 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:57.778 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 8.2603ms
2025-07-21 10:47:57.940 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:57.944 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:57.948 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 7.1341ms
2025-07-21 10:47:58.125 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:58.128 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:58.132 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 6.8878ms
2025-07-21 10:47:58.307 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:47:58.310 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)
DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 10:47:58.316 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 500 null text/plain; charset=utf-8 8.9417ms
2025-07-21 10:49:30.258 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:49:30.329 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:49:30.469 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 10:49:30.560 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 10:49:30.599 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-21 10:49:30.610 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:49:30.617 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:49:30.743 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-21 10:49:30.907 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-21 10:49:31.032 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 10:49:31.034 +03:00 [INF] Hosting environment: Development
2025-07-21 10:49:31.035 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-21 10:49:31.404 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-21 10:49:31.611 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 216.4499ms
2025-07-21 10:49:31.638 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-21 10:49:31.641 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-21 10:49:31.650 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 12.1917ms
2025-07-21 10:49:31.685 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.7915ms
2025-07-21 10:49:31.842 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-21 10:49:31.862 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.7614ms
2025-07-21 10:49:55.693 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl?pathstring=%5CUploads%5CUsers%5C5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:49:55.706 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-21 10:49:56.856 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)'
2025-07-21 10:49:56.887 +03:00 [INF] Route matched with {action = "GetFullImageUrl", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFullImageUrl(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-21 10:50:08.522 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-21 10:50:08.527 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api) in 11630.5719ms
2025-07-21 10:50:08.533 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)'
2025-07-21 10:50:10.456 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl?pathstring=%5CUploads%5CUsers%5C5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 14762.7088ms
2025-07-21 10:50:18.513 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl?pathstring=Uploads%5CUsers%5C5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-21 10:50:18.531 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)'
2025-07-21 10:50:18.535 +03:00 [INF] Route matched with {action = "GetFullImageUrl", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFullImageUrl(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-21 10:50:25.873 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-21 10:50:25.916 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api) in 7378.1965ms
2025-07-21 10:50:25.919 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetFullImageUrl (DoorCompany.Api)'
2025-07-21 10:50:27.879 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUrl?pathstring=Uploads%5CUsers%5C5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 200 null application/json; charset=utf-8 9366.485ms
2025-07-21 10:50:44.713 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>
2025-07-21 10:50:44.783 +03:00 [INF] Sending file. Request path: '/Uploads/Users/<USER>'. Physical path: 'D:\DoorAPP\DoorCompanyApp\DoorCompany.Api\wwwroot\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg'
2025-07-21 10:50:44.787 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>/jpeg 74.3546ms
2025-07-21 10:50:44.895 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/favicon.ico - null null
2025-07-21 10:50:44.901 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/favicon.ico - 404 0 null 6.5811ms
2025-07-21 10:50:44.905 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/favicon.ico, Response status code: 404
2025-07-21 10:51:57.167 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:51:57.252 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:51:57.394 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 10:51:57.500 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 10:51:57.545 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-21 10:51:57.556 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:51:57.565 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 10:51:57.690 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-21 10:51:57.845 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-21 10:51:57.951 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 10:51:57.953 +03:00 [INF] Hosting environment: Development
2025-07-21 10:51:57.954 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-21 10:51:58.209 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-21 10:51:58.448 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 248.5861ms
2025-07-21 10:51:58.480 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-21 10:51:58.480 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-21 10:51:58.490 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.9062ms
2025-07-21 10:51:58.524 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 44.8843ms
2025-07-21 10:51:58.699 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-21 10:51:58.730 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 31.5375ms
2025-07-21 10:53:27.583 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 52
2025-07-21 10:53:27.601 +03:00 [INF] CORS policy execution successful.
2025-07-21 10:53:27.604 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-21 10:53:27.665 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-21 10:53:27.694 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-21 10:53:27.932 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-21 10:53:27.992 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-21 10:53:28.198 +03:00 [INF] Access token generated for user 1
2025-07-21 10:53:28.383 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-21 10:53:28.415 +03:00 [INF] User logged in successfully: admin
2025-07-21 10:53:28.426 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 10:53:28.458 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 757.361ms
2025-07-21 10:53:28.460 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-21 10:53:28.464 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 881.5411ms
2025-07-21 10:54:49.496 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-21 10:54:49.591 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 10:54:49.598 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 10:54:49.704 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 10:54:49.724 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 10:54:49.739 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 137.8654ms
2025-07-21 10:54:49.742 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 10:54:49.744 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 248.0547ms
2025-07-21 10:55:29.005 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5184/api/User/profile - multipart/form-data; boundary=----WebKitFormBoundaryhLB5u4h5iwaQVPa4 448
2025-07-21 10:55:29.014 +03:00 [INF] CORS policy execution successful.
2025-07-21 10:55:29.020 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.UpdateProfile (DoorCompany.Api)'
2025-07-21 10:55:29.028 +03:00 [INF] Route matched with {action = "UpdateProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateProfile(DoorCompany.Service.Dtos.UserDto.UpdateUserProfileDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 10:55:29.076 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 10:55:29.098 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p9='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (DbType = Boolean), @p2='?' (DbType = Boolean), @p3='?' (Size = 4000), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FullName] = @p0, [IsActive] = @p1, [IsDeleted] = @p2, [Password] = @p3, [Phone] = @p4, [ProfileImage] = @p5, [UpdatedAt] = @p6, [UpdatedBy] = @p7, [UserName] = @p8
OUTPUT 1
WHERE [Id] = @p9;
2025-07-21 10:55:29.104 +03:00 [INF] User profile updated successfully: 1
2025-07-21 10:55:29.115 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 10:55:29.119 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 10:55:29.120 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.UpdateProfile (DoorCompany.Api) in 89.7053ms
2025-07-21 10:55:29.122 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.UpdateProfile (DoorCompany.Api)'
2025-07-21 10:55:29.123 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 118.6806ms
2025-07-21 10:56:31.045 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5184/api/User/profile - multipart/form-data; boundary=----WebKitFormBoundaryOM9HAyUqkPBHOg3v 452
2025-07-21 10:56:31.066 +03:00 [INF] CORS policy execution successful.
2025-07-21 10:56:31.070 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.UpdateProfile (DoorCompany.Api)'
2025-07-21 10:56:31.072 +03:00 [INF] Route matched with {action = "UpdateProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateProfile(DoorCompany.Service.Dtos.UserDto.UpdateUserProfileDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 10:56:31.089 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 10:56:31.098 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p9='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (DbType = Boolean), @p2='?' (DbType = Boolean), @p3='?' (Size = 4000), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FullName] = @p0, [IsActive] = @p1, [IsDeleted] = @p2, [Password] = @p3, [Phone] = @p4, [ProfileImage] = @p5, [UpdatedAt] = @p6, [UpdatedBy] = @p7, [UserName] = @p8
OUTPUT 1
WHERE [Id] = @p9;
2025-07-21 10:56:31.101 +03:00 [INF] User profile updated successfully: 1
2025-07-21 10:56:31.113 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 10:56:31.116 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 10:56:31.118 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.UpdateProfile (DoorCompany.Api) in 43.2081ms
2025-07-21 10:56:31.119 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.UpdateProfile (DoorCompany.Api)'
2025-07-21 10:56:31.120 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 75.2063ms
2025-07-21 11:04:05.562 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:04:05.669 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:04:05.844 +03:00 [INF] Executed DbCommand (44ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 11:04:05.931 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 11:04:06.004 +03:00 [INF] Executed DbCommand (25ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-21 11:04:06.043 +03:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:04:06.067 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:04:06.209 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-21 11:04:06.377 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-21 11:04:06.481 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 11:04:06.482 +03:00 [INF] Hosting environment: Development
2025-07-21 11:04:06.483 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-21 11:04:06.695 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-21 11:04:06.931 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 247.0367ms
2025-07-21 11:04:06.961 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-21 11:04:06.965 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-21 11:04:06.973 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 12.4227ms
2025-07-21 11:04:07.009 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.9225ms
2025-07-21 11:04:07.175 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-21 11:04:07.196 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 20.6688ms
2025-07-21 11:04:20.905 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-21 11:04:20.916 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-21 11:04:21.031 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '20/07/2025 09:51:50 ص', Current time (UTC): '21/07/2025 08:04:21 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-21 11:04:21.054 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '20/07/2025 09:51:50 ص', Current time (UTC): '21/07/2025 08:04:21 ص'.
2025-07-21 11:04:21.060 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '20/07/2025 09:51:50 ص', Current time (UTC): '21/07/2025 08:04:21 ص'.
2025-07-21 11:04:21.069 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-21 11:04:21.076 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-21 11:04:21.079 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 174.7411ms
2025-07-21 11:04:37.959 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-21 11:04:37.988 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 11:04:38.015 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:04:38.272 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 11:04:38.380 +03:00 [ERR] Error getting user by id: 1
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
User -> UserResponseDto
DoorCompany.Data.Models.User -> DoorCompany.Service.Dtos.UserDto.UserResponseDto

Type Map configuration:
User -> UserResponseDto
DoorCompany.Data.Models.User -> DoorCompany.Service.Dtos.UserDto.UserResponseDto

Destination Member:
ProfileImage

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_14(User src, UserResponseDto dest, String destMember, ResolutionContext context) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 31
   at lambda_method265(Closure, Object, UserResponseDto, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method265(Closure, Object, UserResponseDto, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.UserService.GetUserByIdAsync(Int32 id) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 86
2025-07-21 11:04:38.417 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 11:04:38.446 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 425.4254ms
2025-07-21 11:04:38.448 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 11:04:38.451 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 400 null application/json; charset=utf-8 492.0547ms
2025-07-21 11:05:01.560 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-21 11:05:01.573 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 11:05:01.575 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:05:07.114 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 11:06:02.061 +03:00 [ERR] Error getting user by id: 1
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
User -> UserResponseDto
DoorCompany.Data.Models.User -> DoorCompany.Service.Dtos.UserDto.UserResponseDto

Type Map configuration:
User -> UserResponseDto
DoorCompany.Data.Models.User -> DoorCompany.Service.Dtos.UserDto.UserResponseDto

Destination Member:
ProfileImage

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_14(User src, UserResponseDto dest, String destMember, ResolutionContext context) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 31
   at lambda_method265(Closure, Object, UserResponseDto, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method265(Closure, Object, UserResponseDto, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.UserService.GetUserByIdAsync(Int32 id) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 86
2025-07-21 11:06:03.668 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 11:06:03.670 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 62092.2989ms
2025-07-21 11:06:03.672 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 11:06:03.674 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 400 null application/json; charset=utf-8 62114.1166ms
2025-07-21 11:06:12.250 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-21 11:06:12.253 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 11:06:12.255 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:06:16.843 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 11:15:40.803 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:15:40.885 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:15:41.037 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 11:15:41.134 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 11:15:41.174 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-21 11:15:41.185 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:15:41.193 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:15:41.349 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-21 11:15:41.501 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-21 11:15:41.630 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 11:15:41.632 +03:00 [INF] Hosting environment: Development
2025-07-21 11:15:41.633 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-21 11:15:41.753 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-21 11:15:41.973 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 229.3461ms
2025-07-21 11:15:41.994 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-21 11:15:42.000 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.289ms
2025-07-21 11:15:42.011 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-21 11:15:42.047 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 36.0279ms
2025-07-21 11:15:42.219 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-21 11:15:42.246 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 27.0302ms
2025-07-21 11:16:03.583 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/1 - null null
2025-07-21 11:16:03.603 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-21 11:16:03.729 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetUserById (DoorCompany.Api)'
2025-07-21 11:16:03.754 +03:00 [INF] Route matched with {action = "GetUserById", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserById(Int32) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:16:04.010 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 11:16:04.065 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 11:16:04.099 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetUserById (DoorCompany.Api) in 338.5678ms
2025-07-21 11:16:04.102 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetUserById (DoorCompany.Api)'
2025-07-21 11:16:04.105 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/1 - 200 null application/json; charset=utf-8 523.3001ms
2025-07-21 11:16:35.272 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/2 - null null
2025-07-21 11:16:35.298 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetUserById (DoorCompany.Api)'
2025-07-21 11:16:35.299 +03:00 [INF] Route matched with {action = "GetUserById", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserById(Int32) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:16:35.390 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 11:16:35.394 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 11:16:35.397 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetUserById (DoorCompany.Api) in 95.9701ms
2025-07-21 11:16:35.399 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetUserById (DoorCompany.Api)'
2025-07-21 11:16:35.400 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/2 - 404 null application/json; charset=utf-8 130.5363ms
2025-07-21 11:16:44.647 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-21 11:16:44.654 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-21 11:16:44.660 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:16:44.698 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-21 11:16:44.797 +03:00 [ERR] Error getting all users
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
List`1 -> List`1
System.Collections.Generic.List`1[[DoorCompany.Data.Models.User, DoorCompany.Data, Version=*******, Culture=neutral, PublicKeyToken=null]] -> System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]
 ---> AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
User -> UserResponseDto
DoorCompany.Data.Models.User -> DoorCompany.Service.Dtos.UserDto.UserResponseDto

Type Map configuration:
User -> UserResponseDto
DoorCompany.Data.Models.User -> DoorCompany.Service.Dtos.UserDto.UserResponseDto

Destination Member:
ProfileImage

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_14(User src, UserResponseDto dest, String destMember, ResolutionContext context) in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 31
   at lambda_method325(Closure, Object, List`1, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method325(Closure, Object, List`1, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method325(Closure, Object, List`1, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.UserService.GetAllUsersAsync() in D:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\UserService.cs:line 67
2025-07-21 11:16:44.838 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-21 11:16:44.842 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 178.9699ms
2025-07-21 11:16:44.844 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-21 11:16:44.845 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 400 null application/json; charset=utf-8 198.1761ms
2025-07-21 11:17:55.323 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:17:55.394 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:17:55.536 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 11:17:55.628 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 11:17:55.670 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-21 11:17:55.681 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:17:55.688 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:17:55.823 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-21 11:17:56.010 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-21 11:17:56.145 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 11:17:56.147 +03:00 [INF] Hosting environment: Development
2025-07-21 11:17:56.149 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-21 11:17:56.512 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-21 11:17:56.719 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 214.915ms
2025-07-21 11:17:56.744 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-21 11:17:56.745 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-21 11:17:56.758 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 14.5836ms
2025-07-21 11:17:56.800 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 54.5774ms
2025-07-21 11:17:56.964 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-21 11:17:56.985 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 21.4099ms
2025-07-21 11:18:11.546 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-21 11:18:11.569 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-21 11:18:11.711 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 11:18:11.734 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:18:11.972 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 11:18:12.010 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 11:18:12.060 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 318.1776ms
2025-07-21 11:18:12.063 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 11:18:12.068 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 522.0534ms
2025-07-21 11:18:32.002 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>
2025-07-21 11:18:32.025 +03:00 [INF] The file /Uploads/Users/<USER>
2025-07-21 11:18:32.027 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>/jpeg 25.8646ms
2025-07-21 11:19:36.284 +03:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:19:36.360 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:19:36.501 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 11:19:36.588 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 11:19:36.629 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-21 11:19:36.640 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:19:36.647 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:19:36.830 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-21 11:19:36.991 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-21 11:19:37.098 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 11:19:37.100 +03:00 [INF] Hosting environment: Development
2025-07-21 11:19:37.101 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-21 11:19:37.481 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-21 11:19:37.689 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 216.1219ms
2025-07-21 11:19:37.718 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-21 11:19:37.719 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-21 11:19:37.728 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.3457ms
2025-07-21 11:19:37.760 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.0368ms
2025-07-21 11:19:37.924 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-21 11:19:37.944 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.8439ms
2025-07-21 11:19:59.720 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-21 11:19:59.743 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-21 11:19:59.867 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-21 11:19:59.890 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:20:00.137 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-21 11:20:00.185 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-21 11:20:00.220 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 324.0686ms
2025-07-21 11:20:00.223 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-21 11:20:00.227 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 507.4186ms
2025-07-21 11:22:55.616 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/User - application/json 126
2025-07-21 11:22:55.622 +03:00 [INF] CORS policy execution successful.
2025-07-21 11:22:55.626 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.CreateUser (DoorCompany.Api)'
2025-07-21 11:22:55.636 +03:00 [INF] Route matched with {action = "CreateUser", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateUser(DoorCompany.Service.Dtos.UserDto.CreateUserDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:22:55.746 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[UserName] = @__userDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-07-21 11:22:55.988 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([CreatedAt], [CreatedBy], [FullName], [IsActive], [IsDeleted], [Password], [Phone], [ProfileImage], [UpdatedAt], [UpdatedBy], [UserName])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-07-21 11:22:56.021 +03:00 [INF] User created successfully: GA
2025-07-21 11:22:56.053 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 11:22:56.066 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 11:22:56.071 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.CreateUser (DoorCompany.Api) in 431.6296ms
2025-07-21 11:22:56.074 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.CreateUser (DoorCompany.Api)'
2025-07-21 11:22:56.076 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/User - 200 null application/json; charset=utf-8 460.2775ms
2025-07-21 11:23:05.077 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-21 11:23:05.093 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-21 11:23:05.098 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:23:05.118 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-21 11:23:05.123 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-21 11:23:05.125 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 23.011ms
2025-07-21 11:23:05.127 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-21 11:23:05.128 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 51.4359ms
2025-07-21 11:24:58.297 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:24:58.376 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:24:58.521 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 11:24:58.618 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 11:24:58.659 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-21 11:24:58.671 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:24:58.678 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 11:24:58.832 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-21 11:24:58.998 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-21 11:24:59.100 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 11:24:59.101 +03:00 [INF] Hosting environment: Development
2025-07-21 11:24:59.102 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-21 11:24:59.471 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-21 11:24:59.713 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 250.6009ms
2025-07-21 11:24:59.751 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-21 11:24:59.751 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-21 11:24:59.761 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.7186ms
2025-07-21 11:24:59.795 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 44.7094ms
2025-07-21 11:24:59.961 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-21 11:24:59.982 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 21.7492ms
2025-07-21 11:25:29.451 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/1/user-Roles - null null
2025-07-21 11:25:29.478 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-21 11:25:29.606 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetUserRoleById (DoorCompany.Api)'
2025-07-21 11:25:29.633 +03:00 [INF] Route matched with {action = "GetUserRoleById", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserRoleById(Int32) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:25:29.884 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 11:25:29.908 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 11:25:29.922 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 11:25:29.947 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetUserRoleById (DoorCompany.Api) in 307.3345ms
2025-07-21 11:25:29.950 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetUserRoleById (DoorCompany.Api)'
2025-07-21 11:25:29.955 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/1/user-Roles - 200 null application/json; charset=utf-8 504.5937ms
2025-07-21 11:25:44.153 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - null null
2025-07-21 11:25:44.162 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-21 11:25:44.167 +03:00 [INF] Route matched with {action = "GetAllUserRoles", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUserRolesAsync() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:25:44.262 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-21 11:25:44.270 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-21 11:25:44.274 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api) in 104.9316ms
2025-07-21 11:25:44.277 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-21 11:25:44.279 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - 200 null application/json; charset=utf-8 126.1238ms
2025-07-21 11:25:53.918 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-21 11:25:53.931 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 11:25:53.936 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-21 11:25:53.970 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-21 11:25:53.991 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-21 11:25:54.008 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 69.6862ms
2025-07-21 11:25:54.010 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-21 11:25:54.012 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 94.4138ms
2025-07-21 17:24:15.106 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 17:24:15.211 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 17:24:15.420 +03:00 [INF] Executed DbCommand (79ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-21 17:24:15.522 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-21 17:24:15.568 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-21 17:24:15.580 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 17:24:15.589 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-21 17:24:15.833 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-21 17:24:16.169 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-21 17:24:16.369 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 17:24:16.370 +03:00 [INF] Hosting environment: Development
2025-07-21 17:24:16.372 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-21 17:24:16.883 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-21 17:24:17.185 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 313.7828ms
2025-07-21 17:24:17.220 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-21 17:24:17.227 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.299ms
2025-07-21 17:24:17.557 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-21 17:24:17.637 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 79.6309ms
2025-07-21 17:24:17.788 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-21 17:24:17.815 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 26.9078ms
