﻿using DoorCompany.Service.Dtos.BasicDto;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos.ShareDto
{
    public class ShareResponseDto : BaseDto
    {
        public int PartnerId { get; set; }
        public string? PartnerName { get; set; }
        public int SharesCount { get; set; }
        public DateTime DistributionDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ShareValue { get; set; }
        public string? Description { get; set; }      
    }

    public class CreateShareDto : BaseCreateDto
    {
        public int PartnerId { get; set; }
        public int SharesCount { get; set; }
        public DateTime DistributionDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ShareValue { get; set; }
        public string? Description { get; set; }
    }

    public class UpdateShareDto : BaseUpdateDto
    {
        public int PartnerId { get; set; }
        public int SharesCount { get; set; }
        public DateTime DistributionDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ShareValue { get; set; }
        public string? Description { get; set; }
    }
}
