import './polyfills.server.mjs';
import{Ea as p,Fa as n,Kc as m,L as t,Za as a,_a as c,kc as u,qb as f}from"./chunk-JADE4TID.mjs";import"./chunk-S6KH3LOX.mjs";var r=class e{static \u0275fac=function(o){return new(o||e)};static \u0275cmp=p({type:e,selectors:[["app-users"]],standalone:!1,decls:2,vars:0,template:function(o,M){o&1&&(a(0,"p"),f(1,"users works!"),c())},encapsulation:2})};var C=[{path:"",component:r}],s=class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=n({type:e});static \u0275inj=t({imports:[m.forChild(C),m]})};var l=class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=n({type:e});static \u0275inj=t({imports:[u,s]})};export{l as UsersModule};
