using DoorCompany.Api.Base;
using DoorCompany.Service.Services;
using DoorCompany.Service.Dtos.InventoryDto;
using DoorCompany.Data.Models;
using Microsoft.AspNetCore.Mvc;
using DoorCompany.Service.Repositories.Interfaces.Basic;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InventoryController : AppControllerBase
    {
        public InventoryController(IUnitOfWork work) : base(work)
        {
        }

        /// <summary>
        /// الحصول على رصيد منتج معين
        /// </summary>
        [HttpGet("product/{productId}")]
        public async Task<IActionResult> GetProductInventory(int productId)
        {
            var result = await _work.InventoryRepository.GetProductInventoryAsync(productId);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على تقرير المخزون
        /// </summary>
        [HttpGet("report")]
        public async Task<IActionResult> GetInventoryReport([FromQuery] InventoryFilterDto filterDto)
        {
            var result = await _work.InventoryRepository.GetInventoryReportAsync(filterDto);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على حركات منتج معين
        /// </summary>
        [HttpGet("product/{productId}/transactions")]
        public async Task<IActionResult> GetProductTransactions(int productId, [FromQuery] InventoryFilterDto filterDto)
        {
            var result = await _work.InventoryRepository.GetProductTransactionsAsync(productId, filterDto);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على حركات المخزون العامة
        /// </summary>
        [HttpGet("transactions")]
        public async Task<IActionResult> GetInventoryTransactions([FromQuery] InventoryFilterDto filterDto)
        {
            var result = await _work.InventoryRepository.GetInventoryTransactionsAsync(filterDto);
            return NewResult(result);
        }

        /// <summary>
        /// التحقق من توفر منتج معين
        /// </summary>
        [HttpGet("check-availability/{productId}")]
        public async Task<IActionResult> CheckProductAvailability(int productId, [FromQuery] decimal requiredQuantity)
        {
            var result = await _work.InventoryRepository.CheckProductAvailabilityAsync(productId, requiredQuantity);
            return NewResult(result);
        }

        /// <summary>
        /// التحقق من توفر منتجات متعددة
        /// </summary>
        [HttpPost("check-availability")]
        public async Task<IActionResult> CheckProductsAvailability([FromBody] List<ProductQuantityDto> products)
        {
            var result = await _work.InventoryRepository.CheckProductsAvailabilityAsync(products);
            return NewResult(result);
        }

        /// <summary>
        /// حساب متوسط التكلفة للمنتج
        /// </summary>
        [HttpGet("product/{productId}/average-cost")]
        public async Task<IActionResult> CalculateAverageCost(int productId)
        {
            var result = await _work.InventoryRepository.CalculateAverageCostAsync(productId);
            return NewResult(result);
        }

        /// <summary>
        /// إعادة حساب أرصدة المخزون (للصيانة)
        /// </summary>
        [HttpPost("recalculate")]
        public async Task<IActionResult> RecalculateInventory()
        {
            var result = await _work.InventoryRepository.RecalculateInventoryAsync();
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على المنتجات التي وصلت للحد الأدنى
        /// </summary>
        [HttpGet("low-stock")]
        public async Task<IActionResult> GetLowStockProducts()
        {
            var result = await _work.InventoryRepository.GetLowStockProductsAsync();
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على المنتجات التي تجاوزت الحد الأقصى
        /// </summary>
        [HttpGet("over-stock")]
        public async Task<IActionResult> GetOverStockProducts()
        {
            var result = await _work.InventoryRepository.GetOverStockProductsAsync();
            return NewResult(result);
        }

        /// <summary>
        /// تحديث رصيد المنتج يدوياً
        /// </summary>
        [HttpPost("product/{productId}/update")]
        public async Task<IActionResult> UpdateProductInventory(int productId, [FromBody] UpdateInventoryDto updateDto)
        {
            var result = await _work.InventoryRepository.UpdateProductInventoryAsync(productId, updateDto.Quantity, updateDto.UnitCost, updateDto.TransactionType);
            return NewResult(result);
        }
    }

    /// <summary>
    /// DTO لتحديث المخزون يدوياً
    /// </summary>
    public class UpdateInventoryDto
    {
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public DoorCompany.Data.Models.InventoryTransactionType TransactionType { get; set; }
        public string? Description { get; set; }
    }
}
