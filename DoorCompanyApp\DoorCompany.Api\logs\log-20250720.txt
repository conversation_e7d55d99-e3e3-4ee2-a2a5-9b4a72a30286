2025-07-20 11:48:45.920 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:48:46.019 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:48:46.235 +03:00 [INF] Executed DbCommand (99ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 11:48:46.344 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 11:48:46.388 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 11:48:46.400 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:48:46.410 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:48:46.610 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 11:48:46.932 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 11:48:47.062 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:48:47.064 +03:00 [INF] Hosting environment: Development
2025-07-20 11:48:47.065 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 11:48:47.429 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 11:48:47.717 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 304.0625ms
2025-07-20 11:48:47.748 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 11:48:47.748 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 11:48:47.755 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.8112ms
2025-07-20 11:48:47.813 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 64.8998ms
2025-07-20 11:48:47.954 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 11:48:47.974 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.2891ms
2025-07-20 11:48:58.996 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - null null
2025-07-20 11:48:59.011 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 11:49:00.234 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-20 11:49:00.254 +03:00 [INF] Route matched with {action = "GetAllPermissons", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPermissons() on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 11:49:00.407 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-20 11:49:00.425 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PermissionDto.PermissionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 11:49:00.447 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api) in 186.7211ms
2025-07-20 11:49:00.450 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-20 11:49:00.455 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - 200 null application/json; charset=utf-8 1458.1822ms
2025-07-20 11:51:04.594 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:51:04.669 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:51:04.811 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 11:51:04.901 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 11:51:04.942 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 11:51:04.953 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:51:04.962 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:51:05.098 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 11:51:05.264 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 11:51:05.507 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:51:05.509 +03:00 [INF] Hosting environment: Development
2025-07-20 11:51:05.510 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 11:51:05.733 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 11:51:05.940 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 215.2499ms
2025-07-20 11:51:05.970 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 11:51:05.970 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 11:51:05.978 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.8951ms
2025-07-20 11:51:06.015 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 45.5127ms
2025-07-20 11:51:06.179 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 11:51:06.198 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.6239ms
2025-07-20 11:51:14.597 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-20 11:51:14.611 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 11:51:14.674 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-20 11:51:14.695 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 11:51:14.932 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Roles] AS [r]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r].[Id] = [s].[RoleId]
ORDER BY [r].[Id], [s].[Id]
2025-07-20 11:51:14.957 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.RoleResponseWithPermissionListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 11:51:14.980 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api) in 279.0887ms
2025-07-20 11:51:14.983 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-20 11:51:14.987 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - 200 null application/json; charset=utf-8 390.1504ms
2025-07-20 11:51:23.051 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:51:23.066 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:51:23.073 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-20 11:51:23.075 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 401 0 null 23.7605ms
2025-07-20 11:51:49.664 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 52
2025-07-20 11:51:49.669 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-20 11:51:49.683 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-20 11:51:49.806 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-20 11:51:49.866 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-20 11:51:50.100 +03:00 [INF] Access token generated for user 1
2025-07-20 11:51:50.283 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-20 11:51:50.315 +03:00 [INF] User logged in successfully: admin
2025-07-20 11:51:50.317 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 11:51:50.332 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 646.3764ms
2025-07-20 11:51:50.334 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-20 11:51:50.336 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 671.5568ms
2025-07-20 11:52:16.531 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-20 11:52:16.603 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-20 11:52:16.605 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 11:52:16.635 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Roles] AS [r]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r].[Id] = [s].[RoleId]
ORDER BY [r].[Id], [s].[Id]
2025-07-20 11:52:16.639 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.RoleResponseWithPermissionListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 11:52:16.640 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api) in 33.835ms
2025-07-20 11:52:16.642 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-20 11:52:16.643 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - 200 null application/json; charset=utf-8 111.6562ms
2025-07-20 11:52:21.548 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:52:21.555 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:21.558 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:21.560 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 11.1744ms
2025-07-20 11:52:25.020 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:52:25.025 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:25.027 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:25.029 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 8.1613ms
2025-07-20 11:52:25.585 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:52:25.590 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:25.600 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:25.602 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 17.2255ms
2025-07-20 11:52:25.794 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:52:25.798 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:25.799 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:25.801 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 6.366ms
2025-07-20 11:52:26.004 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:52:26.008 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:26.009 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:26.010 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 6.5226ms
2025-07-20 11:52:29.081 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:52:29.085 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:29.086 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:29.087 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 6.6859ms
2025-07-20 11:52:29.888 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:52:29.893 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:29.895 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:29.896 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 8.7934ms
2025-07-20 11:52:31.462 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:52:31.476 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:31.480 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:31.482 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 20.0473ms
2025-07-20 11:52:31.629 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:52:31.640 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:31.643 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:31.644 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 15.0395ms
2025-07-20 11:52:31.817 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:52:31.821 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:31.822 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:31.823 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 5.9985ms
2025-07-20 11:52:31.996 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:52:32.001 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:32.003 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:32.004 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 8.2659ms
2025-07-20 11:52:32.168 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:52:32.172 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:32.173 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:32.175 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 6.8548ms
2025-07-20 11:52:32.348 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:52:32.352 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:52:32.353 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:52:32.355 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 6.6816ms
2025-07-20 11:53:14.882 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 52
2025-07-20 11:53:14.889 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-20 11:53:14.890 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-20 11:53:14.946 +03:00 [INF] Executed DbCommand (51ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-20 11:53:15.055 +03:00 [INF] Access token generated for user 1
2025-07-20 11:53:15.065 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-20 11:53:15.070 +03:00 [INF] User logged in successfully: admin
2025-07-20 11:53:15.070 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 11:53:15.072 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 180.604ms
2025-07-20 11:53:15.074 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-20 11:53:15.075 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 197.7593ms
2025-07-20 11:53:23.480 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-20 11:53:23.484 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-20 11:53:23.485 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 11:53:23.507 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Roles] AS [r]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r].[Id] = [s].[RoleId]
ORDER BY [r].[Id], [s].[Id]
2025-07-20 11:53:23.510 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.RoleResponseWithPermissionListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 11:53:23.512 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api) in 24.3241ms
2025-07-20 11:53:23.514 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-20 11:53:23.516 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - 200 null application/json; charset=utf-8 35.5536ms
2025-07-20 11:53:26.787 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:53:26.802 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:53:26.805 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:53:26.807 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 19.9991ms
2025-07-20 11:53:51.271 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - null null
2025-07-20 11:53:51.285 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-20 11:53:51.290 +03:00 [INF] Route matched with {action = "GetAllPermissons", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPermissons() on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 11:53:51.312 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-20 11:53:51.317 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PermissionDto.PermissionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 11:53:51.322 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api) in 29.7746ms
2025-07-20 11:53:51.324 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-20 11:53:51.326 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - 200 null application/json; charset=utf-8 54.9415ms
2025-07-20 11:54:39.449 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:54:39.525 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:54:39.662 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 11:54:39.752 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 11:54:39.791 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 11:54:39.802 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:54:39.809 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:54:39.953 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 11:54:40.110 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 11:54:40.322 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:54:40.327 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 11:54:40.335 +03:00 [INF] Hosting environment: Development
2025-07-20 11:54:40.364 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 11:54:40.589 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 287.2107ms
2025-07-20 11:54:40.615 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 11:54:40.618 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 11:54:40.623 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.5599ms
2025-07-20 11:54:40.656 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 37.9836ms
2025-07-20 11:54:40.820 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 11:54:40.840 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 20.207ms
2025-07-20 11:54:57.671 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:54:57.693 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 11:54:57.828 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The AuthorizationPolicy named: 'Users.View' was not found.
   at Microsoft.AspNetCore.Authorization.AuthorizationPolicy.CombineAsync(IAuthorizationPolicyProvider policyProvider, IEnumerable`1 authorizeData, IEnumerable`1 policies)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-20 11:54:57.885 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 500 null text/plain; charset=utf-8 213.4463ms
2025-07-20 11:55:35.555 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - null null
2025-07-20 11:55:35.570 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-20 11:55:35.593 +03:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllRoles() on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 11:55:35.858 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Roles] AS [r]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r].[Id] = [s].[RoleId]
ORDER BY [r].[Id], [s].[Id]
2025-07-20 11:55:35.893 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.RoleResponseWithPermissionListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 11:55:35.921 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api) in 321.0808ms
2025-07-20 11:55:35.923 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetAllRoles (DoorCompany.Api)'
2025-07-20 11:55:35.928 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/All-Roles - 200 null application/json; charset=utf-8 373.4422ms
2025-07-20 11:56:57.245 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:56:57.321 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:56:57.468 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 11:56:57.558 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 11:56:57.600 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 11:56:57.612 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:56:57.620 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:56:57.754 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 11:56:57.916 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 11:56:58.086 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 11:56:58.108 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:56:58.110 +03:00 [INF] Hosting environment: Development
2025-07-20 11:56:58.111 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 11:56:58.361 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 284.2209ms
2025-07-20 11:56:58.389 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 11:56:58.389 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 11:56:58.396 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.2422ms
2025-07-20 11:56:58.427 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 37.6636ms
2025-07-20 11:56:58.583 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 11:56:58.629 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 45.6518ms
2025-07-20 11:57:13.219 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:57:13.231 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 11:57:13.370 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:57:13.375 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:57:13.376 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 156.8852ms
2025-07-20 11:57:21.078 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:57:21.087 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:57:21.090 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:57:21.092 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 14.019ms
2025-07-20 11:57:52.585 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:57:52.595 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:57:52.596 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:57:52.597 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 14.7461ms
2025-07-20 11:57:59.422 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:57:59.426 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:57:59.428 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:57:59.429 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 6.9398ms
2025-07-20 11:58:18.738 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 11:58:18.752 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:58:18.754 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:58:18.758 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 20.2027ms
2025-07-20 11:58:22.710 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:58:22.715 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 11:58:22.716 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 11:58:22.718 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 8.0847ms
2025-07-20 11:58:36.369 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 11:59:30.291 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:59:30.363 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:59:30.502 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 11:59:30.591 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 11:59:30.632 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 11:59:30.643 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:59:30.651 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 11:59:30.866 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 11:59:31.038 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 11:59:31.164 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:59:31.186 +03:00 [INF] Hosting environment: Development
2025-07-20 11:59:31.187 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 11:59:31.680 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 11:59:31.898 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 227.2482ms
2025-07-20 11:59:31.927 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 11:59:31.929 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 11:59:31.936 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.0973ms
2025-07-20 11:59:31.969 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.3531ms
2025-07-20 11:59:32.129 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 11:59:32.151 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 22.5781ms
2025-07-20 12:00:17.524 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 12:00:17.542 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 12:00:44.307 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 12:00:44.311 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 12:00:44.312 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 26791.3915ms
2025-07-20 12:02:03.648 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:02:03.724 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:02:03.873 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 12:02:03.969 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 12:02:04.012 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 12:02:04.024 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:02:04.033 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:02:04.176 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 12:02:04.352 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 12:02:04.513 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:02:04.515 +03:00 [INF] Hosting environment: Development
2025-07-20 12:02:04.516 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 12:02:04.911 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 12:02:05.119 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 216.2307ms
2025-07-20 12:02:05.148 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 12:02:05.148 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 12:02:05.156 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.1472ms
2025-07-20 12:02:05.187 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 38.4861ms
2025-07-20 12:02:05.347 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 12:02:05.374 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 26.5401ms
2025-07-20 12:02:49.566 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 12:02:49.583 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 12:03:14.279 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 12:03:14.283 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 12:03:14.285 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 403 0 null 24721.7601ms
2025-07-20 12:06:44.527 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:06:44.608 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:06:44.756 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 12:06:44.845 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 12:06:44.891 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 12:06:44.905 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:06:44.914 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:06:45.092 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 12:06:45.278 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 12:06:45.538 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:06:45.541 +03:00 [INF] Hosting environment: Development
2025-07-20 12:06:45.542 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 12:06:45.650 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 12:06:45.873 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 230.7597ms
2025-07-20 12:06:45.905 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 12:06:45.906 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 12:06:45.913 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.0859ms
2025-07-20 12:06:45.948 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.463ms
2025-07-20 12:06:46.122 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 12:06:46.142 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 20.5377ms
2025-07-20 12:06:52.504 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-20 12:06:52.518 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 12:06:52.590 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-20 12:06:52.611 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-20 12:06:52.840 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-20 12:06:52.882 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 12:06:52.920 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 302.6125ms
2025-07-20 12:06:52.924 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-20 12:06:52.929 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 424.3091ms
2025-07-20 12:07:39.322 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-20 12:07:39.329 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-20 12:07:39.330 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-20 12:07:39.428 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-20 12:07:39.434 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 12:07:39.439 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 106.1902ms
2025-07-20 12:07:39.441 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-20 12:07:39.443 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 121.2123ms
2025-07-20 12:07:46.586 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/1 - null null
2025-07-20 12:07:46.593 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetUserById (DoorCompany.Api)'
2025-07-20 12:07:46.604 +03:00 [INF] Route matched with {action = "GetUserById", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserById(Int32) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-20 12:07:46.700 +03:00 [INF] Executed DbCommand (47ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-20 12:07:46.712 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 12:07:46.717 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetUserById (DoorCompany.Api) in 110.34ms
2025-07-20 12:07:46.723 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetUserById (DoorCompany.Api)'
2025-07-20 12:07:46.725 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/1 - 200 null application/json; charset=utf-8 138.7168ms
2025-07-20 12:08:06.320 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/5 - null null
2025-07-20 12:09:32.761 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 12:09:32.767 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-20 12:09:32.769 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/5 - 401 0 null 86449.7792ms
2025-07-20 12:09:35.340 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/5 - null null
2025-07-20 12:10:20.571 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 12:10:20.576 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 12:10:20.577 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/5 - 403 0 null 45237.231ms
2025-07-20 12:11:01.872 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/5 - null null
2025-07-20 12:12:04.916 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:12:05.021 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:12:05.186 +03:00 [INF] Executed DbCommand (45ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 12:12:05.298 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 12:12:05.360 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 12:12:05.375 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:12:05.383 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:12:05.554 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 12:12:05.739 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 12:12:05.882 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:12:05.884 +03:00 [INF] Hosting environment: Development
2025-07-20 12:12:05.886 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 12:12:06.338 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 12:12:06.551 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 222.2593ms
2025-07-20 12:12:06.583 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 12:12:06.584 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 12:12:06.592 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.3258ms
2025-07-20 12:12:06.626 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.0504ms
2025-07-20 12:12:06.786 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 12:12:06.805 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.7399ms
2025-07-20 12:12:39.421 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/2 - null null
2025-07-20 12:12:39.445 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 12:12:53.236 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-20 12:12:53.262 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 12:12:56.468 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-20 12:12:56.490 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-20 12:12:56.504 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 12:12:56.527 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 3258.3983ms
2025-07-20 12:12:56.530 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-20 12:12:56.534 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/2 - 200 null application/json; charset=utf-8 17112.8632ms
2025-07-20 12:13:31.997 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 12:13:35.665 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-20 12:13:35.668 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 12:13:37.095 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-20 12:13:37.102 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-20 12:13:37.105 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 12:13:37.107 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 1436.7295ms
2025-07-20 12:13:37.115 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-20 12:13:37.116 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 200 null application/json; charset=utf-8 5119.5393ms
2025-07-20 12:14:22.700 +03:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:14:22.810 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:14:22.958 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 12:14:23.047 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 12:14:23.089 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 12:14:23.101 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:14:23.115 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:14:23.273 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 12:14:23.468 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 12:14:23.610 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:14:23.624 +03:00 [INF] Hosting environment: Development
2025-07-20 12:14:23.625 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 12:14:23.769 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 12:14:23.984 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 226.3283ms
2025-07-20 12:14:24.000 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 12:14:24.006 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.0346ms
2025-07-20 12:14:24.018 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 12:14:24.057 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.489ms
2025-07-20 12:14:24.211 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 12:14:24.232 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 21.3165ms
2025-07-20 12:14:56.085 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 12:14:56.105 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 12:15:04.501 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 12:15:04.507 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 12:15:04.509 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 8423.3654ms
2025-07-20 12:17:31.037 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - application/json 2304
2025-07-20 12:17:31.060 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-20 12:17:31.091 +03:00 [INF] Route matched with {action = "AddAndRemoveRolePermission", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveRolePermission(DoorCompany.Service.Dtos.RoleDto.UpdateRolePermissionsDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 12:17:31.271 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-20 12:17:31.300 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api) in 201.6505ms
2025-07-20 12:17:31.302 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-20 12:17:31.306 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - 400 null application/problem+json; charset=utf-8 269.2237ms
2025-07-20 12:17:54.266 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - application/json 2304
2025-07-20 12:17:54.270 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-20 12:17:54.272 +03:00 [INF] Route matched with {action = "AddAndRemoveRolePermission", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveRolePermission(DoorCompany.Service.Dtos.RoleDto.UpdateRolePermissionsDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 12:17:54.438 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[@__updateRolePermissionsDto_RoleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__updateRolePermissionsDto_RoleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-20 12:17:54.599 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = Int32), @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?' (DbType = Int32), @p15='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p0;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p1;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p2;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p3;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p4;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p5;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p6;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p7;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p8;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p9;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p10;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p11;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p12;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p13;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p14;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p15;
2025-07-20 12:17:54.685 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = Int32), @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (DbType = Int32), @p18='?' (DbType = Int32), @p19='?' (DbType = Int32), @p20='?' (DbType = Int32), @p21='?' (DbType = Int32), @p22='?' (DbType = Int32), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = Int32), @p29='?' (DbType = Int32), @p30='?' (DbType = Int32), @p31='?' (DbType = Int32), @p32='?' (DbType = Int32), @p33='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [RolePermissions] USING (
VALUES (@p0, @p1, 0),
(@p2, @p3, 1),
(@p4, @p5, 2),
(@p6, @p7, 3),
(@p8, @p9, 4),
(@p10, @p11, 5),
(@p12, @p13, 6),
(@p14, @p15, 7),
(@p16, @p17, 8),
(@p18, @p19, 9),
(@p20, @p21, 10),
(@p22, @p23, 11),
(@p24, @p25, 12),
(@p26, @p27, 13),
(@p28, @p29, 14),
(@p30, @p31, 15),
(@p32, @p33, 16)) AS i ([PermissionId], [RoleId], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([PermissionId], [RoleId])
VALUES (i.[PermissionId], i.[RoleId])
OUTPUT INSERTED.[Id], i._Position;
2025-07-20 12:17:54.709 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 12:17:54.726 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api) in 452.3068ms
2025-07-20 12:17:54.729 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-20 12:17:54.733 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - 200 null application/json; charset=utf-8 468.8831ms
2025-07-20 12:18:05.987 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 12:18:21.368 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 12:18:21.371 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 12:18:21.372 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 15385.0871ms
2025-07-20 12:55:06.459 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:55:06.536 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:55:06.680 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 12:55:06.772 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 12:55:06.812 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 12:55:06.825 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:55:06.832 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 12:55:07.022 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 12:55:07.189 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 12:55:07.312 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:55:07.314 +03:00 [INF] Hosting environment: Development
2025-07-20 12:55:07.315 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 12:55:07.877 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 12:55:08.098 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 230.6761ms
2025-07-20 12:55:08.132 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 12:55:08.132 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 12:55:08.150 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 18.7412ms
2025-07-20 12:55:08.175 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.7313ms
2025-07-20 12:55:08.336 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 12:55:08.361 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 24.6647ms
2025-07-20 12:55:27.727 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 52
2025-07-20 12:55:27.740 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 12:55:27.816 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-20 12:55:27.848 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-20 12:55:28.115 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-20 12:55:28.168 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-20 12:55:28.336 +03:00 [INF] Access token generated for user 1
2025-07-20 12:55:28.521 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-20 12:55:28.551 +03:00 [INF] User logged in successfully: admin
2025-07-20 12:55:28.563 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 12:55:28.597 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 742.6068ms
2025-07-20 12:55:28.600 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-20 12:55:28.604 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 877.5099ms
2025-07-20 12:56:05.960 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 12:56:06.109 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-20 12:56:06.161 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__ToInt32_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__ToInt32_0 AND [u].[IsActive] = CAST(1 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-20 12:56:06.215 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-20 12:56:06.222 +03:00 [INF] Route matched with {action = "GetRoleById", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetRoleById(Int32) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 12:56:06.254 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__roleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__roleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-20 12:56:06.269 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-20 12:56:06.277 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 12:56:06.287 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api) in 62.8848ms
2025-07-20 12:56:06.289 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.GetRoleById (DoorCompany.Api)'
2025-07-20 12:56:06.291 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 200 null application/json; charset=utf-8 331.0627ms
2025-07-20 12:56:59.547 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - application/json 2306
2025-07-20 12:56:59.555 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-20 12:56:59.560 +03:00 [INF] Route matched with {action = "AddAndRemoveRolePermission", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AddAndRemoveRolePermission(DoorCompany.Service.Dtos.RoleDto.UpdateRolePermissionsDto) on controller DoorCompany.Api.Controllers.RoleController (DoorCompany.Api).
2025-07-20 12:56:59.604 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__updateRolePermissionsDto_RoleId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r1].[Id], [r1].[CreatedAt], [r1].[CreatedBy], [r1].[Description], [r1].[IsActive], [r1].[IsDeleted], [r1].[Name], [r1].[UpdatedAt], [r1].[UpdatedBy], [s].[Id], [s].[PermissionId], [s].[RoleId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [Roles] AS [r]
    WHERE [r].[Id] = @__updateRolePermissionsDto_RoleId_0 AND [r].[IsDeleted] = CAST(0 AS bit)
) AS [r1]
LEFT JOIN (
    SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [RolePermissions] AS [r0]
    INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
) AS [s] ON [r1].[Id] = [s].[RoleId]
ORDER BY [r1].[Id], [s].[Id]
2025-07-20 12:56:59.666 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = Int32), @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p0;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p1;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p2;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p3;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p4;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p5;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p6;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p7;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p8;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p9;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p10;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p11;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p12;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p13;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p14;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p15;
DELETE FROM [RolePermissions]
OUTPUT 1
WHERE [Id] = @p16;
2025-07-20 12:56:59.703 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = Int32), @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (DbType = Int32), @p18='?' (DbType = Int32), @p19='?' (DbType = Int32), @p20='?' (DbType = Int32), @p21='?' (DbType = Int32), @p22='?' (DbType = Int32), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = Int32), @p29='?' (DbType = Int32), @p30='?' (DbType = Int32), @p31='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [RolePermissions] USING (
VALUES (@p0, @p1, 0),
(@p2, @p3, 1),
(@p4, @p5, 2),
(@p6, @p7, 3),
(@p8, @p9, 4),
(@p10, @p11, 5),
(@p12, @p13, 6),
(@p14, @p15, 7),
(@p16, @p17, 8),
(@p18, @p19, 9),
(@p20, @p21, 10),
(@p22, @p23, 11),
(@p24, @p25, 12),
(@p26, @p27, 13),
(@p28, @p29, 14),
(@p30, @p31, 15)) AS i ([PermissionId], [RoleId], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([PermissionId], [RoleId])
VALUES (i.[PermissionId], i.[RoleId])
OUTPUT INSERTED.[Id], i._Position;
2025-07-20 12:56:59.711 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.RoleDto.RolePermissionsDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 12:56:59.713 +03:00 [INF] Executed action DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api) in 150.2489ms
2025-07-20 12:56:59.714 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.RoleController.AddAndRemoveRolePermission (DoorCompany.Api)'
2025-07-20 12:56:59.716 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Role/add-Remove-Role-Permmision - 200 null application/json; charset=utf-8 170.0502ms
2025-07-20 12:57:12.627 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Role/1 - null null
2025-07-20 12:57:12.679 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[@__ToInt32_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__ToInt32_0 AND [u].[IsActive] = CAST(1 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-20 12:57:12.684 +03:00 [INF] Authorization failed. These requirements were not met:
DoorCompany.Api.Filters.PermissionRequirement
2025-07-20 12:57:12.688 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-07-20 12:57:12.693 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Role/1 - 403 0 null 65.8468ms
2025-07-20 13:05:19.439 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 13:05:19.532 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 13:05:19.686 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 13:05:19.774 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 13:05:19.814 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 13:05:19.827 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 13:05:19.837 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 13:05:19.997 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 13:05:20.167 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 13:05:20.324 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:05:20.326 +03:00 [INF] Hosting environment: Development
2025-07-20 13:05:20.327 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 13:05:20.701 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 13:05:20.923 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 232.1321ms
2025-07-20 13:05:20.939 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 13:05:20.946 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.0571ms
2025-07-20 13:05:20.952 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 13:05:20.991 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.6419ms
2025-07-20 13:05:21.149 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 13:05:21.168 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 18.8508ms
2025-07-20 13:05:29.802 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - null null
2025-07-20 13:05:29.813 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 13:05:29.878 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-20 13:05:29.905 +03:00 [INF] Route matched with {action = "GetAllPermissons", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPermissons() on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 13:05:30.049 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
2025-07-20 13:05:30.068 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PermissionDto.PermissionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 13:05:30.093 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api) in 181.6403ms
2025-07-20 13:05:30.096 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetAllPermissons (DoorCompany.Api)'
2025-07-20 13:05:30.101 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/all-Permission - 200 null application/json; charset=utf-8 299.3912ms
2025-07-20 13:07:46.089 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - null null
2025-07-20 13:07:46.103 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-20 13:07:46.112 +03:00 [INF] Route matched with {action = "GetAllUserRoles", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUserRolesAsync() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-20 13:07:46.287 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-20 13:07:46.303 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.RoleDto.UserResponseWithRolesListDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 13:07:46.309 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api) in 193.8563ms
2025-07-20 13:07:46.311 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUserRolesAsync (DoorCompany.Api)'
2025-07-20 13:07:46.314 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/All-UserRoles - 200 null application/json; charset=utf-8 225.3927ms
2025-07-20 15:48:24.274 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 15:48:24.383 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 15:48:24.626 +03:00 [INF] Executed DbCommand (111ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 15:48:24.733 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 15:48:24.784 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 15:48:24.798 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 15:48:24.806 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 15:48:24.966 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 15:48:25.213 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 15:48:25.317 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:48:25.318 +03:00 [INF] Hosting environment: Development
2025-07-20 15:48:25.319 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 15:48:25.816 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 15:48:26.063 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 255.3465ms
2025-07-20 15:48:26.094 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 15:48:26.101 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.5191ms
2025-07-20 15:48:26.392 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 15:48:26.431 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.6516ms
2025-07-20 15:48:26.593 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 15:48:26.620 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 26.3734ms
2025-07-20 15:49:45.410 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/TestFolder/Partners - null null
2025-07-20 15:49:45.440 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 15:49:46.635 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 15:49:46.664 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 15:50:13.547 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 15:50:13.554 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 26883.5073ms
2025-07-20 15:50:13.556 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 15:50:13.558 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/TestFolder/Partners - 200 null text/plain; charset=utf-8 28148.1227ms
2025-07-20 15:51:00.663 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/TestFolder/Partners - null null
2025-07-20 15:51:00.676 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 15:51:00.681 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 15:51:11.966 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 15:51:11.969 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 11285.8667ms
2025-07-20 15:51:11.971 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 15:51:11.973 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/TestFolder/Partners - 200 null text/plain; charset=utf-8 11309.4958ms
2025-07-20 15:55:51.312 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 15:55:51.388 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 15:55:51.538 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 15:55:51.627 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 15:55:51.669 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 15:55:51.683 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 15:55:51.690 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 15:55:51.817 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 15:55:51.975 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 15:55:52.083 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:55:52.086 +03:00 [INF] Hosting environment: Development
2025-07-20 15:55:52.088 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 15:55:52.284 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 15:55:52.496 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 221.778ms
2025-07-20 15:55:52.522 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 15:55:52.525 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 15:55:52.529 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.1312ms
2025-07-20 15:55:52.567 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.9495ms
2025-07-20 15:55:52.734 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 15:55:52.754 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.9537ms
2025-07-20 15:56:11.120 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/TestFolder/Partners - null null
2025-07-20 15:56:11.132 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 15:56:11.206 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 15:56:11.234 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 15:56:11.394 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 15:56:11.401 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 159.7484ms
2025-07-20 15:56:11.403 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 15:56:11.408 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/TestFolder/Partners - 200 null text/plain; charset=utf-8 288.2485ms
2025-07-20 16:02:31.446 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:02:31.525 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:02:31.671 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:02:31.760 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:02:31.805 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:02:31.816 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:02:31.825 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:02:31.955 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:02:32.134 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:02:32.241 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:02:32.243 +03:00 [INF] Hosting environment: Development
2025-07-20 16:02:32.244 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:02:32.582 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:02:32.733 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Patterns.RoutePatternException: There is an incomplete parameter in the route template. Check that each '{' character has a matching '}' character.
   at Microsoft.AspNetCore.Routing.Patterns.RoutePatternParser.Parse(String pattern)
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointFactory.AddEndpoints(List`1 endpoints, HashSet`1 routeNames, ActionDescriptor action, IReadOnlyList`1 routes, IReadOnlyList`1 conventions, IReadOnlyList`1 groupConventions, IReadOnlyList`1 finallyConventions, IReadOnlyList`1 groupFinallyConventions, Boolean createInertEndpoints, RoutePattern groupPrefix)
   at Microsoft.AspNetCore.Mvc.Routing.ControllerActionEndpointDataSource.CreateEndpoints(RoutePattern groupPrefix, IReadOnlyList`1 actions, IReadOnlyList`1 conventions, IReadOnlyList`1 groupConventions, IReadOnlyList`1 finallyConventions, IReadOnlyList`1 groupFinallyConventions)
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()
   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)
   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()
   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()
   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)
   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-20 16:02:32.792 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 500 null text/html; charset=utf-8 220.0697ms
2025-07-20 16:02:32.815 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:02:32.815 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:02:32.825 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.3798ms
2025-07-20 16:02:32.868 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 53.1476ms
2025-07-20 16:02:33.021 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/favicon.ico - null null
2025-07-20 16:02:33.027 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Patterns.RoutePatternException: There is an incomplete parameter in the route template. Check that each '{' character has a matching '}' character.
   at Microsoft.AspNetCore.Routing.Patterns.RoutePatternParser.Parse(String pattern)
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointFactory.AddEndpoints(List`1 endpoints, HashSet`1 routeNames, ActionDescriptor action, IReadOnlyList`1 routes, IReadOnlyList`1 conventions, IReadOnlyList`1 groupConventions, IReadOnlyList`1 finallyConventions, IReadOnlyList`1 groupFinallyConventions, Boolean createInertEndpoints, RoutePattern groupPrefix)
   at Microsoft.AspNetCore.Mvc.Routing.ControllerActionEndpointDataSource.CreateEndpoints(RoutePattern groupPrefix, IReadOnlyList`1 actions, IReadOnlyList`1 conventions, IReadOnlyList`1 groupConventions, IReadOnlyList`1 finallyConventions, IReadOnlyList`1 groupFinallyConventions)
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()
   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)
   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()
   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()
   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)
   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-20 16:02:33.037 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/favicon.ico - 500 null text/plain; charset=utf-8 15.6459ms
2025-07-20 16:02:36.662 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:02:36.674 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Patterns.RoutePatternException: There is an incomplete parameter in the route template. Check that each '{' character has a matching '}' character.
   at Microsoft.AspNetCore.Routing.Patterns.RoutePatternParser.Parse(String pattern)
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointFactory.AddEndpoints(List`1 endpoints, HashSet`1 routeNames, ActionDescriptor action, IReadOnlyList`1 routes, IReadOnlyList`1 conventions, IReadOnlyList`1 groupConventions, IReadOnlyList`1 finallyConventions, IReadOnlyList`1 groupFinallyConventions, Boolean createInertEndpoints, RoutePattern groupPrefix)
   at Microsoft.AspNetCore.Mvc.Routing.ControllerActionEndpointDataSource.CreateEndpoints(RoutePattern groupPrefix, IReadOnlyList`1 actions, IReadOnlyList`1 conventions, IReadOnlyList`1 groupConventions, IReadOnlyList`1 finallyConventions, IReadOnlyList`1 groupFinallyConventions)
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()
   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)
   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()
   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()
   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)
   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-20 16:02:36.684 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 500 null text/html; charset=utf-8 21.5887ms
2025-07-20 16:02:36.721 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:02:36.721 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:02:36.727 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 5.269ms
2025-07-20 16:02:36.761 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.6706ms
2025-07-20 16:02:36.805 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/favicon.ico - null null
2025-07-20 16:02:36.812 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Patterns.RoutePatternException: There is an incomplete parameter in the route template. Check that each '{' character has a matching '}' character.
   at Microsoft.AspNetCore.Routing.Patterns.RoutePatternParser.Parse(String pattern)
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointFactory.AddEndpoints(List`1 endpoints, HashSet`1 routeNames, ActionDescriptor action, IReadOnlyList`1 routes, IReadOnlyList`1 conventions, IReadOnlyList`1 groupConventions, IReadOnlyList`1 finallyConventions, IReadOnlyList`1 groupFinallyConventions, Boolean createInertEndpoints, RoutePattern groupPrefix)
   at Microsoft.AspNetCore.Mvc.Routing.ControllerActionEndpointDataSource.CreateEndpoints(RoutePattern groupPrefix, IReadOnlyList`1 actions, IReadOnlyList`1 conventions, IReadOnlyList`1 groupConventions, IReadOnlyList`1 finallyConventions, IReadOnlyList`1 groupFinallyConventions)
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.UpdateEndpoints()
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.Initialize()
   at Microsoft.AspNetCore.Mvc.Routing.ActionEndpointDataSourceBase.GetChangeToken()
   at Microsoft.Extensions.Primitives.ChangeToken.ChangeTokenRegistration`1..ctor(Func`1 changeTokenProducer, Action`1 changeTokenConsumer, TState state)
   at Microsoft.Extensions.Primitives.ChangeToken.OnChange(Func`1 changeTokenProducer, Action changeTokenConsumer)
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateChangeTokenUnsynchronized(Boolean collectionChanged)
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureChangeTokenInitialized()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken()
   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()
   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)
   at Microsoft.AspNetCore.Routing.Matching.DataSourceDependentMatcher..ctor(EndpointDataSource dataSource, Lifetime lifetime, Func`1 matcherBuilderFactory)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcherFactory.CreateMatcher(EndpointDataSource dataSource)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.InitializeCoreAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatcher|10_0(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task`1 matcherTask)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-20 16:02:36.819 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/favicon.ico - 500 null text/plain; charset=utf-8 14.5478ms
2025-07-20 16:02:56.629 +03:00 [INF] Executed DbCommand (96ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:02:56.713 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:02:56.857 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:02:56.955 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:02:56.996 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:02:57.007 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:02:57.015 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:02:57.152 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:02:57.309 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:02:57.430 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:02:57.437 +03:00 [INF] Hosting environment: Development
2025-07-20 16:02:57.438 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:02:57.841 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:02:58.054 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 222.3884ms
2025-07-20 16:02:58.081 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:02:58.081 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:02:58.090 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.4681ms
2025-07-20 16:02:58.130 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 49.4042ms
2025-07-20 16:02:58.302 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:02:58.327 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 25.7848ms
2025-07-20 16:04:53.676 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:04:53.763 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:04:53.944 +03:00 [INF] Executed DbCommand (40ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:04:54.087 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:04:54.139 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:04:54.150 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:04:54.157 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:04:54.319 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:04:54.507 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:04:54.654 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:04:54.658 +03:00 [INF] Hosting environment: Development
2025-07-20 16:04:54.659 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:04:54.907 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:04:55.198 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 300.5716ms
2025-07-20 16:04:55.220 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:04:55.228 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:04:55.229 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.267ms
2025-07-20 16:04:55.278 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 49.7651ms
2025-07-20 16:04:55.445 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:04:55.475 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 30.2387ms
2025-07-20 16:09:32.819 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:09:32.896 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:09:33.044 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:09:33.135 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:09:33.180 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:09:33.191 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:09:33.198 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:09:33.339 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:09:33.508 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:09:33.620 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:09:33.622 +03:00 [INF] Hosting environment: Development
2025-07-20 16:09:33.623 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:09:34.166 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:09:34.388 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 232.2724ms
2025-07-20 16:09:34.412 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:09:34.416 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:09:34.420 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.465ms
2025-07-20 16:09:34.452 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 36.3771ms
2025-07-20 16:09:34.619 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:09:34.635 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api). See inner exception
 ---> System.ArgumentException: An item with the same key has already been added. Key: file
   at System.Collections.Generic.Dictionary`2.TryInsert(TKey key, TValue value, InsertionBehavior behavior)
   at System.Collections.Generic.Dictionary`2.Add(TKey key, TValue value)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchemaFromFormParameters(IEnumerable`1 formParameters, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateRequestBodyFromFormParameters(ApiDescription apiDescription, SchemaRepository schemaRepository, IEnumerable`1 formParameters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateRequestBody(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperation(ApiDescription apiDescription, SchemaRepository schemaRepository)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperation(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-20 16:09:34.648 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 29.6978ms
2025-07-20 16:10:00.840 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:10:00.923 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:10:01.062 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:10:01.165 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:10:01.209 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:10:01.221 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:10:01.229 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:10:01.373 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:10:01.558 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:10:01.664 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:10:01.666 +03:00 [INF] Hosting environment: Development
2025-07-20 16:10:01.667 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:10:01.911 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:10:02.135 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 234.2085ms
2025-07-20 16:10:02.163 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:10:02.167 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:10:02.176 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 13.1027ms
2025-07-20 16:10:02.211 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.969ms
2025-07-20 16:10:02.366 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:10:02.390 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 24.6767ms
2025-07-20 16:12:48.839 +03:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:12:48.954 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:12:49.109 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:12:49.200 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:12:49.246 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:12:49.259 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:12:49.267 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:12:49.402 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:12:49.556 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:12:49.668 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:12:49.670 +03:00 [INF] Hosting environment: Development
2025-07-20 16:12:49.671 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:12:50.397 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:12:50.608 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 220.0694ms
2025-07-20 16:12:50.630 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:12:50.634 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:12:50.639 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.288ms
2025-07-20 16:12:50.676 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.0369ms
2025-07-20 16:12:50.836 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:12:50.861 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 25.1058ms
2025-07-20 16:13:28.503 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:13:28.581 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:13:28.731 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:13:28.829 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:13:28.871 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:13:28.883 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:13:28.892 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:13:29.022 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:13:29.178 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:13:29.303 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:13:29.305 +03:00 [INF] Hosting environment: Development
2025-07-20 16:13:29.306 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:13:29.495 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:13:29.702 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 216.8364ms
2025-07-20 16:13:29.717 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:13:29.722 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 5.4127ms
2025-07-20 16:13:29.729 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:13:29.768 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.0753ms
2025-07-20 16:13:29.959 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:13:29.985 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 25.6929ms
2025-07-20 16:13:49.308 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/TestFolder - null null
2025-07-20 16:13:49.338 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 16:13:49.435 +03:00 [INF] Executing endpoint '405 HTTP Method Not Supported'
2025-07-20 16:13:49.436 +03:00 [INF] Executed endpoint '405 HTTP Method Not Supported'
2025-07-20 16:13:49.438 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/TestFolder - 405 0 null 130.1821ms
2025-07-20 16:13:57.390 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/ - null null
2025-07-20 16:13:57.398 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/ - 404 0 null 8.6391ms
2025-07-20 16:13:57.404 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/, Response status code: 404
2025-07-20 16:14:12.022 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger - null null
2025-07-20 16:14:12.028 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger - 301 0 null 6.2789ms
2025-07-20 16:14:12.032 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:14:12.040 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 7.949ms
2025-07-20 16:14:12.050 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/swagger-ui.css - null null
2025-07-20 16:14:12.052 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/swagger-ui-bundle.js - null null
2025-07-20 16:14:12.057 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/swagger-ui-standalone-preset.js - null null
2025-07-20 16:14:12.057 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:14:12.071 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 14.2246ms
2025-07-20 16:14:12.079 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:14:12.149 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 69.2156ms
2025-07-20 16:14:12.158 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-07-20 16:14:12.158 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-07-20 16:14:12.158 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-07-20 16:14:12.165 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/swagger-ui-standalone-preset.js - 200 230280 text/javascript 107.3264ms
2025-07-20 16:14:12.165 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/swagger-ui.css - 200 152034 text/css 114.6505ms
2025-07-20 16:14:12.166 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/swagger-ui-bundle.js - 200 1456926 text/javascript 114.2413ms
2025-07-20 16:14:12.347 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:14:12.364 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.1726ms
2025-07-20 16:14:12.371 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/favicon-32x32.png - null null
2025-07-20 16:14:12.375 +03:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-07-20 16:14:12.377 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/favicon-32x32.png - 200 628 image/png 5.3236ms
2025-07-20 16:14:55.973 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - multipart/form-data; boundary=----WebKitFormBoundary7a8xKhmKsNbSMByr 34783
2025-07-20 16:14:55.981 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 16:14:56.013 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 16:15:12.263 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 16:15:12.270 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 16250.6569ms
2025-07-20 16:15:12.272 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 16:15:12.274 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - 200 null text/plain; charset=utf-8 16301.5914ms
2025-07-20 16:36:15.316 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:36:15.396 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:36:15.561 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:36:15.653 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:36:15.696 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:36:15.707 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:36:15.715 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:36:15.872 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:36:16.067 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:36:16.296 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:36:16.298 +03:00 [INF] Hosting environment: Development
2025-07-20 16:36:16.298 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:36:16.821 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:36:17.044 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 233.804ms
2025-07-20 16:36:17.069 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:36:17.072 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:36:17.076 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.1523ms
2025-07-20 16:36:17.117 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 44.8182ms
2025-07-20 16:36:17.280 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:36:17.302 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 22.0138ms
2025-07-20 16:36:23.667 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:36:23.678 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 11.3193ms
2025-07-20 16:36:23.689 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:36:23.689 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:36:23.692 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 3.1831ms
2025-07-20 16:36:23.707 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 17.7263ms
2025-07-20 16:36:23.919 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:36:23.938 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.8091ms
2025-07-20 16:36:52.039 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - multipart/form-data; boundary=----WebKitFormBoundaryTqfQI4n354BSlIAj 34783
2025-07-20 16:36:52.060 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 16:36:52.128 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 16:36:52.154 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 16:37:28.345 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 16:37:28.351 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 36192.2304ms
2025-07-20 16:37:28.354 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 16:37:28.357 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - 200 null text/plain; charset=utf-8 36317.7901ms
2025-07-20 16:43:23.981 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:43:24.059 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:43:24.211 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:43:24.313 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:43:24.358 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:43:24.371 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:43:24.380 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:43:24.511 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:43:24.681 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:43:24.802 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:43:24.804 +03:00 [INF] Hosting environment: Development
2025-07-20 16:43:24.805 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:43:25.091 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:43:25.310 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 230.712ms
2025-07-20 16:43:25.339 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:43:25.342 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:43:25.347 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.5884ms
2025-07-20 16:43:25.392 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 50.3346ms
2025-07-20 16:43:25.560 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:43:25.579 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 18.9011ms
2025-07-20 16:43:35.431 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:43:35.442 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 10.8983ms
2025-07-20 16:43:35.456 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:43:35.456 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:43:35.472 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 15.9657ms
2025-07-20 16:43:35.483 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 26.9772ms
2025-07-20 16:43:35.696 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:43:35.715 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 18.8305ms
2025-07-20 16:43:53.139 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - multipart/form-data; boundary=----WebKitFormBoundary3fUT9QxEvN7fMXUV 34783
2025-07-20 16:43:53.153 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 16:43:53.228 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 16:43:53.255 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 16:44:11.249 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 16:44:11.255 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 17993.1293ms
2025-07-20 16:44:11.257 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 16:44:11.260 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - 200 null text/plain; charset=utf-8 18121.3191ms
2025-07-20 16:45:48.461 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:45:48.539 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:45:48.704 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:45:48.799 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:45:48.847 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:45:48.863 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:45:48.871 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:45:49.009 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:45:49.177 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:45:49.290 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:45:49.292 +03:00 [INF] Hosting environment: Development
2025-07-20 16:45:49.293 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:45:49.501 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:45:49.719 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 226.0545ms
2025-07-20 16:45:49.748 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:45:49.753 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:45:49.767 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 19.1978ms
2025-07-20 16:45:49.800 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.5739ms
2025-07-20 16:45:49.970 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:45:49.995 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 25.1403ms
2025-07-20 16:45:52.267 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:45:52.278 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 11.1559ms
2025-07-20 16:45:52.290 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:45:52.290 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:45:52.312 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 21.9702ms
2025-07-20 16:45:52.325 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 35.1363ms
2025-07-20 16:45:52.534 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:45:52.565 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 31.4075ms
2025-07-20 16:46:07.310 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - multipart/form-data; boundary=----WebKitFormBoundaryVaGpuLJNQT6CUwNx 34783
2025-07-20 16:46:07.332 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 16:46:07.397 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 16:46:07.424 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 16:46:30.137 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 16:46:30.145 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 22714.9175ms
2025-07-20 16:46:30.148 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 16:46:30.154 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - 200 null text/plain; charset=utf-8 22843.9543ms
2025-07-20 16:50:34.944 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:50:35.022 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:50:35.184 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 16:50:35.273 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 16:50:35.314 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 16:50:35.326 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:50:35.334 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 16:50:35.464 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 16:50:35.620 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 16:50:35.748 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:50:35.749 +03:00 [INF] Hosting environment: Development
2025-07-20 16:50:35.751 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 16:50:36.063 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 16:50:36.270 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 215.516ms
2025-07-20 16:50:36.295 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 16:50:36.296 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 16:50:36.303 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.8725ms
2025-07-20 16:50:36.338 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.57ms
2025-07-20 16:50:36.511 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 16:50:36.533 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 22.2546ms
2025-07-20 17:10:47.102 +03:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:10:47.180 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:10:47.342 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 17:10:47.442 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 17:10:47.486 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 17:10:47.499 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:10:47.508 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:10:47.649 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 17:10:47.837 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 17:10:48.037 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:10:48.161 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 17:10:48.163 +03:00 [INF] Hosting environment: Development
2025-07-20 17:10:48.164 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 17:10:48.272 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 243.6218ms
2025-07-20 17:10:48.306 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:10:48.306 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:10:48.323 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 15.238ms
2025-07-20 17:10:48.355 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 49.0512ms
2025-07-20 17:10:48.521 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:10:48.551 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 29.6596ms
2025-07-20 17:13:49.225 +03:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:13:49.300 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:13:49.446 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 17:13:49.533 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 17:13:49.578 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 17:13:49.591 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:13:49.599 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:13:49.727 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 17:13:49.885 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 17:13:49.986 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 17:13:49.988 +03:00 [INF] Hosting environment: Development
2025-07-20 17:13:49.989 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 17:13:50.393 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:13:50.603 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 219.2651ms
2025-07-20 17:13:50.631 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:13:50.631 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:13:50.641 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 11.3631ms
2025-07-20 17:13:50.673 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.7268ms
2025-07-20 17:13:50.839 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:13:50.858 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.247ms
2025-07-20 17:15:30.068 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:15:30.145 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:15:30.313 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 17:15:30.400 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 17:15:30.442 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 17:15:30.454 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:15:30.462 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:15:30.594 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 17:15:30.748 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 17:15:30.875 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 17:15:30.877 +03:00 [INF] Hosting environment: Development
2025-07-20 17:15:30.878 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 17:15:31.060 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:15:31.274 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 222.0417ms
2025-07-20 17:15:31.302 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:15:31.303 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:15:31.310 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.6472ms
2025-07-20 17:15:31.349 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 46.6912ms
2025-07-20 17:15:31.513 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:15:31.538 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 25.2138ms
2025-07-20 17:16:22.863 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:16:22.974 +03:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:16:23.121 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 17:16:23.228 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 17:16:23.286 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 17:16:23.312 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:16:23.330 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:16:23.479 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 17:16:23.655 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 17:16:23.813 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 17:16:23.816 +03:00 [INF] Hosting environment: Development
2025-07-20 17:16:23.817 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 17:16:23.898 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:16:24.129 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 243.4999ms
2025-07-20 17:16:24.147 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:16:24.156 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.3993ms
2025-07-20 17:16:24.167 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:16:24.215 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.863ms
2025-07-20 17:16:24.414 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:16:24.447 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 32.5196ms
2025-07-20 17:16:45.423 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 52
2025-07-20 17:16:45.444 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 17:16:45.519 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-20 17:16:45.547 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-20 17:16:45.822 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-20 17:16:45.885 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-20 17:16:46.112 +03:00 [INF] Access token generated for user 1
2025-07-20 17:16:46.301 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-20 17:16:46.331 +03:00 [INF] User logged in successfully: admin
2025-07-20 17:16:46.343 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 17:16:46.381 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 826.8865ms
2025-07-20 17:16:46.384 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-20 17:16:46.388 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 964.559ms
2025-07-20 17:20:47.389 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:20:47.487 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:20:47.656 +03:00 [INF] Executed DbCommand (45ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 17:20:47.768 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 17:20:47.809 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 17:20:47.822 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:20:47.831 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:20:47.988 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 17:20:48.176 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 17:20:48.286 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 17:20:48.287 +03:00 [INF] Hosting environment: Development
2025-07-20 17:20:48.288 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 17:20:49.036 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:20:49.248 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 222.0065ms
2025-07-20 17:20:49.274 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:20:49.279 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:20:49.285 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.4381ms
2025-07-20 17:20:49.326 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 46.9959ms
2025-07-20 17:20:49.486 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:20:49.513 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 27.3148ms
2025-07-20 17:22:37.351 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:22:37.425 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:22:37.577 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 17:22:37.664 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 17:22:37.705 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 17:22:37.717 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:22:37.725 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:22:37.861 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 17:22:38.030 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 17:22:38.145 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 17:22:38.147 +03:00 [INF] Hosting environment: Development
2025-07-20 17:22:38.148 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 17:22:38.412 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:22:38.625 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 222.9922ms
2025-07-20 17:22:38.640 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:22:38.647 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.4484ms
2025-07-20 17:22:38.658 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:22:38.700 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.368ms
2025-07-20 17:22:38.854 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:22:38.873 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 18.8494ms
2025-07-20 17:24:41.950 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:24:42.031 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:24:42.183 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 17:24:42.274 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 17:24:42.315 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 17:24:42.326 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:24:42.337 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:24:42.468 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 17:24:42.628 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 17:24:42.755 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 17:24:42.757 +03:00 [INF] Hosting environment: Development
2025-07-20 17:24:42.758 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 17:24:43.227 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:24:43.448 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 231.2904ms
2025-07-20 17:24:43.475 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:24:43.479 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:24:43.488 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 13.3231ms
2025-07-20 17:24:43.527 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 48.0793ms
2025-07-20 17:24:43.688 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:24:43.710 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 22.1241ms
2025-07-20 17:33:19.469 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:33:19.558 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:33:19.757 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 17:33:19.853 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 17:33:19.898 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 17:33:19.910 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:33:19.918 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:33:20.067 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 17:33:20.248 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 17:33:20.370 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 17:33:20.372 +03:00 [INF] Hosting environment: Development
2025-07-20 17:33:20.374 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 17:33:20.778 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:33:20.996 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 227.5746ms
2025-07-20 17:33:21.029 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:33:21.029 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:33:21.038 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.603ms
2025-07-20 17:33:21.080 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 51.7759ms
2025-07-20 17:33:21.249 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:33:21.276 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 27.0921ms
2025-07-20 17:33:26.388 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:33:26.399 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 11.5059ms
2025-07-20 17:33:26.410 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:33:26.410 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:33:26.422 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 11.955ms
2025-07-20 17:33:26.432 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 22.754ms
2025-07-20 17:33:26.649 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:33:26.669 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.4303ms
2025-07-20 17:33:57.680 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - multipart/form-data; boundary=----WebKitFormBoundarymEGibmC2gzJXPXR7 179207
2025-07-20 17:33:57.702 +03:00 [INF] CORS policy execution successful.
2025-07-20 17:33:57.705 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 17:33:57.786 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 17:33:57.817 +03:00 [INF] Route matched with {action = "CreatePermission", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePermissionAsync(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 17:33:58.000 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 17:33:58.018 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api) in 194.215ms
2025-07-20 17:33:58.021 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.CreatePermissionAsync (DoorCompany.Api)'
2025-07-20 17:33:58.027 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Permission/TestFolder - 200 null application/json; charset=utf-8 347.1329ms
2025-07-20 17:38:01.154 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:38:01.228 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:38:01.375 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 17:38:01.465 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 17:38:01.506 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 17:38:01.517 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:38:01.526 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 17:38:01.701 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 17:38:01.913 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 17:38:02.019 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 17:38:02.021 +03:00 [INF] Hosting environment: Development
2025-07-20 17:38:02.022 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 17:38:02.507 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 17:38:02.725 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 227.8546ms
2025-07-20 17:38:02.751 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 17:38:02.755 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 17:38:02.762 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 11.5772ms
2025-07-20 17:38:02.803 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 48.6652ms
2025-07-20 17:38:02.963 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 17:38:02.989 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 26.2647ms
2025-07-20 17:39:11.449 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/Uploads\Partners\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 17:39:11.469 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 17:39:11.547 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:39:11.575 +03:00 [INF] Route matched with {action = "GetImage", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 17:39:11.708 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 17:39:11.717 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api) in 135.3295ms
2025-07-20 17:39:11.720 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:39:11.723 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/Uploads\Partners\869c11828e104c53b24d504b98e00acc.jpg - 200 34492 image/jpeg 277.2603ms
2025-07-20 17:40:11.140 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/\Uploads\Partners\981b70613ea744dfa2f5711d26511325.jpg - null null
2025-07-20 17:40:11.151 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:40:11.154 +03:00 [INF] Route matched with {action = "GetImage", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 17:40:11.172 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 17:40:11.175 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api) in 17.8774ms
2025-07-20 17:40:11.176 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:40:11.177 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/\Uploads\Partners\981b70613ea744dfa2f5711d26511325.jpg - 404 null text/plain; charset=utf-8 39.1492ms
2025-07-20 17:40:17.055 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/Uploads\Partners\981b70613ea744dfa2f5711d26511325.jpg - null null
2025-07-20 17:40:17.062 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:40:17.064 +03:00 [INF] Route matched with {action = "GetImage", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 17:40:17.081 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 17:40:17.099 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api) in 33.8004ms
2025-07-20 17:40:17.102 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:40:17.106 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/Uploads\Partners\981b70613ea744dfa2f5711d26511325.jpg - ********** image/jpeg 50.563ms
2025-07-20 17:40:26.912 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/Uploads\partners\981b70613ea744dfa2f5711d26511325.jpg - null null
2025-07-20 17:40:26.916 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:40:26.917 +03:00 [INF] Route matched with {action = "GetImage", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 17:40:26.921 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 17:40:26.928 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api) in 6.5729ms
2025-07-20 17:40:26.937 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:40:26.938 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/Uploads\partners\981b70613ea744dfa2f5711d26511325.jpg - ********** image/jpeg 26.2365ms
2025-07-20 17:40:34.286 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Permission/uploads\partners\981b70613ea744dfa2f5711d26511325.jpg - null null
2025-07-20 17:40:34.290 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:40:34.291 +03:00 [INF] Route matched with {action = "GetImage", controller = "Permission"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.PermissionController (DoorCompany.Api).
2025-07-20 17:40:34.297 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 17:40:34.302 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api) in 8.1266ms
2025-07-20 17:40:34.304 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PermissionController.GetImage (DoorCompany.Api)'
2025-07-20 17:40:34.307 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Permission/uploads\partners\981b70613ea744dfa2f5711d26511325.jpg - ********** image/jpeg 20.9515ms
2025-07-20 18:08:11.812 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:08:11.926 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:08:12.071 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 18:08:12.171 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 18:08:12.212 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 18:08:12.224 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:08:12.232 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:08:12.378 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 18:08:12.548 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 18:08:12.679 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 18:08:12.681 +03:00 [INF] Hosting environment: Development
2025-07-20 18:08:12.682 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 18:08:12.842 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 18:08:13.068 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 235.5712ms
2025-07-20 18:08:13.095 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 18:08:13.095 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 18:08:13.112 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 16.9889ms
2025-07-20 18:08:13.147 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 51.5707ms
2025-07-20 18:08:13.311 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 18:08:13.336 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 24.3522ms
2025-07-20 18:08:34.065 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-20 18:08:34.085 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 18:08:34.160 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-20 18:08:34.166 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-20 18:08:34.168 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 103.1362ms
2025-07-20 18:09:43.322 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-20 18:09:43.415 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-20 18:09:43.438 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-20 18:09:43.715 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-20 18:09:43.755 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 18:09:43.789 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 344.4597ms
2025-07-20 18:09:43.792 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-20 18:09:43.796 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 474.1355ms
2025-07-20 18:11:24.963 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:11:25.047 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:11:25.204 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 18:11:25.307 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 18:11:25.371 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 18:11:25.385 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:11:25.402 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:11:25.573 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 18:11:25.743 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 18:11:25.854 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 18:11:25.856 +03:00 [INF] Hosting environment: Development
2025-07-20 18:11:25.857 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 18:11:26.287 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 18:11:26.339 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 18:11:26.544 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 202.7455ms
2025-07-20 18:11:26.547 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 18:11:26.547 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 18:11:26.544 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 263.778ms
2025-07-20 18:11:26.562 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 16.5561ms
2025-07-20 18:11:26.584 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 18:11:26.588 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 18:11:26.592 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.8746ms
2025-07-20 18:11:26.605 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 17.1866ms
2025-07-20 18:11:26.605 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 58.3021ms
2025-07-20 18:11:26.815 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 18:11:26.821 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 18:11:26.852 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 30.3259ms
2025-07-20 18:11:26.852 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 36.4424ms
2025-07-20 18:11:36.835 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-20 18:11:36.847 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 18:11:37.001 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-20 18:11:37.022 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-20 18:11:37.274 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-20 18:11:37.312 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 18:11:37.347 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 318.1291ms
2025-07-20 18:11:37.349 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-20 18:11:37.353 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 518.0433ms
2025-07-20 18:12:29.409 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5184/api/User/profile - multipart/form-data; boundary=----WebKitFormBoundaryaQloTN6panBCErVA 179423
2025-07-20 18:12:29.419 +03:00 [INF] CORS policy execution successful.
2025-07-20 18:12:29.430 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.UpdateProfile (DoorCompany.Api)'
2025-07-20 18:12:29.442 +03:00 [INF] Route matched with {action = "UpdateProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateProfile(DoorCompany.Service.Dtos.UserDto.UpdateUserProfileDto) on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-20 18:12:29.575 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 18:12:29.692 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p9='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (DbType = Boolean), @p2='?' (DbType = Boolean), @p3='?' (Size = 4000), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FullName] = @p0, [IsActive] = @p1, [IsDeleted] = @p2, [Password] = @p3, [Phone] = @p4, [ProfileImage] = @p5, [UpdatedAt] = @p6, [UpdatedBy] = @p7, [UserName] = @p8
OUTPUT 1
WHERE [Id] = @p9;
2025-07-20 18:12:29.709 +03:00 [INF] User profile updated successfully: 1
2025-07-20 18:12:29.719 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-20 18:12:29.724 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-20 18:12:29.727 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.UpdateProfile (DoorCompany.Api) in 282.2294ms
2025-07-20 18:12:29.729 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.UpdateProfile (DoorCompany.Api)'
2025-07-20 18:12:29.733 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 324.5104ms
2025-07-20 18:13:00.716 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:13:00.724 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:13:00.736 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:13:00.751 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:13:00.753 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 12.9031ms
2025-07-20 18:13:00.755 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:13:00.756 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 39.8778ms
2025-07-20 18:13:12.488 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:13:12.492 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:13:12.502 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:13:12.509 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:13:12.511 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 3.7796ms
2025-07-20 18:13:12.513 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:13:12.515 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 27.1485ms
2025-07-20 18:13:29.301 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:13:29.305 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:13:29.306 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:13:29.309 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:13:29.312 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 3.0414ms
2025-07-20 18:13:29.313 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:13:29.315 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 13.9301ms
2025-07-20 18:13:41.727 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/%2FUploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:13:41.730 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:13:41.743 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:13:41.750 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:13:41.752 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 4.317ms
2025-07-20 18:13:41.755 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:13:41.756 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/%2FUploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 29.4091ms
2025-07-20 18:14:21.656 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:14:21.661 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:14:21.662 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:14:21.664 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:14:21.666 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 2.3083ms
2025-07-20 18:14:21.673 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:14:21.675 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 24.876ms
2025-07-20 18:15:07.700 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>
2025-07-20 18:15:07.762 +03:00 [INF] Sending file. Request path: '/Uploads/Users/<USER>'. Physical path: 'D:\DoorAPP\DoorCompanyApp\DoorCompany.Api\wwwroot\Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg'
2025-07-20 18:15:07.765 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>/jpeg 65.7819ms
2025-07-20 18:15:07.915 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/favicon.ico - null null
2025-07-20 18:15:07.920 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/favicon.ico - 404 0 null 5.2806ms
2025-07-20 18:15:07.924 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/favicon.ico, Response status code: 404
2025-07-20 18:15:13.999 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:14.002 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:14.004 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:14.007 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:14.009 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 2.6118ms
2025-07-20 18:15:14.011 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:14.012 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 13.7516ms
2025-07-20 18:15:14.737 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:14.740 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:14.741 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:14.746 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:14.748 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 2.4071ms
2025-07-20 18:15:14.749 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:14.752 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 14.9454ms
2025-07-20 18:15:14.927 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:14.932 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:14.933 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:14.938 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:14.939 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 2.061ms
2025-07-20 18:15:14.941 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:14.942 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 14.5465ms
2025-07-20 18:15:15.110 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:15.114 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.115 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:15.121 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:15.123 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 3.1316ms
2025-07-20 18:15:15.125 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.126 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 16.3669ms
2025-07-20 18:15:15.291 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:15.294 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.295 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:15.298 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:15.300 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 2.7137ms
2025-07-20 18:15:15.302 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.304 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 13.3412ms
2025-07-20 18:15:15.461 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:15.466 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.467 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:15.471 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:15.474 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 3.6811ms
2025-07-20 18:15:15.476 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.477 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 15.894ms
2025-07-20 18:15:15.624 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:15.628 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.629 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:15.633 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:15.636 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 3.9344ms
2025-07-20 18:15:15.638 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.639 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 15.2727ms
2025-07-20 18:15:15.810 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:15.814 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.816 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:15.824 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:15.831 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 6.8892ms
2025-07-20 18:15:15.836 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.838 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 27.8379ms
2025-07-20 18:15:15.984 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:15.988 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:15.989 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:16.000 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:16.002 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 4.0521ms
2025-07-20 18:15:16.004 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:16.006 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 21.3359ms
2025-07-20 18:15:16.190 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:16.193 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:16.194 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:16.197 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:16.198 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 2.2219ms
2025-07-20 18:15:16.200 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:16.202 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 11.7493ms
2025-07-20 18:15:17.353 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 18:15:17.361 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 7.3882ms
2025-07-20 18:15:17.372 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 18:15:17.373 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 18:15:17.375 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 2.9618ms
2025-07-20 18:15:17.398 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 25.8489ms
2025-07-20 18:15:17.584 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 18:15:17.602 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 18.2492ms
2025-07-20 18:15:25.456 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:25.460 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:25.461 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:15:25.464 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:15:25.466 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 2.4374ms
2025-07-20 18:15:25.467 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:25.468 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 12.2968ms
2025-07-20 18:15:49.854 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:15:49.863 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:15:49.865 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:16:55.964 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:16:55.974 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 66105.9259ms
2025-07-20 18:16:55.975 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:16:58.244 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 68390.6129ms
2025-07-20 18:17:00.627 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:17:00.631 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:17:00.633 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:17:32.856 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:17:32.858 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 32222.9214ms
2025-07-20 18:17:32.859 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:17:36.055 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 35427.4618ms
2025-07-20 18:18:03.945 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:18:03.949 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:18:03.950 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:18:22.014 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:18:22.016 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 18063.8901ms
2025-07-20 18:18:22.018 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:18:24.084 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 20138.3707ms
2025-07-20 18:18:52.133 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2Fpartners%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:18:52.140 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:18:52.141 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:19:05.791 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:19:05.794 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 13650.8098ms
2025-07-20 18:19:05.795 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:19:07.330 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2Fpartners%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 15197.2439ms
2025-07-20 18:19:41.016 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Partners\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 18:19:41.031 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:19:41.032 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:19:49.550 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 18:19:49.554 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 8519.3219ms
2025-07-20 18:19:49.556 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:19:49.557 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Partners\869c11828e104c53b24d504b98e00acc.jpg - 200 34492 image/jpeg 8546.9246ms
2025-07-20 18:19:57.480 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 18:19:57.484 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:19:57.485 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:20:03.726 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:20:03.728 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 6240.2731ms
2025-07-20 18:20:03.729 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:20:03.731 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - 404 null text/plain; charset=utf-8 6251.3019ms
2025-07-20 18:20:14.839 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/all-users - null null
2025-07-20 18:20:14.843 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-20 18:20:14.849 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-20 18:20:15.003 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
ORDER BY [u].[Id], [s].[Id]
2025-07-20 18:20:15.017 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-20 18:20:15.022 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api) in 170.3443ms
2025-07-20 18:20:15.024 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetAllUsers (DoorCompany.Api)'
2025-07-20 18:20:15.026 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/all-users - 200 null application/json; charset=utf-8 186.5621ms
2025-07-20 18:28:03.815 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 18:28:03.842 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:28:03.843 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:28:29.101 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:28:29.103 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 25256.6631ms
2025-07-20 18:28:29.104 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:28:29.106 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - 404 null text/plain; charset=utf-8 25291.0079ms
2025-07-20 18:30:01.484 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 18:30:01.496 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:30:01.499 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:30:14.100 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:30:14.103 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 12599.6231ms
2025-07-20 18:30:14.104 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:30:14.105 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - 404 null text/plain; charset=utf-8 12622.1182ms
2025-07-20 18:38:02.790 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:38:02.882 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:38:03.091 +03:00 [INF] Executed DbCommand (39ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 18:38:03.214 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 18:38:03.270 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 18:38:03.282 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:38:03.290 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:38:03.425 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 18:38:03.582 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 18:38:03.689 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 18:38:03.691 +03:00 [INF] Hosting environment: Development
2025-07-20 18:38:03.692 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 18:38:04.232 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 18:38:04.441 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 219.3091ms
2025-07-20 18:38:04.457 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 18:38:04.464 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.7726ms
2025-07-20 18:38:04.475 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 18:38:04.518 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.6895ms
2025-07-20 18:38:04.680 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 18:38:04.703 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 23.01ms
2025-07-20 18:38:23.965 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 18:38:23.978 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 18:38:24.059 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:38:24.087 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:38:51.314 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:38:51.320 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 27227.0038ms
2025-07-20 18:38:51.322 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:38:51.325 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - 404 null text/plain; charset=utf-8 27360.1869ms
2025-07-20 18:41:12.631 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 18:41:12.647 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:41:12.650 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:42:59.072 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:42:59.074 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 106421.574ms
2025-07-20 18:42:59.075 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:42:59.990 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - 404 null text/plain; charset=utf-8 107359.6983ms
2025-07-20 18:43:15.217 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 18:43:15.223 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:43:15.225 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:45:07.842 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:45:07.950 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:45:08.115 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 18:45:08.220 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 18:45:08.264 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 18:45:08.276 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:45:08.283 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:45:08.428 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 18:45:08.604 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 18:45:08.711 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 18:45:08.713 +03:00 [INF] Hosting environment: Development
2025-07-20 18:45:08.714 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 18:45:09.108 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 18:45:09.327 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 227.5481ms
2025-07-20 18:45:09.356 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 18:45:09.358 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 18:45:09.367 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 11.0392ms
2025-07-20 18:45:09.402 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 44.3614ms
2025-07-20 18:45:09.560 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 18:45:09.586 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 25.8837ms
2025-07-20 18:45:16.747 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 18:45:16.759 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 18:45:16.845 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:45:16.874 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:47:11.309 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:47:11.316 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 114435.1957ms
2025-07-20 18:47:11.317 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:47:11.320 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - 404 null text/plain; charset=utf-8 114573.3519ms
2025-07-20 18:47:14.174 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\DoorC5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:47:14.184 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:47:14.190 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:47:23.116 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:47:23.119 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 8925.4413ms
2025-07-20 18:47:23.121 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:47:23.123 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\DoorC5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 8948.9369ms
2025-07-20 18:47:34.756 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\C5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:47:34.762 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:47:34.765 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:47:44.461 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:47:44.463 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 9680.4049ms
2025-07-20 18:47:44.464 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:47:46.252 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\C5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 11496.4626ms
2025-07-20 18:48:19.823 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:48:19.827 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:48:19.829 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:48:28.636 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 18:48:28.651 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 8820.3177ms
2025-07-20 18:48:28.654 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:48:28.657 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - ********** image/jpeg 8833.8583ms
2025-07-20 18:48:57.334 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:48:57.342 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:48:57.343 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:50:43.795 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:50:43.803 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 106457.9302ms
2025-07-20 18:50:43.812 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:50:43.816 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 404 null text/plain; charset=utf-8 106482.4707ms
2025-07-20 18:51:05.271 +03:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:51:05.365 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:51:05.521 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 18:51:05.610 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 18:51:05.650 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 18:51:05.661 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:51:05.669 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:51:05.802 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 18:51:05.972 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 18:51:06.098 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 18:51:06.100 +03:00 [INF] Hosting environment: Development
2025-07-20 18:51:06.101 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 18:51:06.392 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 18:51:06.605 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 223.8845ms
2025-07-20 18:51:06.634 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 18:51:06.635 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 18:51:06.647 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 12.5515ms
2025-07-20 18:51:06.683 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.9511ms
2025-07-20 18:51:06.994 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 18:51:07.018 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 23.7344ms
2025-07-20 18:51:16.611 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:51:16.622 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 18:51:16.696 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:51:16.722 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:51:34.777 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 18:51:34.796 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 18068.6949ms
2025-07-20 18:51:34.798 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:51:34.802 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - ********** image/jpeg 18191.1256ms
2025-07-20 18:51:48.172 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:51:48.183 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:51:48.193 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:51:57.672 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 18:51:57.688 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 9489.5989ms
2025-07-20 18:51:57.690 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:51:57.694 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - ********** image/jpeg 9522.8042ms
2025-07-20 18:52:41.223 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:52:41.301 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:52:41.458 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 18:52:41.564 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 18:52:41.603 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 18:52:41.617 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:52:41.626 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 18:52:41.762 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 18:52:41.964 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 18:52:42.067 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 18:52:42.069 +03:00 [INF] Hosting environment: Development
2025-07-20 18:52:42.071 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 18:52:42.697 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 18:52:42.910 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 228.2001ms
2025-07-20 18:52:42.939 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 18:52:42.944 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 18:52:42.954 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 14.1332ms
2025-07-20 18:52:42.990 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 46.0013ms
2025-07-20 18:52:43.258 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 18:52:43.287 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 28.7628ms
2025-07-20 18:52:56.259 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-20 18:52:56.273 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 18:52:56.356 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-20 18:52:56.362 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-20 18:52:56.366 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 107.3007ms
2025-07-20 18:53:04.704 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - null null
2025-07-20 18:53:04.715 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:53:04.748 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:53:14.557 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'System.String'.
2025-07-20 18:53:14.564 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 9808.9149ms
2025-07-20 18:53:14.566 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:53:14.568 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\869c11828e104c53b24d504b98e00acc.jpg - 404 null text/plain; charset=utf-8 9864.4215ms
2025-07-20 18:54:17.223 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:54:17.228 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:54:17.229 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:54:17.245 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 18:54:17.263 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 31.3746ms
2025-07-20 18:54:17.265 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:54:17.271 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users\5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - ********** image/jpeg 47.7246ms
2025-07-20 18:54:30.486 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:54:30.490 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:54:30.491 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:54:30.504 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 18:54:30.511 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 17.3538ms
2025-07-20 18:54:30.513 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:54:30.515 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - ********** image/jpeg 29.6095ms
2025-07-20 18:54:33.835 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:54:33.839 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:54:33.840 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:54:33.845 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 18:54:33.849 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 5.7673ms
2025-07-20 18:54:33.851 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:54:33.853 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads\Users%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - ********** image/jpeg 17.587ms
2025-07-20 18:54:37.497 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 18:54:37.500 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:54:37.502 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 18:54:37.507 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 18:54:37.512 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 7.81ms
2025-07-20 18:54:37.514 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 18:54:37.516 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - ********** image/jpeg 18.7084ms
2025-07-20 19:07:16.521 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 19:07:16.626 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 19:07:16.784 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 19:07:16.879 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 19:07:16.924 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 19:07:16.935 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 19:07:16.944 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 19:07:17.098 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 19:07:17.282 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 19:07:17.454 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 19:07:17.455 +03:00 [INF] Hosting environment: Development
2025-07-20 19:07:17.457 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 19:07:17.626 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 19:07:17.839 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 222.2346ms
2025-07-20 19:07:17.854 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 19:07:17.861 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.1198ms
2025-07-20 19:07:17.874 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 19:07:17.916 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.8571ms
2025-07-20 19:07:18.069 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 19:07:18.093 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 23.5885ms
2025-07-20 19:07:21.755 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 19:07:21.770 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 19:07:21.853 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 19:07:21.883 +03:00 [INF] Route matched with {action = "GetImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 19:07:22.054 +03:00 [INF] Executing FileContentResult, sending file with download name '' ...
2025-07-20 19:07:22.077 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api) in 186.8575ms
2025-07-20 19:07:22.081 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetImage (DoorCompany.Api)'
2025-07-20 19:07:22.087 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/Uploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - ********** image/jpeg 331.819ms
2025-07-20 19:07:39.753 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 19:07:39.773 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)'
2025-07-20 19:07:39.784 +03:00 [INF] Route matched with {action = "GetFullImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFullImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 19:07:39.795 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 19:07:39.797 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api) in 10.9939ms
2025-07-20 19:07:39.800 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)'
2025-07-20 19:07:39.801 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 200 null text/plain; charset=utf-8 48.5507ms
2025-07-20 19:09:21.781 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 19:09:21.869 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 19:09:22.024 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-20 19:09:22.121 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-20 19:09:22.162 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-20 19:09:22.173 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 19:09:22.185 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-20 19:09:22.387 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-20 19:09:22.573 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-20 19:09:22.686 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 19:09:22.688 +03:00 [INF] Hosting environment: Development
2025-07-20 19:09:22.689 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-20 19:09:23.464 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-20 19:09:23.679 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 224.7097ms
2025-07-20 19:09:23.711 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 19:09:23.715 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-20 19:09:23.721 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.4769ms
2025-07-20 19:09:23.757 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.2599ms
2025-07-20 19:09:23.923 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-20 19:09:23.953 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 29.7719ms
2025-07-20 19:09:37.310 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - null null
2025-07-20 19:09:37.335 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-20 19:09:37.420 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)'
2025-07-20 19:09:37.447 +03:00 [INF] Route matched with {action = "GetFullImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFullImage(System.String) on controller DoorCompany.Api.Controllers.ImageController (DoorCompany.Api).
2025-07-20 19:09:37.577 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 19:09:37.584 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api) in 130.8775ms
2025-07-20 19:09:37.586 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ImageController.GetFullImage (DoorCompany.Api)'
2025-07-20 19:09:37.588 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Image/GetFullPathUploads%2FUsers%2F5027a2c7ca054d2c8baeaa18cffa5ac9.jpg - 200 null text/plain; charset=utf-8 278.327ms
