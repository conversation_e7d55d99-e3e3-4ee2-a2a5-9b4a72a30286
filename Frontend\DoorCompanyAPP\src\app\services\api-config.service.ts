import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class ApiConfigService {
 constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  public getApiUrl(): string {
    // القيمة الافتراضية للخادم أو عندما لا يكون window متاحاً
    let apiUrl = 'http://*************:822/api';
     
    if (isPlatformBrowser(this.platformId)) {
      const host = window.location.hostname;
      const port = window.location.port;
      if((host.startsWith('localhost') && port == '4200')) {
     return   apiUrl = 'http://localhost:5184/api';
      } else {
        // إذا لم يكن هناك منفذ محدد، نستخدم المنفذ الافتراضي 80
     
      
      if(host.startsWith('192.168.') || 
         host.startsWith('10.') || 
         host.startsWith('172.') || 
         host.startsWith('localhost')) {
        
        // إذا كان المضيف هو عنوان محلي أو شبكة خاصة
     return   apiUrl = `http://${host}:${port}/api`;

      } else {
        // إذا كان المضيف هو عنوان عام
       apiUrl = `http://${host}:${port}/api`;

      }
    }
  }
    return apiUrl;
  }
}