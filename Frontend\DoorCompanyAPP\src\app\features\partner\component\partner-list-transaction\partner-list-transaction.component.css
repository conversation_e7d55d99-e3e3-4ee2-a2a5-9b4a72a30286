.partners-container {
  padding: 24px;
  max-width: 1400px; 
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 500;
  color: #1976d2;
}

.page-title mat-icon {
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
}

.create-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  font-weight: 500;
}

.filters-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filters-row {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  flex-wrap: wrap;
}

.search-field {
  flex: 1;
  min-width: 250px;
}

.filter-field,
.date-field {
  min-width: 150px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48px;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-container {
  overflow-x: auto;
}

.partners-table {
  width: 100%;
  min-width: 400px;
}

.partners-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #333;
  padding: 16px 12px;
}

.partners-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #e0e0e0;
}

.partners-table tr:hover {
  background-color: #f9f9f9;
}

.invoice-cell .invoice-date {
  font-size: 0.875rem;
  color: #666;
  margin-top: 4px;
}

.amount-cell {
  font-weight: 500;
  color: #2e7d32;
  font-size: 1rem;
}

.payment-info .paid-amount {
  font-weight: 500;
  margin-bottom: 4px;
}

.payment-info .remaining-amount {
  font-size: 0.875rem;
  color: #f57c00;
  margin-top: 4px;
}

.status-paid {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-unpaid {
  background-color: #ffebee;
  color: #c62828;
}

.status-partial {
  background-color: #fff3e0;
  color: #f57c00;
}

.actions-cell {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.actions-cell button {
  min-width: 36px;
  min-height: 36px;
}

.actions-cell button:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.no-data {
  text-align: center;
  padding: 48px 24px;
  color: #666;
}

.no-data mat-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-data p {
  font-size: 1.125rem;
  margin: 0;
}

.image-container{
  text-align: center;
  padding: 1px;
  border: 1px solid #ccc;
  border-radius: 1px;
  max-width: 150px;
  margin: 0 auto;
}
.image-partner{
  object-fit: contain;
  max-width: 149px;
  max-height: 149px;
}

.preview-section{
  text-align: center;
  padding: 1px;
  border: 1px solid #ccc;
  border-radius: 1px;
  max-width: 150px;
  margin: 0 auto;
}

.preview-container img{
  object-fit: contain;
  max-width: 149px;
  max-height: 149px;
}
/* Responsive Design */
 @media (max-width: 768px) {
  .partners-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
  }

  .page-title {
    font-size: 1.5rem;
    justify-content: center;
  }

  .create-button {
    width: 100%;
    justify-content: center;
  }

  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-field,
  .filter-field,
  .date-field {
    min-width: unset;
    width: 100%;
  }

  .filter-actions {
    justify-content: center;
    margin-top: 16px;
  }

  .partners-table {
    min-width: 800px;
  }

  .partners-table .mat-column-representative,
  .partners-table .mat-column-paymentMethod {
    display: none;
  }

  .actions-cell {
    flex-direction: column;
    gap: 2px;
  }
} 
.partner-chip {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.775rem;
  font-weight: 500;
}
@media (max-width: 480px) {
  .partners-table {
    min-width: 600px;
  }

  .partners-table .mat-column-invoiceDate {
    display: none;
  }

  .page-title {
    font-size: 1.25rem;
  }
}

/* RTL Support */
[dir="rtl"] .page-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .page-title {
  flex-direction: row-reverse;
}

[dir="rtl"] .create-button {
  flex-direction: row-reverse;
}

[dir="rtl"] .filters-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .filter-actions {
  flex-direction: row-reverse;
}

[dir="rtl"] .actions-cell {
  flex-direction: row-reverse;
}