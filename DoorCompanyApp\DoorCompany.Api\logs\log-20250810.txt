2025-08-10 13:49:40.432 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ProductInventory'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-10 13:49:40.485 +03:00 [WRN] No store type was specified for the decimal property 'Balance' on entity type 'ProductInventory'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-10 13:50:00.927 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ProductInventory'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-10 13:50:00.962 +03:00 [WRN] No store type was specified for the decimal property 'Balance' on entity type 'ProductInventory'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-10 14:11:15.190 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 14:11:15.258 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-08-10 14:11:15.311 +03:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-08-10 14:11:15.431 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-08-10 14:11:15.441 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 14:11:15.443 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-10 14:11:15.448 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-10 14:11:15.458 +03:00 [INF] Applying migration '20250810104942_addProductInventroy'.
2025-08-10 14:11:15.586 +03:00 [INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ProductInventories] (
    [Id] int NOT NULL IDENTITY,
    [ProductId] int NOT NULL,
    [AverageCost] decimal(18,2) NOT NULL,
    [Balance] decimal(18,2) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ProductInventories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ProductInventories_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);
2025-08-10 14:11:15.589 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_ProductInventories_ProductId] ON [ProductInventories] ([ProductId]);
2025-08-10 14:11:15.593 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250810104942_addProductInventroy', N'9.0.7');
2025-08-10 14:11:15.608 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-08-10 14:13:23.884 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 14:13:24.062 +03:00 [INF] Executed DbCommand (93ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 14:13:24.548 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 14:13:24.575 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 14:13:24.923 +03:00 [INF] Executed DbCommand (138ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 14:13:25.040 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 14:13:25.099 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 14:13:25.113 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 14:13:25.124 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 14:13:25.138 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 14:13:25.150 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 14:13:25.162 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 14:13:25.174 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 14:13:25.455 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 14:13:25.871 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 14:13:26.040 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 14:13:26.050 +03:00 [INF] Hosting environment: Development
2025-08-10 14:13:26.059 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 14:13:26.496 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 14:13:26.834 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 362.0226ms
2025-08-10 14:13:26.862 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 14:13:26.871 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.2624ms
2025-08-10 14:13:27.138 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 14:13:27.199 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 61.1767ms
2025-08-10 14:13:27.362 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 14:13:27.407 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 45.1208ms
2025-08-10 14:13:43.729 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Product/1 - null null
2025-08-10 14:13:43.740 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 14:13:45.034 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.GetProductById (DoorCompany.Api)'
2025-08-10 14:13:45.066 +03:00 [INF] Route matched with {action = "GetProductById", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProductById(Int32) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-10 14:13:45.538 +03:00 [INF] Executed DbCommand (105ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Barcode], [s].[CategoryId], [s].[Code], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[ImagePath], [s].[IsActive], [s].[IsDeleted], [s].[ItemType], [s].[MaximumStock], [s].[MinimumStock], [s].[Name], [s].[SortOrder], [s].[StandardCost], [s].[UnitId], [s].[UpdatedAt], [s].[UpdatedBy], [s].[Id0], [s].[CategoryTypeId], [s].[Code0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description0], [s].[ImageUrl], [s].[IsActive0], [s].[IsDeleted0], [s].[Name0], [s].[ParentCategoryId], [s].[SortOrder0], [s].[Symbol], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[Id1], [s].[CreatedAt1], [s].[CreatedBy1], [s].[Description1], [s].[IsActive1], [s].[IsDeleted1], [s].[Name1], [s].[Symbol0], [s].[UpdatedAt1], [s].[UpdatedBy1], [i0].[Id], [i0].[AverageCost], [i0].[BalanceAfter], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[InvoiceMasterId], [i0].[IsActive], [i0].[IsDeleted], [i0].[ProductId], [i0].[Quantity], [i0].[ReferenceNumber], [i0].[TotalCost], [i0].[TransactionDate], [i0].[Type], [i0].[UnitCost], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [i].[Id] AS [Id0], [i].[CategoryTypeId], [i].[Code] AS [Code0], [i].[CreatedAt] AS [CreatedAt0], [i].[CreatedBy] AS [CreatedBy0], [i].[Description] AS [Description0], [i].[ImageUrl], [i].[IsActive] AS [IsActive0], [i].[IsDeleted] AS [IsDeleted0], [i].[Name] AS [Name0], [i].[ParentCategoryId], [i].[SortOrder] AS [SortOrder0], [i].[Symbol], [i].[UpdatedAt] AS [UpdatedAt0], [i].[UpdatedBy] AS [UpdatedBy0], [u].[Id] AS [Id1], [u].[CreatedAt] AS [CreatedAt1], [u].[CreatedBy] AS [CreatedBy1], [u].[Description] AS [Description1], [u].[IsActive] AS [IsActive1], [u].[IsDeleted] AS [IsDeleted1], [u].[Name] AS [Name1], [u].[Symbol] AS [Symbol0], [u].[UpdatedAt] AS [UpdatedAt1], [u].[UpdatedBy] AS [UpdatedBy1]
    FROM [Products] AS [p]
    INNER JOIN [ItemCategories] AS [i] ON [p].[CategoryId] = [i].[Id]
    INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [s]
LEFT JOIN [InventoryTransactions] AS [i0] ON [s].[Id] = [i0].[ProductId]
ORDER BY [s].[Id], [s].[Id0], [s].[Id1]
2025-08-10 14:13:45.566 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.ProductResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 14:13:45.625 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.GetProductById (DoorCompany.Api) in 553.0566ms
2025-08-10 14:13:45.627 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.GetProductById (DoorCompany.Api)'
2025-08-10 14:13:45.631 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Product/1 - 404 null application/json; charset=utf-8 1902.5979ms
2025-08-10 15:43:47.625 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 15:43:47.790 +03:00 [INF] Executed DbCommand (97ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 15:43:48.108 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:43:48.134 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:43:48.317 +03:00 [INF] Executed DbCommand (39ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 15:43:48.419 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 15:43:48.469 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 15:43:48.484 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:43:48.502 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:43:48.529 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:43:48.549 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:43:48.572 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:43:48.588 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:43:48.743 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 15:43:48.918 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 15:43:49.055 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 15:43:49.057 +03:00 [INF] Hosting environment: Development
2025-08-10 15:43:49.059 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 15:43:49.174 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 15:43:49.373 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 208.4879ms
2025-08-10 15:43:49.389 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 15:43:49.397 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.451ms
2025-08-10 15:43:49.406 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 15:43:49.445 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 38.7783ms
2025-08-10 15:43:49.639 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 15:43:49.702 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 63.8414ms
2025-08-10 15:51:38.716 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 15:51:38.875 +03:00 [INF] Executed DbCommand (99ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 15:51:39.259 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:51:39.284 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:51:39.448 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 15:51:39.547 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 15:51:39.600 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 15:51:39.616 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:51:39.627 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:51:39.650 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:51:39.663 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:51:39.677 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:51:39.690 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 15:51:39.863 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 15:51:40.032 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 15:51:40.148 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 15:51:40.154 +03:00 [INF] Hosting environment: Development
2025-08-10 15:51:40.155 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 15:51:40.548 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 15:51:40.740 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 201.1762ms
2025-08-10 15:51:40.767 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 15:51:40.771 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 15:51:40.774 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.4061ms
2025-08-10 15:51:40.813 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.5384ms
2025-08-10 15:51:41.005 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 15:51:41.061 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 56.4033ms
2025-08-10 17:23:23.677 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 17:23:23.905 +03:00 [INF] Executed DbCommand (171ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 17:23:24.288 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:23:24.314 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:23:24.479 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 17:23:24.576 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 17:23:24.655 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 17:23:24.670 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:23:24.680 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:23:24.694 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:23:24.705 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:23:24.720 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:23:24.731 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:23:24.958 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 17:23:25.138 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 17:23:25.290 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 17:23:25.292 +03:00 [INF] Hosting environment: Development
2025-08-10 17:23:25.293 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 17:23:25.446 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 17:23:25.718 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 282.6567ms
2025-08-10 17:23:25.741 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 17:23:25.744 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 17:23:25.748 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.7396ms
2025-08-10 17:23:25.782 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 37.8675ms
2025-08-10 17:23:25.973 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 17:23:26.058 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 85.8451ms
2025-08-10 17:23:44.410 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Category/1 - null null
2025-08-10 17:23:44.430 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 17:23:44.542 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 17:23:44.569 +03:00 [INF] Route matched with {action = "GetCategoryById", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategoryById(Int32) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 17:23:44.830 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 17:23:44.845 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 17:23:44.875 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api) in 299.6077ms
2025-08-10 17:23:44.877 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 17:23:44.882 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Category/1 - 404 null application/json; charset=utf-8 471.8808ms
2025-08-10 17:24:57.966 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Category - multipart/form-data; boundary=----WebKitFormBoundarynxLcsmsrAbvjY423 1683
2025-08-10 17:24:57.975 +03:00 [INF] CORS policy execution successful.
2025-08-10 17:24:57.996 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:24:58.006 +03:00 [INF] Route matched with {action = "CreateCategory", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateCategoryAsync(DoorCompany.Service.Dtos.ProductDto.CreateCategoryDto) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 17:24:58.140 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Name] = @__createDto_Name_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:24:58.155 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Code] = @__createDto_Code_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:24:58.176 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy]
FROM [ItemCategories] AS [i]
WHERE [i].[Id] = @__p_0
2025-08-10 17:24:58.179 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 17:24:58.182 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api) in 172.8199ms
2025-08-10 17:24:58.184 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:24:58.185 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Category - 400 null application/json; charset=utf-8 219.582ms
2025-08-10 17:25:14.816 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Category - multipart/form-data; boundary=----WebKitFormBoundaryjzEutS44trJqrEvA 1683
2025-08-10 17:25:14.820 +03:00 [INF] CORS policy execution successful.
2025-08-10 17:25:14.823 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:25:14.824 +03:00 [INF] Route matched with {action = "CreateCategory", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateCategoryAsync(DoorCompany.Service.Dtos.ProductDto.CreateCategoryDto) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 17:25:14.871 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Name] = @__createDto_Name_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:25:14.875 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Code] = @__createDto_Code_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:25:14.881 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy]
FROM [ItemCategories] AS [i]
WHERE [i].[Id] = @__p_0
2025-08-10 17:25:14.891 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 17:25:14.892 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api) in 66.3769ms
2025-08-10 17:25:14.894 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:25:14.895 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Category - 400 null application/json; charset=utf-8 79.2638ms
2025-08-10 17:46:16.541 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Category - multipart/form-data; boundary=----WebKitFormBoundarycl1KTm7CsOr5TP1n 1683
2025-08-10 17:46:16.548 +03:00 [INF] CORS policy execution successful.
2025-08-10 17:46:16.549 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:46:16.550 +03:00 [INF] Route matched with {action = "CreateCategory", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateCategoryAsync(DoorCompany.Service.Dtos.ProductDto.CreateCategoryDto) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 17:46:16.806 +03:00 [INF] Executed DbCommand (244ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Name] = @__createDto_Name_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:46:21.564 +03:00 [INF] Executed DbCommand (4,754ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Code] = @__createDto_Code_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:46:21.738 +03:00 [INF] Executed DbCommand (170ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy]
FROM [ItemCategories] AS [i]
WHERE [i].[Id] = @__p_0
2025-08-10 17:46:21.743 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 17:46:21.747 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api) in 5194.4954ms
2025-08-10 17:46:21.749 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:46:21.752 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Category - 400 null application/json; charset=utf-8 5211.8909ms
2025-08-10 17:46:27.660 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Category - multipart/form-data; boundary=----WebKitFormBoundaryrIsTnBBTdAUYvqrX 1683
2025-08-10 17:46:27.664 +03:00 [INF] CORS policy execution successful.
2025-08-10 17:46:27.665 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:46:27.667 +03:00 [INF] Route matched with {action = "CreateCategory", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateCategoryAsync(DoorCompany.Service.Dtos.ProductDto.CreateCategoryDto) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 17:46:27.675 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Name] = @__createDto_Name_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:46:28.209 +03:00 [INF] Executed DbCommand (531ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Code] = @__createDto_Code_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:46:28.426 +03:00 [INF] Executed DbCommand (213ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy]
FROM [ItemCategories] AS [i]
WHERE [i].[Id] = @__p_0
2025-08-10 17:46:28.429 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 17:46:28.432 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api) in 762.0546ms
2025-08-10 17:46:28.434 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:46:28.436 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Category - 400 null application/json; charset=utf-8 775.8952ms
2025-08-10 17:48:25.821 +03:00 [INF] Executed DbCommand (231ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 17:48:45.046 +03:00 [ERR] Failed executing DbCommand (19,172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 17:48:45.206 +03:00 [ERR] حدث خطأ أثناء تهيئة البيانات الأولية
Microsoft.Data.SqlClient.SqlException (0x80131904): A transport-level error has occurred when receiving results from the server. (provider: Session Provider, error: 19 - Physical connection is not usable)
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.ThrowExceptionAndWarning(Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.ReadSniError(TdsParserStateObject stateObj, UInt32 error)
   at Microsoft.Data.SqlClient.TdsParserStateObject.ReadSniSyncOverAsync()
   at Microsoft.Data.SqlClient.TdsParserStateObject.TryReadNetworkPacket()
   at Microsoft.Data.SqlClient.TdsParserStateObject.TryPrepareBuffer()
   at Microsoft.Data.SqlClient.TdsParserStateObject.TryReadByte(Byte& value)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteScalar()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteScalar(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.<HasTables>b__13_0(ISqlServerConnection connection)
   at Microsoft.EntityFrameworkCore.ExecutionStrategyExtensions.<>c__DisplayClass12_0`2.<Execute>b__0(DbContext _, TState s)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass28_0`2.<Execute>b__0(DbContext context, TState state)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementation[TState,TResult](Func`3 operation, Func`3 verifySucceeded, TState state)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ExecutionStrategyExtensions.Execute[TState,TResult](IExecutionStrategy strategy, TState state, Func`2 operation, Func`2 verifySucceeded)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.HasTables()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Program.cs:line 187
ClientConnectionId:0d933385-7739-4e80-98a3-57fd9d0d526b
Error Number:-1,State:0,Class:20
2025-08-10 17:48:45.414 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 17:48:45.578 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 17:48:45.689 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 17:48:45.691 +03:00 [INF] Hosting environment: Development
2025-08-10 17:48:45.700 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 17:48:46.076 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 17:48:46.283 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 217.4366ms
2025-08-10 17:48:46.314 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 17:48:46.315 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 17:48:46.323 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.5675ms
2025-08-10 17:48:46.367 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 52.3447ms
2025-08-10 17:48:46.563 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 17:48:46.603 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 40.5424ms
2025-08-10 17:50:41.841 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Category - multipart/form-data; boundary=----WebKitFormBoundary9kKVgqghqi3Bgevd 1368
2025-08-10 17:50:41.849 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 17:50:41.898 +03:00 [INF] CORS policy execution successful.
2025-08-10 17:50:41.965 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:50:41.996 +03:00 [INF] Route matched with {action = "CreateCategory", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateCategoryAsync(DoorCompany.Service.Dtos.ProductDto.CreateCategoryDto) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 17:50:42.884 +03:00 [INF] Executed DbCommand (77ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Name] = @__createDto_Name_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:50:42.954 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Code] = @__createDto_Code_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 17:50:43.077 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy]
FROM [ItemCategories] AS [i]
WHERE [i].[Id] = @__p_0
2025-08-10 17:50:43.103 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 17:50:43.134 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api) in 1133.0495ms
2025-08-10 17:50:43.137 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 17:50:43.140 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Category - 400 null application/json; charset=utf-8 1299.5922ms
2025-08-10 18:07:55.891 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 18:07:56.044 +03:00 [INF] Executed DbCommand (97ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 18:07:56.361 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:07:56.403 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:07:56.577 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 18:07:56.674 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 18:07:56.735 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 18:07:56.749 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:07:56.760 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:07:56.774 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:07:56.786 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:07:56.797 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:07:56.809 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:07:56.953 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 18:07:57.104 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 18:07:57.235 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 18:07:57.238 +03:00 [INF] Hosting environment: Development
2025-08-10 18:07:57.240 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 18:07:57.417 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 18:07:57.615 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 206.8949ms
2025-08-10 18:07:57.641 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 18:07:57.644 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 18:07:57.648 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.9151ms
2025-08-10 18:07:57.691 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.5333ms
2025-08-10 18:07:57.876 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 18:07:57.914 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 38.1241ms
2025-08-10 18:09:19.469 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Category - multipart/form-data; boundary=----WebKitFormBoundaryQseiDXyt9wIzrDBN 1379
2025-08-10 18:09:19.477 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 18:09:19.520 +03:00 [INF] CORS policy execution successful.
2025-08-10 18:09:19.583 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 18:09:19.613 +03:00 [INF] Route matched with {action = "CreateCategory", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateCategoryAsync(DoorCompany.Service.Dtos.ProductDto.CreateCategoryDto) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:09:19.844 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Name] = @__createDto_Name_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:09:19.858 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Code] = @__createDto_Code_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:09:20.237 +03:00 [INF] Executed DbCommand (52ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (Size = 4000), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (Size = 4000), @p12='?' (DbType = DateTime2), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [ItemCategories] ([CategoryTypeId], [Code], [CreatedAt], [CreatedBy], [Description], [ImageUrl], [IsActive], [IsDeleted], [Name], [ParentCategoryId], [SortOrder], [Symbol], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-08-10 18:09:20.286 +03:00 [INF] Category created successfully: الاصناف
2025-08-10 18:09:20.396 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:09:20.583 +03:00 [ERR] Error creating Category: الاصناف
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Type Map configuration:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Destination Member:
ImageUrl

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_68(ItemCategory src, CategoryResponseDto dest, String destMember, ResolutionContext context) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 92
   at lambda_method570(Closure, Object, CategoryResponseDto, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method570(Closure, Object, CategoryResponseDto, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.ProductService.GetCategoryByIdAsync(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 144
   at DoorCompany.Service.Repositories.Implementations.ProductService.CreateCategoryAsync(CreateCategoryDto createDto) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 193
2025-08-10 18:09:20.632 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 18:09:20.660 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api) in 1040.3647ms
2025-08-10 18:09:20.664 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 18:09:20.668 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Category - 400 null application/json; charset=utf-8 1198.3518ms
2025-08-10 18:09:44.511 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Category - multipart/form-data; boundary=----WebKitFormBoundaryo3vBorQSIhTaZsUa 1379
2025-08-10 18:09:44.522 +03:00 [INF] CORS policy execution successful.
2025-08-10 18:09:44.524 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 18:09:44.526 +03:00 [INF] Route matched with {action = "CreateCategory", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateCategoryAsync(DoorCompany.Service.Dtos.ProductDto.CreateCategoryDto) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:09:44.650 +03:00 [INF] Executed DbCommand (43ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Name] = @__createDto_Name_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:09:44.654 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 18:09:44.655 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api) in 126.8413ms
2025-08-10 18:09:44.657 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 18:09:44.659 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Category - 400 null application/json; charset=utf-8 148.4907ms
2025-08-10 18:09:56.759 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Category/1 - null null
2025-08-10 18:09:56.765 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:09:56.772 +03:00 [INF] Route matched with {action = "GetCategoryById", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategoryById(Int32) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:09:56.789 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:09:56.907 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api) in 131.8558ms
2025-08-10 18:09:56.911 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:09:56.915 +03:00 [ERR] An unhandled exception has occurred while executing the request.
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Type Map configuration:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Destination Member:
ImageUrl

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_68(ItemCategory src, CategoryResponseDto dest, String destMember, ResolutionContext context) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 92
   at lambda_method570(Closure, Object, CategoryResponseDto, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method570(Closure, Object, CategoryResponseDto, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.ProductService.GetCategoryByIdAsync(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 144
   at DoorCompany.Api.Controllers.CategoryController.GetCategoryById(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\CategoryController.cs:line 32
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-08-10 18:09:56.938 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Category/1 - 500 null text/plain; charset=utf-8 178.7805ms
2025-08-10 18:12:30.695 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Category/1 - null null
2025-08-10 18:12:30.715 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:12:30.717 +03:00 [INF] Route matched with {action = "GetCategoryById", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategoryById(Int32) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:12:30.725 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:12:30.825 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api) in 105.1139ms
2025-08-10 18:12:30.827 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:12:30.829 +03:00 [ERR] An unhandled exception has occurred while executing the request.
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Type Map configuration:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Destination Member:
ImageUrl

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_68(ItemCategory src, CategoryResponseDto dest, String destMember, ResolutionContext context) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 92
   at lambda_method570(Closure, Object, CategoryResponseDto, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method570(Closure, Object, CategoryResponseDto, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.ProductService.GetCategoryByIdAsync(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 144
   at DoorCompany.Api.Controllers.CategoryController.GetCategoryById(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\CategoryController.cs:line 32
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-08-10 18:12:30.840 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Category/1 - 500 null text/plain; charset=utf-8 147.6996ms
2025-08-10 18:14:54.892 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 18:14:55.188 +03:00 [INF] Executed DbCommand (238ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 18:14:55.558 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:14:55.588 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:14:55.760 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 18:14:55.868 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 18:14:55.923 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 18:14:55.939 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:14:55.950 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:14:56.006 +03:00 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:14:56.024 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:14:56.036 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:14:56.088 +03:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:14:56.241 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 18:14:56.415 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 18:14:56.563 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 18:14:56.571 +03:00 [INF] Hosting environment: Development
2025-08-10 18:14:56.572 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 18:14:56.949 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 18:14:57.150 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 209.8225ms
2025-08-10 18:14:57.170 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 18:14:57.177 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.8603ms
2025-08-10 18:14:57.185 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 18:14:57.224 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.0644ms
2025-08-10 18:14:57.420 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 18:14:57.483 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 63.3644ms
2025-08-10 18:16:14.812 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Category/1 - null null
2025-08-10 18:16:14.833 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 18:16:14.926 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:16:14.953 +03:00 [INF] Route matched with {action = "GetCategoryById", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategoryById(Int32) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:16:15.232 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:16:15.383 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api) in 423.4077ms
2025-08-10 18:16:15.385 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:16:15.390 +03:00 [ERR] An unhandled exception has occurred while executing the request.
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Type Map configuration:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Destination Member:
ImageUrl

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_68(ItemCategory src, CategoryResponseDto dest, String destMember, ResolutionContext context) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 92
   at lambda_method385(Closure, Object, CategoryResponseDto, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method385(Closure, Object, CategoryResponseDto, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.ProductService.GetCategoryByIdAsync(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 144
   at DoorCompany.Api.Controllers.CategoryController.GetCategoryById(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\CategoryController.cs:line 32
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-08-10 18:16:15.447 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Category/1 - 500 null text/plain; charset=utf-8 634.6952ms
2025-08-10 18:21:06.151 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 18:21:06.224 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 18:21:06.586 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:21:06.613 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:21:06.774 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 18:21:06.874 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 18:21:06.914 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 18:21:06.925 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:21:06.934 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:21:06.941 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:21:06.949 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:21:06.957 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:21:06.965 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:21:07.112 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 18:21:07.274 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 18:21:07.392 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 18:21:07.400 +03:00 [INF] Hosting environment: Development
2025-08-10 18:21:07.401 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 18:21:07.634 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 18:21:07.820 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 194.0475ms
2025-08-10 18:21:07.848 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 18:21:07.849 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 18:21:07.856 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.6666ms
2025-08-10 18:21:07.891 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.3552ms
2025-08-10 18:21:08.073 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 18:21:08.128 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 54.6124ms
2025-08-10 18:21:17.867 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Category/1 - null null
2025-08-10 18:21:17.874 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 18:21:17.973 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:21:17.998 +03:00 [INF] Route matched with {action = "GetCategoryById", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategoryById(Int32) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:21:18.255 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:21:18.409 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api) in 404.0566ms
2025-08-10 18:21:18.413 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:21:18.417 +03:00 [ERR] An unhandled exception has occurred while executing the request.
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Type Map configuration:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Destination Member:
ImageUrl

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_68(ItemCategory src, CategoryResponseDto dest, String destMember, ResolutionContext context) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 92
   at lambda_method385(Closure, Object, CategoryResponseDto, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method385(Closure, Object, CategoryResponseDto, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.ProductService.GetCategoryByIdAsync(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 144
   at DoorCompany.Api.Controllers.CategoryController.GetCategoryById(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\CategoryController.cs:line 32
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-08-10 18:21:18.471 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Category/1 - 500 null text/plain; charset=utf-8 605.002ms
2025-08-10 18:23:02.341 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 18:23:02.431 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 18:23:02.734 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:23:02.756 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:23:02.928 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 18:23:03.021 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 18:23:03.065 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 18:23:03.077 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:23:03.085 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:23:03.095 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:23:03.103 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:23:03.111 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:23:03.131 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:23:03.290 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 18:23:03.444 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 18:23:03.611 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 18:23:03.613 +03:00 [INF] Hosting environment: Development
2025-08-10 18:23:03.614 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 18:23:03.773 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 18:23:03.963 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 213.3051ms
2025-08-10 18:23:03.978 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 18:23:03.984 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.6581ms
2025-08-10 18:23:03.995 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 18:23:04.032 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 37.4936ms
2025-08-10 18:23:04.205 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 18:23:04.240 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 35.2976ms
2025-08-10 18:23:14.039 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Category/1 - null null
2025-08-10 18:23:14.061 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 18:23:14.167 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:23:14.194 +03:00 [INF] Route matched with {action = "GetCategoryById", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategoryById(Int32) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:23:14.449 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:23:14.635 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api) in 435.2425ms
2025-08-10 18:23:14.638 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:23:14.642 +03:00 [ERR] An unhandled exception has occurred while executing the request.
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Type Map configuration:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Destination Member:
ImageUrl

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_68(ItemCategory src, CategoryResponseDto dest, String destMember, ResolutionContext context) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 92
   at lambda_method385(Closure, Object, CategoryResponseDto, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method385(Closure, Object, CategoryResponseDto, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.ProductService.GetCategoryByIdAsync(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 144
   at DoorCompany.Api.Controllers.CategoryController.GetCategoryById(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\CategoryController.cs:line 32
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-08-10 18:23:14.687 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Category/1 - 500 null text/plain; charset=utf-8 648.118ms
2025-08-10 18:24:17.310 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 18:24:17.384 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 18:24:17.716 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:24:17.739 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:24:17.906 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 18:24:18.000 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 18:24:18.041 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 18:24:18.052 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:24:18.060 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:24:18.068 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:24:18.075 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:24:18.085 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:24:18.094 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:24:18.289 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 18:24:18.455 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 18:24:18.621 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 18:24:18.623 +03:00 [INF] Hosting environment: Development
2025-08-10 18:24:18.624 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 18:24:18.627 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 18:24:18.826 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 211.4814ms
2025-08-10 18:24:18.854 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 18:24:18.857 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 18:24:18.863 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.354ms
2025-08-10 18:24:18.896 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.1387ms
2025-08-10 18:24:19.078 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 18:24:19.143 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 65.0107ms
2025-08-10 18:24:25.963 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Category/1 - null null
2025-08-10 18:24:25.970 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 18:24:26.068 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:24:26.093 +03:00 [INF] Route matched with {action = "GetCategoryById", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategoryById(Int32) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:24:26.358 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:24:26.514 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api) in 414.2023ms
2025-08-10 18:24:26.517 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:24:26.522 +03:00 [ERR] An unhandled exception has occurred while executing the request.
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Type Map configuration:
ItemCategory -> CategoryResponseDto
DoorCompany.Data.Models.ItemCategory -> DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto

Destination Member:
ImageUrl

 ---> System.InvalidOperationException: Context.Items are only available when using a Map overload that takes Action<IMappingOperationOptions>!
   at AutoMapper.ResolutionContext.ThrowInvalidMap()
   at AutoMapper.ResolutionContext.get_Items()
   at DoorCompany.Service.Mapping.MappingProfile.<>c.<.ctor>b__0_68(ItemCategory src, CategoryResponseDto dest, String destMember, ResolutionContext context) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Mapping\MappingProfile.cs:line 92
   at lambda_method385(Closure, Object, CategoryResponseDto, ResolutionContext)
   --- End of inner exception stack trace ---
   at lambda_method385(Closure, Object, CategoryResponseDto, ResolutionContext)
   at DoorCompany.Service.Repositories.Implementations.ProductService.GetCategoryByIdAsync(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 144
   at DoorCompany.Api.Controllers.CategoryController.GetCategoryById(Int32 id) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Controllers\CategoryController.cs:line 32
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-08-10 18:24:26.571 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Category/1 - 500 null text/plain; charset=utf-8 608.9123ms
2025-08-10 18:29:28.402 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 18:29:28.473 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 18:29:28.807 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:29:28.828 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:29:28.981 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 18:29:29.111 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 18:29:29.150 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 18:29:29.162 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:29:29.169 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:29:29.177 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:29:29.188 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:29:29.197 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:29:29.205 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:29:29.355 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 18:29:29.509 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 18:29:29.702 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 18:29:29.704 +03:00 [INF] Hosting environment: Development
2025-08-10 18:29:29.705 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 18:29:29.733 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 18:29:29.940 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 218.3619ms
2025-08-10 18:29:29.971 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 18:29:29.973 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 18:29:29.981 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.1434ms
2025-08-10 18:29:30.016 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.6036ms
2025-08-10 18:29:30.202 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 18:29:30.241 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 39.2373ms
2025-08-10 18:29:40.535 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Category/1 - null null
2025-08-10 18:29:40.542 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 18:29:40.656 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:29:40.681 +03:00 [INF] Route matched with {action = "GetCategoryById", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategoryById(Int32) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:29:40.945 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:29:40.976 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 18:29:41.017 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api) in 330.4104ms
2025-08-10 18:29:41.020 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.GetCategoryById (DoorCompany.Api)'
2025-08-10 18:29:41.024 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Category/1 - 200 null application/json; charset=utf-8 489.158ms
2025-08-10 18:31:11.999 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Category - multipart/form-data; boundary=----WebKitFormBoundaryNjFXDCe4MK6LKDVy 1253
2025-08-10 18:31:12.006 +03:00 [INF] CORS policy execution successful.
2025-08-10 18:31:12.008 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 18:31:12.016 +03:00 [INF] Route matched with {action = "CreateCategory", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateCategoryAsync(DoorCompany.Service.Dtos.ProductDto.CreateCategoryDto) on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 18:31:12.141 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Name] = @__createDto_Name_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:31:12.202 +03:00 [INF] Executed DbCommand (49ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ItemCategories] AS [i]
        WHERE [i].[Code] = @__createDto_Code_0 AND [i].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 18:31:12.226 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy]
FROM [ItemCategories] AS [i]
WHERE [i].[Id] = @__p_0
2025-08-10 18:31:12.277 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__childId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[ParentCategoryId]
FROM [ItemCategories] AS [i]
WHERE [i].[Id] = @__childId_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:31:12.568 +03:00 [INF] Executed DbCommand (137ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (Size = 4000), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (Size = 4000), @p12='?' (DbType = DateTime2), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [ItemCategories] ([CategoryTypeId], [Code], [CreatedAt], [CreatedBy], [Description], [ImageUrl], [IsActive], [IsDeleted], [Name], [ParentCategoryId], [SortOrder], [Symbol], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-08-10 18:31:12.627 +03:00 [INF] Category created successfully: صنف اولي
2025-08-10 18:31:12.639 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
WHERE [i].[Id] = @__id_0 AND [i].[IsDeleted] = CAST(0 AS bit)
2025-08-10 18:31:12.643 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 18:31:12.645 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api) in 627.4666ms
2025-08-10 18:31:12.647 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.CreateCategoryAsync (DoorCompany.Api)'
2025-08-10 18:31:12.648 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Category - 200 null application/json; charset=utf-8 649.3573ms
2025-08-10 19:02:44.230 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-10 19:02:44.319 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-10 19:02:44.657 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:02:44.679 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:02:44.904 +03:00 [INF] Executed DbCommand (83ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-10 19:02:45.073 +03:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-10 19:02:45.133 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-10 19:02:45.147 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:02:45.156 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:02:45.172 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:02:45.191 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:02:45.200 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:02:45.209 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:02:45.369 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-10 19:02:45.583 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-10 19:02:45.691 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 19:02:45.693 +03:00 [INF] Hosting environment: Development
2025-08-10 19:02:45.694 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-10 19:02:45.979 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-10 19:02:46.181 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 211.0687ms
2025-08-10 19:02:46.227 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-10 19:02:46.228 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-10 19:02:46.235 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.011ms
2025-08-10 19:02:46.271 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.0644ms
2025-08-10 19:02:46.691 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-10 19:02:46.760 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 69.0399ms
2025-08-10 19:03:00.244 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Category - null null
2025-08-10 19:03:00.254 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-10 19:03:01.492 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.CategoryController.GetAllCategoryAsync (DoorCompany.Api)'
2025-08-10 19:03:01.512 +03:00 [INF] Route matched with {action = "GetAllCategory", controller = "Category"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllCategoryAsync() on controller DoorCompany.Api.Controllers.CategoryController (DoorCompany.Api).
2025-08-10 19:03:01.835 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[Name], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy], [i0].[Id], [i0].[CategoryTypeId], [i0].[Code], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[ImageUrl], [i0].[IsActive], [i0].[IsDeleted], [i0].[Name], [i0].[ParentCategoryId], [i0].[SortOrder], [i0].[Symbol], [i0].[UpdatedAt], [i0].[UpdatedBy], [i1].[Id], [i1].[CategoryTypeId], [i1].[Code], [i1].[CreatedAt], [i1].[CreatedBy], [i1].[Description], [i1].[ImageUrl], [i1].[IsActive], [i1].[IsDeleted], [i1].[Name], [i1].[ParentCategoryId], [i1].[SortOrder], [i1].[Symbol], [i1].[UpdatedAt], [i1].[UpdatedBy]
FROM [ItemCategories] AS [i]
LEFT JOIN [ItemCategories] AS [i0] ON [i].[ParentCategoryId] = [i0].[Id]
LEFT JOIN [ItemCategories] AS [i1] ON [i].[Id] = [i1].[ParentCategoryId]
ORDER BY [i].[Id], [i0].[Id]
2025-08-10 19:03:01.877 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.ProductDto.CategoryResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-10 19:03:01.911 +03:00 [INF] Executed action DoorCompany.Api.Controllers.CategoryController.GetAllCategoryAsync (DoorCompany.Api) in 394.0355ms
2025-08-10 19:03:01.914 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.CategoryController.GetAllCategoryAsync (DoorCompany.Api)'
2025-08-10 19:03:01.919 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Category - 200 null application/json; charset=utf-8 1675.0922ms
2025-08-10 19:07:01.781 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product - multipart/form-data; boundary=----WebKitFormBoundaryOmpvYdXMAtAB1IVY 1752
2025-08-10 19:07:01.788 +03:00 [INF] CORS policy execution successful.
2025-08-10 19:07:01.791 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api)'
2025-08-10 19:07:01.811 +03:00 [INF] Route matched with {action = "CreateProduct", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateProductAsync(DoorCompany.Service.Dtos.ProductDto.CreateProductDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-10 19:07:01.945 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Name] = @__createDto_Name_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:07:01.956 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Code] = @__createDto_Code_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:07:01.967 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_Barcode_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Barcode] = @__createDto_Barcode_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:07:02.379 +03:00 [INF] Executed DbCommand (113ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (DbType = Int32), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (DbType = Int32), @p10='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p12='?' (Size = 4000), @p13='?' (DbType = Int32), @p14='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Products] ([Barcode], [CategoryId], [Code], [CreatedAt], [CreatedBy], [Description], [ImagePath], [IsActive], [IsDeleted], [ItemType], [MaximumStock], [MinimumStock], [Name], [SortOrder], [StandardCost], [UnitId], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-08-10 19:07:02.434 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'ImagePath', table 'DoorDb.dbo.Products'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:543075ee-03c4-4eb0-adfc-2b6d42406591
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'ImagePath', table 'DoorDb.dbo.Products'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:543075ee-03c4-4eb0-adfc-2b6d42406591
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-08-10 19:07:02.636 +03:00 [ERR] Error creating Product: اول صنف
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'ImagePath', table 'DoorDb.dbo.Products'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:543075ee-03c4-4eb0-adfc-2b6d42406591
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at DoorCompany.Data.Context.ApplicationDbContext.SaveChangesAsync(CancellationToken cancellationToken) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Data\Context\ApplicationDbContext.cs:line 163
   at DoorCompany.Service.Repositories.Implementations.Basic.GenericRepository`1.AddAsync(T entity) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\Basic\GenericRepository.cs:line 41
   at DoorCompany.Service.Repositories.Implementations.ProductService.CreateProductAsync(CreateProductDto createDto) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 102
2025-08-10 19:07:02.671 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.ProductResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 19:07:02.683 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api) in 869.6372ms
2025-08-10 19:07:02.685 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api)'
2025-08-10 19:07:02.686 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product - 400 null application/json; charset=utf-8 905.2752ms
2025-08-10 19:08:20.400 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product - multipart/form-data; boundary=----WebKitFormBoundaryPIKbffM44NiV9cXE 1752
2025-08-10 19:08:20.407 +03:00 [INF] CORS policy execution successful.
2025-08-10 19:08:20.411 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api)'
2025-08-10 19:08:20.413 +03:00 [INF] Route matched with {action = "CreateProduct", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateProductAsync(DoorCompany.Service.Dtos.ProductDto.CreateProductDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-10 19:08:25.770 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Name] = @__createDto_Name_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:08:26.906 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Code] = @__createDto_Code_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:08:28.087 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createDto_Barcode_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Barcode] = @__createDto_Barcode_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-10 19:09:04.404 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (DbType = Int32), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (DbType = Int32), @p10='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p12='?' (Size = 4000), @p13='?' (DbType = Int32), @p14='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Products] ([Barcode], [CategoryId], [Code], [CreatedAt], [CreatedBy], [Description], [ImagePath], [IsActive], [IsDeleted], [ItemType], [MaximumStock], [MinimumStock], [Name], [SortOrder], [StandardCost], [UnitId], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-08-10 19:09:04.414 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'ImagePath', table 'DoorDb.dbo.Products'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:543075ee-03c4-4eb0-adfc-2b6d42406591
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'ImagePath', table 'DoorDb.dbo.Products'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:543075ee-03c4-4eb0-adfc-2b6d42406591
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-08-10 19:09:36.721 +03:00 [ERR] Error creating Product: اول صنف
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'ImagePath', table 'DoorDb.dbo.Products'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:543075ee-03c4-4eb0-adfc-2b6d42406591
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at DoorCompany.Data.Context.ApplicationDbContext.SaveChangesAsync(CancellationToken cancellationToken) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Data\Context\ApplicationDbContext.cs:line 163
   at DoorCompany.Service.Repositories.Implementations.Basic.GenericRepository`1.AddAsync(T entity) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\Basic\GenericRepository.cs:line 41
   at DoorCompany.Service.Repositories.Implementations.ProductService.CreateProductAsync(CreateProductDto createDto) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\ProductService.cs:line 102
2025-08-10 19:09:36.741 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.ProductResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-10 19:09:36.742 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api) in 76327.1743ms
2025-08-10 19:09:36.744 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api)'
2025-08-10 19:09:36.745 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product - 400 null application/json; charset=utf-8 76346.7766ms
