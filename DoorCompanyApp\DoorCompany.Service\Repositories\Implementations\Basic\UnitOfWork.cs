﻿using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Implementations.Basic
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly IUnitOfWorkOfService _work;

        public IPartnerRepository PartnerRepository { get; }
        public IUserRepository UserRepository { get; }
      
        public IMainActionRepository MainActionRepository { get; }

        public IShareRepository ShareRepository { get; }

        public IProductRepository ProductRepository { get; }

        public IInventoryRepository InventoryRepository { get; }

        public IFinancialRepository FinancialRepository { get; }

        public IInvoiceRepository InvoiceRepository { get; }

        public UnitOfWork(IUnitOfWorkOfService work, IPartnerRepository partnerRepository,
                          IUserRepository userRepository, IMainActionRepository mainactionRepository,
                          IShareRepository shareRepository, IProductRepository productRepository,
                          IInventoryRepository inventoryRepository, IFinancialRepository financialRepository,
                          IInvoiceRepository invoiceRepository)
        {
            _work = work;
            PartnerRepository = partnerRepository;
            UserRepository = userRepository;
            MainActionRepository = mainactionRepository;
            ShareRepository = shareRepository;
            ProductRepository = productRepository;
            InventoryRepository = inventoryRepository;
            FinancialRepository = financialRepository;
            InvoiceRepository = invoiceRepository;
        }

       
    }
}
