2025-07-23 11:12:41.478 +03:00 [INF] Executed DbCommand (66ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-23 11:12:41.607 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-23 11:12:41.889 +03:00 [INF] Executed DbCommand (140ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-07-23 11:12:42.022 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-07-23 11:12:42.087 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-07-23 11:12:42.101 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-23 11:12:42.110 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-23 11:12:42.429 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-23 11:12:42.928 +03:00 [INF] Now listening on: http://localhost:5184
2025-07-23 11:12:43.030 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 11:12:43.204 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 11:12:43.205 +03:00 [INF] Hosting environment: Development
2025-07-23 11:12:43.207 +03:00 [INF] Content root path: D:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-07-23 11:12:43.210 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:12:43.244 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 239.484ms
2025-07-23 11:12:43.253 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 11:12:43.268 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:12:43.270 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-23 11:12:44.723 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:12:44.762 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:12:45.064 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-23 11:12:45.140 +03:00 [INF] Executed DbCommand (42ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:12:45.149 +03:00 [WRN] Login attempt with invalid username: dfgfd
2025-07-23 11:12:45.161 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:12:45.193 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 423.6416ms
2025-07-23 11:12:45.195 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:12:45.201 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 1947.6626ms
2025-07-23 11:12:46.490 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-23 11:12:46.776 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 285.4631ms
2025-07-23 11:12:46.794 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-23 11:12:46.800 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.2822ms
2025-07-23 11:12:46.814 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-23 11:12:46.893 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 79.3447ms
2025-07-23 11:12:47.053 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-23 11:12:47.082 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 29.1277ms
2025-07-23 11:12:50.255 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 11:12:50.261 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:12:50.262 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 7.3617ms
2025-07-23 11:12:50.268 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 11:12:50.272 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:12:50.275 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:12:50.276 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:12:50.352 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:12:50.356 +03:00 [WRN] Login attempt with invalid username: dfgfd
2025-07-23 11:12:50.358 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:12:50.359 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 80.6599ms
2025-07-23 11:12:50.361 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:12:50.363 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 94.9761ms
2025-07-23 11:12:50.367 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 11:12:50.370 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:12:50.373 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:12:50.374 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:12:50.385 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:12:50.389 +03:00 [WRN] Login attempt with invalid username: dfgfd
2025-07-23 11:12:50.390 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:12:50.392 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 15.7931ms
2025-07-23 11:12:50.393 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:12:50.395 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 28.2554ms
2025-07-23 11:12:50.399 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 11:12:50.402 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:12:50.404 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:12:50.405 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:12:50.410 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:12:50.414 +03:00 [WRN] Login attempt with invalid username: dfgfd
2025-07-23 11:12:50.415 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:12:50.417 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 10.2993ms
2025-07-23 11:12:50.419 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:12:50.421 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 22.4023ms
2025-07-23 11:13:06.848 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 11:13:06.852 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:13:06.854 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 6.25ms
2025-07-23 11:13:06.860 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 11:13:06.863 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:13:06.865 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:06.867 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:13:06.875 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:13:06.882 +03:00 [WRN] Login attempt with invalid username: dfgfd
2025-07-23 11:13:06.885 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:13:06.887 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 17.4565ms
2025-07-23 11:13:06.890 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:06.892 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 32.8471ms
2025-07-23 11:13:06.900 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 11:13:06.903 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:13:06.904 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:06.905 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:13:06.910 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:13:06.915 +03:00 [WRN] Login attempt with invalid username: dfgfd
2025-07-23 11:13:06.916 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:13:06.918 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 10.8146ms
2025-07-23 11:13:06.920 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:06.922 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 22.1976ms
2025-07-23 11:13:06.928 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 11:13:06.932 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:13:06.933 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:06.935 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:13:06.939 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:13:06.943 +03:00 [WRN] Login attempt with invalid username: dfgfd
2025-07-23 11:13:06.944 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:13:06.946 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 9.9352ms
2025-07-23 11:13:06.948 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:06.950 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 22.2618ms
2025-07-23 11:13:56.503 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 11:13:56.506 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:13:56.507 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 4.2324ms
2025-07-23 11:13:56.520 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 44
2025-07-23 11:13:56.524 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:13:56.526 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:56.528 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:13:56.563 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:13:56.568 +03:00 [WRN] Login attempt with invalid username: dfgdfg
2025-07-23 11:13:56.569 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:13:56.573 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 42.7808ms
2025-07-23 11:13:56.575 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:56.577 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 56.7989ms
2025-07-23 11:13:56.587 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 44
2025-07-23 11:13:56.591 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:13:56.593 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:56.595 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:13:56.601 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:13:56.605 +03:00 [WRN] Login attempt with invalid username: dfgdfg
2025-07-23 11:13:56.606 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:13:56.608 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 9.9386ms
2025-07-23 11:13:56.610 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:56.611 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 23.3782ms
2025-07-23 11:13:56.619 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 44
2025-07-23 11:13:56.622 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:13:56.624 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:56.625 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:13:56.631 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:13:56.636 +03:00 [WRN] Login attempt with invalid username: dfgdfg
2025-07-23 11:13:56.638 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:13:56.640 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 11.6518ms
2025-07-23 11:13:56.642 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:13:56.643 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 24.6005ms
2025-07-23 11:14:05.816 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 11:14:05.820 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:05.821 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 5.1803ms
2025-07-23 11:14:05.825 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 45
2025-07-23 11:14:05.828 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:05.830 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:05.831 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:14:05.839 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:14:05.851 +03:00 [WRN] Login attempt with invalid username: dfgdfg
2025-07-23 11:14:05.853 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:14:05.855 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 21.2518ms
2025-07-23 11:14:05.857 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:05.859 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 33.1833ms
2025-07-23 11:14:05.864 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 45
2025-07-23 11:14:05.868 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:05.870 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:05.871 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:14:05.879 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:14:05.887 +03:00 [WRN] Login attempt with invalid username: dfgdfg
2025-07-23 11:14:05.888 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:14:05.890 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 15.9603ms
2025-07-23 11:14:05.893 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:05.894 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 30.3494ms
2025-07-23 11:14:05.903 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 45
2025-07-23 11:14:05.907 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:05.908 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:05.909 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:14:05.918 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:14:05.922 +03:00 [WRN] Login attempt with invalid username: dfgdfg
2025-07-23 11:14:05.924 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:14:05.925 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 13.3707ms
2025-07-23 11:14:05.927 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:05.929 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 25.9428ms
2025-07-23 11:14:20.793 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 11:14:20.797 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:20.799 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 6.216ms
2025-07-23 11:14:20.805 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 45
2025-07-23 11:14:20.809 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:20.810 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:20.812 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:14:20.820 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:14:20.825 +03:00 [WRN] Login attempt with invalid username: dfgdfg
2025-07-23 11:14:20.827 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:14:20.829 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 13.7685ms
2025-07-23 11:14:20.831 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:20.832 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 27.6181ms
2025-07-23 11:14:20.839 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 45
2025-07-23 11:14:20.842 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:20.843 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:20.844 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:14:20.851 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:14:20.857 +03:00 [WRN] Login attempt with invalid username: dfgdfg
2025-07-23 11:14:20.858 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:14:20.860 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 13.0926ms
2025-07-23 11:14:20.862 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:20.863 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 24.5315ms
2025-07-23 11:14:20.870 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 45
2025-07-23 11:14:20.874 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:20.875 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:20.877 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:14:20.883 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:14:20.890 +03:00 [WRN] Login attempt with invalid username: dfgdfg
2025-07-23 11:14:20.892 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:14:20.893 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 13.8264ms
2025-07-23 11:14:20.895 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:20.896 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 26.2498ms
2025-07-23 11:14:30.053 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 11:14:30.057 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:30.058 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 4.5149ms
2025-07-23 11:14:30.063 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 52
2025-07-23 11:14:30.066 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:30.067 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:30.069 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:14:30.102 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:14:30.107 +03:00 [WRN] Login attempt with invalid username: dfgdfgghdg
2025-07-23 11:14:30.109 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:14:30.111 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 39.4494ms
2025-07-23 11:14:30.112 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:30.114 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 50.5563ms
2025-07-23 11:14:30.120 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 52
2025-07-23 11:14:30.124 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:30.125 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:30.127 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:14:30.161 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:14:30.166 +03:00 [WRN] Login attempt with invalid username: dfgdfgghdg
2025-07-23 11:14:30.168 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:14:30.170 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 41.325ms
2025-07-23 11:14:30.172 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:30.174 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 54.1559ms
2025-07-23 11:14:30.181 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 52
2025-07-23 11:14:30.185 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:14:30.188 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:30.189 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:14:30.235 +03:00 [INF] Executed DbCommand (41ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:14:30.239 +03:00 [WRN] Login attempt with invalid username: dfgdfgghdg
2025-07-23 11:14:30.241 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:14:30.243 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 50.6565ms
2025-07-23 11:14:30.244 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:14:30.246 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 65.5616ms
2025-07-23 11:21:39.718 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 11:21:39.725 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:21:39.726 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 7.9078ms
2025-07-23 11:21:39.733 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 11:21:39.738 +03:00 [INF] CORS policy execution successful.
2025-07-23 11:21:39.739 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:21:39.740 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 11:21:39.883 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 11:21:40.116 +03:00 [INF] Access token generated for user 1
2025-07-23 11:21:40.360 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 11:21:40.393 +03:00 [INF] User logged in successfully: admin
2025-07-23 11:21:40.395 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 11:21:40.412 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 669.8928ms
2025-07-23 11:21:40.414 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 11:21:40.416 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 682.6153ms
2025-07-23 12:15:20.245 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:15:20.252 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:15:20.254 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 9.0581ms
2025-07-23 12:15:20.260 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 12:15:20.269 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:15:20.270 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:20.272 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:15:20.417 +03:00 [INF] Executed DbCommand (39ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:15:20.421 +03:00 [WRN] Login attempt with invalid username: asd
2025-07-23 12:15:20.422 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:15:20.424 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 150.7845ms
2025-07-23 12:15:20.426 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:20.429 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 168.7739ms
2025-07-23 12:15:20.438 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 12:15:20.447 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:15:20.448 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:20.449 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:15:20.455 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:15:20.458 +03:00 [WRN] Login attempt with invalid username: asd
2025-07-23 12:15:20.459 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:15:20.460 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 9.1992ms
2025-07-23 12:15:20.462 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:20.463 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 25.1368ms
2025-07-23 12:15:20.469 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 41
2025-07-23 12:15:20.473 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:15:20.474 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:20.475 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:15:20.480 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:15:20.484 +03:00 [WRN] Login attempt with invalid username: asd
2025-07-23 12:15:20.485 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:15:20.487 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 10.6178ms
2025-07-23 12:15:20.489 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:20.490 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 20.7567ms
2025-07-23 12:15:39.153 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:15:39.158 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:15:39.159 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 6.335ms
2025-07-23 12:15:39.164 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:15:39.167 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:15:39.168 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:39.169 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:15:39.176 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:15:39.297 +03:00 [INF] Access token generated for user 1
2025-07-23 12:15:39.311 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:15:39.315 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:15:39.316 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:15:39.319 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 148.3914ms
2025-07-23 12:15:39.322 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:39.324 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 160.0557ms
2025-07-23 12:15:44.808 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:15:44.812 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:15:44.813 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 4.8748ms
2025-07-23 12:15:44.829 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:15:44.832 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:15:44.833 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:44.835 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:15:44.843 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:15:44.954 +03:00 [INF] Access token generated for user 1
2025-07-23 12:15:44.961 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:15:44.965 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:15:44.967 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:15:44.969 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 132.5179ms
2025-07-23 12:15:44.972 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:44.974 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 144.662ms
2025-07-23 12:15:45.980 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:15:48.761 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:15:48.978 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:49.013 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:15:49.867 +03:00 [INF] Executed DbCommand (323ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:15:50.166 +03:00 [INF] Access token generated for user 1
2025-07-23 12:15:50.198 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:15:50.202 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:15:50.204 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:15:50.212 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 670.4853ms
2025-07-23 12:15:50.220 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:15:50.223 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 4243.0554ms
2025-07-23 12:16:13.718 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:16:13.728 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:16:13.729 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 10.431ms
2025-07-23 12:16:13.734 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:16:13.744 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:16:13.745 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:13.747 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:16:13.754 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:16:13.857 +03:00 [INF] Access token generated for user 1
2025-07-23 12:16:13.861 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:16:13.866 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:16:13.867 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:16:13.868 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 119.0142ms
2025-07-23 12:16:13.870 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:13.871 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 137.3452ms
2025-07-23 12:16:15.754 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:16:15.760 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:16:15.761 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:15.762 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:16:15.828 +03:00 [INF] Executed DbCommand (62ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:16:15.947 +03:00 [INF] Access token generated for user 1
2025-07-23 12:16:15.960 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:16:15.964 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:16:15.965 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:16:15.967 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 202.6841ms
2025-07-23 12:16:15.969 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:15.971 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 216.7404ms
2025-07-23 12:16:18.104 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:16:18.108 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:16:18.110 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:18.112 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:16:18.149 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:16:18.294 +03:00 [INF] Access token generated for user 1
2025-07-23 12:16:18.355 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:16:18.359 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:16:18.361 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:16:18.363 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 248.4217ms
2025-07-23 12:16:18.365 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:18.367 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 263.2927ms
2025-07-23 12:16:18.966 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:16:18.970 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:16:18.971 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 4.5358ms
2025-07-23 12:16:18.976 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:16:18.979 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:16:18.981 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:18.983 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:16:19.021 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:16:19.138 +03:00 [INF] Access token generated for user 1
2025-07-23 12:16:19.144 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:16:19.147 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:16:19.148 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:16:19.150 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 164.5443ms
2025-07-23 12:16:19.151 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:19.153 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 177.1427ms
2025-07-23 12:16:19.369 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:16:19.372 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:16:19.375 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:19.376 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:16:19.414 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:16:19.538 +03:00 [INF] Access token generated for user 1
2025-07-23 12:16:19.544 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:16:19.547 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:16:19.548 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:16:19.549 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 170.6127ms
2025-07-23 12:16:19.551 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:16:19.553 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 184.1474ms
2025-07-23 12:18:29.287 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:18:29.295 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:29.296 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 9.343ms
2025-07-23 12:18:29.301 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:29.310 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:29.311 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:29.313 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:29.360 +03:00 [INF] Executed DbCommand (43ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:29.466 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:29.473 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:29.477 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:29.479 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:29.481 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 165.7605ms
2025-07-23 12:18:29.482 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:29.483 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 182.2984ms
2025-07-23 12:18:32.894 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:32.897 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:32.898 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:32.899 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:32.923 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:33.031 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:33.037 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:33.043 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:33.046 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:33.048 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 145.9246ms
2025-07-23 12:18:33.050 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:33.051 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 157.6174ms
2025-07-23 12:18:34.130 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:34.134 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:34.136 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:34.138 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:34.165 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:34.273 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:34.279 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:34.282 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:34.284 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:34.286 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 144.7252ms
2025-07-23 12:18:34.287 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:34.288 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 158.7991ms
2025-07-23 12:18:34.959 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:18:34.963 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:34.964 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 4.8655ms
2025-07-23 12:18:34.969 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:34.973 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:34.974 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:34.975 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:34.983 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:35.174 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:35.179 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:35.182 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:35.183 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:35.187 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 208.9907ms
2025-07-23 12:18:35.194 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:35.196 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 226.7379ms
2025-07-23 12:18:35.335 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:35.337 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:35.338 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:35.340 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:35.346 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:35.458 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:35.462 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:35.466 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:35.467 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:35.468 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 125.4913ms
2025-07-23 12:18:35.470 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:35.471 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 136.6884ms
2025-07-23 12:18:35.510 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:35.514 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:35.515 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:35.516 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:35.520 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:35.637 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:35.641 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:35.645 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:35.647 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:35.649 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 131.644ms
2025-07-23 12:18:35.652 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:35.656 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 145.7859ms
2025-07-23 12:18:35.694 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:35.697 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:35.699 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:35.700 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:35.704 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:35.817 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:35.821 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:35.824 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:35.825 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:35.828 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 126.2611ms
2025-07-23 12:18:35.830 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:35.831 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 137.2953ms
2025-07-23 12:18:35.883 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:35.887 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:35.888 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:35.889 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:35.897 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:36.011 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:36.015 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:36.017 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:36.018 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:36.020 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 126.7943ms
2025-07-23 12:18:36.021 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.023 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 140.01ms
2025-07-23 12:18:36.062 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:36.065 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:36.066 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.067 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:36.072 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:36.189 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:36.193 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:36.196 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:36.197 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:36.199 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 129.9712ms
2025-07-23 12:18:36.202 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.205 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 142.8571ms
2025-07-23 12:18:36.247 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:36.250 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:36.252 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.253 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:36.258 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:36.375 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:36.378 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:36.381 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:36.382 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:36.384 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 128.1763ms
2025-07-23 12:18:36.385 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.387 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 139.8173ms
2025-07-23 12:18:36.553 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:36.556 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:36.557 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.559 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:36.566 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:36.698 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:36.701 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:36.704 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:36.706 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:36.708 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 145.6716ms
2025-07-23 12:18:36.710 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.712 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 158.9798ms
2025-07-23 12:18:36.799 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:36.801 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:36.802 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.803 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:36.809 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:36.917 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:36.920 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:36.924 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:36.925 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:36.927 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 122.3475ms
2025-07-23 12:18:36.930 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.932 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 133.1734ms
2025-07-23 12:18:36.983 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:36.987 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:36.988 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:36.989 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:37.005 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:37.121 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:37.124 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:37.127 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:37.128 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:37.129 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 127.188ms
2025-07-23 12:18:37.131 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:37.132 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 148.6609ms
2025-07-23 12:18:37.200 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:37.203 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:37.205 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:37.206 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:37.212 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:37.329 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:37.332 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:37.335 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:37.336 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:37.338 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 129.1499ms
2025-07-23 12:18:37.339 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:37.341 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 141.1972ms
2025-07-23 12:18:37.390 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:37.394 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:37.395 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:37.398 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:37.405 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:37.508 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:37.513 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:37.516 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:37.517 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:37.519 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 118.1845ms
2025-07-23 12:18:37.520 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:37.522 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 132.2715ms
2025-07-23 12:18:37.693 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:18:37.698 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:18:37.699 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:37.701 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:18:37.706 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:18:37.813 +03:00 [INF] Access token generated for user 1
2025-07-23 12:18:37.817 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:18:37.820 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:18:37.822 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:18:37.824 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 120.6491ms
2025-07-23 12:18:37.827 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:18:37.828 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 135.5266ms
2025-07-23 12:19:23.221 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:19:23.231 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:19:23.233 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 11.7369ms
2025-07-23 12:19:23.240 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 12:19:23.246 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:19:23.247 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:19:23.248 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:19:23.253 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:19:23.382 +03:00 [INF] Access token generated for user 1
2025-07-23 12:19:23.385 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 12:19:23.387 +03:00 [INF] User logged in successfully: admin
2025-07-23 12:19:23.388 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:19:23.390 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 140.1443ms
2025-07-23 12:19:23.392 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:19:23.393 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 153.0971ms
2025-07-23 12:23:07.977 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:23:07.986 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:23:07.987 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 9.5713ms
2025-07-23 12:23:07.995 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 38
2025-07-23 12:23:07.998 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:23:07.999 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:23:08.000 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:23:08.032 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:23:08.037 +03:00 [WRN] Login attempt with invalid username: ghj
2025-07-23 12:23:08.038 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:23:08.040 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 37.1765ms
2025-07-23 12:23:08.041 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:23:08.042 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 47.0755ms
2025-07-23 12:23:08.046 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 38
2025-07-23 12:23:08.055 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:23:08.056 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:23:08.058 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:23:08.092 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:23:08.096 +03:00 [WRN] Login attempt with invalid username: ghj
2025-07-23 12:23:08.097 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:23:08.099 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 37.0861ms
2025-07-23 12:23:08.100 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:23:08.101 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 55.3282ms
2025-07-23 12:23:08.107 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 38
2025-07-23 12:23:08.110 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:23:08.111 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:23:08.112 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:23:08.116 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:23:08.121 +03:00 [WRN] Login attempt with invalid username: ghj
2025-07-23 12:23:08.122 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:23:08.125 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 10.6128ms
2025-07-23 12:23:08.126 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:23:08.127 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 20.2337ms
2025-07-23 12:24:17.113 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:24:17.122 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:24:17.124 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 10.8584ms
2025-07-23 12:24:17.131 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 44
2025-07-23 12:24:17.137 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:24:17.138 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:17.140 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:24:17.173 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:24:17.178 +03:00 [WRN] Login attempt with invalid username: ghjert
2025-07-23 12:24:17.179 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:24:17.180 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 37.3044ms
2025-07-23 12:24:17.181 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:17.182 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 51.947ms
2025-07-23 12:24:17.186 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 44
2025-07-23 12:24:17.190 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:24:17.191 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:17.192 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:24:17.198 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:24:17.204 +03:00 [WRN] Login attempt with invalid username: ghjert
2025-07-23 12:24:17.207 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:24:17.211 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 16.7593ms
2025-07-23 12:24:17.213 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:17.214 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 28.1155ms
2025-07-23 12:24:17.219 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 44
2025-07-23 12:24:17.223 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:24:17.225 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:17.226 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:24:17.229 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:24:17.233 +03:00 [WRN] Login attempt with invalid username: ghjert
2025-07-23 12:24:17.234 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:24:17.236 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 8.6033ms
2025-07-23 12:24:17.238 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:17.240 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 20.5588ms
2025-07-23 12:24:32.666 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 12:24:32.669 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:24:32.671 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 4.3929ms
2025-07-23 12:24:32.676 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 44
2025-07-23 12:24:32.679 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:24:32.680 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:32.681 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:24:32.685 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:24:32.691 +03:00 [WRN] Login attempt with invalid username: ghjert
2025-07-23 12:24:32.692 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:24:32.694 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 11.3107ms
2025-07-23 12:24:32.696 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:32.697 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 21.2134ms
2025-07-23 12:24:32.701 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 44
2025-07-23 12:24:32.704 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:24:32.705 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:32.707 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:24:32.711 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:24:32.715 +03:00 [WRN] Login attempt with invalid username: ghjert
2025-07-23 12:24:32.716 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:24:32.718 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 9.46ms
2025-07-23 12:24:32.721 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:32.722 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 21.9338ms
2025-07-23 12:24:32.727 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 44
2025-07-23 12:24:32.730 +03:00 [INF] CORS policy execution successful.
2025-07-23 12:24:32.731 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:32.732 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 12:24:32.737 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 12:24:32.743 +03:00 [WRN] Login attempt with invalid username: ghjert
2025-07-23 12:24:32.744 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 12:24:32.746 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 11.2603ms
2025-07-23 12:24:32.747 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 12:24:32.749 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 21.3147ms
2025-07-23 15:55:43.153 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 15:55:43.175 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 15:55:43.184 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 15:55:43.186 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 33.4087ms
2025-07-23 15:57:47.010 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 15:57:47.115 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '21/07/2025 08:53:28 ص', Current time (UTC): '23/07/2025 12:57:47 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-23 15:57:47.182 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '21/07/2025 08:53:28 ص', Current time (UTC): '23/07/2025 12:57:47 م'.
2025-07-23 15:57:47.186 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '21/07/2025 08:53:28 ص', Current time (UTC): '23/07/2025 12:57:47 م'.
2025-07-23 15:57:47.189 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 15:57:47.197 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 15:57:47.202 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 192.6849ms
2025-07-23 15:58:12.244 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 52
2025-07-23 15:58:12.252 +03:00 [INF] CORS policy execution successful.
2025-07-23 15:58:12.255 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '21/07/2025 08:53:28 ص', Current time (UTC): '23/07/2025 12:58:12 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-23 15:58:12.257 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '21/07/2025 08:53:28 ص', Current time (UTC): '23/07/2025 12:58:12 م'.
2025-07-23 15:58:12.262 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '21/07/2025 08:53:28 ص', Current time (UTC): '23/07/2025 12:58:12 م'.
2025-07-23 15:58:12.264 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 15:58:12.265 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 15:58:12.395 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 15:58:12.499 +03:00 [INF] Access token generated for user 1
2025-07-23 15:58:12.508 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 15:58:12.513 +03:00 [INF] User logged in successfully: admin
2025-07-23 15:58:12.514 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 15:58:12.516 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 248.5913ms
2025-07-23 15:58:12.517 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 15:58:12.519 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 275.3996ms
2025-07-23 16:00:03.910 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 16:00:03.937 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 16:00:03.944 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-23 16:00:03.992 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-23 16:00:04.021 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 16:00:04.038 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 90.6122ms
2025-07-23 16:00:04.040 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 16:00:04.041 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 131.1578ms
2025-07-23 16:24:11.814 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 16:24:11.825 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 16:24:11.827 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-23 16:24:11.957 +03:00 [INF] Executed DbCommand (26ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-23 16:24:11.962 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 16:24:11.964 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 131.9755ms
2025-07-23 16:24:11.965 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 16:24:11.966 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 152.302ms
2025-07-23 16:39:38.452 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 16:39:38.460 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:39:38.462 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 10.1291ms
2025-07-23 16:39:38.470 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 16:39:38.474 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:39:38.478 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:39:38.480 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 16:39:38.612 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 16:39:38.716 +03:00 [INF] Access token generated for user 1
2025-07-23 16:39:38.725 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 16:39:38.729 +03:00 [INF] User logged in successfully: admin
2025-07-23 16:39:38.731 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 16:39:38.733 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 249.341ms
2025-07-23 16:39:38.735 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:39:38.737 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 267.3522ms
2025-07-23 16:40:39.782 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/auth/login - null null
2025-07-23 16:40:39.808 +03:00 [INF] Executing endpoint '405 HTTP Method Not Supported'
2025-07-23 16:40:39.810 +03:00 [INF] Executed endpoint '405 HTTP Method Not Supported'
2025-07-23 16:40:39.811 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/auth/login - 405 0 null 28.7127ms
2025-07-23 16:48:15.239 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 16:48:15.249 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:48:15.251 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 11.4456ms
2025-07-23 16:48:15.261 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 16:48:15.264 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:48:15.265 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:48:15.267 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 16:48:15.417 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 16:48:15.511 +03:00 [INF] Access token generated for user 1
2025-07-23 16:48:15.521 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 16:48:15.525 +03:00 [INF] User logged in successfully: admin
2025-07-23 16:48:15.526 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 16:48:15.527 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 256.4927ms
2025-07-23 16:48:15.529 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:48:15.531 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 269.6784ms
2025-07-23 16:49:41.495 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 16:49:41.503 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:49:41.504 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 9.1576ms
2025-07-23 16:49:41.512 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 16:49:41.514 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:49:41.515 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:49:41.517 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 16:49:41.553 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 16:49:41.655 +03:00 [INF] Access token generated for user 1
2025-07-23 16:49:41.662 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 16:49:41.665 +03:00 [INF] User logged in successfully: admin
2025-07-23 16:49:41.667 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 16:49:41.669 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 149.8254ms
2025-07-23 16:49:41.671 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:49:41.673 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 160.8594ms
2025-07-23 16:49:41.680 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:49:41.687 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:49:41.689 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 9.0486ms
2025-07-23 16:49:41.696 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 16:49:41.753 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:49:41.757 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:49:41.759 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 5.7572ms
2025-07-23 16:49:41.763 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 16:49:41.768 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:49:41.774 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:49:41.776 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 7.7537ms
2025-07-23 16:49:41.780 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 16:50:38.608 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 16:50:38.617 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:50:38.619 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 10.6278ms
2025-07-23 16:50:38.625 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 16:50:38.633 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:50:38.635 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:50:38.636 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 16:50:38.668 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 16:50:38.766 +03:00 [INF] Access token generated for user 1
2025-07-23 16:50:38.774 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 16:50:38.777 +03:00 [INF] User logged in successfully: admin
2025-07-23 16:50:38.778 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 16:50:38.780 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 141.2027ms
2025-07-23 16:50:38.781 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:50:38.783 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 158.1198ms
2025-07-23 16:50:38.792 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:50:38.795 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:50:38.796 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 4.5047ms
2025-07-23 16:50:38.803 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 16:50:38.821 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:50:38.825 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:50:38.827 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 6.6131ms
2025-07-23 16:50:38.839 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 16:50:38.882 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:50:38.886 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:50:38.888 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 6.189ms
2025-07-23 16:50:38.893 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 16:55:53.421 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 16:55:53.424 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:55:53.427 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 5.1683ms
2025-07-23 16:55:53.434 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 16:55:53.437 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:55:53.438 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:55:53.439 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 16:55:53.571 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 16:55:53.675 +03:00 [INF] Access token generated for user 1
2025-07-23 16:55:53.684 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 16:55:53.687 +03:00 [INF] User logged in successfully: admin
2025-07-23 16:55:53.688 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 16:55:53.689 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 247.9346ms
2025-07-23 16:55:53.691 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 16:55:53.692 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 258.0279ms
2025-07-23 16:55:53.701 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:55:53.704 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:55:53.706 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 4.6652ms
2025-07-23 16:55:53.714 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 16:55:53.744 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:55:53.750 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:55:53.752 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 7.9417ms
2025-07-23 16:55:53.758 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 16:55:53.817 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:55:53.826 +03:00 [INF] CORS policy execution successful.
2025-07-23 16:55:53.829 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 11.0848ms
2025-07-23 16:55:53.833 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 16:56:15.537 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - null null
2025-07-23 16:56:15.549 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/http://localhost:5184/api/User/profile - 404 0 null 12.4046ms
2025-07-23 16:56:15.552 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/api/http://localhost:5184/api/User/profile, Response status code: 404
2025-07-23 17:19:17.083 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 17:19:17.088 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:19:17.090 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 6.9653ms
2025-07-23 17:19:17.098 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:19:17.105 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:19:17.106 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:19:17.107 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:19:17.229 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:19:17.334 +03:00 [INF] Access token generated for user 1
2025-07-23 17:19:17.341 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 17:19:17.346 +03:00 [INF] User logged in successfully: admin
2025-07-23 17:19:17.351 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:19:17.352 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 242.5816ms
2025-07-23 17:19:17.354 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:19:17.355 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 259.7384ms
2025-07-23 17:19:17.364 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:19:17.368 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:19:17.370 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:19:17.371 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:19:17.373 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 9.0919ms
2025-07-23 17:19:17.410 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:19:17.415 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:19:17.417 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:19:17.421 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:19:17.429 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 18.8056ms
2025-07-23 17:19:17.537 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:19:17.541 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:19:17.543 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:19:17.544 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:19:17.545 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 8.5185ms
2025-07-23 17:19:29.471 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:19:29.484 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:19:29.485 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:19:29.489 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 17.9826ms
2025-07-23 17:19:57.700 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:19:57.708 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:19:57.710 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:19:57.712 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 11.8629ms
2025-07-23 17:32:49.845 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 17:32:49.855 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:32:49.858 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 12.863ms
2025-07-23 17:32:49.863 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:32:49.874 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:32:49.875 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:32:49.876 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:32:49.988 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:32:50.093 +03:00 [INF] Access token generated for user 1
2025-07-23 17:32:50.101 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 17:32:50.104 +03:00 [INF] User logged in successfully: admin
2025-07-23 17:32:50.105 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:32:50.107 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 227.9918ms
2025-07-23 17:32:50.112 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:32:50.113 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 249.847ms
2025-07-23 17:32:50.120 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:32:50.126 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:32:50.127 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:32:50.128 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:32:50.130 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 9.6578ms
2025-07-23 17:32:50.164 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:32:50.169 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:32:50.174 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:32:50.176 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:32:50.180 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 16.4017ms
2025-07-23 17:32:50.263 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:32:50.267 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:32:50.268 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:32:50.269 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:32:50.271 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 8.0957ms
2025-07-23 17:33:01.958 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:33:01.972 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:33:01.974 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:33:01.975 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 17.3098ms
2025-07-23 17:39:39.782 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 17:39:39.791 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:39:39.793 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 10.9759ms
2025-07-23 17:39:39.805 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:39:39.810 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:39:39.811 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:39:39.814 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:39:39.847 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:39:39.952 +03:00 [INF] Access token generated for user 1
2025-07-23 17:39:39.959 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 17:39:39.962 +03:00 [INF] User logged in successfully: admin
2025-07-23 17:39:39.963 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:39:39.965 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 147.6811ms
2025-07-23 17:39:39.966 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:39:39.971 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 166.4954ms
2025-07-23 17:39:39.976 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:39:39.979 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:39:39.980 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:39:39.981 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:39:39.982 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 5.8212ms
2025-07-23 17:39:40.023 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:39:40.029 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:39:40.032 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:39:40.034 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:39:40.035 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 12.2216ms
2025-07-23 17:39:40.104 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:39:40.108 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:39:40.109 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:39:40.111 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:39:40.112 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 8.6141ms
2025-07-23 17:40:44.687 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 17:40:44.695 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:40:44.696 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 9.1383ms
2025-07-23 17:40:44.705 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:40:44.709 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:40:44.710 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:40:44.711 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:40:44.748 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:40:44.856 +03:00 [INF] Access token generated for user 1
2025-07-23 17:40:44.863 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 17:40:44.867 +03:00 [INF] User logged in successfully: admin
2025-07-23 17:40:44.869 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:40:44.870 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 156.0932ms
2025-07-23 17:40:44.872 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:40:44.878 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 172.0669ms
2025-07-23 17:40:44.884 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:40:44.887 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:40:44.890 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:40:44.892 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:40:44.894 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 9.4465ms
2025-07-23 17:40:44.995 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:40:45.004 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:40:45.006 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:40:45.008 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:40:45.011 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 15.255ms
2025-07-23 17:40:45.066 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:40:45.071 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:40:45.073 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:40:45.074 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:40:45.075 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 9.5272ms
2025-07-23 17:43:21.860 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 17:43:21.870 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:43:21.873 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 12.7923ms
2025-07-23 17:43:21.886 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:43:21.889 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:43:21.891 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:43:21.892 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:43:21.948 +03:00 [INF] Executed DbCommand (51ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:43:22.065 +03:00 [WRN] Login attempt with invalid password for user: admin
2025-07-23 17:43:22.067 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:43:22.069 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 173.5821ms
2025-07-23 17:43:22.071 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:43:22.073 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 187.0597ms
2025-07-23 17:43:22.080 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:43:22.086 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:43:22.087 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:43:22.089 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:43:22.122 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:43:22.255 +03:00 [WRN] Login attempt with invalid password for user: admin
2025-07-23 17:43:22.256 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:43:22.258 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 167.3778ms
2025-07-23 17:43:22.260 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:43:22.261 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 180.8621ms
2025-07-23 17:43:22.267 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:43:22.280 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:43:22.281 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:43:22.282 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:43:22.313 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:43:22.478 +03:00 [WRN] Login attempt with invalid password for user: admin
2025-07-23 17:43:22.479 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:43:22.481 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 197.2294ms
2025-07-23 17:43:22.483 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:43:22.485 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 401 null application/json; charset=utf-8 217.667ms
2025-07-23 17:43:39.204 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 17:43:39.208 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:43:39.210 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 5.8854ms
2025-07-23 17:43:39.217 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:43:39.222 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:43:39.224 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:43:39.225 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:43:39.261 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:43:39.358 +03:00 [INF] Access token generated for user 1
2025-07-23 17:43:39.364 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 17:43:39.368 +03:00 [INF] User logged in successfully: admin
2025-07-23 17:43:39.370 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:43:39.372 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 144.1097ms
2025-07-23 17:43:39.380 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:43:39.381 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 164.8052ms
2025-07-23 17:43:39.388 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:43:39.393 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:43:39.394 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:43:39.395 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:43:39.396 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 8.1989ms
2025-07-23 17:43:39.420 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:43:39.424 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:43:39.425 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:43:39.427 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:43:39.428 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 8.4294ms
2025-07-23 17:43:39.496 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:43:39.502 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:43:39.504 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:43:39.506 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:43:39.510 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 14.2879ms
2025-07-23 17:45:53.471 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 17:45:53.480 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:45:53.481 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 9.9491ms
2025-07-23 17:45:53.489 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:45:53.492 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:45:53.493 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:45:53.495 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:45:53.527 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:45:53.631 +03:00 [INF] Access token generated for user 1
2025-07-23 17:45:53.637 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 17:45:53.640 +03:00 [INF] User logged in successfully: admin
2025-07-23 17:45:53.644 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:45:53.646 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 148.9101ms
2025-07-23 17:45:53.648 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:45:53.649 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 159.7793ms
2025-07-23 17:45:53.656 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:45:53.661 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:45:53.663 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:45:53.664 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:45:53.665 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 8.8306ms
2025-07-23 17:45:53.693 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:45:53.698 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:45:53.700 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:45:53.701 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:45:53.706 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 12.4837ms
2025-07-23 17:45:53.772 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:45:53.777 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:45:53.778 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:45:53.779 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:45:53.781 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 8.1255ms
2025-07-23 17:51:48.975 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 17:51:48.985 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:51:48.988 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 12.6698ms
2025-07-23 17:51:48.994 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 17:51:49.003 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:51:49.004 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:51:49.006 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 17:51:49.128 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 17:51:49.233 +03:00 [INF] Access token generated for user 1
2025-07-23 17:51:49.240 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 17:51:49.244 +03:00 [INF] User logged in successfully: admin
2025-07-23 17:51:49.244 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:51:49.246 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 237.142ms
2025-07-23 17:51:49.247 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 17:51:49.248 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 254.015ms
2025-07-23 17:51:49.260 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 17:51:49.263 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:51:49.264 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 17:51:49.265 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 17:51:49.270 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 9.7698ms
2025-07-23 17:51:49.301 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-07-23 17:51:49.305 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:51:49.308 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 6.3501ms
2025-07-23 17:51:49.332 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - application/json null
2025-07-23 17:51:49.336 +03:00 [INF] CORS policy execution successful.
2025-07-23 17:51:49.339 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 17:51:49.340 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-23 17:51:49.356 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-23 17:51:49.369 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 17:51:49.371 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 25.8813ms
2025-07-23 17:51:49.373 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 17:51:49.380 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 48.0955ms
2025-07-23 18:08:23.491 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>
2025-07-23 18:08:23.542 +03:00 [INF] The file /Uploads/Users/<USER>
2025-07-23 18:08:23.545 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>/jpeg 54.9568ms
2025-07-23 18:53:20.455 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-23 18:53:20.487 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 31.5716ms
2025-07-23 18:53:20.524 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-23 18:53:20.538 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 13.6296ms
2025-07-23 18:53:20.748 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-23 18:53:20.779 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 31.4292ms
2025-07-23 18:53:21.103 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-23 18:53:21.159 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 56.3294ms
2025-07-23 18:53:43.648 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 18:53:43.669 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 18:53:43.670 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-23 18:53:43.781 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-23 18:53:43.784 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 18:53:43.786 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 113.8042ms
2025-07-23 18:53:43.787 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 18:53:43.788 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 140.6345ms
2025-07-23 19:08:24.889 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-23 19:08:24.898 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.3098ms
2025-07-23 19:08:25.214 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-23 19:08:25.230 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 16.0815ms
2025-07-23 19:08:35.579 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 19:08:35.596 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '23/07/2025 03:51:49 م', Current time (UTC): '23/07/2025 04:08:35 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-23 19:08:35.600 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '23/07/2025 03:51:49 م', Current time (UTC): '23/07/2025 04:08:35 م'.
2025-07-23 19:08:35.601 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '23/07/2025 03:51:49 م', Current time (UTC): '23/07/2025 04:08:35 م'.
2025-07-23 19:08:35.603 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:08:35.608 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:08:35.610 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 31.1977ms
2025-07-23 19:08:55.144 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-07-23 19:08:55.154 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 9.8683ms
2025-07-23 19:08:55.191 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-07-23 19:08:55.191 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-07-23 19:08:55.200 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.1543ms
2025-07-23 19:08:55.220 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 29.3293ms
2025-07-23 19:08:55.301 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-07-23 19:08:55.319 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 17.1832ms
2025-07-23 19:09:14.451 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 19:09:14.467 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '23/07/2025 03:51:49 م', Current time (UTC): '23/07/2025 04:09:14 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-23 19:09:14.472 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '23/07/2025 03:51:49 م', Current time (UTC): '23/07/2025 04:09:14 م'.
2025-07-23 19:09:14.473 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '23/07/2025 03:51:49 م', Current time (UTC): '23/07/2025 04:09:14 م'.
2025-07-23 19:09:14.474 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:09:14.478 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:09:14.480 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 29.4117ms
2025-07-23 19:10:28.212 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 19:10:28.221 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:10:28.222 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 10.6411ms
2025-07-23 19:10:28.232 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 19:10:28.236 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:10:28.238 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 19:10:28.239 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 19:10:28.361 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 19:10:28.466 +03:00 [INF] Access token generated for user 1
2025-07-23 19:10:28.475 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 19:10:28.478 +03:00 [INF] User logged in successfully: admin
2025-07-23 19:10:28.479 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 19:10:28.483 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 241.9089ms
2025-07-23 19:10:28.485 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 19:10:28.487 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 255.0591ms
2025-07-23 19:10:28.495 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-07-23 19:10:28.500 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:10:28.501 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 5.9731ms
2025-07-23 19:10:28.505 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - application/json null
2025-07-23 19:10:28.508 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:10:28.509 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '23/07/2025 03:51:49 م', Current time (UTC): '23/07/2025 04:10:28 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-07-23 19:10:28.514 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '23/07/2025 03:51:49 م', Current time (UTC): '23/07/2025 04:10:28 م'.
2025-07-23 19:10:28.516 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '23/07/2025 03:51:49 م', Current time (UTC): '23/07/2025 04:10:28 م'.
2025-07-23 19:10:28.517 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:10:28.518 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:10:28.520 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 14.7248ms
2025-07-23 19:10:28.551 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - application/json null
2025-07-23 19:10:28.556 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:10:28.559 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 19:10:28.561 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-23 19:10:28.578 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-23 19:10:28.583 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 19:10:28.585 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 20.2964ms
2025-07-23 19:10:28.587 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 19:10:28.589 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 38.0228ms
2025-07-23 19:23:37.121 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 19:23:37.129 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:23:37.130 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 9.5985ms
2025-07-23 19:23:37.142 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 19:23:37.145 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:23:37.146 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 19:23:37.147 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 19:23:37.269 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 19:23:37.371 +03:00 [INF] Access token generated for user 1
2025-07-23 19:23:37.378 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 19:23:37.383 +03:00 [INF] User logged in successfully: admin
2025-07-23 19:23:37.384 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 19:23:37.386 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 234.3049ms
2025-07-23 19:23:37.387 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 19:23:37.388 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 246.0925ms
2025-07-23 19:23:37.396 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 19:23:37.403 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:23:37.404 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:23:37.405 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:23:37.407 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 10.334ms
2025-07-23 19:23:37.480 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-07-23 19:23:37.486 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:23:37.487 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 7.2078ms
2025-07-23 19:23:37.495 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - application/json null
2025-07-23 19:23:37.498 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:23:37.500 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 19:23:37.501 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-23 19:23:37.511 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-23 19:23:37.514 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 19:23:37.516 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 12.3592ms
2025-07-23 19:23:37.517 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 19:23:37.519 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 23.5631ms
2025-07-23 19:26:09.692 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - null null
2025-07-23 19:26:09.701 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:09.702 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/login - 204 null null 9.8237ms
2025-07-23 19:26:09.712 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/login - application/json 43
2025-07-23 19:26:09.716 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:09.717 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 19:26:09.719 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 19:26:09.753 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-07-23 19:26:09.844 +03:00 [INF] Access token generated for user 1
2025-07-23 19:26:09.851 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-07-23 19:26:09.854 +03:00 [INF] User logged in successfully: admin
2025-07-23 19:26:09.855 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 19:26:09.857 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 135.1147ms
2025-07-23 19:26:09.858 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-07-23 19:26:09.859 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/login - 200 null application/json; charset=utf-8 147.0845ms
2025-07-23 19:26:09.867 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-07-23 19:26:09.873 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:09.874 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:09.875 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:09.876 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 9.4725ms
2025-07-23 19:26:09.908 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-07-23 19:26:09.912 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:09.915 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 7.1525ms
2025-07-23 19:26:09.919 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - application/json null
2025-07-23 19:26:09.923 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:09.926 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 19:26:09.927 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-07-23 19:26:09.948 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-07-23 19:26:09.952 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-23 19:26:09.955 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 21.2386ms
2025-07-23 19:26:09.958 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-07-23 19:26:09.964 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 45.0633ms
2025-07-23 19:26:13.450 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/logout - null null
2025-07-23 19:26:13.454 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:13.455 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/logout - 204 null null 4.6778ms
2025-07-23 19:26:13.459 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:13.462 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:13.463 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-23 19:26:13.468 +03:00 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Logout() on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-07-23 19:26:24.736 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId]
FROM [RefreshTokens] AS [r]
WHERE [r].[UserId] = @__userId_0 AND [r].[IsRevoked] = CAST(0 AS bit)
2025-07-23 19:26:24.957 +03:00 [INF] Executed DbCommand (155ms) [Parameters=[@p12='?' (DbType = Int32), @p0='?' (DbType = DateTime2), @p1='?' (Size = 4000), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime2), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000), @p11='?' (DbType = Int32), @p25='?' (DbType = Int32), @p13='?' (DbType = DateTime2), @p14='?' (Size = 4000), @p15='?' (DbType = Boolean), @p16='?' (DbType = Boolean), @p17='?' (DbType = Boolean), @p18='?' (DbType = DateTime2), @p19='?' (Size = 4000), @p20='?' (Size = 4000), @p21='?' (DbType = DateTime2), @p22='?' (DbType = Int32), @p23='?' (Size = 4000), @p24='?' (DbType = Int32), @p38='?' (DbType = Int32), @p26='?' (DbType = DateTime2), @p27='?' (Size = 4000), @p28='?' (DbType = Boolean), @p29='?' (DbType = Boolean), @p30='?' (DbType = Boolean), @p31='?' (DbType = DateTime2), @p32='?' (Size = 4000), @p33='?' (Size = 4000), @p34='?' (DbType = DateTime2), @p35='?' (DbType = Int32), @p36='?' (Size = 4000), @p37='?' (DbType = Int32), @p51='?' (DbType = Int32), @p39='?' (DbType = DateTime2), @p40='?' (Size = 4000), @p41='?' (DbType = Boolean), @p42='?' (DbType = Boolean), @p43='?' (DbType = Boolean), @p44='?' (DbType = DateTime2), @p45='?' (Size = 4000), @p46='?' (Size = 4000), @p47='?' (DbType = DateTime2), @p48='?' (DbType = Int32), @p49='?' (Size = 4000), @p50='?' (DbType = Int32), @p64='?' (DbType = Int32), @p52='?' (DbType = DateTime2), @p53='?' (Size = 4000), @p54='?' (DbType = Boolean), @p55='?' (DbType = Boolean), @p56='?' (DbType = Boolean), @p57='?' (DbType = DateTime2), @p58='?' (Size = 4000), @p59='?' (Size = 4000), @p60='?' (DbType = DateTime2), @p61='?' (DbType = Int32), @p62='?' (Size = 4000), @p63='?' (DbType = Int32), @p77='?' (DbType = Int32), @p65='?' (DbType = DateTime2), @p66='?' (Size = 4000), @p67='?' (DbType = Boolean), @p68='?' (DbType = Boolean), @p69='?' (DbType = Boolean), @p70='?' (DbType = DateTime2), @p71='?' (Size = 4000), @p72='?' (Size = 4000), @p73='?' (DbType = DateTime2), @p74='?' (DbType = Int32), @p75='?' (Size = 4000), @p76='?' (DbType = Int32), @p90='?' (DbType = Int32), @p78='?' (DbType = DateTime2), @p79='?' (Size = 4000), @p80='?' (DbType = Boolean), @p81='?' (DbType = Boolean), @p82='?' (DbType = Boolean), @p83='?' (DbType = DateTime2), @p84='?' (Size = 4000), @p85='?' (Size = 4000), @p86='?' (DbType = DateTime2), @p87='?' (DbType = Int32), @p88='?' (Size = 4000), @p89='?' (DbType = Int32), @p103='?' (DbType = Int32), @p91='?' (DbType = DateTime2), @p92='?' (Size = 4000), @p93='?' (DbType = Boolean), @p94='?' (DbType = Boolean), @p95='?' (DbType = Boolean), @p96='?' (DbType = DateTime2), @p97='?' (Size = 4000), @p98='?' (Size = 4000), @p99='?' (DbType = DateTime2), @p100='?' (DbType = Int32), @p101='?' (Size = 4000), @p102='?' (DbType = Int32), @p116='?' (DbType = Int32), @p104='?' (DbType = DateTime2), @p105='?' (Size = 4000), @p106='?' (DbType = Boolean), @p107='?' (DbType = Boolean), @p108='?' (DbType = Boolean), @p109='?' (DbType = DateTime2), @p110='?' (Size = 4000), @p111='?' (Size = 4000), @p112='?' (DbType = DateTime2), @p113='?' (DbType = Int32), @p114='?' (Size = 4000), @p115='?' (DbType = Int32), @p129='?' (DbType = Int32), @p117='?' (DbType = DateTime2), @p118='?' (Size = 4000), @p119='?' (DbType = Boolean), @p120='?' (DbType = Boolean), @p121='?' (DbType = Boolean), @p122='?' (DbType = DateTime2), @p123='?' (Size = 4000), @p124='?' (Size = 4000), @p125='?' (DbType = DateTime2), @p126='?' (DbType = Int32), @p127='?' (Size = 4000), @p128='?' (DbType = Int32), @p142='?' (DbType = Int32), @p130='?' (DbType = DateTime2), @p131='?' (Size = 4000), @p132='?' (DbType = Boolean), @p133='?' (DbType = Boolean), @p134='?' (DbType = Boolean), @p135='?' (DbType = DateTime2), @p136='?' (Size = 4000), @p137='?' (Size = 4000), @p138='?' (DbType = DateTime2), @p139='?' (DbType = Int32), @p140='?' (Size = 4000), @p141='?' (DbType = Int32), @p155='?' (DbType = Int32), @p143='?' (DbType = DateTime2), @p144='?' (Size = 4000), @p145='?' (DbType = Boolean), @p146='?' (DbType = Boolean), @p147='?' (DbType = Boolean), @p148='?' (DbType = DateTime2), @p149='?' (Size = 4000), @p150='?' (Size = 4000), @p151='?' (DbType = DateTime2), @p152='?' (DbType = Int32), @p153='?' (Size = 4000), @p154='?' (DbType = Int32), @p168='?' (DbType = Int32), @p156='?' (DbType = DateTime2), @p157='?' (Size = 4000), @p158='?' (DbType = Boolean), @p159='?' (DbType = Boolean), @p160='?' (DbType = Boolean), @p161='?' (DbType = DateTime2), @p162='?' (Size = 4000), @p163='?' (Size = 4000), @p164='?' (DbType = DateTime2), @p165='?' (DbType = Int32), @p166='?' (Size = 4000), @p167='?' (DbType = Int32), @p181='?' (DbType = Int32), @p169='?' (DbType = DateTime2), @p170='?' (Size = 4000), @p171='?' (DbType = Boolean), @p172='?' (DbType = Boolean), @p173='?' (DbType = Boolean), @p174='?' (DbType = DateTime2), @p175='?' (Size = 4000), @p176='?' (Size = 4000), @p177='?' (DbType = DateTime2), @p178='?' (DbType = Int32), @p179='?' (Size = 4000), @p180='?' (DbType = Int32), @p194='?' (DbType = Int32), @p182='?' (DbType = DateTime2), @p183='?' (Size = 4000), @p184='?' (DbType = Boolean), @p185='?' (DbType = Boolean), @p186='?' (DbType = Boolean), @p187='?' (DbType = DateTime2), @p188='?' (Size = 4000), @p189='?' (Size = 4000), @p190='?' (DbType = DateTime2), @p191='?' (DbType = Int32), @p192='?' (Size = 4000), @p193='?' (DbType = Int32), @p207='?' (DbType = Int32), @p195='?' (DbType = DateTime2), @p196='?' (Size = 4000), @p197='?' (DbType = Boolean), @p198='?' (DbType = Boolean), @p199='?' (DbType = Boolean), @p200='?' (DbType = DateTime2), @p201='?' (Size = 4000), @p202='?' (Size = 4000), @p203='?' (DbType = DateTime2), @p204='?' (DbType = Int32), @p205='?' (Size = 4000), @p206='?' (DbType = Int32), @p220='?' (DbType = Int32), @p208='?' (DbType = DateTime2), @p209='?' (Size = 4000), @p210='?' (DbType = Boolean), @p211='?' (DbType = Boolean), @p212='?' (DbType = Boolean), @p213='?' (DbType = DateTime2), @p214='?' (Size = 4000), @p215='?' (Size = 4000), @p216='?' (DbType = DateTime2), @p217='?' (DbType = Int32), @p218='?' (Size = 4000), @p219='?' (DbType = Int32), @p233='?' (DbType = Int32), @p221='?' (DbType = DateTime2), @p222='?' (Size = 4000), @p223='?' (DbType = Boolean), @p224='?' (DbType = Boolean), @p225='?' (DbType = Boolean), @p226='?' (DbType = DateTime2), @p227='?' (Size = 4000), @p228='?' (Size = 4000), @p229='?' (DbType = DateTime2), @p230='?' (DbType = Int32), @p231='?' (Size = 4000), @p232='?' (DbType = Int32), @p246='?' (DbType = Int32), @p234='?' (DbType = DateTime2), @p235='?' (Size = 4000), @p236='?' (DbType = Boolean), @p237='?' (DbType = Boolean), @p238='?' (DbType = Boolean), @p239='?' (DbType = DateTime2), @p240='?' (Size = 4000), @p241='?' (Size = 4000), @p242='?' (DbType = DateTime2), @p243='?' (DbType = Int32), @p244='?' (Size = 4000), @p245='?' (DbType = Int32), @p259='?' (DbType = Int32), @p247='?' (DbType = DateTime2), @p248='?' (Size = 4000), @p249='?' (DbType = Boolean), @p250='?' (DbType = Boolean), @p251='?' (DbType = Boolean), @p252='?' (DbType = DateTime2), @p253='?' (Size = 4000), @p254='?' (Size = 4000), @p255='?' (DbType = DateTime2), @p256='?' (DbType = Int32), @p257='?' (Size = 4000), @p258='?' (DbType = Int32), @p272='?' (DbType = Int32), @p260='?' (DbType = DateTime2), @p261='?' (Size = 4000), @p262='?' (DbType = Boolean), @p263='?' (DbType = Boolean), @p264='?' (DbType = Boolean), @p265='?' (DbType = DateTime2), @p266='?' (Size = 4000), @p267='?' (Size = 4000), @p268='?' (DbType = DateTime2), @p269='?' (DbType = Int32), @p270='?' (Size = 4000), @p271='?' (DbType = Int32), @p285='?' (DbType = Int32), @p273='?' (DbType = DateTime2), @p274='?' (Size = 4000), @p275='?' (DbType = Boolean), @p276='?' (DbType = Boolean), @p277='?' (DbType = Boolean), @p278='?' (DbType = DateTime2), @p279='?' (Size = 4000), @p280='?' (Size = 4000), @p281='?' (DbType = DateTime2), @p282='?' (DbType = Int32), @p283='?' (Size = 4000), @p284='?' (DbType = Int32), @p298='?' (DbType = Int32), @p286='?' (DbType = DateTime2), @p287='?' (Size = 4000), @p288='?' (DbType = Boolean), @p289='?' (DbType = Boolean), @p290='?' (DbType = Boolean), @p291='?' (DbType = DateTime2), @p292='?' (Size = 4000), @p293='?' (Size = 4000), @p294='?' (DbType = DateTime2), @p295='?' (DbType = Int32), @p296='?' (Size = 4000), @p297='?' (DbType = Int32), @p311='?' (DbType = Int32), @p299='?' (DbType = DateTime2), @p300='?' (Size = 4000), @p301='?' (DbType = Boolean), @p302='?' (DbType = Boolean), @p303='?' (DbType = Boolean), @p304='?' (DbType = DateTime2), @p305='?' (Size = 4000), @p306='?' (Size = 4000), @p307='?' (DbType = DateTime2), @p308='?' (DbType = Int32), @p309='?' (Size = 4000), @p310='?' (DbType = Int32), @p324='?' (DbType = Int32), @p312='?' (DbType = DateTime2), @p313='?' (Size = 4000), @p314='?' (DbType = Boolean), @p315='?' (DbType = Boolean), @p316='?' (DbType = Boolean), @p317='?' (DbType = DateTime2), @p318='?' (Size = 4000), @p319='?' (Size = 4000), @p320='?' (DbType = DateTime2), @p321='?' (DbType = Int32), @p322='?' (Size = 4000), @p323='?' (DbType = Int32), @p337='?' (DbType = Int32), @p325='?' (DbType = DateTime2), @p326='?' (Size = 4000), @p327='?' (DbType = Boolean), @p328='?' (DbType = Boolean), @p329='?' (DbType = Boolean), @p330='?' (DbType = DateTime2), @p331='?' (Size = 4000), @p332='?' (Size = 4000), @p333='?' (DbType = DateTime2), @p334='?' (DbType = Int32), @p335='?' (Size = 4000), @p336='?' (DbType = Int32), @p350='?' (DbType = Int32), @p338='?' (DbType = DateTime2), @p339='?' (Size = 4000), @p340='?' (DbType = Boolean), @p341='?' (DbType = Boolean), @p342='?' (DbType = Boolean), @p343='?' (DbType = DateTime2), @p344='?' (Size = 4000), @p345='?' (Size = 4000), @p346='?' (DbType = DateTime2), @p347='?' (DbType = Int32), @p348='?' (Size = 4000), @p349='?' (DbType = Int32), @p363='?' (DbType = Int32), @p351='?' (DbType = DateTime2), @p352='?' (Size = 4000), @p353='?' (DbType = Boolean), @p354='?' (DbType = Boolean), @p355='?' (DbType = Boolean), @p356='?' (DbType = DateTime2), @p357='?' (Size = 4000), @p358='?' (Size = 4000), @p359='?' (DbType = DateTime2), @p360='?' (DbType = Int32), @p361='?' (Size = 4000), @p362='?' (DbType = Int32), @p376='?' (DbType = Int32), @p364='?' (DbType = DateTime2), @p365='?' (Size = 4000), @p366='?' (DbType = Boolean), @p367='?' (DbType = Boolean), @p368='?' (DbType = Boolean), @p369='?' (DbType = DateTime2), @p370='?' (Size = 4000), @p371='?' (Size = 4000), @p372='?' (DbType = DateTime2), @p373='?' (DbType = Int32), @p374='?' (Size = 4000), @p375='?' (DbType = Int32), @p389='?' (DbType = Int32), @p377='?' (DbType = DateTime2), @p378='?' (Size = 4000), @p379='?' (DbType = Boolean), @p380='?' (DbType = Boolean), @p381='?' (DbType = Boolean), @p382='?' (DbType = DateTime2), @p383='?' (Size = 4000), @p384='?' (Size = 4000), @p385='?' (DbType = DateTime2), @p386='?' (DbType = Int32), @p387='?' (Size = 4000), @p388='?' (DbType = Int32), @p402='?' (DbType = Int32), @p390='?' (DbType = DateTime2), @p391='?' (Size = 4000), @p392='?' (DbType = Boolean), @p393='?' (DbType = Boolean), @p394='?' (DbType = Boolean), @p395='?' (DbType = DateTime2), @p396='?' (Size = 4000), @p397='?' (Size = 4000), @p398='?' (DbType = DateTime2), @p399='?' (DbType = Int32), @p400='?' (Size = 4000), @p401='?' (DbType = Int32), @p415='?' (DbType = Int32), @p403='?' (DbType = DateTime2), @p404='?' (Size = 4000), @p405='?' (DbType = Boolean), @p406='?' (DbType = Boolean), @p407='?' (DbType = Boolean), @p408='?' (DbType = DateTime2), @p409='?' (Size = 4000), @p410='?' (Size = 4000), @p411='?' (DbType = DateTime2), @p412='?' (DbType = Int32), @p413='?' (Size = 4000), @p414='?' (DbType = Int32), @p428='?' (DbType = Int32), @p416='?' (DbType = DateTime2), @p417='?' (Size = 4000), @p418='?' (DbType = Boolean), @p419='?' (DbType = Boolean), @p420='?' (DbType = Boolean), @p421='?' (DbType = DateTime2), @p422='?' (Size = 4000), @p423='?' (Size = 4000), @p424='?' (DbType = DateTime2), @p425='?' (DbType = Int32), @p426='?' (Size = 4000), @p427='?' (DbType = Int32), @p441='?' (DbType = Int32), @p429='?' (DbType = DateTime2), @p430='?' (Size = 4000), @p431='?' (DbType = Boolean), @p432='?' (DbType = Boolean), @p433='?' (DbType = Boolean), @p434='?' (DbType = DateTime2), @p435='?' (Size = 4000), @p436='?' (Size = 4000), @p437='?' (DbType = DateTime2), @p438='?' (DbType = Int32), @p439='?' (Size = 4000), @p440='?' (DbType = Int32), @p454='?' (DbType = Int32), @p442='?' (DbType = DateTime2), @p443='?' (Size = 4000), @p444='?' (DbType = Boolean), @p445='?' (DbType = Boolean), @p446='?' (DbType = Boolean), @p447='?' (DbType = DateTime2), @p448='?' (Size = 4000), @p449='?' (Size = 4000), @p450='?' (DbType = DateTime2), @p451='?' (DbType = Int32), @p452='?' (Size = 4000), @p453='?' (DbType = Int32), @p467='?' (DbType = Int32), @p455='?' (DbType = DateTime2), @p456='?' (Size = 4000), @p457='?' (DbType = Boolean), @p458='?' (DbType = Boolean), @p459='?' (DbType = Boolean), @p460='?' (DbType = DateTime2), @p461='?' (Size = 4000), @p462='?' (Size = 4000), @p463='?' (DbType = DateTime2), @p464='?' (DbType = Int32), @p465='?' (Size = 4000), @p466='?' (DbType = Int32), @p480='?' (DbType = Int32), @p468='?' (DbType = DateTime2), @p469='?' (Size = 4000), @p470='?' (DbType = Boolean), @p471='?' (DbType = Boolean), @p472='?' (DbType = Boolean), @p473='?' (DbType = DateTime2), @p474='?' (Size = 4000), @p475='?' (Size = 4000), @p476='?' (DbType = DateTime2), @p477='?' (DbType = Int32), @p478='?' (Size = 4000), @p479='?' (DbType = Int32), @p493='?' (DbType = Int32), @p481='?' (DbType = DateTime2), @p482='?' (Size = 4000), @p483='?' (DbType = Boolean), @p484='?' (DbType = Boolean), @p485='?' (DbType = Boolean), @p486='?' (DbType = DateTime2), @p487='?' (Size = 4000), @p488='?' (Size = 4000), @p489='?' (DbType = DateTime2), @p490='?' (DbType = Int32), @p491='?' (Size = 4000), @p492='?' (DbType = Int32), @p506='?' (DbType = Int32), @p494='?' (DbType = DateTime2), @p495='?' (Size = 4000), @p496='?' (DbType = Boolean), @p497='?' (DbType = Boolean), @p498='?' (DbType = Boolean), @p499='?' (DbType = DateTime2), @p500='?' (Size = 4000), @p501='?' (Size = 4000), @p502='?' (DbType = DateTime2), @p503='?' (DbType = Int32), @p504='?' (Size = 4000), @p505='?' (DbType = Int32), @p519='?' (DbType = Int32), @p507='?' (DbType = DateTime2), @p508='?' (Size = 4000), @p509='?' (DbType = Boolean), @p510='?' (DbType = Boolean), @p511='?' (DbType = Boolean), @p512='?' (DbType = DateTime2), @p513='?' (Size = 4000), @p514='?' (Size = 4000), @p515='?' (DbType = DateTime2), @p516='?' (DbType = Int32), @p517='?' (Size = 4000), @p518='?' (DbType = Int32), @p532='?' (DbType = Int32), @p520='?' (DbType = DateTime2), @p521='?' (Size = 4000), @p522='?' (DbType = Boolean), @p523='?' (DbType = Boolean), @p524='?' (DbType = Boolean), @p525='?' (DbType = DateTime2), @p526='?' (Size = 4000), @p527='?' (Size = 4000), @p528='?' (DbType = DateTime2), @p529='?' (DbType = Int32), @p530='?' (Size = 4000), @p531='?' (DbType = Int32), @p545='?' (DbType = Int32), @p533='?' (DbType = DateTime2), @p534='?' (Size = 4000), @p535='?' (DbType = Boolean), @p536='?' (DbType = Boolean), @p537='?' (DbType = Boolean), @p538='?' (DbType = DateTime2), @p539='?' (Size = 4000), @p540='?' (Size = 4000), @p541='?' (DbType = DateTime2), @p542='?' (DbType = Int32), @p543='?' (Size = 4000), @p544='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p0, [IpAddress] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [IsRevoked] = @p4, [RevokedAt] = @p5, [RevokedReason] = @p6, [Token] = @p7, [UpdatedAt] = @p8, [UpdatedBy] = @p9, [UserAgent] = @p10, [UserId] = @p11
OUTPUT 1
WHERE [Id] = @p12;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p13, [IpAddress] = @p14, [IsActive] = @p15, [IsDeleted] = @p16, [IsRevoked] = @p17, [RevokedAt] = @p18, [RevokedReason] = @p19, [Token] = @p20, [UpdatedAt] = @p21, [UpdatedBy] = @p22, [UserAgent] = @p23, [UserId] = @p24
OUTPUT 1
WHERE [Id] = @p25;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p26, [IpAddress] = @p27, [IsActive] = @p28, [IsDeleted] = @p29, [IsRevoked] = @p30, [RevokedAt] = @p31, [RevokedReason] = @p32, [Token] = @p33, [UpdatedAt] = @p34, [UpdatedBy] = @p35, [UserAgent] = @p36, [UserId] = @p37
OUTPUT 1
WHERE [Id] = @p38;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p39, [IpAddress] = @p40, [IsActive] = @p41, [IsDeleted] = @p42, [IsRevoked] = @p43, [RevokedAt] = @p44, [RevokedReason] = @p45, [Token] = @p46, [UpdatedAt] = @p47, [UpdatedBy] = @p48, [UserAgent] = @p49, [UserId] = @p50
OUTPUT 1
WHERE [Id] = @p51;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p52, [IpAddress] = @p53, [IsActive] = @p54, [IsDeleted] = @p55, [IsRevoked] = @p56, [RevokedAt] = @p57, [RevokedReason] = @p58, [Token] = @p59, [UpdatedAt] = @p60, [UpdatedBy] = @p61, [UserAgent] = @p62, [UserId] = @p63
OUTPUT 1
WHERE [Id] = @p64;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p65, [IpAddress] = @p66, [IsActive] = @p67, [IsDeleted] = @p68, [IsRevoked] = @p69, [RevokedAt] = @p70, [RevokedReason] = @p71, [Token] = @p72, [UpdatedAt] = @p73, [UpdatedBy] = @p74, [UserAgent] = @p75, [UserId] = @p76
OUTPUT 1
WHERE [Id] = @p77;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p78, [IpAddress] = @p79, [IsActive] = @p80, [IsDeleted] = @p81, [IsRevoked] = @p82, [RevokedAt] = @p83, [RevokedReason] = @p84, [Token] = @p85, [UpdatedAt] = @p86, [UpdatedBy] = @p87, [UserAgent] = @p88, [UserId] = @p89
OUTPUT 1
WHERE [Id] = @p90;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p91, [IpAddress] = @p92, [IsActive] = @p93, [IsDeleted] = @p94, [IsRevoked] = @p95, [RevokedAt] = @p96, [RevokedReason] = @p97, [Token] = @p98, [UpdatedAt] = @p99, [UpdatedBy] = @p100, [UserAgent] = @p101, [UserId] = @p102
OUTPUT 1
WHERE [Id] = @p103;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p104, [IpAddress] = @p105, [IsActive] = @p106, [IsDeleted] = @p107, [IsRevoked] = @p108, [RevokedAt] = @p109, [RevokedReason] = @p110, [Token] = @p111, [UpdatedAt] = @p112, [UpdatedBy] = @p113, [UserAgent] = @p114, [UserId] = @p115
OUTPUT 1
WHERE [Id] = @p116;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p117, [IpAddress] = @p118, [IsActive] = @p119, [IsDeleted] = @p120, [IsRevoked] = @p121, [RevokedAt] = @p122, [RevokedReason] = @p123, [Token] = @p124, [UpdatedAt] = @p125, [UpdatedBy] = @p126, [UserAgent] = @p127, [UserId] = @p128
OUTPUT 1
WHERE [Id] = @p129;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p130, [IpAddress] = @p131, [IsActive] = @p132, [IsDeleted] = @p133, [IsRevoked] = @p134, [RevokedAt] = @p135, [RevokedReason] = @p136, [Token] = @p137, [UpdatedAt] = @p138, [UpdatedBy] = @p139, [UserAgent] = @p140, [UserId] = @p141
OUTPUT 1
WHERE [Id] = @p142;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p143, [IpAddress] = @p144, [IsActive] = @p145, [IsDeleted] = @p146, [IsRevoked] = @p147, [RevokedAt] = @p148, [RevokedReason] = @p149, [Token] = @p150, [UpdatedAt] = @p151, [UpdatedBy] = @p152, [UserAgent] = @p153, [UserId] = @p154
OUTPUT 1
WHERE [Id] = @p155;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p156, [IpAddress] = @p157, [IsActive] = @p158, [IsDeleted] = @p159, [IsRevoked] = @p160, [RevokedAt] = @p161, [RevokedReason] = @p162, [Token] = @p163, [UpdatedAt] = @p164, [UpdatedBy] = @p165, [UserAgent] = @p166, [UserId] = @p167
OUTPUT 1
WHERE [Id] = @p168;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p169, [IpAddress] = @p170, [IsActive] = @p171, [IsDeleted] = @p172, [IsRevoked] = @p173, [RevokedAt] = @p174, [RevokedReason] = @p175, [Token] = @p176, [UpdatedAt] = @p177, [UpdatedBy] = @p178, [UserAgent] = @p179, [UserId] = @p180
OUTPUT 1
WHERE [Id] = @p181;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p182, [IpAddress] = @p183, [IsActive] = @p184, [IsDeleted] = @p185, [IsRevoked] = @p186, [RevokedAt] = @p187, [RevokedReason] = @p188, [Token] = @p189, [UpdatedAt] = @p190, [UpdatedBy] = @p191, [UserAgent] = @p192, [UserId] = @p193
OUTPUT 1
WHERE [Id] = @p194;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p195, [IpAddress] = @p196, [IsActive] = @p197, [IsDeleted] = @p198, [IsRevoked] = @p199, [RevokedAt] = @p200, [RevokedReason] = @p201, [Token] = @p202, [UpdatedAt] = @p203, [UpdatedBy] = @p204, [UserAgent] = @p205, [UserId] = @p206
OUTPUT 1
WHERE [Id] = @p207;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p208, [IpAddress] = @p209, [IsActive] = @p210, [IsDeleted] = @p211, [IsRevoked] = @p212, [RevokedAt] = @p213, [RevokedReason] = @p214, [Token] = @p215, [UpdatedAt] = @p216, [UpdatedBy] = @p217, [UserAgent] = @p218, [UserId] = @p219
OUTPUT 1
WHERE [Id] = @p220;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p221, [IpAddress] = @p222, [IsActive] = @p223, [IsDeleted] = @p224, [IsRevoked] = @p225, [RevokedAt] = @p226, [RevokedReason] = @p227, [Token] = @p228, [UpdatedAt] = @p229, [UpdatedBy] = @p230, [UserAgent] = @p231, [UserId] = @p232
OUTPUT 1
WHERE [Id] = @p233;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p234, [IpAddress] = @p235, [IsActive] = @p236, [IsDeleted] = @p237, [IsRevoked] = @p238, [RevokedAt] = @p239, [RevokedReason] = @p240, [Token] = @p241, [UpdatedAt] = @p242, [UpdatedBy] = @p243, [UserAgent] = @p244, [UserId] = @p245
OUTPUT 1
WHERE [Id] = @p246;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p247, [IpAddress] = @p248, [IsActive] = @p249, [IsDeleted] = @p250, [IsRevoked] = @p251, [RevokedAt] = @p252, [RevokedReason] = @p253, [Token] = @p254, [UpdatedAt] = @p255, [UpdatedBy] = @p256, [UserAgent] = @p257, [UserId] = @p258
OUTPUT 1
WHERE [Id] = @p259;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p260, [IpAddress] = @p261, [IsActive] = @p262, [IsDeleted] = @p263, [IsRevoked] = @p264, [RevokedAt] = @p265, [RevokedReason] = @p266, [Token] = @p267, [UpdatedAt] = @p268, [UpdatedBy] = @p269, [UserAgent] = @p270, [UserId] = @p271
OUTPUT 1
WHERE [Id] = @p272;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p273, [IpAddress] = @p274, [IsActive] = @p275, [IsDeleted] = @p276, [IsRevoked] = @p277, [RevokedAt] = @p278, [RevokedReason] = @p279, [Token] = @p280, [UpdatedAt] = @p281, [UpdatedBy] = @p282, [UserAgent] = @p283, [UserId] = @p284
OUTPUT 1
WHERE [Id] = @p285;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p286, [IpAddress] = @p287, [IsActive] = @p288, [IsDeleted] = @p289, [IsRevoked] = @p290, [RevokedAt] = @p291, [RevokedReason] = @p292, [Token] = @p293, [UpdatedAt] = @p294, [UpdatedBy] = @p295, [UserAgent] = @p296, [UserId] = @p297
OUTPUT 1
WHERE [Id] = @p298;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p299, [IpAddress] = @p300, [IsActive] = @p301, [IsDeleted] = @p302, [IsRevoked] = @p303, [RevokedAt] = @p304, [RevokedReason] = @p305, [Token] = @p306, [UpdatedAt] = @p307, [UpdatedBy] = @p308, [UserAgent] = @p309, [UserId] = @p310
OUTPUT 1
WHERE [Id] = @p311;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p312, [IpAddress] = @p313, [IsActive] = @p314, [IsDeleted] = @p315, [IsRevoked] = @p316, [RevokedAt] = @p317, [RevokedReason] = @p318, [Token] = @p319, [UpdatedAt] = @p320, [UpdatedBy] = @p321, [UserAgent] = @p322, [UserId] = @p323
OUTPUT 1
WHERE [Id] = @p324;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p325, [IpAddress] = @p326, [IsActive] = @p327, [IsDeleted] = @p328, [IsRevoked] = @p329, [RevokedAt] = @p330, [RevokedReason] = @p331, [Token] = @p332, [UpdatedAt] = @p333, [UpdatedBy] = @p334, [UserAgent] = @p335, [UserId] = @p336
OUTPUT 1
WHERE [Id] = @p337;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p338, [IpAddress] = @p339, [IsActive] = @p340, [IsDeleted] = @p341, [IsRevoked] = @p342, [RevokedAt] = @p343, [RevokedReason] = @p344, [Token] = @p345, [UpdatedAt] = @p346, [UpdatedBy] = @p347, [UserAgent] = @p348, [UserId] = @p349
OUTPUT 1
WHERE [Id] = @p350;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p351, [IpAddress] = @p352, [IsActive] = @p353, [IsDeleted] = @p354, [IsRevoked] = @p355, [RevokedAt] = @p356, [RevokedReason] = @p357, [Token] = @p358, [UpdatedAt] = @p359, [UpdatedBy] = @p360, [UserAgent] = @p361, [UserId] = @p362
OUTPUT 1
WHERE [Id] = @p363;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p364, [IpAddress] = @p365, [IsActive] = @p366, [IsDeleted] = @p367, [IsRevoked] = @p368, [RevokedAt] = @p369, [RevokedReason] = @p370, [Token] = @p371, [UpdatedAt] = @p372, [UpdatedBy] = @p373, [UserAgent] = @p374, [UserId] = @p375
OUTPUT 1
WHERE [Id] = @p376;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p377, [IpAddress] = @p378, [IsActive] = @p379, [IsDeleted] = @p380, [IsRevoked] = @p381, [RevokedAt] = @p382, [RevokedReason] = @p383, [Token] = @p384, [UpdatedAt] = @p385, [UpdatedBy] = @p386, [UserAgent] = @p387, [UserId] = @p388
OUTPUT 1
WHERE [Id] = @p389;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p390, [IpAddress] = @p391, [IsActive] = @p392, [IsDeleted] = @p393, [IsRevoked] = @p394, [RevokedAt] = @p395, [RevokedReason] = @p396, [Token] = @p397, [UpdatedAt] = @p398, [UpdatedBy] = @p399, [UserAgent] = @p400, [UserId] = @p401
OUTPUT 1
WHERE [Id] = @p402;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p403, [IpAddress] = @p404, [IsActive] = @p405, [IsDeleted] = @p406, [IsRevoked] = @p407, [RevokedAt] = @p408, [RevokedReason] = @p409, [Token] = @p410, [UpdatedAt] = @p411, [UpdatedBy] = @p412, [UserAgent] = @p413, [UserId] = @p414
OUTPUT 1
WHERE [Id] = @p415;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p416, [IpAddress] = @p417, [IsActive] = @p418, [IsDeleted] = @p419, [IsRevoked] = @p420, [RevokedAt] = @p421, [RevokedReason] = @p422, [Token] = @p423, [UpdatedAt] = @p424, [UpdatedBy] = @p425, [UserAgent] = @p426, [UserId] = @p427
OUTPUT 1
WHERE [Id] = @p428;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p429, [IpAddress] = @p430, [IsActive] = @p431, [IsDeleted] = @p432, [IsRevoked] = @p433, [RevokedAt] = @p434, [RevokedReason] = @p435, [Token] = @p436, [UpdatedAt] = @p437, [UpdatedBy] = @p438, [UserAgent] = @p439, [UserId] = @p440
OUTPUT 1
WHERE [Id] = @p441;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p442, [IpAddress] = @p443, [IsActive] = @p444, [IsDeleted] = @p445, [IsRevoked] = @p446, [RevokedAt] = @p447, [RevokedReason] = @p448, [Token] = @p449, [UpdatedAt] = @p450, [UpdatedBy] = @p451, [UserAgent] = @p452, [UserId] = @p453
OUTPUT 1
WHERE [Id] = @p454;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p455, [IpAddress] = @p456, [IsActive] = @p457, [IsDeleted] = @p458, [IsRevoked] = @p459, [RevokedAt] = @p460, [RevokedReason] = @p461, [Token] = @p462, [UpdatedAt] = @p463, [UpdatedBy] = @p464, [UserAgent] = @p465, [UserId] = @p466
OUTPUT 1
WHERE [Id] = @p467;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p468, [IpAddress] = @p469, [IsActive] = @p470, [IsDeleted] = @p471, [IsRevoked] = @p472, [RevokedAt] = @p473, [RevokedReason] = @p474, [Token] = @p475, [UpdatedAt] = @p476, [UpdatedBy] = @p477, [UserAgent] = @p478, [UserId] = @p479
OUTPUT 1
WHERE [Id] = @p480;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p481, [IpAddress] = @p482, [IsActive] = @p483, [IsDeleted] = @p484, [IsRevoked] = @p485, [RevokedAt] = @p486, [RevokedReason] = @p487, [Token] = @p488, [UpdatedAt] = @p489, [UpdatedBy] = @p490, [UserAgent] = @p491, [UserId] = @p492
OUTPUT 1
WHERE [Id] = @p493;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p494, [IpAddress] = @p495, [IsActive] = @p496, [IsDeleted] = @p497, [IsRevoked] = @p498, [RevokedAt] = @p499, [RevokedReason] = @p500, [Token] = @p501, [UpdatedAt] = @p502, [UpdatedBy] = @p503, [UserAgent] = @p504, [UserId] = @p505
OUTPUT 1
WHERE [Id] = @p506;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p507, [IpAddress] = @p508, [IsActive] = @p509, [IsDeleted] = @p510, [IsRevoked] = @p511, [RevokedAt] = @p512, [RevokedReason] = @p513, [Token] = @p514, [UpdatedAt] = @p515, [UpdatedBy] = @p516, [UserAgent] = @p517, [UserId] = @p518
OUTPUT 1
WHERE [Id] = @p519;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p520, [IpAddress] = @p521, [IsActive] = @p522, [IsDeleted] = @p523, [IsRevoked] = @p524, [RevokedAt] = @p525, [RevokedReason] = @p526, [Token] = @p527, [UpdatedAt] = @p528, [UpdatedBy] = @p529, [UserAgent] = @p530, [UserId] = @p531
OUTPUT 1
WHERE [Id] = @p532;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p533, [IpAddress] = @p534, [IsActive] = @p535, [IsDeleted] = @p536, [IsRevoked] = @p537, [RevokedAt] = @p538, [RevokedReason] = @p539, [Token] = @p540, [UpdatedAt] = @p541, [UpdatedBy] = @p542, [UserAgent] = @p543, [UserId] = @p544
OUTPUT 1
WHERE [Id] = @p545;
2025-07-23 19:26:25.000 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[@p12='?' (DbType = Int32), @p0='?' (DbType = DateTime2), @p1='?' (Size = 4000), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (DbType = DateTime2), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000), @p11='?' (DbType = Int32), @p25='?' (DbType = Int32), @p13='?' (DbType = DateTime2), @p14='?' (Size = 4000), @p15='?' (DbType = Boolean), @p16='?' (DbType = Boolean), @p17='?' (DbType = Boolean), @p18='?' (DbType = DateTime2), @p19='?' (Size = 4000), @p20='?' (Size = 4000), @p21='?' (DbType = DateTime2), @p22='?' (DbType = Int32), @p23='?' (Size = 4000), @p24='?' (DbType = Int32), @p38='?' (DbType = Int32), @p26='?' (DbType = DateTime2), @p27='?' (Size = 4000), @p28='?' (DbType = Boolean), @p29='?' (DbType = Boolean), @p30='?' (DbType = Boolean), @p31='?' (DbType = DateTime2), @p32='?' (Size = 4000), @p33='?' (Size = 4000), @p34='?' (DbType = DateTime2), @p35='?' (DbType = Int32), @p36='?' (Size = 4000), @p37='?' (DbType = Int32), @p51='?' (DbType = Int32), @p39='?' (DbType = DateTime2), @p40='?' (Size = 4000), @p41='?' (DbType = Boolean), @p42='?' (DbType = Boolean), @p43='?' (DbType = Boolean), @p44='?' (DbType = DateTime2), @p45='?' (Size = 4000), @p46='?' (Size = 4000), @p47='?' (DbType = DateTime2), @p48='?' (DbType = Int32), @p49='?' (Size = 4000), @p50='?' (DbType = Int32), @p64='?' (DbType = Int32), @p52='?' (DbType = DateTime2), @p53='?' (Size = 4000), @p54='?' (DbType = Boolean), @p55='?' (DbType = Boolean), @p56='?' (DbType = Boolean), @p57='?' (DbType = DateTime2), @p58='?' (Size = 4000), @p59='?' (Size = 4000), @p60='?' (DbType = DateTime2), @p61='?' (DbType = Int32), @p62='?' (Size = 4000), @p63='?' (DbType = Int32), @p77='?' (DbType = Int32), @p65='?' (DbType = DateTime2), @p66='?' (Size = 4000), @p67='?' (DbType = Boolean), @p68='?' (DbType = Boolean), @p69='?' (DbType = Boolean), @p70='?' (DbType = DateTime2), @p71='?' (Size = 4000), @p72='?' (Size = 4000), @p73='?' (DbType = DateTime2), @p74='?' (DbType = Int32), @p75='?' (Size = 4000), @p76='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p0, [IpAddress] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [IsRevoked] = @p4, [RevokedAt] = @p5, [RevokedReason] = @p6, [Token] = @p7, [UpdatedAt] = @p8, [UpdatedBy] = @p9, [UserAgent] = @p10, [UserId] = @p11
OUTPUT 1
WHERE [Id] = @p12;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p13, [IpAddress] = @p14, [IsActive] = @p15, [IsDeleted] = @p16, [IsRevoked] = @p17, [RevokedAt] = @p18, [RevokedReason] = @p19, [Token] = @p20, [UpdatedAt] = @p21, [UpdatedBy] = @p22, [UserAgent] = @p23, [UserId] = @p24
OUTPUT 1
WHERE [Id] = @p25;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p26, [IpAddress] = @p27, [IsActive] = @p28, [IsDeleted] = @p29, [IsRevoked] = @p30, [RevokedAt] = @p31, [RevokedReason] = @p32, [Token] = @p33, [UpdatedAt] = @p34, [UpdatedBy] = @p35, [UserAgent] = @p36, [UserId] = @p37
OUTPUT 1
WHERE [Id] = @p38;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p39, [IpAddress] = @p40, [IsActive] = @p41, [IsDeleted] = @p42, [IsRevoked] = @p43, [RevokedAt] = @p44, [RevokedReason] = @p45, [Token] = @p46, [UpdatedAt] = @p47, [UpdatedBy] = @p48, [UserAgent] = @p49, [UserId] = @p50
OUTPUT 1
WHERE [Id] = @p51;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p52, [IpAddress] = @p53, [IsActive] = @p54, [IsDeleted] = @p55, [IsRevoked] = @p56, [RevokedAt] = @p57, [RevokedReason] = @p58, [Token] = @p59, [UpdatedAt] = @p60, [UpdatedBy] = @p61, [UserAgent] = @p62, [UserId] = @p63
OUTPUT 1
WHERE [Id] = @p64;
UPDATE [RefreshTokens] SET [ExpiryDate] = @p65, [IpAddress] = @p66, [IsActive] = @p67, [IsDeleted] = @p68, [IsRevoked] = @p69, [RevokedAt] = @p70, [RevokedReason] = @p71, [Token] = @p72, [UpdatedAt] = @p73, [UpdatedBy] = @p74, [UserAgent] = @p75, [UserId] = @p76
OUTPUT 1
WHERE [Id] = @p77;
2025-07-23 19:26:25.023 +03:00 [INF] All refresh tokens revoked for user 1
2025-07-23 19:26:25.024 +03:00 [INF] User logged out successfully: 1
2025-07-23 19:26:25.026 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-23 19:26:25.029 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api) in 11557.9295ms
2025-07-23 19:26:25.030 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Logout (DoorCompany.Api)'
2025-07-23 19:26:25.032 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 200 null application/json; charset=utf-8 11572.6771ms
2025-07-23 19:26:27.132 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/logout - null null
2025-07-23 19:26:27.137 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:27.138 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/logout - 204 null null 6.7028ms
2025-07-23 19:26:27.144 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:27.147 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:27.149 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:27.150 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:27.152 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.3411ms
2025-07-23 19:26:27.161 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:27.165 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:27.167 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:27.169 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:27.174 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 12.8871ms
2025-07-23 19:26:27.183 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:27.213 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:27.216 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:27.219 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:27.220 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 37.529ms
2025-07-23 19:26:28.017 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.021 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.022 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.023 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.024 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.0103ms
2025-07-23 19:26:28.033 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.036 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.037 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.039 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.043 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 10.4685ms
2025-07-23 19:26:28.051 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.054 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.057 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.058 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.059 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.1532ms
2025-07-23 19:26:28.202 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.206 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.207 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.209 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.210 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.4125ms
2025-07-23 19:26:28.218 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.222 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.223 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.224 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.227 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 9.2882ms
2025-07-23 19:26:28.235 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.238 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.239 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.242 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.243 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.162ms
2025-07-23 19:26:28.369 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.373 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.374 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.375 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.376 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.2986ms
2025-07-23 19:26:28.384 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.386 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.387 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.389 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.390 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.6268ms
2025-07-23 19:26:28.397 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.400 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.402 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.403 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.404 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.2244ms
2025-07-23 19:26:28.529 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.533 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.534 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.535 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.537 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.7337ms
2025-07-23 19:26:28.543 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.546 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.547 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.548 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.553 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 9.7635ms
2025-07-23 19:26:28.559 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.562 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.563 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.566 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.568 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.5036ms
2025-07-23 19:26:28.681 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.685 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.686 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.687 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.689 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.4385ms
2025-07-23 19:26:28.695 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.698 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.699 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.700 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.702 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.6033ms
2025-07-23 19:26:28.709 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.712 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.713 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.714 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.715 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.4381ms
2025-07-23 19:26:28.858 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.862 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.864 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.865 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.867 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.9302ms
2025-07-23 19:26:28.875 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.880 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.882 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.883 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.884 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.8516ms
2025-07-23 19:26:28.891 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:28.894 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:28.895 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:28.896 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:28.898 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.7592ms
2025-07-23 19:26:29.025 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.029 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.031 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.032 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.033 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.2767ms
2025-07-23 19:26:29.040 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.043 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.047 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.048 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.049 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 9.4906ms
2025-07-23 19:26:29.056 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.059 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.062 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.064 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.066 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 9.6289ms
2025-07-23 19:26:29.201 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.205 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.206 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.207 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.209 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.508ms
2025-07-23 19:26:29.217 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.220 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.222 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.223 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.224 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.6389ms
2025-07-23 19:26:29.232 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.236 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.238 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.240 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.241 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.4243ms
2025-07-23 19:26:29.353 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.357 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.358 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.359 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.360 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.2975ms
2025-07-23 19:26:29.367 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.372 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.373 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.374 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.375 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.5729ms
2025-07-23 19:26:29.382 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.387 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.388 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.389 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.390 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.4751ms
2025-07-23 19:26:29.514 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.517 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.519 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.520 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.521 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.4119ms
2025-07-23 19:26:29.527 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.530 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.531 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.533 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.534 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.5025ms
2025-07-23 19:26:29.542 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.545 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.546 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.547 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.549 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.4438ms
2025-07-23 19:26:29.665 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.669 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.670 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.671 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.672 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.0189ms
2025-07-23 19:26:29.678 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.681 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.682 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.684 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.685 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.4572ms
2025-07-23 19:26:29.692 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.696 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.697 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.698 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.700 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.8485ms
2025-07-23 19:26:29.801 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.805 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.806 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.807 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.809 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.45ms
2025-07-23 19:26:29.816 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.820 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.821 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.822 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.823 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.92ms
2025-07-23 19:26:29.830 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:29.835 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:29.836 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:29.837 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:29.838 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.1089ms
2025-07-23 19:26:35.937 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/logout - null null
2025-07-23 19:26:35.940 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:35.941 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/logout - 204 null null 4.1578ms
2025-07-23 19:26:35.945 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:35.948 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:35.949 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:35.950 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:35.951 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.1689ms
2025-07-23 19:26:35.957 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:35.963 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:35.965 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:35.966 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:35.967 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 10.0504ms
2025-07-23 19:26:35.974 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:35.979 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:35.980 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:35.981 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:35.983 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 9.3816ms
2025-07-23 19:26:42.860 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/auth/logout - null null
2025-07-23 19:26:42.865 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:42.867 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/auth/logout - 204 null null 6.7862ms
2025-07-23 19:26:42.872 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:42.876 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:42.877 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:42.878 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:42.880 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.6393ms
2025-07-23 19:26:42.889 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:42.893 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:42.894 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:42.895 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:42.897 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.2806ms
2025-07-23 19:26:42.906 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:42.909 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:42.911 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:42.911 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:42.913 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.2278ms
2025-07-23 19:26:43.042 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:43.045 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:43.046 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:43.048 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:43.052 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 10.9121ms
2025-07-23 19:26:43.059 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:43.062 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:43.063 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:43.067 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:43.071 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 11.7283ms
2025-07-23 19:26:43.079 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:43.082 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:43.083 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:43.084 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:43.088 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.842ms
2025-07-23 19:26:43.225 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:43.228 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:43.229 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:43.230 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:43.232 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 6.8182ms
2025-07-23 19:26:43.238 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:43.243 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:43.245 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:43.246 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:43.247 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 9.3646ms
2025-07-23 19:26:43.254 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:43.258 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:43.259 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:43.260 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:43.262 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.5752ms
2025-07-23 19:26:43.409 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:43.412 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:43.413 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:43.414 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:43.416 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 7.3017ms
2025-07-23 19:26:43.425 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:43.430 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:43.432 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:43.432 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:43.434 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 9.219ms
2025-07-23 19:26:43.440 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/auth/logout - application/json 2
2025-07-23 19:26:43.445 +03:00 [INF] CORS policy execution successful.
2025-07-23 19:26:43.447 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-23 19:26:43.448 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-23 19:26:43.449 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/auth/logout - 401 0 null 8.8638ms
