﻿using DoorCompany.Data.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace DoorCompany.Service.Services
{
    public class FileUploadService : IFileUploadService
    {
      
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<FileUploadService> _logger;
        private readonly string[] _allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
        private readonly List<string> _allowedMimeTypes;
        private readonly long _maxFileSize = 5 * 1024 * 1024; // 5MB
        private readonly string _uploadsPath;
        public FileUploadService(IWebHostEnvironment environment, ILogger<FileUploadService> logger)
        {
            _environment = environment;
            _logger = logger;
            _uploadsPath = Path.Combine(_environment.WebRootPath, "Uploads");
            _allowedMimeTypes = new List<string> { "image/jpeg", "image/png", "image/webp" };
            Directory.CreateDirectory(_uploadsPath);
            Directory.CreateDirectory(Path.Combine(_uploadsPath, "Users"));
            Directory.CreateDirectory(Path.Combine(_uploadsPath, "Partners"));
            Directory.CreateDirectory(Path.Combine(_uploadsPath, "Items"));
        }

        public async Task<string> UploadImageAsync(IFormFile file, string folder)
        {
            try
            {
                if (!IsValidImage(file))
                {
                    return "InvalidImageType";
                }
                //  var path = _environment.WebRootPath + "/images" + "/" + Location + "/";
                var uploadPath = Path.Combine(_uploadsPath, folder);


                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);

                }


                // Generate unique filename
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                var fileName = Guid.NewGuid().ToString().Replace("-", string.Empty) + fileExtension;
                var filePath = Path.Combine(uploadPath, fileName);
                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                    await stream.FlushAsync();
                }

                // Return relative path
                var relativePath =$"Uploads/{folder}/{fileName}".Replace("\\", "/");

                return relativePath;


            }
            catch (Exception)
            {

               return string.Empty;
            }

        } 
        public async Task<byte[]?> GetImageAsync(string imagePath)
        {
            try
            {
                if (string.IsNullOrEmpty(imagePath))
                    return null;

                var decodedPath = Uri.UnescapeDataString(imagePath);

                var fullPath = Path.Combine(_environment.WebRootPath, decodedPath.Replace("/", "\\"));
           
                if (File.Exists(fullPath))
                {
                    return await File.ReadAllBytesAsync(fullPath);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting image: {ImagePath}", imagePath);
                return null;
            }
        }
        public async Task<string?> GetFullImagePath(string relativePath)
        {
            if (string.IsNullOrWhiteSpace(relativePath))
                return null;

            var decodedPath = Uri.UnescapeDataString(relativePath);

            var fullPath = Path.Combine(_environment.WebRootPath, decodedPath.Replace("/", "\\"));

            if (File.Exists(fullPath))
            {
                return fullPath;
            }

            return null;
        }
        public async Task<string?> GetFullImagePathUrl(HttpRequest request, string relativePath)
        {
            if (string.IsNullOrWhiteSpace(relativePath))
                return null;

            var decodedPath = Uri.UnescapeDataString(relativePath);

            var fullPath = Path.Combine(_environment.WebRootPath, decodedPath.Replace("/", "\\"));

            if (!File.Exists(fullPath))
            {
                return null;
            }

            // تحويل المسار النسبي إلى URL
            var publicPath = decodedPath.Replace(Path.DirectorySeparatorChar, '/');
            var baseUrl = $"{request.Scheme}://{request.Host}";
            return $"{baseUrl}/{publicPath}";
        }

        public async Task<bool> DeleteImage(string imagePath)
        {
            try
            {
                if (string.IsNullOrEmpty(imagePath))
                    return true;

                var fullPath =await GetFullImagePath(imagePath);

                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                    _logger.LogInformation("Profile image deleted: {ImagePath}", imagePath);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting profile image: {ImagePath}", imagePath);
                return false;
            }
        }
        public bool IsValidImage(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return false;

            // Check file size
            if (file.Length > _maxFileSize)
                return false;

            // Check file extension
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!_allowedExtensions.Contains(extension))
                return false;

            // Check content type
            var allowedContentTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp" };
            if (!allowedContentTypes.Contains(file.ContentType.ToLowerInvariant()))
                return false;

            return true;
        }


    }
}
