﻿using DoorCompany.Service.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Interfaces
{
    public interface IMainActionRepository
    {
        Task<ApiResponse<List<BasicActionResponse>>> GetALLMainActionAsync();
        Task<ApiResponse<List<BasicActionResponse>>> GetMainActionByActionAsync(int actionParent);
        Task<ApiResponse<BasicActionResponse>> GetMainActionByIdAsync(int id);
      
    }
}
