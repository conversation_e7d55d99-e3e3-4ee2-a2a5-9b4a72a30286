import './polyfills.server.mjs';
import{a as G}from"./chunk-HY3M65TY.mjs";import{b as ft}from"./chunk-L4YQHDFK.mjs";import"./chunk-LSS2YBGE.mjs";import{b as X,c as Z,d as tt,g as et,j as nt,k as ot,l as it,m as ut,n as lt,o as st,p as ct,q as mt,r as pt,s as gt,t as dt}from"./chunk-JEHCA7E6.mjs";import{b as U,d as x,f as Y,g as H,j as V,l as $,m as J,p as K,q as Q,r as W,s as rt,t as at}from"./chunk-5SQR5QFE.mjs";import{da as N,ea as B,fa as q,ga as D}from"./chunk-UDSHM75V.mjs";import{$a as p,Ac as R,Ea as k,Ec as j,Fa as d,H as y,Hc as T,Ja as m,K as w,Kc as P,L as g,N as S,Pa as F,Qa as l,Za as o,_a as r,fb as M,gb as f,gc as I,kc as E,qb as a,rb as _,sb as v,va as u,xc as A,yc as z,za as c,zc as L}from"./chunk-JADE4TID.mjs";import"./chunk-S6KH3LOX.mjs";var h=class t{constructor(e){this.apiService=e}login(e){let n="auth/login",i=e;return this.apiService.post(n,i).pipe(y(s=>{s.succeeded&&s.data&&(console.log("Login successful, storing token and user data"),console.log("User role:",s.data.roles),console.log("Token:",s.data.accessToken.substring(0,20)+"..."))}))}static \u0275fac=function(n){return new(n||t)(S(q))};static \u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"})};function Ct(t,e){if(t&1&&(o(0,"div",32)(1,"mat-icon"),a(2,"error"),r(),o(3,"span"),a(4),r()()),t&2){let n=f();u(4),_(n.errorMessage)}}function bt(t,e){if(t&1&&(o(0,"mat-error"),a(1),r()),t&2){let n=f();u(),v(" ",n.getErrorMessage("username")," ")}}function Mt(t,e){if(t&1&&(o(0,"mat-error"),a(1),r()),t&2){let n=f();u(),v(" ",n.getErrorMessage("password")," ")}}function _t(t,e){t&1&&p(0,"mat-spinner",33)}function vt(t,e){t&1&&(o(0,"mat-icon",34),a(1,"login"),r())}function Pt(t,e){t&1&&(o(0,"span"),a(1,"\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"),r())}function Ot(t,e){t&1&&(o(0,"span"),a(1,"\u062C\u0627\u0631\u064A \u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644..."),r())}var C=class t{constructor(e,n,i,s,wt,St){this.formBuilder=e;this.authService=n;this.router=i;this.route=s;this.snackBar=wt;this.authtestService=St}loginForm;loading=!1;hidePassword=!0;returnUrl="/";errorMessage="";ngOnInit(){this.initializeForm(),this.returnUrl=this.route.snapshot.queryParams.returnUrl||"auth/login",this.authService.isLoggedIn&&this.router.navigate([this.returnUrl])}initializeForm(){this.loginForm=this.formBuilder.group({username:["",[x.required]],password:["",[x.required,x.minLength(6)]]})}get f(){return this.loginForm.controls}onSubmit(){if(this.loginForm.invalid){this.markFormGroupTouched();return}this.loading=!0,this.errorMessage="";let e=this.loginForm.value;this.authService.login(e).subscribe({next:n=>{n.statusCode==200&&n.data&&(this.snackBar.open("\u062A\u0645 \u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644 \u0628\u0646\u062C\u0627\u062D","\u0625\u063A\u0644\u0627\u0642",{duration:3e3,horizontalPosition:"center",verticalPosition:"top",panelClass:["success-snackbar"]}),this.router.navigate(["/"])),this.loading=!1},error:n=>{this.loading=!1}})}markFormGroupTouched(){Object.keys(this.loginForm.controls).forEach(e=>{this.loginForm.get(e)?.markAsTouched()})}showError(e){this.errorMessage=e,this.snackBar.open(e,"\u0625\u063A\u0644\u0627\u0642",{duration:5e3,horizontalPosition:"center",verticalPosition:"top",panelClass:["error-snackbar"]})}getErrorMessage(e){let n=this.loginForm.get(e);if(n?.hasError("required"))return`${this.getFieldDisplayName(e)} \u0645\u0637\u0644\u0648\u0628`;if(n?.hasError("minlength")){let i=n.errors?.minlength.requiredLength;return`${this.getFieldDisplayName(e)} \u064A\u062C\u0628 \u0623\u0646 \u064A\u0643\u0648\u0646 ${i} \u0623\u062D\u0631\u0641 \u0639\u0644\u0649 \u0627\u0644\u0623\u0642\u0644`}return""}getFieldDisplayName(e){return{username:"\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645",password:"\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631"}[e]||e}static \u0275fac=function(n){return new(n||t)(c(K),c(D),c(T),c(j),c(N),c(h))};static \u0275cmp=k({type:t,selectors:[["app-login"]],standalone:!1,decls:50,vars:13,consts:[[1,"login-container"],[1,"login-background"],[1,"background-pattern"],[1,"background-overlay"],[1,"login-content"],[1,"company-section"],[1,"company-logo"],[1,"logo-icon"],[1,"company-name"],[1,"company-subtitle"],[1,"login-card-container"],[1,"login-card"],[1,"login-header"],[1,"header-content"],[1,"login-icon"],[1,"header-text"],[1,"login-title"],[1,"login-subtitle"],[1,"login-form-content"],["class","error-message",4,"ngIf"],[1,"login-form",3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","username","placeholder","\u0623\u062F\u062E\u0644 \u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645","autocomplete","username"],["matSuffix",""],[4,"ngIf"],["matInput","","formControlName","password","placeholder","\u0623\u062F\u062E\u0644 \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631","autocomplete","current-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",1,"toggle-password-btn",3,"click"],["mat-raised-button","","color","primary","type","submit",1,"login-button","full-width",3,"disabled"],["diameter","20","class","login-spinner",4,"ngIf"],["class","button-icon",4,"ngIf"],[1,"login-footer"],[1,"footer-text"],[1,"error-message"],["diameter","20",1,"login-spinner"],[1,"button-icon"]],template:function(n,i){n&1&&(o(0,"div",0)(1,"div",1),p(2,"div",2)(3,"div",3),r(),o(4,"div",4)(5,"div",5)(6,"div",6)(7,"mat-icon",7),a(8,"business"),r()(),o(9,"h1",8),a(10,"\u0634\u0631\u0643\u0629 \u062C\u0646\u0631\u0627\u0644 \u0644\u0644\u0623\u062C\u0647\u0632\u0629 \u0627\u0644\u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A\u0629"),r(),o(11,"p",9),a(12,"\u0646\u0638\u0627\u0645 \u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0628\u064A\u0639\u0627\u062A"),r()(),o(13,"div",10)(14,"mat-card",11)(15,"mat-card-header",12)(16,"div",13)(17,"mat-icon",14),a(18,"login"),r(),o(19,"div",15)(20,"mat-card-title",16),a(21,"\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"),r(),o(22,"mat-card-subtitle",17),a(23,"\u0623\u062F\u062E\u0644 \u0628\u064A\u0627\u0646\u0627\u062A\u0643 \u0644\u0644\u0648\u0635\u0648\u0644 \u0625\u0644\u0649 \u0627\u0644\u0646\u0638\u0627\u0645"),r()()()(),o(24,"mat-card-content",18),m(25,Ct,5,1,"div",19),o(26,"form",20),M("ngSubmit",function(){return i.onSubmit()}),o(27,"mat-form-field",21)(28,"mat-label"),a(29,"\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645"),r(),p(30,"input",22),o(31,"mat-icon",23),a(32,"person"),r(),m(33,bt,2,1,"mat-error",24),r(),o(34,"mat-form-field",21)(35,"mat-label"),a(36,"\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631"),r(),p(37,"input",25),o(38,"button",26),M("click",function(){return i.hidePassword=!i.hidePassword}),o(39,"mat-icon"),a(40),r()(),m(41,Mt,2,1,"mat-error",24),r(),o(42,"button",27),m(43,_t,1,0,"mat-spinner",28)(44,vt,2,0,"mat-icon",29)(45,Pt,2,0,"span",24)(46,Ot,2,0,"span",24),r()()()()(),o(47,"div",30)(48,"p",31),a(49,"\xA9 2025 \u0634\u0631\u0643\u0629 \u062C\u0646\u0631\u0627\u0644 \u0644\u0644\u0623\u062C\u0647\u0632\u0629 \u0627\u0644\u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A\u0629. \u062C\u0645\u064A\u0639 \u0627\u0644\u062D\u0642\u0648\u0642 \u0645\u062D\u0641\u0648\u0638\u0629."),r()()()()),n&2&&(u(25),l("ngIf",i.errorMessage),u(),l("formGroup",i.loginForm),u(7),l("ngIf",i.f.username.touched&&i.f.username.invalid),u(4),l("type",i.hidePassword?"password":"text"),u(),F("aria-label","\u0625\u0638\u0647\u0627\u0631/\u0625\u062E\u0641\u0627\u0621 \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631")("aria-pressed",i.hidePassword),u(2),_(i.hidePassword?"visibility_off":"visibility"),u(),l("ngIf",i.f.password.touched&&i.f.password.invalid),u(),l("disabled",i.loginForm.invalid||i.loading),u(),l("ngIf",i.loading),u(),l("ngIf",!i.loading),u(),l("ngIf",!i.loading),u(),l("ngIf",i.loading))},dependencies:[I,rt,ut,st,mt,ct,lt,et,X,Z,tt,gt,V,U,Y,H,$,J,ot],styles:["body[_ngcontent-%COMP%]{direction:rtl}.login-container[_ngcontent-%COMP%]{min-height:100vh;position:relative;overflow:hidden;display:flex;align-items:center;justify-content:center}.login-background[_ngcontent-%COMP%]{position:absolute;inset:0;z-index:0;background:linear-gradient(135deg,#667eea,#764ba2,#667eea)}.background-pattern[_ngcontent-%COMP%]{position:absolute;inset:0;background-image:radial-gradient(circle at 25% 25%,rgba(255,255,255,.1) 0%,transparent 50%),radial-gradient(circle at 75% 75%,rgba(255,255,255,.1) 0%,transparent 50%);background-size:100px 100px;animation:_ngcontent-%COMP%_float 20s ease-in-out infinite}.background-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:#0000001a}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0)}50%{transform:translateY(-20px)}}.login-content[_ngcontent-%COMP%]{position:relative;z-index:1;width:100%;max-width:1200px;padding:20px;display:flex;flex-direction:column;align-items:center;gap:32px}.company-section[_ngcontent-%COMP%]{text-align:center;color:#fff;margin-bottom:20px}.company-logo[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;width:80px;height:80px;background:#fff3;border-radius:50%;margin-bottom:16px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:2px solid rgba(255,255,255,.3)}.logo-icon[_ngcontent-%COMP%]{font-size:40px;width:40px;height:40px;color:#fff}.company-name[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin:0 0 8px;text-shadow:2px 2px 4px rgba(0,0,0,.3)}.company-subtitle[_ngcontent-%COMP%]{font-size:1.25rem;margin:0;opacity:.9;font-weight:400}.login-card-container[_ngcontent-%COMP%]{width:100%;max-width:450px}.login-card[_ngcontent-%COMP%]{border-radius:20px;box-shadow:0 20px 60px #0000004d;background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,.2);overflow:hidden;transition:transform .3s ease,box-shadow .3s ease}.login-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 25px 70px #0006}.login-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3f51b5,#5c6bc0);color:#fff;padding:24px;margin:0}.header-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.login-icon[_ngcontent-%COMP%]{font-size:32px;width:32px;height:32px}.header-text[_ngcontent-%COMP%]{flex:1}.login-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;margin:0 0 4px;color:#fff}.login-subtitle[_ngcontent-%COMP%]{font-size:.95rem;margin:0;opacity:.9;color:#fff}.login-form-content[_ngcontent-%COMP%]{padding:32px 24px 24px}.error-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:rgba(#f44336,.1);color:#f44336;padding:12px 16px;border-radius:8px;margin-bottom:20px;border:1px solid rgba(#f44336,.2)}.login-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.toggle-password-btn[_ngcontent-%COMP%]{margin-left:8px;border:none}.login-button[_ngcontent-%COMP%]{height:56px;font-size:1.1rem;font-weight:600;border-radius:12px;position:relative;transition:all .3s ease}.login-spinner[_ngcontent-%COMP%]{margin-left:8px}.button-icon[_ngcontent-%COMP%]{margin-left:8px;font-size:20px;width:20px;height:20px}.button-icon[_ngcontent-%COMP%]:not(:disabled):hover{transform:translateY(-2px);box-shadow:0 8px 25px rgba(#3f51b5,.4)}.demo-credentials[_ngcontent-%COMP%]{width:100%;max-width:450px}.demo-card[_ngcontent-%COMP%]{background:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:16px;border:1px solid rgba(255,255,255,.3)}.demo-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;color:#2196f3}mat-icon[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px}h3[_ngcontent-%COMP%]{margin:0;font-size:1.1rem;font-weight:600}.credential-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:8px 0;color:#555}mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#3f51b5}span[_ngcontent-%COMP%]{font-size:.95rem}.login-footer[_ngcontent-%COMP%]{text-align:center;color:#fffc}.footer-text[_ngcontent-%COMP%]{margin:0;font-size:.875rem;font-weight:400}@media (max-width: 768px){.company-name[_ngcontent-%COMP%]{font-size:2rem}.company-subtitle[_ngcontent-%COMP%]{font-size:1.1rem}.login-content[_ngcontent-%COMP%]{gap:24px;padding:16px}.company-section[_ngcontent-%COMP%]{margin-bottom:16px}.login-header[_ngcontent-%COMP%]{padding:20px}.login-icon[_ngcontent-%COMP%]{font-size:28px;width:28px;height:28px}.login-title[_ngcontent-%COMP%]{font-size:1.3rem}.login-subtitle[_ngcontent-%COMP%]{font-size:.9rem}.login-form-content[_ngcontent-%COMP%]{padding:24px 20px 20px}}@media (max-width: 480px){.company-name[_ngcontent-%COMP%]{font-size:1.5rem}.company-subtitle[_ngcontent-%COMP%]{font-size:1rem}.login-content[_ngcontent-%COMP%]{gap:20px;padding:12px}.company-logo[_ngcontent-%COMP%]{width:60px;height:60px}.logo-icon[_ngcontent-%COMP%]{font-size:30px;width:30px;height:30px}.login-header[_ngcontent-%COMP%]{padding:16px}.header-content[_ngcontent-%COMP%]{flex-direction:column;text-align:center;gap:12px}.login-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.login-title[_ngcontent-%COMP%]{font-size:1.2rem}.login-subtitle[_ngcontent-%COMP%]{font-size:.85rem}.login-form-content[_ngcontent-%COMP%]{padding:20px 16px 16px}.login-button[_ngcontent-%COMP%]{height:52px;font-size:1rem}}[dir=rtl][_ngcontent-%COMP%]   .login-spinner[_ngcontent-%COMP%], .button-icon[_ngcontent-%COMP%]{margin-left:0;margin-right:8px}.error-message[_ngcontent-%COMP%], .demo-header[_ngcontent-%COMP%], .credential-item[_ngcontent-%COMP%]{text-align:right}h1[_ngcontent-%COMP%], h2[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h4[_ngcontent-%COMP%], h5[_ngcontent-%COMP%], h6[_ngcontent-%COMP%]{font-family:Cairo,Roboto,sans-serif;font-weight:600}p[_ngcontent-%COMP%], span[_ngcontent-%COMP%], div[_ngcontent-%COMP%]{font-family:Cairo,Roboto,sans-serif}  .mat-form-field{width:100%}.error-snackbar[_ngcontent-%COMP%]{background-color:#f44336!important;color:#fff!important}.success-snackbar[_ngcontent-%COMP%]{background-color:#4caf50!important;color:#fff!important}.mat-mdc-form-field[_ngcontent-%COMP%]{direction:rtl;text-align:right}.mat-mdc-form-field-subscript-wrapper[_ngcontent-%COMP%]{direction:rtl}.mat-mdc-form-field-error[_ngcontent-%COMP%]{text-align:right}.mat-mdc-input-element[_ngcontent-%COMP%], .mat-mdc-select[_ngcontent-%COMP%]{text-align:right;direction:rtl}"]})};var yt=[{path:"login",component:C}],b=class t{static \u0275fac=function(n){return new(n||t)};static \u0275mod=d({type:t});static \u0275inj=g({imports:[P.forChild(yt),P]})};var xt=class t{static \u0275fac=function(n){return new(n||t)};static \u0275mod=d({type:t});static \u0275inj=g({providers:[A(L(),z([G]))],imports:[E,b,at,pt,nt,ft,dt,W,it,Q,B,R]})};export{xt as AuthModule};
