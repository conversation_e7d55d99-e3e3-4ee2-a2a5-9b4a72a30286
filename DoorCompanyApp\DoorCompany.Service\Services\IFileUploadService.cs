﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Services
{
    public interface IFileUploadService
    {
        Task<string> UploadImageAsync(IFormFile file, string folder);
        Task<byte[]?> GetImageAsync(string imagePath);
        Task<string> GetFullImagePath(string relativePath);
        Task<string> GetFullImagePathUrl(HttpRequest request,string relativePath);
        Task<bool> DeleteImage(string imagePath);
        bool IsValidImage(IFormFile file);
    }
}
