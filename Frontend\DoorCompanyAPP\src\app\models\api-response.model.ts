export interface ApiResponse<T> {
  statusCode: number
  meta: string
  succeeded: boolean
  message: string
  errors: any[]
  data: T
}

export interface PagedResponse<T> {
  data: any[]
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export class PagedRequest {
  pageNumber: number =1;
  pageSize: number = 10;
  searchTerm?: string;
  isActive?: boolean;
}



