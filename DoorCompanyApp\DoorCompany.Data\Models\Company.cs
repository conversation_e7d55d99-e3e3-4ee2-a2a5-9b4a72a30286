﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Data.Models
{
    public class Company : BaseName
    {
        /// <summary>
        /// رقم السجل التجارى.
        /// </summary>
        public string? CommercialRegister { get; set; }
        
        /// <summary>
        /// رقم السجل الصناعي.
        /// </summary>
        public string? IndustrialRegister { get; set; }

        /// <summary>
        /// رقم السجل الضريبي.
        /// </summary>
        public string? TaxRegister { get; set; }

        /// <summary>
        /// تاريخ انشاء الشركة
        /// </summary>
        public DateTime? EstablishmentDate { get; set; }

         /// <summary>
        /// عدد الاسهم للشركة
       /// </summary>
        public int TotalShares { get; set; }
        public string? Code { get; set; }
        public string? Symbol { get; set; }
        public string? LogoPath { get; set; }
    }
}
