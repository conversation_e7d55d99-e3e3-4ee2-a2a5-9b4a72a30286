import{$ as R,$a as Ue,Aa as ze,Ab as Ze,Ba as A,C as _t,Ca as k,Cb as O,Da as w,Ea as X,Eb as Li,F as Kt,G as Ne,Ga as Ve,Gb as Bi,Hb as ne,I as Y,Ia as Qt,Ib as b,J as Oi,Ja as $,K as et,Kb as ji,Ma as H,N as m,Na as Jt,O as x,P as y,Pa as Fi,Q as B,R as r,Sb as ae,Ta as ot,U as gt,Ua as G,V as At,Va as Z,W as Ii,Wb as zi,X as Mi,Za as Ni,_ as it,a as f,ab as He,b as Ci,ba as M,bb as Mt,ca as h,cb as nt,d as V,e as Yt,ea as C,eb as te,ec as Vi,f as Si,fa as Le,fb as ee,g as p,ga as Ti,gb as ie,h as pt,ha as Be,ia as $t,ja as Gt,ka as U,kb as <PERSON>,l as ht,la as <PERSON>,m as Xt,ma as qt,mb as Ye,na as Ot,o as Rt,p as Ri,q as Di,sa as vt,sb as oe,t as Ai,ta as yt,u as ft,ua as j,v as Pe,va as je,w as bt,x as Zt,y as Dt,ya as It,yb as Xe,z as Fe}from"./chunk-KFKFGDWN.js";function Tt(o){return o.buttons===0||o.detail===0}function Pt(o){let i=o.touches&&o.touches[0]||o.changedTouches&&o.changedTouches[0];return!!i&&i.identifier===-1&&(i.radiusX==null||i.radiusX===1)&&(i.radiusY==null||i.radiusY===1)}var Ke;function Ui(){if(Ke==null){let o=typeof document<"u"?document.head:null;Ke=!!(o&&(o.createShadowRoot||o.attachShadow))}return Ke}function $e(o){if(Ui()){let i=o.getRootNode?o.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&i instanceof ShadowRoot)return i}return null}function Ge(){let o=typeof document<"u"&&document?document.activeElement:null;for(;o&&o.shadowRoot;){let i=o.shadowRoot.activeElement;if(i===o)break;o=i}return o}function T(o){return o.composedPath?o.composedPath()[0]:o.target}function P(o,i,t,e,n){let a=parseInt(Xe.major),s=parseInt(Xe.minor);return a>19||a===19&&s>0||a===0&&s===0?o.listen(i,t,e,n):(i.addEventListener(t,e,n),()=>{i.removeEventListener(t,e,n)})}var qe;try{qe=typeof Intl<"u"&&Intl.v8BreakIterator}catch{qe=!1}var g=(()=>{class o{_platformId=r(Gt);isBrowser=this._platformId?ae(this._platformId):typeof document=="object"&&!!document;EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent);TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent);BLINK=this.isBrowser&&!!(window.chrome||qe)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT;WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT;IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window);FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent);ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT;SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT;constructor(){}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var Ft;function Hi(){if(Ft==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>Ft=!0}))}finally{Ft=Ft||!1}return Ft}function wt(o){return Hi()?o:!!o.capture}function Wi(o,i=0){return Yi(o)?Number(o):arguments.length===2?i:0}function Yi(o){return!isNaN(parseFloat(o))&&!isNaN(Number(o))}function W(o){return o instanceof C?o.nativeElement:o}var Xi=new y("cdk-input-modality-detector-options"),Zi={ignoreKeys:[18,17,224,91,16]},Ki=650,Qe={passive:!0,capture:!0},$i=(()=>{class o{_platform=r(g);_listenerCleanups;modalityDetected;modalityChanged;get mostRecentModality(){return this._modality.value}_mostRecentTarget=null;_modality=new pt(null);_options;_lastTouchMs=0;_onKeydown=t=>{this._options?.ignoreKeys?.some(e=>e===t.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=T(t))};_onMousedown=t=>{Date.now()-this._lastTouchMs<Ki||(this._modality.next(Tt(t)?"keyboard":"mouse"),this._mostRecentTarget=T(t))};_onTouchstart=t=>{if(Pt(t)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=T(t)};constructor(){let t=r(h),e=r(b),n=r(Xi,{optional:!0});if(this._options=f(f({},Zi),n),this.modalityDetected=this._modality.pipe(Kt(1)),this.modalityChanged=this.modalityDetected.pipe(Fe()),this._platform.isBrowser){let a=r(j).createRenderer(null,null);this._listenerCleanups=t.runOutsideAngular(()=>[P(a,e,"keydown",this._onKeydown,Qe),P(a,e,"mousedown",this._onMousedown,Qe),P(a,e,"touchstart",this._onTouchstart,Qe)])}}ngOnDestroy(){this._modality.complete(),this._listenerCleanups?.forEach(t=>t())}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),Nt=function(o){return o[o.IMMEDIATE=0]="IMMEDIATE",o[o.EVENTUAL=1]="EVENTUAL",o}(Nt||{}),Gi=new y("cdk-focus-monitor-default-options"),re=wt({passive:!0,capture:!0}),se=(()=>{class o{_ngZone=r(h);_platform=r(g);_inputModalityDetector=r($i);_origin=null;_lastFocusOrigin;_windowFocused=!1;_windowFocusTimeoutId;_originTimeoutId;_originFromTouchInteraction=!1;_elementInfo=new Map;_monitoredElementCount=0;_rootNodeFocusListenerCount=new Map;_detectionMode;_windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=setTimeout(()=>this._windowFocused=!1)};_document=r(b,{optional:!0});_stopInputModalityDetector=new p;constructor(){let t=r(Gi,{optional:!0});this._detectionMode=t?.detectionMode||Nt.IMMEDIATE}_rootNodeFocusAndBlurListener=t=>{let e=T(t);for(let n=e;n;n=n.parentElement)t.type==="focus"?this._onFocus(t,n):this._onBlur(t,n)};monitor(t,e=!1){let n=W(t);if(!this._platform.isBrowser||n.nodeType!==1)return ht();let a=$e(n)||this._getDocument(),s=this._elementInfo.get(n);if(s)return e&&(s.checkChildren=!0),s.subject;let c={checkChildren:e,subject:new p,rootNode:a};return this._elementInfo.set(n,c),this._registerGlobalListeners(c),c.subject}stopMonitoring(t){let e=W(t),n=this._elementInfo.get(e);n&&(n.subject.complete(),this._setClasses(e),this._elementInfo.delete(e),this._removeGlobalListeners(n))}focusVia(t,e,n){let a=W(t),s=this._getDocument().activeElement;a===s?this._getClosestElementsInfo(a).forEach(([c,d])=>this._originChanged(c,e,d)):(this._setOrigin(e),typeof a.focus=="function"&&a.focus(n))}ngOnDestroy(){this._elementInfo.forEach((t,e)=>this.stopMonitoring(e))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(t){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(t)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:t&&this._isLastInteractionFromInputLabel(t)?"mouse":"program"}_shouldBeAttributedToTouch(t){return this._detectionMode===Nt.EVENTUAL||!!t?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(t,e){t.classList.toggle("cdk-focused",!!e),t.classList.toggle("cdk-touch-focused",e==="touch"),t.classList.toggle("cdk-keyboard-focused",e==="keyboard"),t.classList.toggle("cdk-mouse-focused",e==="mouse"),t.classList.toggle("cdk-program-focused",e==="program")}_setOrigin(t,e=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=t,this._originFromTouchInteraction=t==="touch"&&e,this._detectionMode===Nt.IMMEDIATE){clearTimeout(this._originTimeoutId);let n=this._originFromTouchInteraction?Ki:1;this._originTimeoutId=setTimeout(()=>this._origin=null,n)}})}_onFocus(t,e){let n=this._elementInfo.get(e),a=T(t);!n||!n.checkChildren&&e!==a||this._originChanged(e,this._getFocusOrigin(a),n)}_onBlur(t,e){let n=this._elementInfo.get(e);!n||n.checkChildren&&t.relatedTarget instanceof Node&&e.contains(t.relatedTarget)||(this._setClasses(e),this._emitOrigin(n,null))}_emitOrigin(t,e){t.subject.observers.length&&this._ngZone.run(()=>t.subject.next(e))}_registerGlobalListeners(t){if(!this._platform.isBrowser)return;let e=t.rootNode,n=this._rootNodeFocusListenerCount.get(e)||0;n||this._ngZone.runOutsideAngular(()=>{e.addEventListener("focus",this._rootNodeFocusAndBlurListener,re),e.addEventListener("blur",this._rootNodeFocusAndBlurListener,re)}),this._rootNodeFocusListenerCount.set(e,n+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(Y(this._stopInputModalityDetector)).subscribe(a=>{this._setOrigin(a,!0)}))}_removeGlobalListeners(t){let e=t.rootNode;if(this._rootNodeFocusListenerCount.has(e)){let n=this._rootNodeFocusListenerCount.get(e);n>1?this._rootNodeFocusListenerCount.set(e,n-1):(e.removeEventListener("focus",this._rootNodeFocusAndBlurListener,re),e.removeEventListener("blur",this._rootNodeFocusAndBlurListener,re),this._rootNodeFocusListenerCount.delete(e))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(t,e,n){this._setClasses(t,e),this._emitOrigin(n,e),this._lastFocusOrigin=e}_getClosestElementsInfo(t){let e=[];return this._elementInfo.forEach((n,a)=>{(a===t||n.checkChildren&&a.contains(t))&&e.push([a,n])}),e}_isLastInteractionFromInputLabel(t){let{_mostRecentTarget:e,mostRecentModality:n}=this._inputModalityDetector;if(n!=="mouse"||!e||e===t||t.nodeName!=="INPUT"&&t.nodeName!=="TEXTAREA"||t.disabled)return!1;let a=t.labels;if(a){for(let s=0;s<a.length;s++)if(a[s].contains(e))return!0}return!1}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),Wo=(()=>{class o{_elementRef=r(C);_focusMonitor=r(se);_monitorSubscription;_focusOrigin=null;cdkFocusChange=new M;constructor(){}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){let t=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(t,t.nodeType===1&&t.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(e=>{this._focusOrigin=e,this.cdkFocusChange.emit(e)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"]})}return o})();var ce=new WeakMap,F=(()=>{class o{_appRef;_injector=r(R);_environmentInjector=r(gt);load(t){let e=this._appRef=this._appRef||this._injector.get(Qt),n=ce.get(e);n||(n={loaders:new Set,refs:[]},ce.set(e,n),e.onDestroy(()=>{ce.get(e)?.refs.forEach(a=>a.destroy()),ce.delete(e)})),n.loaders.has(t)||(n.loaders.add(t),n.refs.push(ne(t,{environmentInjector:this._environmentInjector})))}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var le=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275cmp=A({type:o,selectors:[["ng-component"]],exportAs:["cdkVisuallyHidden"],decls:0,vars:0,template:function(e,n){},styles:[`.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}
`],encapsulation:2,changeDetection:0})}return o})();function xt(o){return Array.isArray(o)?o:[o]}var qi=new Set,at,Ji=(()=>{class o{_platform=r(g);_nonce=r(Pi,{optional:!0});_matchMedia;constructor(){this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):Xo}matchMedia(t){return(this._platform.WEBKIT||this._platform.BLINK)&&Yo(t,this._nonce),this._matchMedia(t)}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();function Yo(o,i){if(!qi.has(o))try{at||(at=document.createElement("style"),i&&at.setAttribute("nonce",i),at.setAttribute("type","text/css"),document.head.appendChild(at)),at.sheet&&(at.sheet.insertRule(`@media ${o} {body{ }}`,0),qi.add(o))}catch(t){console.error(t)}}function Xo(o){return{matches:o==="all"||o==="",media:o,addListener:()=>{},removeListener:()=>{}}}var Lt=(()=>{class o{_mediaMatcher=r(Ji);_zone=r(h);_queries=new Map;_destroySubject=new p;constructor(){}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(t){return Qi(xt(t)).some(n=>this._registerQuery(n).mql.matches)}observe(t){let n=Qi(xt(t)).map(s=>this._registerQuery(s).observable),a=Ri(n);return a=Di(a.pipe(Dt(1)),a.pipe(Kt(1),Zt(0))),a.pipe(Rt(s=>{let c={matches:!1,breakpoints:{}};return s.forEach(({matches:d,query:u})=>{c.matches=c.matches||d,c.breakpoints[u]=d}),c}))}_registerQuery(t){if(this._queries.has(t))return this._queries.get(t);let e=this._mediaMatcher.matchMedia(t),a={observable:new Yt(s=>{let c=d=>this._zone.run(()=>s.next(d));return e.addListener(c),()=>{e.removeListener(c)}}).pipe(Ne(e),Rt(({matches:s})=>({query:t,matches:s})),Y(this._destroySubject)),mql:e};return this._queries.set(t,a),a}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();function Qi(o){return o.map(i=>i.split(",")).reduce((i,t)=>i.concat(t)).map(i=>i.trim())}var Zo=(()=>{class o{create(t){return typeof MutationObserver>"u"?null:new MutationObserver(t)}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var to=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({providers:[Zo]})}return o})();var Ko=(()=>{class o{_platform=r(g);constructor(){}isDisabled(t){return t.hasAttribute("disabled")}isVisible(t){return Go(t)&&getComputedStyle(t).visibility==="visible"}isTabbable(t){if(!this._platform.isBrowser)return!1;let e=$o(an(t));if(e&&(eo(e)===-1||!this.isVisible(e)))return!1;let n=t.nodeName.toLowerCase(),a=eo(t);return t.hasAttribute("contenteditable")?a!==-1:n==="iframe"||n==="object"||this._platform.WEBKIT&&this._platform.IOS&&!on(t)?!1:n==="audio"?t.hasAttribute("controls")?a!==-1:!1:n==="video"?a===-1?!1:a!==null?!0:this._platform.FIREFOX||t.hasAttribute("controls"):t.tabIndex>=0}isFocusable(t,e){return nn(t)&&!this.isDisabled(t)&&(e?.ignoreVisibility||this.isVisible(t))}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();function $o(o){try{return o.frameElement}catch{return null}}function Go(o){return!!(o.offsetWidth||o.offsetHeight||typeof o.getClientRects=="function"&&o.getClientRects().length)}function qo(o){let i=o.nodeName.toLowerCase();return i==="input"||i==="select"||i==="button"||i==="textarea"}function Qo(o){return tn(o)&&o.type=="hidden"}function Jo(o){return en(o)&&o.hasAttribute("href")}function tn(o){return o.nodeName.toLowerCase()=="input"}function en(o){return o.nodeName.toLowerCase()=="a"}function no(o){if(!o.hasAttribute("tabindex")||o.tabIndex===void 0)return!1;let i=o.getAttribute("tabindex");return!!(i&&!isNaN(parseInt(i,10)))}function eo(o){if(!no(o))return null;let i=parseInt(o.getAttribute("tabindex")||"",10);return isNaN(i)?-1:i}function on(o){let i=o.nodeName.toLowerCase(),t=i==="input"&&o.type;return t==="text"||t==="password"||i==="select"||i==="textarea"}function nn(o){return Qo(o)?!1:qo(o)||Jo(o)||o.hasAttribute("contenteditable")||no(o)}function an(o){return o.ownerDocument&&o.ownerDocument.defaultView||window}var ti=class{_element;_checker;_ngZone;_document;_injector;_startAnchor;_endAnchor;_hasAttached=!1;startAnchorListener=()=>this.focusLastTabbableElement();endAnchorListener=()=>this.focusFirstTabbableElement();get enabled(){return this._enabled}set enabled(i){this._enabled=i,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(i,this._startAnchor),this._toggleAnchorTabIndex(i,this._endAnchor))}_enabled=!0;constructor(i,t,e,n,a=!1,s){this._element=i,this._checker=t,this._ngZone=e,this._document=n,this._injector=s,a||this.attachAnchors()}destroy(){let i=this._startAnchor,t=this._endAnchor;i&&(i.removeEventListener("focus",this.startAnchorListener),i.remove()),t&&(t.removeEventListener("focus",this.endAnchorListener),t.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return this._hasAttached?!0:(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(i){return new Promise(t=>{this._executeOnStable(()=>t(this.focusInitialElement(i)))})}focusFirstTabbableElementWhenReady(i){return new Promise(t=>{this._executeOnStable(()=>t(this.focusFirstTabbableElement(i)))})}focusLastTabbableElementWhenReady(i){return new Promise(t=>{this._executeOnStable(()=>t(this.focusLastTabbableElement(i)))})}_getRegionBoundary(i){let t=this._element.querySelectorAll(`[cdk-focus-region-${i}], [cdkFocusRegion${i}], [cdk-focus-${i}]`);return i=="start"?t.length?t[0]:this._getFirstTabbableElement(this._element):t.length?t[t.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(i){let t=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(t){if(!this._checker.isFocusable(t)){let e=this._getFirstTabbableElement(t);return e?.focus(i),!!e}return t.focus(i),!0}return this.focusFirstTabbableElement(i)}focusFirstTabbableElement(i){let t=this._getRegionBoundary("start");return t&&t.focus(i),!!t}focusLastTabbableElement(i){let t=this._getRegionBoundary("end");return t&&t.focus(i),!!t}hasAttached(){return this._hasAttached}_getFirstTabbableElement(i){if(this._checker.isFocusable(i)&&this._checker.isTabbable(i))return i;let t=i.children;for(let e=0;e<t.length;e++){let n=t[e].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(t[e]):null;if(n)return n}return null}_getLastTabbableElement(i){if(this._checker.isFocusable(i)&&this._checker.isTabbable(i))return i;let t=i.children;for(let e=t.length-1;e>=0;e--){let n=t[e].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(t[e]):null;if(n)return n}return null}_createAnchor(){let i=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,i),i.classList.add("cdk-visually-hidden"),i.classList.add("cdk-focus-trap-anchor"),i.setAttribute("aria-hidden","true"),i}_toggleAnchorTabIndex(i,t){i?t.setAttribute("tabindex","0"):t.removeAttribute("tabindex")}toggleAnchors(i){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(i,this._startAnchor),this._toggleAnchorTabIndex(i,this._endAnchor))}_executeOnStable(i){this._injector?Ot(i,{injector:this._injector}):setTimeout(i)}},ao=(()=>{class o{_checker=r(Ko);_ngZone=r(h);_document=r(b);_injector=r(R);constructor(){r(F).load(le)}create(t,e=!1){return new ti(t,this._checker,this._ngZone,this._document,e,this._injector)}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),rn=(()=>{class o{_elementRef=r(C);_focusTrapFactory=r(ao);focusTrap;_previouslyFocusedElement=null;get enabled(){return this.focusTrap?.enabled||!1}set enabled(t){this.focusTrap&&(this.focusTrap.enabled=t)}autoCapture;constructor(){r(g).isBrowser&&(this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0))}ngOnDestroy(){this.focusTrap?.destroy(),this._previouslyFocusedElement&&(this._previouslyFocusedElement.focus(),this._previouslyFocusedElement=null)}ngAfterContentInit(){this.focusTrap?.attachAnchors(),this.autoCapture&&this._captureFocus()}ngDoCheck(){this.focusTrap&&!this.focusTrap.hasAttached()&&this.focusTrap.attachAnchors()}ngOnChanges(t){let e=t.autoCapture;e&&!e.firstChange&&this.autoCapture&&this.focusTrap?.hasAttached()&&this._captureFocus()}_captureFocus(){this._previouslyFocusedElement=Ge(),this.focusTrap?.focusInitialElementWhenReady()}static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","cdkTrapFocus",""]],inputs:{enabled:[2,"cdkTrapFocus","enabled",O],autoCapture:[2,"cdkTrapFocusAutoCapture","autoCapture",O]},exportAs:["cdkTrapFocus"],features:[At]})}return o})(),ro=new y("liveAnnouncerElement",{providedIn:"root",factory:so});function so(){return null}var co=new y("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),sn=0,ei=(()=>{class o{_ngZone=r(h);_defaultOptions=r(co,{optional:!0});_liveElement;_document=r(b);_previousTimeout;_currentPromise;_currentResolve;constructor(){let t=r(ro,{optional:!0});this._liveElement=t||this._createLiveElement()}announce(t,...e){let n=this._defaultOptions,a,s;return e.length===1&&typeof e[0]=="number"?s=e[0]:[a,s]=e,this.clear(),clearTimeout(this._previousTimeout),a||(a=n&&n.politeness?n.politeness:"polite"),s==null&&n&&(s=n.duration),this._liveElement.setAttribute("aria-live",a),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(c=>this._currentResolve=c)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=t,typeof s=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),s)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let t="cdk-live-announcer-element",e=this._document.getElementsByClassName(t),n=this._document.createElement("div");for(let a=0;a<e.length;a++)e[a].remove();return n.classList.add(t),n.classList.add("cdk-visually-hidden"),n.setAttribute("aria-atomic","true"),n.setAttribute("aria-live","polite"),n.id=`cdk-live-announcer-${sn++}`,this._document.body.appendChild(n),n}_exposeAnnouncerToModals(t){let e=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let n=0;n<e.length;n++){let a=e[n],s=a.getAttribute("aria-owns");s?s.indexOf(t)===-1&&a.setAttribute("aria-owns",s+" "+t):a.setAttribute("aria-owns",t)}}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var q=function(o){return o[o.NONE=0]="NONE",o[o.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",o[o.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",o}(q||{}),io="cdk-high-contrast-black-on-white",oo="cdk-high-contrast-white-on-black",Je="cdk-high-contrast-active",de=(()=>{class o{_platform=r(g);_hasCheckedHighContrastMode;_document=r(b);_breakpointSubscription;constructor(){this._breakpointSubscription=r(Lt).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return q.NONE;let t=this._document.createElement("div");t.style.backgroundColor="rgb(1,2,3)",t.style.position="absolute",this._document.body.appendChild(t);let e=this._document.defaultView||window,n=e&&e.getComputedStyle?e.getComputedStyle(t):null,a=(n&&n.backgroundColor||"").replace(/ /g,"");switch(t.remove(),a){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return q.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return q.BLACK_ON_WHITE}return q.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let t=this._document.body.classList;t.remove(Je,io,oo),this._hasCheckedHighContrastMode=!0;let e=this.getHighContrastMode();e===q.BLACK_ON_WHITE?t.add(Je,io):e===q.WHITE_ON_BLACK&&t.add(Je,oo)}}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),cn=(()=>{class o{constructor(){r(de)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({imports:[to]})}return o})();var ii={},Bt=(()=>{class o{_appId=r($t);getId(t){return this._appId!=="ng"&&(t+=this._appId),ii.hasOwnProperty(t)||(ii[t]=0),`${t}${ii[t]++}`}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var ln=200,me=class{_letterKeyStream=new p;_items=[];_selectedItemIndex=-1;_pressedLetters=[];_skipPredicateFn;_selectedItem=new p;selectedItem=this._selectedItem;constructor(i,t){let e=typeof t?.debounceInterval=="number"?t.debounceInterval:ln;t?.skipPredicate&&(this._skipPredicateFn=t.skipPredicate),this.setItems(i),this._setupKeyHandler(e)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(i){this._selectedItemIndex=i}setItems(i){this._items=i}handleKey(i){let t=i.keyCode;i.key&&i.key.length===1?this._letterKeyStream.next(i.key.toLocaleUpperCase()):(t>=65&&t<=90||t>=48&&t<=57)&&this._letterKeyStream.next(String.fromCharCode(t))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(i){this._letterKeyStream.pipe(et(t=>this._pressedLetters.push(t)),Zt(i),ft(()=>this._pressedLetters.length>0),Rt(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(t=>{for(let e=1;e<this._items.length+1;e++){let n=(this._selectedItemIndex+e)%this._items.length,a=this._items[n];if(!this._skipPredicateFn?.(a)&&a.getLabel?.().toLocaleUpperCase().trim().indexOf(t)===0){this._selectedItem.next(a);break}}this._pressedLetters=[]})}};function ue(o,...i){return i.length?i.some(t=>o[t]):o.altKey||o.shiftKey||o.ctrlKey||o.metaKey}var pe=class{_items;_activeItemIndex=-1;_activeItem=Ti(null);_wrap=!1;_typeaheadSubscription=V.EMPTY;_itemChangesSubscription;_vertical=!0;_horizontal;_allowedModifierKeys=[];_homeAndEnd=!1;_pageUpAndDown={enabled:!1,delta:10};_effectRef;_typeahead;_skipPredicateFn=i=>i.disabled;constructor(i,t){this._items=i,i instanceof Be?this._itemChangesSubscription=i.changes.subscribe(e=>this._itemsChanged(e.toArray())):Le(i)&&(this._effectRef=Bi(()=>this._itemsChanged(i()),{injector:t}))}tabOut=new p;change=new p;skipPredicate(i){return this._skipPredicateFn=i,this}withWrap(i=!0){return this._wrap=i,this}withVerticalOrientation(i=!0){return this._vertical=i,this}withHorizontalOrientation(i){return this._horizontal=i,this}withAllowedModifierKeys(i){return this._allowedModifierKeys=i,this}withTypeAhead(i=200){this._typeaheadSubscription.unsubscribe();let t=this._getItemsArray();return this._typeahead=new me(t,{debounceInterval:typeof i=="number"?i:void 0,skipPredicate:e=>this._skipPredicateFn(e)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(e=>{this.setActiveItem(e)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(i=!0){return this._homeAndEnd=i,this}withPageUpDown(i=!0,t=10){return this._pageUpAndDown={enabled:i,delta:t},this}setActiveItem(i){let t=this._activeItem();this.updateActiveItem(i),this._activeItem()!==t&&this.change.next(this._activeItemIndex)}onKeydown(i){let t=i.keyCode,n=["altKey","ctrlKey","metaKey","shiftKey"].every(a=>!i[a]||this._allowedModifierKeys.indexOf(a)>-1);switch(t){case 9:this.tabOut.next();return;case 40:if(this._vertical&&n){this.setNextItemActive();break}else return;case 38:if(this._vertical&&n){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&n){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&n){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&n){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&n){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&n){let a=this._activeItemIndex-this._pageUpAndDown.delta;this._setActiveItemByIndex(a>0?a:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&n){let a=this._activeItemIndex+this._pageUpAndDown.delta,s=this._getItemsArray().length;this._setActiveItemByIndex(a<s?a:s-1,-1);break}else return;default:(n||ue(i,"shiftKey"))&&this._typeahead?.handleKey(i);return}this._typeahead?.reset(),i.preventDefault()}get activeItemIndex(){return this._activeItemIndex}get activeItem(){return this._activeItem()}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(i){let t=this._getItemsArray(),e=typeof i=="number"?i:t.indexOf(i),n=t[e];this._activeItem.set(n??null),this._activeItemIndex=e,this._typeahead?.setCurrentSelectedItemIndex(e)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(i){this._wrap?this._setActiveInWrapMode(i):this._setActiveInDefaultMode(i)}_setActiveInWrapMode(i){let t=this._getItemsArray();for(let e=1;e<=t.length;e++){let n=(this._activeItemIndex+i*e+t.length)%t.length,a=t[n];if(!this._skipPredicateFn(a)){this.setActiveItem(n);return}}}_setActiveInDefaultMode(i){this._setActiveItemByIndex(this._activeItemIndex+i,i)}_setActiveItemByIndex(i,t){let e=this._getItemsArray();if(e[i]){for(;this._skipPredicateFn(e[i]);)if(i+=t,!e[i])return;this.setActiveItem(i)}}_getItemsArray(){return Le(this._items)?this._items():this._items instanceof Be?this._items.toArray():this._items}_itemsChanged(i){this._typeahead?.setItems(i);let t=this._activeItem();if(t){let e=i.indexOf(t);e>-1&&e!==this._activeItemIndex&&(this._activeItemIndex=e,this._typeahead?.setCurrentSelectedItemIndex(e))}}};var oi=class extends pe{setActiveItem(i){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(i),this.activeItem&&this.activeItem.setActiveStyles()}};var mo=" ";function dn(o,i,t){let e=fe(o,i);t=t.trim(),!e.some(n=>n.trim()===t)&&(e.push(t),o.setAttribute(i,e.join(mo)))}function mn(o,i,t){let e=fe(o,i);t=t.trim();let n=e.filter(a=>a!==t);n.length?o.setAttribute(i,n.join(mo)):o.removeAttribute(i)}function fe(o,i){return o.getAttribute(i)?.match(/\S+/g)??[]}var uo="cdk-describedby-message",he="cdk-describedby-host",ai=0,jr=(()=>{class o{_platform=r(g);_document=r(b);_messageRegistry=new Map;_messagesContainer=null;_id=`${ai++}`;constructor(){r(F).load(le),this._id=r($t)+"-"+ai++}describe(t,e,n){if(!this._canBeDescribed(t,e))return;let a=ni(e,n);typeof e!="string"?(lo(e,this._id),this._messageRegistry.set(a,{messageElement:e,referenceCount:0})):this._messageRegistry.has(a)||this._createMessageElement(e,n),this._isElementDescribedByMessage(t,a)||this._addMessageReference(t,a)}removeDescription(t,e,n){if(!e||!this._isElementNode(t))return;let a=ni(e,n);if(this._isElementDescribedByMessage(t,a)&&this._removeMessageReference(t,a),typeof e=="string"){let s=this._messageRegistry.get(a);s&&s.referenceCount===0&&this._deleteMessageElement(a)}this._messagesContainer?.childNodes.length===0&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){let t=this._document.querySelectorAll(`[${he}="${this._id}"]`);for(let e=0;e<t.length;e++)this._removeCdkDescribedByReferenceIds(t[e]),t[e].removeAttribute(he);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(t,e){let n=this._document.createElement("div");lo(n,this._id),n.textContent=t,e&&n.setAttribute("role",e),this._createMessagesContainer(),this._messagesContainer.appendChild(n),this._messageRegistry.set(ni(t,e),{messageElement:n,referenceCount:0})}_deleteMessageElement(t){this._messageRegistry.get(t)?.messageElement?.remove(),this._messageRegistry.delete(t)}_createMessagesContainer(){if(this._messagesContainer)return;let t="cdk-describedby-message-container",e=this._document.querySelectorAll(`.${t}[platform="server"]`);for(let a=0;a<e.length;a++)e[a].remove();let n=this._document.createElement("div");n.style.visibility="hidden",n.classList.add(t),n.classList.add("cdk-visually-hidden"),this._platform.isBrowser||n.setAttribute("platform","server"),this._document.body.appendChild(n),this._messagesContainer=n}_removeCdkDescribedByReferenceIds(t){let e=fe(t,"aria-describedby").filter(n=>n.indexOf(uo)!=0);t.setAttribute("aria-describedby",e.join(" "))}_addMessageReference(t,e){let n=this._messageRegistry.get(e);dn(t,"aria-describedby",n.messageElement.id),t.setAttribute(he,this._id),n.referenceCount++}_removeMessageReference(t,e){let n=this._messageRegistry.get(e);n.referenceCount--,mn(t,"aria-describedby",n.messageElement.id),t.removeAttribute(he)}_isElementDescribedByMessage(t,e){let n=fe(t,"aria-describedby"),a=this._messageRegistry.get(e),s=a&&a.messageElement.id;return!!s&&n.indexOf(s)!=-1}_canBeDescribed(t,e){if(!this._isElementNode(t))return!1;if(e&&typeof e=="object")return!0;let n=e==null?"":`${e}`.trim(),a=t.getAttribute("aria-label");return n?!a||a.trim()!==n:!1}_isElementNode(t){return t.nodeType===this._document.ELEMENT_NODE}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();function ni(o,i){return typeof o=="string"?`${i||""}/${o}`:o}function lo(o,i){o.id||(o.id=`${uo}-${i}-${ai++}`)}var z=function(o){return o[o.NORMAL=0]="NORMAL",o[o.NEGATED=1]="NEGATED",o[o.INVERTED=2]="INVERTED",o}(z||{}),be,rt;function _e(){if(rt==null){if(typeof document!="object"||!document||typeof Element!="function"||!Element)return rt=!1,rt;if("scrollBehavior"in document.documentElement.style)rt=!0;else{let o=Element.prototype.scrollTo;o?rt=!/\{\s*\[native code\]\s*\}/.test(o.toString()):rt=!1}}return rt}function kt(){if(typeof document!="object"||!document)return z.NORMAL;if(be==null){let o=document.createElement("div"),i=o.style;o.dir="rtl",i.width="1px",i.overflow="auto",i.visibility="hidden",i.pointerEvents="none",i.position="absolute";let t=document.createElement("div"),e=t.style;e.width="2px",e.height="1px",o.appendChild(t),document.body.appendChild(o),be=z.NORMAL,o.scrollLeft===0&&(o.scrollLeft=1,be=o.scrollLeft===0?z.NEGATED:z.INVERTED),o.remove()}return be}function ri(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}var Et,po=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"];function Kr(){if(Et)return Et;if(typeof document!="object"||!document)return Et=new Set(po),Et;let o=document.createElement("input");return Et=new Set(po.filter(i=>(o.setAttribute("type",i),o.type===i))),Et}function un(o){return o!=null&&`${o}`!="false"}function E(o){return o==null?"":typeof o=="string"?o:`${o}px`}function is(o,i=/\s+/){let t=[];if(o!=null){let e=Array.isArray(o)?o:`${o}`.split(i);for(let n of e){let a=`${n}`.trim();a&&t.push(a)}}return t}var N=function(o){return o[o.FADING_IN=0]="FADING_IN",o[o.VISIBLE=1]="VISIBLE",o[o.FADING_OUT=2]="FADING_OUT",o[o.HIDDEN=3]="HIDDEN",o}(N||{}),si=class{_renderer;element;config;_animationForciblyDisabledThroughCss;state=N.HIDDEN;constructor(i,t,e,n=!1){this._renderer=i,this.element=t,this.config=e,this._animationForciblyDisabledThroughCss=n}fadeOut(){this._renderer.fadeOutRipple(this)}},ho=wt({passive:!0,capture:!0}),ci=class{_events=new Map;addHandler(i,t,e,n){let a=this._events.get(t);if(a){let s=a.get(e);s?s.add(n):a.set(e,new Set([n]))}else this._events.set(t,new Map([[e,new Set([n])]])),i.runOutsideAngular(()=>{document.addEventListener(t,this._delegateEventHandler,ho)})}removeHandler(i,t,e){let n=this._events.get(i);if(!n)return;let a=n.get(t);a&&(a.delete(e),a.size===0&&n.delete(t),n.size===0&&(this._events.delete(i),document.removeEventListener(i,this._delegateEventHandler,ho)))}_delegateEventHandler=i=>{let t=T(i);t&&this._events.get(i.type)?.forEach((e,n)=>{(n===t||n.contains(t))&&e.forEach(a=>a.handleEvent(i))})}},jt={enterDuration:225,exitDuration:150},pn=800,fo=wt({passive:!0,capture:!0}),bo=["mousedown","touchstart"],_o=["mouseup","mouseleave","touchend","touchcancel"],hn=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275cmp=A({type:o,selectors:[["ng-component"]],hostAttrs:["mat-ripple-style-loader",""],decls:0,vars:0,template:function(e,n){},styles:[`.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}
`],encapsulation:2,changeDetection:0})}return o})(),zt=class o{_target;_ngZone;_platform;_containerElement;_triggerElement;_isPointerDown=!1;_activeRipples=new Map;_mostRecentTransientRipple;_lastTouchStartEvent;_pointerUpEventsRegistered=!1;_containerRect;static _eventManager=new ci;constructor(i,t,e,n,a){this._target=i,this._ngZone=t,this._platform=n,n.isBrowser&&(this._containerElement=W(e)),a&&a.get(F).load(hn)}fadeInRipple(i,t,e={}){let n=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),a=f(f({},jt),e.animation);e.centered&&(i=n.left+n.width/2,t=n.top+n.height/2);let s=e.radius||fn(i,t,n),c=i-n.left,d=t-n.top,u=a.enterDuration,l=document.createElement("div");l.classList.add("mat-ripple-element"),l.style.left=`${c-s}px`,l.style.top=`${d-s}px`,l.style.height=`${s*2}px`,l.style.width=`${s*2}px`,e.color!=null&&(l.style.backgroundColor=e.color),l.style.transitionDuration=`${u}ms`,this._containerElement.appendChild(l);let v=window.getComputedStyle(l),L=v.transitionProperty,I=v.transitionDuration,D=L==="none"||I==="0s"||I==="0s, 0s"||n.width===0&&n.height===0,S=new si(this,l,e,D);l.style.transform="scale3d(1, 1, 1)",S.state=N.FADING_IN,e.persistent||(this._mostRecentTransientRipple=S);let tt=null;return!D&&(u||a.exitDuration)&&this._ngZone.runOutsideAngular(()=>{let ki=()=>{tt&&(tt.fallbackTimer=null),clearTimeout(Ei),this._finishRippleTransition(S)},Te=()=>this._destroyRipple(S),Ei=setTimeout(Te,u+100);l.addEventListener("transitionend",ki),l.addEventListener("transitioncancel",Te),tt={onTransitionEnd:ki,onTransitionCancel:Te,fallbackTimer:Ei}}),this._activeRipples.set(S,tt),(D||!u)&&this._finishRippleTransition(S),S}fadeOutRipple(i){if(i.state===N.FADING_OUT||i.state===N.HIDDEN)return;let t=i.element,e=f(f({},jt),i.config.animation);t.style.transitionDuration=`${e.exitDuration}ms`,t.style.opacity="0",i.state=N.FADING_OUT,(i._animationForciblyDisabledThroughCss||!e.exitDuration)&&this._finishRippleTransition(i)}fadeOutAll(){this._getActiveRipples().forEach(i=>i.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(i=>{i.config.persistent||i.fadeOut()})}setupTriggerEvents(i){let t=W(i);!this._platform.isBrowser||!t||t===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=t,bo.forEach(e=>{o._eventManager.addHandler(this._ngZone,e,t,this)}))}handleEvent(i){i.type==="mousedown"?this._onMousedown(i):i.type==="touchstart"?this._onTouchStart(i):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{_o.forEach(t=>{this._triggerElement.addEventListener(t,this,fo)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(i){i.state===N.FADING_IN?this._startFadeOutTransition(i):i.state===N.FADING_OUT&&this._destroyRipple(i)}_startFadeOutTransition(i){let t=i===this._mostRecentTransientRipple,{persistent:e}=i.config;i.state=N.VISIBLE,!e&&(!t||!this._isPointerDown)&&i.fadeOut()}_destroyRipple(i){let t=this._activeRipples.get(i)??null;this._activeRipples.delete(i),this._activeRipples.size||(this._containerRect=null),i===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),i.state=N.HIDDEN,t!==null&&(i.element.removeEventListener("transitionend",t.onTransitionEnd),i.element.removeEventListener("transitioncancel",t.onTransitionCancel),t.fallbackTimer!==null&&clearTimeout(t.fallbackTimer)),i.element.remove()}_onMousedown(i){let t=Tt(i),e=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+pn;!this._target.rippleDisabled&&!t&&!e&&(this._isPointerDown=!0,this.fadeInRipple(i.clientX,i.clientY,this._target.rippleConfig))}_onTouchStart(i){if(!this._target.rippleDisabled&&!Pt(i)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;let t=i.changedTouches;if(t)for(let e=0;e<t.length;e++)this.fadeInRipple(t[e].clientX,t[e].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(i=>{let t=i.state===N.VISIBLE||i.config.terminateOnPointerUp&&i.state===N.FADING_IN;!i.config.persistent&&t&&i.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){let i=this._triggerElement;i&&(bo.forEach(t=>o._eventManager.removeHandler(t,i,this)),this._pointerUpEventsRegistered&&(_o.forEach(t=>i.removeEventListener(t,this,fo)),this._pointerUpEventsRegistered=!1))}};function fn(o,i,t){let e=Math.max(Math.abs(o-t.left),Math.abs(o-t.right)),n=Math.max(Math.abs(i-t.top),Math.abs(i-t.bottom));return Math.sqrt(e*e+n*n)}var li=new y("mat-ripple-global-options"),bs=(()=>{class o{_elementRef=r(C);_animationMode=r(U,{optional:!0});color;unbounded;centered;radius=0;animation;get disabled(){return this._disabled}set disabled(t){t&&this.fadeOutAllNonPersistent(),this._disabled=t,this._setupTriggerEventsIfEnabled()}_disabled=!1;get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(t){this._trigger=t,this._setupTriggerEventsIfEnabled()}_trigger;_rippleRenderer;_globalOptions;_isInitialized=!1;constructor(){let t=r(h),e=r(g),n=r(li,{optional:!0}),a=r(R);this._globalOptions=n||{},this._rippleRenderer=new zt(this,t,this._elementRef,e,a)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:f(f(f({},this._globalOptions.animation),this._animationMode==="NoopAnimations"?{enterDuration:0,exitDuration:0}:{}),this.animation),terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(t,e=0,n){return typeof t=="number"?this._rippleRenderer.fadeInRipple(t,e,f(f({},this.rippleConfig),n)):this._rippleRenderer.fadeInRipple(0,0,f(f({},this.rippleConfig),t))}static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(e,n){e&2&&H("mat-ripple-unbounded",n.unbounded)},inputs:{color:[0,"matRippleColor","color"],unbounded:[0,"matRippleUnbounded","unbounded"],centered:[0,"matRippleCentered","centered"],radius:[0,"matRippleRadius","radius"],animation:[0,"matRippleAnimation","animation"],disabled:[0,"matRippleDisabled","disabled"],trigger:[0,"matRippleTrigger","trigger"]},exportAs:["matRipple"]})}return o})();var bn={capture:!0},_n=["focus","mousedown","mouseenter","touchstart"],di="mat-ripple-loader-uninitialized",mi="mat-ripple-loader-class-name",go="mat-ripple-loader-centered",ge="mat-ripple-loader-disabled",vo=(()=>{class o{_document=r(b);_animationMode=r(U,{optional:!0});_globalRippleOptions=r(li,{optional:!0});_platform=r(g);_ngZone=r(h);_injector=r(R);_eventCleanups;_hosts=new Map;constructor(){let t=r(j).createRenderer(null,null);this._eventCleanups=this._ngZone.runOutsideAngular(()=>_n.map(e=>P(t,this._document,e,this._onInteraction,bn)))}ngOnDestroy(){let t=this._hosts.keys();for(let e of t)this.destroyRipple(e);this._eventCleanups.forEach(e=>e())}configureRipple(t,e){t.setAttribute(di,this._globalRippleOptions?.namespace??""),(e.className||!t.hasAttribute(mi))&&t.setAttribute(mi,e.className||""),e.centered&&t.setAttribute(go,""),e.disabled&&t.setAttribute(ge,"")}setDisabled(t,e){let n=this._hosts.get(t);n?(n.target.rippleDisabled=e,!e&&!n.hasSetUpEvents&&(n.hasSetUpEvents=!0,n.renderer.setupTriggerEvents(t))):e?t.setAttribute(ge,""):t.removeAttribute(ge)}_onInteraction=t=>{let e=T(t);if(e instanceof HTMLElement){let n=e.closest(`[${di}="${this._globalRippleOptions?.namespace??""}"]`);n&&this._createRipple(n)}};_createRipple(t){if(!this._document||this._hosts.has(t))return;t.querySelector(".mat-ripple")?.remove();let e=this._document.createElement("span");e.classList.add("mat-ripple",t.getAttribute(mi)),t.append(e);let n=this._animationMode==="NoopAnimations",a=this._globalRippleOptions,s=n?0:a?.animation?.enterDuration??jt.enterDuration,c=n?0:a?.animation?.exitDuration??jt.exitDuration,d={rippleDisabled:n||a?.disabled||t.hasAttribute(ge),rippleConfig:{centered:t.hasAttribute(go),terminateOnPointerUp:a?.terminateOnPointerUp,animation:{enterDuration:s,exitDuration:c}}},u=new zt(d,this._ngZone,e,this._platform,this._injector),l=!d.rippleDisabled;l&&u.setupTriggerEvents(t),this._hosts.set(t,{target:d,renderer:u,hasSetUpEvents:l}),t.removeAttribute(di)}destroyRipple(t){let e=this._hosts.get(t);e&&(e.renderer._removeTriggerEvents(),this._hosts.delete(t))}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var yo=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275cmp=A({type:o,selectors:[["structural-styles"]],decls:0,vars:0,template:function(e,n){},styles:[`.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:""}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}
`],encapsulation:2,changeDetection:0})}return o})();var gn=["mat-icon-button",""],vn=["*"];var yn=new y("MAT_BUTTON_CONFIG");var wn=[{attribute:"mat-button",mdcClasses:["mdc-button","mat-mdc-button"]},{attribute:"mat-flat-button",mdcClasses:["mdc-button","mdc-button--unelevated","mat-mdc-unelevated-button"]},{attribute:"mat-raised-button",mdcClasses:["mdc-button","mdc-button--raised","mat-mdc-raised-button"]},{attribute:"mat-stroked-button",mdcClasses:["mdc-button","mdc-button--outlined","mat-mdc-outlined-button"]},{attribute:"mat-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mat-mdc-fab"]},{attribute:"mat-mini-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mdc-fab--mini","mat-mdc-mini-fab"]},{attribute:"mat-icon-button",mdcClasses:["mdc-icon-button","mat-mdc-icon-button"]}],ui=(()=>{class o{_elementRef=r(C);_ngZone=r(h);_animationMode=r(U,{optional:!0});_focusMonitor=r(se);_rippleLoader=r(vo);_isFab=!1;color;get disableRipple(){return this._disableRipple}set disableRipple(t){this._disableRipple=t,this._updateRippleDisabled()}_disableRipple=!1;get disabled(){return this._disabled}set disabled(t){this._disabled=t,this._updateRippleDisabled()}_disabled=!1;ariaDisabled;disabledInteractive;constructor(){r(F).load(yo);let t=r(yn,{optional:!0}),e=this._elementRef.nativeElement,n=e.classList;this.disabledInteractive=t?.disabledInteractive??!1,this.color=t?.color??null,this._rippleLoader?.configureRipple(e,{className:"mat-mdc-button-ripple"});for(let{attribute:a,mdcClasses:s}of wn)e.hasAttribute(a)&&n.add(...s)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0)}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(t="program",e){t?this._focusMonitor.focusVia(this._elementRef.nativeElement,t,e):this._elementRef.nativeElement.focus(e)}_getAriaDisabled(){return this.ariaDisabled!=null?this.ariaDisabled:this.disabled&&this.disabledInteractive?!0:null}_getDisabledAttribute(){return this.disabledInteractive||!this.disabled?null:!0}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,inputs:{color:"color",disableRipple:[2,"disableRipple","disableRipple",O],disabled:[2,"disabled","disabled",O],ariaDisabled:[2,"aria-disabled","ariaDisabled",O],disabledInteractive:[2,"disabledInteractive","disabledInteractive",O]}})}return o})();var xn=(()=>{class o extends ui{constructor(){super(),this._rippleLoader.configureRipple(this._elementRef.nativeElement,{centered:!0})}static \u0275fac=function(e){return new(e||o)};static \u0275cmp=A({type:o,selectors:[["button","mat-icon-button",""]],hostVars:14,hostBindings:function(e,n){e&2&&($("disabled",n._getDisabledAttribute())("aria-disabled",n._getAriaDisabled()),Jt(n.color?"mat-"+n.color:""),H("mat-mdc-button-disabled",n.disabled)("mat-mdc-button-disabled-interactive",n.disabledInteractive)("_mat-animation-noopable",n._animationMode==="NoopAnimations")("mat-unthemed",!n.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[X],attrs:gn,ngContentSelectors:vn,decls:4,vars:0,consts:[[1,"mat-mdc-button-persistent-ripple","mdc-icon-button__ripple"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(e,n){e&1&&(Mt(),Z(0,"span",0),nt(1),Z(2,"span",1)(3,"span",2))},styles:[`.mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:"";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}
`,`@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}
`],encapsulation:2,changeDetection:0})}return o})();var kn=new y("cdk-dir-doc",{providedIn:"root",factory:En});function En(){return r(b)}var Cn=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function pi(o){let i=o?.toLowerCase()||"";return i==="auto"&&typeof navigator<"u"&&navigator?.language?Cn.test(navigator.language)?"rtl":"ltr":i==="rtl"?"rtl":"ltr"}var st=(()=>{class o{value="ltr";change=new M;constructor(){let t=r(kn,{optional:!0});if(t){let e=t.body?t.body.dir:null,n=t.documentElement?t.documentElement.dir:null;this.value=pi(e||n||"ltr")}}ngOnDestroy(){this.change.complete()}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var Ks=(()=>{class o{_dir="ltr";_isInitialized=!1;_rawDir;change=new M;get dir(){return this._dir}set dir(t){let e=this._dir;this._dir=pi(t),this._rawDir=t,e!==this._dir&&this._isInitialized&&this.change.emit(this._dir)}get value(){return this.dir}ngAfterContentInit(){this._isInitialized=!0}ngOnDestroy(){this.change.complete()}static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","dir",""]],hostVars:1,hostBindings:function(e,n){e&2&&$("dir",n._rawDir)},inputs:{dir:"dir"},outputs:{change:"dirChange"},exportAs:["dir"],features:[oe([{provide:st,useExisting:o}])]})}return o})(),Q=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({})}return o})();var K=(()=>{class o{constructor(){r(de)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({imports:[Q,Q]})}return o})();var wo=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({imports:[K,K]})}return o})();var Sn=["mat-button",""],Rn=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],Dn=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"];var xo=(()=>{class o extends ui{static \u0275fac=(()=>{let t;return function(n){return(t||(t=it(o)))(n||o)}})();static \u0275cmp=A({type:o,selectors:[["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""]],hostVars:14,hostBindings:function(e,n){e&2&&($("disabled",n._getDisabledAttribute())("aria-disabled",n._getAriaDisabled()),Jt(n.color?"mat-"+n.color:""),H("mat-mdc-button-disabled",n.disabled)("mat-mdc-button-disabled-interactive",n.disabledInteractive)("_mat-animation-noopable",n._animationMode==="NoopAnimations")("mat-unthemed",!n.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[X],attrs:Sn,ngContentSelectors:Dn,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(e,n){e&1&&(Mt(Rn),Z(0,"span",0),nt(1),ot(2,"span",1),nt(3,1),G(),nt(4,2),Z(5,"span",2)(6,"span",3)),e&2&&H("mdc-button__ripple",!n._isFab)("mdc-fab__ripple",n._isFab)},styles:[`.mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 12px);height:var(--mdc-text-button-container-height, 40px);font-family:var(--mdc-text-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-sys-label-large-weight))}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-sys-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 16px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display, block)}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-filled-button-container-height, 40px);font-family:var(--mdc-filled-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-filled-button-horizontal-padding, 24px)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-sys-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-unelevated-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display, block)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-sys-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-sys-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-filled-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-sys-level1));height:var(--mdc-protected-button-container-height, 40px);font-family:var(--mdc-protected-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-protected-button-horizontal-padding, 24px)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-raised-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-raised-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display, block)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-sys-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-sys-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-sys-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-protected-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-sys-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-outlined-button-container-height, 40px);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-sys-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-sys-corner-full));border-width:var(--mdc-outlined-button-outline-width, 1px);padding:0 var(--mat-outlined-button-horizontal-padding, 24px)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-outlined-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-outlined-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display, block)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-sys-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-sys-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:var(--mdc-outlined-button-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-focus-indicator,.mat-mdc-unelevated-button .mat-focus-indicator,.mat-mdc-raised-button .mat-focus-indicator,.mat-mdc-outlined-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-button:focus>.mat-focus-indicator::before,.mat-mdc-unelevated-button:focus>.mat-focus-indicator::before,.mat-mdc-raised-button:focus>.mat-focus-indicator::before,.mat-mdc-outlined-button:focus>.mat-focus-indicator::before{content:"";border-radius:inherit}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-focus-indicator::before,.mat-mdc-raised-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}
`,`@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}
`],encapsulation:2,changeDetection:0})}return o})();var ko=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({imports:[K,wo,K]})}return o})();var Ct=class{_attachedHost;attach(i){return this._attachedHost=i,i.attach(this)}detach(){let i=this._attachedHost;i!=null&&(this._attachedHost=null,i.detach())}get isAttached(){return this._attachedHost!=null}setAttachedHost(i){this._attachedHost=i}},ct=class extends Ct{component;viewContainerRef;injector;componentFactoryResolver;projectableNodes;constructor(i,t,e,n,a){super(),this.component=i,this.viewContainerRef=t,this.injector=e,this.projectableNodes=a}},J=class extends Ct{templateRef;viewContainerRef;context;injector;constructor(i,t,e,n){super(),this.templateRef=i,this.viewContainerRef=t,this.context=e,this.injector=n}get origin(){return this.templateRef.elementRef}attach(i,t=this.context){return this.context=t,super.attach(i)}detach(){return this.context=void 0,super.detach()}},ve=class extends Ct{element;constructor(i){super(),this.element=i instanceof C?i.nativeElement:i}},lt=class{_attachedPortal;_disposeFn;_isDisposed=!1;hasAttached(){return!!this._attachedPortal}attach(i){if(i instanceof ct)return this._attachedPortal=i,this.attachComponentPortal(i);if(i instanceof J)return this._attachedPortal=i,this.attachTemplatePortal(i);if(this.attachDomPortal&&i instanceof ve)return this._attachedPortal=i,this.attachDomPortal(i)}attachDomPortal=null;detach(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()}dispose(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0}setDisposeFn(i){this._disposeFn=i}_invokeDisposeFn(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)}};var Vt=class extends lt{outletElement;_appRef;_defaultInjector;_document;constructor(i,t,e,n,a){super(),this.outletElement=i,this._appRef=e,this._defaultInjector=n,this._document=a}attachComponentPortal(i){let t;if(i.viewContainerRef){let e=i.injector||i.viewContainerRef.injector,n=e.get(ze,null,{optional:!0})||void 0;t=i.viewContainerRef.createComponent(i.component,{index:i.viewContainerRef.length,injector:e,ngModuleRef:n,projectableNodes:i.projectableNodes||void 0}),this.setDisposeFn(()=>t.destroy())}else{let e=this._appRef,n=i.injector||this._defaultInjector||R.NULL,a=n.get(gt,e.injector);t=ne(i.component,{elementInjector:n,environmentInjector:a,projectableNodes:i.projectableNodes||void 0}),e.attachView(t.hostView),this.setDisposeFn(()=>{e.viewCount>0&&e.detachView(t.hostView),t.destroy()})}return this.outletElement.appendChild(this._getComponentRootNode(t)),this._attachedPortal=i,t}attachTemplatePortal(i){let t=i.viewContainerRef,e=t.createEmbeddedView(i.templateRef,i.context,{injector:i.injector});return e.rootNodes.forEach(n=>this.outletElement.appendChild(n)),e.detectChanges(),this.setDisposeFn(()=>{let n=t.indexOf(e);n!==-1&&t.remove(n)}),this._attachedPortal=i,e}attachDomPortal=i=>{let t=i.element;t.parentNode;let e=this._document.createComment("dom-portal");t.parentNode.insertBefore(e,t),this.outletElement.appendChild(t),this._attachedPortal=i,super.setDisposeFn(()=>{e.parentNode&&e.parentNode.replaceChild(t,e)})};dispose(){super.dispose(),this.outletElement.remove()}_getComponentRootNode(i){return i.hostView.rootNodes[0]}};var ye=(()=>{class o extends lt{_moduleRef=r(ze,{optional:!0});_document=r(b);_viewContainerRef=r(It);_isInitialized=!1;_attachedRef;constructor(){super()}get portal(){return this._attachedPortal}set portal(t){this.hasAttached()&&!t&&!this._isInitialized||(this.hasAttached()&&super.detach(),t&&super.attach(t),this._attachedPortal=t||null)}attached=new M;get attachedRef(){return this._attachedRef}ngOnInit(){this._isInitialized=!0}ngOnDestroy(){super.dispose(),this._attachedRef=this._attachedPortal=null}attachComponentPortal(t){t.setAttachedHost(this);let e=t.viewContainerRef!=null?t.viewContainerRef:this._viewContainerRef,n=e.createComponent(t.component,{index:e.length,injector:t.injector||e.injector,projectableNodes:t.projectableNodes||void 0,ngModuleRef:this._moduleRef||void 0});return e!==this._viewContainerRef&&this._getRootNode().appendChild(n.hostView.rootNodes[0]),super.setDisposeFn(()=>n.destroy()),this._attachedPortal=t,this._attachedRef=n,this.attached.emit(n),n}attachTemplatePortal(t){t.setAttachedHost(this);let e=this._viewContainerRef.createEmbeddedView(t.templateRef,t.context,{injector:t.injector});return super.setDisposeFn(()=>this._viewContainerRef.clear()),this._attachedPortal=t,this._attachedRef=e,this.attached.emit(e),e}attachDomPortal=t=>{let e=t.element;e.parentNode;let n=this._document.createComment("dom-portal");t.setAttachedHost(this),e.parentNode.insertBefore(n,e),this._getRootNode().appendChild(e),this._attachedPortal=t,super.setDisposeFn(()=>{n.parentNode&&n.parentNode.replaceChild(e,n)})};_getRootNode(){let t=this._viewContainerRef.element.nativeElement;return t.nodeType===t.ELEMENT_NODE?t:t.parentNode}static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","cdkPortalOutlet",""]],inputs:{portal:[0,"cdkPortalOutlet","portal"]},outputs:{attached:"attached"},exportAs:["cdkPortalOutlet"],features:[X]})}return o})();var Ut=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({})}return o})();var Eo={XSmall:"(max-width: 599.98px)",Small:"(min-width: 600px) and (max-width: 959.98px)",Medium:"(min-width: 960px) and (max-width: 1279.98px)",Large:"(min-width: 1280px) and (max-width: 1919.98px)",XLarge:"(min-width: 1920px)",Handset:"(max-width: 599.98px) and (orientation: portrait), (max-width: 959.98px) and (orientation: landscape)",Tablet:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), (min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",Web:"(min-width: 840px) and (orientation: portrait), (min-width: 1280px) and (orientation: landscape)",HandsetPortrait:"(max-width: 599.98px) and (orientation: portrait)",TabletPortrait:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)",WebPortrait:"(min-width: 840px) and (orientation: portrait)",HandsetLandscape:"(max-width: 959.98px) and (orientation: landscape)",TabletLandscape:"(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",WebLandscape:"(min-width: 1280px) and (orientation: landscape)"};var Co=class{};function Dc(o){return o&&typeof o.connect=="function"&&!(o instanceof Si)}var An=function(o){return o[o.REPLACED=0]="REPLACED",o[o.INSERTED=1]="INSERTED",o[o.MOVED=2]="MOVED",o[o.REMOVED=3]="REMOVED",o}(An||{}),Ic=new y("_ViewRepeater");var On=20,we=(()=>{class o{_ngZone=r(h);_platform=r(g);_renderer=r(j).createRenderer(null,null);_cleanupGlobalListener;constructor(){}_scrolled=new p;_scrolledCount=0;scrollContainers=new Map;register(t){this.scrollContainers.has(t)||this.scrollContainers.set(t,t.elementScrolled().subscribe(()=>this._scrolled.next(t)))}deregister(t){let e=this.scrollContainers.get(t);e&&(e.unsubscribe(),this.scrollContainers.delete(t))}scrolled(t=On){return this._platform.isBrowser?new Yt(e=>{this._cleanupGlobalListener||(this._cleanupGlobalListener=this._ngZone.runOutsideAngular(()=>this._renderer.listen("document","scroll",()=>this._scrolled.next())));let n=t>0?this._scrolled.pipe(Pe(t)).subscribe(e):this._scrolled.subscribe(e);return this._scrolledCount++,()=>{n.unsubscribe(),this._scrolledCount--,this._scrolledCount||(this._cleanupGlobalListener?.(),this._cleanupGlobalListener=void 0)}}):ht()}ngOnDestroy(){this._cleanupGlobalListener?.(),this._cleanupGlobalListener=void 0,this.scrollContainers.forEach((t,e)=>this.deregister(e)),this._scrolled.complete()}ancestorScrolled(t,e){let n=this.getAncestorScrollContainers(t);return this.scrolled(e).pipe(ft(a=>!a||n.indexOf(a)>-1))}getAncestorScrollContainers(t){let e=[];return this.scrollContainers.forEach((n,a)=>{this._scrollableContainsElement(a,t)&&e.push(a)}),e}_scrollableContainsElement(t,e){let n=W(e),a=t.getElementRef().nativeElement;do if(n==a)return!0;while(n=n.parentElement);return!1}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),In=(()=>{class o{elementRef=r(C);scrollDispatcher=r(we);ngZone=r(h);dir=r(st,{optional:!0});_scrollElement=this.elementRef.nativeElement;_destroyed=new p;_renderer=r(je);_cleanupScroll;_elementScrolled=new p;constructor(){}ngOnInit(){this._cleanupScroll=this.ngZone.runOutsideAngular(()=>this._renderer.listen(this._scrollElement,"scroll",t=>this._elementScrolled.next(t))),this.scrollDispatcher.register(this)}ngOnDestroy(){this._cleanupScroll?.(),this._elementScrolled.complete(),this.scrollDispatcher.deregister(this),this._destroyed.next(),this._destroyed.complete()}elementScrolled(){return this._elementScrolled}getElementRef(){return this.elementRef}scrollTo(t){let e=this.elementRef.nativeElement,n=this.dir&&this.dir.value=="rtl";t.left==null&&(t.left=n?t.end:t.start),t.right==null&&(t.right=n?t.start:t.end),t.bottom!=null&&(t.top=e.scrollHeight-e.clientHeight-t.bottom),n&&kt()!=z.NORMAL?(t.left!=null&&(t.right=e.scrollWidth-e.clientWidth-t.left),kt()==z.INVERTED?t.left=t.right:kt()==z.NEGATED&&(t.left=t.right?-t.right:t.right)):t.right!=null&&(t.left=e.scrollWidth-e.clientWidth-t.right),this._applyScrollToOptions(t)}_applyScrollToOptions(t){let e=this.elementRef.nativeElement;_e()?e.scrollTo(t):(t.top!=null&&(e.scrollTop=t.top),t.left!=null&&(e.scrollLeft=t.left))}measureScrollOffset(t){let e="left",n="right",a=this.elementRef.nativeElement;if(t=="top")return a.scrollTop;if(t=="bottom")return a.scrollHeight-a.clientHeight-a.scrollTop;let s=this.dir&&this.dir.value=="rtl";return t=="start"?t=s?n:e:t=="end"&&(t=s?e:n),s&&kt()==z.INVERTED?t==e?a.scrollWidth-a.clientWidth-a.scrollLeft:a.scrollLeft:s&&kt()==z.NEGATED?t==e?a.scrollLeft+a.scrollWidth-a.clientWidth:-a.scrollLeft:t==e?a.scrollLeft:a.scrollWidth-a.clientWidth-a.scrollLeft}static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","cdk-scrollable",""],["","cdkScrollable",""]]})}return o})(),Mn=20,xe=(()=>{class o{_platform=r(g);_listeners;_viewportSize;_change=new p;_document=r(b,{optional:!0});constructor(){let t=r(h),e=r(j).createRenderer(null,null);t.runOutsideAngular(()=>{if(this._platform.isBrowser){let n=a=>this._change.next(a);this._listeners=[e.listen("window","resize",n),e.listen("window","orientationchange",n)]}this.change().subscribe(()=>this._viewportSize=null)})}ngOnDestroy(){this._listeners?.forEach(t=>t()),this._change.complete()}getViewportSize(){this._viewportSize||this._updateViewportSize();let t={width:this._viewportSize.width,height:this._viewportSize.height};return this._platform.isBrowser||(this._viewportSize=null),t}getViewportRect(){let t=this.getViewportScrollPosition(),{width:e,height:n}=this.getViewportSize();return{top:t.top,left:t.left,bottom:t.top+n,right:t.left+e,height:n,width:e}}getViewportScrollPosition(){if(!this._platform.isBrowser)return{top:0,left:0};let t=this._document,e=this._getWindow(),n=t.documentElement,a=n.getBoundingClientRect(),s=-a.top||t.body.scrollTop||e.scrollY||n.scrollTop||0,c=-a.left||t.body.scrollLeft||e.scrollX||n.scrollLeft||0;return{top:s,left:c}}change(t=Mn){return t>0?this._change.pipe(Pe(t)):this._change}_getWindow(){return this._document.defaultView||window}_updateViewportSize(){let t=this._getWindow();this._viewportSize=this._platform.isBrowser?{width:t.innerWidth,height:t.innerHeight}:{width:0,height:0}}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var hi=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({})}return o})(),fi=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({imports:[Q,hi,Q,hi]})}return o})();var So=_e(),ke=class{_viewportRuler;_previousHTMLStyles={top:"",left:""};_previousScrollPosition;_isEnabled=!1;_document;constructor(i,t){this._viewportRuler=i,this._document=t}attach(){}enable(){if(this._canBeEnabled()){let i=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=i.style.left||"",this._previousHTMLStyles.top=i.style.top||"",i.style.left=E(-this._previousScrollPosition.left),i.style.top=E(-this._previousScrollPosition.top),i.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){let i=this._document.documentElement,t=this._document.body,e=i.style,n=t.style,a=e.scrollBehavior||"",s=n.scrollBehavior||"";this._isEnabled=!1,e.left=this._previousHTMLStyles.left,e.top=this._previousHTMLStyles.top,i.classList.remove("cdk-global-scrollblock"),So&&(e.scrollBehavior=n.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),So&&(e.scrollBehavior=a,n.scrollBehavior=s)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;let t=this._document.documentElement,e=this._viewportRuler.getViewportSize();return t.scrollHeight>e.height||t.scrollWidth>e.width}};var Ee=class{_scrollDispatcher;_ngZone;_viewportRuler;_config;_scrollSubscription=null;_overlayRef;_initialScrollPosition;constructor(i,t,e,n){this._scrollDispatcher=i,this._ngZone=t,this._viewportRuler=e,this._config=n}attach(i){this._overlayRef,this._overlayRef=i}enable(){if(this._scrollSubscription)return;let i=this._scrollDispatcher.scrolled(0).pipe(ft(t=>!t||!this._overlayRef.overlayElement.contains(t.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=i.subscribe(()=>{let t=this._viewportRuler.getViewportScrollPosition().top;Math.abs(t-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=i.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}_detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}},Ht=class{enable(){}disable(){}attach(){}};function bi(o,i){return i.some(t=>{let e=o.bottom<t.top,n=o.top>t.bottom,a=o.right<t.left,s=o.left>t.right;return e||n||a||s})}function Ro(o,i){return i.some(t=>{let e=o.top<t.top,n=o.bottom>t.bottom,a=o.left<t.left,s=o.right>t.right;return e||n||a||s})}var Ce=class{_scrollDispatcher;_viewportRuler;_ngZone;_config;_scrollSubscription=null;_overlayRef;constructor(i,t,e,n){this._scrollDispatcher=i,this._viewportRuler=t,this._ngZone=e,this._config=n}attach(i){this._overlayRef,this._overlayRef=i}enable(){if(!this._scrollSubscription){let i=this._config?this._config.scrollThrottle:0;this._scrollSubscription=this._scrollDispatcher.scrolled(i).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){let t=this._overlayRef.overlayElement.getBoundingClientRect(),{width:e,height:n}=this._viewportRuler.getViewportSize();bi(t,[{width:e,height:n,bottom:n,right:e,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}})}}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}},To=(()=>{class o{_scrollDispatcher=r(we);_viewportRuler=r(xe);_ngZone=r(h);_document=r(b);constructor(){}noop=()=>new Ht;close=t=>new Ee(this._scrollDispatcher,this._ngZone,this._viewportRuler,t);block=()=>new ke(this._viewportRuler,this._document);reposition=t=>new Ce(this._scrollDispatcher,this._viewportRuler,this._ngZone,t);static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),mt=class{positionStrategy;scrollStrategy=new Ht;panelClass="";hasBackdrop=!1;backdropClass="cdk-overlay-dark-backdrop";width;height;minWidth;minHeight;maxWidth;maxHeight;direction;disposeOnNavigation=!1;constructor(i){if(i){let t=Object.keys(i);for(let e of t)i[e]!==void 0&&(this[e]=i[e])}}};var Se=class{connectionPair;scrollableViewProperties;constructor(i,t){this.connectionPair=i,this.scrollableViewProperties=t}};var Po=(()=>{class o{_attachedOverlays=[];_document=r(b);_isAttached;constructor(){}ngOnDestroy(){this.detach()}add(t){this.remove(t),this._attachedOverlays.push(t)}remove(t){let e=this._attachedOverlays.indexOf(t);e>-1&&this._attachedOverlays.splice(e,1),this._attachedOverlays.length===0&&this.detach()}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),Fo=(()=>{class o extends Po{_ngZone=r(h);_renderer=r(j).createRenderer(null,null);_cleanupKeydown;add(t){super.add(t),this._isAttached||(this._ngZone.runOutsideAngular(()=>{this._cleanupKeydown=this._renderer.listen("body","keydown",this._keydownListener)}),this._isAttached=!0)}detach(){this._isAttached&&(this._cleanupKeydown?.(),this._isAttached=!1)}_keydownListener=t=>{let e=this._attachedOverlays;for(let n=e.length-1;n>-1;n--)if(e[n]._keydownEvents.observers.length>0){this._ngZone.run(()=>e[n]._keydownEvents.next(t));break}};static \u0275fac=(()=>{let t;return function(n){return(t||(t=it(o)))(n||o)}})();static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),No=(()=>{class o extends Po{_platform=r(g);_ngZone=r(h);_renderer=r(j).createRenderer(null,null);_cursorOriginalValue;_cursorStyleIsSet=!1;_pointerDownEventTarget;_cleanups;add(t){if(super.add(t),!this._isAttached){let e=this._document.body,n={capture:!0};this._cleanups=this._ngZone.runOutsideAngular(()=>[P(this._renderer,e,"pointerdown",this._pointerDownListener,n),P(this._renderer,e,"click",this._clickListener,n),P(this._renderer,e,"auxclick",this._clickListener,n),P(this._renderer,e,"contextmenu",this._clickListener,n)]),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=e.style.cursor,e.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){this._isAttached&&(this._cleanups?.forEach(t=>t()),this._cleanups=void 0,this._platform.IOS&&this._cursorStyleIsSet&&(this._document.body.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1)}_pointerDownListener=t=>{this._pointerDownEventTarget=T(t)};_clickListener=t=>{let e=T(t),n=t.type==="click"&&this._pointerDownEventTarget?this._pointerDownEventTarget:e;this._pointerDownEventTarget=null;let a=this._attachedOverlays.slice();for(let s=a.length-1;s>-1;s--){let c=a[s];if(c._outsidePointerEvents.observers.length<1||!c.hasAttached())continue;if(Do(c.overlayElement,e)||Do(c.overlayElement,n))break;let d=c._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>d.next(t)):d.next(t)}};static \u0275fac=(()=>{let t;return function(n){return(t||(t=it(o)))(n||o)}})();static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();function Do(o,i){let t=typeof ShadowRoot<"u"&&ShadowRoot,e=i;for(;e;){if(e===o)return!0;e=t&&e instanceof ShadowRoot?e.host:e.parentNode}return!1}var Lo=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275cmp=A({type:o,selectors:[["ng-component"]],hostAttrs:["cdk-overlay-style-loader",""],decls:0,vars:0,template:function(e,n){},styles:[`.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}
`],encapsulation:2,changeDetection:0})}return o})(),Bo=(()=>{class o{_platform=r(g);_containerElement;_document=r(b);_styleLoader=r(F);constructor(){}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._loadStyles(),this._containerElement||this._createContainer(),this._containerElement}_createContainer(){let t="cdk-overlay-container";if(this._platform.isBrowser||ri()){let n=this._document.querySelectorAll(`.${t}[platform="server"], .${t}[platform="test"]`);for(let a=0;a<n.length;a++)n[a].remove()}let e=this._document.createElement("div");e.classList.add(t),ri()?e.setAttribute("platform","test"):this._platform.isBrowser||e.setAttribute("platform","server"),this._document.body.appendChild(e),this._containerElement=e}_loadStyles(){this._styleLoader.load(Lo)}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),_i=class{_renderer;_ngZone;element;_cleanupClick;_cleanupTransitionEnd;_fallbackTimeout;constructor(i,t,e,n){this._renderer=t,this._ngZone=e,this.element=i.createElement("div"),this.element.classList.add("cdk-overlay-backdrop"),this._cleanupClick=t.listen(this.element,"click",n)}detach(){this._ngZone.runOutsideAngular(()=>{let i=this.element;clearTimeout(this._fallbackTimeout),this._cleanupTransitionEnd?.(),this._cleanupTransitionEnd=this._renderer.listen(i,"transitionend",this.dispose),this._fallbackTimeout=setTimeout(this.dispose,500),i.style.pointerEvents="none",i.classList.remove("cdk-overlay-backdrop-showing")})}dispose=()=>{clearTimeout(this._fallbackTimeout),this._cleanupClick?.(),this._cleanupTransitionEnd?.(),this._cleanupClick=this._cleanupTransitionEnd=this._fallbackTimeout=void 0,this.element.remove()}},Re=class{_portalOutlet;_host;_pane;_config;_ngZone;_keyboardDispatcher;_document;_location;_outsideClickDispatcher;_animationsDisabled;_injector;_renderer;_backdropClick=new p;_attachments=new p;_detachments=new p;_positionStrategy;_scrollStrategy;_locationChanges=V.EMPTY;_backdropRef=null;_previousHostParent;_keydownEvents=new p;_outsidePointerEvents=new p;_renders=new p;_afterRenderRef;_afterNextRenderRef;constructor(i,t,e,n,a,s,c,d,u,l=!1,v,L){this._portalOutlet=i,this._host=t,this._pane=e,this._config=n,this._ngZone=a,this._keyboardDispatcher=s,this._document=c,this._location=d,this._outsideClickDispatcher=u,this._animationsDisabled=l,this._injector=v,this._renderer=L,n.scrollStrategy&&(this._scrollStrategy=n.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=n.positionStrategy,this._afterRenderRef=Li(()=>qt(()=>{this._renders.next()},{injector:this._injector}))}get overlayElement(){return this._pane}get backdropElement(){return this._backdropRef?.element||null}get hostElement(){return this._host}attach(i){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);let t=this._portalOutlet.attach(i);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._afterNextRenderRef?.destroy(),this._afterNextRenderRef=Ot(()=>{this.hasAttached()&&this.updatePosition()},{injector:this._injector}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),typeof t?.onDestroy=="function"&&t.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),t}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();let i=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),this._detachContentWhenEmpty(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),i}dispose(){let i=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._backdropRef?.dispose(),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._afterNextRenderRef?.destroy(),this._previousHostParent=this._pane=this._host=this._backdropRef=null,i&&this._detachments.next(),this._detachments.complete(),this._afterRenderRef.destroy(),this._renders.complete()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(i){i!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=i,this.hasAttached()&&(i.attach(this),this.updatePosition()))}updateSize(i){this._config=f(f({},this._config),i),this._updateElementSize()}setDirection(i){this._config=Ci(f({},this._config),{direction:i}),this._updateElementDirection()}addPanelClass(i){this._pane&&this._toggleClasses(this._pane,i,!0)}removePanelClass(i){this._pane&&this._toggleClasses(this._pane,i,!1)}getDirection(){let i=this._config.direction;return i?typeof i=="string"?i:i.value:"ltr"}updateScrollStrategy(i){i!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=i,this.hasAttached()&&(i.attach(this),i.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;let i=this._pane.style;i.width=E(this._config.width),i.height=E(this._config.height),i.minWidth=E(this._config.minWidth),i.minHeight=E(this._config.minHeight),i.maxWidth=E(this._config.maxWidth),i.maxHeight=E(this._config.maxHeight)}_togglePointerEvents(i){this._pane.style.pointerEvents=i?"":"none"}_attachBackdrop(){let i="cdk-overlay-backdrop-showing";this._backdropRef?.dispose(),this._backdropRef=new _i(this._document,this._renderer,this._ngZone,t=>{this._backdropClick.next(t)}),this._animationsDisabled&&this._backdropRef.element.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropRef.element,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropRef.element,this._host),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>this._backdropRef?.element.classList.add(i))}):this._backdropRef.element.classList.add(i)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){this._animationsDisabled?(this._backdropRef?.dispose(),this._backdropRef=null):this._backdropRef?.detach()}_toggleClasses(i,t,e){let n=xt(t||[]).filter(a=>!!a);n.length&&(e?i.classList.add(...n):i.classList.remove(...n))}_detachContentWhenEmpty(){this._ngZone.runOutsideAngular(()=>{let i=this._renders.pipe(Y(Ai(this._attachments,this._detachments))).subscribe(()=>{(!this._pane||!this._host||this._pane.children.length===0)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),i.unsubscribe())})})}_disposeScrollStrategy(){let i=this._scrollStrategy;i?.disable(),i?.detach?.()}},Ao="cdk-overlay-connected-position-bounding-box",Tn=/([A-Za-z%]+)$/,De=class{_viewportRuler;_document;_platform;_overlayContainer;_overlayRef;_isInitialRender;_lastBoundingBoxSize={width:0,height:0};_isPushed=!1;_canPush=!0;_growAfterOpen=!1;_hasFlexibleDimensions=!0;_positionLocked=!1;_originRect;_overlayRect;_viewportRect;_containerRect;_viewportMargin=0;_scrollables=[];_preferredPositions=[];_origin;_pane;_isDisposed;_boundingBox;_lastPosition;_lastScrollVisibility;_positionChanges=new p;_resizeSubscription=V.EMPTY;_offsetX=0;_offsetY=0;_transformOriginSelector;_appliedPanelClasses=[];_previousPushAmount;positionChanges=this._positionChanges;get positions(){return this._preferredPositions}constructor(i,t,e,n,a){this._viewportRuler=t,this._document=e,this._platform=n,this._overlayContainer=a,this.setOrigin(i)}attach(i){this._overlayRef&&this._overlayRef,this._validatePositions(),i.hostElement.classList.add(Ao),this._overlayRef=i,this._boundingBox=i.hostElement,this._pane=i.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition){this.reapplyLastPosition();return}this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let i=this._originRect,t=this._overlayRect,e=this._viewportRect,n=this._containerRect,a=[],s;for(let c of this._preferredPositions){let d=this._getOriginPoint(i,n,c),u=this._getOverlayPoint(d,t,c),l=this._getOverlayFit(u,t,e,c);if(l.isCompletelyWithinViewport){this._isPushed=!1,this._applyPosition(c,d);return}if(this._canFitWithFlexibleDimensions(l,u,e)){a.push({position:c,origin:d,overlayRect:t,boundingBoxRect:this._calculateBoundingBoxRect(d,c)});continue}(!s||s.overlayFit.visibleArea<l.visibleArea)&&(s={overlayFit:l,overlayPoint:u,originPoint:d,position:c,overlayRect:t})}if(a.length){let c=null,d=-1;for(let u of a){let l=u.boundingBoxRect.width*u.boundingBoxRect.height*(u.position.weight||1);l>d&&(d=l,c=u)}this._isPushed=!1,this._applyPosition(c.position,c.origin);return}if(this._canPush){this._isPushed=!0,this._applyPosition(s.position,s.originPoint);return}this._applyPosition(s.position,s.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&dt(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(Ao),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;let i=this._lastPosition;if(i){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let t=this._getOriginPoint(this._originRect,this._containerRect,i);this._applyPosition(i,t)}else this.apply()}withScrollableContainers(i){return this._scrollables=i,this}withPositions(i){return this._preferredPositions=i,i.indexOf(this._lastPosition)===-1&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(i){return this._viewportMargin=i,this}withFlexibleDimensions(i=!0){return this._hasFlexibleDimensions=i,this}withGrowAfterOpen(i=!0){return this._growAfterOpen=i,this}withPush(i=!0){return this._canPush=i,this}withLockedPosition(i=!0){return this._positionLocked=i,this}setOrigin(i){return this._origin=i,this}withDefaultOffsetX(i){return this._offsetX=i,this}withDefaultOffsetY(i){return this._offsetY=i,this}withTransformOriginOn(i){return this._transformOriginSelector=i,this}_getOriginPoint(i,t,e){let n;if(e.originX=="center")n=i.left+i.width/2;else{let s=this._isRtl()?i.right:i.left,c=this._isRtl()?i.left:i.right;n=e.originX=="start"?s:c}t.left<0&&(n-=t.left);let a;return e.originY=="center"?a=i.top+i.height/2:a=e.originY=="top"?i.top:i.bottom,t.top<0&&(a-=t.top),{x:n,y:a}}_getOverlayPoint(i,t,e){let n;e.overlayX=="center"?n=-t.width/2:e.overlayX==="start"?n=this._isRtl()?-t.width:0:n=this._isRtl()?0:-t.width;let a;return e.overlayY=="center"?a=-t.height/2:a=e.overlayY=="top"?0:-t.height,{x:i.x+n,y:i.y+a}}_getOverlayFit(i,t,e,n){let a=Io(t),{x:s,y:c}=i,d=this._getOffset(n,"x"),u=this._getOffset(n,"y");d&&(s+=d),u&&(c+=u);let l=0-s,v=s+a.width-e.width,L=0-c,I=c+a.height-e.height,D=this._subtractOverflows(a.width,l,v),S=this._subtractOverflows(a.height,L,I),tt=D*S;return{visibleArea:tt,isCompletelyWithinViewport:a.width*a.height===tt,fitsInViewportVertically:S===a.height,fitsInViewportHorizontally:D==a.width}}_canFitWithFlexibleDimensions(i,t,e){if(this._hasFlexibleDimensions){let n=e.bottom-t.y,a=e.right-t.x,s=Oo(this._overlayRef.getConfig().minHeight),c=Oo(this._overlayRef.getConfig().minWidth),d=i.fitsInViewportVertically||s!=null&&s<=n,u=i.fitsInViewportHorizontally||c!=null&&c<=a;return d&&u}return!1}_pushOverlayOnScreen(i,t,e){if(this._previousPushAmount&&this._positionLocked)return{x:i.x+this._previousPushAmount.x,y:i.y+this._previousPushAmount.y};let n=Io(t),a=this._viewportRect,s=Math.max(i.x+n.width-a.width,0),c=Math.max(i.y+n.height-a.height,0),d=Math.max(a.top-e.top-i.y,0),u=Math.max(a.left-e.left-i.x,0),l=0,v=0;return n.width<=a.width?l=u||-s:l=i.x<this._viewportMargin?a.left-e.left-i.x:0,n.height<=a.height?v=d||-c:v=i.y<this._viewportMargin?a.top-e.top-i.y:0,this._previousPushAmount={x:l,y:v},{x:i.x+l,y:i.y+v}}_applyPosition(i,t){if(this._setTransformOrigin(i),this._setOverlayElementStyles(t,i),this._setBoundingBoxStyles(t,i),i.panelClass&&this._addPanelClasses(i.panelClass),this._positionChanges.observers.length){let e=this._getScrollVisibility();if(i!==this._lastPosition||!this._lastScrollVisibility||!Pn(this._lastScrollVisibility,e)){let n=new Se(i,e);this._positionChanges.next(n)}this._lastScrollVisibility=e}this._lastPosition=i,this._isInitialRender=!1}_setTransformOrigin(i){if(!this._transformOriginSelector)return;let t=this._boundingBox.querySelectorAll(this._transformOriginSelector),e,n=i.overlayY;i.overlayX==="center"?e="center":this._isRtl()?e=i.overlayX==="start"?"right":"left":e=i.overlayX==="start"?"left":"right";for(let a=0;a<t.length;a++)t[a].style.transformOrigin=`${e} ${n}`}_calculateBoundingBoxRect(i,t){let e=this._viewportRect,n=this._isRtl(),a,s,c;if(t.overlayY==="top")s=i.y,a=e.height-s+this._viewportMargin;else if(t.overlayY==="bottom")c=e.height-i.y+this._viewportMargin*2,a=e.height-c+this._viewportMargin;else{let I=Math.min(e.bottom-i.y+e.top,i.y),D=this._lastBoundingBoxSize.height;a=I*2,s=i.y-I,a>D&&!this._isInitialRender&&!this._growAfterOpen&&(s=i.y-D/2)}let d=t.overlayX==="start"&&!n||t.overlayX==="end"&&n,u=t.overlayX==="end"&&!n||t.overlayX==="start"&&n,l,v,L;if(u)L=e.width-i.x+this._viewportMargin*2,l=i.x-this._viewportMargin;else if(d)v=i.x,l=e.right-i.x;else{let I=Math.min(e.right-i.x+e.left,i.x),D=this._lastBoundingBoxSize.width;l=I*2,v=i.x-I,l>D&&!this._isInitialRender&&!this._growAfterOpen&&(v=i.x-D/2)}return{top:s,left:v,bottom:c,right:L,width:l,height:a}}_setBoundingBoxStyles(i,t){let e=this._calculateBoundingBoxRect(i,t);!this._isInitialRender&&!this._growAfterOpen&&(e.height=Math.min(e.height,this._lastBoundingBoxSize.height),e.width=Math.min(e.width,this._lastBoundingBoxSize.width));let n={};if(this._hasExactPosition())n.top=n.left="0",n.bottom=n.right=n.maxHeight=n.maxWidth="",n.width=n.height="100%";else{let a=this._overlayRef.getConfig().maxHeight,s=this._overlayRef.getConfig().maxWidth;n.height=E(e.height),n.top=E(e.top),n.bottom=E(e.bottom),n.width=E(e.width),n.left=E(e.left),n.right=E(e.right),t.overlayX==="center"?n.alignItems="center":n.alignItems=t.overlayX==="end"?"flex-end":"flex-start",t.overlayY==="center"?n.justifyContent="center":n.justifyContent=t.overlayY==="bottom"?"flex-end":"flex-start",a&&(n.maxHeight=E(a)),s&&(n.maxWidth=E(s))}this._lastBoundingBoxSize=e,dt(this._boundingBox.style,n)}_resetBoundingBoxStyles(){dt(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){dt(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(i,t){let e={},n=this._hasExactPosition(),a=this._hasFlexibleDimensions,s=this._overlayRef.getConfig();if(n){let l=this._viewportRuler.getViewportScrollPosition();dt(e,this._getExactOverlayY(t,i,l)),dt(e,this._getExactOverlayX(t,i,l))}else e.position="static";let c="",d=this._getOffset(t,"x"),u=this._getOffset(t,"y");d&&(c+=`translateX(${d}px) `),u&&(c+=`translateY(${u}px)`),e.transform=c.trim(),s.maxHeight&&(n?e.maxHeight=E(s.maxHeight):a&&(e.maxHeight="")),s.maxWidth&&(n?e.maxWidth=E(s.maxWidth):a&&(e.maxWidth="")),dt(this._pane.style,e)}_getExactOverlayY(i,t,e){let n={top:"",bottom:""},a=this._getOverlayPoint(t,this._overlayRect,i);if(this._isPushed&&(a=this._pushOverlayOnScreen(a,this._overlayRect,e)),i.overlayY==="bottom"){let s=this._document.documentElement.clientHeight;n.bottom=`${s-(a.y+this._overlayRect.height)}px`}else n.top=E(a.y);return n}_getExactOverlayX(i,t,e){let n={left:"",right:""},a=this._getOverlayPoint(t,this._overlayRect,i);this._isPushed&&(a=this._pushOverlayOnScreen(a,this._overlayRect,e));let s;if(this._isRtl()?s=i.overlayX==="end"?"left":"right":s=i.overlayX==="end"?"right":"left",s==="right"){let c=this._document.documentElement.clientWidth;n.right=`${c-(a.x+this._overlayRect.width)}px`}else n.left=E(a.x);return n}_getScrollVisibility(){let i=this._getOriginRect(),t=this._pane.getBoundingClientRect(),e=this._scrollables.map(n=>n.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:Ro(i,e),isOriginOutsideView:bi(i,e),isOverlayClipped:Ro(t,e),isOverlayOutsideView:bi(t,e)}}_subtractOverflows(i,...t){return t.reduce((e,n)=>e-Math.max(n,0),i)}_getNarrowedViewportRect(){let i=this._document.documentElement.clientWidth,t=this._document.documentElement.clientHeight,e=this._viewportRuler.getViewportScrollPosition();return{top:e.top+this._viewportMargin,left:e.left+this._viewportMargin,right:e.left+i-this._viewportMargin,bottom:e.top+t-this._viewportMargin,width:i-2*this._viewportMargin,height:t-2*this._viewportMargin}}_isRtl(){return this._overlayRef.getDirection()==="rtl"}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(i,t){return t==="x"?i.offsetX==null?this._offsetX:i.offsetX:i.offsetY==null?this._offsetY:i.offsetY}_validatePositions(){}_addPanelClasses(i){this._pane&&xt(i).forEach(t=>{t!==""&&this._appliedPanelClasses.indexOf(t)===-1&&(this._appliedPanelClasses.push(t),this._pane.classList.add(t))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(i=>{this._pane.classList.remove(i)}),this._appliedPanelClasses=[])}_getOriginRect(){let i=this._origin;if(i instanceof C)return i.nativeElement.getBoundingClientRect();if(i instanceof Element)return i.getBoundingClientRect();let t=i.width||0,e=i.height||0;return{top:i.y,bottom:i.y+e,left:i.x,right:i.x+t,height:e,width:t}}};function dt(o,i){for(let t in i)i.hasOwnProperty(t)&&(o[t]=i[t]);return o}function Oo(o){if(typeof o!="number"&&o!=null){let[i,t]=o.split(Tn);return!t||t==="px"?parseFloat(i):null}return o||null}function Io(o){return{top:Math.floor(o.top),right:Math.floor(o.right),bottom:Math.floor(o.bottom),left:Math.floor(o.left),width:Math.floor(o.width),height:Math.floor(o.height)}}function Pn(o,i){return o===i?!0:o.isOriginClipped===i.isOriginClipped&&o.isOriginOutsideView===i.isOriginOutsideView&&o.isOverlayClipped===i.isOverlayClipped&&o.isOverlayOutsideView===i.isOverlayOutsideView}var Mo="cdk-global-overlay-wrapper",Ae=class{_overlayRef;_cssPosition="static";_topOffset="";_bottomOffset="";_alignItems="";_xPosition="";_xOffset="";_width="";_height="";_isDisposed=!1;attach(i){let t=i.getConfig();this._overlayRef=i,this._width&&!t.width&&i.updateSize({width:this._width}),this._height&&!t.height&&i.updateSize({height:this._height}),i.hostElement.classList.add(Mo),this._isDisposed=!1}top(i=""){return this._bottomOffset="",this._topOffset=i,this._alignItems="flex-start",this}left(i=""){return this._xOffset=i,this._xPosition="left",this}bottom(i=""){return this._topOffset="",this._bottomOffset=i,this._alignItems="flex-end",this}right(i=""){return this._xOffset=i,this._xPosition="right",this}start(i=""){return this._xOffset=i,this._xPosition="start",this}end(i=""){return this._xOffset=i,this._xPosition="end",this}width(i=""){return this._overlayRef?this._overlayRef.updateSize({width:i}):this._width=i,this}height(i=""){return this._overlayRef?this._overlayRef.updateSize({height:i}):this._height=i,this}centerHorizontally(i=""){return this.left(i),this._xPosition="center",this}centerVertically(i=""){return this.top(i),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;let i=this._overlayRef.overlayElement.style,t=this._overlayRef.hostElement.style,e=this._overlayRef.getConfig(),{width:n,height:a,maxWidth:s,maxHeight:c}=e,d=(n==="100%"||n==="100vw")&&(!s||s==="100%"||s==="100vw"),u=(a==="100%"||a==="100vh")&&(!c||c==="100%"||c==="100vh"),l=this._xPosition,v=this._xOffset,L=this._overlayRef.getConfig().direction==="rtl",I="",D="",S="";d?S="flex-start":l==="center"?(S="center",L?D=v:I=v):L?l==="left"||l==="end"?(S="flex-end",I=v):(l==="right"||l==="start")&&(S="flex-start",D=v):l==="left"||l==="start"?(S="flex-start",I=v):(l==="right"||l==="end")&&(S="flex-end",D=v),i.position=this._cssPosition,i.marginLeft=d?"0":I,i.marginTop=u?"0":this._topOffset,i.marginBottom=this._bottomOffset,i.marginRight=d?"0":D,t.justifyContent=S,t.alignItems=u?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;let i=this._overlayRef.overlayElement.style,t=this._overlayRef.hostElement,e=t.style;t.classList.remove(Mo),e.justifyContent=e.alignItems=i.marginTop=i.marginBottom=i.marginLeft=i.marginRight=i.position="",this._overlayRef=null,this._isDisposed=!0}},jo=(()=>{class o{_viewportRuler=r(xe);_document=r(b);_platform=r(g);_overlayContainer=r(Bo);constructor(){}global(){return new Ae}flexibleConnectedTo(t){return new De(t,this._viewportRuler,this._document,this._platform,this._overlayContainer)}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),ut=(()=>{class o{scrollStrategies=r(To);_overlayContainer=r(Bo);_positionBuilder=r(jo);_keyboardDispatcher=r(Fo);_injector=r(R);_ngZone=r(h);_document=r(b);_directionality=r(st);_location=r(ji);_outsideClickDispatcher=r(No);_animationsModuleType=r(U,{optional:!0});_idGenerator=r(Bt);_renderer=r(j).createRenderer(null,null);_appRef;_styleLoader=r(F);constructor(){}create(t){this._styleLoader.load(Lo);let e=this._createHostElement(),n=this._createPaneElement(e),a=this._createPortalOutlet(n),s=new mt(t);return s.direction=s.direction||this._directionality.value,new Re(a,e,n,s,this._ngZone,this._keyboardDispatcher,this._document,this._location,this._outsideClickDispatcher,this._animationsModuleType==="NoopAnimations",this._injector.get(gt),this._renderer)}position(){return this._positionBuilder}_createPaneElement(t){let e=this._document.createElement("div");return e.id=this._idGenerator.getId("cdk-overlay-"),e.classList.add("cdk-overlay-pane"),t.appendChild(e),e}_createHostElement(){let t=this._document.createElement("div");return this._overlayContainer.getContainerElement().appendChild(t),t}_createPortalOutlet(t){return this._appRef||(this._appRef=this._injector.get(Qt)),new Vt(t,null,this._appRef,this._injector,this._document)}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),Fn=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}],zo=new y("cdk-connected-overlay-scroll-strategy",{providedIn:"root",factory:()=>{let o=r(ut);return()=>o.scrollStrategies.reposition()}}),gi=(()=>{class o{elementRef=r(C);constructor(){}static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","cdk-overlay-origin",""],["","overlay-origin",""],["","cdkOverlayOrigin",""]],exportAs:["cdkOverlayOrigin"]})}return o})(),Nn=(()=>{class o{_overlay=r(ut);_dir=r(st,{optional:!0});_overlayRef;_templatePortal;_backdropSubscription=V.EMPTY;_attachSubscription=V.EMPTY;_detachSubscription=V.EMPTY;_positionSubscription=V.EMPTY;_offsetX;_offsetY;_position;_scrollStrategyFactory=r(zo);_disposeOnNavigation=!1;_ngZone=r(h);origin;positions;positionStrategy;get offsetX(){return this._offsetX}set offsetX(t){this._offsetX=t,this._position&&this._updatePositionStrategy(this._position)}get offsetY(){return this._offsetY}set offsetY(t){this._offsetY=t,this._position&&this._updatePositionStrategy(this._position)}width;height;minWidth;minHeight;backdropClass;panelClass;viewportMargin=0;scrollStrategy;open=!1;disableClose=!1;transformOriginSelector;hasBackdrop=!1;lockPosition=!1;flexibleDimensions=!1;growAfterOpen=!1;push=!1;get disposeOnNavigation(){return this._disposeOnNavigation}set disposeOnNavigation(t){this._disposeOnNavigation=t}backdropClick=new M;positionChange=new M;attach=new M;detach=new M;overlayKeydown=new M;overlayOutsideClick=new M;constructor(){let t=r(yt),e=r(It);this._templatePortal=new J(t,e),this.scrollStrategy=this._scrollStrategyFactory()}get overlayRef(){return this._overlayRef}get dir(){return this._dir?this._dir.value:"ltr"}ngOnDestroy(){this._attachSubscription.unsubscribe(),this._detachSubscription.unsubscribe(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this._overlayRef?.dispose()}ngOnChanges(t){this._position&&(this._updatePositionStrategy(this._position),this._overlayRef?.updateSize({width:this.width,minWidth:this.minWidth,height:this.height,minHeight:this.minHeight}),t.origin&&this.open&&this._position.apply()),t.open&&(this.open?this.attachOverlay():this.detachOverlay())}_createOverlay(){(!this.positions||!this.positions.length)&&(this.positions=Fn);let t=this._overlayRef=this._overlay.create(this._buildConfig());this._attachSubscription=t.attachments().subscribe(()=>this.attach.emit()),this._detachSubscription=t.detachments().subscribe(()=>this.detach.emit()),t.keydownEvents().subscribe(e=>{this.overlayKeydown.next(e),e.keyCode===27&&!this.disableClose&&!ue(e)&&(e.preventDefault(),this.detachOverlay())}),this._overlayRef.outsidePointerEvents().subscribe(e=>{let n=this._getOriginElement(),a=T(e);(!n||n!==a&&!n.contains(a))&&this.overlayOutsideClick.next(e)})}_buildConfig(){let t=this._position=this.positionStrategy||this._createPositionStrategy(),e=new mt({direction:this._dir||"ltr",positionStrategy:t,scrollStrategy:this.scrollStrategy,hasBackdrop:this.hasBackdrop,disposeOnNavigation:this.disposeOnNavigation});return(this.width||this.width===0)&&(e.width=this.width),(this.height||this.height===0)&&(e.height=this.height),(this.minWidth||this.minWidth===0)&&(e.minWidth=this.minWidth),(this.minHeight||this.minHeight===0)&&(e.minHeight=this.minHeight),this.backdropClass&&(e.backdropClass=this.backdropClass),this.panelClass&&(e.panelClass=this.panelClass),e}_updatePositionStrategy(t){let e=this.positions.map(n=>({originX:n.originX,originY:n.originY,overlayX:n.overlayX,overlayY:n.overlayY,offsetX:n.offsetX||this.offsetX,offsetY:n.offsetY||this.offsetY,panelClass:n.panelClass||void 0}));return t.setOrigin(this._getOrigin()).withPositions(e).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector)}_createPositionStrategy(){let t=this._overlay.position().flexibleConnectedTo(this._getOrigin());return this._updatePositionStrategy(t),t}_getOrigin(){return this.origin instanceof gi?this.origin.elementRef:this.origin}_getOriginElement(){return this.origin instanceof gi?this.origin.elementRef.nativeElement:this.origin instanceof C?this.origin.nativeElement:typeof Element<"u"&&this.origin instanceof Element?this.origin:null}attachOverlay(){this._overlayRef?this._overlayRef.getConfig().hasBackdrop=this.hasBackdrop:this._createOverlay(),this._overlayRef.hasAttached()||this._overlayRef.attach(this._templatePortal),this.hasBackdrop?this._backdropSubscription=this._overlayRef.backdropClick().subscribe(t=>{this.backdropClick.emit(t)}):this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.positionChange.observers.length>0&&(this._positionSubscription=this._position.positionChanges.pipe(Oi(()=>this.positionChange.observers.length>0)).subscribe(t=>{this._ngZone.run(()=>this.positionChange.emit(t)),this.positionChange.observers.length===0&&this._positionSubscription.unsubscribe()})),this.open=!0}detachOverlay(){this._overlayRef?.detach(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.open=!1}static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","cdk-connected-overlay",""],["","connected-overlay",""],["","cdkConnectedOverlay",""]],inputs:{origin:[0,"cdkConnectedOverlayOrigin","origin"],positions:[0,"cdkConnectedOverlayPositions","positions"],positionStrategy:[0,"cdkConnectedOverlayPositionStrategy","positionStrategy"],offsetX:[0,"cdkConnectedOverlayOffsetX","offsetX"],offsetY:[0,"cdkConnectedOverlayOffsetY","offsetY"],width:[0,"cdkConnectedOverlayWidth","width"],height:[0,"cdkConnectedOverlayHeight","height"],minWidth:[0,"cdkConnectedOverlayMinWidth","minWidth"],minHeight:[0,"cdkConnectedOverlayMinHeight","minHeight"],backdropClass:[0,"cdkConnectedOverlayBackdropClass","backdropClass"],panelClass:[0,"cdkConnectedOverlayPanelClass","panelClass"],viewportMargin:[0,"cdkConnectedOverlayViewportMargin","viewportMargin"],scrollStrategy:[0,"cdkConnectedOverlayScrollStrategy","scrollStrategy"],open:[0,"cdkConnectedOverlayOpen","open"],disableClose:[0,"cdkConnectedOverlayDisableClose","disableClose"],transformOriginSelector:[0,"cdkConnectedOverlayTransformOriginOn","transformOriginSelector"],hasBackdrop:[2,"cdkConnectedOverlayHasBackdrop","hasBackdrop",O],lockPosition:[2,"cdkConnectedOverlayLockPosition","lockPosition",O],flexibleDimensions:[2,"cdkConnectedOverlayFlexibleDimensions","flexibleDimensions",O],growAfterOpen:[2,"cdkConnectedOverlayGrowAfterOpen","growAfterOpen",O],push:[2,"cdkConnectedOverlayPush","push",O],disposeOnNavigation:[2,"cdkConnectedOverlayDisposeOnNavigation","disposeOnNavigation",O]},outputs:{backdropClick:"backdropClick",positionChange:"positionChange",attach:"attach",detach:"detach",overlayKeydown:"overlayKeydown",overlayOutsideClick:"overlayOutsideClick"},exportAs:["cdkConnectedOverlay"],features:[At]})}return o})();function Ln(o){return()=>o.scrollStrategies.reposition()}var Bn={provide:zo,deps:[ut],useFactory:Ln},vi=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({providers:[ut,Bn],imports:[Q,Ut,fi,fi]})}return o})();function jn(o,i){if(o&1){let t=Ni();ot(0,"div",1)(1,"button",2),Ue("click",function(){Ii(t);let n=He();return Mi(n.action())}),We(2),G()()}if(o&2){let t=He();vt(2),Ye(" ",t.data.action," ")}}var zn=["label"];function Vn(o,i){}var Un=Math.pow(2,31)-1,Wt=class{_overlayRef;instance;containerInstance;_afterDismissed=new p;_afterOpened=new p;_onAction=new p;_durationTimeoutId;_dismissedByAction=!1;constructor(i,t){this._overlayRef=t,this.containerInstance=i,i._onExit.subscribe(()=>this._finishDismiss())}dismiss(){this._afterDismissed.closed||this.containerInstance.exit(),clearTimeout(this._durationTimeoutId)}dismissWithAction(){this._onAction.closed||(this._dismissedByAction=!0,this._onAction.next(),this._onAction.complete(),this.dismiss()),clearTimeout(this._durationTimeoutId)}closeWithAction(){this.dismissWithAction()}_dismissAfter(i){this._durationTimeoutId=setTimeout(()=>this.dismiss(),Math.min(i,Un))}_open(){this._afterOpened.closed||(this._afterOpened.next(),this._afterOpened.complete())}_finishDismiss(){this._overlayRef.dispose(),this._onAction.closed||this._onAction.complete(),this._afterDismissed.next({dismissedByAction:this._dismissedByAction}),this._afterDismissed.complete(),this._dismissedByAction=!1}afterDismissed(){return this._afterDismissed}afterOpened(){return this.containerInstance._onEnter}onAction(){return this._onAction}},Vo=new y("MatSnackBarData"),St=class{politeness="assertive";announcementMessage="";viewContainerRef;duration=0;panelClass;direction;data=null;horizontalPosition="center";verticalPosition="bottom"},Hn=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","matSnackBarLabel",""]],hostAttrs:[1,"mat-mdc-snack-bar-label","mdc-snackbar__label"]})}return o})(),Wn=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","matSnackBarActions",""]],hostAttrs:[1,"mat-mdc-snack-bar-actions","mdc-snackbar__actions"]})}return o})(),Yn=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275dir=w({type:o,selectors:[["","matSnackBarAction",""]],hostAttrs:[1,"mat-mdc-snack-bar-action","mdc-snackbar__action"]})}return o})(),Uo=(()=>{class o{snackBarRef=r(Wt);data=r(Vo);constructor(){}action(){this.snackBarRef.dismissWithAction()}get hasAction(){return!!this.data.action}static \u0275fac=function(e){return new(e||o)};static \u0275cmp=A({type:o,selectors:[["simple-snack-bar"]],hostAttrs:[1,"mat-mdc-simple-snack-bar"],exportAs:["matSnackBar"],decls:3,vars:2,consts:[["matSnackBarLabel",""],["matSnackBarActions",""],["mat-button","","matSnackBarAction","",3,"click"]],template:function(e,n){e&1&&(ot(0,"div",0),We(1),G(),Ve(2,jn,3,1,"div",1)),e&2&&(vt(),Ye(" ",n.data.message,`
`),vt(),Fi(n.hasAction?2:-1))},dependencies:[xo,Hn,Wn,Yn],styles:[`.mat-mdc-simple-snack-bar{display:flex}
`],encapsulation:2,changeDetection:0})}return o})(),yi="_mat-snack-bar-enter",wi="_mat-snack-bar-exit",Xn=(()=>{class o extends lt{_ngZone=r(h);_elementRef=r(C);_changeDetectorRef=r(Ze);_platform=r(g);_rendersRef;_animationsDisabled=r(U,{optional:!0})==="NoopAnimations";snackBarConfig=r(St);_document=r(b);_trackedModals=new Set;_enterFallback;_exitFallback;_renders=new p;_announceDelay=150;_announceTimeoutId;_destroyed=!1;_portalOutlet;_onAnnounce=new p;_onExit=new p;_onEnter=new p;_animationState="void";_live;_label;_role;_liveElementId=r(Bt).getId("mat-snack-bar-container-live-");constructor(){super();let t=this.snackBarConfig;t.politeness==="assertive"&&!t.announcementMessage?this._live="assertive":t.politeness==="off"?this._live="off":this._live="polite",this._platform.FIREFOX&&(this._live==="polite"&&(this._role="status"),this._live==="assertive"&&(this._role="alert")),this._rendersRef=qt(()=>this._renders.next(),{manualCleanup:!0})}attachComponentPortal(t){this._assertNotAttached();let e=this._portalOutlet.attachComponentPortal(t);return this._afterPortalAttached(),e}attachTemplatePortal(t){this._assertNotAttached();let e=this._portalOutlet.attachTemplatePortal(t);return this._afterPortalAttached(),e}attachDomPortal=t=>{this._assertNotAttached();let e=this._portalOutlet.attachDomPortal(t);return this._afterPortalAttached(),e};onAnimationEnd(t){t===wi?this._completeExit():t===yi&&(clearTimeout(this._enterFallback),this._ngZone.run(()=>{this._onEnter.next(),this._onEnter.complete()}))}enter(){this._destroyed||(this._animationState="visible",this._changeDetectorRef.markForCheck(),this._changeDetectorRef.detectChanges(),this._screenReaderAnnounce(),this._animationsDisabled?this._renders.pipe(Dt(1)).subscribe(()=>{this._ngZone.run(()=>queueMicrotask(()=>this.onAnimationEnd(yi)))}):(clearTimeout(this._enterFallback),this._enterFallback=setTimeout(()=>{this._elementRef.nativeElement.classList.add("mat-snack-bar-fallback-visible"),this.onAnimationEnd(yi)},200)))}exit(){return this._destroyed?ht(void 0):(this._ngZone.run(()=>{this._animationState="hidden",this._changeDetectorRef.markForCheck(),this._elementRef.nativeElement.setAttribute("mat-exit",""),clearTimeout(this._announceTimeoutId),this._animationsDisabled?this._renders.pipe(Dt(1)).subscribe(()=>{this._ngZone.run(()=>queueMicrotask(()=>this.onAnimationEnd(wi)))}):(clearTimeout(this._exitFallback),this._exitFallback=setTimeout(()=>this.onAnimationEnd(wi),200))}),this._onExit)}ngOnDestroy(){this._destroyed=!0,this._clearFromModals(),this._completeExit(),this._renders.complete(),this._rendersRef.destroy()}_completeExit(){clearTimeout(this._exitFallback),queueMicrotask(()=>{this._onExit.next(),this._onExit.complete()})}_afterPortalAttached(){let t=this._elementRef.nativeElement,e=this.snackBarConfig.panelClass;e&&(Array.isArray(e)?e.forEach(s=>t.classList.add(s)):t.classList.add(e)),this._exposeToModals();let n=this._label.nativeElement,a="mdc-snackbar__label";n.classList.toggle(a,!n.querySelector(`.${a}`))}_exposeToModals(){let t=this._liveElementId,e=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let n=0;n<e.length;n++){let a=e[n],s=a.getAttribute("aria-owns");this._trackedModals.add(a),s?s.indexOf(t)===-1&&a.setAttribute("aria-owns",s+" "+t):a.setAttribute("aria-owns",t)}}_clearFromModals(){this._trackedModals.forEach(t=>{let e=t.getAttribute("aria-owns");if(e){let n=e.replace(this._liveElementId,"").trim();n.length>0?t.setAttribute("aria-owns",n):t.removeAttribute("aria-owns")}}),this._trackedModals.clear()}_assertNotAttached(){this._portalOutlet.hasAttached()}_screenReaderAnnounce(){this._announceTimeoutId||this._ngZone.runOutsideAngular(()=>{this._announceTimeoutId=setTimeout(()=>{if(this._destroyed)return;let t=this._elementRef.nativeElement,e=t.querySelector("[aria-hidden]"),n=t.querySelector("[aria-live]");if(e&&n){let a=null;this._platform.isBrowser&&document.activeElement instanceof HTMLElement&&e.contains(document.activeElement)&&(a=document.activeElement),e.removeAttribute("aria-hidden"),n.appendChild(e),a?.focus(),this._onAnnounce.next(),this._onAnnounce.complete()}},this._announceDelay)})}static \u0275fac=function(e){return new(e||o)};static \u0275cmp=A({type:o,selectors:[["mat-snack-bar-container"]],viewQuery:function(e,n){if(e&1&&(te(ye,7),te(zn,7)),e&2){let a;ee(a=ie())&&(n._portalOutlet=a.first),ee(a=ie())&&(n._label=a.first)}},hostAttrs:[1,"mdc-snackbar","mat-mdc-snack-bar-container"],hostVars:6,hostBindings:function(e,n){e&1&&Ue("animationend",function(s){return n.onAnimationEnd(s.animationName)})("animationcancel",function(s){return n.onAnimationEnd(s.animationName)}),e&2&&H("mat-snack-bar-container-enter",n._animationState==="visible")("mat-snack-bar-container-exit",n._animationState==="hidden")("mat-snack-bar-container-animations-enabled",!n._animationsDisabled)},features:[X],decls:6,vars:3,consts:[["label",""],[1,"mdc-snackbar__surface","mat-mdc-snackbar-surface"],[1,"mat-mdc-snack-bar-label"],["aria-hidden","true"],["cdkPortalOutlet",""]],template:function(e,n){e&1&&(ot(0,"div",1)(1,"div",2,0)(3,"div",3),Ve(4,Vn,0,0,"ng-template",4),G(),Z(5,"div"),G()()),e&2&&(vt(5),$("aria-live",n._live)("role",n._role)("id",n._liveElementId))},dependencies:[ye],styles:[`@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}
`],encapsulation:2})}return o})();function Zn(){return new St}var Kn=new y("mat-snack-bar-default-options",{providedIn:"root",factory:Zn}),xi=(()=>{class o{_overlay=r(ut);_live=r(ei);_injector=r(R);_breakpointObserver=r(Lt);_parentSnackBar=r(o,{optional:!0,skipSelf:!0});_defaultConfig=r(Kn);_snackBarRefAtThisLevel=null;simpleSnackBarComponent=Uo;snackBarContainerComponent=Xn;handsetCssClass="mat-mdc-snack-bar-handset";get _openedSnackBarRef(){let t=this._parentSnackBar;return t?t._openedSnackBarRef:this._snackBarRefAtThisLevel}set _openedSnackBarRef(t){this._parentSnackBar?this._parentSnackBar._openedSnackBarRef=t:this._snackBarRefAtThisLevel=t}constructor(){}openFromComponent(t,e){return this._attach(t,e)}openFromTemplate(t,e){return this._attach(t,e)}open(t,e="",n){let a=f(f({},this._defaultConfig),n);return a.data={message:t,action:e},a.announcementMessage===t&&(a.announcementMessage=void 0),this.openFromComponent(this.simpleSnackBarComponent,a)}dismiss(){this._openedSnackBarRef&&this._openedSnackBarRef.dismiss()}ngOnDestroy(){this._snackBarRefAtThisLevel&&this._snackBarRefAtThisLevel.dismiss()}_attachSnackBarContainer(t,e){let n=e&&e.viewContainerRef&&e.viewContainerRef.injector,a=R.create({parent:n||this._injector,providers:[{provide:St,useValue:e}]}),s=new ct(this.snackBarContainerComponent,e.viewContainerRef,a),c=t.attach(s);return c.instance.snackBarConfig=e,c.instance}_attach(t,e){let n=f(f(f({},new St),this._defaultConfig),e),a=this._createOverlay(n),s=this._attachSnackBarContainer(a,n),c=new Wt(s,a);if(t instanceof yt){let d=new J(t,null,{$implicit:n.data,snackBarRef:c});c.instance=s.attachTemplatePortal(d)}else{let d=this._createInjector(n,c),u=new ct(t,void 0,d),l=s.attachComponentPortal(u);c.instance=l.instance}return this._breakpointObserver.observe(Eo.HandsetPortrait).pipe(Y(a.detachments())).subscribe(d=>{a.overlayElement.classList.toggle(this.handsetCssClass,d.matches)}),n.announcementMessage&&s._onAnnounce.subscribe(()=>{this._live.announce(n.announcementMessage,n.politeness)}),this._animateSnackBar(c,n),this._openedSnackBarRef=c,this._openedSnackBarRef}_animateSnackBar(t,e){t.afterDismissed().subscribe(()=>{this._openedSnackBarRef==t&&(this._openedSnackBarRef=null),e.announcementMessage&&this._live.clear()}),e.duration&&e.duration>0&&t.afterOpened().subscribe(()=>t._dismissAfter(e.duration)),this._openedSnackBarRef?(this._openedSnackBarRef.afterDismissed().subscribe(()=>{t.containerInstance.enter()}),this._openedSnackBarRef.dismiss()):t.containerInstance.enter()}_createOverlay(t){let e=new mt;e.direction=t.direction;let n=this._overlay.position().global(),a=t.direction==="rtl",s=t.horizontalPosition==="left"||t.horizontalPosition==="start"&&!a||t.horizontalPosition==="end"&&a,c=!s&&t.horizontalPosition!=="center";return s?n.left("0"):c?n.right("0"):n.centerHorizontally(),t.verticalPosition==="top"?n.top("0"):n.bottom("0"),e.positionStrategy=n,this._overlay.create(e)}_createInjector(t,e){let n=t&&t.viewContainerRef&&t.viewContainerRef.injector;return R.create({parent:n||this._injector,providers:[{provide:Wt,useValue:e},{provide:Vo,useValue:t.data}]})}static \u0275fac=function(e){return new(e||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var bd=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=k({type:o});static \u0275inj=x({providers:[xi],imports:[vi,Ut,ko,K,Uo,K]})}return o})();var Oe=class o{constructor(i){this.snackBar=i}logErrorResponse(i){if(i.status===0||typeof ErrorEvent<"u"&&i.error instanceof ErrorEvent)this.showError("\u062D\u062F\u062B \u062E\u0637\u0623 \u0641\u064A \u0627\u0644\u0627\u062A\u0635\u0627\u0644 \u0628\u0627\u0644\u062E\u0627\u062F\u0645. \u064A\u0631\u062C\u0649 \u0627\u0644\u062A\u062D\u0642\u0642 \u0645\u0646 \u0627\u062A\u0635\u0627\u0644 \u0627\u0644\u0625\u0646\u062A\u0631\u0646\u062A \u0627\u0644\u062E\u0627\u0635 \u0628\u0643.");else{let t=i.error?.message||i.message||"\u062E\u0637\u0623 \u063A\u064A\u0631 \u0645\u0639\u0631\u0648\u0641";switch(i.status){case 400:this.showError(t||"\u0637\u0644\u0628 \u063A\u064A\u0631 \u0635\u0627\u0644\u062D");break;case 401:this.showError(t||"\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644 \u0645\u0637\u0644\u0648\u0628");break;case 403:this.showError(t||"\u063A\u064A\u0631 \u0645\u0633\u0645\u0648\u062D \u0628\u0627\u0644\u0648\u0635\u0648\u0644 \u0644\u0647\u0630\u0627 ");break;case 404:this.showError(t||"\u0627\u0644\u0645\u0648\u0631\u062F \u063A\u064A\u0631 \u0645\u0648\u062C\u0648\u062F");break;case 500:this.showError(t||"\u062D\u062F\u062B \u062E\u0637\u0623 \u0641\u064A \u0627\u0644\u062E\u0627\u062F\u0645");break;default:this.showError(`\u062D\u062F\u062B \u062E\u0637\u0623: ${i.status}`);break}}return Xt(()=>new Error(`Error ${i.status}: ${i.message||"Unknown error"}`))}showError(i){this.snackBar.open(i,"Close",{duration:5e3,horizontalPosition:"center",verticalPosition:"top",panelClass:["error-snackbar"]})}static \u0275fac=function(t){return new(t||o)(B(xi))};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})};var Ie=class o{constructor(i){this.platformId=i}getApiUrl(){let i="http://*************:822/api";if(ae(this.platformId)){let t=window.location.hostname,e=window.location.port;if(t.startsWith("localhost")&&e=="4200")return i="http://localhost:5184/api";if(t.startsWith("192.168.")||t.startsWith("10.")||t.startsWith("172.")||t.startsWith("localhost"))return i=`http://${t}:${e}/api`;i=`http://${t}:${e}/api`}return i}static \u0275fac=function(t){return new(t||o)(B(Gt))};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})};var Me=class o{constructor(i,t,e){this.http=i;this.handleErrorService=t;this.apiConfigService=e;this.apiUrl=this.apiConfigService.getApiUrl()}apiUrl;get(i,t){return this.http.get(`${this.apiUrl}/${i}`,t).pipe(_t(2),bt(e=>this.handleErrorService.logErrorResponse(e)))}post(i,t){return this.http.post(`${this.apiUrl}/${i}`,t).pipe(_t(2),bt(e=>this.handleErrorService.logErrorResponse(e)))}put(i,t){return this.http.put(`${this.apiUrl}/${i}`,t).pipe(_t(2),bt(e=>this.handleErrorService.logErrorResponse(e)))}delete(i){return this.http.delete(`${this.apiUrl}/${i}`).pipe(_t(2),bt(t=>this.handleErrorService.logErrorResponse(t)))}patch(i,t){return this.http.patch(`${this.apiUrl}/${i}`,t).pipe(_t(2),bt(e=>this.handleErrorService.logErrorResponse(e)))}static \u0275fac=function(t){return new(t||o)(B(zi),B(Oe),B(Ie))};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})};var Ho=class o{constructor(i,t){this.apiService=i;this.router=t;if(typeof localStorage<"u"){let e=localStorage.getItem("token"),n=localStorage.getItem("refreshToken");e&&n&&(this.currentUserSubject.next(null),this.tokenSubject.next(e),this.refreshTokenSubject.next(n))}}currentUserSubject=new pt(null);tokenSubject=new pt(null);currentUser$=this.currentUserSubject.asObservable();token$=this.tokenSubject.asObservable();isRefreshing=!1;refreshTokenSubject=new pt(null);refreshedtoken$=this.refreshTokenSubject.asObservable();get currentUserValue(){return this.currentUserSubject.value}get tokenValue(){return this.tokenSubject.value}getRefreshToken(){return localStorage.getItem("refreshToken")}startRefresh(){this.isRefreshing=!0,this.refreshTokenSubject.next(null)}endRefresh(i){this.isRefreshing=!1,this.refreshTokenSubject.next(i)}get isLoggedIn(){return!!this.tokenValue}login(i){let t="Auth/login",e=i;return this.apiService.post(t,e).pipe(et(n=>{if(n.succeeded&&n.data){typeof localStorage<"u"&&(localStorage.setItem("token",n.data.accessToken),localStorage.setItem("tokenExpiry",n.data.tokenExpiry.toString()),localStorage.setItem("refreshToken",n.data.refreshToken.toString()));let s=this.parseJwt(n.data.accessToken).permission||[];this.getUserPermissions(s),this.getCurrentUser().subscribe(c=>{c.succeeded&&c.data&&this.currentUserSubject.next(c.data)}),this.tokenSubject.next(n.data.accessToken),this.refreshTokenSubject.next(n.data.refreshToken)}}))}getCurrentUser(){return this.apiService.get("User/profile").pipe(et(i=>{i.succeeded&&i.data&&this.currentUserSubject.next(i.data)}))}logout(){return this.apiService.post("Auth/logout",{}).pipe(et(()=>{}))}forceLogout(){this.clearLocalStorage(),this.router.navigate(["/auth/login"])}clearLocalStorage(){typeof localStorage<"u"&&(localStorage.removeItem("currentUser"),localStorage.removeItem("token"),localStorage.removeItem("tokenExpiry"),localStorage.removeItem("refreshToken")),this.currentUserSubject.next(null),this.tokenSubject.next(null),this.refreshTokenSubject.next(null)}isTokenExpired(){if(typeof localStorage>"u")return!0;let i=localStorage.getItem("tokenExpiry");if(!i)return!0;try{let t=new Date(i);return isNaN(t.getTime())?!0:t.getTime()<=new Date().getTime()}catch{return!0}}hasRole(i){let t=this.currentUserValue;return t?!!t.permissions.find(e=>e.userRole===i):!1}getUserPermissions(i){return i.map(t=>t)}viewUserPermissions(){return{permissions:this.parseJwt(this.tokenValue||"").permission||[]}}hasPermission(i){let t=this.viewUserPermissions().permissions;return t?!!t.find(e=>e===i):!1}refreshToken(){let i=this.getRefreshToken();if(!i)return this.clearLocalStorage(),this.router.navigate(["/auth/login"]),Xt(()=>new Error("No refresh token available"));let t={refreshToken:i};return this.apiService.post("Auth/refresh-token",t).pipe(et(e=>{e.succeeded&&e.statusCode===200?(typeof localStorage<"u"&&(localStorage.setItem("token",e.data.accessToken),localStorage.setItem("tokenExpiry",e.data.tokenExpiry.toString()),localStorage.setItem("refreshToken",e.data.refreshToken.toString())),this.tokenSubject.next(e.data.accessToken)):(this.clearLocalStorage(),this.router.navigate(["/auth/login"]))}))}parseJwt(i){let e=i.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),n=decodeURIComponent(atob(e).split("").map(a=>"%"+("00"+a.charCodeAt(0).toString(16)).slice(-2)).join(""));return JSON.parse(n)}static \u0275fac=function(t){return new(t||o)(B(Me),B(Vi))};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})};export{Ge as a,P as b,g as c,wt as d,Wi as e,Yi as f,W as g,se as h,Wo as i,F as j,le as k,to as l,Ko as m,ao as n,rn as o,ei as p,cn as q,Bt as r,ue as s,oi as t,dn as u,mn as v,jr as w,Kr as x,un as y,is as z,bs as A,yo as B,xn as C,st as D,Ks as E,K as F,wo as G,xo as H,ko as I,ct as J,J as K,lt as L,ye as M,Ut as N,Co as O,Dc as P,An as Q,Ic as R,we as S,In as T,xe as U,hi as V,fi as W,mt as X,Bo as Y,Re as Z,De as _,ut as $,gi as aa,Nn as ba,vi as ca,xi as da,bd as ea,Me as fa,Ho as ga};
