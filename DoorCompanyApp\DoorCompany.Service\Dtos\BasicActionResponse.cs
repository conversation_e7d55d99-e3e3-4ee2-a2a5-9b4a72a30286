﻿using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos.BasicDto;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos
{
    public class BasicActionResponse : BaseNameDto
    {
        public int? ParentActionId { get; set; }
        public int? ActionTypeId { get; set; }      
        public string? ActionType { get; set; }    
    }
}
