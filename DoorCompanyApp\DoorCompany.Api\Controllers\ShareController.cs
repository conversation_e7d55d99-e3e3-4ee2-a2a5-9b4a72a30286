﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos.ShareDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ShareController : AppControllerBase
    {
        public ShareController(IUnitOfWork work) : base(work)
        {
        }
        /// <summary>
        /// Get all Shares
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAllShares()
        {
            var response = await _work.ShareRepository.GetAllShareAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get Share by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetShareById(int id)
        {
            var response = await _work.ShareRepository.GetShareByIdAsync(id);
            return NewResult(response);
        }
        /// <summary>
        /// create Share
        /// </summary>
        /// <param name="createDto"></param>
        /// <returns></returns>
        [HttpPost()]
        public async Task<IActionResult> CreateShareAsycn(CreateShareDto createDto)
        {
            if (createDto == null)
            {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.ShareRepository.CreateShareAsycn(createDto);
            return NewResult(response);
        }

        /// <summary>
        /// Update Share
        /// </summary>
        /// <param name="Dto"></param>
        /// <returns></returns>
        [HttpPut()]
        public async Task<IActionResult> UpdateShareAsycn(UpdateShareDto Dto)
        {
            if (Dto == null)
            {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.ShareRepository.UpdateShareAsycn(Dto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete Share
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteShareAsync(int id)
        {
            var response = await _work.ShareRepository.DeleteShareAsync(id);
            return NewResult(response);
        }



    }
}
