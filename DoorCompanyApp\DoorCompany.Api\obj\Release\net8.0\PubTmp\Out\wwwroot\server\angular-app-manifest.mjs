
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {
  "src/app/features/auth/auth.module.ts": [
    {
      "path": "chunk-VEW7PVF5.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-HKHJ3A6U.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-BKOE74GJ.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/dashboard/dashboard.module.ts": [
    {
      "path": "chunk-N2CFN6CY.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/users/users.module.ts": [
    {
      "path": "chunk-YNMORVHL.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/profile/profile.module.ts": [
    {
      "path": "chunk-UMPFDFGF.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-ZPTOQNW7.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-BKOE74GJ.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/partner/partner.module.ts": [
    {
      "path": "chunk-3WJJNDKD.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-HKHJ3A6U.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-ZPTOQNW7.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-BKOE74GJ.js",
      "dynamicImport": false
    }
  ]
},
  assets: {
    'index.csr.html': {size: 51269, hash: '7482fcfd9e930e46085137dd90b99439de9374be639c05e72fb11ce18dea89a7', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 17655, hash: '3f315d0ad3d460efd3413253d2c54bd6b7a8714202577e74a395d1e439b9948f', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-7WIUEL3Y.css': {size: 37077, hash: '5JqMDt1NBEo', text: () => import('./assets-chunks/styles-7WIUEL3Y_css.mjs').then(m => m.default)}
  },
};
