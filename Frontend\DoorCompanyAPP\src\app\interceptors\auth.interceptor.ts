import { HttpInterceptorFn, HttpRequest, HttpHandlerFn, HttpEvent } from '@angular/common/http';
import { AuthService } from '../services/auth.service';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, switchMap, throwError, Observable } from 'rxjs';

export const authInterceptor: HttpInterceptorFn= (req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<HttpEvent<unknown>> => {
  const authService = inject(AuthService);
  const router = inject(Router);

  const token = authService.tokenValue;
  let authReq = req;
 
  if (token && !authService.isTokenExpired()) {
    authReq = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`
      }
    });
  }
//return next(authReq);
  // أرسل الطلب
  // if (!token || !authService.isTokenExpired()) {
  //   //   إذا لم يكن هناك توكن أو كان التوكن منتهي الصلاحية، قم بمعالجة الخطأ
  //   return next(authReq);
  // }

   return next(authReq).pipe(
    catchError((error: any) => {  
      if (error.status === 401 && error.error === null) {
         return handle401Error(authReq, next, authService, router);
      } else {
        return throwError(() => error); 
      }
    })
   );
};


// معالجة خطأ 401
function handle401Error(req: HttpRequest<unknown>, next: HttpHandlerFn, authService: AuthService, router: Router): Observable<HttpEvent<unknown>> {

  if (authService.isTokenExpired()) {
    authService.forceLogout();
    return throwError(() => new Error('Session invalid: Token rejected by server'));
  }

  const refreshToken = authService.getRefreshToken();
  if (!refreshToken) {
    authService.forceLogout();
    return throwError(() => new Error('No refresh token available'));
  }

  // إذا كان التحديث جارٍ بالفعل، انتظر حتى ينتهي
  if (authService.isRefreshing) {
    return authService.refreshTokenSubject.pipe(
      switchMap((newToken) => {
        if (newToken) {
          const newReq = req.clone({
            setHeaders: { Authorization: `Bearer ${newToken}` },
          });
          return next(newReq);
        } else {
          return throwError(() => new Error('Refresh token failed'));
        }
      }),
      catchError((err) => {
        authService.forceLogout();
        return throwError(() => err);
      })
    );
  }

  // بدء عملية التحديث
  authService.startRefresh();

  return authService.refreshToken().pipe(
    switchMap((response) => {
      if (response.succeeded && response.data?.accessToken) {
        const newToken = response.data.accessToken;

        // حفظ التوكن الجديد داخليًا
        authService.endRefresh(newToken);

        // إعادة إرسال الطلب الأصلي بـ التوكن الجديد
        const newReq = req.clone({
          setHeaders: { Authorization: `Bearer ${newToken}` },
        });

        return next(newReq);
      } else {
        // إذا لم ينجح التحديث (مثلاً: refresh token منتهٍ)
        authService.endRefresh(null);
        authService.forceLogout();
        return throwError(() => new Error('Token refresh failed'));
      }
    }),
    catchError((err) => {
      authService.endRefresh(null);
      authService.forceLogout();
      return throwError(() => err);
    })
  );
}


