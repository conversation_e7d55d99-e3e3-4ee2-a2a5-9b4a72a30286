import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger
} from "./chunk-EAHIU56T.js";
import "./chunk-6SY62HHQ.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-CH57JH4D.js";
import "./chunk-UOQTX635.js";
import "./chunk-3L5FUQUV.js";
import "./chunk-VOS4RXAJ.js";
import {
  MatError,
  Mat<PERSON><PERSON><PERSON>ield,
  <PERSON>Hint,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>refix,
  MatSuffix
} from "./chunk-GCZAWPPH.js";
import "./chunk-YDI5XXYD.js";
import "./chunk-6Z5ANHK5.js";
import "./chunk-JVNBSTIL.js";
import "./chunk-U7TDSWPI.js";
import "./chunk-AWW4JXEK.js";
import "./chunk-REX7EOCC.js";
import "./chunk-Z6VDCL5P.js";
import "./chunk-QYGT4RVM.js";
import "./chunk-NTOHQGJU.js";
import "./chunk-UMDVK2TP.js";
import "./chunk-TP3HGHP5.js";
import "./chunk-XW6SDNL2.js";
import "./chunk-JTNASOKH.js";
import "./chunk-SPDM6LSN.js";
import "./chunk-R5VTAUEX.js";
import "./chunk-N55ZFUUE.js";
import {
  require_operators
} from "./chunk-SBQGIAH2.js";
import {
  require_cjs
} from "./chunk-MKHVCWTC.js";
import {
  __toESM
} from "./chunk-YHCV7DAQ.js";

// ../../node_modules/@angular/material/fesm2022/select.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);
var matSelectAnimations = {
  // Represents
  // trigger('transformPanelWrap', [
  //   transition('* => void', query('@transformPanel', [animateChild()], {optional: true})),
  // ])
  /**
   * This animation ensures the select's overlay panel animation (transformPanel) is called when
   * closing the select.
   * This is needed due to https://github.com/angular/angular/issues/23302
   */
  transformPanelWrap: {
    type: 7,
    name: "transformPanelWrap",
    definitions: [{
      type: 1,
      expr: "* => void",
      animation: {
        type: 11,
        selector: "@transformPanel",
        animation: [{
          type: 9,
          options: null
        }],
        options: {
          optional: true
        }
      },
      options: null
    }],
    options: {}
  },
  // Represents
  // trigger('transformPanel', [
  //   state(
  //     'void',
  //     style({
  //       opacity: 0,
  //       transform: 'scale(1, 0.8)',
  //     }),
  //   ),
  //   transition(
  //     'void => showing',
  //     animate(
  //       '120ms cubic-bezier(0, 0, 0.2, 1)',
  //       style({
  //         opacity: 1,
  //         transform: 'scale(1, 1)',
  //       }),
  //     ),
  //   ),
  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),
  // ])
  /** This animation transforms the select's overlay panel on and off the page. */
  transformPanel: {
    type: 7,
    name: "transformPanel",
    definitions: [{
      type: 0,
      name: "void",
      styles: {
        type: 6,
        styles: {
          opacity: 0,
          transform: "scale(1, 0.8)"
        },
        offset: null
      }
    }, {
      type: 1,
      expr: "void => showing",
      animation: {
        type: 4,
        styles: {
          type: 6,
          styles: {
            opacity: 1,
            transform: "scale(1, 1)"
          },
          offset: null
        },
        timings: "120ms cubic-bezier(0, 0, 0.2, 1)"
      },
      options: null
    }, {
      type: 1,
      expr: "* => void",
      animation: {
        type: 4,
        styles: {
          type: 6,
          styles: {
            opacity: 0
          },
          offset: null
        },
        timings: "100ms linear"
      },
      options: null
    }],
    options: {}
  }
};
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
