﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Data.Models
{
    public class FinancialTransaction : BaseEntity
    {
        public DateTime TransactionDate { get; set; }
        public TransactionType TransactionType { get; set; } //ايراد//     منصرف//   تحويل   
        public AccountType AccountType { get; set; }
    
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        public int? ReferenceId { get; set; }
        public ReferenceType ReferenceType { get; set; } = ReferenceType.None;
        public string? Description { get; set; }
    }


    // Enum definitions for better type safety
    public enum TransactionType
    {
        [Description("إيراد")]
        Income,

        [Description("منصرف")]
        Expense,

        [Description("تحويل")]
        Transfer
    }

    public enum AccountType
    {
        [Description("نقدى")]
        Cash,

        [Description("بنك")]
        Bank,

        [Description("تحصيل")]
        Receivable,

        [Description("سداد")]
        Payable,

        [Description("استثمار")]
        Investment,

        [Description("استثمار")]
        Credit
    }

    public enum ReferenceType
    {
        [Description("بدون مرجع (للتحويلات الداخلية)")]
        None = 0,

        [Description("فاتورة بيع")]
        Sale = 1,

        [Description("فاتورة شراء")]
        Purchase = 2,

        [Description("راتب موظف")]
        Salary = 3,

        [Description("توزيع أرباح")]
        Dividend = 4,

        [Description("استثمار شريك")]
        Investment = 5,

        [Description("مصروف عام")]
        Expense = 6,

        [Description("استرداد مبلغ")]
        Refund = 7,

        [Description("تسوية مالية")]
        Adjustment = 8,

        [Description("قرض")]
        Loan = 9,

        [Description("سداد عام")]
        Payment = 10,

        [Description("سداد لمورد")]
        PaySupplier = 11,

        [Description("تحصيل من عميل")]
        ReceiveFromCustomer = 12
    }
}
