import { ChangeDetectorRef, Component, HostListener, NgZone, OnInit } from '@angular/core';
import { AuthService } from '../../services/auth.service';
import { User } from '../../models/user.model';
import { UserService } from '../../services/user.service';
import { Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
@Component({
  selector: 'app-layout',
   standalone: false,
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.css']
})
export class LayoutComponent implements OnInit {
  mobileMenuOpen = false;
  sidebarCollapsed = false;
  isMobile = false;
  userMenuOpen = false;
  currentUser: User | null = null;
  private avatarTimestamp: number = Date.now();
  private cachedAvatarUrl: string = '';

  // متغير لتتبع حالة قائمة الشركاء
  isPartnerMenuOpen = false;


  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private authService: AuthService,
    private userService: UserService,
    private cdr: ChangeDetectorRef,
    private zone: NgZone
  ) {
    this.checkScreenSize();
  }

  ngOnInit(): void {
  //  this.userService.currentUser$.subscribe(user => {
  //   this.currentUser = user;
  //   if (user) {
  //     this.avatarTimestamp = Date.now();
  //     this.cdr.detectChanges();
  //   }
  // });

 this.userService.currentUser$.subscribe(user => {
      const previousId = this.currentUser?.id;
      const currentId = user?.id;

      this.currentUser = user;

      // لا حاجة لتحديث متغير، getAvatarUrl() يُعيد رابطًا جديدًا تلقائيًا
      if (previousId !== currentId || this.currentUser?.profileImage) {
        // لا تفعل شيئًا، الـ getter سيتعامل مع التغيير
      }
    });



    
  //   this.userService.currentUser$.subscribe(user => {
     
  //     const previousUser = this.currentUser;
  //     this.currentUser = user;

  //     // فقط أعد تحميل الصورة إذا تغيرت البيانات
  //     if (!previousUser ||
  //         previousUser.id !== user?.id ||
  //         previousUser.profileImage !== user?.profileImage) {
  //       this.cachedAvatarUrl = ''; // امسح الكاش
  // //        this.zone.run(() => {
  // //     this.avatarTimestamp = Date.now();
  // //     this.cdr.detectChanges();
  // //   });
  // // }
  //       // this.avatarTimestamp = Date.now();
  //       // this.cdr.detectChanges();
  //       // الحل الآمن
  //   Promise.resolve().then(() => {
  //     this.avatarTimestamp = Date.now();
  //   });
  // }
  //   });





    // // فقط اطلب من API إذا لم يكن المستخدم محملًا
    // if (!this.userService.currentUserValue) {
    //   this.userService.getCurrentUser().subscribe({
    //     next: (response) => {
    //       if (response.statusCode === 200 && response.data) {
    //         this.currentUser = response.data;
    //       }
    //     },
    //     error: (error) => {
    //       console.error('Error loading user profile:', error);
    //     }
    //   });
    // }
  }

  @HostListener('window:resize')
  onResize(): void {
    this.checkScreenSize();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: any): void {
    if (!event.target.closest('.user-info') && !event.target.closest('.user-menu')) {
      this.userMenuOpen = false;
    }
  }

  private checkScreenSize() {
    if (isPlatformBrowser(this.platformId)) {
      this.isMobile = window.innerWidth < 768;
      if (!this.isMobile) {
        this.mobileMenuOpen = false;
      }
    }
  }

  // getAvatarUrl(): string {
  //   if (this.cachedAvatarUrl) return this.cachedAvatarUrl;

  //   if (this.currentUser?.profileImage) {
  //     this.cachedAvatarUrl = `${this.currentUser.profileImage}?v=${this.avatarTimestamp}`;
  //   } else {
  //     this.cachedAvatarUrl = '/assets/images/default-avatar.png';
  //   }

  //   return this.cachedAvatarUrl;
  // }

getAvatarUrl(): string {
    const base = this.currentUser?.profileImage;
    const timestamp = Date.now();
    if (base) {
      return `${base}?t=${timestamp}`;
    }
    return '/assets/images/default-avatar.png';
  }



  toggleSidebar(): void {
    if (this.isMobile) {
      this.mobileMenuOpen = !this.mobileMenuOpen;
    } else {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    }
  }

  closeMobileMenu(): void {
    this.mobileMenuOpen = false;
  }

  toggleUserMenu(): void {
    this.userMenuOpen = !this.userMenuOpen;
  }

  onNavItemClick(): void {
    if (this.isMobile) {
      this.mobileMenuOpen = false;
    }
  }

toggleMenu(menu: string) {
    switch (menu) {
      // case 'stores':
      //   this.isStoresMenuOpen = !this.isStoresMenuOpen;
      //   break;
      case 'partners':
        this.isPartnerMenuOpen = !this.isPartnerMenuOpen;
        break;
      // case 'suppliers':
      //   this.isSuppliersMenuOpen = !this.isSuppliersMenuOpen;
      //   break;
    }
  }

  logout(): void {
   this.userMenuOpen = false;  
   this.authService.forceLogout(); 
   this.userService.clearLocalStorage(); 
   this.authService.logout().subscribe();


  }
}