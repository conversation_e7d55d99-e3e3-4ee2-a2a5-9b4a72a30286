import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { ApiResponse } from '../models/api-response.model';
import { AuthService } from './auth.service';
import { BehaviorSubject, distinctUntilChanged, filter, Observable, tap } from 'rxjs';
import { ChangePasswordRequest, UpdateProfileRequest, User } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  private loaded = false; 

  constructor(private apiService: ApiService, private authService: AuthService) {
  
    // this.authService.token$.subscribe(token => {
    //   if (token && !this.loaded) {
    //     this.loadUser();
    //   }
    // });

    this.initializeUserFromStorage();

    // ✅ 2. استمع لتغيرات التوكن لتحميل المستخدم تلقائيًا
    this.authService.token$
      .pipe(
        filter(token => !!token && !this.authService.isTokenExpired()),
        distinctUntilChanged()
      )
      .subscribe(() => {
        this.loadUser();
      });




   }

   loadUser(): void {
    if (this.loaded) return;

    this.getCurrentUser().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.loaded = true;
        }
      },
      error: (err) => {
        console.error('Failed to load user', err);
        this.loaded = false;
      }
    });
  }

 getCurrentUser(): Observable<ApiResponse<User>> {
     return this.apiService.get<User>(`User/profile`)
        .pipe(
                tap(response => {
           if (response.succeeded && response.data) {
             if (typeof localStorage !== 'undefined') {
               localStorage.setItem('currentUser', JSON.stringify(response.data));
             }
             this.currentUserSubject.next(response.data);
           }
         })
       );
    }

 // ✅ تهيئة من localStorage
  private initializeUserFromStorage(): void {
    if (typeof localStorage === 'undefined') return;

    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        this.currentUserSubject.next(user);   
      } catch (e) {
        console.warn('Failed to parse currentUser from localStorage');
      }
    }
  }



get currentUserValue(): User | null {
  return this.currentUserSubject.value;
}
uploadProfileImage(file: File): Observable<ApiResponse<User>> {
    const formData = new FormData();
    formData.append('ProfileImage', file);
  
    return this.apiService.put<User>(`User/profile-image`, formData)
      .pipe(
        tap(response => {
          if (response.succeeded && response.data) {
            if (typeof localStorage !== 'undefined') {
              localStorage.setItem('currentUser', JSON.stringify(response.data));
            }
            this.currentUserSubject.next(response.data);
          }
        })
      );
  }


 updateProfile(profileData: UpdateProfileRequest): Observable<ApiResponse<User>> {

const formData = new FormData();
  formData.append('fullName', profileData.fullName);
  formData.append('phone', profileData.phone); 
  if (profileData.profileImage) {
    formData.append('profileImage', profileData.profileImage);
  }
    return this.apiService.put<User>(`User/profile`, formData)
      .pipe(
        tap(response => {
          if (response.succeeded && response.data) {
            if (typeof localStorage !== 'undefined') {
              localStorage.setItem('currentUser', JSON.stringify(response.data));
            }
            this.currentUserSubject.next(response.data);
          }
        })
      );
  }   

changePassword(changePasswordRequest: ChangePasswordRequest): Observable<ApiResponse<boolean>> {
    if (!changePasswordRequest.userId) {
      changePasswordRequest.userId = this.currentUserValue?.id || 0;
    } 
    return this.apiService.post<boolean>(`User/change-password`, changePasswordRequest)
      .pipe(
        tap(response => {
          if (response.succeeded) {
            // Optionally handle success, e.g., show a notification
          }
        })
      );
  }

   clearLocalStorage(): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('currentUser');   
    }
     this.currentUserSubject.next(null);
  }

getUserPermissions(): string[] {
    const user = this.currentUserValue;
    console.log('User Permissions:', user);
    return user ? user.permissions.map(p => p.userRole) : [];
  }


}