import{a as Te}from"./chunk-YMJA7MAW.js";import{a as ze}from"./chunk-PAYZX34A.js";import{a as Ve,b as Ue}from"./chunk-P4EX7ALU.js";import{a as Ne}from"./chunk-SEMAVOI3.js";import{a as Fe,c as Re,t as qe}from"./chunk-BZACXYOM.js";import{A as Pe,B as Ee,E as De,F as Z,ga as b,h as Oe,j as Ie,r as Le}from"./chunk-5FTQYYYZ.js";import{$a as l,Ab as A,Ba as g,Ca as M,Cb as C,Db as _e,Ga as h,Ja as E,Ka as m,M as $,Ma as w,Na as ae,Nb as ge,O as y,P as W,Pa as re,R as f,Sb as ve,Ta as i,Tb as be,Ua as a,Ub as we,V as Y,Va as _,W as u,X as p,Xb as ke,Y as O,Yb as Ce,Z as B,Za as k,Zb as ye,_a as oe,_b as xe,aa as J,ab as v,ac as Me,ba as G,bb as se,bc as Se,ca as K,cb as ce,dc as T,ea as ee,eb as de,ec as S,fb as le,fc as z,gb as me,gc as Ae,hc as Q,ja as te,jb as D,ka as ie,kb as s,lb as ue,pa as ne,qa as L,ra as P,sa as d,sb as pe,tb as he,wa as x,xb as fe}from"./chunk-KFKFGDWN.js";var Xe=()=>["Permissions.Partner.View"];function $e(t,o){t&1&&(i(0,"h2"),s(1,"\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0634\u0631\u0643\u0629"),a())}function We(t,o){t&1&&(i(0,"span",42),s(1,"\u0644\u0648\u062D\u0629 \u0627\u0644\u062A\u062D\u0643\u0645"),a())}function Ye(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u0634\u0631\u0643\u0627\u0621"),a())}function Je(t,o){if(t&1){let n=k();i(0,"div",47)(1,"a",48),l("click",function(){u(n);let r=v(2);return p(r.onNavItemClick())}),i(2,"span",10),s(3,"\u{1F4C4}"),a(),i(4,"span",42),s(5,"\u0627\u0644\u0634\u0631\u0643\u0627\u0621"),a()(),i(6,"a",49),l("click",function(){u(n);let r=v(2);return p(r.onNavItemClick())}),i(7,"span",10),s(8,"\u{1F4DD}"),a(),i(9,"span",42),s(10,"\u0645\u0639\u0627\u0645\u0644\u0627\u062A \u0627\u0644\u0634\u0631\u0643\u0627\u0621"),a()(),i(11,"a",50),l("click",function(){u(n);let r=v(2);return p(r.onNavItemClick())}),i(12,"span",10),O(),i(13,"svg",51),_(14,"path",52),a()(),B(),i(15,"span",42),s(16,"\u0645\u0639\u0627\u0645\u0644\u0627\u062A \u0627\u0644\u0627\u0633\u0647\u0645"),a()()()}}function Ke(t,o){if(t&1){let n=k();i(0,"div")(1,"a",43),l("click",function(){u(n);let r=v();return p(r.toggleMenu("partners"))}),i(2,"span",10),O(),i(3,"svg",44),_(4,"path",45),a()(),h(5,Ye,2,0,"span",11),B(),i(6,"span",39),s(7," \u25BC "),a()(),h(8,Je,17,0,"div",46),a()}if(t&2){let n=v();d(5),m("ngIf",!n.sidebarCollapsed||n.isMobile),d(),w("open",n.isPartnerMenuOpen),d(2),m("ngIf",n.isPartnerMenuOpen&&(!n.sidebarCollapsed||n.isMobile))}}function et(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u0639\u0645\u0644\u0627\u0621"),a())}function tt(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u0645\u0648\u0631\u062F\u064A\u0646"),a())}function it(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u0645\u0646\u062A\u062C\u0627\u062A"),a())}function nt(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u062A\u0635\u0646\u064A\u0641\u0627\u062A"),a())}function at(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u0648\u062D\u062F\u0627\u062A"),a())}function rt(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u0641\u0648\u0627\u062A\u064A\u0631"),a())}function ot(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u062D\u0633\u0627\u0628\u0627\u062A"),a())}function st(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u0645\u062E\u0627\u0632\u0646"),a())}function ct(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u062E\u0632\u0627\u0626\u0646"),a())}function dt(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u0642\u064A\u0648\u062F \u0627\u0644\u0645\u062D\u0627\u0633\u0628\u064A\u0629"),a())}function lt(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631"),a())}function mt(t,o){t&1&&(i(0,"h3",53),s(1,"\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0646\u0638\u0627\u0645"),a())}function ut(t,o){t&1&&(i(0,"span",42),s(1,"\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645\u064A\u0646"),a())}function pt(t,o){t&1&&(i(0,"span",42),s(1,"\u0627\u0644\u0623\u062F\u0648\u0627\u0631 \u0648\u0627\u0644\u0635\u0644\u0627\u062D\u064A\u0627\u062A"),a())}function ht(t,o){t&1&&(i(0,"span",42),s(1,"\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0646\u0638\u0627\u0645"),a())}function ft(t,o){if(t&1&&(i(0,"div",54),_(1,"img",55),a()),t&2){let n=v();d(),m("src",n.currentUser==null?null:n.currentUser.profileImage,ne)}}function _t(t,o){t&1&&(i(0,"span",54),s(1,"\u{1F464}"),a())}function gt(t,o){if(t&1){let n=k();i(0,"div",56)(1,"a",57),s(2,"\u0627\u0644\u0645\u0644\u0641 \u0627\u0644\u0634\u062E\u0635\u064A"),a(),i(3,"a",58),s(4,"\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A"),a(),_(5,"div",59),i(6,"a",60),l("click",function(){u(n);let r=v();return p(r.logout())}),s(7,"\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062E\u0631\u0648\u062C"),a()()}}var V=class t{constructor(o,n,e,r,c){this.platformId=o;this.authService=n;this.userService=e;this.cdr=r;this.zone=c;this.checkScreenSize()}mobileMenuOpen=!1;sidebarCollapsed=!1;isMobile=!1;userMenuOpen=!1;currentUser=null;avatarTimestamp=Date.now();cachedAvatarUrl="";isPartnerMenuOpen=!1;ngOnInit(){this.userService.currentUser$.subscribe(o=>{let n=this.currentUser?.id,e=o?.id;this.currentUser=o,n!==e||this.currentUser?.profileImage})}onResize(){this.checkScreenSize()}onDocumentClick(o){!o.target.closest(".user-info")&&!o.target.closest(".user-menu")&&(this.userMenuOpen=!1)}checkScreenSize(){ve(this.platformId)&&(this.isMobile=window.innerWidth<768,this.isMobile||(this.mobileMenuOpen=!1))}getAvatarUrl(){let o=this.currentUser?.profileImage,n=Date.now();return o?`${o}?t=${n}`:"/assets/images/default-avatar.png"}toggleSidebar(){this.isMobile?this.mobileMenuOpen=!this.mobileMenuOpen:this.sidebarCollapsed=!this.sidebarCollapsed}closeMobileMenu(){this.mobileMenuOpen=!1}toggleUserMenu(){this.userMenuOpen=!this.userMenuOpen}onNavItemClick(){this.isMobile&&(this.mobileMenuOpen=!1)}toggleMenu(o){switch(o){case"partners":this.isPartnerMenuOpen=!this.isPartnerMenuOpen;break}}logout(){this.userMenuOpen=!1,this.authService.forceLogout(),this.userService.clearLocalStorage(),this.authService.logout().subscribe()}static \u0275fac=function(n){return new(n||t)(x(te),x(b),x(ze),x(A),x(K))};static \u0275cmp=g({type:t,selectors:[["app-layout"]],hostBindings:function(n,e){n&1&&l("resize",function(){return e.onResize()},!1,L)("click",function(c){return e.onDocumentClick(c)},!1,P)},standalone:!1,decls:97,vars:29,consts:[["noAvatar",""],["dir","rtl",1,"layout-container"],[1,"mobile-overlay",3,"click"],[1,"sidebar"],[1,"sidebar-header"],[4,"ngIf"],[1,"toggle-btn",3,"click"],[1,"hamburger-icon"],[1,"nav-list"],["routerLink","/app/dashboard","routerLinkActive","active",1,"nav-item",3,"click"],[1,"nav-icon"],["class","nav-text",4,"ngIf"],[4,"appHasPermission"],["routerLink","/app/customers","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/suppliers","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/products","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/categories","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/units","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/invoices","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/accounts","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/warehouses","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/treasuries","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/journal-entries","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/reports","routerLinkActive","active",1,"nav-item",3,"click"],[1,"nav-divider"],[1,"admin-section"],["class","section-title",4,"ngIf"],["routerLink","/app/users","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/roles","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/settings","routerLinkActive","active",1,"nav-item",3,"click"],[1,"main-container"],[1,"top-bar"],[1,"top-bar-left"],[1,"menu-btn",3,"click"],[1,"page-title"],[1,"top-bar-right"],[1,"user-info",3,"click"],[1,"user-name"],["class","user-avatar",4,"ngIf","ngIfElse"],[1,"dropdown-arrow"],["class","user-menu",4,"ngIf"],[1,"page-content"],[1,"nav-text"],["href","javascript:void(0)",1,"nav-item","dropdown-toggle",3,"click"],["xmlns","http://www.w3.org/2000/svg","height","24px","viewBox","0 -960 960 960","width","24px","fill","#FBE6A3"],["d","M40-160v-160q0-34 23.5-57t56.5-23h131q20 0 38 10t29 27q29 39 71.5 61t90.5 22q49 0 91.5-22t70.5-61q13-17 30.5-27t36.5-10h131q34 0 57 23t23 57v160H640v-91q-35 25-75.5 38T480-200q-43 0-84-13.5T320-252v92H40Zm440-160q-38 0-72-17.5T351-386q-17-25-42.5-39.5T253-440q22-37 93-58.5T480-520q63 0 134 21.5t93 58.5q-29 0-55 14.5T609-386q-22 32-56 49t-73 17ZM160-440q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T280-560q0 50-34.5 85T160-440Zm640 0q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T920-560q0 50-34.5 85T800-440ZM480-560q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T600-680q0 50-34.5 85T480-560Z"],["class","dropdown-menu",4,"ngIf"],[1,"dropdown-menu"],["routerLink","/partners","routerLinkActiveOptions","{exact: true}",1,"nav-item","nested",3,"click"],["routerLink","/partners/transactions-list","routerLinkActiveOptions","{exact: true}",1,"nav-item","nested",3,"click"],["routerLink","/partners/share-list","routerLinkActive","active",1,"nav-item","nested",3,"click"],["xmlns","http://www.w3.org/2000/svg","height","24px","viewBox","0 -960 960 960","width","24px","fill","#255290"],["d","M480 0q-94 0-177.5-33.5t-148-93Q90-186 49-266.5T0-440h80q8 72 38.5 134.5t79 110.5Q246-147 309-117.5T444-82l-62-62 56-56L618-20Q584-9 549.5-4.5T480 0Zm120-200v-80H360q-33 0-56.5-23.5T280-360v-240h-80v-80h80v-80h80v400h400v80h-80v80h-80Zm0-240v-160H440v-80h160q33 0 56.5 23.5T680-600v160h-80Zm280-80q-7-72-38-134.5T762.5-765Q714-813 651-842.5T516-878l62 62-56 56-180-180q34-11 68.5-15.5T480-960q94 0 177.5 33.5t148 93Q870-774 911-693.5T960-520h-80Z"],[1,"section-title"],[1,"user-avatar"],["alt","User Avatar",1,"user-cover",3,"src"],[1,"user-menu"],["href","#","routerLink","/profile",1,"menu-item"],["href","#",1,"menu-item"],[1,"menu-divider"],["href","#",1,"menu-item","logout-button",3,"click"]],template:function(n,e){if(n&1){let r=k();i(0,"div",1)(1,"div",2),l("click",function(){return u(r),p(e.closeMobileMenu())}),a(),i(2,"div",3)(3,"div",4),h(4,$e,2,0,"h2",5),i(5,"button",6),l("click",function(){return u(r),p(e.toggleSidebar())}),i(6,"span",7),_(7,"span")(8,"span")(9,"span"),a()()(),i(10,"nav",8)(11,"a",9),l("click",function(){return u(r),p(e.onNavItemClick())}),i(12,"span",10),s(13,"\u{1F4CA}"),a(),h(14,We,2,0,"span",11),a(),h(15,Ke,9,4,"div",12),i(16,"a",13),l("click",function(){return u(r),p(e.onNavItemClick())}),i(17,"span",10),s(18,"\u{1F465}"),a(),h(19,et,2,0,"span",11),a(),i(20,"a",14),l("click",function(){return u(r),p(e.onNavItemClick())}),i(21,"span",10),s(22,"\u{1F3E2}"),a(),h(23,tt,2,0,"span",11),a(),i(24,"a",15),l("click",function(){return u(r),p(e.onNavItemClick())}),i(25,"span",10),s(26,"\u{1F4E6}"),a(),h(27,it,2,0,"span",11),a(),i(28,"a",16),l("click",function(){return u(r),p(e.onNavItemClick())}),i(29,"span",10),s(30,"\u{1F3F7}\uFE0F"),a(),h(31,nt,2,0,"span",11),a(),i(32,"a",17),l("click",function(){return u(r),p(e.onNavItemClick())}),i(33,"span",10),s(34,"\u{1F4CF}"),a(),h(35,at,2,0,"span",11),a(),i(36,"a",18),l("click",function(){return u(r),p(e.onNavItemClick())}),i(37,"span",10),s(38,"\u{1F4C4}"),a(),h(39,rt,2,0,"span",11),a(),i(40,"a",19),l("click",function(){return u(r),p(e.onNavItemClick())}),i(41,"span",10),s(42,"\u{1F4B0}"),a(),h(43,ot,2,0,"span",11),a(),i(44,"a",20),l("click",function(){return u(r),p(e.onNavItemClick())}),i(45,"span",10),s(46,"\u{1F3EA}"),a(),h(47,st,2,0,"span",11),a(),i(48,"a",21),l("click",function(){return u(r),p(e.onNavItemClick())}),i(49,"span",10),s(50,"\u{1F3E6}"),a(),h(51,ct,2,0,"span",11),a(),i(52,"a",22),l("click",function(){return u(r),p(e.onNavItemClick())}),i(53,"span",10),s(54,"\u{1F4DA}"),a(),h(55,dt,2,0,"span",11),a(),i(56,"a",23),l("click",function(){return u(r),p(e.onNavItemClick())}),i(57,"span",10),s(58,"\u{1F4C8}"),a(),h(59,lt,2,0,"span",11),a(),_(60,"div",24),i(61,"div",25),h(62,mt,2,0,"h3",26),i(63,"a",27),l("click",function(){return u(r),p(e.onNavItemClick())}),i(64,"span",10),s(65,"\u{1F464}"),a(),h(66,ut,2,0,"span",11),a(),i(67,"a",28),l("click",function(){return u(r),p(e.onNavItemClick())}),i(68,"span",10),s(69,"\u{1F510}"),a(),h(70,pt,2,0,"span",11),a(),i(71,"a",29),l("click",function(){return u(r),p(e.onNavItemClick())}),i(72,"span",10),s(73,"\u2699\uFE0F"),a(),h(74,ht,2,0,"span",11),a()()()(),i(75,"div",30)(76,"div",31)(77,"div",32)(78,"button",33),l("click",function(){return u(r),p(e.toggleSidebar())}),i(79,"span",7),_(80,"span")(81,"span")(82,"span"),a()(),i(83,"h1",34),s(84,"\u0646\u0638\u0627\u0645 \u0627\u062F\u0627\u0631\u0629 \u0627\u0644\u0627\u0628\u0648\u0627\u0628 \u0627\u0644\u0645\u0635\u0641\u062D\u0629"),a()(),i(85,"div",35)(86,"div",36),l("click",function(){return u(r),p(e.toggleUserMenu())}),i(87,"span",37),s(88),a(),h(89,ft,2,1,"div",38)(90,_t,2,0,"ng-template",null,0,fe),i(92,"span",39),s(93,"\u25BC"),a()(),h(94,gt,8,0,"div",40),a()(),i(95,"div",41),_(96,"router-outlet"),a()()()}if(n&2){let r=D(91);d(),w("active",e.mobileMenuOpen),d(),w("collapsed",e.sidebarCollapsed)("mobile-open",e.mobileMenuOpen),d(2),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(10),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(),m("appHasPermission",he(28,Xe)),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(3),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(4),m("ngIf",!e.sidebarCollapsed||e.isMobile),d(14),ue(e.currentUser==null?null:e.currentUser.fullName),d(),m("ngIf",e.currentUser==null?null:e.currentUser.profileImage)("ngIfElse",r),d(5),m("ngIf",e.userMenuOpen)}},dependencies:[ge,T,z,Ae,De,Ve],styles:[".layout-container[_ngcontent-%COMP%]{display:flex;height:100vh;direction:rtl;font-family:Cairo,Arial,sans-serif;position:relative}.mobile-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#00000080;z-index:99;opacity:0;visibility:hidden;transition:all .3s ease}.mobile-overlay.active[_ngcontent-%COMP%]{opacity:1;visibility:visible}.sidebar[_ngcontent-%COMP%]{width:280px;background:#1e293b;color:#fff;transition:all .3s ease;overflow-y:Scroll;position:relative;z-index:100;box-shadow:-2px 0 10px #0000001a}.sidebar.collapsed[_ngcontent-%COMP%]{width:70px}.sidebar-header[_ngcontent-%COMP%]{background:#0f172a;padding:16px;display:flex;align-items:center;justify-content:space-between;min-height:64px;box-sizing:border-box;border-bottom:1px solid #334155}.sidebar-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:18px;font-weight:600;white-space:nowrap;transition:opacity .3s ease}.toggle-btn[_ngcontent-%COMP%]{background:none;border:none;color:#fff;cursor:pointer;padding:8px;border-radius:6px;transition:all .3s ease;display:flex;align-items:center;justify-content:center}.toggle-btn[_ngcontent-%COMP%]:hover{background:#ffffff1a}.hamburger-icon[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:20px;height:16px;position:relative}.hamburger-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:block;height:2px;width:100%;background:#fff;border-radius:1px;transition:all .3s ease;transform-origin:center}.hamburger-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){margin-bottom:5px}.hamburger-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){margin-bottom:5px}.nav-list[_ngcontent-%COMP%]{padding:16px 0;overflow-y:auto;flex:1}.nav-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 20px;color:#cbd5e1;text-decoration:none;transition:all .3s ease;margin:2px 8px;border-radius:8px;position:relative;min-height:44px;box-sizing:border-box}.nav-item[_ngcontent-%COMP%]:hover{background:#334155;color:#fff;transform:translate(2px)}.nav-item.active[_ngcontent-%COMP%]{background:#3b82f6;color:#fff;box-shadow:0 2px 8px #3b82f64d}.nav-icon[_ngcontent-%COMP%]{font-size:20px;margin-left:12px;min-width:24px;text-align:center;transition:all .3s ease}.nav-text[_ngcontent-%COMP%]{white-space:nowrap;font-weight:700;transition:all .3s ease;font-family:cairo,Arial,sans-serif;font-size:20px}.sidebar.collapsed[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{padding:12px;justify-content:center;margin:2px 4px}.sidebar.collapsed[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{margin-left:0;font-size:22px}.nav-divider[_ngcontent-%COMP%]{height:1px;background:#334155;margin:16px 20px}.admin-section[_ngcontent-%COMP%]{margin-top:16px}.section-title[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#64748b;margin:16px 20px 8px;text-transform:uppercase;letter-spacing:.5px}.main-container[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;background:#f8fafc}.top-bar[_ngcontent-%COMP%]{background:#fff;padding:0 24px;display:flex;align-items:center;justify-content:space-between;min-height:64px;box-shadow:0 2px 4px #0000001a;z-index:10}.top-bar-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.menu-btn[_ngcontent-%COMP%]{background:green;border:none;font-size:20px;cursor:pointer;padding:8px;border-radius:4px;transition:background .3s ease}.menu-btn[_ngcontent-%COMP%]:hover{background:#f1f5f9}.page-title[_ngcontent-%COMP%]{margin:0;font-size:20px;font-weight:600;color:#1e293b}.top-bar-right[_ngcontent-%COMP%]{position:relative}.user-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px 16px;background:#f8fafc;border-radius:8px;cursor:pointer;transition:background .3s ease}.user-cover[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;border:2px solid #3b82f6}.user-info[_ngcontent-%COMP%]:hover{background:#e2e8f0}.user-name[_ngcontent-%COMP%]{font-weight:500;color:#1e293b}.user-avatar[_ngcontent-%COMP%]{font-size:20px}.dropdown-arrow[_ngcontent-%COMP%]{font-size:12px;color:#64748b}.user-menu[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;background:#fff;border:1px solid #e2e8f0;border-radius:8px;box-shadow:0 4px 12px #0000001a;min-width:200px;z-index:20}.menu-item[_ngcontent-%COMP%]{display:block;padding:12px 16px;color:#1e293b;text-decoration:none;transition:background .3s ease}.menu-item[_ngcontent-%COMP%]:hover{background:#f8fafc}.menu-divider[_ngcontent-%COMP%]{height:1px;background:#e2e8f0;margin:8px 0}.page-content[_ngcontent-%COMP%]{flex:1;overflow-y:auto}@media (max-width: 1024px){.sidebar[_ngcontent-%COMP%]{width:260px}.sidebar.collapsed[_ngcontent-%COMP%]{width:60px}}@media (max-width: 768px){.layout-container[_ngcontent-%COMP%]{overflow:hidden}.sidebar[_ngcontent-%COMP%]{position:fixed;z-index:100;height:100vh;width:280px!important;right:0;top:0;transform:translate(100%);box-shadow:-4px 0 20px #0000004d}.sidebar.mobile-open[_ngcontent-%COMP%]{transform:translate(0)}.sidebar.collapsed[_ngcontent-%COMP%]{width:280px!important;transform:translate(100%)}.sidebar.collapsed.mobile-open[_ngcontent-%COMP%]{transform:translate(0)}.main-container[_ngcontent-%COMP%]{width:100%;margin-right:0}.top-bar[_ngcontent-%COMP%]{padding:0 16px}.page-title[_ngcontent-%COMP%]{font-size:18px}.user-name[_ngcontent-%COMP%]{display:none}.nav-item[_ngcontent-%COMP%]{padding:14px 20px;margin:2px 8px}.nav-icon[_ngcontent-%COMP%]{font-size:22px;margin-left:16px}.nav-text[_ngcontent-%COMP%]{font-size:16px}.user-menu[_ngcontent-%COMP%]{right:0;margin-right:auto}}@media (max-width: 480px){.top-bar[_ngcontent-%COMP%]{padding:0 12px}.page-title[_ngcontent-%COMP%]{font-size:16px}.sidebar[_ngcontent-%COMP%]{width:260px!important}.nav-item[_ngcontent-%COMP%], .sidebar-header[_ngcontent-%COMP%]{padding:12px 16px}.sidebar-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px}}@media (max-width: 768px) and (orientation: portrait){.sidebar[_ngcontent-%COMP%]{width:300px!important}}@media (max-width: 1024px) and (orientation: landscape){.sidebar[_ngcontent-%COMP%]{width:240px}.sidebar.collapsed[_ngcontent-%COMP%]{width:60px}}.nav-item.dropdown-toggle[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;cursor:pointer}.dropdown-arrow[_ngcontent-%COMP%]{transition:transform .3s ease;font-size:.7em}.dropdown-arrow.open[_ngcontent-%COMP%]{transform:rotate(180deg)}.dropdown-menu[_ngcontent-%COMP%]{overflow:hidden;animation:_ngcontent-%COMP%_slideDown .2s ease}@keyframes _ngcontent-%COMP%_slideDown{0%{max-height:0;opacity:0;padding-top:0}to{max-height:200px;opacity:1;padding-top:5px}}.nav-item.nested[_ngcontent-%COMP%]{padding:8px 20px;font-size:.95em;color:#ee1b1b}.nav-item.nested[_ngcontent-%COMP%]:hover, .nav-item.nested.active[_ngcontent-%COMP%]{background-color:#1e88e5;color:#7aeca0}"]})};var Be=(t,o)=>{let n=f(b),e=f(S);return n.tokenValue&&!n.isTokenExpired()?(e.navigate(["/"]),!1):!0};var F=(t,o)=>{let n=f(b),e=f(S);return n.tokenValue&&!n.isTokenExpired()?!0:(e.navigate(["/auth/login"]),!1)};var R=class t{static \u0275fac=function(n){return new(n||t)};static \u0275cmp=g({type:t,selectors:[["app-unauthorized"]],standalone:!1,decls:7,vars:0,consts:[[1,"unauthorized-container"],["routerLink","/dashboard",1,"btn","btn-primary"]],template:function(n,e){n&1&&(i(0,"div",0)(1,"h1"),s(2,"\u063A\u064A\u0631 \u0645\u0635\u0631\u062D"),a(),i(3,"p"),s(4,"\u0644\u064A\u0633 \u0644\u062F\u064A\u0643 \u0627\u0644\u0635\u0644\u0627\u062D\u064A\u0629 \u0644\u0644\u0648\u0635\u0648\u0644 \u0625\u0644\u0649 \u0647\u0630\u0647 \u0627\u0644\u0635\u0641\u062D\u0629."),a(),i(5,"a",1),s(6,"\u0627\u0644\u0639\u0648\u062F\u0629 \u0625\u0644\u0649 \u0627\u0644\u0635\u0641\u062D\u0629 \u0627\u0644\u0631\u0626\u064A\u0633\u064A\u0629"),a()())},dependencies:[z],styles:[".unauthorized-container[_ngcontent-%COMP%]{text-align:center;margin-top:100px}.unauthorized-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:48px;color:#d9534f}.unauthorized-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:18px;margin:20px 0}.btn[_ngcontent-%COMP%]{padding:10px 20px;text-decoration:none;color:#fff;background-color:#0275d8;border-radius:5px}"]})};var Ge=(t,o)=>{let n=f(b),e=f(S),r=t.data.permissions;if(!r)return!0;let c=n.viewUserPermissions().permissions;return r.some(H=>c.includes(H))?!0:(e.navigate(["/unauthorized"]),!1)};var vt=[{path:"auth",canActivate:[Be],loadChildren:()=>import("./chunk-VEW7PVF5.js").then(t=>t.AuthModule)},{path:"",component:V,children:[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"dashboard",canActivate:[F],loadChildren:()=>import("./chunk-N2CFN6CY.js").then(t=>t.DashboardModule)},{path:"users",canActivate:[F],loadChildren:()=>import("./chunk-YNMORVHL.js").then(t=>t.UsersModule)},{path:"profile",loadChildren:()=>import("./chunk-UMPFDFGF.js").then(t=>t.ProfileModule)},{path:"partners",canActivate:[F,Ge],data:{permissions:["Permissions.Partner.View"]},loadChildren:()=>import("./chunk-3WJJNDKD.js").then(t=>t.PartnerModule)}]},{path:"unauthorized",component:R},{path:"**",redirectTo:"auth/login"}],q=class t{static \u0275fac=function(n){return new(n||t)};static \u0275mod=M({type:t});static \u0275inj=y({imports:[Q.forRoot(vt),Q]})};var N=class t{title="DoorCompanyAPP";mobileMenuOpen=!1;sidebarCollapsed=!1;isMobile=!1;userMenuOpen=!1;constructor(){}onResize(){this.checkScreenSize()}onDocumentClick(o){!o.target.closest(".user-info")&&!o.target.closest(".user-menu")&&(this.userMenuOpen=!1)}checkScreenSize(){this.isMobile=window.innerWidth<=768,this.isMobile||(this.mobileMenuOpen=!1)}toggleSidebar(){this.isMobile?this.mobileMenuOpen=!this.mobileMenuOpen:this.sidebarCollapsed=!this.sidebarCollapsed}closeMobileMenu(){this.mobileMenuOpen=!1}toggleUserMenu(){this.userMenuOpen=!this.userMenuOpen}onNavItemClick(){this.isMobile&&(this.mobileMenuOpen=!1)}static \u0275fac=function(n){return new(n||t)};static \u0275cmp=g({type:t,selectors:[["app-root"]],hostBindings:function(n,e){n&1&&l("resize",function(){return e.onResize()},!1,L)("click",function(c){return e.onDocumentClick(c)},!1,P)},standalone:!1,decls:1,vars:0,template:function(n,e){n&1&&_(0,"router-outlet")},dependencies:[T],encapsulation:2})};var bt=["switch"],wt=["*"];function kt(t,o){t&1&&(i(0,"span",10),O(),i(1,"svg",12),_(2,"path",13),a(),i(3,"svg",14),_(4,"path",15),a()())}var Ct=new W("mat-slide-toggle-default-options",{providedIn:"root",factory:()=>({disableToggleValue:!1,hideIcon:!1,disabledInteractive:!1})}),yt={provide:Fe,useExisting:$(()=>Ze),multi:!0},U=class{source;checked;constructor(o,n){this.source=o,this.checked=n}},Ze=(()=>{class t{_elementRef=f(ee);_focusMonitor=f(Oe);_changeDetectorRef=f(A);defaults=f(Ct);_onChange=n=>{};_onTouched=()=>{};_validatorOnChange=()=>{};_uniqueId;_checked=!1;_createChangeEvent(n){return new U(this,n)}_labelId;get buttonId(){return`${this.id||this._uniqueId}-button`}_switchElement;focus(){this._switchElement.nativeElement.focus()}_noopAnimations;_focused;name=null;id;labelPosition="after";ariaLabel=null;ariaLabelledby=null;ariaDescribedby;required;color;disabled=!1;disableRipple=!1;tabIndex=0;get checked(){return this._checked}set checked(n){this._checked=n,this._changeDetectorRef.markForCheck()}hideIcon;disabledInteractive;change=new G;toggleChange=new G;get inputId(){return`${this.id||this._uniqueId}-input`}constructor(){f(Ie).load(Ee);let n=f(new J("tabindex"),{optional:!0}),e=this.defaults,r=f(ie,{optional:!0});this.tabIndex=n==null?0:parseInt(n)||0,this.color=e.color||"accent",this._noopAnimations=r==="NoopAnimations",this.id=this._uniqueId=f(Le).getId("mat-mdc-slide-toggle-"),this.hideIcon=e.hideIcon??!1,this.disabledInteractive=e.disabledInteractive??!1,this._labelId=this._uniqueId+"-label"}ngAfterContentInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(n=>{n==="keyboard"||n==="program"?(this._focused=!0,this._changeDetectorRef.markForCheck()):n||Promise.resolve().then(()=>{this._focused=!1,this._onTouched(),this._changeDetectorRef.markForCheck()})})}ngOnChanges(n){n.required&&this._validatorOnChange()}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef)}writeValue(n){this.checked=!!n}registerOnChange(n){this._onChange=n}registerOnTouched(n){this._onTouched=n}validate(n){return this.required&&n.value!==!0?{required:!0}:null}registerOnValidatorChange(n){this._validatorOnChange=n}setDisabledState(n){this.disabled=n,this._changeDetectorRef.markForCheck()}toggle(){this.checked=!this.checked,this._onChange(this.checked)}_emitChangeEvent(){this._onChange(this.checked),this.change.emit(this._createChangeEvent(this.checked))}_handleClick(){this.disabled||(this.toggleChange.emit(),this.defaults.disableToggleValue||(this.checked=!this.checked,this._onChange(this.checked),this.change.emit(new U(this,this.checked))))}_getAriaLabelledBy(){return this.ariaLabelledby?this.ariaLabelledby:this.ariaLabel?null:this._labelId}static \u0275fac=function(e){return new(e||t)};static \u0275cmp=g({type:t,selectors:[["mat-slide-toggle"]],viewQuery:function(e,r){if(e&1&&de(bt,5),e&2){let c;le(c=me())&&(r._switchElement=c.first)}},hostAttrs:[1,"mat-mdc-slide-toggle"],hostVars:13,hostBindings:function(e,r){e&2&&(oe("id",r.id),E("tabindex",null)("aria-label",null)("name",null)("aria-labelledby",null),ae(r.color?"mat-"+r.color:""),w("mat-mdc-slide-toggle-focused",r._focused)("mat-mdc-slide-toggle-checked",r.checked)("_mat-animation-noopable",r._noopAnimations))},inputs:{name:"name",id:"id",labelPosition:"labelPosition",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],required:[2,"required","required",C],color:"color",disabled:[2,"disabled","disabled",C],disableRipple:[2,"disableRipple","disableRipple",C],tabIndex:[2,"tabIndex","tabIndex",n=>n==null?0:_e(n)],checked:[2,"checked","checked",C],hideIcon:[2,"hideIcon","hideIcon",C],disabledInteractive:[2,"disabledInteractive","disabledInteractive",C]},outputs:{change:"change",toggleChange:"toggleChange"},exportAs:["matSlideToggle"],features:[pe([yt,{provide:Re,useExisting:t,multi:!0}]),Y],ngContentSelectors:wt,decls:13,vars:27,consts:[["switch",""],["mat-internal-form-field","",3,"labelPosition"],["role","switch","type","button",1,"mdc-switch",3,"click","tabIndex","disabled"],[1,"mdc-switch__track"],[1,"mdc-switch__handle-track"],[1,"mdc-switch__handle"],[1,"mdc-switch__shadow"],[1,"mdc-elevation-overlay"],[1,"mdc-switch__ripple"],["mat-ripple","",1,"mat-mdc-slide-toggle-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mdc-switch__icons"],[1,"mdc-label",3,"click","for"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--on"],["d","M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--off"],["d","M20 13H4v-2h16v2z"]],template:function(e,r){if(e&1){let c=k();se(),i(0,"div",1)(1,"button",2,0),l("click",function(){return u(c),p(r._handleClick())}),_(3,"span",3),i(4,"span",4)(5,"span",5)(6,"span",6),_(7,"span",7),a(),i(8,"span",8),_(9,"span",9),a(),h(10,kt,5,0,"span",10),a()()(),i(11,"label",11),l("click",function(H){return u(c),p(H.stopPropagation())}),ce(12),a()()}if(e&2){let c=D(2);m("labelPosition",r.labelPosition),d(),w("mdc-switch--selected",r.checked)("mdc-switch--unselected",!r.checked)("mdc-switch--checked",r.checked)("mdc-switch--disabled",r.disabled)("mat-mdc-slide-toggle-disabled-interactive",r.disabledInteractive),m("tabIndex",r.disabled&&!r.disabledInteractive?-1:r.tabIndex)("disabled",r.disabled&&!r.disabledInteractive),E("id",r.buttonId)("name",r.name)("aria-label",r.ariaLabel)("aria-labelledby",r._getAriaLabelledBy())("aria-describedby",r.ariaDescribedby)("aria-required",r.required||null)("aria-checked",r.checked)("aria-disabled",r.disabled&&r.disabledInteractive?"true":null),d(8),m("matRippleTrigger",c)("matRippleDisabled",r.disableRipple||r.disabled)("matRippleCentered",!0),d(),re(r.hideIcon?-1:10),d(),m("for",r.buttonId),E("id",r._labelId)}},dependencies:[Pe,Ne],styles:[`.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative;width:var(--mdc-switch-track-width, 52px)}.mdc-switch.mdc-switch--disabled{cursor:default;pointer-events:none}.mdc-switch.mat-mdc-slide-toggle-disabled-interactive{pointer-events:auto}.mdc-switch__track{overflow:hidden;position:relative;width:100%;height:var(--mdc-switch-track-height, 32px);border-radius:var(--mdc-switch-track-shape, var(--mat-sys-corner-full))}.mdc-switch--disabled.mdc-switch .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";height:100%;left:0;position:absolute;width:100%;border-width:var(--mat-switch-track-outline-width, 2px);border-color:var(--mat-switch-track-outline-color, var(--mat-sys-outline))}.mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track::after{border-width:var(--mat-switch-selected-track-outline-width, 2px);border-color:var(--mat-switch-selected-track-outline-color, transparent)}.mdc-switch--disabled .mdc-switch__track::before,.mdc-switch--disabled .mdc-switch__track::after{border-width:var(--mat-switch-disabled-unselected-track-outline-width, 2px);border-color:var(--mat-switch-disabled-unselected-track-outline-color, var(--mat-sys-on-surface))}@media(forced-colors: active){.mdc-switch__track{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0);background:var(--mdc-switch-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch--selected .mdc-switch__track::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, var(--mat-sys-surface-variant))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::before,.mdc-switch.mdc-switch--disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch__track::after{transform:translateX(-100%);background:var(--mdc-switch-selected-track-color, var(--mat-sys-primary))}[dir=rtl] .mdc-switch__track::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::after{transform:translateX(0)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, var(--mat-sys-primary))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::after,.mdc-switch.mdc-switch--disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, var(--mat-sys-on-surface))}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0);width:calc(100% - var(--mdc-switch-handle-width))}[dir=rtl] .mdc-switch__handle-track{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto;transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1);width:var(--mdc-switch-handle-width);height:var(--mdc-switch-handle-height);border-radius:var(--mdc-switch-handle-shape, var(--mat-sys-corner-full))}[dir=rtl] .mdc-switch__handle{left:auto;right:0}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size, 16px);height:var(--mat-switch-unselected-handle-size, 16px);margin:var(--mat-switch-unselected-handle-horizontal-margin, 0 8px)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin, 0 4px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size, 24px);height:var(--mat-switch-selected-handle-size, 24px);margin:var(--mat-switch-selected-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size, 24px);height:var(--mat-switch-with-icon-handle-size, 24px)}.mat-mdc-slide-toggle .mdc-switch:active:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size, 28px);height:var(--mat-switch-pressed-handle-size, 28px)}.mat-mdc-slide-toggle .mdc-switch--selected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin, 0 22px)}.mat-mdc-slide-toggle .mdc-switch--unselected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin, 0 2px)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity, 1)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity, 0.38)}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media(forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mat-sys-on-primary))}.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, var(--mat-sys-primary-container))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:hover:not(:focus):not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:focus:not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:active .mdc-switch__handle::after,.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, var(--mat-sys-surface))}.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, var(--mat-sys-outline))}.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, var(--mat-sys-on-surface))}.mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__shadow,.mdc-switch.mdc-switch--disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1;width:var(--mdc-switch-state-layer-size, 40px);height:var(--mdc-switch-state-layer-size, 40px)}.mdc-switch__ripple::after{content:"";opacity:0}.mdc-switch--disabled .mdc-switch__ripple::after{display:none}.mat-mdc-slide-toggle-disabled-interactive .mdc-switch__ripple::after{display:block}.mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:75ms opacity cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:focus .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:active .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:hover:not(:focus) .mdc-switch__ripple::after,.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-pressed-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-selected-pressed-state-layer-color, var(--mat-sys-primary));opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch__icons{position:relative;height:100%;width:100%;z-index:1;transform:translateZ(0)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 16px);height:var(--mdc-switch-unselected-icon-size, 16px);fill:var(--mdc-switch-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 16px);height:var(--mdc-switch-selected-icon-size, 16px);fill:var(--mdc-switch-selected-icon-color, var(--mat-sys-on-primary-container))}.mdc-switch--selected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mat-sys-on-surface))}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{-webkit-user-select:none;user-select:none;display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-focus-indicator::before{content:""}.mat-mdc-slide-toggle .mat-internal-form-field{color:var(--mat-switch-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-switch-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-switch-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-switch-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-switch-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-switch-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}
`],encapsulation:2,changeDetection:0})}return t})();var Qe=(()=>{class t{static \u0275fac=function(e){return new(e||t)};static \u0275mod=M({type:t});static \u0275inj=y({imports:[Ze,Z,Z]})}return t})();var j=class t{static \u0275fac=function(n){return new(n||t)};static \u0275mod=M({type:t,bootstrap:[N]});static \u0275inj=y({providers:[Se(Me()),ke(ye(),Ce([Te]))],imports:[we,q,Qe,xe,qe,Ue]})};be().bootstrapModule(j,{ngZoneEventCoalescing:!0}).catch(t=>console.error(t));
