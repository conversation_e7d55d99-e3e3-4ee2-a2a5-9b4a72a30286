2025-08-09 16:45:52.710 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-09 16:45:52.756 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-08-09 16:45:52.835 +03:00 [INF] Executed DbCommand (76ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-08-09 16:45:52.938 +03:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-08-09 16:45:52.948 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-09 16:45:52.950 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-09 16:45:52.955 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-09 16:45:52.965 +03:00 [INF] Applying migration '20250809134527_addProductStore'.
2025-08-09 16:45:53.114 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ItemCategories] (
    [Id] int NOT NULL IDENTITY,
    [ParentCategoryId] int NULL,
    [Code] nvarchar(max) NULL,
    [Symbol] nvarchar(max) NULL,
    [CategoryTypeId] int NULL,
    [ImageUrl] nvarchar(max) NULL,
    [SortOrder] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_ItemCategories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ItemCategories_ItemCategories_ParentCategoryId] FOREIGN KEY ([ParentCategoryId]) REFERENCES [ItemCategories] ([Id])
);
2025-08-09 16:45:53.117 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Parties] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NULL,
    [Email] nvarchar(max) NULL,
    [Address] nvarchar(max) NULL,
    [TaxNumber] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Parties] PRIMARY KEY ([Id])
);
2025-08-09 16:45:53.120 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Units] (
    [Id] int NOT NULL IDENTITY,
    [Symbol] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Units] PRIMARY KEY ([Id])
);
2025-08-09 16:45:53.125 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Customers] (
    [Id] int NOT NULL IDENTITY,
    [PartyId] int NOT NULL,
    [CreditLimit] decimal(18,2) NOT NULL,
    [SalesRepresentative] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Customers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Customers_Parties_PartyId] FOREIGN KEY ([PartyId]) REFERENCES [Parties] ([Id]) ON DELETE CASCADE
);
2025-08-09 16:45:53.129 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InvoiceMasters] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceNumber] nvarchar(max) NOT NULL,
    [InvoiceType] int NOT NULL,
    [InvoiceDate] datetime2 NOT NULL,
    [PartyId] int NOT NULL,
    [SubTotal] decimal(18,2) NOT NULL,
    [ItemDiscountAmount] decimal(18,2) NOT NULL,
    [InvoiceDiscountAmount] decimal(18,2) NOT NULL,
    [TaxAmount] decimal(18,2) NOT NULL,
    [TotalAmount] decimal(18,2) NOT NULL,
    [PaidAmount] decimal(18,2) NOT NULL,
    [RemainingAmount] decimal(18,2) NOT NULL,
    [PaymentType] int NOT NULL,
    [IsPaid] bit NOT NULL,
    [Notes] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_InvoiceMasters] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InvoiceMasters_Parties_PartyId] FOREIGN KEY ([PartyId]) REFERENCES [Parties] ([Id]) ON DELETE CASCADE
);
2025-08-09 16:45:53.423 +03:00 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ProductionUnits] (
    [Id] int NOT NULL IDENTITY,
    [PartyId] int NOT NULL,
    [ManagerName] nvarchar(max) NULL,
    [CapacityUnitsPerDay] int NULL,
    [ProductionType] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ProductionUnits] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ProductionUnits_Parties_PartyId] FOREIGN KEY ([PartyId]) REFERENCES [Parties] ([Id]) ON DELETE CASCADE
);
2025-08-09 16:45:53.436 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Suppliers] (
    [Id] int NOT NULL IDENTITY,
    [PartyId] int NOT NULL,
    [BankAccount] nvarchar(max) NULL,
    [Website] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Suppliers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Suppliers_Parties_PartyId] FOREIGN KEY ([PartyId]) REFERENCES [Parties] ([Id]) ON DELETE CASCADE
);
2025-08-09 16:45:53.459 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Products] (
    [Id] int NOT NULL IDENTITY,
    [Code] nvarchar(max) NOT NULL,
    [Barcode] nvarchar(max) NOT NULL,
    [CategoryId] int NOT NULL,
    [UnitId] int NOT NULL,
    [StandardCost] decimal(18,2) NOT NULL,
    [MinimumStock] decimal(18,2) NULL,
    [MaximumStock] decimal(18,2) NULL,
    [SortOrder] int NULL,
    [ItemType] int NULL,
    [ImagePath] nvarchar(max) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Products] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Products_ItemCategories_CategoryId] FOREIGN KEY ([CategoryId]) REFERENCES [ItemCategories] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_Products_Units_UnitId] FOREIGN KEY ([UnitId]) REFERENCES [Units] ([Id]) ON DELETE CASCADE
);
2025-08-09 16:45:53.464 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InventoryTransactions] (
    [Id] int NOT NULL IDENTITY,
    [Type] int NOT NULL,
    [TransactionDate] datetime2 NOT NULL,
    [ProductId] int NOT NULL,
    [Quantity] decimal(18,2) NOT NULL,
    [UnitCost] decimal(18,2) NOT NULL,
    [TotalCost] decimal(18,2) NOT NULL,
    [BalanceAfter] decimal(18,2) NOT NULL,
    [AverageCost] decimal(18,2) NOT NULL,
    [ReferenceNumber] nvarchar(max) NULL,
    [Description] nvarchar(max) NULL,
    [InvoiceMasterId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_InventoryTransactions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InventoryTransactions_InvoiceMasters_InvoiceMasterId] FOREIGN KEY ([InvoiceMasterId]) REFERENCES [InvoiceMasters] ([Id]),
    CONSTRAINT [FK_InventoryTransactions_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);
2025-08-09 16:45:53.468 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InvoiceItems] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceMasterId] int NOT NULL,
    [ProductId] int NOT NULL,
    [Quantity] decimal(18,2) NOT NULL,
    [UnitPrice] decimal(18,2) NOT NULL,
    [DiscountPercentage] decimal(18,2) NOT NULL,
    [DiscountAmount] decimal(18,2) NOT NULL,
    [TotalPrice] decimal(18,2) NOT NULL,
    [Notes] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_InvoiceItems] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InvoiceItems_InvoiceMasters_InvoiceMasterId] FOREIGN KEY ([InvoiceMasterId]) REFERENCES [InvoiceMasters] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_InvoiceItems_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);
2025-08-09 16:45:56.417 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Customers_PartyId] ON [Customers] ([PartyId]);
2025-08-09 16:45:56.420 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryTransactions_InvoiceMasterId] ON [InventoryTransactions] ([InvoiceMasterId]);
2025-08-09 16:45:56.422 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryTransactions_ProductId] ON [InventoryTransactions] ([ProductId]);
2025-08-09 16:45:56.424 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceItems_InvoiceMasterId] ON [InvoiceItems] ([InvoiceMasterId]);
2025-08-09 16:45:56.425 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceItems_ProductId] ON [InvoiceItems] ([ProductId]);
2025-08-09 16:45:56.427 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceMasters_PartyId] ON [InvoiceMasters] ([PartyId]);
2025-08-09 16:45:56.429 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ItemCategories_ParentCategoryId] ON [ItemCategories] ([ParentCategoryId]);
2025-08-09 16:45:56.430 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_ProductionUnits_PartyId] ON [ProductionUnits] ([PartyId]);
2025-08-09 16:45:56.432 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Products_CategoryId] ON [Products] ([CategoryId]);
2025-08-09 16:45:56.433 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Products_UnitId] ON [Products] ([UnitId]);
2025-08-09 16:45:56.435 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Suppliers_PartyId] ON [Suppliers] ([PartyId]);
2025-08-09 16:45:56.438 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250809134527_addProductStore', N'9.0.7');
2025-08-09 16:45:56.485 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-08-09 17:09:59.356 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-09 17:09:59.518 +03:00 [INF] Executed DbCommand (95ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-09 17:09:59.850 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:09:59.876 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:10:00.102 +03:00 [INF] Executed DbCommand (87ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-09 17:10:00.205 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-09 17:10:00.275 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-09 17:10:00.289 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:10:00.299 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:10:00.325 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:10:00.337 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:10:00.347 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:10:00.359 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:10:00.625 +03:00 [ERR] Failed executing DbCommand (63ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Int32), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = DateTime2), @p12='?' (DbType = Int32), @p13='?' (Size = 4000), @p14='?' (DbType = Boolean), @p15='?' (DbType = Boolean), @p16='?' (Size = 4000), @p17='?' (Size = 4000), @p18='?' (DbType = DateTime2), @p19='?' (DbType = Int32), @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime2), @p22='?' (DbType = Int32), @p23='?' (Size = 4000), @p24='?' (DbType = Boolean), @p25='?' (DbType = Boolean), @p26='?' (Size = 4000), @p27='?' (Size = 4000), @p28='?' (DbType = DateTime2), @p29='?' (DbType = Int32), @p30='?' (DbType = Int32), @p31='?' (DbType = DateTime2), @p32='?' (DbType = Int32), @p33='?' (Size = 4000), @p34='?' (DbType = Boolean), @p35='?' (DbType = Boolean), @p36='?' (Size = 4000), @p37='?' (Size = 4000), @p38='?' (DbType = DateTime2), @p39='?' (DbType = Int32), @p40='?' (DbType = Int32), @p41='?' (DbType = DateTime2), @p42='?' (DbType = Int32), @p43='?' (Size = 4000), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?' (Size = 4000), @p47='?' (Size = 4000), @p48='?' (DbType = DateTime2), @p49='?' (DbType = Int32), @p50='?' (DbType = Int32), @p51='?' (DbType = DateTime2), @p52='?' (DbType = Int32), @p53='?' (Size = 4000), @p54='?' (DbType = Boolean), @p55='?' (DbType = Boolean), @p56='?' (Size = 4000), @p57='?' (Size = 4000), @p58='?' (DbType = DateTime2), @p59='?' (DbType = Int32), @p60='?' (DbType = Int32), @p61='?' (DbType = DateTime2), @p62='?' (DbType = Int32), @p63='?' (Size = 4000), @p64='?' (DbType = Boolean), @p65='?' (DbType = Boolean), @p66='?' (Size = 4000), @p67='?' (Size = 4000), @p68='?' (DbType = DateTime2), @p69='?' (DbType = Int32), @p70='?' (DbType = Int32), @p71='?' (DbType = DateTime2), @p72='?' (DbType = Int32), @p73='?' (Size = 4000), @p74='?' (DbType = Boolean), @p75='?' (DbType = Boolean), @p76='?' (Size = 4000), @p77='?' (Size = 4000), @p78='?' (DbType = DateTime2), @p79='?' (DbType = Int32), @p80='?' (DbType = Int32), @p81='?' (DbType = DateTime2), @p82='?' (DbType = Int32), @p83='?' (Size = 4000), @p84='?' (DbType = Boolean), @p85='?' (DbType = Boolean), @p86='?' (Size = 4000), @p87='?' (Size = 4000), @p88='?' (DbType = DateTime2), @p89='?' (DbType = Int32), @p90='?' (DbType = Int32), @p91='?' (DbType = DateTime2), @p92='?' (DbType = Int32), @p93='?' (Size = 4000), @p94='?' (DbType = Boolean), @p95='?' (DbType = Boolean), @p96='?' (Size = 4000), @p97='?' (Size = 4000), @p98='?' (DbType = DateTime2), @p99='?' (DbType = Int32), @p100='?' (DbType = Int32), @p101='?' (DbType = DateTime2), @p102='?' (DbType = Int32), @p103='?' (Size = 4000), @p104='?' (DbType = Boolean), @p105='?' (DbType = Boolean), @p106='?' (Size = 4000), @p107='?' (Size = 4000), @p108='?' (DbType = DateTime2), @p109='?' (DbType = Int32), @p110='?' (DbType = Int32), @p111='?' (DbType = DateTime2), @p112='?' (DbType = Int32), @p113='?' (Size = 4000), @p114='?' (DbType = Boolean), @p115='?' (DbType = Boolean), @p116='?' (Size = 4000), @p117='?' (Size = 4000), @p118='?' (DbType = DateTime2), @p119='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Units] ([Id], [CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [Symbol], [UpdatedAt], [UpdatedBy])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9),
(@p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19),
(@p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29),
(@p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39),
(@p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49),
(@p50, @p51, @p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59),
(@p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69),
(@p70, @p71, @p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79),
(@p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89),
(@p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, @p99),
(@p100, @p101, @p102, @p103, @p104, @p105, @p106, @p107, @p108, @p109),
(@p110, @p111, @p112, @p113, @p114, @p115, @p116, @p117, @p118, @p119);
2025-08-09 17:10:00.666 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert explicit value for identity column in table 'Units' when IDENTITY_INSERT is set to OFF.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:38e3138a-408f-4f1e-aa8c-c6be0cef4d13
Error Number:544,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert explicit value for identity column in table 'Units' when IDENTITY_INSERT is set to OFF.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:38e3138a-408f-4f1e-aa8c-c6be0cef4d13
Error Number:544,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-08-09 17:10:00.889 +03:00 [ERR] حدث خطأ أثناء تهيئة البيانات الأولية
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert explicit value for identity column in table 'Units' when IDENTITY_INSERT is set to OFF.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:38e3138a-408f-4f1e-aa8c-c6be0cef4d13
Error Number:544,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at DoorCompany.Data.Context.ApplicationDbContext.SaveChangesAsync(CancellationToken cancellationToken) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Data\Context\ApplicationDbContext.cs:line 158
   at DoorCompany.Data.Seeds.DefaultUsers.SeedUnitsAsync(ApplicationDbContext context) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Data\Seeds\DefaultUsers.cs:line 218
   at Program.<Main>$(String[] args) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Api\Program.cs:line 196
2025-08-09 17:10:01.085 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-09 17:10:01.272 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-09 17:10:01.389 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-09 17:10:01.390 +03:00 [INF] Hosting environment: Development
2025-08-09 17:10:01.392 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-09 17:10:02.340 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-09 17:10:02.571 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 242.1448ms
2025-08-09 17:10:02.609 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-09 17:10:02.616 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.1452ms
2025-08-09 17:10:02.908 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-09 17:10:02.962 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 54.8956ms
2025-08-09 17:10:03.158 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-09 17:10:03.197 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 38.9983ms
2025-08-09 17:11:18.796 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-09 17:11:19.017 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-09 17:11:19.328 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:11:19.350 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:11:19.521 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-09 17:11:19.625 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-09 17:11:19.669 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-09 17:11:19.682 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:11:19.690 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:11:19.700 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:11:19.709 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:11:19.717 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:11:19.726 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-09 17:11:19.922 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime2), @p10='?' (DbType = Int32), @p11='?' (Size = 4000), @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (Size = 4000), @p15='?' (Size = 4000), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32), @p18='?' (DbType = DateTime2), @p19='?' (DbType = Int32), @p20='?' (Size = 4000), @p21='?' (DbType = Boolean), @p22='?' (DbType = Boolean), @p23='?' (Size = 4000), @p24='?' (Size = 4000), @p25='?' (DbType = DateTime2), @p26='?' (DbType = Int32), @p27='?' (DbType = DateTime2), @p28='?' (DbType = Int32), @p29='?' (Size = 4000), @p30='?' (DbType = Boolean), @p31='?' (DbType = Boolean), @p32='?' (Size = 4000), @p33='?' (Size = 4000), @p34='?' (DbType = DateTime2), @p35='?' (DbType = Int32), @p36='?' (DbType = DateTime2), @p37='?' (DbType = Int32), @p38='?' (Size = 4000), @p39='?' (DbType = Boolean), @p40='?' (DbType = Boolean), @p41='?' (Size = 4000), @p42='?' (Size = 4000), @p43='?' (DbType = DateTime2), @p44='?' (DbType = Int32), @p45='?' (DbType = DateTime2), @p46='?' (DbType = Int32), @p47='?' (Size = 4000), @p48='?' (DbType = Boolean), @p49='?' (DbType = Boolean), @p50='?' (Size = 4000), @p51='?' (Size = 4000), @p52='?' (DbType = DateTime2), @p53='?' (DbType = Int32), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (Size = 4000), @p57='?' (DbType = Boolean), @p58='?' (DbType = Boolean), @p59='?' (Size = 4000), @p60='?' (Size = 4000), @p61='?' (DbType = DateTime2), @p62='?' (DbType = Int32), @p63='?' (DbType = DateTime2), @p64='?' (DbType = Int32), @p65='?' (Size = 4000), @p66='?' (DbType = Boolean), @p67='?' (DbType = Boolean), @p68='?' (Size = 4000), @p69='?' (Size = 4000), @p70='?' (DbType = DateTime2), @p71='?' (DbType = Int32), @p72='?' (DbType = DateTime2), @p73='?' (DbType = Int32), @p74='?' (Size = 4000), @p75='?' (DbType = Boolean), @p76='?' (DbType = Boolean), @p77='?' (Size = 4000), @p78='?' (Size = 4000), @p79='?' (DbType = DateTime2), @p80='?' (DbType = Int32), @p81='?' (DbType = DateTime2), @p82='?' (DbType = Int32), @p83='?' (Size = 4000), @p84='?' (DbType = Boolean), @p85='?' (DbType = Boolean), @p86='?' (Size = 4000), @p87='?' (Size = 4000), @p88='?' (DbType = DateTime2), @p89='?' (DbType = Int32), @p90='?' (DbType = DateTime2), @p91='?' (DbType = Int32), @p92='?' (Size = 4000), @p93='?' (DbType = Boolean), @p94='?' (DbType = Boolean), @p95='?' (Size = 4000), @p96='?' (Size = 4000), @p97='?' (DbType = DateTime2), @p98='?' (DbType = Int32), @p99='?' (DbType = DateTime2), @p100='?' (DbType = Int32), @p101='?' (Size = 4000), @p102='?' (DbType = Boolean), @p103='?' (DbType = Boolean), @p104='?' (Size = 4000), @p105='?' (Size = 4000), @p106='?' (DbType = DateTime2), @p107='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [Units] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, 0),
(@p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, 1),
(@p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, 2),
(@p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, 3),
(@p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, 4),
(@p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, 5),
(@p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, 6),
(@p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, 7),
(@p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, 8),
(@p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89, 9),
(@p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, 10),
(@p99, @p100, @p101, @p102, @p103, @p104, @p105, @p106, @p107, 11)) AS i ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [Symbol], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [Symbol], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsActive], i.[IsDeleted], i.[Name], i.[Symbol], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-08-09 17:11:20.096 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-09 17:11:20.272 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-09 17:11:20.423 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-09 17:11:20.427 +03:00 [INF] Hosting environment: Development
2025-08-09 17:11:20.429 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-09 17:11:20.512 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-09 17:11:20.714 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 210.7488ms
2025-08-09 17:11:20.880 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-09 17:11:20.880 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-09 17:11:20.890 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.0187ms
2025-08-09 17:11:20.924 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 44.4126ms
2025-08-09 17:11:21.192 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-09 17:11:24.512 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 3320.4458ms
