﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos.RoleDto
{

    public class UserResponseWithRolesDto
    {
        public int UserId { get; set; }
        public string? FullName { get; set; }
        public List<CheckBoxViewModel> Roles { get; set; } = new List<CheckBoxViewModel>();
    }
    public class UserResponseWithRolesListDto
    {
        public int UserId { get; set; }
        public string? FullName { get; set; }
        public string? Roles { get; set; }
    }
    public class RoleResponseWithPermissionListDto
    {
        public int RoleId { get; set; }
        public string? RoleName { get; set; }
        public string? Permissions { get; set; }
    }
    public class CheckBoxViewModel
    {
        public int Id { get; set; }
        public string? DisplayValue { get; set; }
        public bool IsSelected { get; set; }
    }

    public class RolePermissionsDto
    {
        public int RoleId { get; set; }
        public string? RoleName { get; set; }
        public List<CheckBoxViewModel> Permissions { get; set; } = new List<CheckBoxViewModel>();
    }



    public class UpdateUserRolesDto
    {
        public int UserId { get; set; }
        public List<CheckBoxViewModel> Roles { get; set; } = new List<CheckBoxViewModel>();
    }

    public class UpdateRolePermissionsDto
    {
        public int RoleId { get; set; }
        public List<CheckBoxViewModel> Permissions { get; set; } = new List<CheckBoxViewModel>();
    }

    public class CreateRoleDto
    {
        [Required]
        public string? RoleName { get; set; }   
    }

    public class UpdateRoleDto
    {
        [Required]
        public int Id { get; set; }
        [Required]
        public string? RoleName { get; set; }
    }
}
