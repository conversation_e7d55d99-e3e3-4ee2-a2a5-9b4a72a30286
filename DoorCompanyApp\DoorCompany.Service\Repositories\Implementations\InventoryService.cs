using AutoMapper;
using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.InventoryDto;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.EntityFrameworkCore;

namespace DoorCompany.Service.Repositories.Implementations
{
    public class InventoryService :ResponseH<PERSON>ler, IInventoryRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;

        public InventoryService(IUnitOfWorkOfService unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<ApiResponse<bool>> AddInventoryTransactionAsync(InventoryTransaction transaction)
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // إضافة الحركة
                await _unitOfWork.InventoryTransactions.AddAsync(transaction);

                // تحديث رصيد المنتج
                await UpdateProductInventoryAfterTransaction(transaction);

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return Success(true, "تم إضافة حركة المخزون بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest<bool>($"خطأ في إضافة حركة المخزون: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> AddInventoryTransactionsAsync(List<InventoryTransaction> transactions)
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync();

                foreach (var transaction in transactions)
                {
                    await _unitOfWork.InventoryTransactions.AddAsync(transaction);
                    await UpdateProductInventoryAfterTransaction(transaction);
                }

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return Success(true, "تم إضافة حركات المخزون بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest<bool>($"خطأ في إضافة حركات المخزون: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> UpdateProductInventoryAsync(int productId, decimal quantity, decimal unitCost, InventoryTransactionType transactionType)
        {
            try
            {
                var transaction = new InventoryTransaction
                {
                    ProductId = productId,
                    Quantity = quantity,
                    UnitCost = unitCost,
                    TotalCost = quantity * unitCost,
                    Type = transactionType,
                    TransactionDate = DateTime.Now,
                    Description = "تحديث مخزون"
                };

                return await AddInventoryTransactionAsync(transaction);
            }
            catch (Exception ex)
            {
                return BadRequest<bool>($"خطأ في تحديث المخزون: {ex.Message}");
            }
        }

        public async Task<ApiResponse<ProductInventoryDto>> GetProductInventoryAsync(int productId)
        {
            try
            {
                var inventory = await _unitOfWork.ProductInventorys
                    .GetTableNoTracking()
                    .Include(p => p.Product)
                    .FirstOrDefaultAsync(p => p.ProductId == productId);

                if (inventory == null)
                {
                    return NotFound<ProductInventoryDto>("المنتج غير موجود في المخزون");
                }

                var result = new ProductInventoryDto
                {
                    ProductId = inventory.ProductId,
                    ProductName = inventory.Product.Name,
                    ProductCode = inventory.Product.Code,
                    Balance = inventory.Balance,
                    AverageCost = inventory.AverageCost,
                    TotalValue = inventory.Balance * inventory.AverageCost,
                    MinimumStock = inventory.Product.MinimumStock,
                    MaximumStock = inventory.Product.MaximumStock,
                    IsLowStock = inventory.Product.MinimumStock.HasValue && inventory.Balance <= inventory.Product.MinimumStock.Value,
                    IsOverStock = inventory.Product.MaximumStock.HasValue && inventory.Balance >= inventory.Product.MaximumStock.Value
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                return BadRequest<ProductInventoryDto>($"خطأ في الحصول على بيانات المخزون: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResponse<ProductInventoryDto>>> GetInventoryReportAsync(InventoryFilterDto filterDto)
        {
            try
            {
                var query = _unitOfWork.ProductInventorys
                    .GetTableNoTracking();

                if (filterDto.ProductId.HasValue)
                    query = query.Where(p => p.ProductId == filterDto.ProductId.Value);


                query = query.Include(p => p.Product);

                if (!string.IsNullOrEmpty(filterDto.SearchTerm))
                    query = query.Where(p => p.Product.Name.Contains(filterDto.SearchTerm) || 
                                           p.Product.Code.Contains(filterDto.SearchTerm));

                if (filterDto.IsLowStock.HasValue && filterDto.IsLowStock.Value)
                    query = query.Where(p => p.Product.MinimumStock.HasValue && p.Balance <= p.Product.MinimumStock.Value);

                if (filterDto.IsOverStock.HasValue && filterDto.IsOverStock.Value)
                    query = query.Where(p => p.Product.MaximumStock.HasValue && p.Balance >= p.Product.MaximumStock.Value);

                var totalCount = await query.CountAsync();
                var items = await query
                    .Skip((filterDto.PageNumber - 1) * filterDto.PageSize)
                    .Take(filterDto.PageSize)
                    .Select(p => new ProductInventoryDto
                    {
                        ProductId = p.ProductId,
                        ProductName = p.Product.Name,
                        ProductCode = p.Product.Code,
                        Balance = p.Balance,
                        AverageCost = p.AverageCost,
                        TotalValue = p.Balance * p.AverageCost,
                        MinimumStock = p.Product.MinimumStock,
                        MaximumStock = p.Product.MaximumStock,
                        IsLowStock = p.Product.MinimumStock.HasValue && p.Balance <= p.Product.MinimumStock.Value,
                        IsOverStock = p.Product.MaximumStock.HasValue && p.Balance >= p.Product.MaximumStock.Value
                    })
                    .ToListAsync();
         
                var data = new PagedResponse<ProductInventoryDto>
                {
                    Items = items,
                    PageNumber =  filterDto.PageNumber,
                    PageSize =  filterDto.PageSize,
                    TotalCount =  totalCount   
                };
              
                return Success(data);
            }
            catch (Exception ex)
            {
                return NotFound<PagedResponse<ProductInventoryDto>>($"خطأ في الحصول على تقرير المخزون: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResponse<InventoryTransactionDto>>> GetProductTransactionsAsync(int productId, InventoryFilterDto filterDto)
        {
            try
            {
                var query = _unitOfWork.InventoryTransactions
                    .GetTableNoTracking()
                    .Include(t => t.Product)
                    .Where(t => t.ProductId == productId);

                if (filterDto.TransactionType.HasValue)
                    query = query.Where(t => t.Type == filterDto.TransactionType.Value);

                if (filterDto.FromDate.HasValue)
                    query = query.Where(t => t.TransactionDate >= filterDto.FromDate.Value);

                if (filterDto.ToDate.HasValue)
                    query = query.Where(t => t.TransactionDate <= filterDto.ToDate.Value);

                query = query.OrderByDescending(t => t.TransactionDate);

                var totalCount = await query.CountAsync();
                var items = await query
                    .Skip((filterDto.PageNumber - 1) * filterDto.PageSize)
                    .Take(filterDto.PageSize)
                    .Select(t => new InventoryTransactionDto
                    {
                        Id = t.Id,
                        TransactionDate = t.TransactionDate,
                        Type = t.Type,
                        TypeName = t.Type.ToString(),
                        ProductId = t.ProductId,
                        ProductName = t.Product.Name,
                        Quantity = t.Quantity,
                        UnitCost = t.UnitCost,
                        TotalCost = t.TotalCost,
                        BalanceAfter = t.BalanceAfter,
                        AverageCost = t.AverageCost,
                        ReferenceNumber = t.ReferenceNumber,
                        Description = t.Description
                    })
                    .ToListAsync();

                var data = new PagedResponse<InventoryTransactionDto>
                {
                    Items = items,
                    PageNumber =  filterDto.PageNumber,
                    PageSize =  filterDto.PageSize,
                    TotalCount =  totalCount   
                };
              
                return Success(data);
               
            }
            catch (Exception ex)
            {
                return NotFound<PagedResponse<InventoryTransactionDto>>($"خطأ في الحصول على حركات المنتج: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResponse<InventoryTransactionDto>>> GetInventoryTransactionsAsync(InventoryFilterDto filterDto)
        {
            try
            {
                var query = _unitOfWork.InventoryTransactions
                    .GetTableNoTracking();


                if (filterDto.ProductId.HasValue)
                    query = query.Where(t => t.ProductId == filterDto.ProductId.Value);

                query = query.Include(t => t.Product);

                if (filterDto.TransactionType.HasValue)
                    query = query.Where(t => t.Type == filterDto.TransactionType.Value);

                if (filterDto.FromDate.HasValue)
                    query = query.Where(t => t.TransactionDate >= filterDto.FromDate.Value);

                if (filterDto.ToDate.HasValue)
                    query = query.Where(t => t.TransactionDate <= filterDto.ToDate.Value);

                if (!string.IsNullOrEmpty(filterDto.SearchTerm))
                    query = query.Where(t => t.Product.Name.Contains(filterDto.SearchTerm) ||
                                           t.Product.Code.Contains(filterDto.SearchTerm) ||
                                           (t.ReferenceNumber != null && t.ReferenceNumber.Contains(filterDto.SearchTerm)));

                query = query.OrderByDescending(t => t.TransactionDate);

                var totalCount = await query.CountAsync();
                var items = await query
                    .Skip((filterDto.PageNumber - 1) * filterDto.PageSize)
                    .Take(filterDto.PageSize)
                    .Select(t => new InventoryTransactionDto
                    {
                        Id = t.Id,
                        TransactionDate = t.TransactionDate,
                        Type = t.Type,
                        TypeName = t.Type.ToString(),
                        ProductId = t.ProductId,
                        ProductName = t.Product.Name,
                        Quantity = t.Quantity,
                        UnitCost = t.UnitCost,
                        TotalCost = t.TotalCost,
                        BalanceAfter = t.BalanceAfter,
                        AverageCost = t.AverageCost,
                        ReferenceNumber = t.ReferenceNumber,
                        Description = t.Description
                    })
                    .ToListAsync();

                var data = new PagedResponse<InventoryTransactionDto> { Items = items, PageNumber = filterDto.PageNumber, PageSize = filterDto.PageSize, TotalCount = totalCount };

                return Success(data);

            }
            catch (Exception ex)
            {
                return NotFound<PagedResponse<InventoryTransactionDto>>($"خطأ في الحصول على حركات المخزون: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> CheckProductAvailabilityAsync(int productId, decimal requiredQuantity)
        {
            try
            {
                var inventory = await _unitOfWork.ProductInventorys
                    .GetTableNoTracking()
                    .FirstOrDefaultAsync(p => p.ProductId == productId);

                if (inventory == null)
                    return BadRequest<bool>("المنتج غير موجود في المخزون");

                var isAvailable = inventory.Balance >= requiredQuantity;
                return Success(isAvailable,
                    isAvailable ? "الكمية متوفرة" : $"الكمية غير متوفرة. المتوفر: {inventory.Balance}");
            }
            catch (Exception ex)
            {
                return BadRequest<bool>($"خطأ في التحقق من توفر المنتج: {ex.Message}");
            }
        }

        public async Task<ApiResponse<List<ProductAvailabilityDto>>> CheckProductsAvailabilityAsync(List<ProductQuantityDto> products)
        {
            try
            {
                var result = new List<ProductAvailabilityDto>();

                foreach (var product in products)
                {
                    var inventory = await _unitOfWork.ProductInventorys
                        .GetTableNoTracking()
                        .Include(p => p.Product)
                        .FirstOrDefaultAsync(p => p.ProductId == product.ProductId);

                    if (inventory != null)
                    {
                        var isAvailable = inventory.Balance >= product.Quantity;
                        result.Add(new ProductAvailabilityDto
                        {
                            ProductId = product.ProductId,
                            ProductName = inventory.Product.Name,
                            RequiredQuantity = product.Quantity,
                            AvailableQuantity = inventory.Balance,
                            IsAvailable = isAvailable,
                            ShortageQuantity = isAvailable ? 0 : product.Quantity - inventory.Balance
                        });
                    }
                    else
                    {
                        var productInfo = await _unitOfWork.Products.GetByIdAsync(product.ProductId);
                        result.Add(new ProductAvailabilityDto
                        {
                            ProductId = product.ProductId,
                            ProductName = productInfo?.Name ?? "منتج غير معروف",
                            RequiredQuantity = product.Quantity,
                            AvailableQuantity = 0,
                            IsAvailable = false,
                            ShortageQuantity = product.Quantity
                        });
                    }
                }

                return Success(result);
            }
            catch (Exception ex)
            {
                return BadRequest<List<ProductAvailabilityDto>>($"خطأ في التحقق من توفر المنتجات: {ex.Message}");
            }
        }

        public async Task<ApiResponse<decimal>> CalculateAverageCostAsync(int productId)
        {
            try
            {
                var inventory = await _unitOfWork.ProductInventorys
                    .GetTableNoTracking()
                    .FirstOrDefaultAsync(p => p.ProductId == productId);

                if (inventory == null)
                    return NotFound<decimal>("المنتج غير موجود في المخزون");

                return Success(inventory.AverageCost);
            }
            catch (Exception ex)
            {
                return BadRequest<decimal>($"خطأ في حساب متوسط التكلفة: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> RecalculateInventoryAsync()
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // إعادة تعيين جميع الأرصدة
                var inventories = await _unitOfWork.ProductInventorys.GetTableNoTracking().ToListAsync();
                foreach (var inventory in inventories)
                {
                    inventory.Balance = 0;
                    inventory.AverageCost = 0;
                  await  _unitOfWork.ProductInventorys.UpdateAsync(inventory);
                }

                // إعادة حساب الأرصدة من الحركات
                var transactions = await _unitOfWork.InventoryTransactions
                    .GetTableNoTracking()
                    .OrderBy(t => t.TransactionDate)
                    .ThenBy(t => t.Id)
                    .ToListAsync();

                foreach (var transaction in transactions)
                {
                    await UpdateProductInventoryAfterTransaction(transaction);
                }

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return Success(true, "تم إعادة حساب أرصدة المخزون بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest<bool>($"خطأ في إعادة حساب المخزون: {ex.Message}");
            }
        }

        public async Task<ApiResponse<List<ProductInventoryDto>>> GetLowStockProductsAsync()
        {
            try
            {
                var lowStockProducts = await _unitOfWork.ProductInventorys
                    .GetTableNoTracking()
                    .Include(p => p.Product)
                    .Where(p => p.Product.MinimumStock.HasValue && p.Balance <= p.Product.MinimumStock.Value)
                    .Select(p => new ProductInventoryDto
                    {
                        ProductId = p.ProductId,
                        ProductName = p.Product.Name,
                        ProductCode = p.Product.Code,
                        Balance = p.Balance,
                        AverageCost = p.AverageCost,
                        TotalValue = p.Balance * p.AverageCost,
                        MinimumStock = p.Product.MinimumStock,
                        MaximumStock = p.Product.MaximumStock,
                        IsLowStock = true,
                        IsOverStock = false
                    })
                    .ToListAsync();

                return Success(lowStockProducts);
            }
            catch (Exception ex)
            {
                return BadRequest<List<ProductInventoryDto>>($"خطأ في الحصول على المنتجات منخفضة المخزون: {ex.Message}");
            }
        }

        public async Task<ApiResponse<List<ProductInventoryDto>>> GetOverStockProductsAsync()
        {
            try
            {
                var overStockProducts = await _unitOfWork.ProductInventorys
                    .GetTableNoTracking()
                    .Include(p => p.Product)
                    .Where(p => p.Product.MaximumStock.HasValue && p.Balance >= p.Product.MaximumStock.Value)
                    .Select(p => new ProductInventoryDto
                    {
                        ProductId = p.ProductId,
                        ProductName = p.Product.Name,
                        ProductCode = p.Product.Code,
                        Balance = p.Balance,
                        AverageCost = p.AverageCost,
                        TotalValue = p.Balance * p.AverageCost,
                        MinimumStock = p.Product.MinimumStock,
                        MaximumStock = p.Product.MaximumStock,
                        IsLowStock = false,
                        IsOverStock = true
                    })
                    .ToListAsync();

                return Success(overStockProducts);
            }
            catch (Exception ex)
            {
                return BadRequest<List<ProductInventoryDto>>($"خطأ في الحصول على المنتجات مرتفعة المخزون: {ex.Message}");
            }
        }

        private async Task UpdateProductInventoryAfterTransaction(InventoryTransaction transaction)
        {
            var inventory = await _unitOfWork.ProductInventorys
                .GetTableAsTracking()
                .FirstOrDefaultAsync(p => p.ProductId == transaction.ProductId);

            if (inventory == null)
            {
                inventory = new ProductInventory
                {
                    ProductId = transaction.ProductId,
                    Balance = 0,
                    AverageCost = 0
                };
                await _unitOfWork.ProductInventorys.AddAsync(inventory);
            }

            // حساب الرصيد الجديد
            decimal newBalance = inventory.Balance;
            decimal newAverageCost = inventory.AverageCost;

            if (transaction.Type == InventoryTransactionType.ProductionInput ||
                transaction.Type == InventoryTransactionType.Purchase ||
                transaction.Type == InventoryTransactionType.SalesReturn     
            )
            {
                // إضافة للمخزون
                decimal totalValue = (inventory.Balance * inventory.AverageCost) + transaction.TotalCost;
                newBalance = inventory.Balance + transaction.Quantity;
                newAverageCost = newBalance > 0 ? totalValue / newBalance : 0;
            }
            else if (transaction.Type == InventoryTransactionType.ProductionOutput || 
                     transaction.Type == InventoryTransactionType.Sale ||
                     transaction.Type == InventoryTransactionType.PurchaseReturn
                )
            {
                // خروج من المخزون
                newBalance = inventory.Balance - transaction.Quantity;
                // متوسط التكلفة يبقى كما هو عند الخروج
            }

            inventory.Balance = newBalance;
            inventory.AverageCost = newAverageCost;

            // تحديث الحركة بالرصيد الجديد
            transaction.BalanceAfter = newBalance;
            transaction.AverageCost = newAverageCost;

            await _unitOfWork.ProductInventorys.UpdateAsync(inventory);
        }
    }
}
