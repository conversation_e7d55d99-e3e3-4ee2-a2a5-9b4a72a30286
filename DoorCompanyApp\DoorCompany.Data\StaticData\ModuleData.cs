﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Data.StaticData
{
    public static class ModuleData
    {

        public static List<string> GenerateModulesList(string Module)
        {
            return new List<string>()
            {
                $"Permissions.{Module}.View",
                $"Permissions.{Module}.Add",
                $"Permissions.{Module}.Edit",
                $"Permissions.{Module}.Delete"
            };
        }
    }
}
