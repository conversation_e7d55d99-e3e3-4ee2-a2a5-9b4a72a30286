﻿using DoorCompany.Data.Context;
using DoorCompany.Data.Models;
using DoorCompany.Data.StaticData;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static DoorCompany.Data.Seeds.DefaultUsers;

namespace DoorCompany.Data.Seeds
{
    public static class DefaultUsers
    {
        public static async Task SeedBasicUserAsync(ApplicationDbContext context)
        {
            if (context.Users.Any())
                return;

            if (!context.Users.Any(u => u.UserName == "Admin"))
            {
                var defaultUser = new User
                {

                    UserName = "admin",
                    FullName = "مدير النظام",
                    Password = "eEZPEDAT0eLC131RCecDF38MAyaj3SHPezzQ7iuONHTAAvAHZVnmCR4QfKX3EQIY",
                    Phone = "",
                    ProfileImage = "",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                };


                context.Users.Add(defaultUser);
                await context.SaveChangesAsync();
            }
        }
        public static async Task SeedBasicRolesAsync(ApplicationDbContext context)
        {
            if (context.Roles.Any())
                return;

            var roles = Enum.GetValues(typeof(RolesEnum))
                            .Cast<RolesEnum>()
                            .Select(roleEnum => new Role
                            {
                                Name = roleEnum.ToString(),
                                Description = roleEnum.GetDescription(),
                                CreatedAt = DateTime.Now,
                                CreatedBy = 1
                            }).ToList();

            context.Roles.AddRange(roles);
            await context.SaveChangesAsync();

        }
        public static async Task AssignAllRolesToAdminAsync(ApplicationDbContext context)
        {
            // Step 1: Get Admin user (assuming ID = 1)
            var adminUser = await context.Users.FindAsync(1);
            if (adminUser == null)
                throw new Exception("Admin user not found.");

            // Step 2: Get all roles
            var allRoles = await context.Roles.ToListAsync();

            // Step 3: Check existing assignments
            var existingRoleIds = await context.UserRoles
                .Where(ur => ur.UserId == adminUser.Id)
                .Select(ur => ur.RoleId)
                .ToListAsync();

            // Step 4: Add missing roles
            var newUserRoles = allRoles
                .Where(role => !existingRoleIds.Contains(role.Id))
                .Select(role => new UserRole
                {
                    UserId = adminUser.Id,
                    RoleId = role.Id
                }).ToList();

            if (newUserRoles.Any())
            {
                context.UserRoles.AddRange(newUserRoles);
                await context.SaveChangesAsync();
            }
        }
        public static async Task SeedBasicPermissionAsync(ApplicationDbContext context)
        {
            if (context.Permissions.Any() && context.RolePermissions.Any())
                return;

            var allPermissions = new List<Permission>();

            foreach (PermissionEnum permissionEnum in Enum.GetValues(typeof(PermissionEnum)))
            {
                var modulePermissions = ModuleData.GenerateModulesList(permissionEnum.ToString());
                foreach (var permissionName in modulePermissions)
                {
                    allPermissions.Add(new Permission
                    {
                        Name = permissionName,
                        Description = permissionEnum.GetDescription(),
                        CreatedAt = DateTime.Now,
                        CreatedBy = 1
                    });
                }
            }

            context.Permissions.AddRange(allPermissions);
            await context.SaveChangesAsync();

            // التأكد من وجود الدور Admin
            var adminRole = await context.Roles.FirstOrDefaultAsync(r => r.Name == "Admin");
            if (adminRole == null)
            {
                adminRole = new Role
                {
                    Name = "Admin"
                };
                context.Roles.Add(adminRole);
                await context.SaveChangesAsync();
            }

            // ربط جميع الصلاحيات بدور Admin
            var adminRolePermissions = allPermissions.Select(p => new RolePermission
            {
                RoleId = adminRole.Id,
                PermissionId = p.Id
            }).ToList();

            context.RolePermissions.AddRange(adminRolePermissions);
            await context.SaveChangesAsync();


        }
        public static async Task SeedBasicMainTypeAsync(ApplicationDbContext context)
        {
            if (context.ActionTypes.Any())
                return;

            var actionType = Enum.GetValues(typeof(ActionTypeEnum))
                              .Cast<ActionTypeEnum>()
                              .Select(actionTypeEnum => new ActionType
                              {
                                  Name = actionTypeEnum.ToString(),
                                  Description = actionTypeEnum.GetDescription(),
                                  CreatedAt = DateTime.Now,
                                  CreatedBy = 1
                              }).ToList();

            context.ActionTypes.AddRange(actionType);
            await context.SaveChangesAsync();

        }
        public static async Task SeedBasicMainActionAsync(ApplicationDbContext context)
        {
            if (context.MainActions.Any())
                return;

            context.MainActions.AddRange(DataMainactions.GenrateMainactionList());
            await context.SaveChangesAsync();

        }

        public static async Task SeedCompanyAsync(ApplicationDbContext context)
        {
            if (context.Companies.Any())
                return;
            if (!context.Companies.Any(u => u.Id == 1))
            {
                var defaultCompany = new Company
                {
                    Name = "شركة الابواب المصفحة",
                    Description = "شركة الابواب المصفحة بالمنطقة الصناعية بمدينة 15 مايو",
                    Code = "Door",
                    Symbol = "DOO",
                    EstablishmentDate = DateTime.Now,
                    CommercialRegister = "*********",
                    IndustrialRegister = "*********",
                    TaxRegister = "*********",
                    TotalShares = 10000,
                    IsDeleted = false,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                };
                context.Companies.Add(defaultCompany);
                await context.SaveChangesAsync();
            }
        }

        public static async Task SeedUnitsAsync(ApplicationDbContext context)
        {

            if (context.Units.Any())
                return;
          
                List<Unit> defaultUnits = new List<Unit>()
                {  
                new Unit { Name = "Piece", Description = "قطعة", Symbol = "Pc", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Name = "Kilogram", Description = "كيلوجرام", Symbol = "Kg", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Name = "Gram", Description = "جرام", Symbol = "g", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Name = "Liter", Description = "لتر", Symbol = "L", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Name = "Milliliter", Description = "مليلتر", Symbol = "ml", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Name = "Meter", Description = "متر", Symbol = "m", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Name = "Centimeter", Description = "سنتيمتر", Symbol = "cm", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Name = "Box", Description = "صندوق", Symbol = "Box", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Name = "Carton", Description = "كرتونة", Symbol = "Ctn", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit {  Name = "Pack", Description = "عبوة", Symbol = "Pck", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit {  Name = "Bottle", Description = "زجاجة", Symbol = "Btl", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit {  Name= "Can",  Description = "علبة", Symbol = "Can", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) } };
              await context.Units.AddRangeAsync(defaultUnits);
              await context.SaveChangesAsync();
                      
        }
        public enum RolesEnum
        {

            [Description("إدارة كاملة لجميع صلاحيات البرنامج")]
            Admin,

            [Description("إدارة كاملة لجميع صلاحيات الشركاء")]
            Partner,

            [Description("إدارة كاملة لجميع صلاحيات المخازن")]
            Stores,

            [Description("إدارة كاملة لجميع صلاحيات الحسابات")]
            Financial,

            [Description("إدارة كاملة لجميع صلاحيات المستخدمين")]
            Users
        }

        public enum PermissionEnum
        {

            [Description("المستخدمين")]
            Users,

            [Description("الشركاء")]
            Partner,

            [Description("المخازن")]
            Stores,

            [Description("الحسابات")]
            Financial,


        }

        public enum ActionTypeEnum
        {

            [Description("اضافة")]
            In,

            [Description("خصم")]
            Out, 
            
            [Description("تسوية")]
            NoAction,


        }

        public static class DataMainactions
        {
            public static List<MainAction> GenrateMainactionList()
            {
                return new List<MainAction>         
                {
                    new MainAction { Name = "الشركاء", Description = "حركات الشركاء", ActionTypeId = 3, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                    new MainAction { Name = "إيداع", Description = "حركات الشركاء", ActionTypeId = 1, ParentActionId = 1, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                    new MainAction { Name = "سحوبات", Description = "حركات الشركاء", ActionTypeId = 2, ParentActionId = 1, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               };          
            }

        }
    }
}
