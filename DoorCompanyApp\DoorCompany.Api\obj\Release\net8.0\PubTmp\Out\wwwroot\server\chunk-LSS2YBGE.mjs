import './polyfills.server.mjs';
import{Ea as a,Sa as l,hb as o,ib as r}from"./chunk-JADE4TID.mjs";var d=["mat-internal-form-field",""],m=["*"],g=(()=>{class i{labelPosition;static \u0275fac=function(t){return new(t||i)};static \u0275cmp=a({type:i,selectors:[["div","mat-internal-form-field",""]],hostAttrs:[1,"mdc-form-field","mat-internal-form-field"],hostVars:2,hostBindings:function(t,n){t&2&&l("mdc-form-field--align-end",n.labelPosition==="before")},inputs:{labelPosition:"labelPosition"},attrs:d,ngContentSelectors:m,decls:1,vars:0,template:function(t,n){t&1&&(o(),r(0))},styles:[`.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}
`],encapsulation:2,changeDetection:0})}return i})();export{g as a};
