using DoorCompany.Api.Base;
using DoorCompany.Service.Services;
using DoorCompany.Service.Dtos.FinancialDto;
using DoorCompany.Data.Models;
using Microsoft.AspNetCore.Mvc;
using DoorCompany.Service.Repositories.Interfaces.Basic;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FinancialController : AppControllerBase
    {
        public FinancialController(IUnitOfWork work) : base(work)
        {
        }

        /// <summary>
        /// إضافة معاملة مالية
        /// </summary>
        [HttpPost("transactions")]
        public async Task<IActionResult> AddFinancialTransaction([FromBody] CreateFinancialTransactionDto createDto)
        {
            var result = await _work.FinancialRepository.AddFinancialTransactionAsync(createDto);
            return NewResult(result);
        }

        /// <summary>
        /// تحديث معاملة مالية
        /// </summary>
        [HttpPut("transactions/{id}")]
        public async Task<IActionResult> UpdateFinancialTransaction(int id, [FromBody] UpdateFinancialTransactionDto updateDto)
        {
            var result = await _work.FinancialRepository.UpdateFinancialTransactionAsync(id, updateDto);
            return NewResult(result);
        }

        /// <summary>
        /// حذف معاملة مالية
        /// </summary>
        [HttpDelete("transactions/{id}")]
        public async Task<IActionResult> DeleteFinancialTransaction(int id)
        {
            var result = await _work.FinancialRepository.DeleteFinancialTransactionAsync(id);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على معاملة مالية
        /// </summary>
        [HttpGet("transactions/{id}")]
        public async Task<IActionResult> GetFinancialTransaction(int id)
        {
            var result = await _work.FinancialRepository.GetFinancialTransactionAsync(id);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على المعاملات المالية
        /// </summary>
        [HttpGet("transactions")]
        public async Task<IActionResult> GetFinancialTransactions([FromQuery] FinancialFilterDto filterDto)
        {
            var result = await _work.FinancialRepository.GetFinancialTransactionsAsync(filterDto);
            return NewResult(result);
        }

        /// <summary>
        /// تسجيل سداد لمورد
        /// </summary>
        [HttpPost("supplier-payment")]
        public async Task<IActionResult> RecordSupplierPayment([FromBody] SupplierPaymentDto paymentDto)
        {
            var result = await _work.FinancialRepository.RecordSupplierPaymentAsync(paymentDto.SupplierId, paymentDto.Amount, paymentDto.PaymentType, paymentDto.Notes);
            return NewResult(result);
        }

        /// <summary>
        /// تسجيل تحصيل من عميل
        /// </summary>
        [HttpPost("customer-receipt")]
        public async Task<IActionResult> RecordCustomerReceipt([FromBody] CustomerReceiptDto receiptDto)
        {
            var result = await _work.FinancialRepository.RecordCustomerReceiptAsync(receiptDto.CustomerId, receiptDto.Amount, receiptDto.PaymentType, receiptDto.Notes);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على رصيد الخزينة
        /// </summary>
        [HttpGet("cash-balance")]
        public async Task<IActionResult> GetCashBalance()
        {
            var result = await _work.FinancialRepository.GetCashBalanceAsync();
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على تقرير الخزينة
        /// </summary>
        [HttpGet("cash-report")]
        public async Task<IActionResult> GetCashReport([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var result = await _work.FinancialRepository.GetCashReportAsync(fromDate, toDate);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على حساب عميل
        /// </summary>
        [HttpGet("customer-account/{customerId}")]
        public async Task<IActionResult> GetCustomerAccount(int customerId, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var result = await _work.FinancialRepository.GetCustomerAccountAsync(customerId, fromDate, toDate);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على حساب مورد
        /// </summary>
        [HttpGet("supplier-account/{supplierId}")]
        public async Task<IActionResult> GetSupplierAccount(int supplierId, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var result = await _work.FinancialRepository.GetSupplierAccountAsync(supplierId, fromDate, toDate);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على أرصدة العملاء
        /// </summary>
        [HttpGet("customers-balances")]
        public async Task<IActionResult> GetCustomersBalances([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            var result = await _work.FinancialRepository.GetCustomersBalancesAsync(pageNumber, pageSize);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على أرصدة الموردين
        /// </summary>
        [HttpGet("suppliers-balances")]
        public async Task<IActionResult> GetSuppliersBalances([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            var result = await _work.FinancialRepository.GetSuppliersBalancesAsync(pageNumber, pageSize);
            return NewResult(result);
        }

        /// <summary>
        /// تحويل بين الحسابات
        /// </summary>
        [HttpPost("transfer")]
        public async Task<IActionResult> TransferBetweenAccounts([FromBody] TransferDto transferDto)
        {
            var result = await _work.FinancialRepository.TransferBetweenAccountsAsync(transferDto);
            return NewResult(result);
        }
    }

    /// <summary>
    /// DTO لسداد المورد
    /// </summary>
    public class SupplierPaymentDto
    {
        public int SupplierId { get; set; }
        public decimal Amount { get; set; }
        public DoorCompany.Data.Models.PaymentType PaymentType { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// DTO لتحصيل العميل
    /// </summary>
    public class CustomerReceiptDto
    {
        public int CustomerId { get; set; }
        public decimal Amount { get; set; }
        public DoorCompany.Data.Models.PaymentType PaymentType { get; set; }
        public string? Notes { get; set; }
    }
}
