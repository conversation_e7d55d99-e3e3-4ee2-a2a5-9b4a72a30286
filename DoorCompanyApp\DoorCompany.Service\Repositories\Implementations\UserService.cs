﻿using AutoMapper;
using Azure;
using DoorComapany.Service.Authentication;
using DoorCompany.Data.Models;
using DoorCompany.Data.StaticData;
using DoorCompany.Service.Authentication;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.ImageDto;
using DoorCompany.Service.Dtos.PermissionDto;
using DoorCompany.Service.Dtos.RoleDto;
using DoorCompany.Service.Dtos.UserDto;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using DoorCompany.Service.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Implementations
{
    public class UserService : ResponseHandler, IUserRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<UserService> _logger;
        private readonly IPasswordHashingService _passwordHashingService;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly IFileUploadService _fileUploadService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserService(
             IUnitOfWorkOfService unitOfWork,
             IMapper mapper,
             ILogger<UserService> logger,
             IPasswordHashingService passwordHashingService,
             IJwtTokenService jwtTokenService,
             IFileUploadService fileUploadService,
             IHttpContextAccessor httpContextAccessor)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _passwordHashingService = passwordHashingService;
            _jwtTokenService = jwtTokenService;
            _fileUploadService = fileUploadService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<ApiResponse<List<UserResponseDto>>> GetAllUsersAsync()
        {
            try
            {
                var users = await _unitOfWork.Users.GetTableNoTracking()
                            .Include(u => u.UserRoles!)
                            .ThenInclude(up => up.Roles!)
                            .ToListAsync();

                if (!users.Any())
                    return NotFound<List<UserResponseDto>>("لا يوجد مستخدمين");

                var userDtos = _mapper.Map<List<UserResponseDto>>(users, opt =>
                {
                    opt.Items["baseUrl"] = $"{_httpContextAccessor.HttpContext.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}";
                });
                return Success(userDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all users");
                return BadRequest<List<UserResponseDto>>("حدث خطأ أثناء جلب بيانات المستخدمين");
            }
        }

        public async Task<ApiResponse<UserResponseDto>> GetUserByIdAsync(int id)
        {
            try
            {
                var user = await _unitOfWork.Users.GetTableNoTracking()
                    .Include(u => u.UserRoles!)
                    .ThenInclude(up => up.Roles!)
                    .FirstOrDefaultAsync(u => u.Id == id && !u.IsDeleted);

                if (user == null)
                    return NotFound<UserResponseDto>("المستخدم غير موجود");

                var userDto = _mapper.Map<UserResponseDto>(user, opt =>
                {
                    opt.Items["baseUrl"] = $"{_httpContextAccessor.HttpContext.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}";
                });
                return Success(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by id: {UserId}", id);
                return BadRequest<UserResponseDto>("حدث خطأ أثناء جلب بيانات المستخدم");
            }
        }

        public async Task<ApiResponse<UserResponseDto>> CreateUserAsync(CreateUserDto userDto)
        {
            try
            {
                // Check if username already exists
                var existingUser = await _unitOfWork.Users.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.UserName == userDto.UserName && !u.IsDeleted);

                if (existingUser != null)
                    return BadRequest<UserResponseDto>("اسم المستخدم موجود بالفعل");

                var user = _mapper.Map<User>(userDto);
                user.Password = _passwordHashingService.HashPassword(userDto.Password);
                // CreatedAt and CreatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User created successfully: {Username}", user.UserName);

                var result = await GetUserByIdAsync(user.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user: {Username}", userDto.UserName);
                return BadRequest<UserResponseDto>("حدث خطأ أثناء إنشاء المستخدم");
            }
        }

        public async Task<ApiResponse<UserResponseDto>> UpdateUserAsync(UpdateUserDto userDto)
        {
            try
            {
                var existingUser = await _unitOfWork.Users.GetByIdAsync(userDto.Id);
                if (existingUser == null || existingUser.IsDeleted)
                    return NotFound<UserResponseDto>("المستخدم غير موجود");

                // Check if username is taken by another user
                var duplicateUser = await _unitOfWork.Users.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.UserName == userDto.UserName && u.Id != userDto.Id && !u.IsDeleted);

                if (duplicateUser != null)
                    return BadRequest<UserResponseDto>("اسم المستخدم موجود بالفعل");

                _mapper.Map(userDto, existingUser);
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Users.UpdateAsync(existingUser);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User updated successfully: {UserId}", existingUser.Id);

                var result = await GetUserByIdAsync(existingUser.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user: {UserId}", userDto.Id);
                return BadRequest<UserResponseDto>("حدث خطأ أثناء تحديث المستخدم");
            }
        }

        public async Task<ApiResponse<bool>> DeleteUserAsync(int id)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(id);
                if (user == null || user.IsDeleted)
                    return NotFound<bool>("المستخدم غير موجود");

                // Soft delete
                user.IsDeleted = true;
                user.IsActive = false;
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Users.UpdateAsync(user);

                // Revoke all refresh tokens
                await _jwtTokenService.RevokeAllUserTokensAsync(id);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User deleted successfully: {UserId}", id);
                return Success(true, "تم حذف المستخدم بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user: {UserId}", id);
                return BadRequest<bool>("حدث خطأ أثناء حذف المستخدم");
            }
        }

        public async Task<ApiResponse<bool>> ChangePasswordAsync(ChangePasswordDto changePasswordDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(changePasswordDto.UserId);
                if (user == null || user.IsDeleted)
                    return NotFound<bool>("المستخدم غير موجود");

                // Verify current password
                if (!_passwordHashingService.VerifyPassword(changePasswordDto.CurrentPassword, user.Password))
                    return BadRequest<bool>("كلمة المرور الحالية غير صحيحة");

                // Hash new password
                user.Password = _passwordHashingService.HashPassword(changePasswordDto.NewPassword);
                user.UpdatedAt = DateTime.Now;

                await _unitOfWork.Users.UpdateAsync(user);

                // Revoke all refresh tokens to force re-login
                await _jwtTokenService.RevokeAllUserTokensAsync(user.Id);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Password changed successfully for user: {UserId}", user.Id);
                return Success(true, "تم تغيير كلمة المرور بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user: {UserId}", changePasswordDto.UserId);
                return BadRequest<bool>("حدث خطأ أثناء تغيير كلمة المرور");
            }
        }

        public async Task<ApiResponse<bool>> ResetPasswordAsync(int userId, string newPassword)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null || user.IsDeleted)
                    return NotFound<bool>("المستخدم غير موجود");

                user.Password = _passwordHashingService.HashPassword(newPassword);
                user.UpdatedAt = DateTime.Now;

                await _unitOfWork.Users.UpdateAsync(user);

                // Revoke all refresh tokens
                await _jwtTokenService.RevokeAllUserTokensAsync(userId);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Password reset successfully for user: {UserId}", userId);
                return Success(true, "تم إعادة تعيين كلمة المرور بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting password for user: {UserId}", userId);
                return BadRequest<bool>("حدث خطأ أثناء إعادة تعيين كلمة المرور");
            }
        }


        // Authentication Methods
        public async Task<ApiResponse<LoginResponseDto>> LoginAsync(LoginDto loginDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetTableNoTracking()
                      .Include(u => u.UserRoles!)
                    .ThenInclude(ur => ur.Roles!)
                        .ThenInclude(r => r.RolePermissions!)
                            .ThenInclude(rp => rp.Permissions)
                    .FirstOrDefaultAsync(u => u.UserName == loginDto.UserName && !u.IsDeleted);

                if (user == null || !user.IsActive)
                {
                    _logger.LogWarning("Login attempt with invalid username: {Username}", loginDto.UserName);
                    return Unauthorized<LoginResponseDto>("اسم المستخدم أو كلمة المرور غير صحيحة");
                }

                if (!_passwordHashingService.VerifyPassword(loginDto.Password, user.Password))
                {
                    _logger.LogWarning("Login attempt with invalid password for user: {Username}", loginDto.UserName);
                    return Unauthorized<LoginResponseDto>("اسم المستخدم أو كلمة المرور غير صحيحة");
                }

                //Get User Roles
                var roles = user.UserRoles!.Select(ur => ur.Roles.Name).ToList();


                // Get user permissions
                var permissions = user.UserRoles!
                    .SelectMany(ur => ur.Roles.RolePermissions!)
                    .Select(rp => rp.Permissions.Name)
                    .Distinct()
                    .ToList();

                // Generate tokens
                var accessToken = await _jwtTokenService.GenerateAccessTokenAsync(user, permissions);
                var refreshToken = await _jwtTokenService.GenerateRefreshTokenAsync();

                // Store refresh token
                var refreshTokenEntity = new RefreshToken
                {
                    Token = refreshToken,
                    UserId = user.Id,
                    ExpiryDate = DateTime.UtcNow.AddDays(7), // 7 days
                    CreatedAt = DateTime.Now
                };

                await _unitOfWork.RefreshTokens.AddAsync(refreshTokenEntity);
                await _unitOfWork.SaveChangesAsync();

                var loginResponse = new LoginResponseDto
                {
                    UserId = user.Id,
                    UserName = user.UserName,
                    FullName = user.FullName,
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    TokenExpiry = DateTime.UtcNow.AddMinutes(60), // 1 hour
                    Roles = roles,
                    Permissions = permissions
                };

                _logger.LogInformation("User logged in successfully: {Username}", user.UserName);
                return Success(loginResponse, "تم تسجيل الدخول بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {Username}", loginDto.UserName);
                return BadRequest<LoginResponseDto>("حدث خطأ أثناء تسجيل الدخول");
            }
        }
        public async Task<ApiResponse<LoginResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshTokenDto)
        {
            try
            {
                var storedToken = await _unitOfWork.RefreshTokens.GetTableNoTracking()
                   .Include(u => u.User!)
                    .ThenInclude(u => u.UserRoles!)
                    .ThenInclude(ur => ur.Roles!)
                        .ThenInclude(r => r.RolePermissions!)
                            .ThenInclude(rp => rp.Permissions)
                    .FirstOrDefaultAsync(rt => rt.Token == refreshTokenDto.RefreshToken && !rt.IsRevoked && rt.ExpiryDate > DateTime.UtcNow);

                if (storedToken == null)
                {
                    _logger.LogWarning("Invalid refresh token used");
                    return Unauthorized<LoginResponseDto>("رمز التحديث غير صالح");
                }

                if (!storedToken.User.IsActive || storedToken.User.IsDeleted)
                {
                    _logger.LogWarning("Refresh token used for inactive user: {UserId}", storedToken.UserId);
                    return Unauthorized<LoginResponseDto>("المستخدم غير نشط");
                }

                // Generate new access token
                var newAccessToken = await _jwtTokenService.RefreshAccessTokenAsync(refreshTokenDto.RefreshToken);

                //Get User Roles
                var roles = storedToken.User.UserRoles!.Select(ur => ur.Roles.Name).ToList();


                // Get user permissions
                var permissions = storedToken.User.UserRoles!
                    .SelectMany(ur => ur.Roles.RolePermissions!)
                    .Select(rp => rp.Permissions.Name)
                    .Distinct()
                    .ToList();

                var loginResponse = new LoginResponseDto
                {
                    UserId = storedToken.User.Id,
                    UserName = storedToken.User.UserName,
                    FullName = storedToken.User.FullName,
                    AccessToken = newAccessToken,
                    RefreshToken = refreshTokenDto.RefreshToken, // Keep the same refresh token
                    TokenExpiry = DateTime.UtcNow.AddMinutes(60),
                    Roles = roles,
                    Permissions = permissions
                };

                _logger.LogInformation("Token refreshed successfully for user: {UserId}", storedToken.UserId);
                return Success(loginResponse, "تم تحديث الرمز بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing token");
                return BadRequest<LoginResponseDto>("حدث خطأ أثناء تحديث الرمز");
            }
        }
        public async Task<ApiResponse<bool>> LogoutAsync(int userId)
        {
            try
            {
                await _jwtTokenService.RevokeAllUserTokensAsync(userId);
                _logger.LogInformation("User logged out successfully: {UserId}", userId);
                return Success(true, "تم تسجيل الخروج بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout for user: {UserId}", userId);
                return BadRequest<bool>("حدث خطأ أثناء تسجيل الخروج");
            }
        }
        public async Task<ApiResponse<bool>> ValidateTokenAsync(string token)
        {
            try
            {
                var principal = await _jwtTokenService.ValidateTokenAsync(token);
                return Success(principal != null, principal != null ? "الرمز صالح" : "الرمز غير صالح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating token");
                return BadRequest<bool>("حدث خطأ أثناء التحقق من الرمز");
            }
        }

        //Authorization UserRole

        public async Task<ApiResponse<List<UserResponseWithRolesListDto>>> GetAllUserRoleAsync()
        {
            var users = await _unitOfWork.Users.GetTableNoTracking()
                             .Include(u => u.UserRoles!)
                             .ThenInclude(up => up.Roles!)
                             .ToListAsync();

            if (!users.Any())
                return NotFound<List<UserResponseWithRolesListDto>>("لا يوجد مستخدمين");


            var response = users.Select(user => new UserResponseWithRolesListDto
            {
                UserId = user.Id,
                FullName = user.FullName,
                Roles = string.Join(", ", user.UserRoles!.Select(ur => ur.Roles.Name))

            }).ToList();

            return Success(response);

        }
        public async Task<ApiResponse<UserResponseWithRolesDto>> GetUserRoleByIdAsync(int userId)
        {
            var user = await _unitOfWork.Users.GetTableNoTracking()
                   .Include(u => u.UserRoles!)
                   .ThenInclude(up => up.Roles!)
                   .FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted);

            if (user == null)
                return NotFound<UserResponseWithRolesDto>("المستخدم غير موجود");

            var allRoles = await _unitOfWork.Roles.GetTableNoTracking().ToListAsync();

            var response = new UserResponseWithRolesDto
            {
                UserId = user.Id,
                FullName = user.FullName,
                Roles = allRoles.Select(role => new CheckBoxViewModel
                {
                    Id = role.Id,
                    DisplayValue = role.Name,
                    IsSelected = user.UserRoles!.Any(ur => ur.RoleId == role.Id)
                }).ToList()
            };
            return Success(response);
        }
        public async Task<ApiResponse<UserResponseWithRolesDto>> UpdateUserRoleAsync(UpdateUserRolesDto updateUserRolesDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetTableNoTracking()
                    .Include(u => u.UserRoles!)
                    .ThenInclude(up => up.Roles!)
                    .FirstOrDefaultAsync(u => u.Id == updateUserRolesDto.UserId && !u.IsDeleted);

                if (user == null)
                    return NotFound<UserResponseWithRolesDto>("المستخدم غير موجود");


                // حذف الأدوار القديمة
                var existingUserRoles = user.UserRoles!.ToList();
                if (existingUserRoles.Any())
                {
                    await _unitOfWork.UserRoles.DeleteRangeAsync(existingUserRoles);
                }

                // إضافة الأدوار الجديدة المختارة فقط
                var selectedRoles = updateUserRolesDto.Roles
                    .Where(r => r.IsSelected)
                    .Select(r => new UserRole
                    {
                        UserId = updateUserRolesDto.UserId,
                        RoleId = r.Id
                    }).ToList();

                if (selectedRoles.Any())
                {
                    await _unitOfWork.UserRoles.AddRangeAsync(selectedRoles);
                }

                // يمكنك هنا تجهيز داتا الرجوع حسب الحاجة
                var result = new UserResponseWithRolesDto
                {
                    UserId = user.Id,
                    FullName = user.FullName,
                    Roles = updateUserRolesDto.Roles
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user roles for userId: {UserId}", updateUserRolesDto.UserId);
                return BadRequest<UserResponseWithRolesDto>("حدث خطأ أثناء تحديث صلاحيات المستخدم");
            }



        }
        public async Task<ApiResponse<List<RoleResponseWithPermissionListDto>>> GetAllRolesAsync()
        {
            var roles = await _unitOfWork.Roles.GetTableNoTracking()
                               .Include(a => a.RolePermissions!)
                                 .ThenInclude(a => a.Permissions)
                          .ToListAsync();

            if (!roles.Any())
                return NotFound<List<RoleResponseWithPermissionListDto>>("لا يوجد ادوار");


            var response = roles.Select(role => new RoleResponseWithPermissionListDto
            {
                RoleId = role.Id,
                RoleName = role.Name,
                Permissions = string.Join(", ", role.RolePermissions!.Select(ur => ur.Permissions.Name))

            }).ToList();

            return Success(response);

        }
        public async Task<ApiResponse<RolePermissionsDto>> GetRolePermissionsByIdAsync(int roleId)
        {
            var role = await _unitOfWork.Roles.GetTableNoTracking()
                              .Include(a => a.RolePermissions!)
                             .ThenInclude(a => a.Permissions)
                  .FirstOrDefaultAsync(u => u.Id == roleId && !u.IsDeleted);

            if (role == null)
                return NotFound<RolePermissionsDto>("الدور غير موجود");

            var allPermission = await _unitOfWork.Permissions.GetTableNoTracking().ToListAsync();

            var response = new RolePermissionsDto
            {
                RoleId = role.Id,
                RoleName = role.Name,
                Permissions = allPermission.Select(perm => new CheckBoxViewModel
                {
                    Id = perm.Id,
                    DisplayValue = perm.Name,
                    IsSelected = role.RolePermissions!.Any(ur => ur.PermissionId == perm.Id)
                }).ToList()
            };
            return Success(response);
        }

        public async Task<ApiResponse<RolePermissionsDto>> UpdateRolePermiissionAsync(UpdateRolePermissionsDto updateRolePermissionsDto)
        {
            try
            {
                var role = await _unitOfWork.Roles.GetTableNoTracking()
                             .Include(a => a.RolePermissions!)
                            .ThenInclude(a => a.Permissions)
                 .FirstOrDefaultAsync(u => u.Id == updateRolePermissionsDto.RoleId && !u.IsDeleted);

                if (role == null)
                    return NotFound<RolePermissionsDto>("الدور غير موجود");


                // حذف الصلاحيات القديمة
                var existingRolePermission = role.RolePermissions!.ToList();
                if (existingRolePermission.Any())
                {
                    await _unitOfWork.RolePermissions.DeleteRangeAsync(existingRolePermission);
                }

                // إضافة الصلاحيات الجديدة المختارة فقط
                var selectedRoles = updateRolePermissionsDto.Permissions
                    .Where(r => r.IsSelected)
                    .Select(r => new RolePermission
                    {
                        RoleId = updateRolePermissionsDto.RoleId,
                        PermissionId = r.Id
                    }).ToList();

                if (selectedRoles.Any())
                {
                    await _unitOfWork.RolePermissions.AddRangeAsync(selectedRoles);
                }

                // يمكنك هنا تجهيز داتا الرجوع حسب الحاجة
                var result = new RolePermissionsDto
                {
                    RoleId = role.Id,
                    RoleName = role.Name,
                    Permissions = updateRolePermissionsDto.Permissions
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user roles for RoleID: {RoleId}", updateRolePermissionsDto.RoleId);
                return BadRequest<RolePermissionsDto>("حدث خطأ أثناء تحديث صلاحيات المستخدم");
            }
        }

        public async Task<ApiResponse<RolePermissionsDto>> CreateRoleAsync(CreateRoleDto model)
        {
            try
            {
                // Check if role already exists
                var existingRole = await _unitOfWork.Roles.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.Name == model.RoleName && !u.IsDeleted);

                if (existingRole != null)
                    return BadRequest<RolePermissionsDto>("اسم الدور موجود بالفعل");

                if(model.RoleName == null) return BadRequest<RolePermissionsDto>("ادخل اسم الدور");
                var role = new Role
                {
                    Name = model.RoleName.ToString()
                };

                await _unitOfWork.Roles.AddAsync(role);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Role created successfully: {Name}", role.Name);


                // يمكنك هنا تجهيز داتا الرجوع حسب الحاجة
                var result = new RolePermissionsDto
                {
                    RoleId = role.Id,
                    RoleName = role.Name,
                    Permissions = null!
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Role: {RoleName}", model.RoleName);
                return BadRequest<RolePermissionsDto>("حدث خطأ أثناء إنشاء الدور");
            }
        }

        public async Task<ApiResponse<RolePermissionsDto>> UpdateRoleAsync(UpdateRoleDto model)
        {
            try
            {
                var existingRole = await _unitOfWork.Roles.GetByIdAsync(model.Id);
                if (existingRole == null || existingRole.IsDeleted)
                    return NotFound<RolePermissionsDto>("الدور غير موجود");

                // Check if Rolename is taken by another Role
                var duplicateRole = await _unitOfWork.Roles.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.Name == model.RoleName && u.Id != model.Id && !u.IsDeleted);

                if (duplicateRole != null)
                    return BadRequest<RolePermissionsDto>("اسم الدور موجود بالفعل");

               existingRole.Name = model.RoleName!.ToString();

                await _unitOfWork.Roles.UpdateAsync(existingRole);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Role updated successfully: {RoleId}", existingRole.Id);
                // يمكنك هنا تجهيز داتا الرجوع حسب الحاجة
                var result = new RolePermissionsDto
                {
                    RoleId = existingRole.Id,
                    RoleName = existingRole.Name,
                    Permissions = null!
                };

                return Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Role: {RoleName}", model.RoleName);
                return BadRequest<RolePermissionsDto>("حدث خطأ أثناء إنشاء الدور");
            }
        }

        public async Task<ApiResponse<List<PermissionResponseDto>>> GetAllPermissionAsync()
        {
            try
            {
                var permissions  = await _unitOfWork.Permissions.GetTableNoTracking()
                                  .ToListAsync();

                if (!permissions.Any())
                    return NotFound<List<PermissionResponseDto>>("لا يوجد صلاحيات");

                var response = permissions.Select(perm => new PermissionResponseDto
                {
                    Id = perm.Id,
                    Name = perm.Name,
                    Description = perm.Description

                }).ToList();

                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all Permission");
                return BadRequest<List<PermissionResponseDto>>("حدث خطأ أثناء جلب بيانات الصلاحيات");
            }
        }

      public async Task<ApiResponse<List<PermissionResponseDto>>> CreatePermissonAsync(CreatePermissionDto dto)
      {
            try
            {
                if(dto == null)
                {
                    return BadRequest<List<PermissionResponseDto>>("اسم الدور موجود بالفعل");
                }
                var normalizedModule = dto.Name!.Trim();

                // توليد قائمة الصلاحيات بناءً على اسم الوحدة
                var generatedPermissions = ModuleData.GenerateModulesList(normalizedModule);

                // جلب الصلاحيات الموجودة مسبقًا فقط من نفس المجموعة
                var existingPermissions = await _unitOfWork.Permissions.GetTableNoTracking()
                    .Where(p => generatedPermissions.Contains(p.Name))
                    .Select(p => p.Name)
                    .ToListAsync();

                // تحديد الصلاحيات الجديدة فقط التي لم تُضاف
                var newPermissions = generatedPermissions
                    .Where(p => !existingPermissions.Contains(p))
                    .Select(permissionName => new Permission
                    {
                        Name = permissionName,
                       Description = dto.Description ?? normalizedModule
                      
                    }).ToList();

                if (!newPermissions.Any())
                    return BadRequest<List<PermissionResponseDto>>("كل صلاحيات هذه الوحدة موجودة بالفعل.");

                await _unitOfWork.Permissions.AddRangeAsync(newPermissions);
                await _unitOfWork.SaveChangesAsync();



             var response = newPermissions.Select(perm => new PermissionResponseDto
                {
                    Id = perm.Id,
                    Name = perm.Name,
                    Description = perm.Description

                }).ToList();

                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Role: {RoleName}", dto.Name);
                return BadRequest<List<PermissionResponseDto>>("حدث خطأ أثناء إنشاء الصلاحية");
            }
        }

        // Profile Image Management Methods
        public async Task<ApiResponse<UserResponseDto>> UpdateUserProfileAsync(UpdateUserProfileDto userDto)
        {
            try
            {
                var existingUser = await _unitOfWork.Users.GetByIdAsync(userDto.Id);
                if (existingUser == null || existingUser.IsDeleted)
                    return NotFound<UserResponseDto>("المستخدم غير موجود");

                // Handle profile image upload if provided
                if (userDto.ProfileImage != null)
                {
                    // Delete old profile image if exists
                    if (!string.IsNullOrEmpty(existingUser.ProfileImage))
                    {
                         _fileUploadService.DeleteImage(existingUser.ProfileImage);
                    }

                    // Upload new profile image
                    var imagePath = await _fileUploadService.UploadImageAsync(userDto.ProfileImage, "Users");
                    existingUser.ProfileImage = imagePath;
                }

                // Update user profile fields
                existingUser.FullName = userDto.FullName;
                existingUser.Phone = userDto.Phone;
                existingUser.UpdatedAt = DateTime.Now;

                await _unitOfWork.Users.UpdateAsync(existingUser);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User profile updated successfully: {UserId}", existingUser.Id);

                var result = await GetUserByIdAsync(existingUser.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user profile: {UserId}", userDto.Id);
                return BadRequest<UserResponseDto>("حدث خطأ أثناء تحديث الملف الشخصي");
            }
        }

        public async Task<ApiResponse<UserResponseDto>> UpdateProfileImage(ChangeProfileImageDto userDto)
        {
            try
            {
                var existingUser = await _unitOfWork.Users.GetByIdAsync(userDto.Id);
                if (existingUser == null || existingUser.IsDeleted)
                    return NotFound<UserResponseDto>("المستخدم غير موجود");

                // Handle profile image upload if provided
                if (userDto.ProfileImage != null)
                {
                    // Delete old profile image if exists
                    if (!string.IsNullOrEmpty(existingUser.ProfileImage))
                    {
                        _fileUploadService.DeleteImage(existingUser.ProfileImage);
                    }

                    // Upload new profile image
                    var imagePath = await _fileUploadService.UploadImageAsync(userDto.ProfileImage, "Users");
                    existingUser.ProfileImage = imagePath;
                }

                // Update user profile fields
                existingUser.UpdatedAt = DateTime.Now;

                await _unitOfWork.Users.UpdateAsync(existingUser);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User profile updated successfully: {UserId}", existingUser.Id);

                var result = await GetUserByIdAsync(existingUser.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user profile: {UserId}", userDto.Id);
                return BadRequest<UserResponseDto>("حدث خطأ أثناء تحديث الملف الشخصي");
            }
        }
    }
}