{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/datepicker.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, inject, ElementRef, <PERSON><PERSON><PERSON>, EventEmitter, Injector, Renderer2, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, Optional, SkipSelf, InjectionToken, ChangeDetectorRef, ViewChild, ANIMATION_MODULE_TYPE, ViewContainerRef, booleanAttribute, Directive, forwardRef, signal, HostAttributeToken, ContentChild, TemplateRef, NgModule } from '@angular/core';\nimport { Subject, Subscription, merge, of } from 'rxjs';\nimport { D as DateAdapter, a as MAT_DATE_FORMATS } from './date-formats-K6TQue-Y.mjs';\nimport { _IdGenerator, CdkMonitorFocus, CdkTrapFocus, A11yModule } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceStringArray } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey, SPACE, ENTER, PAGE_DOWN, PAGE_UP, END, HOME, DOWN_ARROW, UP_ARROW, RIGHT_ARROW, LEFT_ARROW, BACKSPACE } from '@angular/cdk/keycodes';\nimport { Overlay, FlexibleConnectedPositionStrategy, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { Platform, _bindEventWithOptions, _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { ComponentPortal, CdkPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { NgClass, DOCUMENT } from '@angular/common';\nimport { startWith, take, filter } from 'rxjs/operators';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { M as MatIconButton } from './icon-button-D1J0zeqv.mjs';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, Validators, ControlContainer, NgForm, FormGroupDirective, NgControl } from '@angular/forms';\nimport { M as MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-D1GvPuqO.mjs';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-DqPi4knt.mjs';\nimport { E as ErrorStateMatcher } from './error-options-Dm2JJUbF.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport './index-SYVYjXwK.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport './ripple-loader-Ce3DAhPW.mjs';\nimport '@angular/cdk/observers/private';\n\n/** @docs-private */\nconst _c0 = [\"mat-calendar-body\", \"\"];\nfunction _forTrack0($index, $item) {\n  return this._trackRow($item);\n}\nconst _forTrack1 = ($index, $item) => $item.id;\nfunction MatCalendarBody_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 0)(1, \"td\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"padding-top\", ctx_r0._cellPadding)(\"padding-bottom\", ctx_r0._cellPadding);\n    i0.ɵɵattribute(\"colspan\", ctx_r0.numCols);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.label, \" \");\n  }\n}\nfunction MatCalendarBody_For_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"padding-top\", ctx_r0._cellPadding)(\"padding-bottom\", ctx_r0._cellPadding);\n    i0.ɵɵattribute(\"colspan\", ctx_r0._firstRowOffset);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0._firstRowOffset >= ctx_r0.labelMinRequiredCells ? ctx_r0.label : \"\", \" \");\n  }\n}\nfunction MatCalendarBody_For_2_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 6)(1, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function MatCalendarBody_For_2_For_3_Template_button_click_1_listener($event) {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0._cellClicked(item_r3, $event));\n    })(\"focus\", function MatCalendarBody_For_2_For_3_Template_button_focus_1_listener($event) {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0._emitActiveDateChange(item_r3, $event));\n    });\n    i0.ɵɵelementStart(2, \"span\", 8);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"span\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ɵ$index_14_r4 = ctx.$index;\n    const ɵ$index_7_r5 = i0.ɵɵnextContext().$index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"width\", ctx_r0._cellWidth)(\"padding-top\", ctx_r0._cellPadding)(\"padding-bottom\", ctx_r0._cellPadding);\n    i0.ɵɵattribute(\"data-mat-row\", ɵ$index_7_r5)(\"data-mat-col\", ɵ$index_14_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"mat-calendar-body-disabled\", !item_r3.enabled)(\"mat-calendar-body-active\", ctx_r0._isActiveCell(ɵ$index_7_r5, ɵ$index_14_r4))(\"mat-calendar-body-range-start\", ctx_r0._isRangeStart(item_r3.compareValue))(\"mat-calendar-body-range-end\", ctx_r0._isRangeEnd(item_r3.compareValue))(\"mat-calendar-body-in-range\", ctx_r0._isInRange(item_r3.compareValue))(\"mat-calendar-body-comparison-bridge-start\", ctx_r0._isComparisonBridgeStart(item_r3.compareValue, ɵ$index_7_r5, ɵ$index_14_r4))(\"mat-calendar-body-comparison-bridge-end\", ctx_r0._isComparisonBridgeEnd(item_r3.compareValue, ɵ$index_7_r5, ɵ$index_14_r4))(\"mat-calendar-body-comparison-start\", ctx_r0._isComparisonStart(item_r3.compareValue))(\"mat-calendar-body-comparison-end\", ctx_r0._isComparisonEnd(item_r3.compareValue))(\"mat-calendar-body-in-comparison-range\", ctx_r0._isInComparisonRange(item_r3.compareValue))(\"mat-calendar-body-preview-start\", ctx_r0._isPreviewStart(item_r3.compareValue))(\"mat-calendar-body-preview-end\", ctx_r0._isPreviewEnd(item_r3.compareValue))(\"mat-calendar-body-in-preview\", ctx_r0._isInPreview(item_r3.compareValue));\n    i0.ɵɵproperty(\"ngClass\", item_r3.cssClasses)(\"tabindex\", ctx_r0._isActiveCell(ɵ$index_7_r5, ɵ$index_14_r4) ? 0 : -1);\n    i0.ɵɵattribute(\"aria-label\", item_r3.ariaLabel)(\"aria-disabled\", !item_r3.enabled || null)(\"aria-pressed\", ctx_r0._isSelected(item_r3.compareValue))(\"aria-current\", ctx_r0.todayValue === item_r3.compareValue ? \"date\" : null)(\"aria-describedby\", ctx_r0._getDescribedby(item_r3.compareValue));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"mat-calendar-body-selected\", ctx_r0._isSelected(item_r3.compareValue))(\"mat-calendar-body-comparison-identical\", ctx_r0._isComparisonIdentical(item_r3.compareValue))(\"mat-calendar-body-today\", ctx_r0.todayValue === item_r3.compareValue);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.displayValue, \" \");\n  }\n}\nfunction MatCalendarBody_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 1);\n    i0.ɵɵtemplate(1, MatCalendarBody_For_2_Conditional_1_Template, 2, 6, \"td\", 4);\n    i0.ɵɵrepeaterCreate(2, MatCalendarBody_For_2_For_3_Template, 5, 48, \"td\", 5, _forTrack1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r6 = ctx.$implicit;\n    const ɵ$index_7_r5 = ctx.$index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ɵ$index_7_r5 === 0 && ctx_r0._firstRowOffset ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(row_r6);\n  }\n}\nfunction MatMonthView_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 2)(1, \"span\", 6);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 3);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r1.long);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r1.narrow);\n  }\n}\nconst _c1 = [\"*\"];\nfunction MatCalendar_ng_template_0_Template(rf, ctx) {}\nfunction MatCalendar_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-month-view\", 4);\n    i0.ɵɵtwoWayListener(\"activeDateChange\", function MatCalendar_Case_2_Template_mat_month_view_activeDateChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.activeDate, $event) || (ctx_r1.activeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"_userSelection\", function MatCalendar_Case_2_Template_mat_month_view__userSelection_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._dateSelected($event));\n    })(\"dragStarted\", function MatCalendar_Case_2_Template_mat_month_view_dragStarted_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._dragStarted($event));\n    })(\"dragEnded\", function MatCalendar_Case_2_Template_mat_month_view_dragEnded_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._dragEnded($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"activeDate\", ctx_r1.activeDate);\n    i0.ɵɵproperty(\"selected\", ctx_r1.selected)(\"dateFilter\", ctx_r1.dateFilter)(\"maxDate\", ctx_r1.maxDate)(\"minDate\", ctx_r1.minDate)(\"dateClass\", ctx_r1.dateClass)(\"comparisonStart\", ctx_r1.comparisonStart)(\"comparisonEnd\", ctx_r1.comparisonEnd)(\"startDateAccessibleName\", ctx_r1.startDateAccessibleName)(\"endDateAccessibleName\", ctx_r1.endDateAccessibleName)(\"activeDrag\", ctx_r1._activeDrag);\n  }\n}\nfunction MatCalendar_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-year-view\", 5);\n    i0.ɵɵtwoWayListener(\"activeDateChange\", function MatCalendar_Case_3_Template_mat_year_view_activeDateChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.activeDate, $event) || (ctx_r1.activeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"monthSelected\", function MatCalendar_Case_3_Template_mat_year_view_monthSelected_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._monthSelectedInYearView($event));\n    })(\"selectedChange\", function MatCalendar_Case_3_Template_mat_year_view_selectedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._goToDateInView($event, \"month\"));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"activeDate\", ctx_r1.activeDate);\n    i0.ɵɵproperty(\"selected\", ctx_r1.selected)(\"dateFilter\", ctx_r1.dateFilter)(\"maxDate\", ctx_r1.maxDate)(\"minDate\", ctx_r1.minDate)(\"dateClass\", ctx_r1.dateClass);\n  }\n}\nfunction MatCalendar_Case_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-multi-year-view\", 6);\n    i0.ɵɵtwoWayListener(\"activeDateChange\", function MatCalendar_Case_4_Template_mat_multi_year_view_activeDateChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.activeDate, $event) || (ctx_r1.activeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"yearSelected\", function MatCalendar_Case_4_Template_mat_multi_year_view_yearSelected_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._yearSelectedInMultiYearView($event));\n    })(\"selectedChange\", function MatCalendar_Case_4_Template_mat_multi_year_view_selectedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._goToDateInView($event, \"year\"));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"activeDate\", ctx_r1.activeDate);\n    i0.ɵɵproperty(\"selected\", ctx_r1.selected)(\"dateFilter\", ctx_r1.dateFilter)(\"maxDate\", ctx_r1.maxDate)(\"minDate\", ctx_r1.minDate)(\"dateClass\", ctx_r1.dateClass);\n  }\n}\nfunction MatDatepickerContent_ng_template_2_Template(rf, ctx) {}\nconst _c2 = [\"button\"];\nconst _c3 = [[[\"\", \"matDatepickerToggleIcon\", \"\"]]];\nconst _c4 = [\"[matDatepickerToggleIcon]\"];\nfunction MatDatepickerToggle_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 2);\n    i0.ɵɵelement(1, \"path\", 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c5 = [[[\"input\", \"matStartDate\", \"\"]], [[\"input\", \"matEndDate\", \"\"]]];\nconst _c6 = [\"input[matStartDate]\", \"input[matEndDate]\"];\nfunction MatDatepickerActions_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction createMissingDateImplError(provider) {\n  return Error(`MatDatepicker: No provider found for ${provider}. You must add one of the following ` + `to your app config: provideNativeDateAdapter, provideDateFnsAdapter, ` + `provideLuxonDateAdapter, provideMomentDateAdapter, or provide a custom implementation.`);\n}\n\n/** Datepicker data that requires internationalization. */\nclass MatDatepickerIntl {\n  /**\n   * Stream that emits whenever the labels here are changed. Use this to notify\n   * components if the labels have changed after initialization.\n   */\n  changes = new Subject();\n  /** A label for the calendar popup (used by screen readers). */\n  calendarLabel = 'Calendar';\n  /** A label for the button used to open the calendar popup (used by screen readers). */\n  openCalendarLabel = 'Open calendar';\n  /** Label for the button used to close the calendar popup. */\n  closeCalendarLabel = 'Close calendar';\n  /** A label for the previous month button (used by screen readers). */\n  prevMonthLabel = 'Previous month';\n  /** A label for the next month button (used by screen readers). */\n  nextMonthLabel = 'Next month';\n  /** A label for the previous year button (used by screen readers). */\n  prevYearLabel = 'Previous year';\n  /** A label for the next year button (used by screen readers). */\n  nextYearLabel = 'Next year';\n  /** A label for the previous multi-year button (used by screen readers). */\n  prevMultiYearLabel = 'Previous 24 years';\n  /** A label for the next multi-year button (used by screen readers). */\n  nextMultiYearLabel = 'Next 24 years';\n  /** A label for the 'switch to month view' button (used by screen readers). */\n  switchToMonthViewLabel = 'Choose date';\n  /** A label for the 'switch to year view' button (used by screen readers). */\n  switchToMultiYearViewLabel = 'Choose month and year';\n  /**\n   * A label for the first date of a range of dates (used by screen readers).\n   * @deprecated Provide your own internationalization string.\n   * @breaking-change 17.0.0\n   */\n  startDateLabel = 'Start date';\n  /**\n   * A label for the last date of a range of dates (used by screen readers).\n   * @deprecated Provide your own internationalization string.\n   * @breaking-change 17.0.0\n   */\n  endDateLabel = 'End date';\n  /**\n   * A label for the Comparison date of a range of dates (used by screen readers).\n   */\n  comparisonDateLabel = 'Comparison range';\n  /** Formats a range of years (used for visuals). */\n  formatYearRange(start, end) {\n    return `${start} \\u2013 ${end}`;\n  }\n  /** Formats a label for a range of years (used by screen readers). */\n  formatYearRangeLabel(start, end) {\n    return `${start} to ${end}`;\n  }\n  static ɵfac = function MatDatepickerIntl_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerIntl)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatDatepickerIntl,\n    factory: MatDatepickerIntl.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nlet uniqueIdCounter$1 = 0;\n/**\n * An internal class that represents the data corresponding to a single calendar cell.\n * @docs-private\n */\nclass MatCalendarCell {\n  value;\n  displayValue;\n  ariaLabel;\n  enabled;\n  cssClasses;\n  compareValue;\n  rawValue;\n  id = uniqueIdCounter$1++;\n  constructor(value, displayValue, ariaLabel, enabled, cssClasses = {}, compareValue = value, rawValue) {\n    this.value = value;\n    this.displayValue = displayValue;\n    this.ariaLabel = ariaLabel;\n    this.enabled = enabled;\n    this.cssClasses = cssClasses;\n    this.compareValue = compareValue;\n    this.rawValue = rawValue;\n  }\n}\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = {\n  passive: false,\n  capture: true\n};\n/** Event options that can be used to bind a passive, capturing event. */\nconst passiveCapturingEventOptions = {\n  passive: true,\n  capture: true\n};\n/** Event options that can be used to bind a passive, non-capturing event. */\nconst passiveEventOptions = {\n  passive: true\n};\n/**\n * An internal component used to display calendar data in a table.\n * @docs-private\n */\nclass MatCalendarBody {\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _intl = inject(MatDatepickerIntl);\n  _eventCleanups;\n  /**\n   * Used to skip the next focus event when rendering the preview range.\n   * We need a flag like this, because some browsers fire focus events asynchronously.\n   */\n  _skipNextFocus;\n  /**\n   * Used to focus the active cell after change detection has run.\n   */\n  _focusActiveCellAfterViewChecked = false;\n  /** The label for the table. (e.g. \"Jan 2017\"). */\n  label;\n  /** The cells to display in the table. */\n  rows;\n  /** The value in the table that corresponds to today. */\n  todayValue;\n  /** Start value of the selected date range. */\n  startValue;\n  /** End value of the selected date range. */\n  endValue;\n  /** The minimum number of free cells needed to fit the label in the first row. */\n  labelMinRequiredCells;\n  /** The number of columns in the table. */\n  numCols = 7;\n  /** The cell number of the active cell in the table. */\n  activeCell = 0;\n  ngAfterViewChecked() {\n    if (this._focusActiveCellAfterViewChecked) {\n      this._focusActiveCell();\n      this._focusActiveCellAfterViewChecked = false;\n    }\n  }\n  /** Whether a range is being selected. */\n  isRange = false;\n  /**\n   * The aspect ratio (width / height) to use for the cells in the table. This aspect ratio will be\n   * maintained even as the table resizes.\n   */\n  cellAspectRatio = 1;\n  /** Start of the comparison range. */\n  comparisonStart;\n  /** End of the comparison range. */\n  comparisonEnd;\n  /** Start of the preview range. */\n  previewStart = null;\n  /** End of the preview range. */\n  previewEnd = null;\n  /** ARIA Accessible name of the `<input matStartDate/>` */\n  startDateAccessibleName;\n  /** ARIA Accessible name of the `<input matEndDate/>` */\n  endDateAccessibleName;\n  /** Emits when a new value is selected. */\n  selectedValueChange = new EventEmitter();\n  /** Emits when the preview has changed as a result of a user action. */\n  previewChange = new EventEmitter();\n  activeDateChange = new EventEmitter();\n  /** Emits the date at the possible start of a drag event. */\n  dragStarted = new EventEmitter();\n  /** Emits the date at the conclusion of a drag, or null if mouse was not released on a date. */\n  dragEnded = new EventEmitter();\n  /** The number of blank cells to put at the beginning for the first row. */\n  _firstRowOffset;\n  /** Padding for the individual date cells. */\n  _cellPadding;\n  /** Width of an individual cell. */\n  _cellWidth;\n  /** ID for the start date label. */\n  _startDateLabelId;\n  /** ID for the end date label. */\n  _endDateLabelId;\n  /** ID for the comparison start date label. */\n  _comparisonStartDateLabelId;\n  /** ID for the comparison end date label. */\n  _comparisonEndDateLabelId;\n  _didDragSinceMouseDown = false;\n  _injector = inject(Injector);\n  comparisonDateAccessibleName = this._intl.comparisonDateLabel;\n  /**\n   * Tracking function for rows based on their identity. Ideally we would use some sort of\n   * key on the row, but that would require a breaking change for the `rows` input. We don't\n   * use the built-in identity tracking, because it logs warnings.\n   */\n  _trackRow = row => row;\n  constructor() {\n    const renderer = inject(Renderer2);\n    const idGenerator = inject(_IdGenerator);\n    this._startDateLabelId = idGenerator.getId('mat-calendar-body-start-');\n    this._endDateLabelId = idGenerator.getId('mat-calendar-body-end-');\n    this._comparisonStartDateLabelId = idGenerator.getId('mat-calendar-body-comparison-start-');\n    this._comparisonEndDateLabelId = idGenerator.getId('mat-calendar-body-comparison-end-');\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    this._ngZone.runOutsideAngular(() => {\n      const element = this._elementRef.nativeElement;\n      const cleanups = [\n      // `touchmove` is active since we need to call `preventDefault`.\n      _bindEventWithOptions(renderer, element, 'touchmove', this._touchmoveHandler, activeCapturingEventOptions), _bindEventWithOptions(renderer, element, 'mouseenter', this._enterHandler, passiveCapturingEventOptions), _bindEventWithOptions(renderer, element, 'focus', this._enterHandler, passiveCapturingEventOptions), _bindEventWithOptions(renderer, element, 'mouseleave', this._leaveHandler, passiveCapturingEventOptions), _bindEventWithOptions(renderer, element, 'blur', this._leaveHandler, passiveCapturingEventOptions), _bindEventWithOptions(renderer, element, 'mousedown', this._mousedownHandler, passiveEventOptions), _bindEventWithOptions(renderer, element, 'touchstart', this._mousedownHandler, passiveEventOptions)];\n      if (this._platform.isBrowser) {\n        cleanups.push(renderer.listen('window', 'mouseup', this._mouseupHandler), renderer.listen('window', 'touchend', this._touchendHandler));\n      }\n      this._eventCleanups = cleanups;\n    });\n  }\n  /** Called when a cell is clicked. */\n  _cellClicked(cell, event) {\n    // Ignore \"clicks\" that are actually canceled drags (eg the user dragged\n    // off and then went back to this cell to undo).\n    if (this._didDragSinceMouseDown) {\n      return;\n    }\n    if (cell.enabled) {\n      this.selectedValueChange.emit({\n        value: cell.value,\n        event\n      });\n    }\n  }\n  _emitActiveDateChange(cell, event) {\n    if (cell.enabled) {\n      this.activeDateChange.emit({\n        value: cell.value,\n        event\n      });\n    }\n  }\n  /** Returns whether a cell should be marked as selected. */\n  _isSelected(value) {\n    return this.startValue === value || this.endValue === value;\n  }\n  ngOnChanges(changes) {\n    const columnChanges = changes['numCols'];\n    const {\n      rows,\n      numCols\n    } = this;\n    if (changes['rows'] || columnChanges) {\n      this._firstRowOffset = rows && rows.length && rows[0].length ? numCols - rows[0].length : 0;\n    }\n    if (changes['cellAspectRatio'] || columnChanges || !this._cellPadding) {\n      this._cellPadding = `${50 * this.cellAspectRatio / numCols}%`;\n    }\n    if (columnChanges || !this._cellWidth) {\n      this._cellWidth = `${100 / numCols}%`;\n    }\n  }\n  ngOnDestroy() {\n    this._eventCleanups.forEach(cleanup => cleanup());\n  }\n  /** Returns whether a cell is active. */\n  _isActiveCell(rowIndex, colIndex) {\n    let cellNumber = rowIndex * this.numCols + colIndex;\n    // Account for the fact that the first row may not have as many cells.\n    if (rowIndex) {\n      cellNumber -= this._firstRowOffset;\n    }\n    return cellNumber == this.activeCell;\n  }\n  /**\n   * Focuses the active cell after the microtask queue is empty.\n   *\n   * Adding a 0ms setTimeout seems to fix Voiceover losing focus when pressing PageUp/PageDown\n   * (issue #24330).\n   *\n   * Determined a 0ms by gradually increasing duration from 0 and testing two use cases with screen\n   * reader enabled:\n   *\n   * 1. Pressing PageUp/PageDown repeatedly with pausing between each key press.\n   * 2. Pressing and holding the PageDown key with repeated keys enabled.\n   *\n   * Test 1 worked roughly 95-99% of the time with 0ms and got a little bit better as the duration\n   * increased. Test 2 got slightly better until the duration was long enough to interfere with\n   * repeated keys. If the repeated key speed was faster than the timeout duration, then pressing\n   * and holding pagedown caused the entire page to scroll.\n   *\n   * Since repeated key speed can verify across machines, determined that any duration could\n   * potentially interfere with repeated keys. 0ms would be best because it almost entirely\n   * eliminates the focus being lost in Voiceover (#24330) without causing unintended side effects.\n   * Adding delay also complicates writing tests.\n   */\n  _focusActiveCell(movePreview = true) {\n    afterNextRender(() => {\n      setTimeout(() => {\n        const activeCell = this._elementRef.nativeElement.querySelector('.mat-calendar-body-active');\n        if (activeCell) {\n          if (!movePreview) {\n            this._skipNextFocus = true;\n          }\n          activeCell.focus();\n        }\n      });\n    }, {\n      injector: this._injector\n    });\n  }\n  /** Focuses the active cell after change detection has run and the microtask queue is empty. */\n  _scheduleFocusActiveCellAfterViewChecked() {\n    this._focusActiveCellAfterViewChecked = true;\n  }\n  /** Gets whether a value is the start of the main range. */\n  _isRangeStart(value) {\n    return isStart(value, this.startValue, this.endValue);\n  }\n  /** Gets whether a value is the end of the main range. */\n  _isRangeEnd(value) {\n    return isEnd(value, this.startValue, this.endValue);\n  }\n  /** Gets whether a value is within the currently-selected range. */\n  _isInRange(value) {\n    return isInRange(value, this.startValue, this.endValue, this.isRange);\n  }\n  /** Gets whether a value is the start of the comparison range. */\n  _isComparisonStart(value) {\n    return isStart(value, this.comparisonStart, this.comparisonEnd);\n  }\n  /** Whether the cell is a start bridge cell between the main and comparison ranges. */\n  _isComparisonBridgeStart(value, rowIndex, colIndex) {\n    if (!this._isComparisonStart(value) || this._isRangeStart(value) || !this._isInRange(value)) {\n      return false;\n    }\n    let previousCell = this.rows[rowIndex][colIndex - 1];\n    if (!previousCell) {\n      const previousRow = this.rows[rowIndex - 1];\n      previousCell = previousRow && previousRow[previousRow.length - 1];\n    }\n    return previousCell && !this._isRangeEnd(previousCell.compareValue);\n  }\n  /** Whether the cell is an end bridge cell between the main and comparison ranges. */\n  _isComparisonBridgeEnd(value, rowIndex, colIndex) {\n    if (!this._isComparisonEnd(value) || this._isRangeEnd(value) || !this._isInRange(value)) {\n      return false;\n    }\n    let nextCell = this.rows[rowIndex][colIndex + 1];\n    if (!nextCell) {\n      const nextRow = this.rows[rowIndex + 1];\n      nextCell = nextRow && nextRow[0];\n    }\n    return nextCell && !this._isRangeStart(nextCell.compareValue);\n  }\n  /** Gets whether a value is the end of the comparison range. */\n  _isComparisonEnd(value) {\n    return isEnd(value, this.comparisonStart, this.comparisonEnd);\n  }\n  /** Gets whether a value is within the current comparison range. */\n  _isInComparisonRange(value) {\n    return isInRange(value, this.comparisonStart, this.comparisonEnd, this.isRange);\n  }\n  /**\n   * Gets whether a value is the same as the start and end of the comparison range.\n   * For context, the functions that we use to determine whether something is the start/end of\n   * a range don't allow for the start and end to be on the same day, because we'd have to use\n   * much more specific CSS selectors to style them correctly in all scenarios. This is fine for\n   * the regular range, because when it happens, the selected styles take over and still show where\n   * the range would've been, however we don't have these selected styles for a comparison range.\n   * This function is used to apply a class that serves the same purpose as the one for selected\n   * dates, but it only applies in the context of a comparison range.\n   */\n  _isComparisonIdentical(value) {\n    // Note that we don't need to null check the start/end\n    // here, because the `value` will always be defined.\n    return this.comparisonStart === this.comparisonEnd && value === this.comparisonStart;\n  }\n  /** Gets whether a value is the start of the preview range. */\n  _isPreviewStart(value) {\n    return isStart(value, this.previewStart, this.previewEnd);\n  }\n  /** Gets whether a value is the end of the preview range. */\n  _isPreviewEnd(value) {\n    return isEnd(value, this.previewStart, this.previewEnd);\n  }\n  /** Gets whether a value is inside the preview range. */\n  _isInPreview(value) {\n    return isInRange(value, this.previewStart, this.previewEnd, this.isRange);\n  }\n  /** Gets ids of aria descriptions for the start and end of a date range. */\n  _getDescribedby(value) {\n    if (!this.isRange) {\n      return null;\n    }\n    if (this.startValue === value && this.endValue === value) {\n      return `${this._startDateLabelId} ${this._endDateLabelId}`;\n    } else if (this.startValue === value) {\n      return this._startDateLabelId;\n    } else if (this.endValue === value) {\n      return this._endDateLabelId;\n    }\n    if (this.comparisonStart !== null && this.comparisonEnd !== null) {\n      if (value === this.comparisonStart && value === this.comparisonEnd) {\n        return `${this._comparisonStartDateLabelId} ${this._comparisonEndDateLabelId}`;\n      } else if (value === this.comparisonStart) {\n        return this._comparisonStartDateLabelId;\n      } else if (value === this.comparisonEnd) {\n        return this._comparisonEndDateLabelId;\n      }\n    }\n    return null;\n  }\n  /**\n   * Event handler for when the user enters an element\n   * inside the calendar body (e.g. by hovering in or focus).\n   */\n  _enterHandler = event => {\n    if (this._skipNextFocus && event.type === 'focus') {\n      this._skipNextFocus = false;\n      return;\n    }\n    // We only need to hit the zone when we're selecting a range.\n    if (event.target && this.isRange) {\n      const cell = this._getCellFromElement(event.target);\n      if (cell) {\n        this._ngZone.run(() => this.previewChange.emit({\n          value: cell.enabled ? cell : null,\n          event\n        }));\n      }\n    }\n  };\n  _touchmoveHandler = event => {\n    if (!this.isRange) return;\n    const target = getActualTouchTarget(event);\n    const cell = target ? this._getCellFromElement(target) : null;\n    if (target !== event.target) {\n      this._didDragSinceMouseDown = true;\n    }\n    // If the initial target of the touch is a date cell, prevent default so\n    // that the move is not handled as a scroll.\n    if (getCellElement(event.target)) {\n      event.preventDefault();\n    }\n    this._ngZone.run(() => this.previewChange.emit({\n      value: cell?.enabled ? cell : null,\n      event\n    }));\n  };\n  /**\n   * Event handler for when the user's pointer leaves an element\n   * inside the calendar body (e.g. by hovering out or blurring).\n   */\n  _leaveHandler = event => {\n    // We only need to hit the zone when we're selecting a range.\n    if (this.previewEnd !== null && this.isRange) {\n      if (event.type !== 'blur') {\n        this._didDragSinceMouseDown = true;\n      }\n      // Only reset the preview end value when leaving cells. This looks better, because\n      // we have a gap between the cells and the rows and we don't want to remove the\n      // range just for it to show up again when the user moves a few pixels to the side.\n      if (event.target && this._getCellFromElement(event.target) && !(event.relatedTarget && this._getCellFromElement(event.relatedTarget))) {\n        this._ngZone.run(() => this.previewChange.emit({\n          value: null,\n          event\n        }));\n      }\n    }\n  };\n  /**\n   * Triggered on mousedown or touchstart on a date cell.\n   * Respsonsible for starting a drag sequence.\n   */\n  _mousedownHandler = event => {\n    if (!this.isRange) return;\n    this._didDragSinceMouseDown = false;\n    // Begin a drag if a cell within the current range was targeted.\n    const cell = event.target && this._getCellFromElement(event.target);\n    if (!cell || !this._isInRange(cell.compareValue)) {\n      return;\n    }\n    this._ngZone.run(() => {\n      this.dragStarted.emit({\n        value: cell.rawValue,\n        event\n      });\n    });\n  };\n  /** Triggered on mouseup anywhere. Respsonsible for ending a drag sequence. */\n  _mouseupHandler = event => {\n    if (!this.isRange) return;\n    const cellElement = getCellElement(event.target);\n    if (!cellElement) {\n      // Mouseup happened outside of datepicker. Cancel drag.\n      this._ngZone.run(() => {\n        this.dragEnded.emit({\n          value: null,\n          event\n        });\n      });\n      return;\n    }\n    if (cellElement.closest('.mat-calendar-body') !== this._elementRef.nativeElement) {\n      // Mouseup happened inside a different month instance.\n      // Allow it to handle the event.\n      return;\n    }\n    this._ngZone.run(() => {\n      const cell = this._getCellFromElement(cellElement);\n      this.dragEnded.emit({\n        value: cell?.rawValue ?? null,\n        event\n      });\n    });\n  };\n  /** Triggered on touchend anywhere. Respsonsible for ending a drag sequence. */\n  _touchendHandler = event => {\n    const target = getActualTouchTarget(event);\n    if (target) {\n      this._mouseupHandler({\n        target\n      });\n    }\n  };\n  /** Finds the MatCalendarCell that corresponds to a DOM node. */\n  _getCellFromElement(element) {\n    const cell = getCellElement(element);\n    if (cell) {\n      const row = cell.getAttribute('data-mat-row');\n      const col = cell.getAttribute('data-mat-col');\n      if (row && col) {\n        return this.rows[parseInt(row)][parseInt(col)];\n      }\n    }\n    return null;\n  }\n  static ɵfac = function MatCalendarBody_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCalendarBody)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCalendarBody,\n    selectors: [[\"\", \"mat-calendar-body\", \"\"]],\n    hostAttrs: [1, \"mat-calendar-body\"],\n    inputs: {\n      label: \"label\",\n      rows: \"rows\",\n      todayValue: \"todayValue\",\n      startValue: \"startValue\",\n      endValue: \"endValue\",\n      labelMinRequiredCells: \"labelMinRequiredCells\",\n      numCols: \"numCols\",\n      activeCell: \"activeCell\",\n      isRange: \"isRange\",\n      cellAspectRatio: \"cellAspectRatio\",\n      comparisonStart: \"comparisonStart\",\n      comparisonEnd: \"comparisonEnd\",\n      previewStart: \"previewStart\",\n      previewEnd: \"previewEnd\",\n      startDateAccessibleName: \"startDateAccessibleName\",\n      endDateAccessibleName: \"endDateAccessibleName\"\n    },\n    outputs: {\n      selectedValueChange: \"selectedValueChange\",\n      previewChange: \"previewChange\",\n      activeDateChange: \"activeDateChange\",\n      dragStarted: \"dragStarted\",\n      dragEnded: \"dragEnded\"\n    },\n    exportAs: [\"matCalendarBody\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    attrs: _c0,\n    decls: 11,\n    vars: 11,\n    consts: [[\"aria-hidden\", \"true\"], [\"role\", \"row\"], [1, \"mat-calendar-body-hidden-label\", 3, \"id\"], [1, \"mat-calendar-body-label\"], [1, \"mat-calendar-body-label\", 3, \"paddingTop\", \"paddingBottom\"], [\"role\", \"gridcell\", 1, \"mat-calendar-body-cell-container\", 3, \"width\", \"paddingTop\", \"paddingBottom\"], [\"role\", \"gridcell\", 1, \"mat-calendar-body-cell-container\"], [\"type\", \"button\", 1, \"mat-calendar-body-cell\", 3, \"click\", \"focus\", \"ngClass\", \"tabindex\"], [1, \"mat-calendar-body-cell-content\", \"mat-focus-indicator\"], [\"aria-hidden\", \"true\", 1, \"mat-calendar-body-cell-preview\"]],\n    template: function MatCalendarBody_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatCalendarBody_Conditional_0_Template, 3, 6, \"tr\", 0);\n        i0.ɵɵrepeaterCreate(1, MatCalendarBody_For_2_Template, 4, 1, \"tr\", 1, _forTrack0, true);\n        i0.ɵɵelementStart(3, \"span\", 2);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"span\", 2);\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"span\", 2);\n        i0.ɵɵtext(8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"span\", 2);\n        i0.ɵɵtext(10);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx._firstRowOffset < ctx.labelMinRequiredCells ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵrepeater(ctx.rows);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"id\", ctx._startDateLabelId);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.startDateAccessibleName, \"\\n\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"id\", ctx._endDateLabelId);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.endDateAccessibleName, \"\\n\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"id\", ctx._comparisonStartDateLabelId);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate2(\" \", ctx.comparisonDateAccessibleName, \" \", ctx.startDateAccessibleName, \"\\n\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"id\", ctx._comparisonEndDateLabelId);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate2(\" \", ctx.comparisonDateAccessibleName, \" \", ctx.endDateAccessibleName, \"\\n\");\n      }\n    },\n    dependencies: [NgClass],\n    styles: [\".mat-calendar-body{min-width:224px}.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-outline-color, var(--mat-sys-primary))}.mat-calendar-body-label{height:0;line-height:0;text-align:start;padding-left:4.7142857143%;padding-right:4.7142857143%;font-size:var(--mat-datepicker-calendar-body-label-text-size, var(--mat-sys-title-small-size));font-weight:var(--mat-datepicker-calendar-body-label-text-weight, var(--mat-sys-title-small-weight));color:var(--mat-datepicker-calendar-body-label-text-color, var(--mat-sys-on-surface))}.mat-calendar-body-hidden-label{display:none}.mat-calendar-body-cell-container{position:relative;height:0;line-height:0}.mat-calendar-body-cell{position:absolute;top:0;left:0;width:100%;height:100%;background:none;text-align:center;outline:none;font-family:inherit;margin:0;font-family:var(--mat-datepicker-calendar-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-datepicker-calendar-text-size, var(--mat-sys-body-medium-size));-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-calendar-body-cell::-moz-focus-inner{border:0}.mat-calendar-body-cell::before,.mat-calendar-body-cell::after,.mat-calendar-body-cell-preview{content:\\\"\\\";position:absolute;top:5%;left:0;z-index:0;box-sizing:border-box;display:block;height:90%;width:100%}.mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-start::after,.mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,.mat-calendar-body-comparison-start::after,.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:5%;width:95%;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,[dir=rtl] .mat-calendar-body-comparison-start::after,[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:0;border-radius:0;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,.mat-calendar-body-comparison-end::after,.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}[dir=rtl] .mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,[dir=rtl] .mat-calendar-body-comparison-end::after,[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{left:5%;border-radius:0;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-comparison-bridge-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-bridge-end.mat-calendar-body-range-start::after{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end.mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-end.mat-calendar-body-range-start::after{width:90%}.mat-calendar-body-in-preview{color:var(--mat-datepicker-calendar-date-preview-state-outline-color, var(--mat-sys-primary))}.mat-calendar-body-in-preview .mat-calendar-body-cell-preview{border-top:dashed 1px;border-bottom:dashed 1px}.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:dashed 1px}[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:0;border-right:dashed 1px}.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:dashed 1px}[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:0;border-left:dashed 1px}.mat-calendar-body-disabled{cursor:default}.mat-calendar-body-disabled>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){color:var(--mat-datepicker-calendar-date-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-calendar-body-disabled>.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-disabled-state-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mat-calendar-body-disabled{opacity:.5}}.mat-calendar-body-cell-content{top:5%;left:5%;z-index:1;display:flex;align-items:center;justify-content:center;box-sizing:border-box;width:90%;height:90%;line-height:1;border-width:1px;border-style:solid;border-radius:999px;color:var(--mat-datepicker-calendar-date-text-color, var(--mat-sys-on-surface));border-color:var(--mat-datepicker-calendar-date-outline-color, transparent)}.mat-calendar-body-cell-content.mat-focus-indicator{position:absolute}@media(forced-colors: active){.mat-calendar-body-cell-content{border:none}}.cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-focus-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(hover: hover){.mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-hover-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}}.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-state-background-color, var(--mat-sys-primary));color:var(--mat-datepicker-calendar-date-selected-state-text-color, var(--mat-sys-on-primary))}.mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-disabled-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-calendar-body-selected.mat-calendar-body-today{box-shadow:inset 0 0 0 1px var(--mat-datepicker-calendar-date-today-selected-state-outline-color, var(--mat-sys-primary))}.mat-calendar-body-in-range::before{background:var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-sys-primary-container))}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container))}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container))}.mat-calendar-body-comparison-bridge-start::before,[dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-sys-primary-container)) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container)) 50%)}.mat-calendar-body-comparison-bridge-end::before,[dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-sys-primary-container)) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container)) 50%)}.mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:var(--mat-datepicker-calendar-date-in-overlap-range-state-background-color, var(--mat-sys-secondary-container))}.mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:var(--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color, var(--mat-sys-secondary))}@media(forced-colors: active){.mat-datepicker-popup:not(:empty),.mat-calendar-body-cell:not(.mat-calendar-body-in-range) .mat-calendar-body-selected{outline:solid 1px}.mat-calendar-body-today{outline:dotted 1px}.mat-calendar-body-cell::before,.mat-calendar-body-cell::after,.mat-calendar-body-selected{background:none}.mat-calendar-body-in-range::before,.mat-calendar-body-comparison-bridge-start::before,.mat-calendar-body-comparison-bridge-end::before{border-top:solid 1px;border-bottom:solid 1px}.mat-calendar-body-range-start::before{border-left:solid 1px}[dir=rtl] .mat-calendar-body-range-start::before{border-left:0;border-right:solid 1px}.mat-calendar-body-range-end::before{border-right:solid 1px}[dir=rtl] .mat-calendar-body-range-end::before{border-right:0;border-left:solid 1px}.mat-calendar-body-in-comparison-range::before{border-top:dashed 1px;border-bottom:dashed 1px}.mat-calendar-body-comparison-start::before{border-left:dashed 1px}[dir=rtl] .mat-calendar-body-comparison-start::before{border-left:0;border-right:dashed 1px}.mat-calendar-body-comparison-end::before{border-right:dashed 1px}[dir=rtl] .mat-calendar-body-comparison-end::before{border-right:0;border-left:dashed 1px}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCalendarBody, [{\n    type: Component,\n    args: [{\n      selector: '[mat-calendar-body]',\n      host: {\n        'class': 'mat-calendar-body'\n      },\n      exportAs: 'matCalendarBody',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgClass],\n      template: \"<!--\\n  If there's not enough space in the first row, create a separate label row. We mark this row as\\n  aria-hidden because we don't want it to be read out as one of the weeks in the month.\\n-->\\n@if (_firstRowOffset < labelMinRequiredCells) {\\n  <tr aria-hidden=\\\"true\\\">\\n    <td class=\\\"mat-calendar-body-label\\\"\\n        [attr.colspan]=\\\"numCols\\\"\\n        [style.paddingTop]=\\\"_cellPadding\\\"\\n        [style.paddingBottom]=\\\"_cellPadding\\\">\\n      {{label}}\\n    </td>\\n  </tr>\\n}\\n\\n<!-- Create the first row separately so we can include a special spacer cell. -->\\n@for (row of rows; track _trackRow(row); let rowIndex = $index) {\\n  <tr role=\\\"row\\\">\\n    <!--\\n      This cell is purely decorative, but we can't put `aria-hidden` or `role=\\\"presentation\\\"` on it,\\n      because it throws off the week days for the rest of the row on NVDA. The aspect ratio of the\\n      table cells is maintained by setting the top and bottom padding as a percentage of the width\\n      (a variant of the trick described here: https://www.w3schools.com/howto/howto_css_aspect_ratio.asp).\\n    -->\\n    @if (rowIndex === 0 && _firstRowOffset) {\\n      <td\\n        class=\\\"mat-calendar-body-label\\\"\\n        [attr.colspan]=\\\"_firstRowOffset\\\"\\n        [style.paddingTop]=\\\"_cellPadding\\\"\\n        [style.paddingBottom]=\\\"_cellPadding\\\">\\n        {{_firstRowOffset >= labelMinRequiredCells ? label : ''}}\\n      </td>\\n    }\\n    <!--\\n      Each gridcell in the calendar contains a button, which signals to assistive technology that the\\n      cell is interactable, as well as the selection state via `aria-pressed`. See #23476 for\\n      background.\\n    -->\\n    @for (item of row; track item.id; let colIndex = $index) {\\n      <td\\n        role=\\\"gridcell\\\"\\n        class=\\\"mat-calendar-body-cell-container\\\"\\n        [style.width]=\\\"_cellWidth\\\"\\n        [style.paddingTop]=\\\"_cellPadding\\\"\\n        [style.paddingBottom]=\\\"_cellPadding\\\"\\n        [attr.data-mat-row]=\\\"rowIndex\\\"\\n        [attr.data-mat-col]=\\\"colIndex\\\"\\n      >\\n        <button\\n            type=\\\"button\\\"\\n            class=\\\"mat-calendar-body-cell\\\"\\n            [ngClass]=\\\"item.cssClasses\\\"\\n            [tabindex]=\\\"_isActiveCell(rowIndex, colIndex) ? 0 : -1\\\"\\n            [class.mat-calendar-body-disabled]=\\\"!item.enabled\\\"\\n            [class.mat-calendar-body-active]=\\\"_isActiveCell(rowIndex, colIndex)\\\"\\n            [class.mat-calendar-body-range-start]=\\\"_isRangeStart(item.compareValue)\\\"\\n            [class.mat-calendar-body-range-end]=\\\"_isRangeEnd(item.compareValue)\\\"\\n            [class.mat-calendar-body-in-range]=\\\"_isInRange(item.compareValue)\\\"\\n            [class.mat-calendar-body-comparison-bridge-start]=\\\"_isComparisonBridgeStart(item.compareValue, rowIndex, colIndex)\\\"\\n            [class.mat-calendar-body-comparison-bridge-end]=\\\"_isComparisonBridgeEnd(item.compareValue, rowIndex, colIndex)\\\"\\n            [class.mat-calendar-body-comparison-start]=\\\"_isComparisonStart(item.compareValue)\\\"\\n            [class.mat-calendar-body-comparison-end]=\\\"_isComparisonEnd(item.compareValue)\\\"\\n            [class.mat-calendar-body-in-comparison-range]=\\\"_isInComparisonRange(item.compareValue)\\\"\\n            [class.mat-calendar-body-preview-start]=\\\"_isPreviewStart(item.compareValue)\\\"\\n            [class.mat-calendar-body-preview-end]=\\\"_isPreviewEnd(item.compareValue)\\\"\\n            [class.mat-calendar-body-in-preview]=\\\"_isInPreview(item.compareValue)\\\"\\n            [attr.aria-label]=\\\"item.ariaLabel\\\"\\n            [attr.aria-disabled]=\\\"!item.enabled || null\\\"\\n            [attr.aria-pressed]=\\\"_isSelected(item.compareValue)\\\"\\n            [attr.aria-current]=\\\"todayValue === item.compareValue ? 'date' : null\\\"\\n            [attr.aria-describedby]=\\\"_getDescribedby(item.compareValue)\\\"\\n            (click)=\\\"_cellClicked(item, $event)\\\"\\n            (focus)=\\\"_emitActiveDateChange(item, $event)\\\">\\n            <span class=\\\"mat-calendar-body-cell-content mat-focus-indicator\\\"\\n              [class.mat-calendar-body-selected]=\\\"_isSelected(item.compareValue)\\\"\\n              [class.mat-calendar-body-comparison-identical]=\\\"_isComparisonIdentical(item.compareValue)\\\"\\n              [class.mat-calendar-body-today]=\\\"todayValue === item.compareValue\\\">\\n              {{item.displayValue}}\\n            </span>\\n            <span class=\\\"mat-calendar-body-cell-preview\\\" aria-hidden=\\\"true\\\"></span>\\n        </button>\\n      </td>\\n    }\\n  </tr>\\n}\\n\\n<span [id]=\\\"_startDateLabelId\\\" class=\\\"mat-calendar-body-hidden-label\\\">\\n  {{startDateAccessibleName}}\\n</span>\\n<span [id]=\\\"_endDateLabelId\\\" class=\\\"mat-calendar-body-hidden-label\\\">\\n  {{endDateAccessibleName}}\\n</span>\\n<span [id]=\\\"_comparisonStartDateLabelId\\\" class=\\\"mat-calendar-body-hidden-label\\\">\\n  {{comparisonDateAccessibleName}} {{startDateAccessibleName}}\\n</span>\\n<span [id]=\\\"_comparisonEndDateLabelId\\\" class=\\\"mat-calendar-body-hidden-label\\\">\\n  {{comparisonDateAccessibleName}} {{endDateAccessibleName}}\\n</span>\\n\",\n      styles: [\".mat-calendar-body{min-width:224px}.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-outline-color, var(--mat-sys-primary))}.mat-calendar-body-label{height:0;line-height:0;text-align:start;padding-left:4.7142857143%;padding-right:4.7142857143%;font-size:var(--mat-datepicker-calendar-body-label-text-size, var(--mat-sys-title-small-size));font-weight:var(--mat-datepicker-calendar-body-label-text-weight, var(--mat-sys-title-small-weight));color:var(--mat-datepicker-calendar-body-label-text-color, var(--mat-sys-on-surface))}.mat-calendar-body-hidden-label{display:none}.mat-calendar-body-cell-container{position:relative;height:0;line-height:0}.mat-calendar-body-cell{position:absolute;top:0;left:0;width:100%;height:100%;background:none;text-align:center;outline:none;font-family:inherit;margin:0;font-family:var(--mat-datepicker-calendar-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-datepicker-calendar-text-size, var(--mat-sys-body-medium-size));-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-calendar-body-cell::-moz-focus-inner{border:0}.mat-calendar-body-cell::before,.mat-calendar-body-cell::after,.mat-calendar-body-cell-preview{content:\\\"\\\";position:absolute;top:5%;left:0;z-index:0;box-sizing:border-box;display:block;height:90%;width:100%}.mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-start::after,.mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,.mat-calendar-body-comparison-start::after,.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:5%;width:95%;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,[dir=rtl] .mat-calendar-body-comparison-start::after,[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:0;border-radius:0;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,.mat-calendar-body-comparison-end::after,.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}[dir=rtl] .mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,[dir=rtl] .mat-calendar-body-comparison-end::after,[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{left:5%;border-radius:0;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-comparison-bridge-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-bridge-end.mat-calendar-body-range-start::after{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end.mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-end.mat-calendar-body-range-start::after{width:90%}.mat-calendar-body-in-preview{color:var(--mat-datepicker-calendar-date-preview-state-outline-color, var(--mat-sys-primary))}.mat-calendar-body-in-preview .mat-calendar-body-cell-preview{border-top:dashed 1px;border-bottom:dashed 1px}.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:dashed 1px}[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:0;border-right:dashed 1px}.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:dashed 1px}[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:0;border-left:dashed 1px}.mat-calendar-body-disabled{cursor:default}.mat-calendar-body-disabled>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){color:var(--mat-datepicker-calendar-date-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-calendar-body-disabled>.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-disabled-state-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mat-calendar-body-disabled{opacity:.5}}.mat-calendar-body-cell-content{top:5%;left:5%;z-index:1;display:flex;align-items:center;justify-content:center;box-sizing:border-box;width:90%;height:90%;line-height:1;border-width:1px;border-style:solid;border-radius:999px;color:var(--mat-datepicker-calendar-date-text-color, var(--mat-sys-on-surface));border-color:var(--mat-datepicker-calendar-date-outline-color, transparent)}.mat-calendar-body-cell-content.mat-focus-indicator{position:absolute}@media(forced-colors: active){.mat-calendar-body-cell-content{border:none}}.cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-focus-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(hover: hover){.mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-hover-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}}.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-state-background-color, var(--mat-sys-primary));color:var(--mat-datepicker-calendar-date-selected-state-text-color, var(--mat-sys-on-primary))}.mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-disabled-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-calendar-body-selected.mat-calendar-body-today{box-shadow:inset 0 0 0 1px var(--mat-datepicker-calendar-date-today-selected-state-outline-color, var(--mat-sys-primary))}.mat-calendar-body-in-range::before{background:var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-sys-primary-container))}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container))}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container))}.mat-calendar-body-comparison-bridge-start::before,[dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-sys-primary-container)) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container)) 50%)}.mat-calendar-body-comparison-bridge-end::before,[dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-sys-primary-container)) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container)) 50%)}.mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:var(--mat-datepicker-calendar-date-in-overlap-range-state-background-color, var(--mat-sys-secondary-container))}.mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:var(--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color, var(--mat-sys-secondary))}@media(forced-colors: active){.mat-datepicker-popup:not(:empty),.mat-calendar-body-cell:not(.mat-calendar-body-in-range) .mat-calendar-body-selected{outline:solid 1px}.mat-calendar-body-today{outline:dotted 1px}.mat-calendar-body-cell::before,.mat-calendar-body-cell::after,.mat-calendar-body-selected{background:none}.mat-calendar-body-in-range::before,.mat-calendar-body-comparison-bridge-start::before,.mat-calendar-body-comparison-bridge-end::before{border-top:solid 1px;border-bottom:solid 1px}.mat-calendar-body-range-start::before{border-left:solid 1px}[dir=rtl] .mat-calendar-body-range-start::before{border-left:0;border-right:solid 1px}.mat-calendar-body-range-end::before{border-right:solid 1px}[dir=rtl] .mat-calendar-body-range-end::before{border-right:0;border-left:solid 1px}.mat-calendar-body-in-comparison-range::before{border-top:dashed 1px;border-bottom:dashed 1px}.mat-calendar-body-comparison-start::before{border-left:dashed 1px}[dir=rtl] .mat-calendar-body-comparison-start::before{border-left:0;border-right:dashed 1px}.mat-calendar-body-comparison-end::before{border-right:dashed 1px}[dir=rtl] .mat-calendar-body-comparison-end::before{border-right:0;border-left:dashed 1px}}\\n\"]\n    }]\n  }], () => [], {\n    label: [{\n      type: Input\n    }],\n    rows: [{\n      type: Input\n    }],\n    todayValue: [{\n      type: Input\n    }],\n    startValue: [{\n      type: Input\n    }],\n    endValue: [{\n      type: Input\n    }],\n    labelMinRequiredCells: [{\n      type: Input\n    }],\n    numCols: [{\n      type: Input\n    }],\n    activeCell: [{\n      type: Input\n    }],\n    isRange: [{\n      type: Input\n    }],\n    cellAspectRatio: [{\n      type: Input\n    }],\n    comparisonStart: [{\n      type: Input\n    }],\n    comparisonEnd: [{\n      type: Input\n    }],\n    previewStart: [{\n      type: Input\n    }],\n    previewEnd: [{\n      type: Input\n    }],\n    startDateAccessibleName: [{\n      type: Input\n    }],\n    endDateAccessibleName: [{\n      type: Input\n    }],\n    selectedValueChange: [{\n      type: Output\n    }],\n    previewChange: [{\n      type: Output\n    }],\n    activeDateChange: [{\n      type: Output\n    }],\n    dragStarted: [{\n      type: Output\n    }],\n    dragEnded: [{\n      type: Output\n    }]\n  });\n})();\n/** Checks whether a node is a table cell element. */\nfunction isTableCell(node) {\n  return node?.nodeName === 'TD';\n}\n/**\n * Gets the date table cell element that is or contains the specified element.\n * Or returns null if element is not part of a date cell.\n */\nfunction getCellElement(element) {\n  let cell;\n  if (isTableCell(element)) {\n    cell = element;\n  } else if (isTableCell(element.parentNode)) {\n    cell = element.parentNode;\n  } else if (isTableCell(element.parentNode?.parentNode)) {\n    cell = element.parentNode.parentNode;\n  }\n  return cell?.getAttribute('data-mat-row') != null ? cell : null;\n}\n/** Checks whether a value is the start of a range. */\nfunction isStart(value, start, end) {\n  return end !== null && start !== end && value < end && value === start;\n}\n/** Checks whether a value is the end of a range. */\nfunction isEnd(value, start, end) {\n  return start !== null && start !== end && value >= start && value === end;\n}\n/** Checks whether a value is inside of a range. */\nfunction isInRange(value, start, end, rangeEnabled) {\n  return rangeEnabled && start !== null && end !== null && start !== end && value >= start && value <= end;\n}\n/**\n * Extracts the element that actually corresponds to a touch event's location\n * (rather than the element that initiated the sequence of touch events).\n */\nfunction getActualTouchTarget(event) {\n  const touchLocation = event.changedTouches[0];\n  return document.elementFromPoint(touchLocation.clientX, touchLocation.clientY);\n}\n\n/** A class representing a range of dates. */\nclass DateRange {\n  start;\n  end;\n  /**\n   * Ensures that objects with a `start` and `end` property can't be assigned to a variable that\n   * expects a `DateRange`\n   */\n  // tslint:disable-next-line:no-unused-variable\n  _disableStructuralEquivalency;\n  constructor(/** The start date of the range. */\n  start, /** The end date of the range. */\n  end) {\n    this.start = start;\n    this.end = end;\n  }\n}\n/**\n * A selection model containing a date selection.\n * @docs-private\n */\nclass MatDateSelectionModel {\n  selection;\n  _adapter;\n  _selectionChanged = new Subject();\n  /** Emits when the selection has changed. */\n  selectionChanged = this._selectionChanged;\n  constructor(/** The current selection. */\n  selection, _adapter) {\n    this.selection = selection;\n    this._adapter = _adapter;\n    this.selection = selection;\n  }\n  /**\n   * Updates the current selection in the model.\n   * @param value New selection that should be assigned.\n   * @param source Object that triggered the selection change.\n   */\n  updateSelection(value, source) {\n    const oldValue = this.selection;\n    this.selection = value;\n    this._selectionChanged.next({\n      selection: value,\n      source,\n      oldValue\n    });\n  }\n  ngOnDestroy() {\n    this._selectionChanged.complete();\n  }\n  _isValidDateInstance(date) {\n    return this._adapter.isDateInstance(date) && this._adapter.isValid(date);\n  }\n  static ɵfac = function MatDateSelectionModel_Factory(__ngFactoryType__) {\n    i0.ɵɵinvalidFactory();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatDateSelectionModel,\n    factory: MatDateSelectionModel.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDateSelectionModel, [{\n    type: Injectable\n  }], () => [{\n    type: undefined\n  }, {\n    type: DateAdapter\n  }], null);\n})();\n/**\n * A selection model that contains a single date.\n * @docs-private\n */\nclass MatSingleDateSelectionModel extends MatDateSelectionModel {\n  constructor(adapter) {\n    super(null, adapter);\n  }\n  /**\n   * Adds a date to the current selection. In the case of a single date selection, the added date\n   * simply overwrites the previous selection\n   */\n  add(date) {\n    super.updateSelection(date, this);\n  }\n  /** Checks whether the current selection is valid. */\n  isValid() {\n    return this.selection != null && this._isValidDateInstance(this.selection);\n  }\n  /**\n   * Checks whether the current selection is complete. In the case of a single date selection, this\n   * is true if the current selection is not null.\n   */\n  isComplete() {\n    return this.selection != null;\n  }\n  /** Clones the selection model. */\n  clone() {\n    const clone = new MatSingleDateSelectionModel(this._adapter);\n    clone.updateSelection(this.selection, this);\n    return clone;\n  }\n  static ɵfac = function MatSingleDateSelectionModel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSingleDateSelectionModel)(i0.ɵɵinject(DateAdapter));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatSingleDateSelectionModel,\n    factory: MatSingleDateSelectionModel.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSingleDateSelectionModel, [{\n    type: Injectable\n  }], () => [{\n    type: DateAdapter\n  }], null);\n})();\n/**\n * A selection model that contains a date range.\n * @docs-private\n */\nclass MatRangeDateSelectionModel extends MatDateSelectionModel {\n  constructor(adapter) {\n    super(new DateRange(null, null), adapter);\n  }\n  /**\n   * Adds a date to the current selection. In the case of a date range selection, the added date\n   * fills in the next `null` value in the range. If both the start and the end already have a date,\n   * the selection is reset so that the given date is the new `start` and the `end` is null.\n   */\n  add(date) {\n    let {\n      start,\n      end\n    } = this.selection;\n    if (start == null) {\n      start = date;\n    } else if (end == null) {\n      end = date;\n    } else {\n      start = date;\n      end = null;\n    }\n    super.updateSelection(new DateRange(start, end), this);\n  }\n  /** Checks whether the current selection is valid. */\n  isValid() {\n    const {\n      start,\n      end\n    } = this.selection;\n    // Empty ranges are valid.\n    if (start == null && end == null) {\n      return true;\n    }\n    // Complete ranges are only valid if both dates are valid and the start is before the end.\n    if (start != null && end != null) {\n      return this._isValidDateInstance(start) && this._isValidDateInstance(end) && this._adapter.compareDate(start, end) <= 0;\n    }\n    // Partial ranges are valid if the start/end is valid.\n    return (start == null || this._isValidDateInstance(start)) && (end == null || this._isValidDateInstance(end));\n  }\n  /**\n   * Checks whether the current selection is complete. In the case of a date range selection, this\n   * is true if the current selection has a non-null `start` and `end`.\n   */\n  isComplete() {\n    return this.selection.start != null && this.selection.end != null;\n  }\n  /** Clones the selection model. */\n  clone() {\n    const clone = new MatRangeDateSelectionModel(this._adapter);\n    clone.updateSelection(this.selection, this);\n    return clone;\n  }\n  static ɵfac = function MatRangeDateSelectionModel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRangeDateSelectionModel)(i0.ɵɵinject(DateAdapter));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatRangeDateSelectionModel,\n    factory: MatRangeDateSelectionModel.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRangeDateSelectionModel, [{\n    type: Injectable\n  }], () => [{\n    type: DateAdapter\n  }], null);\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SINGLE_DATE_SELECTION_MODEL_FACTORY(parent, adapter) {\n  return parent || new MatSingleDateSelectionModel(adapter);\n}\n/**\n * Used to provide a single selection model to a component.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER = {\n  provide: MatDateSelectionModel,\n  deps: [[new Optional(), new SkipSelf(), MatDateSelectionModel], DateAdapter],\n  useFactory: MAT_SINGLE_DATE_SELECTION_MODEL_FACTORY\n};\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_RANGE_DATE_SELECTION_MODEL_FACTORY(parent, adapter) {\n  return parent || new MatRangeDateSelectionModel(adapter);\n}\n/**\n * Used to provide a range selection model to a component.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_RANGE_DATE_SELECTION_MODEL_PROVIDER = {\n  provide: MatDateSelectionModel,\n  deps: [[new Optional(), new SkipSelf(), MatDateSelectionModel], DateAdapter],\n  useFactory: MAT_RANGE_DATE_SELECTION_MODEL_FACTORY\n};\n\n/** Injection token used to customize the date range selection behavior. */\nconst MAT_DATE_RANGE_SELECTION_STRATEGY = new InjectionToken('MAT_DATE_RANGE_SELECTION_STRATEGY');\n/** Provides the default date range selection behavior. */\nclass DefaultMatCalendarRangeStrategy {\n  _dateAdapter;\n  constructor(_dateAdapter) {\n    this._dateAdapter = _dateAdapter;\n  }\n  selectionFinished(date, currentRange) {\n    let {\n      start,\n      end\n    } = currentRange;\n    if (start == null) {\n      start = date;\n    } else if (end == null && date && this._dateAdapter.compareDate(date, start) >= 0) {\n      end = date;\n    } else {\n      start = date;\n      end = null;\n    }\n    return new DateRange(start, end);\n  }\n  createPreview(activeDate, currentRange) {\n    let start = null;\n    let end = null;\n    if (currentRange.start && !currentRange.end && activeDate) {\n      start = currentRange.start;\n      end = activeDate;\n    }\n    return new DateRange(start, end);\n  }\n  createDrag(dragOrigin, originalRange, newDate) {\n    let start = originalRange.start;\n    let end = originalRange.end;\n    if (!start || !end) {\n      // Can't drag from an incomplete range.\n      return null;\n    }\n    const adapter = this._dateAdapter;\n    const isRange = adapter.compareDate(start, end) !== 0;\n    const diffYears = adapter.getYear(newDate) - adapter.getYear(dragOrigin);\n    const diffMonths = adapter.getMonth(newDate) - adapter.getMonth(dragOrigin);\n    const diffDays = adapter.getDate(newDate) - adapter.getDate(dragOrigin);\n    if (isRange && adapter.sameDate(dragOrigin, originalRange.start)) {\n      start = newDate;\n      if (adapter.compareDate(newDate, end) > 0) {\n        end = adapter.addCalendarYears(end, diffYears);\n        end = adapter.addCalendarMonths(end, diffMonths);\n        end = adapter.addCalendarDays(end, diffDays);\n      }\n    } else if (isRange && adapter.sameDate(dragOrigin, originalRange.end)) {\n      end = newDate;\n      if (adapter.compareDate(newDate, start) < 0) {\n        start = adapter.addCalendarYears(start, diffYears);\n        start = adapter.addCalendarMonths(start, diffMonths);\n        start = adapter.addCalendarDays(start, diffDays);\n      }\n    } else {\n      start = adapter.addCalendarYears(start, diffYears);\n      start = adapter.addCalendarMonths(start, diffMonths);\n      start = adapter.addCalendarDays(start, diffDays);\n      end = adapter.addCalendarYears(end, diffYears);\n      end = adapter.addCalendarMonths(end, diffMonths);\n      end = adapter.addCalendarDays(end, diffDays);\n    }\n    return new DateRange(start, end);\n  }\n  static ɵfac = function DefaultMatCalendarRangeStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DefaultMatCalendarRangeStrategy)(i0.ɵɵinject(DateAdapter));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultMatCalendarRangeStrategy,\n    factory: DefaultMatCalendarRangeStrategy.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultMatCalendarRangeStrategy, [{\n    type: Injectable\n  }], () => [{\n    type: DateAdapter\n  }], null);\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_CALENDAR_RANGE_STRATEGY_PROVIDER_FACTORY(parent, adapter) {\n  return parent || new DefaultMatCalendarRangeStrategy(adapter);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_CALENDAR_RANGE_STRATEGY_PROVIDER = {\n  provide: MAT_DATE_RANGE_SELECTION_STRATEGY,\n  deps: [[new Optional(), new SkipSelf(), MAT_DATE_RANGE_SELECTION_STRATEGY], DateAdapter],\n  useFactory: MAT_CALENDAR_RANGE_STRATEGY_PROVIDER_FACTORY\n};\nconst DAYS_PER_WEEK = 7;\nlet uniqueIdCounter = 0;\n/**\n * An internal component used to display a single month in the datepicker.\n * @docs-private\n */\nclass MatMonthView {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _dateFormats = inject(MAT_DATE_FORMATS, {\n    optional: true\n  });\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _rangeStrategy = inject(MAT_DATE_RANGE_SELECTION_STRATEGY, {\n    optional: true\n  });\n  _rerenderSubscription = Subscription.EMPTY;\n  /** Flag used to filter out space/enter keyup events that originated outside of the view. */\n  _selectionKeyPressed;\n  /**\n   * The date to display in this month view (everything other than the month and year is ignored).\n   */\n  get activeDate() {\n    return this._activeDate;\n  }\n  set activeDate(value) {\n    const oldActiveDate = this._activeDate;\n    const validDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value)) || this._dateAdapter.today();\n    this._activeDate = this._dateAdapter.clampDate(validDate, this.minDate, this.maxDate);\n    if (!this._hasSameMonthAndYear(oldActiveDate, this._activeDate)) {\n      this._init();\n    }\n  }\n  _activeDate;\n  /** The currently selected date. */\n  get selected() {\n    return this._selected;\n  }\n  set selected(value) {\n    if (value instanceof DateRange) {\n      this._selected = value;\n    } else {\n      this._selected = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    }\n    this._setRanges(this._selected);\n  }\n  _selected;\n  /** The minimum selectable date. */\n  get minDate() {\n    return this._minDate;\n  }\n  set minDate(value) {\n    this._minDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _minDate;\n  /** The maximum selectable date. */\n  get maxDate() {\n    return this._maxDate;\n  }\n  set maxDate(value) {\n    this._maxDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _maxDate;\n  /** Function used to filter which dates are selectable. */\n  dateFilter;\n  /** Function that can be used to add custom CSS classes to dates. */\n  dateClass;\n  /** Start of the comparison range. */\n  comparisonStart;\n  /** End of the comparison range. */\n  comparisonEnd;\n  /** ARIA Accessible name of the `<input matStartDate/>` */\n  startDateAccessibleName;\n  /** ARIA Accessible name of the `<input matEndDate/>` */\n  endDateAccessibleName;\n  /** Origin of active drag, or null when dragging is not active. */\n  activeDrag = null;\n  /** Emits when a new date is selected. */\n  selectedChange = new EventEmitter();\n  /** Emits when any date is selected. */\n  _userSelection = new EventEmitter();\n  /** Emits when the user initiates a date range drag via mouse or touch. */\n  dragStarted = new EventEmitter();\n  /**\n   * Emits when the user completes or cancels a date range drag.\n   * Emits null when the drag was canceled or the newly selected date range if completed.\n   */\n  dragEnded = new EventEmitter();\n  /** Emits when any date is activated. */\n  activeDateChange = new EventEmitter();\n  /** The body of calendar table */\n  _matCalendarBody;\n  /** The label for this month (e.g. \"January 2017\"). */\n  _monthLabel;\n  /** Grid of calendar cells representing the dates of the month. */\n  _weeks;\n  /** The number of blank cells in the first row before the 1st of the month. */\n  _firstWeekOffset;\n  /** Start value of the currently-shown date range. */\n  _rangeStart;\n  /** End value of the currently-shown date range. */\n  _rangeEnd;\n  /** Start value of the currently-shown comparison date range. */\n  _comparisonRangeStart;\n  /** End value of the currently-shown comparison date range. */\n  _comparisonRangeEnd;\n  /** Start of the preview range. */\n  _previewStart;\n  /** End of the preview range. */\n  _previewEnd;\n  /** Whether the user is currently selecting a range of dates. */\n  _isRange;\n  /** The date of the month that today falls on. Null if today is in another month. */\n  _todayDate;\n  /** The names of the weekdays. */\n  _weekdays;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._dateAdapter) {\n        throw createMissingDateImplError('DateAdapter');\n      }\n      if (!this._dateFormats) {\n        throw createMissingDateImplError('MAT_DATE_FORMATS');\n      }\n    }\n    this._activeDate = this._dateAdapter.today();\n  }\n  ngAfterContentInit() {\n    this._rerenderSubscription = this._dateAdapter.localeChanges.pipe(startWith(null)).subscribe(() => this._init());\n  }\n  ngOnChanges(changes) {\n    const comparisonChange = changes['comparisonStart'] || changes['comparisonEnd'];\n    if (comparisonChange && !comparisonChange.firstChange) {\n      this._setRanges(this.selected);\n    }\n    if (changes['activeDrag'] && !this.activeDrag) {\n      this._clearPreview();\n    }\n  }\n  ngOnDestroy() {\n    this._rerenderSubscription.unsubscribe();\n  }\n  /** Handles when a new date is selected. */\n  _dateSelected(event) {\n    const date = event.value;\n    const selectedDate = this._getDateFromDayOfMonth(date);\n    let rangeStartDate;\n    let rangeEndDate;\n    if (this._selected instanceof DateRange) {\n      rangeStartDate = this._getDateInCurrentMonth(this._selected.start);\n      rangeEndDate = this._getDateInCurrentMonth(this._selected.end);\n    } else {\n      rangeStartDate = rangeEndDate = this._getDateInCurrentMonth(this._selected);\n    }\n    if (rangeStartDate !== date || rangeEndDate !== date) {\n      this.selectedChange.emit(selectedDate);\n    }\n    this._userSelection.emit({\n      value: selectedDate,\n      event: event.event\n    });\n    this._clearPreview();\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Takes the index of a calendar body cell wrapped in an event as argument. For the date that\n   * corresponds to the given cell, set `activeDate` to that date and fire `activeDateChange` with\n   * that date.\n   *\n   * This function is used to match each component's model of the active date with the calendar\n   * body cell that was focused. It updates its value of `activeDate` synchronously and updates the\n   * parent's value asynchronously via the `activeDateChange` event. The child component receives an\n   * updated value asynchronously via the `activeCell` Input.\n   */\n  _updateActiveDate(event) {\n    const month = event.value;\n    const oldActiveDate = this._activeDate;\n    this.activeDate = this._getDateFromDayOfMonth(month);\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this._activeDate);\n    }\n  }\n  /** Handles keydown events on the calendar body when calendar is in month view. */\n  _handleCalendarBodyKeydown(event) {\n    // TODO(mmalerba): We currently allow keyboard navigation to disabled dates, but just prevent\n    // disabled ones from being selected. This may not be ideal, we should look into whether\n    // navigation should skip over disabled dates, and if so, how to implement that efficiently.\n    const oldActiveDate = this._activeDate;\n    const isRtl = this._isRtl();\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, isRtl ? 1 : -1);\n        break;\n      case RIGHT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, isRtl ? -1 : 1);\n        break;\n      case UP_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, -7);\n        break;\n      case DOWN_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, 7);\n        break;\n      case HOME:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, 1 - this._dateAdapter.getDate(this._activeDate));\n        break;\n      case END:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, this._dateAdapter.getNumDaysInMonth(this._activeDate) - this._dateAdapter.getDate(this._activeDate));\n        break;\n      case PAGE_UP:\n        this.activeDate = event.altKey ? this._dateAdapter.addCalendarYears(this._activeDate, -1) : this._dateAdapter.addCalendarMonths(this._activeDate, -1);\n        break;\n      case PAGE_DOWN:\n        this.activeDate = event.altKey ? this._dateAdapter.addCalendarYears(this._activeDate, 1) : this._dateAdapter.addCalendarMonths(this._activeDate, 1);\n        break;\n      case ENTER:\n      case SPACE:\n        this._selectionKeyPressed = true;\n        if (this._canSelect(this._activeDate)) {\n          // Prevent unexpected default actions such as form submission.\n          // Note that we only prevent the default action here while the selection happens in\n          // `keyup` below. We can't do the selection here, because it can cause the calendar to\n          // reopen if focus is restored immediately. We also can't call `preventDefault` on `keyup`\n          // because it's too late (see #23305).\n          event.preventDefault();\n        }\n        return;\n      case ESCAPE:\n        // Abort the current range selection if the user presses escape mid-selection.\n        if (this._previewEnd != null && !hasModifierKey(event)) {\n          this._clearPreview();\n          // If a drag is in progress, cancel the drag without changing the\n          // current selection.\n          if (this.activeDrag) {\n            this.dragEnded.emit({\n              value: null,\n              event\n            });\n          } else {\n            this.selectedChange.emit(null);\n            this._userSelection.emit({\n              value: null,\n              event\n            });\n          }\n          event.preventDefault();\n          event.stopPropagation(); // Prevents the overlay from closing.\n        }\n        return;\n      default:\n        // Don't prevent default or focus active cell on keys that we don't explicitly handle.\n        return;\n    }\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n      this._focusActiveCellAfterViewChecked();\n    }\n    // Prevent unexpected default actions such as form submission.\n    event.preventDefault();\n  }\n  /** Handles keyup events on the calendar body when calendar is in month view. */\n  _handleCalendarBodyKeyup(event) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this._selectionKeyPressed && this._canSelect(this._activeDate)) {\n        this._dateSelected({\n          value: this._dateAdapter.getDate(this._activeDate),\n          event\n        });\n      }\n      this._selectionKeyPressed = false;\n    }\n  }\n  /** Initializes this month view. */\n  _init() {\n    this._setRanges(this.selected);\n    this._todayDate = this._getCellCompareValue(this._dateAdapter.today());\n    this._monthLabel = this._dateFormats.display.monthLabel ? this._dateAdapter.format(this.activeDate, this._dateFormats.display.monthLabel) : this._dateAdapter.getMonthNames('short')[this._dateAdapter.getMonth(this.activeDate)].toLocaleUpperCase();\n    let firstOfMonth = this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate), this._dateAdapter.getMonth(this.activeDate), 1);\n    this._firstWeekOffset = (DAYS_PER_WEEK + this._dateAdapter.getDayOfWeek(firstOfMonth) - this._dateAdapter.getFirstDayOfWeek()) % DAYS_PER_WEEK;\n    this._initWeekdays();\n    this._createWeekCells();\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Focuses the active cell after the microtask queue is empty. */\n  _focusActiveCell(movePreview) {\n    this._matCalendarBody._focusActiveCell(movePreview);\n  }\n  /** Focuses the active cell after change detection has run and the microtask queue is empty. */\n  _focusActiveCellAfterViewChecked() {\n    this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked();\n  }\n  /** Called when the user has activated a new cell and the preview needs to be updated. */\n  _previewChanged({\n    event,\n    value: cell\n  }) {\n    if (this._rangeStrategy) {\n      // We can assume that this will be a range, because preview\n      // events aren't fired for single date selections.\n      const value = cell ? cell.rawValue : null;\n      const previewRange = this._rangeStrategy.createPreview(value, this.selected, event);\n      this._previewStart = this._getCellCompareValue(previewRange.start);\n      this._previewEnd = this._getCellCompareValue(previewRange.end);\n      if (this.activeDrag && value) {\n        const dragRange = this._rangeStrategy.createDrag?.(this.activeDrag.value, this.selected, value, event);\n        if (dragRange) {\n          this._previewStart = this._getCellCompareValue(dragRange.start);\n          this._previewEnd = this._getCellCompareValue(dragRange.end);\n        }\n      }\n      // Note that here we need to use `detectChanges`, rather than `markForCheck`, because\n      // the way `_focusActiveCell` is set up at the moment makes it fire at the wrong time\n      // when navigating one month back using the keyboard which will cause this handler\n      // to throw a \"changed after checked\" error when updating the preview state.\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n  /**\n   * Called when the user has ended a drag. If the drag/drop was successful,\n   * computes and emits the new range selection.\n   */\n  _dragEnded(event) {\n    if (!this.activeDrag) return;\n    if (event.value) {\n      // Propagate drag effect\n      const dragDropResult = this._rangeStrategy?.createDrag?.(this.activeDrag.value, this.selected, event.value, event.event);\n      this.dragEnded.emit({\n        value: dragDropResult ?? null,\n        event: event.event\n      });\n    } else {\n      this.dragEnded.emit({\n        value: null,\n        event: event.event\n      });\n    }\n  }\n  /**\n   * Takes a day of the month and returns a new date in the same month and year as the currently\n   *  active date. The returned date will have the same day of the month as the argument date.\n   */\n  _getDateFromDayOfMonth(dayOfMonth) {\n    return this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate), this._dateAdapter.getMonth(this.activeDate), dayOfMonth);\n  }\n  /** Initializes the weekdays. */\n  _initWeekdays() {\n    const firstDayOfWeek = this._dateAdapter.getFirstDayOfWeek();\n    const narrowWeekdays = this._dateAdapter.getDayOfWeekNames('narrow');\n    const longWeekdays = this._dateAdapter.getDayOfWeekNames('long');\n    // Rotate the labels for days of the week based on the configured first day of the week.\n    let weekdays = longWeekdays.map((long, i) => {\n      return {\n        long,\n        narrow: narrowWeekdays[i],\n        id: uniqueIdCounter++\n      };\n    });\n    this._weekdays = weekdays.slice(firstDayOfWeek).concat(weekdays.slice(0, firstDayOfWeek));\n  }\n  /** Creates MatCalendarCells for the dates in this month. */\n  _createWeekCells() {\n    const daysInMonth = this._dateAdapter.getNumDaysInMonth(this.activeDate);\n    const dateNames = this._dateAdapter.getDateNames();\n    this._weeks = [[]];\n    for (let i = 0, cell = this._firstWeekOffset; i < daysInMonth; i++, cell++) {\n      if (cell == DAYS_PER_WEEK) {\n        this._weeks.push([]);\n        cell = 0;\n      }\n      const date = this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate), this._dateAdapter.getMonth(this.activeDate), i + 1);\n      const enabled = this._shouldEnableDate(date);\n      const ariaLabel = this._dateAdapter.format(date, this._dateFormats.display.dateA11yLabel);\n      const cellClasses = this.dateClass ? this.dateClass(date, 'month') : undefined;\n      this._weeks[this._weeks.length - 1].push(new MatCalendarCell(i + 1, dateNames[i], ariaLabel, enabled, cellClasses, this._getCellCompareValue(date), date));\n    }\n  }\n  /** Date filter for the month */\n  _shouldEnableDate(date) {\n    return !!date && (!this.minDate || this._dateAdapter.compareDate(date, this.minDate) >= 0) && (!this.maxDate || this._dateAdapter.compareDate(date, this.maxDate) <= 0) && (!this.dateFilter || this.dateFilter(date));\n  }\n  /**\n   * Gets the date in this month that the given Date falls on.\n   * Returns null if the given Date is in another month.\n   */\n  _getDateInCurrentMonth(date) {\n    return date && this._hasSameMonthAndYear(date, this.activeDate) ? this._dateAdapter.getDate(date) : null;\n  }\n  /** Checks whether the 2 dates are non-null and fall within the same month of the same year. */\n  _hasSameMonthAndYear(d1, d2) {\n    return !!(d1 && d2 && this._dateAdapter.getMonth(d1) == this._dateAdapter.getMonth(d2) && this._dateAdapter.getYear(d1) == this._dateAdapter.getYear(d2));\n  }\n  /** Gets the value that will be used to one cell to another. */\n  _getCellCompareValue(date) {\n    if (date) {\n      // We use the time since the Unix epoch to compare dates in this view, rather than the\n      // cell values, because we need to support ranges that span across multiple months/years.\n      const year = this._dateAdapter.getYear(date);\n      const month = this._dateAdapter.getMonth(date);\n      const day = this._dateAdapter.getDate(date);\n      return new Date(year, month, day).getTime();\n    }\n    return null;\n  }\n  /** Determines whether the user has the RTL layout direction. */\n  _isRtl() {\n    return this._dir && this._dir.value === 'rtl';\n  }\n  /** Sets the current range based on a model value. */\n  _setRanges(selectedValue) {\n    if (selectedValue instanceof DateRange) {\n      this._rangeStart = this._getCellCompareValue(selectedValue.start);\n      this._rangeEnd = this._getCellCompareValue(selectedValue.end);\n      this._isRange = true;\n    } else {\n      this._rangeStart = this._rangeEnd = this._getCellCompareValue(selectedValue);\n      this._isRange = false;\n    }\n    this._comparisonRangeStart = this._getCellCompareValue(this.comparisonStart);\n    this._comparisonRangeEnd = this._getCellCompareValue(this.comparisonEnd);\n  }\n  /** Gets whether a date can be selected in the month view. */\n  _canSelect(date) {\n    return !this.dateFilter || this.dateFilter(date);\n  }\n  /** Clears out preview state. */\n  _clearPreview() {\n    this._previewStart = this._previewEnd = null;\n  }\n  static ɵfac = function MatMonthView_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatMonthView)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatMonthView,\n    selectors: [[\"mat-month-view\"]],\n    viewQuery: function MatMonthView_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatCalendarBody, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._matCalendarBody = _t.first);\n      }\n    },\n    inputs: {\n      activeDate: \"activeDate\",\n      selected: \"selected\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      dateFilter: \"dateFilter\",\n      dateClass: \"dateClass\",\n      comparisonStart: \"comparisonStart\",\n      comparisonEnd: \"comparisonEnd\",\n      startDateAccessibleName: \"startDateAccessibleName\",\n      endDateAccessibleName: \"endDateAccessibleName\",\n      activeDrag: \"activeDrag\"\n    },\n    outputs: {\n      selectedChange: \"selectedChange\",\n      _userSelection: \"_userSelection\",\n      dragStarted: \"dragStarted\",\n      dragEnded: \"dragEnded\",\n      activeDateChange: \"activeDateChange\"\n    },\n    exportAs: [\"matMonthView\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 8,\n    vars: 14,\n    consts: [[\"role\", \"grid\", 1, \"mat-calendar-table\"], [1, \"mat-calendar-table-header\"], [\"scope\", \"col\"], [\"aria-hidden\", \"true\"], [\"colspan\", \"7\", 1, \"mat-calendar-table-header-divider\"], [\"mat-calendar-body\", \"\", 3, \"selectedValueChange\", \"activeDateChange\", \"previewChange\", \"dragStarted\", \"dragEnded\", \"keyup\", \"keydown\", \"label\", \"rows\", \"todayValue\", \"startValue\", \"endValue\", \"comparisonStart\", \"comparisonEnd\", \"previewStart\", \"previewEnd\", \"isRange\", \"labelMinRequiredCells\", \"activeCell\", \"startDateAccessibleName\", \"endDateAccessibleName\"], [1, \"cdk-visually-hidden\"]],\n    template: function MatMonthView_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"table\", 0)(1, \"thead\", 1)(2, \"tr\");\n        i0.ɵɵrepeaterCreate(3, MatMonthView_For_4_Template, 5, 2, \"th\", 2, _forTrack1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"tr\", 3);\n        i0.ɵɵelement(6, \"th\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"tbody\", 5);\n        i0.ɵɵlistener(\"selectedValueChange\", function MatMonthView_Template_tbody_selectedValueChange_7_listener($event) {\n          return ctx._dateSelected($event);\n        })(\"activeDateChange\", function MatMonthView_Template_tbody_activeDateChange_7_listener($event) {\n          return ctx._updateActiveDate($event);\n        })(\"previewChange\", function MatMonthView_Template_tbody_previewChange_7_listener($event) {\n          return ctx._previewChanged($event);\n        })(\"dragStarted\", function MatMonthView_Template_tbody_dragStarted_7_listener($event) {\n          return ctx.dragStarted.emit($event);\n        })(\"dragEnded\", function MatMonthView_Template_tbody_dragEnded_7_listener($event) {\n          return ctx._dragEnded($event);\n        })(\"keyup\", function MatMonthView_Template_tbody_keyup_7_listener($event) {\n          return ctx._handleCalendarBodyKeyup($event);\n        })(\"keydown\", function MatMonthView_Template_tbody_keydown_7_listener($event) {\n          return ctx._handleCalendarBodyKeydown($event);\n        });\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵrepeater(ctx._weekdays);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"label\", ctx._monthLabel)(\"rows\", ctx._weeks)(\"todayValue\", ctx._todayDate)(\"startValue\", ctx._rangeStart)(\"endValue\", ctx._rangeEnd)(\"comparisonStart\", ctx._comparisonRangeStart)(\"comparisonEnd\", ctx._comparisonRangeEnd)(\"previewStart\", ctx._previewStart)(\"previewEnd\", ctx._previewEnd)(\"isRange\", ctx._isRange)(\"labelMinRequiredCells\", 3)(\"activeCell\", ctx._dateAdapter.getDate(ctx.activeDate) - 1)(\"startDateAccessibleName\", ctx.startDateAccessibleName)(\"endDateAccessibleName\", ctx.endDateAccessibleName);\n      }\n    },\n    dependencies: [MatCalendarBody],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMonthView, [{\n    type: Component,\n    args: [{\n      selector: 'mat-month-view',\n      exportAs: 'matMonthView',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatCalendarBody],\n      template: \"<table class=\\\"mat-calendar-table\\\" role=\\\"grid\\\">\\n  <thead class=\\\"mat-calendar-table-header\\\">\\n    <tr>\\n      @for (day of _weekdays; track day.id) {\\n        <th scope=\\\"col\\\">\\n          <span class=\\\"cdk-visually-hidden\\\">{{day.long}}</span>\\n          <span aria-hidden=\\\"true\\\">{{day.narrow}}</span>\\n        </th>\\n      }\\n    </tr>\\n    <tr aria-hidden=\\\"true\\\"><th class=\\\"mat-calendar-table-header-divider\\\" colspan=\\\"7\\\"></th></tr>\\n  </thead>\\n  <tbody mat-calendar-body\\n         [label]=\\\"_monthLabel\\\"\\n         [rows]=\\\"_weeks\\\"\\n         [todayValue]=\\\"_todayDate!\\\"\\n         [startValue]=\\\"_rangeStart!\\\"\\n         [endValue]=\\\"_rangeEnd!\\\"\\n         [comparisonStart]=\\\"_comparisonRangeStart\\\"\\n         [comparisonEnd]=\\\"_comparisonRangeEnd\\\"\\n         [previewStart]=\\\"_previewStart\\\"\\n         [previewEnd]=\\\"_previewEnd\\\"\\n         [isRange]=\\\"_isRange\\\"\\n         [labelMinRequiredCells]=\\\"3\\\"\\n         [activeCell]=\\\"_dateAdapter.getDate(activeDate) - 1\\\"\\n         [startDateAccessibleName]=\\\"startDateAccessibleName\\\"\\n         [endDateAccessibleName]=\\\"endDateAccessibleName\\\"\\n         (selectedValueChange)=\\\"_dateSelected($event)\\\"\\n         (activeDateChange)=\\\"_updateActiveDate($event)\\\"\\n         (previewChange)=\\\"_previewChanged($event)\\\"\\n         (dragStarted)=\\\"dragStarted.emit($event)\\\"\\n         (dragEnded)=\\\"_dragEnded($event)\\\"\\n         (keyup)=\\\"_handleCalendarBodyKeyup($event)\\\"\\n         (keydown)=\\\"_handleCalendarBodyKeydown($event)\\\">\\n  </tbody>\\n</table>\\n\"\n    }]\n  }], () => [], {\n    activeDate: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    minDate: [{\n      type: Input\n    }],\n    maxDate: [{\n      type: Input\n    }],\n    dateFilter: [{\n      type: Input\n    }],\n    dateClass: [{\n      type: Input\n    }],\n    comparisonStart: [{\n      type: Input\n    }],\n    comparisonEnd: [{\n      type: Input\n    }],\n    startDateAccessibleName: [{\n      type: Input\n    }],\n    endDateAccessibleName: [{\n      type: Input\n    }],\n    activeDrag: [{\n      type: Input\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    _userSelection: [{\n      type: Output\n    }],\n    dragStarted: [{\n      type: Output\n    }],\n    dragEnded: [{\n      type: Output\n    }],\n    activeDateChange: [{\n      type: Output\n    }],\n    _matCalendarBody: [{\n      type: ViewChild,\n      args: [MatCalendarBody]\n    }]\n  });\n})();\nconst yearsPerPage = 24;\nconst yearsPerRow = 4;\n/**\n * An internal component used to display a year selector in the datepicker.\n * @docs-private\n */\nclass MatMultiYearView {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _rerenderSubscription = Subscription.EMPTY;\n  /** Flag used to filter out space/enter keyup events that originated outside of the view. */\n  _selectionKeyPressed;\n  /** The date to display in this multi-year view (everything other than the year is ignored). */\n  get activeDate() {\n    return this._activeDate;\n  }\n  set activeDate(value) {\n    let oldActiveDate = this._activeDate;\n    const validDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value)) || this._dateAdapter.today();\n    this._activeDate = this._dateAdapter.clampDate(validDate, this.minDate, this.maxDate);\n    if (!isSameMultiYearView(this._dateAdapter, oldActiveDate, this._activeDate, this.minDate, this.maxDate)) {\n      this._init();\n    }\n  }\n  _activeDate;\n  /** The currently selected date. */\n  get selected() {\n    return this._selected;\n  }\n  set selected(value) {\n    if (value instanceof DateRange) {\n      this._selected = value;\n    } else {\n      this._selected = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    }\n    this._setSelectedYear(value);\n  }\n  _selected;\n  /** The minimum selectable date. */\n  get minDate() {\n    return this._minDate;\n  }\n  set minDate(value) {\n    this._minDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _minDate;\n  /** The maximum selectable date. */\n  get maxDate() {\n    return this._maxDate;\n  }\n  set maxDate(value) {\n    this._maxDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _maxDate;\n  /** A function used to filter which dates are selectable. */\n  dateFilter;\n  /** Function that can be used to add custom CSS classes to date cells. */\n  dateClass;\n  /** Emits when a new year is selected. */\n  selectedChange = new EventEmitter();\n  /** Emits the selected year. This doesn't imply a change on the selected date */\n  yearSelected = new EventEmitter();\n  /** Emits when any date is activated. */\n  activeDateChange = new EventEmitter();\n  /** The body of calendar table */\n  _matCalendarBody;\n  /** Grid of calendar cells representing the currently displayed years. */\n  _years;\n  /** The year that today falls on. */\n  _todayYear;\n  /** The year of the selected date. Null if the selected date is null. */\n  _selectedYear;\n  constructor() {\n    if (!this._dateAdapter && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw createMissingDateImplError('DateAdapter');\n    }\n    this._activeDate = this._dateAdapter.today();\n  }\n  ngAfterContentInit() {\n    this._rerenderSubscription = this._dateAdapter.localeChanges.pipe(startWith(null)).subscribe(() => this._init());\n  }\n  ngOnDestroy() {\n    this._rerenderSubscription.unsubscribe();\n  }\n  /** Initializes this multi-year view. */\n  _init() {\n    this._todayYear = this._dateAdapter.getYear(this._dateAdapter.today());\n    // We want a range years such that we maximize the number of\n    // enabled dates visible at once. This prevents issues where the minimum year\n    // is the last item of a page OR the maximum year is the first item of a page.\n    // The offset from the active year to the \"slot\" for the starting year is the\n    // *actual* first rendered year in the multi-year view.\n    const activeYear = this._dateAdapter.getYear(this._activeDate);\n    const minYearOfPage = activeYear - getActiveOffset(this._dateAdapter, this.activeDate, this.minDate, this.maxDate);\n    this._years = [];\n    for (let i = 0, row = []; i < yearsPerPage; i++) {\n      row.push(minYearOfPage + i);\n      if (row.length == yearsPerRow) {\n        this._years.push(row.map(year => this._createCellForYear(year)));\n        row = [];\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Handles when a new year is selected. */\n  _yearSelected(event) {\n    const year = event.value;\n    const selectedYear = this._dateAdapter.createDate(year, 0, 1);\n    const selectedDate = this._getDateFromYear(year);\n    this.yearSelected.emit(selectedYear);\n    this.selectedChange.emit(selectedDate);\n  }\n  /**\n   * Takes the index of a calendar body cell wrapped in an event as argument. For the date that\n   * corresponds to the given cell, set `activeDate` to that date and fire `activeDateChange` with\n   * that date.\n   *\n   * This function is used to match each component's model of the active date with the calendar\n   * body cell that was focused. It updates its value of `activeDate` synchronously and updates the\n   * parent's value asynchronously via the `activeDateChange` event. The child component receives an\n   * updated value asynchronously via the `activeCell` Input.\n   */\n  _updateActiveDate(event) {\n    const year = event.value;\n    const oldActiveDate = this._activeDate;\n    this.activeDate = this._getDateFromYear(year);\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n    }\n  }\n  /** Handles keydown events on the calendar body when calendar is in multi-year view. */\n  _handleCalendarBodyKeydown(event) {\n    const oldActiveDate = this._activeDate;\n    const isRtl = this._isRtl();\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, isRtl ? 1 : -1);\n        break;\n      case RIGHT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, isRtl ? -1 : 1);\n        break;\n      case UP_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, -yearsPerRow);\n        break;\n      case DOWN_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, yearsPerRow);\n        break;\n      case HOME:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, -getActiveOffset(this._dateAdapter, this.activeDate, this.minDate, this.maxDate));\n        break;\n      case END:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, yearsPerPage - getActiveOffset(this._dateAdapter, this.activeDate, this.minDate, this.maxDate) - 1);\n        break;\n      case PAGE_UP:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, event.altKey ? -yearsPerPage * 10 : -yearsPerPage);\n        break;\n      case PAGE_DOWN:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, event.altKey ? yearsPerPage * 10 : yearsPerPage);\n        break;\n      case ENTER:\n      case SPACE:\n        // Note that we only prevent the default action here while the selection happens in\n        // `keyup` below. We can't do the selection here, because it can cause the calendar to\n        // reopen if focus is restored immediately. We also can't call `preventDefault` on `keyup`\n        // because it's too late (see #23305).\n        this._selectionKeyPressed = true;\n        break;\n      default:\n        // Don't prevent default or focus active cell on keys that we don't explicitly handle.\n        return;\n    }\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n    }\n    this._focusActiveCellAfterViewChecked();\n    // Prevent unexpected default actions such as form submission.\n    event.preventDefault();\n  }\n  /** Handles keyup events on the calendar body when calendar is in multi-year view. */\n  _handleCalendarBodyKeyup(event) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this._selectionKeyPressed) {\n        this._yearSelected({\n          value: this._dateAdapter.getYear(this._activeDate),\n          event\n        });\n      }\n      this._selectionKeyPressed = false;\n    }\n  }\n  _getActiveCell() {\n    return getActiveOffset(this._dateAdapter, this.activeDate, this.minDate, this.maxDate);\n  }\n  /** Focuses the active cell after the microtask queue is empty. */\n  _focusActiveCell() {\n    this._matCalendarBody._focusActiveCell();\n  }\n  /** Focuses the active cell after change detection has run and the microtask queue is empty. */\n  _focusActiveCellAfterViewChecked() {\n    this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked();\n  }\n  /**\n   * Takes a year and returns a new date on the same day and month as the currently active date\n   *  The returned date will have the same year as the argument date.\n   */\n  _getDateFromYear(year) {\n    const activeMonth = this._dateAdapter.getMonth(this.activeDate);\n    const daysInMonth = this._dateAdapter.getNumDaysInMonth(this._dateAdapter.createDate(year, activeMonth, 1));\n    const normalizedDate = this._dateAdapter.createDate(year, activeMonth, Math.min(this._dateAdapter.getDate(this.activeDate), daysInMonth));\n    return normalizedDate;\n  }\n  /** Creates an MatCalendarCell for the given year. */\n  _createCellForYear(year) {\n    const date = this._dateAdapter.createDate(year, 0, 1);\n    const yearName = this._dateAdapter.getYearName(date);\n    const cellClasses = this.dateClass ? this.dateClass(date, 'multi-year') : undefined;\n    return new MatCalendarCell(year, yearName, yearName, this._shouldEnableYear(year), cellClasses);\n  }\n  /** Whether the given year is enabled. */\n  _shouldEnableYear(year) {\n    // disable if the year is greater than maxDate lower than minDate\n    if (year === undefined || year === null || this.maxDate && year > this._dateAdapter.getYear(this.maxDate) || this.minDate && year < this._dateAdapter.getYear(this.minDate)) {\n      return false;\n    }\n    // enable if it reaches here and there's no filter defined\n    if (!this.dateFilter) {\n      return true;\n    }\n    const firstOfYear = this._dateAdapter.createDate(year, 0, 1);\n    // If any date in the year is enabled count the year as enabled.\n    for (let date = firstOfYear; this._dateAdapter.getYear(date) == year; date = this._dateAdapter.addCalendarDays(date, 1)) {\n      if (this.dateFilter(date)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /** Determines whether the user has the RTL layout direction. */\n  _isRtl() {\n    return this._dir && this._dir.value === 'rtl';\n  }\n  /** Sets the currently-highlighted year based on a model value. */\n  _setSelectedYear(value) {\n    this._selectedYear = null;\n    if (value instanceof DateRange) {\n      const displayValue = value.start || value.end;\n      if (displayValue) {\n        this._selectedYear = this._dateAdapter.getYear(displayValue);\n      }\n    } else if (value) {\n      this._selectedYear = this._dateAdapter.getYear(value);\n    }\n  }\n  static ɵfac = function MatMultiYearView_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatMultiYearView)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatMultiYearView,\n    selectors: [[\"mat-multi-year-view\"]],\n    viewQuery: function MatMultiYearView_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatCalendarBody, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._matCalendarBody = _t.first);\n      }\n    },\n    inputs: {\n      activeDate: \"activeDate\",\n      selected: \"selected\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      dateFilter: \"dateFilter\",\n      dateClass: \"dateClass\"\n    },\n    outputs: {\n      selectedChange: \"selectedChange\",\n      yearSelected: \"yearSelected\",\n      activeDateChange: \"activeDateChange\"\n    },\n    exportAs: [\"matMultiYearView\"],\n    decls: 5,\n    vars: 7,\n    consts: [[\"role\", \"grid\", 1, \"mat-calendar-table\"], [\"aria-hidden\", \"true\", 1, \"mat-calendar-table-header\"], [\"colspan\", \"4\", 1, \"mat-calendar-table-header-divider\"], [\"mat-calendar-body\", \"\", 3, \"selectedValueChange\", \"activeDateChange\", \"keyup\", \"keydown\", \"rows\", \"todayValue\", \"startValue\", \"endValue\", \"numCols\", \"cellAspectRatio\", \"activeCell\"]],\n    template: function MatMultiYearView_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"table\", 0)(1, \"thead\", 1)(2, \"tr\");\n        i0.ɵɵelement(3, \"th\", 2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"tbody\", 3);\n        i0.ɵɵlistener(\"selectedValueChange\", function MatMultiYearView_Template_tbody_selectedValueChange_4_listener($event) {\n          return ctx._yearSelected($event);\n        })(\"activeDateChange\", function MatMultiYearView_Template_tbody_activeDateChange_4_listener($event) {\n          return ctx._updateActiveDate($event);\n        })(\"keyup\", function MatMultiYearView_Template_tbody_keyup_4_listener($event) {\n          return ctx._handleCalendarBodyKeyup($event);\n        })(\"keydown\", function MatMultiYearView_Template_tbody_keydown_4_listener($event) {\n          return ctx._handleCalendarBodyKeydown($event);\n        });\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"rows\", ctx._years)(\"todayValue\", ctx._todayYear)(\"startValue\", ctx._selectedYear)(\"endValue\", ctx._selectedYear)(\"numCols\", 4)(\"cellAspectRatio\", 4 / 7)(\"activeCell\", ctx._getActiveCell());\n      }\n    },\n    dependencies: [MatCalendarBody],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMultiYearView, [{\n    type: Component,\n    args: [{\n      selector: 'mat-multi-year-view',\n      exportAs: 'matMultiYearView',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatCalendarBody],\n      template: \"<table class=\\\"mat-calendar-table\\\" role=\\\"grid\\\">\\n  <thead aria-hidden=\\\"true\\\" class=\\\"mat-calendar-table-header\\\">\\n    <tr><th class=\\\"mat-calendar-table-header-divider\\\" colspan=\\\"4\\\"></th></tr>\\n  </thead>\\n  <tbody mat-calendar-body\\n         [rows]=\\\"_years\\\"\\n         [todayValue]=\\\"_todayYear\\\"\\n         [startValue]=\\\"_selectedYear!\\\"\\n         [endValue]=\\\"_selectedYear!\\\"\\n         [numCols]=\\\"4\\\"\\n         [cellAspectRatio]=\\\"4 / 7\\\"\\n         [activeCell]=\\\"_getActiveCell()\\\"\\n         (selectedValueChange)=\\\"_yearSelected($event)\\\"\\n         (activeDateChange)=\\\"_updateActiveDate($event)\\\"\\n         (keyup)=\\\"_handleCalendarBodyKeyup($event)\\\"\\n         (keydown)=\\\"_handleCalendarBodyKeydown($event)\\\">\\n  </tbody>\\n</table>\\n\"\n    }]\n  }], () => [], {\n    activeDate: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    minDate: [{\n      type: Input\n    }],\n    maxDate: [{\n      type: Input\n    }],\n    dateFilter: [{\n      type: Input\n    }],\n    dateClass: [{\n      type: Input\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    yearSelected: [{\n      type: Output\n    }],\n    activeDateChange: [{\n      type: Output\n    }],\n    _matCalendarBody: [{\n      type: ViewChild,\n      args: [MatCalendarBody]\n    }]\n  });\n})();\nfunction isSameMultiYearView(dateAdapter, date1, date2, minDate, maxDate) {\n  const year1 = dateAdapter.getYear(date1);\n  const year2 = dateAdapter.getYear(date2);\n  const startingYear = getStartingYear(dateAdapter, minDate, maxDate);\n  return Math.floor((year1 - startingYear) / yearsPerPage) === Math.floor((year2 - startingYear) / yearsPerPage);\n}\n/**\n * When the multi-year view is first opened, the active year will be in view.\n * So we compute how many years are between the active year and the *slot* where our\n * \"startingYear\" will render when paged into view.\n */\nfunction getActiveOffset(dateAdapter, activeDate, minDate, maxDate) {\n  const activeYear = dateAdapter.getYear(activeDate);\n  return euclideanModulo(activeYear - getStartingYear(dateAdapter, minDate, maxDate), yearsPerPage);\n}\n/**\n * We pick a \"starting\" year such that either the maximum year would be at the end\n * or the minimum year would be at the beginning of a page.\n */\nfunction getStartingYear(dateAdapter, minDate, maxDate) {\n  let startingYear = 0;\n  if (maxDate) {\n    const maxYear = dateAdapter.getYear(maxDate);\n    startingYear = maxYear - yearsPerPage + 1;\n  } else if (minDate) {\n    startingYear = dateAdapter.getYear(minDate);\n  }\n  return startingYear;\n}\n/** Gets remainder that is non-negative, even if first number is negative */\nfunction euclideanModulo(a, b) {\n  return (a % b + b) % b;\n}\n\n/**\n * An internal component used to display a single year in the datepicker.\n * @docs-private\n */\nclass MatYearView {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _dateFormats = inject(MAT_DATE_FORMATS, {\n    optional: true\n  });\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _rerenderSubscription = Subscription.EMPTY;\n  /** Flag used to filter out space/enter keyup events that originated outside of the view. */\n  _selectionKeyPressed;\n  /** The date to display in this year view (everything other than the year is ignored). */\n  get activeDate() {\n    return this._activeDate;\n  }\n  set activeDate(value) {\n    let oldActiveDate = this._activeDate;\n    const validDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value)) || this._dateAdapter.today();\n    this._activeDate = this._dateAdapter.clampDate(validDate, this.minDate, this.maxDate);\n    if (this._dateAdapter.getYear(oldActiveDate) !== this._dateAdapter.getYear(this._activeDate)) {\n      this._init();\n    }\n  }\n  _activeDate;\n  /** The currently selected date. */\n  get selected() {\n    return this._selected;\n  }\n  set selected(value) {\n    if (value instanceof DateRange) {\n      this._selected = value;\n    } else {\n      this._selected = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    }\n    this._setSelectedMonth(value);\n  }\n  _selected;\n  /** The minimum selectable date. */\n  get minDate() {\n    return this._minDate;\n  }\n  set minDate(value) {\n    this._minDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _minDate;\n  /** The maximum selectable date. */\n  get maxDate() {\n    return this._maxDate;\n  }\n  set maxDate(value) {\n    this._maxDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _maxDate;\n  /** A function used to filter which dates are selectable. */\n  dateFilter;\n  /** Function that can be used to add custom CSS classes to date cells. */\n  dateClass;\n  /** Emits when a new month is selected. */\n  selectedChange = new EventEmitter();\n  /** Emits the selected month. This doesn't imply a change on the selected date */\n  monthSelected = new EventEmitter();\n  /** Emits when any date is activated. */\n  activeDateChange = new EventEmitter();\n  /** The body of calendar table */\n  _matCalendarBody;\n  /** Grid of calendar cells representing the months of the year. */\n  _months;\n  /** The label for this year (e.g. \"2017\"). */\n  _yearLabel;\n  /** The month in this year that today falls on. Null if today is in a different year. */\n  _todayMonth;\n  /**\n   * The month in this year that the selected Date falls on.\n   * Null if the selected Date is in a different year.\n   */\n  _selectedMonth;\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._dateAdapter) {\n        throw createMissingDateImplError('DateAdapter');\n      }\n      if (!this._dateFormats) {\n        throw createMissingDateImplError('MAT_DATE_FORMATS');\n      }\n    }\n    this._activeDate = this._dateAdapter.today();\n  }\n  ngAfterContentInit() {\n    this._rerenderSubscription = this._dateAdapter.localeChanges.pipe(startWith(null)).subscribe(() => this._init());\n  }\n  ngOnDestroy() {\n    this._rerenderSubscription.unsubscribe();\n  }\n  /** Handles when a new month is selected. */\n  _monthSelected(event) {\n    const month = event.value;\n    const selectedMonth = this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate), month, 1);\n    this.monthSelected.emit(selectedMonth);\n    const selectedDate = this._getDateFromMonth(month);\n    this.selectedChange.emit(selectedDate);\n  }\n  /**\n   * Takes the index of a calendar body cell wrapped in an event as argument. For the date that\n   * corresponds to the given cell, set `activeDate` to that date and fire `activeDateChange` with\n   * that date.\n   *\n   * This function is used to match each component's model of the active date with the calendar\n   * body cell that was focused. It updates its value of `activeDate` synchronously and updates the\n   * parent's value asynchronously via the `activeDateChange` event. The child component receives an\n   * updated value asynchronously via the `activeCell` Input.\n   */\n  _updateActiveDate(event) {\n    const month = event.value;\n    const oldActiveDate = this._activeDate;\n    this.activeDate = this._getDateFromMonth(month);\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n    }\n  }\n  /** Handles keydown events on the calendar body when calendar is in year view. */\n  _handleCalendarBodyKeydown(event) {\n    // TODO(mmalerba): We currently allow keyboard navigation to disabled dates, but just prevent\n    // disabled ones from being selected. This may not be ideal, we should look into whether\n    // navigation should skip over disabled dates, and if so, how to implement that efficiently.\n    const oldActiveDate = this._activeDate;\n    const isRtl = this._isRtl();\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, isRtl ? 1 : -1);\n        break;\n      case RIGHT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, isRtl ? -1 : 1);\n        break;\n      case UP_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, -4);\n        break;\n      case DOWN_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, 4);\n        break;\n      case HOME:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, -this._dateAdapter.getMonth(this._activeDate));\n        break;\n      case END:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, 11 - this._dateAdapter.getMonth(this._activeDate));\n        break;\n      case PAGE_UP:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, event.altKey ? -10 : -1);\n        break;\n      case PAGE_DOWN:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, event.altKey ? 10 : 1);\n        break;\n      case ENTER:\n      case SPACE:\n        // Note that we only prevent the default action here while the selection happens in\n        // `keyup` below. We can't do the selection here, because it can cause the calendar to\n        // reopen if focus is restored immediately. We also can't call `preventDefault` on `keyup`\n        // because it's too late (see #23305).\n        this._selectionKeyPressed = true;\n        break;\n      default:\n        // Don't prevent default or focus active cell on keys that we don't explicitly handle.\n        return;\n    }\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n      this._focusActiveCellAfterViewChecked();\n    }\n    // Prevent unexpected default actions such as form submission.\n    event.preventDefault();\n  }\n  /** Handles keyup events on the calendar body when calendar is in year view. */\n  _handleCalendarBodyKeyup(event) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this._selectionKeyPressed) {\n        this._monthSelected({\n          value: this._dateAdapter.getMonth(this._activeDate),\n          event\n        });\n      }\n      this._selectionKeyPressed = false;\n    }\n  }\n  /** Initializes this year view. */\n  _init() {\n    this._setSelectedMonth(this.selected);\n    this._todayMonth = this._getMonthInCurrentYear(this._dateAdapter.today());\n    this._yearLabel = this._dateAdapter.getYearName(this.activeDate);\n    let monthNames = this._dateAdapter.getMonthNames('short');\n    // First row of months only contains 5 elements so we can fit the year label on the same row.\n    this._months = [[0, 1, 2, 3], [4, 5, 6, 7], [8, 9, 10, 11]].map(row => row.map(month => this._createCellForMonth(month, monthNames[month])));\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Focuses the active cell after the microtask queue is empty. */\n  _focusActiveCell() {\n    this._matCalendarBody._focusActiveCell();\n  }\n  /** Schedules the matCalendarBody to focus the active cell after change detection has run */\n  _focusActiveCellAfterViewChecked() {\n    this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked();\n  }\n  /**\n   * Gets the month in this year that the given Date falls on.\n   * Returns null if the given Date is in another year.\n   */\n  _getMonthInCurrentYear(date) {\n    return date && this._dateAdapter.getYear(date) == this._dateAdapter.getYear(this.activeDate) ? this._dateAdapter.getMonth(date) : null;\n  }\n  /**\n   * Takes a month and returns a new date in the same day and year as the currently active date.\n   *  The returned date will have the same month as the argument date.\n   */\n  _getDateFromMonth(month) {\n    const normalizedDate = this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate), month, 1);\n    const daysInMonth = this._dateAdapter.getNumDaysInMonth(normalizedDate);\n    return this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate), month, Math.min(this._dateAdapter.getDate(this.activeDate), daysInMonth));\n  }\n  /** Creates an MatCalendarCell for the given month. */\n  _createCellForMonth(month, monthName) {\n    const date = this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate), month, 1);\n    const ariaLabel = this._dateAdapter.format(date, this._dateFormats.display.monthYearA11yLabel);\n    const cellClasses = this.dateClass ? this.dateClass(date, 'year') : undefined;\n    return new MatCalendarCell(month, monthName.toLocaleUpperCase(), ariaLabel, this._shouldEnableMonth(month), cellClasses);\n  }\n  /** Whether the given month is enabled. */\n  _shouldEnableMonth(month) {\n    const activeYear = this._dateAdapter.getYear(this.activeDate);\n    if (month === undefined || month === null || this._isYearAndMonthAfterMaxDate(activeYear, month) || this._isYearAndMonthBeforeMinDate(activeYear, month)) {\n      return false;\n    }\n    if (!this.dateFilter) {\n      return true;\n    }\n    const firstOfMonth = this._dateAdapter.createDate(activeYear, month, 1);\n    // If any date in the month is enabled count the month as enabled.\n    for (let date = firstOfMonth; this._dateAdapter.getMonth(date) == month; date = this._dateAdapter.addCalendarDays(date, 1)) {\n      if (this.dateFilter(date)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /**\n   * Tests whether the combination month/year is after this.maxDate, considering\n   * just the month and year of this.maxDate\n   */\n  _isYearAndMonthAfterMaxDate(year, month) {\n    if (this.maxDate) {\n      const maxYear = this._dateAdapter.getYear(this.maxDate);\n      const maxMonth = this._dateAdapter.getMonth(this.maxDate);\n      return year > maxYear || year === maxYear && month > maxMonth;\n    }\n    return false;\n  }\n  /**\n   * Tests whether the combination month/year is before this.minDate, considering\n   * just the month and year of this.minDate\n   */\n  _isYearAndMonthBeforeMinDate(year, month) {\n    if (this.minDate) {\n      const minYear = this._dateAdapter.getYear(this.minDate);\n      const minMonth = this._dateAdapter.getMonth(this.minDate);\n      return year < minYear || year === minYear && month < minMonth;\n    }\n    return false;\n  }\n  /** Determines whether the user has the RTL layout direction. */\n  _isRtl() {\n    return this._dir && this._dir.value === 'rtl';\n  }\n  /** Sets the currently-selected month based on a model value. */\n  _setSelectedMonth(value) {\n    if (value instanceof DateRange) {\n      this._selectedMonth = this._getMonthInCurrentYear(value.start) || this._getMonthInCurrentYear(value.end);\n    } else {\n      this._selectedMonth = this._getMonthInCurrentYear(value);\n    }\n  }\n  static ɵfac = function MatYearView_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatYearView)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatYearView,\n    selectors: [[\"mat-year-view\"]],\n    viewQuery: function MatYearView_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatCalendarBody, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._matCalendarBody = _t.first);\n      }\n    },\n    inputs: {\n      activeDate: \"activeDate\",\n      selected: \"selected\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      dateFilter: \"dateFilter\",\n      dateClass: \"dateClass\"\n    },\n    outputs: {\n      selectedChange: \"selectedChange\",\n      monthSelected: \"monthSelected\",\n      activeDateChange: \"activeDateChange\"\n    },\n    exportAs: [\"matYearView\"],\n    decls: 5,\n    vars: 9,\n    consts: [[\"role\", \"grid\", 1, \"mat-calendar-table\"], [\"aria-hidden\", \"true\", 1, \"mat-calendar-table-header\"], [\"colspan\", \"4\", 1, \"mat-calendar-table-header-divider\"], [\"mat-calendar-body\", \"\", 3, \"selectedValueChange\", \"activeDateChange\", \"keyup\", \"keydown\", \"label\", \"rows\", \"todayValue\", \"startValue\", \"endValue\", \"labelMinRequiredCells\", \"numCols\", \"cellAspectRatio\", \"activeCell\"]],\n    template: function MatYearView_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"table\", 0)(1, \"thead\", 1)(2, \"tr\");\n        i0.ɵɵelement(3, \"th\", 2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"tbody\", 3);\n        i0.ɵɵlistener(\"selectedValueChange\", function MatYearView_Template_tbody_selectedValueChange_4_listener($event) {\n          return ctx._monthSelected($event);\n        })(\"activeDateChange\", function MatYearView_Template_tbody_activeDateChange_4_listener($event) {\n          return ctx._updateActiveDate($event);\n        })(\"keyup\", function MatYearView_Template_tbody_keyup_4_listener($event) {\n          return ctx._handleCalendarBodyKeyup($event);\n        })(\"keydown\", function MatYearView_Template_tbody_keydown_4_listener($event) {\n          return ctx._handleCalendarBodyKeydown($event);\n        });\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"label\", ctx._yearLabel)(\"rows\", ctx._months)(\"todayValue\", ctx._todayMonth)(\"startValue\", ctx._selectedMonth)(\"endValue\", ctx._selectedMonth)(\"labelMinRequiredCells\", 2)(\"numCols\", 4)(\"cellAspectRatio\", 4 / 7)(\"activeCell\", ctx._dateAdapter.getMonth(ctx.activeDate));\n      }\n    },\n    dependencies: [MatCalendarBody],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatYearView, [{\n    type: Component,\n    args: [{\n      selector: 'mat-year-view',\n      exportAs: 'matYearView',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatCalendarBody],\n      template: \"<table class=\\\"mat-calendar-table\\\" role=\\\"grid\\\">\\n  <thead aria-hidden=\\\"true\\\" class=\\\"mat-calendar-table-header\\\">\\n    <tr><th class=\\\"mat-calendar-table-header-divider\\\" colspan=\\\"4\\\"></th></tr>\\n  </thead>\\n  <tbody mat-calendar-body\\n         [label]=\\\"_yearLabel\\\"\\n         [rows]=\\\"_months\\\"\\n         [todayValue]=\\\"_todayMonth!\\\"\\n         [startValue]=\\\"_selectedMonth!\\\"\\n         [endValue]=\\\"_selectedMonth!\\\"\\n         [labelMinRequiredCells]=\\\"2\\\"\\n         [numCols]=\\\"4\\\"\\n         [cellAspectRatio]=\\\"4 / 7\\\"\\n         [activeCell]=\\\"_dateAdapter.getMonth(activeDate)\\\"\\n         (selectedValueChange)=\\\"_monthSelected($event)\\\"\\n         (activeDateChange)=\\\"_updateActiveDate($event)\\\"\\n         (keyup)=\\\"_handleCalendarBodyKeyup($event)\\\"\\n         (keydown)=\\\"_handleCalendarBodyKeydown($event)\\\">\\n  </tbody>\\n</table>\\n\"\n    }]\n  }], () => [], {\n    activeDate: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    minDate: [{\n      type: Input\n    }],\n    maxDate: [{\n      type: Input\n    }],\n    dateFilter: [{\n      type: Input\n    }],\n    dateClass: [{\n      type: Input\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    monthSelected: [{\n      type: Output\n    }],\n    activeDateChange: [{\n      type: Output\n    }],\n    _matCalendarBody: [{\n      type: ViewChild,\n      args: [MatCalendarBody]\n    }]\n  });\n})();\n\n/** Default header for MatCalendar */\nclass MatCalendarHeader {\n  _intl = inject(MatDatepickerIntl);\n  calendar = inject(MatCalendar);\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _dateFormats = inject(MAT_DATE_FORMATS, {\n    optional: true\n  });\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    const changeDetectorRef = inject(ChangeDetectorRef);\n    this.calendar.stateChanges.subscribe(() => changeDetectorRef.markForCheck());\n  }\n  /** The display text for the current calendar view. */\n  get periodButtonText() {\n    if (this.calendar.currentView == 'month') {\n      return this._dateAdapter.format(this.calendar.activeDate, this._dateFormats.display.monthYearLabel).toLocaleUpperCase();\n    }\n    if (this.calendar.currentView == 'year') {\n      return this._dateAdapter.getYearName(this.calendar.activeDate);\n    }\n    return this._intl.formatYearRange(...this._formatMinAndMaxYearLabels());\n  }\n  /** The aria description for the current calendar view. */\n  get periodButtonDescription() {\n    if (this.calendar.currentView == 'month') {\n      return this._dateAdapter.format(this.calendar.activeDate, this._dateFormats.display.monthYearLabel).toLocaleUpperCase();\n    }\n    if (this.calendar.currentView == 'year') {\n      return this._dateAdapter.getYearName(this.calendar.activeDate);\n    }\n    // Format a label for the window of years displayed in the multi-year calendar view. Use\n    // `formatYearRangeLabel` because it is TTS friendly.\n    return this._intl.formatYearRangeLabel(...this._formatMinAndMaxYearLabels());\n  }\n  /** The `aria-label` for changing the calendar view. */\n  get periodButtonLabel() {\n    return this.calendar.currentView == 'month' ? this._intl.switchToMultiYearViewLabel : this._intl.switchToMonthViewLabel;\n  }\n  /** The label for the previous button. */\n  get prevButtonLabel() {\n    return {\n      'month': this._intl.prevMonthLabel,\n      'year': this._intl.prevYearLabel,\n      'multi-year': this._intl.prevMultiYearLabel\n    }[this.calendar.currentView];\n  }\n  /** The label for the next button. */\n  get nextButtonLabel() {\n    return {\n      'month': this._intl.nextMonthLabel,\n      'year': this._intl.nextYearLabel,\n      'multi-year': this._intl.nextMultiYearLabel\n    }[this.calendar.currentView];\n  }\n  /** Handles user clicks on the period label. */\n  currentPeriodClicked() {\n    this.calendar.currentView = this.calendar.currentView == 'month' ? 'multi-year' : 'month';\n  }\n  /** Handles user clicks on the previous button. */\n  previousClicked() {\n    this.calendar.activeDate = this.calendar.currentView == 'month' ? this._dateAdapter.addCalendarMonths(this.calendar.activeDate, -1) : this._dateAdapter.addCalendarYears(this.calendar.activeDate, this.calendar.currentView == 'year' ? -1 : -yearsPerPage);\n  }\n  /** Handles user clicks on the next button. */\n  nextClicked() {\n    this.calendar.activeDate = this.calendar.currentView == 'month' ? this._dateAdapter.addCalendarMonths(this.calendar.activeDate, 1) : this._dateAdapter.addCalendarYears(this.calendar.activeDate, this.calendar.currentView == 'year' ? 1 : yearsPerPage);\n  }\n  /** Whether the previous period button is enabled. */\n  previousEnabled() {\n    if (!this.calendar.minDate) {\n      return true;\n    }\n    return !this.calendar.minDate || !this._isSameView(this.calendar.activeDate, this.calendar.minDate);\n  }\n  /** Whether the next period button is enabled. */\n  nextEnabled() {\n    return !this.calendar.maxDate || !this._isSameView(this.calendar.activeDate, this.calendar.maxDate);\n  }\n  /** Whether the two dates represent the same view in the current view mode (month or year). */\n  _isSameView(date1, date2) {\n    if (this.calendar.currentView == 'month') {\n      return this._dateAdapter.getYear(date1) == this._dateAdapter.getYear(date2) && this._dateAdapter.getMonth(date1) == this._dateAdapter.getMonth(date2);\n    }\n    if (this.calendar.currentView == 'year') {\n      return this._dateAdapter.getYear(date1) == this._dateAdapter.getYear(date2);\n    }\n    // Otherwise we are in 'multi-year' view.\n    return isSameMultiYearView(this._dateAdapter, date1, date2, this.calendar.minDate, this.calendar.maxDate);\n  }\n  /**\n   * Format two individual labels for the minimum year and maximum year available in the multi-year\n   * calendar view. Returns an array of two strings where the first string is the formatted label\n   * for the minimum year, and the second string is the formatted label for the maximum year.\n   */\n  _formatMinAndMaxYearLabels() {\n    // The offset from the active year to the \"slot\" for the starting year is the\n    // *actual* first rendered year in the multi-year view, and the last year is\n    // just yearsPerPage - 1 away.\n    const activeYear = this._dateAdapter.getYear(this.calendar.activeDate);\n    const minYearOfPage = activeYear - getActiveOffset(this._dateAdapter, this.calendar.activeDate, this.calendar.minDate, this.calendar.maxDate);\n    const maxYearOfPage = minYearOfPage + yearsPerPage - 1;\n    const minYearLabel = this._dateAdapter.getYearName(this._dateAdapter.createDate(minYearOfPage, 0, 1));\n    const maxYearLabel = this._dateAdapter.getYearName(this._dateAdapter.createDate(maxYearOfPage, 0, 1));\n    return [minYearLabel, maxYearLabel];\n  }\n  _periodButtonLabelId = inject(_IdGenerator).getId('mat-calendar-period-label-');\n  static ɵfac = function MatCalendarHeader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCalendarHeader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCalendarHeader,\n    selectors: [[\"mat-calendar-header\"]],\n    exportAs: [\"matCalendarHeader\"],\n    ngContentSelectors: _c1,\n    decls: 17,\n    vars: 11,\n    consts: [[1, \"mat-calendar-header\"], [1, \"mat-calendar-controls\"], [\"aria-live\", \"polite\", 1, \"cdk-visually-hidden\", 3, \"id\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"mat-calendar-period-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"viewBox\", \"0 0 10 5\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-calendar-arrow\"], [\"points\", \"0,0 5,5 10,0\"], [1, \"mat-calendar-spacer\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-calendar-previous-button\", 3, \"click\", \"disabled\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-calendar-next-button\", 3, \"click\", \"disabled\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"]],\n    template: function MatCalendarHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function MatCalendarHeader_Template_button_click_4_listener() {\n          return ctx.currentPeriodClicked();\n        });\n        i0.ɵɵelementStart(5, \"span\", 4);\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(7, \"svg\", 5);\n        i0.ɵɵelement(8, \"polygon\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelement(9, \"div\", 7);\n        i0.ɵɵprojection(10);\n        i0.ɵɵelementStart(11, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function MatCalendarHeader_Template_button_click_11_listener() {\n          return ctx.previousClicked();\n        });\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(12, \"svg\", 9);\n        i0.ɵɵelement(13, \"path\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(14, \"button\", 11);\n        i0.ɵɵlistener(\"click\", function MatCalendarHeader_Template_button_click_14_listener() {\n          return ctx.nextClicked();\n        });\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(15, \"svg\", 9);\n        i0.ɵɵelement(16, \"path\", 12);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"id\", ctx._periodButtonLabelId);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(ctx.periodButtonDescription);\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"aria-label\", ctx.periodButtonLabel)(\"aria-describedby\", ctx._periodButtonLabelId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.periodButtonText);\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"mat-calendar-invert\", ctx.calendar.currentView !== \"month\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", !ctx.previousEnabled());\n        i0.ɵɵattribute(\"aria-label\", ctx.prevButtonLabel);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", !ctx.nextEnabled());\n        i0.ɵɵattribute(\"aria-label\", ctx.nextButtonLabel);\n      }\n    },\n    dependencies: [MatButton, MatIconButton],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCalendarHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-calendar-header',\n      exportAs: 'matCalendarHeader',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatButton, MatIconButton],\n      template: \"<div class=\\\"mat-calendar-header\\\">\\n  <div class=\\\"mat-calendar-controls\\\">\\n    <!-- [Firefox Issue: https://bugzilla.mozilla.org/show_bug.cgi?id=1880533]\\n      Relocated label next to related button and made visually hidden via cdk-visually-hidden\\n      to enable label to appear in a11y tree for SR when using Firefox -->\\n    <span [id]=\\\"_periodButtonLabelId\\\" class=\\\"cdk-visually-hidden\\\" aria-live=\\\"polite\\\">{{periodButtonDescription}}</span>\\n    <button mat-button type=\\\"button\\\" class=\\\"mat-calendar-period-button\\\"\\n            (click)=\\\"currentPeriodClicked()\\\" [attr.aria-label]=\\\"periodButtonLabel\\\"\\n            [attr.aria-describedby]=\\\"_periodButtonLabelId\\\">\\n      <span aria-hidden=\\\"true\\\">{{periodButtonText}}</span>\\n      <svg class=\\\"mat-calendar-arrow\\\" [class.mat-calendar-invert]=\\\"calendar.currentView !== 'month'\\\"\\n           viewBox=\\\"0 0 10 5\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n           <polygon points=\\\"0,0 5,5 10,0\\\"/>\\n      </svg>\\n    </button>\\n\\n    <div class=\\\"mat-calendar-spacer\\\"></div>\\n\\n    <ng-content></ng-content>\\n\\n    <button mat-icon-button type=\\\"button\\\" class=\\\"mat-calendar-previous-button\\\"\\n            [disabled]=\\\"!previousEnabled()\\\" (click)=\\\"previousClicked()\\\"\\n            [attr.aria-label]=\\\"prevButtonLabel\\\">\\n      <svg viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n       </svg>\\n    </button>\\n\\n    <button mat-icon-button type=\\\"button\\\" class=\\\"mat-calendar-next-button\\\"\\n            [disabled]=\\\"!nextEnabled()\\\" (click)=\\\"nextClicked()\\\"\\n            [attr.aria-label]=\\\"nextButtonLabel\\\">\\n      <svg viewBox=\\\"0 0 24 24\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n      </svg>\\n    </button>\\n  </div>\\n</div>\\n\"\n    }]\n  }], () => [], null);\n})();\n/** A calendar that is used as part of the datepicker. */\nclass MatCalendar {\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _dateFormats = inject(MAT_DATE_FORMATS, {\n    optional: true\n  });\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  /** An input indicating the type of the header component, if set. */\n  headerComponent;\n  /** A portal containing the header component type for this calendar. */\n  _calendarHeaderPortal;\n  _intlChanges;\n  /**\n   * Used for scheduling that focus should be moved to the active cell on the next tick.\n   * We need to schedule it, rather than do it immediately, because we have to wait\n   * for Angular to re-evaluate the view children.\n   */\n  _moveFocusOnNextTick = false;\n  /** A date representing the period (month or year) to start the calendar in. */\n  get startAt() {\n    return this._startAt;\n  }\n  set startAt(value) {\n    this._startAt = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _startAt;\n  /** Whether the calendar should be started in month or year view. */\n  startView = 'month';\n  /** The currently selected date. */\n  get selected() {\n    return this._selected;\n  }\n  set selected(value) {\n    if (value instanceof DateRange) {\n      this._selected = value;\n    } else {\n      this._selected = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    }\n  }\n  _selected;\n  /** The minimum selectable date. */\n  get minDate() {\n    return this._minDate;\n  }\n  set minDate(value) {\n    this._minDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _minDate;\n  /** The maximum selectable date. */\n  get maxDate() {\n    return this._maxDate;\n  }\n  set maxDate(value) {\n    this._maxDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _maxDate;\n  /** Function used to filter which dates are selectable. */\n  dateFilter;\n  /** Function that can be used to add custom CSS classes to dates. */\n  dateClass;\n  /** Start of the comparison range. */\n  comparisonStart;\n  /** End of the comparison range. */\n  comparisonEnd;\n  /** ARIA Accessible name of the `<input matStartDate/>` */\n  startDateAccessibleName;\n  /** ARIA Accessible name of the `<input matEndDate/>` */\n  endDateAccessibleName;\n  /** Emits when the currently selected date changes. */\n  selectedChange = new EventEmitter();\n  /**\n   * Emits the year chosen in multiyear view.\n   * This doesn't imply a change on the selected date.\n   */\n  yearSelected = new EventEmitter();\n  /**\n   * Emits the month chosen in year view.\n   * This doesn't imply a change on the selected date.\n   */\n  monthSelected = new EventEmitter();\n  /**\n   * Emits when the current view changes.\n   */\n  viewChanged = new EventEmitter(true);\n  /** Emits when any date is selected. */\n  _userSelection = new EventEmitter();\n  /** Emits a new date range value when the user completes a drag drop operation. */\n  _userDragDrop = new EventEmitter();\n  /** Reference to the current month view component. */\n  monthView;\n  /** Reference to the current year view component. */\n  yearView;\n  /** Reference to the current multi-year view component. */\n  multiYearView;\n  /**\n   * The current active date. This determines which time period is shown and which date is\n   * highlighted when using keyboard navigation.\n   */\n  get activeDate() {\n    return this._clampedActiveDate;\n  }\n  set activeDate(value) {\n    this._clampedActiveDate = this._dateAdapter.clampDate(value, this.minDate, this.maxDate);\n    this.stateChanges.next();\n    this._changeDetectorRef.markForCheck();\n  }\n  _clampedActiveDate;\n  /** Whether the calendar is in month view. */\n  get currentView() {\n    return this._currentView;\n  }\n  set currentView(value) {\n    const viewChangedResult = this._currentView !== value ? value : null;\n    this._currentView = value;\n    this._moveFocusOnNextTick = true;\n    this._changeDetectorRef.markForCheck();\n    if (viewChangedResult) {\n      this.viewChanged.emit(viewChangedResult);\n    }\n  }\n  _currentView;\n  /** Origin of active drag, or null when dragging is not active. */\n  _activeDrag = null;\n  /**\n   * Emits whenever there is a state change that the header may need to respond to.\n   */\n  stateChanges = new Subject();\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._dateAdapter) {\n        throw createMissingDateImplError('DateAdapter');\n      }\n      if (!this._dateFormats) {\n        throw createMissingDateImplError('MAT_DATE_FORMATS');\n      }\n    }\n    this._intlChanges = inject(MatDatepickerIntl).changes.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    });\n  }\n  ngAfterContentInit() {\n    this._calendarHeaderPortal = new ComponentPortal(this.headerComponent || MatCalendarHeader);\n    this.activeDate = this.startAt || this._dateAdapter.today();\n    // Assign to the private property since we don't want to move focus on init.\n    this._currentView = this.startView;\n  }\n  ngAfterViewChecked() {\n    if (this._moveFocusOnNextTick) {\n      this._moveFocusOnNextTick = false;\n      this.focusActiveCell();\n    }\n  }\n  ngOnDestroy() {\n    this._intlChanges.unsubscribe();\n    this.stateChanges.complete();\n  }\n  ngOnChanges(changes) {\n    // Ignore date changes that are at a different time on the same day. This fixes issues where\n    // the calendar re-renders when there is no meaningful change to [minDate] or [maxDate]\n    // (#24435).\n    const minDateChange = changes['minDate'] && !this._dateAdapter.sameDate(changes['minDate'].previousValue, changes['minDate'].currentValue) ? changes['minDate'] : undefined;\n    const maxDateChange = changes['maxDate'] && !this._dateAdapter.sameDate(changes['maxDate'].previousValue, changes['maxDate'].currentValue) ? changes['maxDate'] : undefined;\n    const changeRequiringRerender = minDateChange || maxDateChange || changes['dateFilter'];\n    if (changeRequiringRerender && !changeRequiringRerender.firstChange) {\n      const view = this._getCurrentViewComponent();\n      if (view) {\n        // Schedule focus to be moved to the active date since re-rendering can blur the active\n        // cell (see #29265), however don't do so if focus is outside of the calendar, because it\n        // can steal away the user's attention (see #30635).\n        if (this._elementRef.nativeElement.contains(_getFocusedElementPierceShadowDom())) {\n          this._moveFocusOnNextTick = true;\n        }\n        // We need to `detectChanges` manually here, because the `minDate`, `maxDate` etc. are\n        // passed down to the view via data bindings which won't be up-to-date when we call `_init`.\n        this._changeDetectorRef.detectChanges();\n        view._init();\n      }\n    }\n    this.stateChanges.next();\n  }\n  /** Focuses the active date. */\n  focusActiveCell() {\n    this._getCurrentViewComponent()._focusActiveCell(false);\n  }\n  /** Updates today's date after an update of the active date */\n  updateTodaysDate() {\n    this._getCurrentViewComponent()._init();\n  }\n  /** Handles date selection in the month view. */\n  _dateSelected(event) {\n    const date = event.value;\n    if (this.selected instanceof DateRange || date && !this._dateAdapter.sameDate(date, this.selected)) {\n      this.selectedChange.emit(date);\n    }\n    this._userSelection.emit(event);\n  }\n  /** Handles year selection in the multiyear view. */\n  _yearSelectedInMultiYearView(normalizedYear) {\n    this.yearSelected.emit(normalizedYear);\n  }\n  /** Handles month selection in the year view. */\n  _monthSelectedInYearView(normalizedMonth) {\n    this.monthSelected.emit(normalizedMonth);\n  }\n  /** Handles year/month selection in the multi-year/year views. */\n  _goToDateInView(date, view) {\n    this.activeDate = date;\n    this.currentView = view;\n  }\n  /** Called when the user starts dragging to change a date range. */\n  _dragStarted(event) {\n    this._activeDrag = event;\n  }\n  /**\n   * Called when a drag completes. It may end in cancelation or in the selection\n   * of a new range.\n   */\n  _dragEnded(event) {\n    if (!this._activeDrag) return;\n    if (event.value) {\n      this._userDragDrop.emit(event);\n    }\n    this._activeDrag = null;\n  }\n  /** Returns the component instance that corresponds to the current calendar view. */\n  _getCurrentViewComponent() {\n    // The return type is explicitly written as a union to ensure that the Closure compiler does\n    // not optimize calls to _init(). Without the explicit return type, TypeScript narrows it to\n    // only the first component type. See https://github.com/angular/components/issues/22996.\n    return this.monthView || this.yearView || this.multiYearView;\n  }\n  static ɵfac = function MatCalendar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCalendar)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCalendar,\n    selectors: [[\"mat-calendar\"]],\n    viewQuery: function MatCalendar_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatMonthView, 5);\n        i0.ɵɵviewQuery(MatYearView, 5);\n        i0.ɵɵviewQuery(MatMultiYearView, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.monthView = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.yearView = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.multiYearView = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-calendar\"],\n    inputs: {\n      headerComponent: \"headerComponent\",\n      startAt: \"startAt\",\n      startView: \"startView\",\n      selected: \"selected\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      dateFilter: \"dateFilter\",\n      dateClass: \"dateClass\",\n      comparisonStart: \"comparisonStart\",\n      comparisonEnd: \"comparisonEnd\",\n      startDateAccessibleName: \"startDateAccessibleName\",\n      endDateAccessibleName: \"endDateAccessibleName\"\n    },\n    outputs: {\n      selectedChange: \"selectedChange\",\n      yearSelected: \"yearSelected\",\n      monthSelected: \"monthSelected\",\n      viewChanged: \"viewChanged\",\n      _userSelection: \"_userSelection\",\n      _userDragDrop: \"_userDragDrop\"\n    },\n    exportAs: [\"matCalendar\"],\n    features: [i0.ɵɵProvidersFeature([MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER]), i0.ɵɵNgOnChangesFeature],\n    decls: 5,\n    vars: 2,\n    consts: [[3, \"cdkPortalOutlet\"], [\"cdkMonitorSubtreeFocus\", \"\", \"tabindex\", \"-1\", 1, \"mat-calendar-content\"], [3, \"activeDate\", \"selected\", \"dateFilter\", \"maxDate\", \"minDate\", \"dateClass\", \"comparisonStart\", \"comparisonEnd\", \"startDateAccessibleName\", \"endDateAccessibleName\", \"activeDrag\"], [3, \"activeDate\", \"selected\", \"dateFilter\", \"maxDate\", \"minDate\", \"dateClass\"], [3, \"activeDateChange\", \"_userSelection\", \"dragStarted\", \"dragEnded\", \"activeDate\", \"selected\", \"dateFilter\", \"maxDate\", \"minDate\", \"dateClass\", \"comparisonStart\", \"comparisonEnd\", \"startDateAccessibleName\", \"endDateAccessibleName\", \"activeDrag\"], [3, \"activeDateChange\", \"monthSelected\", \"selectedChange\", \"activeDate\", \"selected\", \"dateFilter\", \"maxDate\", \"minDate\", \"dateClass\"], [3, \"activeDateChange\", \"yearSelected\", \"selectedChange\", \"activeDate\", \"selected\", \"dateFilter\", \"maxDate\", \"minDate\", \"dateClass\"]],\n    template: function MatCalendar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatCalendar_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, MatCalendar_Case_2_Template, 1, 11, \"mat-month-view\", 2)(3, MatCalendar_Case_3_Template, 1, 6, \"mat-year-view\", 3)(4, MatCalendar_Case_4_Template, 1, 6, \"mat-multi-year-view\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._calendarHeaderPortal);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional((tmp_1_0 = ctx.currentView) === \"month\" ? 2 : tmp_1_0 === \"year\" ? 3 : tmp_1_0 === \"multi-year\" ? 4 : -1);\n      }\n    },\n    dependencies: [CdkPortalOutlet, CdkMonitorFocus, MatMonthView, MatYearView, MatMultiYearView],\n    styles: [\".mat-calendar{display:block;line-height:normal;font-family:var(--mat-datepicker-calendar-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-datepicker-calendar-text-size, var(--mat-sys-body-medium-size))}.mat-calendar-header{padding:8px 8px 0 8px}.mat-calendar-content{padding:0 8px 8px 8px;outline:none}.mat-calendar-controls{display:flex;align-items:center;margin:5% calc(4.7142857143% - 16px)}.mat-calendar-spacer{flex:1 1 auto}.mat-calendar-period-button{min-width:0;margin:0 8px;font-size:var(--mat-datepicker-calendar-period-button-text-size, var(--mat-sys-title-small-size));font-weight:var(--mat-datepicker-calendar-period-button-text-weight, var(--mat-sys-title-small-weight));--mdc-text-button-label-text-color:var(--mat-datepicker-calendar-period-button-text-color, var(--mat-sys-on-surface-variant))}.mat-calendar-arrow{display:inline-block;width:10px;height:5px;margin:0 0 0 5px;vertical-align:middle;fill:var(--mat-datepicker-calendar-period-button-icon-color, var(--mat-sys-on-surface-variant))}.mat-calendar-arrow.mat-calendar-invert{transform:rotate(180deg)}[dir=rtl] .mat-calendar-arrow{margin:0 5px 0 0}@media(forced-colors: active){.mat-calendar-arrow{fill:CanvasText}}.mat-datepicker-content .mat-calendar-previous-button:not(.mat-mdc-button-disabled),.mat-datepicker-content .mat-calendar-next-button:not(.mat-mdc-button-disabled){color:var(--mat-datepicker-calendar-navigation-button-icon-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-calendar-previous-button,[dir=rtl] .mat-calendar-next-button{transform:rotate(180deg)}.mat-calendar-table{border-spacing:0;border-collapse:collapse;width:100%}.mat-calendar-table-header th{text-align:center;padding:0 0 8px 0;color:var(--mat-datepicker-calendar-header-text-color, var(--mat-sys-on-surface-variant));font-size:var(--mat-datepicker-calendar-header-text-size, var(--mat-sys-title-small-size));font-weight:var(--mat-datepicker-calendar-header-text-weight, var(--mat-sys-title-small-weight))}.mat-calendar-table-header-divider{position:relative;height:1px}.mat-calendar-table-header-divider::after{content:\\\"\\\";position:absolute;top:0;left:-8px;right:-8px;height:1px;background:var(--mat-datepicker-calendar-header-divider-color, transparent)}.mat-calendar-body-cell-content::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}.mat-calendar-body-cell:focus .mat-focus-indicator::before{content:\\\"\\\"}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCalendar, [{\n    type: Component,\n    args: [{\n      selector: 'mat-calendar',\n      host: {\n        'class': 'mat-calendar'\n      },\n      exportAs: 'matCalendar',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER],\n      imports: [CdkPortalOutlet, CdkMonitorFocus, MatMonthView, MatYearView, MatMultiYearView],\n      template: \"<ng-template [cdkPortalOutlet]=\\\"_calendarHeaderPortal\\\"></ng-template>\\n\\n<div class=\\\"mat-calendar-content\\\" cdkMonitorSubtreeFocus tabindex=\\\"-1\\\">\\n  @switch (currentView) {\\n    @case ('month') {\\n        <mat-month-view\\n            [(activeDate)]=\\\"activeDate\\\"\\n            [selected]=\\\"selected\\\"\\n            [dateFilter]=\\\"dateFilter\\\"\\n            [maxDate]=\\\"maxDate\\\"\\n            [minDate]=\\\"minDate\\\"\\n            [dateClass]=\\\"dateClass\\\"\\n            [comparisonStart]=\\\"comparisonStart\\\"\\n            [comparisonEnd]=\\\"comparisonEnd\\\"\\n            [startDateAccessibleName]=\\\"startDateAccessibleName\\\"\\n            [endDateAccessibleName]=\\\"endDateAccessibleName\\\"\\n            (_userSelection)=\\\"_dateSelected($event)\\\"\\n            (dragStarted)=\\\"_dragStarted($event)\\\"\\n            (dragEnded)=\\\"_dragEnded($event)\\\"\\n            [activeDrag]=\\\"_activeDrag\\\"></mat-month-view>\\n    }\\n\\n    @case ('year') {\\n        <mat-year-view\\n            [(activeDate)]=\\\"activeDate\\\"\\n            [selected]=\\\"selected\\\"\\n            [dateFilter]=\\\"dateFilter\\\"\\n            [maxDate]=\\\"maxDate\\\"\\n            [minDate]=\\\"minDate\\\"\\n            [dateClass]=\\\"dateClass\\\"\\n            (monthSelected)=\\\"_monthSelectedInYearView($event)\\\"\\n            (selectedChange)=\\\"_goToDateInView($event, 'month')\\\"></mat-year-view>\\n    }\\n\\n    @case ('multi-year') {\\n        <mat-multi-year-view\\n            [(activeDate)]=\\\"activeDate\\\"\\n            [selected]=\\\"selected\\\"\\n            [dateFilter]=\\\"dateFilter\\\"\\n            [maxDate]=\\\"maxDate\\\"\\n            [minDate]=\\\"minDate\\\"\\n            [dateClass]=\\\"dateClass\\\"\\n            (yearSelected)=\\\"_yearSelectedInMultiYearView($event)\\\"\\n            (selectedChange)=\\\"_goToDateInView($event, 'year')\\\"></mat-multi-year-view>\\n    }\\n  }\\n</div>\\n\",\n      styles: [\".mat-calendar{display:block;line-height:normal;font-family:var(--mat-datepicker-calendar-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-datepicker-calendar-text-size, var(--mat-sys-body-medium-size))}.mat-calendar-header{padding:8px 8px 0 8px}.mat-calendar-content{padding:0 8px 8px 8px;outline:none}.mat-calendar-controls{display:flex;align-items:center;margin:5% calc(4.7142857143% - 16px)}.mat-calendar-spacer{flex:1 1 auto}.mat-calendar-period-button{min-width:0;margin:0 8px;font-size:var(--mat-datepicker-calendar-period-button-text-size, var(--mat-sys-title-small-size));font-weight:var(--mat-datepicker-calendar-period-button-text-weight, var(--mat-sys-title-small-weight));--mdc-text-button-label-text-color:var(--mat-datepicker-calendar-period-button-text-color, var(--mat-sys-on-surface-variant))}.mat-calendar-arrow{display:inline-block;width:10px;height:5px;margin:0 0 0 5px;vertical-align:middle;fill:var(--mat-datepicker-calendar-period-button-icon-color, var(--mat-sys-on-surface-variant))}.mat-calendar-arrow.mat-calendar-invert{transform:rotate(180deg)}[dir=rtl] .mat-calendar-arrow{margin:0 5px 0 0}@media(forced-colors: active){.mat-calendar-arrow{fill:CanvasText}}.mat-datepicker-content .mat-calendar-previous-button:not(.mat-mdc-button-disabled),.mat-datepicker-content .mat-calendar-next-button:not(.mat-mdc-button-disabled){color:var(--mat-datepicker-calendar-navigation-button-icon-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-calendar-previous-button,[dir=rtl] .mat-calendar-next-button{transform:rotate(180deg)}.mat-calendar-table{border-spacing:0;border-collapse:collapse;width:100%}.mat-calendar-table-header th{text-align:center;padding:0 0 8px 0;color:var(--mat-datepicker-calendar-header-text-color, var(--mat-sys-on-surface-variant));font-size:var(--mat-datepicker-calendar-header-text-size, var(--mat-sys-title-small-size));font-weight:var(--mat-datepicker-calendar-header-text-weight, var(--mat-sys-title-small-weight))}.mat-calendar-table-header-divider{position:relative;height:1px}.mat-calendar-table-header-divider::after{content:\\\"\\\";position:absolute;top:0;left:-8px;right:-8px;height:1px;background:var(--mat-datepicker-calendar-header-divider-color, transparent)}.mat-calendar-body-cell-content::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}.mat-calendar-body-cell:focus .mat-focus-indicator::before{content:\\\"\\\"}\\n\"]\n    }]\n  }], () => [], {\n    headerComponent: [{\n      type: Input\n    }],\n    startAt: [{\n      type: Input\n    }],\n    startView: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    minDate: [{\n      type: Input\n    }],\n    maxDate: [{\n      type: Input\n    }],\n    dateFilter: [{\n      type: Input\n    }],\n    dateClass: [{\n      type: Input\n    }],\n    comparisonStart: [{\n      type: Input\n    }],\n    comparisonEnd: [{\n      type: Input\n    }],\n    startDateAccessibleName: [{\n      type: Input\n    }],\n    endDateAccessibleName: [{\n      type: Input\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    yearSelected: [{\n      type: Output\n    }],\n    monthSelected: [{\n      type: Output\n    }],\n    viewChanged: [{\n      type: Output\n    }],\n    _userSelection: [{\n      type: Output\n    }],\n    _userDragDrop: [{\n      type: Output\n    }],\n    monthView: [{\n      type: ViewChild,\n      args: [MatMonthView]\n    }],\n    yearView: [{\n      type: ViewChild,\n      args: [MatYearView]\n    }],\n    multiYearView: [{\n      type: ViewChild,\n      args: [MatMultiYearView]\n    }]\n  });\n})();\n\n/** Injection token that determines the scroll handling while the calendar is open. */\nconst MAT_DATEPICKER_SCROLL_STRATEGY = new InjectionToken('mat-datepicker-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_DATEPICKER_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY\n};\n/**\n * Component used as the content for the datepicker overlay. We use this instead of using\n * MatCalendar directly as the content so we can control the initial focus. This also gives us a\n * place to put additional features of the overlay that are not part of the calendar itself in the\n * future. (e.g. confirmation buttons).\n * @docs-private\n */\nclass MatDatepickerContent {\n  _elementRef = inject(ElementRef);\n  _animationsDisabled = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  }) === 'NoopAnimations';\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _globalModel = inject(MatDateSelectionModel);\n  _dateAdapter = inject(DateAdapter);\n  _ngZone = inject(NgZone);\n  _rangeSelectionStrategy = inject(MAT_DATE_RANGE_SELECTION_STRATEGY, {\n    optional: true\n  });\n  _stateChanges;\n  _model;\n  _eventCleanups;\n  _animationFallback;\n  /** Reference to the internal calendar component. */\n  _calendar;\n  /**\n   * Theme color of the internal calendar. This API is supported in M2 themes\n   * only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/datepicker/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Reference to the datepicker that created the overlay. */\n  datepicker;\n  /** Start of the comparison range. */\n  comparisonStart;\n  /** End of the comparison range. */\n  comparisonEnd;\n  /** ARIA Accessible name of the `<input matStartDate/>` */\n  startDateAccessibleName;\n  /** ARIA Accessible name of the `<input matEndDate/>` */\n  endDateAccessibleName;\n  /** Whether the datepicker is above or below the input. */\n  _isAbove;\n  /** Emits when an animation has finished. */\n  _animationDone = new Subject();\n  /** Whether there is an in-progress animation. */\n  _isAnimating = false;\n  /** Text for the close button. */\n  _closeButtonText;\n  /** Whether the close button currently has focus. */\n  _closeButtonFocused;\n  /** Portal with projected action buttons. */\n  _actionsPortal = null;\n  /** Id of the label for the `role=\"dialog\"` element. */\n  _dialogLabelId;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._closeButtonText = inject(MatDatepickerIntl).closeCalendarLabel;\n    if (!this._animationsDisabled) {\n      const element = this._elementRef.nativeElement;\n      const renderer = inject(Renderer2);\n      this._eventCleanups = this._ngZone.runOutsideAngular(() => [renderer.listen(element, 'animationstart', this._handleAnimationEvent), renderer.listen(element, 'animationend', this._handleAnimationEvent), renderer.listen(element, 'animationcancel', this._handleAnimationEvent)]);\n    }\n  }\n  ngAfterViewInit() {\n    this._stateChanges = this.datepicker.stateChanges.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n    });\n    this._calendar.focusActiveCell();\n  }\n  ngOnDestroy() {\n    clearTimeout(this._animationFallback);\n    this._eventCleanups?.forEach(cleanup => cleanup());\n    this._stateChanges?.unsubscribe();\n    this._animationDone.complete();\n  }\n  _handleUserSelection(event) {\n    const selection = this._model.selection;\n    const value = event.value;\n    const isRange = selection instanceof DateRange;\n    // If we're selecting a range and we have a selection strategy, always pass the value through\n    // there. Otherwise don't assign null values to the model, unless we're selecting a range.\n    // A null value when picking a range means that the user cancelled the selection (e.g. by\n    // pressing escape), whereas when selecting a single value it means that the value didn't\n    // change. This isn't very intuitive, but it's here for backwards-compatibility.\n    if (isRange && this._rangeSelectionStrategy) {\n      const newSelection = this._rangeSelectionStrategy.selectionFinished(value, selection, event.event);\n      this._model.updateSelection(newSelection, this);\n    } else if (value && (isRange || !this._dateAdapter.sameDate(value, selection))) {\n      this._model.add(value);\n    }\n    // Delegate closing the overlay to the actions.\n    if ((!this._model || this._model.isComplete()) && !this._actionsPortal) {\n      this.datepicker.close();\n    }\n  }\n  _handleUserDragDrop(event) {\n    this._model.updateSelection(event.value, this);\n  }\n  _startExitAnimation() {\n    this._elementRef.nativeElement.classList.add('mat-datepicker-content-exit');\n    if (this._animationsDisabled) {\n      this._animationDone.next();\n    } else {\n      // Some internal apps disable animations in tests using `* {animation: none !important}`.\n      // If that happens, the animation events won't fire and we'll never clean up the overlay.\n      // Add a fallback that will fire if the animation doesn't run in a certain amount of time.\n      clearTimeout(this._animationFallback);\n      this._animationFallback = setTimeout(() => {\n        if (!this._isAnimating) {\n          this._animationDone.next();\n        }\n      }, 200);\n    }\n  }\n  _handleAnimationEvent = event => {\n    const element = this._elementRef.nativeElement;\n    if (event.target !== element || !event.animationName.startsWith('_mat-datepicker-content')) {\n      return;\n    }\n    clearTimeout(this._animationFallback);\n    this._isAnimating = event.type === 'animationstart';\n    element.classList.toggle('mat-datepicker-content-animating', this._isAnimating);\n    if (!this._isAnimating) {\n      this._animationDone.next();\n    }\n  };\n  _getSelected() {\n    return this._model.selection;\n  }\n  /** Applies the current pending selection to the global model. */\n  _applyPendingSelection() {\n    if (this._model !== this._globalModel) {\n      this._globalModel.updateSelection(this._model.selection, this);\n    }\n  }\n  /**\n   * Assigns a new portal containing the datepicker actions.\n   * @param portal Portal with the actions to be assigned.\n   * @param forceRerender Whether a re-render of the portal should be triggered. This isn't\n   * necessary if the portal is assigned during initialization, but it may be required if it's\n   * added at a later point.\n   */\n  _assignActions(portal, forceRerender) {\n    // If we have actions, clone the model so that we have the ability to cancel the selection,\n    // otherwise update the global model directly. Note that we want to assign this as soon as\n    // possible, but `_actionsPortal` isn't available in the constructor so we do it in `ngOnInit`.\n    this._model = portal ? this._globalModel.clone() : this._globalModel;\n    this._actionsPortal = portal;\n    if (forceRerender) {\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n  static ɵfac = function MatDatepickerContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerContent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDatepickerContent,\n    selectors: [[\"mat-datepicker-content\"]],\n    viewQuery: function MatDatepickerContent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatCalendar, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._calendar = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-datepicker-content\"],\n    hostVars: 6,\n    hostBindings: function MatDatepickerContent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-datepicker-content-touch\", ctx.datepicker.touchUi)(\"mat-datepicker-content-animations-enabled\", !ctx._animationsDisabled);\n      }\n    },\n    inputs: {\n      color: \"color\"\n    },\n    exportAs: [\"matDatepickerContent\"],\n    decls: 5,\n    vars: 26,\n    consts: [[\"cdkTrapFocus\", \"\", \"role\", \"dialog\", 1, \"mat-datepicker-content-container\"], [3, \"yearSelected\", \"monthSelected\", \"viewChanged\", \"_userSelection\", \"_userDragDrop\", \"id\", \"startAt\", \"startView\", \"minDate\", \"maxDate\", \"dateFilter\", \"headerComponent\", \"selected\", \"dateClass\", \"comparisonStart\", \"comparisonEnd\", \"startDateAccessibleName\", \"endDateAccessibleName\"], [3, \"cdkPortalOutlet\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"mat-datepicker-close-button\", 3, \"focus\", \"blur\", \"click\", \"color\"]],\n    template: function MatDatepickerContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-calendar\", 1);\n        i0.ɵɵlistener(\"yearSelected\", function MatDatepickerContent_Template_mat_calendar_yearSelected_1_listener($event) {\n          return ctx.datepicker._selectYear($event);\n        })(\"monthSelected\", function MatDatepickerContent_Template_mat_calendar_monthSelected_1_listener($event) {\n          return ctx.datepicker._selectMonth($event);\n        })(\"viewChanged\", function MatDatepickerContent_Template_mat_calendar_viewChanged_1_listener($event) {\n          return ctx.datepicker._viewChanged($event);\n        })(\"_userSelection\", function MatDatepickerContent_Template_mat_calendar__userSelection_1_listener($event) {\n          return ctx._handleUserSelection($event);\n        })(\"_userDragDrop\", function MatDatepickerContent_Template_mat_calendar__userDragDrop_1_listener($event) {\n          return ctx._handleUserDragDrop($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(2, MatDatepickerContent_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n        i0.ɵɵelementStart(3, \"button\", 3);\n        i0.ɵɵlistener(\"focus\", function MatDatepickerContent_Template_button_focus_3_listener() {\n          return ctx._closeButtonFocused = true;\n        })(\"blur\", function MatDatepickerContent_Template_button_blur_3_listener() {\n          return ctx._closeButtonFocused = false;\n        })(\"click\", function MatDatepickerContent_Template_button_click_3_listener() {\n          return ctx.datepicker.close();\n        });\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        let tmp_3_0;\n        i0.ɵɵclassProp(\"mat-datepicker-content-container-with-custom-header\", ctx.datepicker.calendarHeaderComponent)(\"mat-datepicker-content-container-with-actions\", ctx._actionsPortal);\n        i0.ɵɵattribute(\"aria-modal\", true)(\"aria-labelledby\", (tmp_3_0 = ctx._dialogLabelId) !== null && tmp_3_0 !== undefined ? tmp_3_0 : undefined);\n        i0.ɵɵadvance();\n        i0.ɵɵclassMap(ctx.datepicker.panelClass);\n        i0.ɵɵproperty(\"id\", ctx.datepicker.id)(\"startAt\", ctx.datepicker.startAt)(\"startView\", ctx.datepicker.startView)(\"minDate\", ctx.datepicker._getMinDate())(\"maxDate\", ctx.datepicker._getMaxDate())(\"dateFilter\", ctx.datepicker._getDateFilter())(\"headerComponent\", ctx.datepicker.calendarHeaderComponent)(\"selected\", ctx._getSelected())(\"dateClass\", ctx.datepicker.dateClass)(\"comparisonStart\", ctx.comparisonStart)(\"comparisonEnd\", ctx.comparisonEnd)(\"startDateAccessibleName\", ctx.startDateAccessibleName)(\"endDateAccessibleName\", ctx.endDateAccessibleName);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._actionsPortal);\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"cdk-visually-hidden\", !ctx._closeButtonFocused);\n        i0.ɵɵproperty(\"color\", ctx.color || \"primary\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(ctx._closeButtonText);\n      }\n    },\n    dependencies: [CdkTrapFocus, MatCalendar, CdkPortalOutlet, MatButton],\n    styles: [\"@keyframes _mat-datepicker-content-dropdown-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-datepicker-content-dialog-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-datepicker-content-exit{from{opacity:1}to{opacity:0}}.mat-datepicker-content{display:block;border-radius:4px;background-color:var(--mat-datepicker-calendar-container-background-color, var(--mat-sys-surface-container-high));color:var(--mat-datepicker-calendar-container-text-color, var(--mat-sys-on-surface));box-shadow:var(--mat-datepicker-calendar-container-elevation-shadow, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12));border-radius:var(--mat-datepicker-calendar-container-shape, var(--mat-sys-corner-large))}.mat-datepicker-content.mat-datepicker-content-animations-enabled{animation:_mat-datepicker-content-dropdown-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-datepicker-content .mat-calendar{width:296px;height:354px}.mat-datepicker-content .mat-datepicker-content-container-with-custom-header .mat-calendar{height:auto}.mat-datepicker-content .mat-datepicker-close-button{position:absolute;top:100%;left:0;margin-top:8px}.mat-datepicker-content-animating .mat-datepicker-content .mat-datepicker-close-button{display:none}.mat-datepicker-content-container{display:flex;flex-direction:column;justify-content:space-between}.mat-datepicker-content-touch{display:block;max-height:80vh;box-shadow:var(--mat-datepicker-calendar-container-touch-elevation-shadow, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12));border-radius:var(--mat-datepicker-calendar-container-touch-shape, var(--mat-sys-corner-extra-large));position:relative;overflow:visible}.mat-datepicker-content-touch.mat-datepicker-content-animations-enabled{animation:_mat-datepicker-content-dialog-enter 150ms cubic-bezier(0, 0, 0.2, 1)}.mat-datepicker-content-touch .mat-datepicker-content-container{min-height:312px;max-height:788px;min-width:250px;max-width:750px}.mat-datepicker-content-touch .mat-calendar{width:100%;height:auto}.mat-datepicker-content-exit.mat-datepicker-content-animations-enabled{animation:_mat-datepicker-content-exit 100ms linear}@media all and (orientation: landscape){.mat-datepicker-content-touch .mat-datepicker-content-container{width:64vh;height:80vh}}@media all and (orientation: portrait){.mat-datepicker-content-touch .mat-datepicker-content-container{width:80vw;height:100vw}.mat-datepicker-content-touch .mat-datepicker-content-container-with-actions{height:115vw}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerContent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-datepicker-content',\n      host: {\n        'class': 'mat-datepicker-content',\n        '[class]': 'color ? \"mat-\" + color : \"\"',\n        '[class.mat-datepicker-content-touch]': 'datepicker.touchUi',\n        '[class.mat-datepicker-content-animations-enabled]': '!_animationsDisabled'\n      },\n      exportAs: 'matDatepickerContent',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [CdkTrapFocus, MatCalendar, CdkPortalOutlet, MatButton],\n      template: \"<div\\n  cdkTrapFocus\\n  role=\\\"dialog\\\"\\n  [attr.aria-modal]=\\\"true\\\"\\n  [attr.aria-labelledby]=\\\"_dialogLabelId ?? undefined\\\"\\n  class=\\\"mat-datepicker-content-container\\\"\\n  [class.mat-datepicker-content-container-with-custom-header]=\\\"datepicker.calendarHeaderComponent\\\"\\n  [class.mat-datepicker-content-container-with-actions]=\\\"_actionsPortal\\\">\\n  <mat-calendar\\n    [id]=\\\"datepicker.id\\\"\\n    [class]=\\\"datepicker.panelClass\\\"\\n    [startAt]=\\\"datepicker.startAt\\\"\\n    [startView]=\\\"datepicker.startView\\\"\\n    [minDate]=\\\"datepicker._getMinDate()\\\"\\n    [maxDate]=\\\"datepicker._getMaxDate()\\\"\\n    [dateFilter]=\\\"datepicker._getDateFilter()\\\"\\n    [headerComponent]=\\\"datepicker.calendarHeaderComponent\\\"\\n    [selected]=\\\"_getSelected()\\\"\\n    [dateClass]=\\\"datepicker.dateClass\\\"\\n    [comparisonStart]=\\\"comparisonStart\\\"\\n    [comparisonEnd]=\\\"comparisonEnd\\\"\\n    [startDateAccessibleName]=\\\"startDateAccessibleName\\\"\\n    [endDateAccessibleName]=\\\"endDateAccessibleName\\\"\\n    (yearSelected)=\\\"datepicker._selectYear($event)\\\"\\n    (monthSelected)=\\\"datepicker._selectMonth($event)\\\"\\n    (viewChanged)=\\\"datepicker._viewChanged($event)\\\"\\n    (_userSelection)=\\\"_handleUserSelection($event)\\\"\\n    (_userDragDrop)=\\\"_handleUserDragDrop($event)\\\"></mat-calendar>\\n\\n  <ng-template [cdkPortalOutlet]=\\\"_actionsPortal\\\"></ng-template>\\n\\n  <!-- Invisible close button for screen reader users. -->\\n  <button\\n    type=\\\"button\\\"\\n    mat-raised-button\\n    [color]=\\\"color || 'primary'\\\"\\n    class=\\\"mat-datepicker-close-button\\\"\\n    [class.cdk-visually-hidden]=\\\"!_closeButtonFocused\\\"\\n    (focus)=\\\"_closeButtonFocused = true\\\"\\n    (blur)=\\\"_closeButtonFocused = false\\\"\\n    (click)=\\\"datepicker.close()\\\">{{ _closeButtonText }}</button>\\n</div>\\n\",\n      styles: [\"@keyframes _mat-datepicker-content-dropdown-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-datepicker-content-dialog-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-datepicker-content-exit{from{opacity:1}to{opacity:0}}.mat-datepicker-content{display:block;border-radius:4px;background-color:var(--mat-datepicker-calendar-container-background-color, var(--mat-sys-surface-container-high));color:var(--mat-datepicker-calendar-container-text-color, var(--mat-sys-on-surface));box-shadow:var(--mat-datepicker-calendar-container-elevation-shadow, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12));border-radius:var(--mat-datepicker-calendar-container-shape, var(--mat-sys-corner-large))}.mat-datepicker-content.mat-datepicker-content-animations-enabled{animation:_mat-datepicker-content-dropdown-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-datepicker-content .mat-calendar{width:296px;height:354px}.mat-datepicker-content .mat-datepicker-content-container-with-custom-header .mat-calendar{height:auto}.mat-datepicker-content .mat-datepicker-close-button{position:absolute;top:100%;left:0;margin-top:8px}.mat-datepicker-content-animating .mat-datepicker-content .mat-datepicker-close-button{display:none}.mat-datepicker-content-container{display:flex;flex-direction:column;justify-content:space-between}.mat-datepicker-content-touch{display:block;max-height:80vh;box-shadow:var(--mat-datepicker-calendar-container-touch-elevation-shadow, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12));border-radius:var(--mat-datepicker-calendar-container-touch-shape, var(--mat-sys-corner-extra-large));position:relative;overflow:visible}.mat-datepicker-content-touch.mat-datepicker-content-animations-enabled{animation:_mat-datepicker-content-dialog-enter 150ms cubic-bezier(0, 0, 0.2, 1)}.mat-datepicker-content-touch .mat-datepicker-content-container{min-height:312px;max-height:788px;min-width:250px;max-width:750px}.mat-datepicker-content-touch .mat-calendar{width:100%;height:auto}.mat-datepicker-content-exit.mat-datepicker-content-animations-enabled{animation:_mat-datepicker-content-exit 100ms linear}@media all and (orientation: landscape){.mat-datepicker-content-touch .mat-datepicker-content-container{width:64vh;height:80vh}}@media all and (orientation: portrait){.mat-datepicker-content-touch .mat-datepicker-content-container{width:80vw;height:100vw}.mat-datepicker-content-touch .mat-datepicker-content-container-with-actions{height:115vw}}\\n\"]\n    }]\n  }], () => [], {\n    _calendar: [{\n      type: ViewChild,\n      args: [MatCalendar]\n    }],\n    color: [{\n      type: Input\n    }]\n  });\n})();\n/** Base class for a datepicker. */\nclass MatDatepickerBase {\n  _overlay = inject(Overlay);\n  _viewContainerRef = inject(ViewContainerRef);\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _model = inject(MatDateSelectionModel);\n  _scrollStrategy = inject(MAT_DATEPICKER_SCROLL_STRATEGY);\n  _inputStateChanges = Subscription.EMPTY;\n  _document = inject(DOCUMENT);\n  /** An input indicating the type of the custom header component for the calendar, if set. */\n  calendarHeaderComponent;\n  /** The date to open the calendar to initially. */\n  get startAt() {\n    // If an explicit startAt is set we start there, otherwise we start at whatever the currently\n    // selected value is.\n    return this._startAt || (this.datepickerInput ? this.datepickerInput.getStartValue() : null);\n  }\n  set startAt(value) {\n    this._startAt = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  _startAt;\n  /** The view that the calendar should start in. */\n  startView = 'month';\n  /**\n   * Theme color of the datepicker's calendar. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/datepicker/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || (this.datepickerInput ? this.datepickerInput.getThemePalette() : undefined);\n  }\n  set color(value) {\n    this._color = value;\n  }\n  _color;\n  /**\n   * Whether the calendar UI is in touch mode. In touch mode the calendar opens in a dialog rather\n   * than a dropdown and elements have more padding to allow for bigger touch targets.\n   */\n  touchUi = false;\n  /** Whether the datepicker pop-up should be disabled. */\n  get disabled() {\n    return this._disabled === undefined && this.datepickerInput ? this.datepickerInput.disabled : !!this._disabled;\n  }\n  set disabled(value) {\n    if (value !== this._disabled) {\n      this._disabled = value;\n      this.stateChanges.next(undefined);\n    }\n  }\n  _disabled;\n  /** Preferred position of the datepicker in the X axis. */\n  xPosition = 'start';\n  /** Preferred position of the datepicker in the Y axis. */\n  yPosition = 'below';\n  /**\n   * Whether to restore focus to the previously-focused element when the calendar is closed.\n   * Note that automatic focus restoration is an accessibility feature and it is recommended that\n   * you provide your own equivalent, if you decide to turn it off.\n   */\n  restoreFocus = true;\n  /**\n   * Emits selected year in multiyear view.\n   * This doesn't imply a change on the selected date.\n   */\n  yearSelected = new EventEmitter();\n  /**\n   * Emits selected month in year view.\n   * This doesn't imply a change on the selected date.\n   */\n  monthSelected = new EventEmitter();\n  /**\n   * Emits when the current view changes.\n   */\n  viewChanged = new EventEmitter(true);\n  /** Function that can be used to add custom CSS classes to dates. */\n  dateClass;\n  /** Emits when the datepicker has been opened. */\n  openedStream = new EventEmitter();\n  /** Emits when the datepicker has been closed. */\n  closedStream = new EventEmitter();\n  /** Classes to be passed to the date picker panel. */\n  get panelClass() {\n    return this._panelClass;\n  }\n  set panelClass(value) {\n    this._panelClass = coerceStringArray(value);\n  }\n  _panelClass;\n  /** Whether the calendar is open. */\n  get opened() {\n    return this._opened;\n  }\n  set opened(value) {\n    if (value) {\n      this.open();\n    } else {\n      this.close();\n    }\n  }\n  _opened = false;\n  /** The id for the datepicker calendar. */\n  id = inject(_IdGenerator).getId('mat-datepicker-');\n  /** The minimum selectable date. */\n  _getMinDate() {\n    return this.datepickerInput && this.datepickerInput.min;\n  }\n  /** The maximum selectable date. */\n  _getMaxDate() {\n    return this.datepickerInput && this.datepickerInput.max;\n  }\n  _getDateFilter() {\n    return this.datepickerInput && this.datepickerInput.dateFilter;\n  }\n  /** A reference to the overlay into which we've rendered the calendar. */\n  _overlayRef;\n  /** Reference to the component instance rendered in the overlay. */\n  _componentRef;\n  /** The element that was focused before the datepicker was opened. */\n  _focusedElementBeforeOpen = null;\n  /** Unique class that will be added to the backdrop so that the test harnesses can look it up. */\n  _backdropHarnessClass = `${this.id}-backdrop`;\n  /** Currently-registered actions portal. */\n  _actionsPortal;\n  /** The input element this datepicker is associated with. */\n  datepickerInput;\n  /** Emits when the datepicker's state changes. */\n  stateChanges = new Subject();\n  _injector = inject(Injector);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  constructor() {\n    if (!this._dateAdapter && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw createMissingDateImplError('DateAdapter');\n    }\n    this._model.selectionChanged.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const positionChange = changes['xPosition'] || changes['yPosition'];\n    if (positionChange && !positionChange.firstChange && this._overlayRef) {\n      const positionStrategy = this._overlayRef.getConfig().positionStrategy;\n      if (positionStrategy instanceof FlexibleConnectedPositionStrategy) {\n        this._setConnectedPositions(positionStrategy);\n        if (this.opened) {\n          this._overlayRef.updatePosition();\n        }\n      }\n    }\n    this.stateChanges.next(undefined);\n  }\n  ngOnDestroy() {\n    this._destroyOverlay();\n    this.close();\n    this._inputStateChanges.unsubscribe();\n    this.stateChanges.complete();\n  }\n  /** Selects the given date */\n  select(date) {\n    this._model.add(date);\n  }\n  /** Emits the selected year in multiyear view */\n  _selectYear(normalizedYear) {\n    this.yearSelected.emit(normalizedYear);\n  }\n  /** Emits selected month in year view */\n  _selectMonth(normalizedMonth) {\n    this.monthSelected.emit(normalizedMonth);\n  }\n  /** Emits changed view */\n  _viewChanged(view) {\n    this.viewChanged.emit(view);\n  }\n  /**\n   * Register an input with this datepicker.\n   * @param input The datepicker input to register with this datepicker.\n   * @returns Selection model that the input should hook itself up to.\n   */\n  registerInput(input) {\n    if (this.datepickerInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('A MatDatepicker can only be associated with a single input.');\n    }\n    this._inputStateChanges.unsubscribe();\n    this.datepickerInput = input;\n    this._inputStateChanges = input.stateChanges.subscribe(() => this.stateChanges.next(undefined));\n    return this._model;\n  }\n  /**\n   * Registers a portal containing action buttons with the datepicker.\n   * @param portal Portal to be registered.\n   */\n  registerActions(portal) {\n    if (this._actionsPortal && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('A MatDatepicker can only be associated with a single actions row.');\n    }\n    this._actionsPortal = portal;\n    this._componentRef?.instance._assignActions(portal, true);\n  }\n  /**\n   * Removes a portal containing action buttons from the datepicker.\n   * @param portal Portal to be removed.\n   */\n  removeActions(portal) {\n    if (portal === this._actionsPortal) {\n      this._actionsPortal = null;\n      this._componentRef?.instance._assignActions(null, true);\n    }\n  }\n  /** Open the calendar. */\n  open() {\n    // Skip reopening if there's an in-progress animation to avoid overlapping\n    // sequences which can cause \"changed after checked\" errors. See #25837.\n    if (this._opened || this.disabled || this._componentRef?.instance._isAnimating) {\n      return;\n    }\n    if (!this.datepickerInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempted to open an MatDatepicker with no associated input.');\n    }\n    this._focusedElementBeforeOpen = _getFocusedElementPierceShadowDom();\n    this._openOverlay();\n    this._opened = true;\n    this.openedStream.emit();\n  }\n  /** Close the calendar. */\n  close() {\n    // Skip reopening if there's an in-progress animation to avoid overlapping\n    // sequences which can cause \"changed after checked\" errors. See #25837.\n    if (!this._opened || this._componentRef?.instance._isAnimating) {\n      return;\n    }\n    const canRestoreFocus = this.restoreFocus && this._focusedElementBeforeOpen && typeof this._focusedElementBeforeOpen.focus === 'function';\n    const completeClose = () => {\n      // The `_opened` could've been reset already if\n      // we got two events in quick succession.\n      if (this._opened) {\n        this._opened = false;\n        this.closedStream.emit();\n      }\n    };\n    if (this._componentRef) {\n      const {\n        instance,\n        location\n      } = this._componentRef;\n      instance._animationDone.pipe(take(1)).subscribe(() => {\n        const activeElement = this._document.activeElement;\n        // Since we restore focus after the exit animation, we have to check that\n        // the user didn't move focus themselves inside the `close` handler.\n        if (canRestoreFocus && (!activeElement || activeElement === this._document.activeElement || location.nativeElement.contains(activeElement))) {\n          this._focusedElementBeforeOpen.focus();\n        }\n        this._focusedElementBeforeOpen = null;\n        this._destroyOverlay();\n      });\n      instance._startExitAnimation();\n    }\n    if (canRestoreFocus) {\n      // Because IE moves focus asynchronously, we can't count on it being restored before we've\n      // marked the datepicker as closed. If the event fires out of sequence and the element that\n      // we're refocusing opens the datepicker on focus, the user could be stuck with not being\n      // able to close the calendar at all. We work around it by making the logic, that marks\n      // the datepicker as closed, async as well.\n      setTimeout(completeClose);\n    } else {\n      completeClose();\n    }\n  }\n  /** Applies the current pending selection on the overlay to the model. */\n  _applyPendingSelection() {\n    this._componentRef?.instance?._applyPendingSelection();\n  }\n  /** Forwards relevant values from the datepicker to the datepicker content inside the overlay. */\n  _forwardContentValues(instance) {\n    instance.datepicker = this;\n    instance.color = this.color;\n    instance._dialogLabelId = this.datepickerInput.getOverlayLabelId();\n    instance._assignActions(this._actionsPortal, false);\n  }\n  /** Opens the overlay with the calendar. */\n  _openOverlay() {\n    this._destroyOverlay();\n    const isDialog = this.touchUi;\n    const portal = new ComponentPortal(MatDatepickerContent, this._viewContainerRef);\n    const overlayRef = this._overlayRef = this._overlay.create(new OverlayConfig({\n      positionStrategy: isDialog ? this._getDialogStrategy() : this._getDropdownStrategy(),\n      hasBackdrop: true,\n      backdropClass: [isDialog ? 'cdk-overlay-dark-backdrop' : 'mat-overlay-transparent-backdrop', this._backdropHarnessClass],\n      direction: this._dir || 'ltr',\n      scrollStrategy: isDialog ? this._overlay.scrollStrategies.block() : this._scrollStrategy(),\n      panelClass: `mat-datepicker-${isDialog ? 'dialog' : 'popup'}`\n    }));\n    this._getCloseStream(overlayRef).subscribe(event => {\n      if (event) {\n        event.preventDefault();\n      }\n      this.close();\n    });\n    // The `preventDefault` call happens inside the calendar as well, however focus moves into\n    // it inside a timeout which can give browsers a chance to fire off a keyboard event in-between\n    // that can scroll the page (see #24969). Always block default actions of arrow keys for the\n    // entire overlay so the page doesn't get scrolled by accident.\n    overlayRef.keydownEvents().subscribe(event => {\n      const keyCode = event.keyCode;\n      if (keyCode === UP_ARROW || keyCode === DOWN_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW || keyCode === PAGE_UP || keyCode === PAGE_DOWN) {\n        event.preventDefault();\n      }\n    });\n    this._componentRef = overlayRef.attach(portal);\n    this._forwardContentValues(this._componentRef.instance);\n    // Update the position once the calendar has rendered. Only relevant in dropdown mode.\n    if (!isDialog) {\n      afterNextRender(() => {\n        overlayRef.updatePosition();\n      }, {\n        injector: this._injector\n      });\n    }\n  }\n  /** Destroys the current overlay. */\n  _destroyOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = this._componentRef = null;\n    }\n  }\n  /** Gets a position strategy that will open the calendar as a dropdown. */\n  _getDialogStrategy() {\n    return this._overlay.position().global().centerHorizontally().centerVertically();\n  }\n  /** Gets a position strategy that will open the calendar as a dropdown. */\n  _getDropdownStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this.datepickerInput.getConnectedOverlayOrigin()).withTransformOriginOn('.mat-datepicker-content').withFlexibleDimensions(false).withViewportMargin(8).withLockedPosition();\n    return this._setConnectedPositions(strategy);\n  }\n  /** Sets the positions of the datepicker in dropdown mode based on the current configuration. */\n  _setConnectedPositions(strategy) {\n    const primaryX = this.xPosition === 'end' ? 'end' : 'start';\n    const secondaryX = primaryX === 'start' ? 'end' : 'start';\n    const primaryY = this.yPosition === 'above' ? 'bottom' : 'top';\n    const secondaryY = primaryY === 'top' ? 'bottom' : 'top';\n    return strategy.withPositions([{\n      originX: primaryX,\n      originY: secondaryY,\n      overlayX: primaryX,\n      overlayY: primaryY\n    }, {\n      originX: primaryX,\n      originY: primaryY,\n      overlayX: primaryX,\n      overlayY: secondaryY\n    }, {\n      originX: secondaryX,\n      originY: secondaryY,\n      overlayX: secondaryX,\n      overlayY: primaryY\n    }, {\n      originX: secondaryX,\n      originY: primaryY,\n      overlayX: secondaryX,\n      overlayY: secondaryY\n    }]);\n  }\n  /** Gets an observable that will emit when the overlay is supposed to be closed. */\n  _getCloseStream(overlayRef) {\n    const ctrlShiftMetaModifiers = ['ctrlKey', 'shiftKey', 'metaKey'];\n    return merge(overlayRef.backdropClick(), overlayRef.detachments(), overlayRef.keydownEvents().pipe(filter(event => {\n      // Closing on alt + up is only valid when there's an input associated with the datepicker.\n      return event.keyCode === ESCAPE && !hasModifierKey(event) || this.datepickerInput && hasModifierKey(event, 'altKey') && event.keyCode === UP_ARROW && ctrlShiftMetaModifiers.every(modifier => !hasModifierKey(event, modifier));\n    })));\n  }\n  static ɵfac = function MatDatepickerBase_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerBase)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDatepickerBase,\n    inputs: {\n      calendarHeaderComponent: \"calendarHeaderComponent\",\n      startAt: \"startAt\",\n      startView: \"startView\",\n      color: \"color\",\n      touchUi: [2, \"touchUi\", \"touchUi\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      xPosition: \"xPosition\",\n      yPosition: \"yPosition\",\n      restoreFocus: [2, \"restoreFocus\", \"restoreFocus\", booleanAttribute],\n      dateClass: \"dateClass\",\n      panelClass: \"panelClass\",\n      opened: [2, \"opened\", \"opened\", booleanAttribute]\n    },\n    outputs: {\n      yearSelected: \"yearSelected\",\n      monthSelected: \"monthSelected\",\n      viewChanged: \"viewChanged\",\n      openedStream: \"opened\",\n      closedStream: \"closed\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerBase, [{\n    type: Directive\n  }], () => [], {\n    calendarHeaderComponent: [{\n      type: Input\n    }],\n    startAt: [{\n      type: Input\n    }],\n    startView: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    touchUi: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    xPosition: [{\n      type: Input\n    }],\n    yPosition: [{\n      type: Input\n    }],\n    restoreFocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    yearSelected: [{\n      type: Output\n    }],\n    monthSelected: [{\n      type: Output\n    }],\n    viewChanged: [{\n      type: Output\n    }],\n    dateClass: [{\n      type: Input\n    }],\n    openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    panelClass: [{\n      type: Input\n    }],\n    opened: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n// TODO(mmalerba): We use a component instead of a directive here so the user can use implicit\n// template reference variables (e.g. #d vs #d=\"matDatepicker\"). We can change this to a directive\n// if angular adds support for `exportAs: '$implicit'` on directives.\n/** Component responsible for managing the datepicker popup/dialog. */\nclass MatDatepicker extends MatDatepickerBase {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDatepicker_BaseFactory;\n    return function MatDatepicker_Factory(__ngFactoryType__) {\n      return (ɵMatDatepicker_BaseFactory || (ɵMatDatepicker_BaseFactory = i0.ɵɵgetInheritedFactory(MatDatepicker)))(__ngFactoryType__ || MatDatepicker);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDatepicker,\n    selectors: [[\"mat-datepicker\"]],\n    exportAs: [\"matDatepicker\"],\n    features: [i0.ɵɵProvidersFeature([MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER, {\n      provide: MatDatepickerBase,\n      useExisting: MatDatepicker\n    }]), i0.ɵɵInheritDefinitionFeature],\n    decls: 0,\n    vars: 0,\n    template: function MatDatepicker_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepicker, [{\n    type: Component,\n    args: [{\n      selector: 'mat-datepicker',\n      template: '',\n      exportAs: 'matDatepicker',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER, {\n        provide: MatDatepickerBase,\n        useExisting: MatDatepicker\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * An event used for datepicker input and change events. We don't always have access to a native\n * input or change event because the event may have been triggered by the user clicking on the\n * calendar popup. For consistency, we always use MatDatepickerInputEvent instead.\n */\nclass MatDatepickerInputEvent {\n  target;\n  targetElement;\n  /** The new value for the target datepicker input. */\n  value;\n  constructor(/** Reference to the datepicker input component that emitted the event. */\n  target, /** Reference to the native input element associated with the datepicker input. */\n  targetElement) {\n    this.target = target;\n    this.targetElement = targetElement;\n    this.value = this.target.value;\n  }\n}\n/** Base class for datepicker inputs. */\nclass MatDatepickerInputBase {\n  _elementRef = inject(ElementRef);\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _dateFormats = inject(MAT_DATE_FORMATS, {\n    optional: true\n  });\n  /** Whether the component has been initialized. */\n  _isInitialized;\n  /** The value of the input. */\n  get value() {\n    return this._model ? this._getValueFromModel(this._model.selection) : this._pendingValue;\n  }\n  set value(value) {\n    this._assignValueProgrammatically(value);\n  }\n  _model;\n  /** Whether the datepicker-input is disabled. */\n  get disabled() {\n    return !!this._disabled || this._parentDisabled();\n  }\n  set disabled(value) {\n    const newValue = value;\n    const element = this._elementRef.nativeElement;\n    if (this._disabled !== newValue) {\n      this._disabled = newValue;\n      this.stateChanges.next(undefined);\n    }\n    // We need to null check the `blur` method, because it's undefined during SSR.\n    // In Ivy static bindings are invoked earlier, before the element is attached to the DOM.\n    // This can cause an error to be thrown in some browsers (IE/Edge) which assert that the\n    // element has been inserted.\n    if (newValue && this._isInitialized && element.blur) {\n      // Normally, native input elements automatically blur if they turn disabled. This behavior\n      // is problematic, because it would mean that it triggers another change detection cycle,\n      // which then causes a changed after checked error if the input element was focused before.\n      element.blur();\n    }\n  }\n  _disabled;\n  /** Emits when a `change` event is fired on this `<input>`. */\n  dateChange = new EventEmitter();\n  /** Emits when an `input` event is fired on this `<input>`. */\n  dateInput = new EventEmitter();\n  /** Emits when the internal state has changed */\n  stateChanges = new Subject();\n  _onTouched = () => {};\n  _validatorOnChange = () => {};\n  _cvaOnChange = () => {};\n  _valueChangesSubscription = Subscription.EMPTY;\n  _localeSubscription = Subscription.EMPTY;\n  /**\n   * Since the value is kept on the model which is assigned in an Input,\n   * we might get a value before we have a model. This property keeps track\n   * of the value until we have somewhere to assign it.\n   */\n  _pendingValue;\n  /** The form control validator for whether the input parses. */\n  _parseValidator = () => {\n    return this._lastValueValid ? null : {\n      'matDatepickerParse': {\n        'text': this._elementRef.nativeElement.value\n      }\n    };\n  };\n  /** The form control validator for the date filter. */\n  _filterValidator = control => {\n    const controlValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(control.value));\n    return !controlValue || this._matchesFilter(controlValue) ? null : {\n      'matDatepickerFilter': true\n    };\n  };\n  /** The form control validator for the min date. */\n  _minValidator = control => {\n    const controlValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(control.value));\n    const min = this._getMinDate();\n    return !min || !controlValue || this._dateAdapter.compareDate(min, controlValue) <= 0 ? null : {\n      'matDatepickerMin': {\n        'min': min,\n        'actual': controlValue\n      }\n    };\n  };\n  /** The form control validator for the max date. */\n  _maxValidator = control => {\n    const controlValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(control.value));\n    const max = this._getMaxDate();\n    return !max || !controlValue || this._dateAdapter.compareDate(max, controlValue) >= 0 ? null : {\n      'matDatepickerMax': {\n        'max': max,\n        'actual': controlValue\n      }\n    };\n  };\n  /** Gets the base validator functions. */\n  _getValidators() {\n    return [this._parseValidator, this._minValidator, this._maxValidator, this._filterValidator];\n  }\n  /** Registers a date selection model with the input. */\n  _registerModel(model) {\n    this._model = model;\n    this._valueChangesSubscription.unsubscribe();\n    if (this._pendingValue) {\n      this._assignValue(this._pendingValue);\n    }\n    this._valueChangesSubscription = this._model.selectionChanged.subscribe(event => {\n      if (this._shouldHandleChangeEvent(event)) {\n        const value = this._getValueFromModel(event.selection);\n        this._lastValueValid = this._isValidValue(value);\n        this._cvaOnChange(value);\n        this._onTouched();\n        this._formatValue(value);\n        this.dateInput.emit(new MatDatepickerInputEvent(this, this._elementRef.nativeElement));\n        this.dateChange.emit(new MatDatepickerInputEvent(this, this._elementRef.nativeElement));\n      }\n    });\n  }\n  /** Whether the last value set on the input was valid. */\n  _lastValueValid = false;\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._dateAdapter) {\n        throw createMissingDateImplError('DateAdapter');\n      }\n      if (!this._dateFormats) {\n        throw createMissingDateImplError('MAT_DATE_FORMATS');\n      }\n    }\n    // Update the displayed date when the locale changes.\n    this._localeSubscription = this._dateAdapter.localeChanges.subscribe(() => {\n      this._assignValueProgrammatically(this.value);\n    });\n  }\n  ngAfterViewInit() {\n    this._isInitialized = true;\n  }\n  ngOnChanges(changes) {\n    if (dateInputsHaveChanged(changes, this._dateAdapter)) {\n      this.stateChanges.next(undefined);\n    }\n  }\n  ngOnDestroy() {\n    this._valueChangesSubscription.unsubscribe();\n    this._localeSubscription.unsubscribe();\n    this.stateChanges.complete();\n  }\n  /** @docs-private */\n  registerOnValidatorChange(fn) {\n    this._validatorOnChange = fn;\n  }\n  /** @docs-private */\n  validate(c) {\n    return this._validator ? this._validator(c) : null;\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    this._assignValueProgrammatically(value);\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._cvaOnChange = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  _onKeydown(event) {\n    const ctrlShiftMetaModifiers = ['ctrlKey', 'shiftKey', 'metaKey'];\n    const isAltDownArrow = hasModifierKey(event, 'altKey') && event.keyCode === DOWN_ARROW && ctrlShiftMetaModifiers.every(modifier => !hasModifierKey(event, modifier));\n    if (isAltDownArrow && !this._elementRef.nativeElement.readOnly) {\n      this._openPopup();\n      event.preventDefault();\n    }\n  }\n  _onInput(value) {\n    const lastValueWasValid = this._lastValueValid;\n    let date = this._dateAdapter.parse(value, this._dateFormats.parse.dateInput);\n    this._lastValueValid = this._isValidValue(date);\n    date = this._dateAdapter.getValidDateOrNull(date);\n    const hasChanged = !this._dateAdapter.sameDate(date, this.value);\n    // We need to fire the CVA change event for all\n    // nulls, otherwise the validators won't run.\n    if (!date || hasChanged) {\n      this._cvaOnChange(date);\n    } else {\n      // Call the CVA change handler for invalid values\n      // since this is what marks the control as dirty.\n      if (value && !this.value) {\n        this._cvaOnChange(date);\n      }\n      if (lastValueWasValid !== this._lastValueValid) {\n        this._validatorOnChange();\n      }\n    }\n    if (hasChanged) {\n      this._assignValue(date);\n      this.dateInput.emit(new MatDatepickerInputEvent(this, this._elementRef.nativeElement));\n    }\n  }\n  _onChange() {\n    this.dateChange.emit(new MatDatepickerInputEvent(this, this._elementRef.nativeElement));\n  }\n  /** Handles blur events on the input. */\n  _onBlur() {\n    // Reformat the input only if we have a valid value.\n    if (this.value) {\n      this._formatValue(this.value);\n    }\n    this._onTouched();\n  }\n  /** Formats a value and sets it on the input element. */\n  _formatValue(value) {\n    this._elementRef.nativeElement.value = value != null ? this._dateAdapter.format(value, this._dateFormats.display.dateInput) : '';\n  }\n  /** Assigns a value to the model. */\n  _assignValue(value) {\n    // We may get some incoming values before the model was\n    // assigned. Save the value so that we can assign it later.\n    if (this._model) {\n      this._assignValueToModel(value);\n      this._pendingValue = null;\n    } else {\n      this._pendingValue = value;\n    }\n  }\n  /** Whether a value is considered valid. */\n  _isValidValue(value) {\n    return !value || this._dateAdapter.isValid(value);\n  }\n  /**\n   * Checks whether a parent control is disabled. This is in place so that it can be overridden\n   * by inputs extending this one which can be placed inside of a group that can be disabled.\n   */\n  _parentDisabled() {\n    return false;\n  }\n  /** Programmatically assigns a value to the input. */\n  _assignValueProgrammatically(value) {\n    value = this._dateAdapter.deserialize(value);\n    this._lastValueValid = this._isValidValue(value);\n    value = this._dateAdapter.getValidDateOrNull(value);\n    this._assignValue(value);\n    this._formatValue(value);\n  }\n  /** Gets whether a value matches the current date filter. */\n  _matchesFilter(value) {\n    const filter = this._getDateFilter();\n    return !filter || filter(value);\n  }\n  static ɵfac = function MatDatepickerInputBase_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerInputBase)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDatepickerInputBase,\n    inputs: {\n      value: \"value\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      dateChange: \"dateChange\",\n      dateInput: \"dateInput\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerInputBase, [{\n    type: Directive\n  }], () => [], {\n    value: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    dateChange: [{\n      type: Output\n    }],\n    dateInput: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Checks whether the `SimpleChanges` object from an `ngOnChanges`\n * callback has any changes, accounting for date objects.\n */\nfunction dateInputsHaveChanged(changes, adapter) {\n  const keys = Object.keys(changes);\n  for (let key of keys) {\n    const {\n      previousValue,\n      currentValue\n    } = changes[key];\n    if (adapter.isDateInstance(previousValue) && adapter.isDateInstance(currentValue)) {\n      if (!adapter.sameDate(previousValue, currentValue)) {\n        return true;\n      }\n    } else {\n      return true;\n    }\n  }\n  return false;\n}\n\n/** @docs-private */\nconst MAT_DATEPICKER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatDatepickerInput),\n  multi: true\n};\n/** @docs-private */\nconst MAT_DATEPICKER_VALIDATORS = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatDatepickerInput),\n  multi: true\n};\n/** Directive used to connect an input to a MatDatepicker. */\nclass MatDatepickerInput extends MatDatepickerInputBase {\n  _formField = inject(MAT_FORM_FIELD, {\n    optional: true\n  });\n  _closedSubscription = Subscription.EMPTY;\n  _openedSubscription = Subscription.EMPTY;\n  /** The datepicker that this input is associated with. */\n  set matDatepicker(datepicker) {\n    if (datepicker) {\n      this._datepicker = datepicker;\n      this._ariaOwns.set(datepicker.opened ? datepicker.id : null);\n      this._closedSubscription = datepicker.closedStream.subscribe(() => {\n        this._onTouched();\n        this._ariaOwns.set(null);\n      });\n      this._openedSubscription = datepicker.openedStream.subscribe(() => {\n        this._ariaOwns.set(datepicker.id);\n      });\n      this._registerModel(datepicker.registerInput(this));\n    }\n  }\n  _datepicker;\n  /** The id of the panel owned by this input. */\n  _ariaOwns = signal(null);\n  /** The minimum valid date. */\n  get min() {\n    return this._min;\n  }\n  set min(value) {\n    const validValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    if (!this._dateAdapter.sameDate(validValue, this._min)) {\n      this._min = validValue;\n      this._validatorOnChange();\n    }\n  }\n  _min;\n  /** The maximum valid date. */\n  get max() {\n    return this._max;\n  }\n  set max(value) {\n    const validValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    if (!this._dateAdapter.sameDate(validValue, this._max)) {\n      this._max = validValue;\n      this._validatorOnChange();\n    }\n  }\n  _max;\n  /** Function that can be used to filter out dates within the datepicker. */\n  get dateFilter() {\n    return this._dateFilter;\n  }\n  set dateFilter(value) {\n    const wasMatchingValue = this._matchesFilter(this.value);\n    this._dateFilter = value;\n    if (this._matchesFilter(this.value) !== wasMatchingValue) {\n      this._validatorOnChange();\n    }\n  }\n  _dateFilter;\n  /** The combined form control validator for this input. */\n  _validator;\n  constructor() {\n    super();\n    this._validator = Validators.compose(super._getValidators());\n  }\n  /**\n   * Gets the element that the datepicker popup should be connected to.\n   * @return The element to connect the popup to.\n   */\n  getConnectedOverlayOrigin() {\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._elementRef;\n  }\n  /** Gets the ID of an element that should be used a description for the calendar overlay. */\n  getOverlayLabelId() {\n    if (this._formField) {\n      return this._formField.getLabelId();\n    }\n    return this._elementRef.nativeElement.getAttribute('aria-labelledby');\n  }\n  /** Returns the palette used by the input's form field, if any. */\n  getThemePalette() {\n    return this._formField ? this._formField.color : undefined;\n  }\n  /** Gets the value at which the calendar should start. */\n  getStartValue() {\n    return this.value;\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._closedSubscription.unsubscribe();\n    this._openedSubscription.unsubscribe();\n  }\n  /** Opens the associated datepicker. */\n  _openPopup() {\n    if (this._datepicker) {\n      this._datepicker.open();\n    }\n  }\n  _getValueFromModel(modelValue) {\n    return modelValue;\n  }\n  _assignValueToModel(value) {\n    if (this._model) {\n      this._model.updateSelection(value, this);\n    }\n  }\n  /** Gets the input's minimum date. */\n  _getMinDate() {\n    return this._min;\n  }\n  /** Gets the input's maximum date. */\n  _getMaxDate() {\n    return this._max;\n  }\n  /** Gets the input's date filtering function. */\n  _getDateFilter() {\n    return this._dateFilter;\n  }\n  _shouldHandleChangeEvent(event) {\n    return event.source !== this;\n  }\n  static ɵfac = function MatDatepickerInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerInput)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDatepickerInput,\n    selectors: [[\"input\", \"matDatepicker\", \"\"]],\n    hostAttrs: [1, \"mat-datepicker-input\"],\n    hostVars: 6,\n    hostBindings: function MatDatepickerInput_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function MatDatepickerInput_input_HostBindingHandler($event) {\n          return ctx._onInput($event.target.value);\n        })(\"change\", function MatDatepickerInput_change_HostBindingHandler() {\n          return ctx._onChange();\n        })(\"blur\", function MatDatepickerInput_blur_HostBindingHandler() {\n          return ctx._onBlur();\n        })(\"keydown\", function MatDatepickerInput_keydown_HostBindingHandler($event) {\n          return ctx._onKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"disabled\", ctx.disabled);\n        i0.ɵɵattribute(\"aria-haspopup\", ctx._datepicker ? \"dialog\" : null)(\"aria-owns\", ctx._ariaOwns())(\"min\", ctx.min ? ctx._dateAdapter.toIso8601(ctx.min) : null)(\"max\", ctx.max ? ctx._dateAdapter.toIso8601(ctx.max) : null)(\"data-mat-calendar\", ctx._datepicker ? ctx._datepicker.id : null);\n      }\n    },\n    inputs: {\n      matDatepicker: \"matDatepicker\",\n      min: \"min\",\n      max: \"max\",\n      dateFilter: [0, \"matDatepickerFilter\", \"dateFilter\"]\n    },\n    exportAs: [\"matDatepickerInput\"],\n    features: [i0.ɵɵProvidersFeature([MAT_DATEPICKER_VALUE_ACCESSOR, MAT_DATEPICKER_VALIDATORS, {\n      provide: MAT_INPUT_VALUE_ACCESSOR,\n      useExisting: MatDatepickerInput\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerInput, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matDatepicker]',\n      providers: [MAT_DATEPICKER_VALUE_ACCESSOR, MAT_DATEPICKER_VALIDATORS, {\n        provide: MAT_INPUT_VALUE_ACCESSOR,\n        useExisting: MatDatepickerInput\n      }],\n      host: {\n        'class': 'mat-datepicker-input',\n        '[attr.aria-haspopup]': '_datepicker ? \"dialog\" : null',\n        '[attr.aria-owns]': '_ariaOwns()',\n        '[attr.min]': 'min ? _dateAdapter.toIso8601(min) : null',\n        '[attr.max]': 'max ? _dateAdapter.toIso8601(max) : null',\n        // Used by the test harness to tie this input to its calendar. We can't depend on\n        // `aria-owns` for this, because it's only defined while the calendar is open.\n        '[attr.data-mat-calendar]': '_datepicker ? _datepicker.id : null',\n        '[disabled]': 'disabled',\n        '(input)': '_onInput($event.target.value)',\n        '(change)': '_onChange()',\n        '(blur)': '_onBlur()',\n        '(keydown)': '_onKeydown($event)'\n      },\n      exportAs: 'matDatepickerInput'\n    }]\n  }], () => [], {\n    matDatepicker: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    dateFilter: [{\n      type: Input,\n      args: ['matDatepickerFilter']\n    }]\n  });\n})();\n\n/** Can be used to override the icon of a `matDatepickerToggle`. */\nclass MatDatepickerToggleIcon {\n  static ɵfac = function MatDatepickerToggleIcon_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerToggleIcon)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDatepickerToggleIcon,\n    selectors: [[\"\", \"matDatepickerToggleIcon\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerToggleIcon, [{\n    type: Directive,\n    args: [{\n      selector: '[matDatepickerToggleIcon]'\n    }]\n  }], null, null);\n})();\nclass MatDatepickerToggle {\n  _intl = inject(MatDatepickerIntl);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _stateChanges = Subscription.EMPTY;\n  /** Datepicker instance that the button will toggle. */\n  datepicker;\n  /** Tabindex for the toggle. */\n  tabIndex;\n  /** Screen-reader label for the button. */\n  ariaLabel;\n  /** Whether the toggle button is disabled. */\n  get disabled() {\n    if (this._disabled === undefined && this.datepicker) {\n      return this.datepicker.disabled;\n    }\n    return !!this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  _disabled;\n  /** Whether ripples on the toggle should be disabled. */\n  disableRipple;\n  /** Custom icon set by the consumer. */\n  _customIcon;\n  /** Underlying button element. */\n  _button;\n  constructor() {\n    const defaultTabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    const parsedTabIndex = Number(defaultTabIndex);\n    this.tabIndex = parsedTabIndex || parsedTabIndex === 0 ? parsedTabIndex : null;\n  }\n  ngOnChanges(changes) {\n    if (changes['datepicker']) {\n      this._watchStateChanges();\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.unsubscribe();\n  }\n  ngAfterContentInit() {\n    this._watchStateChanges();\n  }\n  _open(event) {\n    if (this.datepicker && !this.disabled) {\n      this.datepicker.open();\n      event.stopPropagation();\n    }\n  }\n  _watchStateChanges() {\n    const datepickerStateChanged = this.datepicker ? this.datepicker.stateChanges : of();\n    const inputStateChanged = this.datepicker && this.datepicker.datepickerInput ? this.datepicker.datepickerInput.stateChanges : of();\n    const datepickerToggled = this.datepicker ? merge(this.datepicker.openedStream, this.datepicker.closedStream) : of();\n    this._stateChanges.unsubscribe();\n    this._stateChanges = merge(this._intl.changes, datepickerStateChanged, inputStateChanged, datepickerToggled).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  static ɵfac = function MatDatepickerToggle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerToggle)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDatepickerToggle,\n    selectors: [[\"mat-datepicker-toggle\"]],\n    contentQueries: function MatDatepickerToggle_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatDatepickerToggleIcon, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._customIcon = _t.first);\n      }\n    },\n    viewQuery: function MatDatepickerToggle_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._button = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-datepicker-toggle\"],\n    hostVars: 8,\n    hostBindings: function MatDatepickerToggle_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatDatepickerToggle_click_HostBindingHandler($event) {\n          return ctx._open($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"tabindex\", null)(\"data-mat-calendar\", ctx.datepicker ? ctx.datepicker.id : null);\n        i0.ɵɵclassProp(\"mat-datepicker-toggle-active\", ctx.datepicker && ctx.datepicker.opened)(\"mat-accent\", ctx.datepicker && ctx.datepicker.color === \"accent\")(\"mat-warn\", ctx.datepicker && ctx.datepicker.color === \"warn\");\n      }\n    },\n    inputs: {\n      datepicker: [0, \"for\", \"datepicker\"],\n      tabIndex: \"tabIndex\",\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      disableRipple: \"disableRipple\"\n    },\n    exportAs: [\"matDatepickerToggle\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c4,\n    decls: 4,\n    vars: 7,\n    consts: [[\"button\", \"\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 3, \"disabled\", \"disableRipple\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"fill\", \"currentColor\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-datepicker-toggle-default-icon\"], [\"d\", \"M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z\"]],\n    template: function MatDatepickerToggle_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c3);\n        i0.ɵɵelementStart(0, \"button\", 1, 0);\n        i0.ɵɵtemplate(2, MatDatepickerToggle_Conditional_2_Template, 2, 0, \":svg:svg\", 2);\n        i0.ɵɵprojection(3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"disabled\", ctx.disabled)(\"disableRipple\", ctx.disableRipple);\n        i0.ɵɵattribute(\"aria-haspopup\", ctx.datepicker ? \"dialog\" : null)(\"aria-label\", ctx.ariaLabel || ctx._intl.openCalendarLabel)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-expanded\", ctx.datepicker ? ctx.datepicker.opened : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(!ctx._customIcon ? 2 : -1);\n      }\n    },\n    dependencies: [MatIconButton],\n    styles: [\".mat-datepicker-toggle{pointer-events:auto;color:var(--mat-datepicker-toggle-icon-color, var(--mat-sys-on-surface-variant))}.mat-datepicker-toggle-active{color:var(--mat-datepicker-toggle-active-state-icon-color, var(--mat-sys-on-surface-variant))}@media(forced-colors: active){.mat-datepicker-toggle-default-icon{color:CanvasText}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerToggle, [{\n    type: Component,\n    args: [{\n      selector: 'mat-datepicker-toggle',\n      host: {\n        'class': 'mat-datepicker-toggle',\n        '[attr.tabindex]': 'null',\n        '[class.mat-datepicker-toggle-active]': 'datepicker && datepicker.opened',\n        '[class.mat-accent]': 'datepicker && datepicker.color === \"accent\"',\n        '[class.mat-warn]': 'datepicker && datepicker.color === \"warn\"',\n        // Used by the test harness to tie this toggle to its datepicker.\n        '[attr.data-mat-calendar]': 'datepicker ? datepicker.id : null',\n        // Bind the `click` on the host, rather than the inner `button`, so that we can call\n        // `stopPropagation` on it without affecting the user's `click` handlers. We need to stop\n        // it so that the input doesn't get focused automatically by the form field (See #21836).\n        '(click)': '_open($event)'\n      },\n      exportAs: 'matDatepickerToggle',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatIconButton],\n      template: \"<button\\n  #button\\n  mat-icon-button\\n  type=\\\"button\\\"\\n  [attr.aria-haspopup]=\\\"datepicker ? 'dialog' : null\\\"\\n  [attr.aria-label]=\\\"ariaLabel || _intl.openCalendarLabel\\\"\\n  [attr.tabindex]=\\\"disabled ? -1 : tabIndex\\\"\\n  [attr.aria-expanded]=\\\"datepicker ? datepicker.opened : null\\\"\\n  [disabled]=\\\"disabled\\\"\\n  [disableRipple]=\\\"disableRipple\\\">\\n\\n  @if (!_customIcon) {\\n    <svg\\n      class=\\\"mat-datepicker-toggle-default-icon\\\"\\n      viewBox=\\\"0 0 24 24\\\"\\n      width=\\\"24px\\\"\\n      height=\\\"24px\\\"\\n      fill=\\\"currentColor\\\"\\n      focusable=\\\"false\\\"\\n      aria-hidden=\\\"true\\\">\\n      <path d=\\\"M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z\\\"/>\\n    </svg>\\n  }\\n\\n  <ng-content select=\\\"[matDatepickerToggleIcon]\\\"></ng-content>\\n</button>\\n\",\n      styles: [\".mat-datepicker-toggle{pointer-events:auto;color:var(--mat-datepicker-toggle-icon-color, var(--mat-sys-on-surface-variant))}.mat-datepicker-toggle-active{color:var(--mat-datepicker-toggle-active-state-icon-color, var(--mat-sys-on-surface-variant))}@media(forced-colors: active){.mat-datepicker-toggle-default-icon{color:CanvasText}}\\n\"]\n    }]\n  }], () => [], {\n    datepicker: [{\n      type: Input,\n      args: ['for']\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input\n    }],\n    _customIcon: [{\n      type: ContentChild,\n      args: [MatDatepickerToggleIcon]\n    }],\n    _button: [{\n      type: ViewChild,\n      args: ['button']\n    }]\n  });\n})();\nclass MatDateRangeInput {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _dateAdapter = inject(DateAdapter, {\n    optional: true\n  });\n  _formField = inject(MAT_FORM_FIELD, {\n    optional: true\n  });\n  _closedSubscription = Subscription.EMPTY;\n  _openedSubscription = Subscription.EMPTY;\n  _startInput;\n  _endInput;\n  /** Current value of the range input. */\n  get value() {\n    return this._model ? this._model.selection : null;\n  }\n  /** Unique ID for the group. */\n  id = inject(_IdGenerator).getId('mat-date-range-input-');\n  /** Whether the control is focused. */\n  focused = false;\n  /** Whether the control's label should float. */\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n  /** Name of the form control. */\n  controlType = 'mat-date-range-input';\n  /**\n   * Implemented as a part of `MatFormFieldControl`.\n   * Set the placeholder attribute on `matStartDate` and `matEndDate`.\n   * @docs-private\n   */\n  get placeholder() {\n    const start = this._startInput?._getPlaceholder() || '';\n    const end = this._endInput?._getPlaceholder() || '';\n    return start || end ? `${start} ${this.separator} ${end}` : '';\n  }\n  /** The range picker that this input is associated with. */\n  get rangePicker() {\n    return this._rangePicker;\n  }\n  set rangePicker(rangePicker) {\n    if (rangePicker) {\n      this._model = rangePicker.registerInput(this);\n      this._rangePicker = rangePicker;\n      this._closedSubscription.unsubscribe();\n      this._openedSubscription.unsubscribe();\n      this._ariaOwns.set(this.rangePicker.opened ? rangePicker.id : null);\n      this._closedSubscription = rangePicker.closedStream.subscribe(() => {\n        this._startInput?._onTouched();\n        this._endInput?._onTouched();\n        this._ariaOwns.set(null);\n      });\n      this._openedSubscription = rangePicker.openedStream.subscribe(() => {\n        this._ariaOwns.set(rangePicker.id);\n      });\n      this._registerModel(this._model);\n    }\n  }\n  _rangePicker;\n  /** The id of the panel owned by this input. */\n  _ariaOwns = signal(null);\n  /** Whether the input is required. */\n  get required() {\n    return this._required ?? (this._isTargetRequired(this) || this._isTargetRequired(this._startInput) || this._isTargetRequired(this._endInput)) ?? false;\n  }\n  set required(value) {\n    this._required = value;\n  }\n  _required;\n  /** Function that can be used to filter out dates within the date range picker. */\n  get dateFilter() {\n    return this._dateFilter;\n  }\n  set dateFilter(value) {\n    const start = this._startInput;\n    const end = this._endInput;\n    const wasMatchingStart = start && start._matchesFilter(start.value);\n    const wasMatchingEnd = end && end._matchesFilter(start.value);\n    this._dateFilter = value;\n    if (start && start._matchesFilter(start.value) !== wasMatchingStart) {\n      start._validatorOnChange();\n    }\n    if (end && end._matchesFilter(end.value) !== wasMatchingEnd) {\n      end._validatorOnChange();\n    }\n  }\n  _dateFilter;\n  /** The minimum valid date. */\n  get min() {\n    return this._min;\n  }\n  set min(value) {\n    const validValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    if (!this._dateAdapter.sameDate(validValue, this._min)) {\n      this._min = validValue;\n      this._revalidate();\n    }\n  }\n  _min;\n  /** The maximum valid date. */\n  get max() {\n    return this._max;\n  }\n  set max(value) {\n    const validValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    if (!this._dateAdapter.sameDate(validValue, this._max)) {\n      this._max = validValue;\n      this._revalidate();\n    }\n  }\n  _max;\n  /** Whether the input is disabled. */\n  get disabled() {\n    return this._startInput && this._endInput ? this._startInput.disabled && this._endInput.disabled : this._groupDisabled;\n  }\n  set disabled(value) {\n    if (value !== this._groupDisabled) {\n      this._groupDisabled = value;\n      this.stateChanges.next(undefined);\n    }\n  }\n  _groupDisabled = false;\n  /** Whether the input is in an error state. */\n  get errorState() {\n    if (this._startInput && this._endInput) {\n      return this._startInput.errorState || this._endInput.errorState;\n    }\n    return false;\n  }\n  /** Whether the datepicker input is empty. */\n  get empty() {\n    const startEmpty = this._startInput ? this._startInput.isEmpty() : false;\n    const endEmpty = this._endInput ? this._endInput.isEmpty() : false;\n    return startEmpty && endEmpty;\n  }\n  /** Value for the `aria-describedby` attribute of the inputs. */\n  _ariaDescribedBy = null;\n  /** Date selection model currently registered with the input. */\n  _model;\n  /** Separator text to be shown between the inputs. */\n  separator = '–';\n  /** Start of the comparison range that should be shown in the calendar. */\n  comparisonStart = null;\n  /** End of the comparison range that should be shown in the calendar. */\n  comparisonEnd = null;\n  /**\n   * Implemented as a part of `MatFormFieldControl`.\n   * TODO(crisbeto): change type to `AbstractControlDirective` after #18206 lands.\n   * @docs-private\n   */\n  ngControl;\n  /** Emits when the input's state has changed. */\n  stateChanges = new Subject();\n  /**\n   * Disable the automatic labeling to avoid issues like #27241.\n   * @docs-private\n   */\n  disableAutomaticLabeling = true;\n  constructor() {\n    if (!this._dateAdapter && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw createMissingDateImplError('DateAdapter');\n    }\n    // The datepicker module can be used both with MDC and non-MDC form fields. We have\n    // to conditionally add the MDC input class so that the range picker looks correctly.\n    if (this._formField?._elementRef.nativeElement.classList.contains('mat-mdc-form-field')) {\n      this._elementRef.nativeElement.classList.add('mat-mdc-input-element', 'mat-mdc-form-field-input-control', 'mdc-text-field__input');\n    }\n    // TODO(crisbeto): remove `as any` after #18206 lands.\n    this.ngControl = inject(ControlContainer, {\n      optional: true,\n      self: true\n    });\n  }\n  /**\n   * Implemented as a part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    this._ariaDescribedBy = ids.length ? ids.join(' ') : null;\n  }\n  /**\n   * Implemented as a part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  onContainerClick() {\n    if (!this.focused && !this.disabled) {\n      if (!this._model || !this._model.selection.start) {\n        this._startInput.focus();\n      } else {\n        this._endInput.focus();\n      }\n    }\n  }\n  ngAfterContentInit() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._startInput) {\n        throw Error('mat-date-range-input must contain a matStartDate input');\n      }\n      if (!this._endInput) {\n        throw Error('mat-date-range-input must contain a matEndDate input');\n      }\n    }\n    if (this._model) {\n      this._registerModel(this._model);\n    }\n    // We don't need to unsubscribe from this, because we\n    // know that the input streams will be completed on destroy.\n    merge(this._startInput.stateChanges, this._endInput.stateChanges).subscribe(() => {\n      this.stateChanges.next(undefined);\n    });\n  }\n  ngOnChanges(changes) {\n    if (dateInputsHaveChanged(changes, this._dateAdapter)) {\n      this.stateChanges.next(undefined);\n    }\n  }\n  ngOnDestroy() {\n    this._closedSubscription.unsubscribe();\n    this._openedSubscription.unsubscribe();\n    this.stateChanges.complete();\n  }\n  /** Gets the date at which the calendar should start. */\n  getStartValue() {\n    return this.value ? this.value.start : null;\n  }\n  /** Gets the input's theme palette. */\n  getThemePalette() {\n    return this._formField ? this._formField.color : undefined;\n  }\n  /** Gets the element to which the calendar overlay should be attached. */\n  getConnectedOverlayOrigin() {\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._elementRef;\n  }\n  /** Gets the ID of an element that should be used a description for the calendar overlay. */\n  getOverlayLabelId() {\n    return this._formField ? this._formField.getLabelId() : null;\n  }\n  /** Gets the value that is used to mirror the state input. */\n  _getInputMirrorValue(part) {\n    const input = part === 'start' ? this._startInput : this._endInput;\n    return input ? input.getMirrorValue() : '';\n  }\n  /** Whether the input placeholders should be hidden. */\n  _shouldHidePlaceholders() {\n    return this._startInput ? !this._startInput.isEmpty() : false;\n  }\n  /** Handles the value in one of the child inputs changing. */\n  _handleChildValueChange() {\n    this.stateChanges.next(undefined);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Opens the date range picker associated with the input. */\n  _openDatepicker() {\n    if (this._rangePicker) {\n      this._rangePicker.open();\n    }\n  }\n  /** Whether the separate text should be hidden. */\n  _shouldHideSeparator() {\n    return (!this._formField || this._formField.getLabelId() && !this._formField._shouldLabelFloat()) && this.empty;\n  }\n  /** Gets the value for the `aria-labelledby` attribute of the inputs. */\n  _getAriaLabelledby() {\n    const formField = this._formField;\n    return formField && formField._hasFloatingLabel() ? formField._labelId : null;\n  }\n  _getStartDateAccessibleName() {\n    return this._startInput._getAccessibleName();\n  }\n  _getEndDateAccessibleName() {\n    return this._endInput._getAccessibleName();\n  }\n  /** Updates the focused state of the range input. */\n  _updateFocus(origin) {\n    this.focused = origin !== null;\n    this.stateChanges.next();\n  }\n  /** Re-runs the validators on the start/end inputs. */\n  _revalidate() {\n    if (this._startInput) {\n      this._startInput._validatorOnChange();\n    }\n    if (this._endInput) {\n      this._endInput._validatorOnChange();\n    }\n  }\n  /** Registers the current date selection model with the start/end inputs. */\n  _registerModel(model) {\n    if (this._startInput) {\n      this._startInput._registerModel(model);\n    }\n    if (this._endInput) {\n      this._endInput._registerModel(model);\n    }\n  }\n  /** Checks whether a specific range input directive is required. */\n  _isTargetRequired(target) {\n    return target?.ngControl?.control?.hasValidator(Validators.required);\n  }\n  static ɵfac = function MatDateRangeInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDateRangeInput)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDateRangeInput,\n    selectors: [[\"mat-date-range-input\"]],\n    hostAttrs: [\"role\", \"group\", 1, \"mat-date-range-input\"],\n    hostVars: 8,\n    hostBindings: function MatDateRangeInput_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.id)(\"aria-labelledby\", ctx._getAriaLabelledby())(\"aria-describedby\", ctx._ariaDescribedBy)(\"data-mat-calendar\", ctx.rangePicker ? ctx.rangePicker.id : null);\n        i0.ɵɵclassProp(\"mat-date-range-input-hide-placeholders\", ctx._shouldHidePlaceholders())(\"mat-date-range-input-required\", ctx.required);\n      }\n    },\n    inputs: {\n      rangePicker: \"rangePicker\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      dateFilter: \"dateFilter\",\n      min: \"min\",\n      max: \"max\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      separator: \"separator\",\n      comparisonStart: \"comparisonStart\",\n      comparisonEnd: \"comparisonEnd\"\n    },\n    exportAs: [\"matDateRangeInput\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatFormFieldControl,\n      useExisting: MatDateRangeInput\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c6,\n    decls: 11,\n    vars: 5,\n    consts: [[\"cdkMonitorSubtreeFocus\", \"\", 1, \"mat-date-range-input-container\", 3, \"cdkFocusChange\"], [1, \"mat-date-range-input-wrapper\"], [\"aria-hidden\", \"true\", 1, \"mat-date-range-input-mirror\"], [1, \"mat-date-range-input-separator\"], [1, \"mat-date-range-input-wrapper\", \"mat-date-range-input-end-wrapper\"]],\n    template: function MatDateRangeInput_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c5);\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵlistener(\"cdkFocusChange\", function MatDateRangeInput_Template_div_cdkFocusChange_0_listener($event) {\n          return ctx._updateFocus($event);\n        });\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementStart(3, \"span\", 2);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 4);\n        i0.ɵɵprojection(8, 1);\n        i0.ɵɵelementStart(9, \"span\", 2);\n        i0.ɵɵtext(10);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx._getInputMirrorValue(\"start\"));\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"mat-date-range-input-separator-hidden\", ctx._shouldHideSeparator());\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(ctx.separator);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx._getInputMirrorValue(\"end\"));\n      }\n    },\n    dependencies: [CdkMonitorFocus],\n    styles: [\".mat-date-range-input{display:block;width:100%}.mat-date-range-input-container{display:flex;align-items:center}.mat-date-range-input-separator{transition:opacity 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);margin:0 4px;color:var(--mat-datepicker-range-input-separator-color, var(--mat-sys-on-surface))}.mat-form-field-disabled .mat-date-range-input-separator{color:var(--mat-datepicker-range-input-disabled-state-separator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}._mat-animation-noopable .mat-date-range-input-separator{transition:none}.mat-date-range-input-separator-hidden{-webkit-user-select:none;user-select:none;opacity:0;transition:none}.mat-date-range-input-wrapper{position:relative;overflow:hidden;max-width:calc(50% - 4px)}.mat-date-range-input-end-wrapper{flex-grow:1}.mat-date-range-input-inner{position:absolute;top:0;left:0;font:inherit;background:rgba(0,0,0,0);color:currentColor;border:none;outline:none;padding:0;margin:0;vertical-align:bottom;text-align:inherit;-webkit-appearance:none;width:100%;height:100%}.mat-date-range-input-inner:-moz-ui-invalid{box-shadow:none}.mat-date-range-input-inner::placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner::-moz-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner::-webkit-input-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner:-ms-input-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner[disabled]{color:var(--mat-datepicker-range-input-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-hide-placeholder .mat-date-range-input-inner::placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}@media(forced-colors: active){.mat-form-field-hide-placeholder .mat-date-range-input-inner::placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::placeholder{opacity:0}}.mat-form-field-hide-placeholder .mat-date-range-input-inner::-moz-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-moz-placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}@media(forced-colors: active){.mat-form-field-hide-placeholder .mat-date-range-input-inner::-moz-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-moz-placeholder{opacity:0}}.mat-form-field-hide-placeholder .mat-date-range-input-inner::-webkit-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-webkit-input-placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}@media(forced-colors: active){.mat-form-field-hide-placeholder .mat-date-range-input-inner::-webkit-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-webkit-input-placeholder{opacity:0}}.mat-form-field-hide-placeholder .mat-date-range-input-inner:-ms-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner:-ms-input-placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}@media(forced-colors: active){.mat-form-field-hide-placeholder .mat-date-range-input-inner:-ms-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner:-ms-input-placeholder{opacity:0}}._mat-animation-noopable .mat-date-range-input-inner::placeholder{transition:none}._mat-animation-noopable .mat-date-range-input-inner::-moz-placeholder{transition:none}._mat-animation-noopable .mat-date-range-input-inner::-webkit-input-placeholder{transition:none}._mat-animation-noopable .mat-date-range-input-inner:-ms-input-placeholder{transition:none}.mat-date-range-input-mirror{-webkit-user-select:none;user-select:none;visibility:hidden;white-space:nowrap;display:inline-block;min-width:2px}.mat-mdc-form-field-type-mat-date-range-input .mat-mdc-form-field-infix{width:200px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDateRangeInput, [{\n    type: Component,\n    args: [{\n      selector: 'mat-date-range-input',\n      exportAs: 'matDateRangeInput',\n      host: {\n        'class': 'mat-date-range-input',\n        '[class.mat-date-range-input-hide-placeholders]': '_shouldHidePlaceholders()',\n        '[class.mat-date-range-input-required]': 'required',\n        '[attr.id]': 'id',\n        'role': 'group',\n        '[attr.aria-labelledby]': '_getAriaLabelledby()',\n        '[attr.aria-describedby]': '_ariaDescribedBy',\n        // Used by the test harness to tie this input to its calendar. We can't depend on\n        // `aria-owns` for this, because it's only defined while the calendar is open.\n        '[attr.data-mat-calendar]': 'rangePicker ? rangePicker.id : null'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatDateRangeInput\n      }],\n      imports: [CdkMonitorFocus],\n      template: \"<div\\n  class=\\\"mat-date-range-input-container\\\"\\n  cdkMonitorSubtreeFocus\\n  (cdkFocusChange)=\\\"_updateFocus($event)\\\">\\n  <div class=\\\"mat-date-range-input-wrapper\\\">\\n    <ng-content select=\\\"input[matStartDate]\\\"></ng-content>\\n    <span\\n      class=\\\"mat-date-range-input-mirror\\\"\\n      aria-hidden=\\\"true\\\">{{_getInputMirrorValue('start')}}</span>\\n  </div>\\n\\n  <span\\n    class=\\\"mat-date-range-input-separator\\\"\\n    [class.mat-date-range-input-separator-hidden]=\\\"_shouldHideSeparator()\\\">{{separator}}</span>\\n\\n  <div class=\\\"mat-date-range-input-wrapper mat-date-range-input-end-wrapper\\\">\\n    <ng-content select=\\\"input[matEndDate]\\\"></ng-content>\\n    <span\\n      class=\\\"mat-date-range-input-mirror\\\"\\n      aria-hidden=\\\"true\\\">{{_getInputMirrorValue('end')}}</span>\\n  </div>\\n</div>\\n\\n\",\n      styles: [\".mat-date-range-input{display:block;width:100%}.mat-date-range-input-container{display:flex;align-items:center}.mat-date-range-input-separator{transition:opacity 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);margin:0 4px;color:var(--mat-datepicker-range-input-separator-color, var(--mat-sys-on-surface))}.mat-form-field-disabled .mat-date-range-input-separator{color:var(--mat-datepicker-range-input-disabled-state-separator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}._mat-animation-noopable .mat-date-range-input-separator{transition:none}.mat-date-range-input-separator-hidden{-webkit-user-select:none;user-select:none;opacity:0;transition:none}.mat-date-range-input-wrapper{position:relative;overflow:hidden;max-width:calc(50% - 4px)}.mat-date-range-input-end-wrapper{flex-grow:1}.mat-date-range-input-inner{position:absolute;top:0;left:0;font:inherit;background:rgba(0,0,0,0);color:currentColor;border:none;outline:none;padding:0;margin:0;vertical-align:bottom;text-align:inherit;-webkit-appearance:none;width:100%;height:100%}.mat-date-range-input-inner:-moz-ui-invalid{box-shadow:none}.mat-date-range-input-inner::placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner::-moz-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner::-webkit-input-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner:-ms-input-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner[disabled]{color:var(--mat-datepicker-range-input-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-hide-placeholder .mat-date-range-input-inner::placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}@media(forced-colors: active){.mat-form-field-hide-placeholder .mat-date-range-input-inner::placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::placeholder{opacity:0}}.mat-form-field-hide-placeholder .mat-date-range-input-inner::-moz-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-moz-placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}@media(forced-colors: active){.mat-form-field-hide-placeholder .mat-date-range-input-inner::-moz-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-moz-placeholder{opacity:0}}.mat-form-field-hide-placeholder .mat-date-range-input-inner::-webkit-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-webkit-input-placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}@media(forced-colors: active){.mat-form-field-hide-placeholder .mat-date-range-input-inner::-webkit-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-webkit-input-placeholder{opacity:0}}.mat-form-field-hide-placeholder .mat-date-range-input-inner:-ms-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner:-ms-input-placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}@media(forced-colors: active){.mat-form-field-hide-placeholder .mat-date-range-input-inner:-ms-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner:-ms-input-placeholder{opacity:0}}._mat-animation-noopable .mat-date-range-input-inner::placeholder{transition:none}._mat-animation-noopable .mat-date-range-input-inner::-moz-placeholder{transition:none}._mat-animation-noopable .mat-date-range-input-inner::-webkit-input-placeholder{transition:none}._mat-animation-noopable .mat-date-range-input-inner:-ms-input-placeholder{transition:none}.mat-date-range-input-mirror{-webkit-user-select:none;user-select:none;visibility:hidden;white-space:nowrap;display:inline-block;min-width:2px}.mat-mdc-form-field-type-mat-date-range-input .mat-mdc-form-field-infix{width:200px}\\n\"]\n    }]\n  }], () => [], {\n    rangePicker: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    dateFilter: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    separator: [{\n      type: Input\n    }],\n    comparisonStart: [{\n      type: Input\n    }],\n    comparisonEnd: [{\n      type: Input\n    }]\n  });\n})();\n\n// This file contains the `_computeAriaAccessibleName` function, which computes what the *expected*\n// ARIA accessible name would be for a given element. Implements a subset of ARIA specification\n// [Accessible Name and Description Computation 1.2](https://www.w3.org/TR/accname-1.2/).\n//\n// Specification accname-1.2 can be summarized by returning the result of the first method\n// available.\n//\n//  1. `aria-labelledby` attribute\n//     ```\n//       <!-- example using aria-labelledby-->\n//       <label id='label-id'>Start Date</label>\n//       <input aria-labelledby='label-id'/>\n//     ```\n//  2. `aria-label` attribute (e.g. `<input aria-label=\"Departure\"/>`)\n//  3. Label with `for`/`id`\n//     ```\n//       <!-- example using for/id -->\n//       <label for=\"current-node\">Label</label>\n//       <input id=\"current-node\"/>\n//     ```\n//  4. `placeholder` attribute (e.g. `<input placeholder=\"06/03/1990\"/>`)\n//  5. `title` attribute (e.g. `<input title=\"Check-In\"/>`)\n//  6. text content\n//     ```\n//       <!-- example using text content -->\n//       <label for=\"current-node\"><span>Departure</span> Date</label>\n//       <input id=\"current-node\"/>\n//     ```\n/**\n * Computes the *expected* ARIA accessible name for argument element based on [accname-1.2\n * specification](https://www.w3.org/TR/accname-1.2/). Implements a subset of accname-1.2,\n * and should only be used for the Datepicker's specific use case.\n *\n * Intended use:\n * This is not a general use implementation. Only implements the parts of accname-1.2 that are\n * required for the Datepicker's specific use case. This function is not intended for any other\n * use.\n *\n * Limitations:\n *  - Only covers the needs of `matStartDate` and `matEndDate`. Does not support other use cases.\n *  - See NOTES's in implementation for specific details on what parts of the accname-1.2\n *  specification are not implemented.\n *\n *  @param element {HTMLInputElement} native &lt;input/&gt; element of `matStartDate` or\n *  `matEndDate` component. Corresponds to the 'Root Element' from accname-1.2\n *\n *  @return expected ARIA accessible name of argument &lt;input/&gt;\n */\nfunction _computeAriaAccessibleName(element) {\n  return _computeAriaAccessibleNameInternal(element, true);\n}\n/**\n * Determine if argument node is an Element based on `nodeType` property. This function is safe to\n * use with server-side rendering.\n */\nfunction ssrSafeIsElement(node) {\n  return node.nodeType === Node.ELEMENT_NODE;\n}\n/**\n * Determine if argument node is an HTMLInputElement based on `nodeName` property. This funciton is\n * safe to use with server-side rendering.\n */\nfunction ssrSafeIsHTMLInputElement(node) {\n  return node.nodeName === 'INPUT';\n}\n/**\n * Determine if argument node is an HTMLTextAreaElement based on `nodeName` property. This\n * funciton is safe to use with server-side rendering.\n */\nfunction ssrSafeIsHTMLTextAreaElement(node) {\n  return node.nodeName === 'TEXTAREA';\n}\n/**\n * Calculate the expected ARIA accessible name for given DOM Node. Given DOM Node may be either the\n * \"Root node\" passed to `_computeAriaAccessibleName` or \"Current node\" as result of recursion.\n *\n * @return the accessible name of argument DOM Node\n *\n * @param currentNode node to determine accessible name of\n * @param isDirectlyReferenced true if `currentNode` is the root node to calculate ARIA accessible\n * name of. False if it is a result of recursion.\n */\nfunction _computeAriaAccessibleNameInternal(currentNode, isDirectlyReferenced) {\n  // NOTE: this differs from accname-1.2 specification.\n  //  - Does not implement Step 1. of accname-1.2: '''If `currentNode`'s role prohibits naming,\n  //    return the empty string (\"\")'''.\n  //  - Does not implement Step 2.A. of accname-1.2: '''if current node is hidden and not directly\n  //    referenced by aria-labelledby... return the empty string.'''\n  // acc-name-1.2 Step 2.B.: aria-labelledby\n  if (ssrSafeIsElement(currentNode) && isDirectlyReferenced) {\n    const labelledbyIds = currentNode.getAttribute?.('aria-labelledby')?.split(/\\s+/g) || [];\n    const validIdRefs = labelledbyIds.reduce((validIds, id) => {\n      const elem = document.getElementById(id);\n      if (elem) {\n        validIds.push(elem);\n      }\n      return validIds;\n    }, []);\n    if (validIdRefs.length) {\n      return validIdRefs.map(idRef => {\n        return _computeAriaAccessibleNameInternal(idRef, false);\n      }).join(' ');\n    }\n  }\n  // acc-name-1.2 Step 2.C.: aria-label\n  if (ssrSafeIsElement(currentNode)) {\n    const ariaLabel = currentNode.getAttribute('aria-label')?.trim();\n    if (ariaLabel) {\n      return ariaLabel;\n    }\n  }\n  // acc-name-1.2 Step 2.D. attribute or element that defines a text alternative\n  //\n  // NOTE: this differs from accname-1.2 specification.\n  // Only implements Step 2.D. for `<label>`,`<input/>`, and `<textarea/>` element. Does not\n  // implement other elements that have an attribute or element that defines a text alternative.\n  if (ssrSafeIsHTMLInputElement(currentNode) || ssrSafeIsHTMLTextAreaElement(currentNode)) {\n    // use label with a `for` attribute referencing the current node\n    if (currentNode.labels?.length) {\n      return Array.from(currentNode.labels).map(x => _computeAriaAccessibleNameInternal(x, false)).join(' ');\n    }\n    // use placeholder if available\n    const placeholder = currentNode.getAttribute('placeholder')?.trim();\n    if (placeholder) {\n      return placeholder;\n    }\n    // use title if available\n    const title = currentNode.getAttribute('title')?.trim();\n    if (title) {\n      return title;\n    }\n  }\n  // NOTE: this differs from accname-1.2 specification.\n  //  - does not implement acc-name-1.2 Step 2.E.: '''if the current node is a control embedded\n  //     within the label... then include the embedded control as part of the text alternative in\n  //     the following manner...'''. Step 2E applies to embedded controls such as textbox, listbox,\n  //     range, etc.\n  //  - does not implement acc-name-1.2 step 2.F.: check that '''role allows name from content''',\n  //    which applies to `currentNode` and its children.\n  //  - does not implement acc-name-1.2 Step 2.F.ii.: '''Check for CSS generated textual content'''\n  //    (e.g. :before and :after).\n  //  - does not implement acc-name-1.2 Step 2.I.: '''if the current node has a Tooltip attribute,\n  //    return its value'''\n  // Return text content with whitespace collapsed into a single space character. Accomplish\n  // acc-name-1.2 steps 2F, 2G, and 2H.\n  return (currentNode.textContent || '').replace(/\\s+/g, ' ').trim();\n}\n\n/**\n * Base class for the individual inputs that can be projected inside a `mat-date-range-input`.\n */\nclass MatDateRangeInputPartBase extends MatDatepickerInputBase {\n  _rangeInput = inject(MatDateRangeInput);\n  _elementRef = inject(ElementRef);\n  _defaultErrorStateMatcher = inject(ErrorStateMatcher);\n  _injector = inject(Injector);\n  _parentForm = inject(NgForm, {\n    optional: true\n  });\n  _parentFormGroup = inject(FormGroupDirective, {\n    optional: true\n  });\n  /**\n   * Form control bound to this input part.\n   * @docs-private\n   */\n  ngControl;\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _errorStateTracker;\n  /** Object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /** Whether the input is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  constructor() {\n    super();\n    this._errorStateTracker = new _ErrorStateTracker(this._defaultErrorStateMatcher, null, this._parentFormGroup, this._parentForm, this.stateChanges);\n  }\n  ngOnInit() {\n    // We need the date input to provide itself as a `ControlValueAccessor` and a `Validator`, while\n    // injecting its `NgControl` so that the error state is handled correctly. This introduces a\n    // circular dependency, because both `ControlValueAccessor` and `Validator` depend on the input\n    // itself. Usually we can work around it for the CVA, but there's no API to do it for the\n    // validator. We work around it here by injecting the `NgControl` in `ngOnInit`, after\n    // everything has been resolved.\n    const ngControl = this._injector.get(NgControl, null, {\n      optional: true,\n      self: true\n    });\n    if (ngControl) {\n      this.ngControl = ngControl;\n      this._errorStateTracker.ngControl = ngControl;\n    }\n  }\n  ngAfterContentInit() {\n    this._register();\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n    }\n  }\n  /** Gets whether the input is empty. */\n  isEmpty() {\n    return this._elementRef.nativeElement.value.length === 0;\n  }\n  /** Gets the placeholder of the input. */\n  _getPlaceholder() {\n    return this._elementRef.nativeElement.placeholder;\n  }\n  /** Focuses the input. */\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n  /** Gets the value that should be used when mirroring the input's size. */\n  getMirrorValue() {\n    const element = this._elementRef.nativeElement;\n    const value = element.value;\n    return value.length > 0 ? value : element.placeholder;\n  }\n  /** Refreshes the error state of the input. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Handles `input` events on the input element. */\n  _onInput(value) {\n    super._onInput(value);\n    this._rangeInput._handleChildValueChange();\n  }\n  /** Opens the datepicker associated with the input. */\n  _openPopup() {\n    this._rangeInput._openDatepicker();\n  }\n  /** Gets the minimum date from the range input. */\n  _getMinDate() {\n    return this._rangeInput.min;\n  }\n  /** Gets the maximum date from the range input. */\n  _getMaxDate() {\n    return this._rangeInput.max;\n  }\n  /** Gets the date filter function from the range input. */\n  _getDateFilter() {\n    return this._rangeInput.dateFilter;\n  }\n  _parentDisabled() {\n    return this._rangeInput._groupDisabled;\n  }\n  _shouldHandleChangeEvent({\n    source\n  }) {\n    return source !== this._rangeInput._startInput && source !== this._rangeInput._endInput;\n  }\n  _assignValueProgrammatically(value) {\n    super._assignValueProgrammatically(value);\n    const opposite = this === this._rangeInput._startInput ? this._rangeInput._endInput : this._rangeInput._startInput;\n    opposite?._validatorOnChange();\n  }\n  _formatValue(value) {\n    super._formatValue(value);\n    // Any time the input value is reformatted we need to tell the parent.\n    this._rangeInput._handleChildValueChange();\n  }\n  /** return the ARIA accessible name of the input element */\n  _getAccessibleName() {\n    return _computeAriaAccessibleName(this._elementRef.nativeElement);\n  }\n  static ɵfac = function MatDateRangeInputPartBase_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDateRangeInputPartBase)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDateRangeInputPartBase,\n    inputs: {\n      errorStateMatcher: \"errorStateMatcher\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDateRangeInputPartBase, [{\n    type: Directive\n  }], () => [], {\n    errorStateMatcher: [{\n      type: Input\n    }]\n  });\n})();\n/** Input for entering the start date in a `mat-date-range-input`. */\nclass MatStartDate extends MatDateRangeInputPartBase {\n  /** Validator that checks that the start date isn't after the end date. */\n  _startValidator = control => {\n    const start = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(control.value));\n    const end = this._model ? this._model.selection.end : null;\n    return !start || !end || this._dateAdapter.compareDate(start, end) <= 0 ? null : {\n      'matStartDateInvalid': {\n        'end': end,\n        'actual': start\n      }\n    };\n  };\n  _validator = Validators.compose([...super._getValidators(), this._startValidator]);\n  _register() {\n    this._rangeInput._startInput = this;\n  }\n  _getValueFromModel(modelValue) {\n    return modelValue.start;\n  }\n  _shouldHandleChangeEvent(change) {\n    if (!super._shouldHandleChangeEvent(change)) {\n      return false;\n    } else {\n      return !change.oldValue?.start ? !!change.selection.start : !change.selection.start || !!this._dateAdapter.compareDate(change.oldValue.start, change.selection.start);\n    }\n  }\n  _assignValueToModel(value) {\n    if (this._model) {\n      const range = new DateRange(value, this._model.selection.end);\n      this._model.updateSelection(range, this);\n      this._rangeInput._handleChildValueChange();\n    }\n  }\n  _onKeydown(event) {\n    const endInput = this._rangeInput._endInput;\n    const element = this._elementRef.nativeElement;\n    const isLtr = this._dir?.value !== 'rtl';\n    // If the user hits RIGHT (LTR) when at the end of the input (and no\n    // selection), move the cursor to the start of the end input.\n    if ((event.keyCode === RIGHT_ARROW && isLtr || event.keyCode === LEFT_ARROW && !isLtr) && element.selectionStart === element.value.length && element.selectionEnd === element.value.length) {\n      event.preventDefault();\n      endInput._elementRef.nativeElement.setSelectionRange(0, 0);\n      endInput.focus();\n    } else {\n      super._onKeydown(event);\n    }\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatStartDate_BaseFactory;\n    return function MatStartDate_Factory(__ngFactoryType__) {\n      return (ɵMatStartDate_BaseFactory || (ɵMatStartDate_BaseFactory = i0.ɵɵgetInheritedFactory(MatStartDate)))(__ngFactoryType__ || MatStartDate);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatStartDate,\n    selectors: [[\"input\", \"matStartDate\", \"\"]],\n    hostAttrs: [\"type\", \"text\", 1, \"mat-start-date\", \"mat-date-range-input-inner\"],\n    hostVars: 5,\n    hostBindings: function MatStartDate_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function MatStartDate_input_HostBindingHandler($event) {\n          return ctx._onInput($event.target.value);\n        })(\"change\", function MatStartDate_change_HostBindingHandler() {\n          return ctx._onChange();\n        })(\"keydown\", function MatStartDate_keydown_HostBindingHandler($event) {\n          return ctx._onKeydown($event);\n        })(\"blur\", function MatStartDate_blur_HostBindingHandler() {\n          return ctx._onBlur();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"disabled\", ctx.disabled);\n        i0.ɵɵattribute(\"aria-haspopup\", ctx._rangeInput.rangePicker ? \"dialog\" : null)(\"aria-owns\", ctx._rangeInput._ariaOwns ? ctx._rangeInput._ariaOwns() : (ctx._rangeInput.rangePicker == null ? null : ctx._rangeInput.rangePicker.opened) && ctx._rangeInput.rangePicker.id || null)(\"min\", ctx._getMinDate() ? ctx._dateAdapter.toIso8601(ctx._getMinDate()) : null)(\"max\", ctx._getMaxDate() ? ctx._dateAdapter.toIso8601(ctx._getMaxDate()) : null);\n      }\n    },\n    outputs: {\n      dateChange: \"dateChange\",\n      dateInput: \"dateInput\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: MatStartDate,\n      multi: true\n    }, {\n      provide: NG_VALIDATORS,\n      useExisting: MatStartDate,\n      multi: true\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStartDate, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matStartDate]',\n      host: {\n        'class': 'mat-start-date mat-date-range-input-inner',\n        '[disabled]': 'disabled',\n        '(input)': '_onInput($event.target.value)',\n        '(change)': '_onChange()',\n        '(keydown)': '_onKeydown($event)',\n        '[attr.aria-haspopup]': '_rangeInput.rangePicker ? \"dialog\" : null',\n        '[attr.aria-owns]': `_rangeInput._ariaOwns\n        ? _rangeInput._ariaOwns()\n        : (_rangeInput.rangePicker?.opened && _rangeInput.rangePicker.id) || null`,\n        '[attr.min]': '_getMinDate() ? _dateAdapter.toIso8601(_getMinDate()) : null',\n        '[attr.max]': '_getMaxDate() ? _dateAdapter.toIso8601(_getMaxDate()) : null',\n        '(blur)': '_onBlur()',\n        'type': 'text'\n      },\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: MatStartDate,\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: MatStartDate,\n        multi: true\n      }],\n      // These need to be specified explicitly, because some tooling doesn't\n      // seem to pick them up from the base class. See #20932.\n      outputs: ['dateChange', 'dateInput']\n    }]\n  }], null, null);\n})();\n/** Input for entering the end date in a `mat-date-range-input`. */\nclass MatEndDate extends MatDateRangeInputPartBase {\n  /** Validator that checks that the end date isn't before the start date. */\n  _endValidator = control => {\n    const end = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(control.value));\n    const start = this._model ? this._model.selection.start : null;\n    return !end || !start || this._dateAdapter.compareDate(end, start) >= 0 ? null : {\n      'matEndDateInvalid': {\n        'start': start,\n        'actual': end\n      }\n    };\n  };\n  _register() {\n    this._rangeInput._endInput = this;\n  }\n  _validator = Validators.compose([...super._getValidators(), this._endValidator]);\n  _getValueFromModel(modelValue) {\n    return modelValue.end;\n  }\n  _shouldHandleChangeEvent(change) {\n    if (!super._shouldHandleChangeEvent(change)) {\n      return false;\n    } else {\n      return !change.oldValue?.end ? !!change.selection.end : !change.selection.end || !!this._dateAdapter.compareDate(change.oldValue.end, change.selection.end);\n    }\n  }\n  _assignValueToModel(value) {\n    if (this._model) {\n      const range = new DateRange(this._model.selection.start, value);\n      this._model.updateSelection(range, this);\n    }\n  }\n  _moveCaretToEndOfStartInput() {\n    const startInput = this._rangeInput._startInput._elementRef.nativeElement;\n    const value = startInput.value;\n    if (value.length > 0) {\n      startInput.setSelectionRange(value.length, value.length);\n    }\n    startInput.focus();\n  }\n  _onKeydown(event) {\n    const element = this._elementRef.nativeElement;\n    const isLtr = this._dir?.value !== 'rtl';\n    // If the user is pressing backspace on an empty end input, move focus back to the start.\n    if (event.keyCode === BACKSPACE && !element.value) {\n      this._moveCaretToEndOfStartInput();\n    }\n    // If the user hits LEFT (LTR) when at the start of the input (and no\n    // selection), move the cursor to the end of the start input.\n    else if ((event.keyCode === LEFT_ARROW && isLtr || event.keyCode === RIGHT_ARROW && !isLtr) && element.selectionStart === 0 && element.selectionEnd === 0) {\n      event.preventDefault();\n      this._moveCaretToEndOfStartInput();\n    } else {\n      super._onKeydown(event);\n    }\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatEndDate_BaseFactory;\n    return function MatEndDate_Factory(__ngFactoryType__) {\n      return (ɵMatEndDate_BaseFactory || (ɵMatEndDate_BaseFactory = i0.ɵɵgetInheritedFactory(MatEndDate)))(__ngFactoryType__ || MatEndDate);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatEndDate,\n    selectors: [[\"input\", \"matEndDate\", \"\"]],\n    hostAttrs: [\"type\", \"text\", 1, \"mat-end-date\", \"mat-date-range-input-inner\"],\n    hostVars: 5,\n    hostBindings: function MatEndDate_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function MatEndDate_input_HostBindingHandler($event) {\n          return ctx._onInput($event.target.value);\n        })(\"change\", function MatEndDate_change_HostBindingHandler() {\n          return ctx._onChange();\n        })(\"keydown\", function MatEndDate_keydown_HostBindingHandler($event) {\n          return ctx._onKeydown($event);\n        })(\"blur\", function MatEndDate_blur_HostBindingHandler() {\n          return ctx._onBlur();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"disabled\", ctx.disabled);\n        i0.ɵɵattribute(\"aria-haspopup\", ctx._rangeInput.rangePicker ? \"dialog\" : null)(\"aria-owns\", ctx._rangeInput._ariaOwns ? ctx._rangeInput._ariaOwns() : (ctx._rangeInput.rangePicker == null ? null : ctx._rangeInput.rangePicker.opened) && ctx._rangeInput.rangePicker.id || null)(\"min\", ctx._getMinDate() ? ctx._dateAdapter.toIso8601(ctx._getMinDate()) : null)(\"max\", ctx._getMaxDate() ? ctx._dateAdapter.toIso8601(ctx._getMaxDate()) : null);\n      }\n    },\n    outputs: {\n      dateChange: \"dateChange\",\n      dateInput: \"dateInput\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: MatEndDate,\n      multi: true\n    }, {\n      provide: NG_VALIDATORS,\n      useExisting: MatEndDate,\n      multi: true\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatEndDate, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matEndDate]',\n      host: {\n        'class': 'mat-end-date mat-date-range-input-inner',\n        '[disabled]': 'disabled',\n        '(input)': '_onInput($event.target.value)',\n        '(change)': '_onChange()',\n        '(keydown)': '_onKeydown($event)',\n        '[attr.aria-haspopup]': '_rangeInput.rangePicker ? \"dialog\" : null',\n        '[attr.aria-owns]': `_rangeInput._ariaOwns\n        ? _rangeInput._ariaOwns()\n        : (_rangeInput.rangePicker?.opened && _rangeInput.rangePicker.id) || null`,\n        '[attr.min]': '_getMinDate() ? _dateAdapter.toIso8601(_getMinDate()) : null',\n        '[attr.max]': '_getMaxDate() ? _dateAdapter.toIso8601(_getMaxDate()) : null',\n        '(blur)': '_onBlur()',\n        'type': 'text'\n      },\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: MatEndDate,\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: MatEndDate,\n        multi: true\n      }],\n      // These need to be specified explicitly, because some tooling doesn't\n      // seem to pick them up from the base class. See #20932.\n      outputs: ['dateChange', 'dateInput']\n    }]\n  }], null, null);\n})();\n\n// TODO(mmalerba): We use a component instead of a directive here so the user can use implicit\n// template reference variables (e.g. #d vs #d=\"matDateRangePicker\"). We can change this to a\n// directive if angular adds support for `exportAs: '$implicit'` on directives.\n/** Component responsible for managing the date range picker popup/dialog. */\nclass MatDateRangePicker extends MatDatepickerBase {\n  _forwardContentValues(instance) {\n    super._forwardContentValues(instance);\n    const input = this.datepickerInput;\n    if (input) {\n      instance.comparisonStart = input.comparisonStart;\n      instance.comparisonEnd = input.comparisonEnd;\n      instance.startDateAccessibleName = input._getStartDateAccessibleName();\n      instance.endDateAccessibleName = input._getEndDateAccessibleName();\n    }\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDateRangePicker_BaseFactory;\n    return function MatDateRangePicker_Factory(__ngFactoryType__) {\n      return (ɵMatDateRangePicker_BaseFactory || (ɵMatDateRangePicker_BaseFactory = i0.ɵɵgetInheritedFactory(MatDateRangePicker)))(__ngFactoryType__ || MatDateRangePicker);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDateRangePicker,\n    selectors: [[\"mat-date-range-picker\"]],\n    exportAs: [\"matDateRangePicker\"],\n    features: [i0.ɵɵProvidersFeature([MAT_RANGE_DATE_SELECTION_MODEL_PROVIDER, MAT_CALENDAR_RANGE_STRATEGY_PROVIDER, {\n      provide: MatDatepickerBase,\n      useExisting: MatDateRangePicker\n    }]), i0.ɵɵInheritDefinitionFeature],\n    decls: 0,\n    vars: 0,\n    template: function MatDateRangePicker_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDateRangePicker, [{\n    type: Component,\n    args: [{\n      selector: 'mat-date-range-picker',\n      template: '',\n      exportAs: 'matDateRangePicker',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [MAT_RANGE_DATE_SELECTION_MODEL_PROVIDER, MAT_CALENDAR_RANGE_STRATEGY_PROVIDER, {\n        provide: MatDatepickerBase,\n        useExisting: MatDateRangePicker\n      }]\n    }]\n  }], null, null);\n})();\n\n/** Button that will close the datepicker and assign the current selection to the data model. */\nclass MatDatepickerApply {\n  _datepicker = inject(MatDatepickerBase);\n  constructor() {}\n  _applySelection() {\n    this._datepicker._applyPendingSelection();\n    this._datepicker.close();\n  }\n  static ɵfac = function MatDatepickerApply_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerApply)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDatepickerApply,\n    selectors: [[\"\", \"matDatepickerApply\", \"\"], [\"\", \"matDateRangePickerApply\", \"\"]],\n    hostBindings: function MatDatepickerApply_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatDatepickerApply_click_HostBindingHandler() {\n          return ctx._applySelection();\n        });\n      }\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerApply, [{\n    type: Directive,\n    args: [{\n      selector: '[matDatepickerApply], [matDateRangePickerApply]',\n      host: {\n        '(click)': '_applySelection()'\n      }\n    }]\n  }], () => [], null);\n})();\n/** Button that will close the datepicker and discard the current selection. */\nclass MatDatepickerCancel {\n  _datepicker = inject(MatDatepickerBase);\n  constructor() {}\n  static ɵfac = function MatDatepickerCancel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerCancel)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDatepickerCancel,\n    selectors: [[\"\", \"matDatepickerCancel\", \"\"], [\"\", \"matDateRangePickerCancel\", \"\"]],\n    hostBindings: function MatDatepickerCancel_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatDatepickerCancel_click_HostBindingHandler() {\n          return ctx._datepicker.close();\n        });\n      }\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerCancel, [{\n    type: Directive,\n    args: [{\n      selector: '[matDatepickerCancel], [matDateRangePickerCancel]',\n      host: {\n        '(click)': '_datepicker.close()'\n      }\n    }]\n  }], () => [], null);\n})();\n/**\n * Container that can be used to project a row of action buttons\n * to the bottom of a datepicker or date range picker.\n */\nclass MatDatepickerActions {\n  _datepicker = inject(MatDatepickerBase);\n  _viewContainerRef = inject(ViewContainerRef);\n  _template;\n  _portal;\n  constructor() {}\n  ngAfterViewInit() {\n    this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    this._datepicker.registerActions(this._portal);\n  }\n  ngOnDestroy() {\n    this._datepicker.removeActions(this._portal);\n    // Needs to be null checked since we initialize it in `ngAfterViewInit`.\n    if (this._portal && this._portal.isAttached) {\n      this._portal?.detach();\n    }\n  }\n  static ɵfac = function MatDatepickerActions_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerActions)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDatepickerActions,\n    selectors: [[\"mat-datepicker-actions\"], [\"mat-date-range-picker-actions\"]],\n    viewQuery: function MatDatepickerActions_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(TemplateRef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._template = _t.first);\n      }\n    },\n    ngContentSelectors: _c1,\n    decls: 1,\n    vars: 0,\n    consts: [[1, \"mat-datepicker-actions\"]],\n    template: function MatDatepickerActions_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, MatDatepickerActions_ng_template_0_Template, 2, 0, \"ng-template\");\n      }\n    },\n    styles: [\".mat-datepicker-actions{display:flex;justify-content:flex-end;align-items:center;padding:0 8px 8px 8px}.mat-datepicker-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-datepicker-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerActions, [{\n    type: Component,\n    args: [{\n      selector: 'mat-datepicker-actions, mat-date-range-picker-actions',\n      template: `\n    <ng-template>\n      <div class=\"mat-datepicker-actions\">\n        <ng-content></ng-content>\n      </div>\n    </ng-template>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      styles: [\".mat-datepicker-actions{display:flex;justify-content:flex-end;align-items:center;padding:0 8px 8px 8px}.mat-datepicker-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-datepicker-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"]\n    }]\n  }], () => [], {\n    _template: [{\n      type: ViewChild,\n      args: [TemplateRef]\n    }]\n  });\n})();\nclass MatDatepickerModule {\n  static ɵfac = function MatDatepickerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDatepickerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatDatepickerModule,\n    imports: [MatButtonModule, OverlayModule, A11yModule, PortalModule, MatCommonModule, MatCalendar, MatCalendarBody, MatDatepicker, MatDatepickerContent, MatDatepickerInput, MatDatepickerToggle, MatDatepickerToggleIcon, MatMonthView, MatYearView, MatMultiYearView, MatCalendarHeader, MatDateRangeInput, MatStartDate, MatEndDate, MatDateRangePicker, MatDatepickerActions, MatDatepickerCancel, MatDatepickerApply],\n    exports: [CdkScrollableModule, MatCalendar, MatCalendarBody, MatDatepicker, MatDatepickerContent, MatDatepickerInput, MatDatepickerToggle, MatDatepickerToggleIcon, MatMonthView, MatYearView, MatMultiYearView, MatCalendarHeader, MatDateRangeInput, MatStartDate, MatEndDate, MatDateRangePicker, MatDatepickerActions, MatDatepickerCancel, MatDatepickerApply]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MatDatepickerIntl, MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER],\n    imports: [MatButtonModule, OverlayModule, A11yModule, PortalModule, MatCommonModule, MatDatepickerContent, MatDatepickerToggle, MatCalendarHeader, CdkScrollableModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDatepickerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatButtonModule, OverlayModule, A11yModule, PortalModule, MatCommonModule, MatCalendar, MatCalendarBody, MatDatepicker, MatDatepickerContent, MatDatepickerInput, MatDatepickerToggle, MatDatepickerToggleIcon, MatMonthView, MatYearView, MatMultiYearView, MatCalendarHeader, MatDateRangeInput, MatStartDate, MatEndDate, MatDateRangePicker, MatDatepickerActions, MatDatepickerCancel, MatDatepickerApply],\n      exports: [CdkScrollableModule, MatCalendar, MatCalendarBody, MatDatepicker, MatDatepickerContent, MatDatepickerInput, MatDatepickerToggle, MatDatepickerToggleIcon, MatMonthView, MatYearView, MatMultiYearView, MatCalendarHeader, MatDateRangeInput, MatStartDate, MatEndDate, MatDateRangePicker, MatDatepickerActions, MatDatepickerCancel, MatDatepickerApply],\n      providers: [MatDatepickerIntl, MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material datepicker.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matDatepickerAnimations = {\n  // Represents:\n  // trigger('transformPanel', [\n  //   transition(\n  //     'void => enter-dropdown',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       keyframes([\n  //         style({opacity: 0, transform: 'scale(1, 0.8)'}),\n  //         style({opacity: 1, transform: 'scale(1, 1)'}),\n  //       ]),\n  //     ),\n  //   ),\n  //   transition(\n  //     'void => enter-dialog',\n  //     animate(\n  //       '150ms cubic-bezier(0, 0, 0.2, 1)',\n  //       keyframes([\n  //         style({opacity: 0, transform: 'scale(0.7)'}),\n  //         style({transform: 'none', opacity: 1}),\n  //       ]),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),\n  // ])\n  /** Transforms the height of the datepicker's calendar. */\n  transformPanel: {\n    type: 7,\n    name: 'transformPanel',\n    definitions: [{\n      type: 1,\n      expr: 'void => enter-dropdown',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          steps: [{\n            type: 6,\n            styles: {\n              opacity: 0,\n              transform: 'scale(1, 0.8)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              opacity: 1,\n              transform: 'scale(1, 1)'\n            },\n            offset: null\n          }]\n        },\n        timings: '120ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: 'void => enter-dialog',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          steps: [{\n            type: 6,\n            styles: {\n              opacity: 0,\n              transform: 'scale(0.7)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'none',\n              opacity: 1\n            },\n            offset: null\n          }]\n        },\n        timings: '150ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '100ms linear'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('fadeInCalendar', [\n  //   state('void', style({opacity: 0})),\n  //   state('enter', style({opacity: 1})),\n  //   // TODO(crisbeto): this animation should be removed since it isn't quite on spec, but we\n  //   // need to keep it until #12440 gets in, otherwise the exit animation will look glitchy.\n  //   transition('void => *', animate('120ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)')),\n  // ])\n  /** Fades in the content of the calendar. */\n  fadeInCalendar: {\n    type: 7,\n    name: 'fadeInCalendar',\n    definitions: [{\n      type: 0,\n      name: 'void',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'enter',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => *',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '120ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { DateRange, DefaultMatCalendarRangeStrategy, MAT_DATEPICKER_SCROLL_STRATEGY, MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY, MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_DATEPICKER_VALIDATORS, MAT_DATEPICKER_VALUE_ACCESSOR, MAT_DATE_RANGE_SELECTION_STRATEGY, MAT_RANGE_DATE_SELECTION_MODEL_FACTORY, MAT_RANGE_DATE_SELECTION_MODEL_PROVIDER, MAT_SINGLE_DATE_SELECTION_MODEL_FACTORY, MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER, MatCalendar, MatCalendarBody, MatCalendarCell, MatCalendarHeader, MatDateRangeInput, MatDateRangePicker, MatDateSelectionModel, MatDatepicker, MatDatepickerActions, MatDatepickerApply, MatDatepickerCancel, MatDatepickerContent, MatDatepickerInput, MatDatepickerInputEvent, MatDatepickerIntl, MatDatepickerModule, MatDatepickerToggle, MatDatepickerToggleIcon, MatEndDate, MatMonthView, MatMultiYearView, MatRangeDateSelectionModel, MatSingleDateSelectionModel, MatStartDate, MatYearView, matDatepickerAnimations, yearsPerPage, yearsPerRow };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,IAAM,MAAM,CAAC,qBAAqB,EAAE;AACpC,SAAS,WAAW,QAAQ,OAAO;AACjC,SAAO,KAAK,UAAU,KAAK;AAC7B;AACA,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC;AACxC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,YAAY,eAAe,OAAO,YAAY,EAAE,kBAAkB,OAAO,YAAY;AACxF,IAAG,YAAY,WAAW,OAAO,OAAO;AACxC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,GAAG;AAAA,EAC9C;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,eAAe,OAAO,YAAY,EAAE,kBAAkB,OAAO,YAAY;AACxF,IAAG,YAAY,WAAW,OAAO,eAAe;AAChD,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,mBAAmB,OAAO,wBAAwB,OAAO,QAAQ,IAAI,GAAG;AAAA,EAC5G;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC,EAAE,GAAG,UAAU,CAAC;AAC5C,IAAG,WAAW,SAAS,SAAS,6DAA6D,QAAQ;AACnG,YAAM,UAAa,cAAc,GAAG,EAAE;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,SAAS,MAAM,CAAC;AAAA,IAC5D,CAAC,EAAE,SAAS,SAAS,6DAA6D,QAAQ;AACxF,YAAM,UAAa,cAAc,GAAG,EAAE;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,sBAAsB,SAAS,MAAM,CAAC;AAAA,IACrE,CAAC;AACD,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,gBAAgB,IAAI;AAC1B,UAAM,eAAkB,cAAc,EAAE;AACxC,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,SAAS,OAAO,UAAU,EAAE,eAAe,OAAO,YAAY,EAAE,kBAAkB,OAAO,YAAY;AACpH,IAAG,YAAY,gBAAgB,YAAY,EAAE,gBAAgB,aAAa;AAC1E,IAAG,UAAU;AACb,IAAG,YAAY,8BAA8B,CAAC,QAAQ,OAAO,EAAE,4BAA4B,OAAO,cAAc,cAAc,aAAa,CAAC,EAAE,iCAAiC,OAAO,cAAc,QAAQ,YAAY,CAAC,EAAE,+BAA+B,OAAO,YAAY,QAAQ,YAAY,CAAC,EAAE,8BAA8B,OAAO,WAAW,QAAQ,YAAY,CAAC,EAAE,6CAA6C,OAAO,yBAAyB,QAAQ,cAAc,cAAc,aAAa,CAAC,EAAE,2CAA2C,OAAO,uBAAuB,QAAQ,cAAc,cAAc,aAAa,CAAC,EAAE,sCAAsC,OAAO,mBAAmB,QAAQ,YAAY,CAAC,EAAE,oCAAoC,OAAO,iBAAiB,QAAQ,YAAY,CAAC,EAAE,yCAAyC,OAAO,qBAAqB,QAAQ,YAAY,CAAC,EAAE,mCAAmC,OAAO,gBAAgB,QAAQ,YAAY,CAAC,EAAE,iCAAiC,OAAO,cAAc,QAAQ,YAAY,CAAC,EAAE,gCAAgC,OAAO,aAAa,QAAQ,YAAY,CAAC;AACtlC,IAAG,WAAW,WAAW,QAAQ,UAAU,EAAE,YAAY,OAAO,cAAc,cAAc,aAAa,IAAI,IAAI,EAAE;AACnH,IAAG,YAAY,cAAc,QAAQ,SAAS,EAAE,iBAAiB,CAAC,QAAQ,WAAW,IAAI,EAAE,gBAAgB,OAAO,YAAY,QAAQ,YAAY,CAAC,EAAE,gBAAgB,OAAO,eAAe,QAAQ,eAAe,SAAS,IAAI,EAAE,oBAAoB,OAAO,gBAAgB,QAAQ,YAAY,CAAC;AACjS,IAAG,UAAU;AACb,IAAG,YAAY,8BAA8B,OAAO,YAAY,QAAQ,YAAY,CAAC,EAAE,0CAA0C,OAAO,uBAAuB,QAAQ,YAAY,CAAC,EAAE,2BAA2B,OAAO,eAAe,QAAQ,YAAY;AAC3P,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,QAAQ,cAAc,GAAG;AAAA,EACtD;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,MAAM,CAAC;AAC5E,IAAG,iBAAiB,GAAG,sCAAsC,GAAG,IAAI,MAAM,GAAG,UAAU;AACvF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,iBAAiB,KAAK,OAAO,kBAAkB,IAAI,EAAE;AACtE,IAAG,UAAU;AACb,IAAG,WAAW,MAAM;AAAA,EACtB;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC1C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,IAAI;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,mCAAmC,IAAI,KAAK;AAAC;AACtD,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,IAAG,iBAAiB,oBAAoB,SAAS,uEAAuE,QAAQ;AAC9H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,MAAG,mBAAmB,OAAO,YAAY,MAAM,MAAM,OAAO,aAAa;AACzE,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,WAAW,kBAAkB,SAAS,qEAAqE,QAAQ;AACpH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,MAAM,CAAC;AAAA,IACpD,CAAC,EAAE,eAAe,SAAS,kEAAkE,QAAQ;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC,EAAE,aAAa,SAAS,gEAAgE,QAAQ;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,iBAAiB,cAAc,OAAO,UAAU;AACnD,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,OAAO,EAAE,WAAW,OAAO,OAAO,EAAE,aAAa,OAAO,SAAS,EAAE,mBAAmB,OAAO,eAAe,EAAE,iBAAiB,OAAO,aAAa,EAAE,2BAA2B,OAAO,uBAAuB,EAAE,yBAAyB,OAAO,qBAAqB,EAAE,cAAc,OAAO,WAAW;AAAA,EACvY;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,iBAAiB,CAAC;AACvC,IAAG,iBAAiB,oBAAoB,SAAS,sEAAsE,QAAQ;AAC7H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,MAAG,mBAAmB,OAAO,YAAY,MAAM,MAAM,OAAO,aAAa;AACzE,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,WAAW,iBAAiB,SAAS,mEAAmE,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,yBAAyB,MAAM,CAAC;AAAA,IAC/D,CAAC,EAAE,kBAAkB,SAAS,oEAAoE,QAAQ;AACxG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,QAAQ,OAAO,CAAC;AAAA,IAC/D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,iBAAiB,cAAc,OAAO,UAAU;AACnD,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,OAAO,EAAE,WAAW,OAAO,OAAO,EAAE,aAAa,OAAO,SAAS;AAAA,EACjK;AACF;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,uBAAuB,CAAC;AAC7C,IAAG,iBAAiB,oBAAoB,SAAS,4EAA4E,QAAQ;AACnI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,MAAG,mBAAmB,OAAO,YAAY,MAAM,MAAM,OAAO,aAAa;AACzE,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,WAAW,gBAAgB,SAAS,wEAAwE,QAAQ;AACrH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,6BAA6B,MAAM,CAAC;AAAA,IACnE,CAAC,EAAE,kBAAkB,SAAS,0EAA0E,QAAQ;AAC9G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,QAAQ,MAAM,CAAC;AAAA,IAC9D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,iBAAiB,cAAc,OAAO,UAAU;AACnD,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,OAAO,EAAE,WAAW,OAAO,OAAO,EAAE,aAAa,OAAO,SAAS;AAAA,EACjK;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAAC;AAC/D,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;AAClD,IAAM,MAAM,CAAC,2BAA2B;AACxC,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,MAAM,CAAC,CAAC,CAAC,SAAS,gBAAgB,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,cAAc,EAAE,CAAC,CAAC;AAC3E,IAAM,MAAM,CAAC,uBAAuB,mBAAmB;AACvD,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,2BAA2B,UAAU;AAC5C,SAAO,MAAM,wCAAwC,QAAQ,iMAA2M;AAC1Q;AAGA,IAAM,oBAAN,MAAM,mBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,UAAU,IAAI,QAAQ;AAAA;AAAA,EAEtB,gBAAgB;AAAA;AAAA,EAEhB,oBAAoB;AAAA;AAAA,EAEpB,qBAAqB;AAAA;AAAA,EAErB,iBAAiB;AAAA;AAAA,EAEjB,iBAAiB;AAAA;AAAA,EAEjB,gBAAgB;AAAA;AAAA,EAEhB,gBAAgB;AAAA;AAAA,EAEhB,qBAAqB;AAAA;AAAA,EAErB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,6BAA6B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,eAAe;AAAA;AAAA;AAAA;AAAA,EAIf,sBAAsB;AAAA;AAAA,EAEtB,gBAAgB,OAAO,KAAK;AAC1B,WAAO,GAAG,KAAK,MAAW,GAAG;AAAA,EAC/B;AAAA;AAAA,EAEA,qBAAqB,OAAO,KAAK;AAC/B,WAAO,GAAG,KAAK,OAAO,GAAG;AAAA,EAC3B;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,mBAAkB;AAAA,IAC3B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAI,oBAAoB;AAKxB,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,KAAK;AAAA,EACL,YAAY,OAAO,cAAc,WAAW,SAAS,aAAa,CAAC,GAAG,eAAe,OAAO,UAAU;AACpG,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,WAAW;AAAA,EAClB;AACF;AAEA,IAAM,8BAA8B;AAAA,EAClC,SAAS;AAAA,EACT,SAAS;AACX;AAEA,IAAM,+BAA+B;AAAA,EACnC,SAAS;AAAA,EACT,SAAS;AACX;AAEA,IAAM,sBAAsB;AAAA,EAC1B,SAAS;AACX;AAKA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,QAAQ,OAAO,iBAAiB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA,EAIA,mCAAmC;AAAA;AAAA,EAEnC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,UAAU;AAAA;AAAA,EAEV,aAAa;AAAA,EACb,qBAAqB;AACnB,QAAI,KAAK,kCAAkC;AACzC,WAAK,iBAAiB;AACtB,WAAK,mCAAmC;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,kBAAkB;AAAA;AAAA,EAElB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,eAAe;AAAA;AAAA,EAEf,aAAa;AAAA;AAAA,EAEb;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,sBAAsB,IAAI,aAAa;AAAA;AAAA,EAEvC,gBAAgB,IAAI,aAAa;AAAA,EACjC,mBAAmB,IAAI,aAAa;AAAA;AAAA,EAEpC,cAAc,IAAI,aAAa;AAAA;AAAA,EAE/B,YAAY,IAAI,aAAa;AAAA;AAAA,EAE7B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,yBAAyB;AAAA,EACzB,YAAY,OAAO,QAAQ;AAAA,EAC3B,+BAA+B,KAAK,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1C,YAAY,SAAO;AAAA,EACnB,cAAc;AACZ,UAAM,WAAW,OAAO,SAAS;AACjC,UAAM,cAAc,OAAO,YAAY;AACvC,SAAK,oBAAoB,YAAY,MAAM,0BAA0B;AACrE,SAAK,kBAAkB,YAAY,MAAM,wBAAwB;AACjE,SAAK,8BAA8B,YAAY,MAAM,qCAAqC;AAC1F,SAAK,4BAA4B,YAAY,MAAM,mCAAmC;AACtF,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,SAAK,QAAQ,kBAAkB,MAAM;AACnC,YAAM,UAAU,KAAK,YAAY;AACjC,YAAM,WAAW;AAAA;AAAA,QAEjB,sBAAsB,UAAU,SAAS,aAAa,KAAK,mBAAmB,2BAA2B;AAAA,QAAG,sBAAsB,UAAU,SAAS,cAAc,KAAK,eAAe,4BAA4B;AAAA,QAAG,sBAAsB,UAAU,SAAS,SAAS,KAAK,eAAe,4BAA4B;AAAA,QAAG,sBAAsB,UAAU,SAAS,cAAc,KAAK,eAAe,4BAA4B;AAAA,QAAG,sBAAsB,UAAU,SAAS,QAAQ,KAAK,eAAe,4BAA4B;AAAA,QAAG,sBAAsB,UAAU,SAAS,aAAa,KAAK,mBAAmB,mBAAmB;AAAA,QAAG,sBAAsB,UAAU,SAAS,cAAc,KAAK,mBAAmB,mBAAmB;AAAA,MAAC;AAChtB,UAAI,KAAK,UAAU,WAAW;AAC5B,iBAAS,KAAK,SAAS,OAAO,UAAU,WAAW,KAAK,eAAe,GAAG,SAAS,OAAO,UAAU,YAAY,KAAK,gBAAgB,CAAC;AAAA,MACxI;AACA,WAAK,iBAAiB;AAAA,IACxB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,aAAa,MAAM,OAAO;AAGxB,QAAI,KAAK,wBAAwB;AAC/B;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,oBAAoB,KAAK;AAAA,QAC5B,OAAO,KAAK;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,sBAAsB,MAAM,OAAO;AACjC,QAAI,KAAK,SAAS;AAChB,WAAK,iBAAiB,KAAK;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,OAAO;AACjB,WAAO,KAAK,eAAe,SAAS,KAAK,aAAa;AAAA,EACxD;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,gBAAgB,QAAQ,SAAS;AACvC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,MAAM,KAAK,eAAe;AACpC,WAAK,kBAAkB,QAAQ,KAAK,UAAU,KAAK,CAAC,EAAE,SAAS,UAAU,KAAK,CAAC,EAAE,SAAS;AAAA,IAC5F;AACA,QAAI,QAAQ,iBAAiB,KAAK,iBAAiB,CAAC,KAAK,cAAc;AACrE,WAAK,eAAe,GAAG,KAAK,KAAK,kBAAkB,OAAO;AAAA,IAC5D;AACA,QAAI,iBAAiB,CAAC,KAAK,YAAY;AACrC,WAAK,aAAa,GAAG,MAAM,OAAO;AAAA,IACpC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,QAAQ,aAAW,QAAQ,CAAC;AAAA,EAClD;AAAA;AAAA,EAEA,cAAc,UAAU,UAAU;AAChC,QAAI,aAAa,WAAW,KAAK,UAAU;AAE3C,QAAI,UAAU;AACZ,oBAAc,KAAK;AAAA,IACrB;AACA,WAAO,cAAc,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,iBAAiB,cAAc,MAAM;AACnC,oBAAgB,MAAM;AACpB,iBAAW,MAAM;AACf,cAAM,aAAa,KAAK,YAAY,cAAc,cAAc,2BAA2B;AAC3F,YAAI,YAAY;AACd,cAAI,CAAC,aAAa;AAChB,iBAAK,iBAAiB;AAAA,UACxB;AACA,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,2CAA2C;AACzC,SAAK,mCAAmC;AAAA,EAC1C;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,WAAO,QAAQ,OAAO,KAAK,YAAY,KAAK,QAAQ;AAAA,EACtD;AAAA;AAAA,EAEA,YAAY,OAAO;AACjB,WAAO,MAAM,OAAO,KAAK,YAAY,KAAK,QAAQ;AAAA,EACpD;AAAA;AAAA,EAEA,WAAW,OAAO;AAChB,WAAO,UAAU,OAAO,KAAK,YAAY,KAAK,UAAU,KAAK,OAAO;AAAA,EACtE;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,WAAO,QAAQ,OAAO,KAAK,iBAAiB,KAAK,aAAa;AAAA,EAChE;AAAA;AAAA,EAEA,yBAAyB,OAAO,UAAU,UAAU;AAClD,QAAI,CAAC,KAAK,mBAAmB,KAAK,KAAK,KAAK,cAAc,KAAK,KAAK,CAAC,KAAK,WAAW,KAAK,GAAG;AAC3F,aAAO;AAAA,IACT;AACA,QAAI,eAAe,KAAK,KAAK,QAAQ,EAAE,WAAW,CAAC;AACnD,QAAI,CAAC,cAAc;AACjB,YAAM,cAAc,KAAK,KAAK,WAAW,CAAC;AAC1C,qBAAe,eAAe,YAAY,YAAY,SAAS,CAAC;AAAA,IAClE;AACA,WAAO,gBAAgB,CAAC,KAAK,YAAY,aAAa,YAAY;AAAA,EACpE;AAAA;AAAA,EAEA,uBAAuB,OAAO,UAAU,UAAU;AAChD,QAAI,CAAC,KAAK,iBAAiB,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,CAAC,KAAK,WAAW,KAAK,GAAG;AACvF,aAAO;AAAA,IACT;AACA,QAAI,WAAW,KAAK,KAAK,QAAQ,EAAE,WAAW,CAAC;AAC/C,QAAI,CAAC,UAAU;AACb,YAAM,UAAU,KAAK,KAAK,WAAW,CAAC;AACtC,iBAAW,WAAW,QAAQ,CAAC;AAAA,IACjC;AACA,WAAO,YAAY,CAAC,KAAK,cAAc,SAAS,YAAY;AAAA,EAC9D;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,WAAO,MAAM,OAAO,KAAK,iBAAiB,KAAK,aAAa;AAAA,EAC9D;AAAA;AAAA,EAEA,qBAAqB,OAAO;AAC1B,WAAO,UAAU,OAAO,KAAK,iBAAiB,KAAK,eAAe,KAAK,OAAO;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,uBAAuB,OAAO;AAG5B,WAAO,KAAK,oBAAoB,KAAK,iBAAiB,UAAU,KAAK;AAAA,EACvE;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,WAAO,QAAQ,OAAO,KAAK,cAAc,KAAK,UAAU;AAAA,EAC1D;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,WAAO,MAAM,OAAO,KAAK,cAAc,KAAK,UAAU;AAAA,EACxD;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,WAAO,UAAU,OAAO,KAAK,cAAc,KAAK,YAAY,KAAK,OAAO;AAAA,EAC1E;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,eAAe,SAAS,KAAK,aAAa,OAAO;AACxD,aAAO,GAAG,KAAK,iBAAiB,IAAI,KAAK,eAAe;AAAA,IAC1D,WAAW,KAAK,eAAe,OAAO;AACpC,aAAO,KAAK;AAAA,IACd,WAAW,KAAK,aAAa,OAAO;AAClC,aAAO,KAAK;AAAA,IACd;AACA,QAAI,KAAK,oBAAoB,QAAQ,KAAK,kBAAkB,MAAM;AAChE,UAAI,UAAU,KAAK,mBAAmB,UAAU,KAAK,eAAe;AAClE,eAAO,GAAG,KAAK,2BAA2B,IAAI,KAAK,yBAAyB;AAAA,MAC9E,WAAW,UAAU,KAAK,iBAAiB;AACzC,eAAO,KAAK;AAAA,MACd,WAAW,UAAU,KAAK,eAAe;AACvC,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,WAAS;AACvB,QAAI,KAAK,kBAAkB,MAAM,SAAS,SAAS;AACjD,WAAK,iBAAiB;AACtB;AAAA,IACF;AAEA,QAAI,MAAM,UAAU,KAAK,SAAS;AAChC,YAAM,OAAO,KAAK,oBAAoB,MAAM,MAAM;AAClD,UAAI,MAAM;AACR,aAAK,QAAQ,IAAI,MAAM,KAAK,cAAc,KAAK;AAAA,UAC7C,OAAO,KAAK,UAAU,OAAO;AAAA,UAC7B;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB,WAAS;AAC3B,QAAI,CAAC,KAAK,QAAS;AACnB,UAAM,SAAS,qBAAqB,KAAK;AACzC,UAAM,OAAO,SAAS,KAAK,oBAAoB,MAAM,IAAI;AACzD,QAAI,WAAW,MAAM,QAAQ;AAC3B,WAAK,yBAAyB;AAAA,IAChC;AAGA,QAAI,eAAe,MAAM,MAAM,GAAG;AAChC,YAAM,eAAe;AAAA,IACvB;AACA,SAAK,QAAQ,IAAI,MAAM,KAAK,cAAc,KAAK;AAAA,MAC7C,OAAO,MAAM,UAAU,OAAO;AAAA,MAC9B;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,WAAS;AAEvB,QAAI,KAAK,eAAe,QAAQ,KAAK,SAAS;AAC5C,UAAI,MAAM,SAAS,QAAQ;AACzB,aAAK,yBAAyB;AAAA,MAChC;AAIA,UAAI,MAAM,UAAU,KAAK,oBAAoB,MAAM,MAAM,KAAK,EAAE,MAAM,iBAAiB,KAAK,oBAAoB,MAAM,aAAa,IAAI;AACrI,aAAK,QAAQ,IAAI,MAAM,KAAK,cAAc,KAAK;AAAA,UAC7C,OAAO;AAAA,UACP;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,WAAS;AAC3B,QAAI,CAAC,KAAK,QAAS;AACnB,SAAK,yBAAyB;AAE9B,UAAM,OAAO,MAAM,UAAU,KAAK,oBAAoB,MAAM,MAAM;AAClE,QAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,KAAK,YAAY,GAAG;AAChD;AAAA,IACF;AACA,SAAK,QAAQ,IAAI,MAAM;AACrB,WAAK,YAAY,KAAK;AAAA,QACpB,OAAO,KAAK;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,kBAAkB,WAAS;AACzB,QAAI,CAAC,KAAK,QAAS;AACnB,UAAM,cAAc,eAAe,MAAM,MAAM;AAC/C,QAAI,CAAC,aAAa;AAEhB,WAAK,QAAQ,IAAI,MAAM;AACrB,aAAK,UAAU,KAAK;AAAA,UAClB,OAAO;AAAA,UACP;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD;AAAA,IACF;AACA,QAAI,YAAY,QAAQ,oBAAoB,MAAM,KAAK,YAAY,eAAe;AAGhF;AAAA,IACF;AACA,SAAK,QAAQ,IAAI,MAAM;AACrB,YAAM,OAAO,KAAK,oBAAoB,WAAW;AACjD,WAAK,UAAU,KAAK;AAAA,QAClB,OAAO,MAAM,YAAY;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,mBAAmB,WAAS;AAC1B,UAAM,SAAS,qBAAqB,KAAK;AACzC,QAAI,QAAQ;AACV,WAAK,gBAAgB;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,SAAS;AAC3B,UAAM,OAAO,eAAe,OAAO;AACnC,QAAI,MAAM;AACR,YAAM,MAAM,KAAK,aAAa,cAAc;AAC5C,YAAM,MAAM,KAAK,aAAa,cAAc;AAC5C,UAAI,OAAO,KAAK;AACd,eAAO,KAAK,KAAK,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC;AAAA,MAC/C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,WAAW,CAAC,GAAG,mBAAmB;AAAA,IAClC,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,uBAAuB;AAAA,MACvB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACP,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,IAC5B,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,eAAe,MAAM,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,kCAAkC,GAAG,IAAI,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,2BAA2B,GAAG,cAAc,eAAe,GAAG,CAAC,QAAQ,YAAY,GAAG,oCAAoC,GAAG,SAAS,cAAc,eAAe,GAAG,CAAC,QAAQ,YAAY,GAAG,kCAAkC,GAAG,CAAC,QAAQ,UAAU,GAAG,0BAA0B,GAAG,SAAS,SAAS,WAAW,UAAU,GAAG,CAAC,GAAG,kCAAkC,qBAAqB,GAAG,CAAC,eAAe,QAAQ,GAAG,gCAAgC,CAAC;AAAA,IACjkB,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,MAAM,CAAC;AACtE,QAAG,iBAAiB,GAAG,gCAAgC,GAAG,GAAG,MAAM,GAAG,YAAY,IAAI;AACtF,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,EAAE;AACZ,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,kBAAkB,IAAI,wBAAwB,IAAI,EAAE;AACzE,QAAG,UAAU;AACb,QAAG,WAAW,IAAI,IAAI;AACtB,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,MAAM,IAAI,iBAAiB;AACzC,QAAG,UAAU;AACb,QAAG,mBAAmB,KAAK,IAAI,yBAAyB,IAAI;AAC5D,QAAG,UAAU;AACb,QAAG,WAAW,MAAM,IAAI,eAAe;AACvC,QAAG,UAAU;AACb,QAAG,mBAAmB,KAAK,IAAI,uBAAuB,IAAI;AAC1D,QAAG,UAAU;AACb,QAAG,WAAW,MAAM,IAAI,2BAA2B;AACnD,QAAG,UAAU;AACb,QAAG,mBAAmB,KAAK,IAAI,8BAA8B,KAAK,IAAI,yBAAyB,IAAI;AACnG,QAAG,UAAU;AACb,QAAG,WAAW,MAAM,IAAI,yBAAyB;AACjD,QAAG,UAAU;AACb,QAAG,mBAAmB,KAAK,IAAI,8BAA8B,KAAK,IAAI,uBAAuB,IAAI;AAAA,MACnG;AAAA,IACF;AAAA,IACA,cAAc,CAAC,OAAO;AAAA,IACtB,QAAQ,CAAC,w2TAA02T;AAAA,IACn3T,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,OAAO;AAAA,MACjB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,w2TAA02T;AAAA,IACr3T,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,SAAS,YAAY,MAAM;AACzB,SAAO,MAAM,aAAa;AAC5B;AAKA,SAAS,eAAe,SAAS;AAC/B,MAAI;AACJ,MAAI,YAAY,OAAO,GAAG;AACxB,WAAO;AAAA,EACT,WAAW,YAAY,QAAQ,UAAU,GAAG;AAC1C,WAAO,QAAQ;AAAA,EACjB,WAAW,YAAY,QAAQ,YAAY,UAAU,GAAG;AACtD,WAAO,QAAQ,WAAW;AAAA,EAC5B;AACA,SAAO,MAAM,aAAa,cAAc,KAAK,OAAO,OAAO;AAC7D;AAEA,SAAS,QAAQ,OAAO,OAAO,KAAK;AAClC,SAAO,QAAQ,QAAQ,UAAU,OAAO,QAAQ,OAAO,UAAU;AACnE;AAEA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,UAAU,QAAQ,UAAU,OAAO,SAAS,SAAS,UAAU;AACxE;AAEA,SAAS,UAAU,OAAO,OAAO,KAAK,cAAc;AAClD,SAAO,gBAAgB,UAAU,QAAQ,QAAQ,QAAQ,UAAU,OAAO,SAAS,SAAS,SAAS;AACvG;AAKA,SAAS,qBAAqB,OAAO;AACnC,QAAM,gBAAgB,MAAM,eAAe,CAAC;AAC5C,SAAO,SAAS,iBAAiB,cAAc,SAAS,cAAc,OAAO;AAC/E;AAGA,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA,EACA,YACA,OACA,KAAK;AACH,SAAK,QAAQ;AACb,SAAK,MAAM;AAAA,EACb;AACF;AAKA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,oBAAoB,IAAI,QAAQ;AAAA;AAAA,EAEhC,mBAAmB,KAAK;AAAA,EACxB,YACA,WAAW,UAAU;AACnB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,OAAO,QAAQ;AAC7B,UAAM,WAAW,KAAK;AACtB,SAAK,YAAY;AACjB,SAAK,kBAAkB,KAAK;AAAA,MAC1B,WAAW;AAAA,MACX;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB,SAAS;AAAA,EAClC;AAAA,EACA,qBAAqB,MAAM;AACzB,WAAO,KAAK,SAAS,eAAe,IAAI,KAAK,KAAK,SAAS,QAAQ,IAAI;AAAA,EACzE;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,IAAG,iBAAiB;AAAA,EACtB;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,EACjC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,8BAAN,MAAM,qCAAoC,sBAAsB;AAAA,EAC9D,YAAY,SAAS;AACnB,UAAM,MAAM,OAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM;AACR,UAAM,gBAAgB,MAAM,IAAI;AAAA,EAClC;AAAA;AAAA,EAEA,UAAU;AACR,WAAO,KAAK,aAAa,QAAQ,KAAK,qBAAqB,KAAK,SAAS;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,QAAQ;AACN,UAAM,QAAQ,IAAI,6BAA4B,KAAK,QAAQ;AAC3D,UAAM,gBAAgB,KAAK,WAAW,IAAI;AAC1C,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,oCAAoC,mBAAmB;AAC5E,WAAO,KAAK,qBAAqB,8BAAgC,SAAS,WAAW,CAAC;AAAA,EACxF;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,6BAA4B;AAAA,EACvC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,6BAAN,MAAM,oCAAmC,sBAAsB;AAAA,EAC7D,YAAY,SAAS;AACnB,UAAM,IAAI,UAAU,MAAM,IAAI,GAAG,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAM;AACR,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,SAAS,MAAM;AACjB,cAAQ;AAAA,IACV,WAAW,OAAO,MAAM;AACtB,YAAM;AAAA,IACR,OAAO;AACL,cAAQ;AACR,YAAM;AAAA,IACR;AACA,UAAM,gBAAgB,IAAI,UAAU,OAAO,GAAG,GAAG,IAAI;AAAA,EACvD;AAAA;AAAA,EAEA,UAAU;AACR,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AAET,QAAI,SAAS,QAAQ,OAAO,MAAM;AAChC,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,QAAQ,OAAO,MAAM;AAChC,aAAO,KAAK,qBAAqB,KAAK,KAAK,KAAK,qBAAqB,GAAG,KAAK,KAAK,SAAS,YAAY,OAAO,GAAG,KAAK;AAAA,IACxH;AAEA,YAAQ,SAAS,QAAQ,KAAK,qBAAqB,KAAK,OAAO,OAAO,QAAQ,KAAK,qBAAqB,GAAG;AAAA,EAC7G;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,WAAO,KAAK,UAAU,SAAS,QAAQ,KAAK,UAAU,OAAO;AAAA,EAC/D;AAAA;AAAA,EAEA,QAAQ;AACN,UAAM,QAAQ,IAAI,4BAA2B,KAAK,QAAQ;AAC1D,UAAM,gBAAgB,KAAK,WAAW,IAAI;AAC1C,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA+B,SAAS,WAAW,CAAC;AAAA,EACvF;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,4BAA2B;AAAA,EACtC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,SAAS,wCAAwC,QAAQ,SAAS;AAChE,SAAO,UAAU,IAAI,4BAA4B,OAAO;AAC1D;AAOA,IAAM,2CAA2C;AAAA,EAC/C,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,qBAAqB,GAAG,WAAW;AAAA,EAC3E,YAAY;AACd;AAMA,SAAS,uCAAuC,QAAQ,SAAS;AAC/D,SAAO,UAAU,IAAI,2BAA2B,OAAO;AACzD;AAOA,IAAM,0CAA0C;AAAA,EAC9C,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,qBAAqB,GAAG,WAAW;AAAA,EAC3E,YAAY;AACd;AAGA,IAAM,oCAAoC,IAAI,eAAe,mCAAmC;AAEhG,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC;AAAA,EACA,YAAY,cAAc;AACxB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,kBAAkB,MAAM,cAAc;AACpC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS,MAAM;AACjB,cAAQ;AAAA,IACV,WAAW,OAAO,QAAQ,QAAQ,KAAK,aAAa,YAAY,MAAM,KAAK,KAAK,GAAG;AACjF,YAAM;AAAA,IACR,OAAO;AACL,cAAQ;AACR,YAAM;AAAA,IACR;AACA,WAAO,IAAI,UAAU,OAAO,GAAG;AAAA,EACjC;AAAA,EACA,cAAc,YAAY,cAAc;AACtC,QAAI,QAAQ;AACZ,QAAI,MAAM;AACV,QAAI,aAAa,SAAS,CAAC,aAAa,OAAO,YAAY;AACzD,cAAQ,aAAa;AACrB,YAAM;AAAA,IACR;AACA,WAAO,IAAI,UAAU,OAAO,GAAG;AAAA,EACjC;AAAA,EACA,WAAW,YAAY,eAAe,SAAS;AAC7C,QAAI,QAAQ,cAAc;AAC1B,QAAI,MAAM,cAAc;AACxB,QAAI,CAAC,SAAS,CAAC,KAAK;AAElB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,QAAQ,YAAY,OAAO,GAAG,MAAM;AACpD,UAAM,YAAY,QAAQ,QAAQ,OAAO,IAAI,QAAQ,QAAQ,UAAU;AACvE,UAAM,aAAa,QAAQ,SAAS,OAAO,IAAI,QAAQ,SAAS,UAAU;AAC1E,UAAM,WAAW,QAAQ,QAAQ,OAAO,IAAI,QAAQ,QAAQ,UAAU;AACtE,QAAI,WAAW,QAAQ,SAAS,YAAY,cAAc,KAAK,GAAG;AAChE,cAAQ;AACR,UAAI,QAAQ,YAAY,SAAS,GAAG,IAAI,GAAG;AACzC,cAAM,QAAQ,iBAAiB,KAAK,SAAS;AAC7C,cAAM,QAAQ,kBAAkB,KAAK,UAAU;AAC/C,cAAM,QAAQ,gBAAgB,KAAK,QAAQ;AAAA,MAC7C;AAAA,IACF,WAAW,WAAW,QAAQ,SAAS,YAAY,cAAc,GAAG,GAAG;AACrE,YAAM;AACN,UAAI,QAAQ,YAAY,SAAS,KAAK,IAAI,GAAG;AAC3C,gBAAQ,QAAQ,iBAAiB,OAAO,SAAS;AACjD,gBAAQ,QAAQ,kBAAkB,OAAO,UAAU;AACnD,gBAAQ,QAAQ,gBAAgB,OAAO,QAAQ;AAAA,MACjD;AAAA,IACF,OAAO;AACL,cAAQ,QAAQ,iBAAiB,OAAO,SAAS;AACjD,cAAQ,QAAQ,kBAAkB,OAAO,UAAU;AACnD,cAAQ,QAAQ,gBAAgB,OAAO,QAAQ;AAC/C,YAAM,QAAQ,iBAAiB,KAAK,SAAS;AAC7C,YAAM,QAAQ,kBAAkB,KAAK,UAAU;AAC/C,YAAM,QAAQ,gBAAgB,KAAK,QAAQ;AAAA,IAC7C;AACA,WAAO,IAAI,UAAU,OAAO,GAAG;AAAA,EACjC;AAAA,EACA,OAAO,OAAO,SAAS,wCAAwC,mBAAmB;AAChF,WAAO,KAAK,qBAAqB,kCAAoC,SAAS,WAAW,CAAC;AAAA,EAC5F;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iCAAgC;AAAA,EAC3C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,SAAS,6CAA6C,QAAQ,SAAS;AACrE,SAAO,UAAU,IAAI,gCAAgC,OAAO;AAC9D;AAMA,IAAM,uCAAuC;AAAA,EAC3C,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,iCAAiC,GAAG,WAAW;AAAA,EACvF,YAAY;AACd;AACA,IAAM,gBAAgB;AACtB,IAAI,kBAAkB;AAKtB,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,eAAe,OAAO,kBAAkB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,iBAAiB,OAAO,mCAAmC;AAAA,IACzD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,wBAAwB,aAAa;AAAA;AAAA,EAErC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,UAAM,gBAAgB,KAAK;AAC3B,UAAM,YAAY,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC,KAAK,KAAK,aAAa,MAAM;AACxH,SAAK,cAAc,KAAK,aAAa,UAAU,WAAW,KAAK,SAAS,KAAK,OAAO;AACpF,QAAI,CAAC,KAAK,qBAAqB,eAAe,KAAK,WAAW,GAAG;AAC/D,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,iBAAiB,WAAW;AAC9B,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,WAAK,YAAY,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,IAC5F;AACA,SAAK,WAAW,KAAK,SAAS;AAAA,EAChC;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA,EAEb,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,cAAc,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,YAAY,IAAI,aAAa;AAAA;AAAA,EAE7B,mBAAmB,IAAI,aAAa;AAAA;AAAA,EAEpC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AACzD,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,2BAA2B,aAAa;AAAA,MAChD;AACA,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,2BAA2B,kBAAkB;AAAA,MACrD;AAAA,IACF;AACA,SAAK,cAAc,KAAK,aAAa,MAAM;AAAA,EAC7C;AAAA,EACA,qBAAqB;AACnB,SAAK,wBAAwB,KAAK,aAAa,cAAc,KAAK,UAAU,IAAI,CAAC,EAAE,UAAU,MAAM,KAAK,MAAM,CAAC;AAAA,EACjH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,mBAAmB,QAAQ,iBAAiB,KAAK,QAAQ,eAAe;AAC9E,QAAI,oBAAoB,CAAC,iBAAiB,aAAa;AACrD,WAAK,WAAW,KAAK,QAAQ;AAAA,IAC/B;AACA,QAAI,QAAQ,YAAY,KAAK,CAAC,KAAK,YAAY;AAC7C,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,UAAM,OAAO,MAAM;AACnB,UAAM,eAAe,KAAK,uBAAuB,IAAI;AACrD,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,qBAAqB,WAAW;AACvC,uBAAiB,KAAK,uBAAuB,KAAK,UAAU,KAAK;AACjE,qBAAe,KAAK,uBAAuB,KAAK,UAAU,GAAG;AAAA,IAC/D,OAAO;AACL,uBAAiB,eAAe,KAAK,uBAAuB,KAAK,SAAS;AAAA,IAC5E;AACA,QAAI,mBAAmB,QAAQ,iBAAiB,MAAM;AACpD,WAAK,eAAe,KAAK,YAAY;AAAA,IACvC;AACA,SAAK,eAAe,KAAK;AAAA,MACvB,OAAO;AAAA,MACP,OAAO,MAAM;AAAA,IACf,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,OAAO;AACvB,UAAM,QAAQ,MAAM;AACpB,UAAM,gBAAgB,KAAK;AAC3B,SAAK,aAAa,KAAK,uBAAuB,KAAK;AACnD,QAAI,KAAK,aAAa,YAAY,eAAe,KAAK,UAAU,GAAG;AACjE,WAAK,iBAAiB,KAAK,KAAK,WAAW;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAEA,2BAA2B,OAAO;AAIhC,UAAM,gBAAgB,KAAK;AAC3B,UAAM,QAAQ,KAAK,OAAO;AAC1B,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,gBAAgB,KAAK,aAAa,QAAQ,IAAI,EAAE;AACpF;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,gBAAgB,KAAK,aAAa,QAAQ,KAAK,CAAC;AACpF;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,gBAAgB,KAAK,aAAa,EAAE;AACxE;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,gBAAgB,KAAK,aAAa,CAAC;AACvE;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,gBAAgB,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ,KAAK,WAAW,CAAC;AACrH;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,gBAAgB,KAAK,aAAa,KAAK,aAAa,kBAAkB,KAAK,WAAW,IAAI,KAAK,aAAa,QAAQ,KAAK,WAAW,CAAC;AACzK;AAAA,MACF,KAAK;AACH,aAAK,aAAa,MAAM,SAAS,KAAK,aAAa,iBAAiB,KAAK,aAAa,EAAE,IAAI,KAAK,aAAa,kBAAkB,KAAK,aAAa,EAAE;AACpJ;AAAA,MACF,KAAK;AACH,aAAK,aAAa,MAAM,SAAS,KAAK,aAAa,iBAAiB,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,kBAAkB,KAAK,aAAa,CAAC;AAClJ;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,aAAK,uBAAuB;AAC5B,YAAI,KAAK,WAAW,KAAK,WAAW,GAAG;AAMrC,gBAAM,eAAe;AAAA,QACvB;AACA;AAAA,MACF,KAAK;AAEH,YAAI,KAAK,eAAe,QAAQ,CAAC,eAAe,KAAK,GAAG;AACtD,eAAK,cAAc;AAGnB,cAAI,KAAK,YAAY;AACnB,iBAAK,UAAU,KAAK;AAAA,cAClB,OAAO;AAAA,cACP;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,eAAe,KAAK,IAAI;AAC7B,iBAAK,eAAe,KAAK;AAAA,cACvB,OAAO;AAAA,cACP;AAAA,YACF,CAAC;AAAA,UACH;AACA,gBAAM,eAAe;AACrB,gBAAM,gBAAgB;AAAA,QACxB;AACA;AAAA,MACF;AAEE;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,YAAY,eAAe,KAAK,UAAU,GAAG;AACjE,WAAK,iBAAiB,KAAK,KAAK,UAAU;AAC1C,WAAK,iCAAiC;AAAA,IACxC;AAEA,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,yBAAyB,OAAO;AAC9B,QAAI,MAAM,YAAY,SAAS,MAAM,YAAY,OAAO;AACtD,UAAI,KAAK,wBAAwB,KAAK,WAAW,KAAK,WAAW,GAAG;AAClE,aAAK,cAAc;AAAA,UACjB,OAAO,KAAK,aAAa,QAAQ,KAAK,WAAW;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,aAAa,KAAK,qBAAqB,KAAK,aAAa,MAAM,CAAC;AACrE,SAAK,cAAc,KAAK,aAAa,QAAQ,aAAa,KAAK,aAAa,OAAO,KAAK,YAAY,KAAK,aAAa,QAAQ,UAAU,IAAI,KAAK,aAAa,cAAc,OAAO,EAAE,KAAK,aAAa,SAAS,KAAK,UAAU,CAAC,EAAE,kBAAkB;AACpP,QAAI,eAAe,KAAK,aAAa,WAAW,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,KAAK,aAAa,SAAS,KAAK,UAAU,GAAG,CAAC;AAC1I,SAAK,oBAAoB,gBAAgB,KAAK,aAAa,aAAa,YAAY,IAAI,KAAK,aAAa,kBAAkB,KAAK;AACjI,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,iBAAiB,aAAa;AAC5B,SAAK,iBAAiB,iBAAiB,WAAW;AAAA,EACpD;AAAA;AAAA,EAEA,mCAAmC;AACjC,SAAK,iBAAiB,yCAAyC;AAAA,EACjE;AAAA;AAAA,EAEA,gBAAgB;AAAA,IACd;AAAA,IACA,OAAO;AAAA,EACT,GAAG;AACD,QAAI,KAAK,gBAAgB;AAGvB,YAAM,QAAQ,OAAO,KAAK,WAAW;AACrC,YAAM,eAAe,KAAK,eAAe,cAAc,OAAO,KAAK,UAAU,KAAK;AAClF,WAAK,gBAAgB,KAAK,qBAAqB,aAAa,KAAK;AACjE,WAAK,cAAc,KAAK,qBAAqB,aAAa,GAAG;AAC7D,UAAI,KAAK,cAAc,OAAO;AAC5B,cAAM,YAAY,KAAK,eAAe,aAAa,KAAK,WAAW,OAAO,KAAK,UAAU,OAAO,KAAK;AACrG,YAAI,WAAW;AACb,eAAK,gBAAgB,KAAK,qBAAqB,UAAU,KAAK;AAC9D,eAAK,cAAc,KAAK,qBAAqB,UAAU,GAAG;AAAA,QAC5D;AAAA,MACF;AAKA,WAAK,mBAAmB,cAAc;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,QAAI,CAAC,KAAK,WAAY;AACtB,QAAI,MAAM,OAAO;AAEf,YAAM,iBAAiB,KAAK,gBAAgB,aAAa,KAAK,WAAW,OAAO,KAAK,UAAU,MAAM,OAAO,MAAM,KAAK;AACvH,WAAK,UAAU,KAAK;AAAA,QAClB,OAAO,kBAAkB;AAAA,QACzB,OAAO,MAAM;AAAA,MACf,CAAC;AAAA,IACH,OAAO;AACL,WAAK,UAAU,KAAK;AAAA,QAClB,OAAO;AAAA,QACP,OAAO,MAAM;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,YAAY;AACjC,WAAO,KAAK,aAAa,WAAW,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,KAAK,aAAa,SAAS,KAAK,UAAU,GAAG,UAAU;AAAA,EACzI;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,iBAAiB,KAAK,aAAa,kBAAkB;AAC3D,UAAM,iBAAiB,KAAK,aAAa,kBAAkB,QAAQ;AACnE,UAAM,eAAe,KAAK,aAAa,kBAAkB,MAAM;AAE/D,QAAI,WAAW,aAAa,IAAI,CAAC,MAAM,MAAM;AAC3C,aAAO;AAAA,QACL;AAAA,QACA,QAAQ,eAAe,CAAC;AAAA,QACxB,IAAI;AAAA,MACN;AAAA,IACF,CAAC;AACD,SAAK,YAAY,SAAS,MAAM,cAAc,EAAE,OAAO,SAAS,MAAM,GAAG,cAAc,CAAC;AAAA,EAC1F;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,cAAc,KAAK,aAAa,kBAAkB,KAAK,UAAU;AACvE,UAAM,YAAY,KAAK,aAAa,aAAa;AACjD,SAAK,SAAS,CAAC,CAAC,CAAC;AACjB,aAAS,IAAI,GAAG,OAAO,KAAK,kBAAkB,IAAI,aAAa,KAAK,QAAQ;AAC1E,UAAI,QAAQ,eAAe;AACzB,aAAK,OAAO,KAAK,CAAC,CAAC;AACnB,eAAO;AAAA,MACT;AACA,YAAM,OAAO,KAAK,aAAa,WAAW,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,KAAK,aAAa,SAAS,KAAK,UAAU,GAAG,IAAI,CAAC;AACxI,YAAM,UAAU,KAAK,kBAAkB,IAAI;AAC3C,YAAM,YAAY,KAAK,aAAa,OAAO,MAAM,KAAK,aAAa,QAAQ,aAAa;AACxF,YAAM,cAAc,KAAK,YAAY,KAAK,UAAU,MAAM,OAAO,IAAI;AACrE,WAAK,OAAO,KAAK,OAAO,SAAS,CAAC,EAAE,KAAK,IAAI,gBAAgB,IAAI,GAAG,UAAU,CAAC,GAAG,WAAW,SAAS,aAAa,KAAK,qBAAqB,IAAI,GAAG,IAAI,CAAC;AAAA,IAC3J;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,MAAM;AACtB,WAAO,CAAC,CAAC,SAAS,CAAC,KAAK,WAAW,KAAK,aAAa,YAAY,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,KAAK,WAAW,KAAK,aAAa,YAAY,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,KAAK,cAAc,KAAK,WAAW,IAAI;AAAA,EACtN;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,MAAM;AAC3B,WAAO,QAAQ,KAAK,qBAAqB,MAAM,KAAK,UAAU,IAAI,KAAK,aAAa,QAAQ,IAAI,IAAI;AAAA,EACtG;AAAA;AAAA,EAEA,qBAAqB,IAAI,IAAI;AAC3B,WAAO,CAAC,EAAE,MAAM,MAAM,KAAK,aAAa,SAAS,EAAE,KAAK,KAAK,aAAa,SAAS,EAAE,KAAK,KAAK,aAAa,QAAQ,EAAE,KAAK,KAAK,aAAa,QAAQ,EAAE;AAAA,EACzJ;AAAA;AAAA,EAEA,qBAAqB,MAAM;AACzB,QAAI,MAAM;AAGR,YAAM,OAAO,KAAK,aAAa,QAAQ,IAAI;AAC3C,YAAM,QAAQ,KAAK,aAAa,SAAS,IAAI;AAC7C,YAAM,MAAM,KAAK,aAAa,QAAQ,IAAI;AAC1C,aAAO,IAAI,KAAK,MAAM,OAAO,GAAG,EAAE,QAAQ;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,QAAQ,KAAK,KAAK,UAAU;AAAA,EAC1C;AAAA;AAAA,EAEA,WAAW,eAAe;AACxB,QAAI,yBAAyB,WAAW;AACtC,WAAK,cAAc,KAAK,qBAAqB,cAAc,KAAK;AAChE,WAAK,YAAY,KAAK,qBAAqB,cAAc,GAAG;AAC5D,WAAK,WAAW;AAAA,IAClB,OAAO;AACL,WAAK,cAAc,KAAK,YAAY,KAAK,qBAAqB,aAAa;AAC3E,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,wBAAwB,KAAK,qBAAqB,KAAK,eAAe;AAC3E,SAAK,sBAAsB,KAAK,qBAAqB,KAAK,aAAa;AAAA,EACzE;AAAA;AAAA,EAEA,WAAW,MAAM;AACf,WAAO,CAAC,KAAK,cAAc,KAAK,WAAW,IAAI;AAAA,EACjD;AAAA;AAAA,EAEA,gBAAgB;AACd,SAAK,gBAAgB,KAAK,cAAc;AAAA,EAC1C;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,WAAW,SAAS,mBAAmB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,CAAC;AAAA,MACnC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,YAAY;AAAA,IACd;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,WAAW;AAAA,MACX,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,QAAQ,GAAG,oBAAoB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,eAAe,MAAM,GAAG,CAAC,WAAW,KAAK,GAAG,mCAAmC,GAAG,CAAC,qBAAqB,IAAI,GAAG,uBAAuB,oBAAoB,iBAAiB,eAAe,aAAa,SAAS,WAAW,SAAS,QAAQ,cAAc,cAAc,YAAY,mBAAmB,iBAAiB,gBAAgB,cAAc,WAAW,yBAAyB,cAAc,2BAA2B,uBAAuB,GAAG,CAAC,GAAG,qBAAqB,CAAC;AAAA,IAChkB,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,IAAI;AACvD,QAAG,iBAAiB,GAAG,6BAA6B,GAAG,GAAG,MAAM,GAAG,UAAU;AAC7E,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,QAAG,UAAU,GAAG,MAAM,CAAC;AACvB,QAAG,aAAa,EAAE;AAClB,QAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,QAAG,WAAW,uBAAuB,SAAS,2DAA2D,QAAQ;AAC/G,iBAAO,IAAI,cAAc,MAAM;AAAA,QACjC,CAAC,EAAE,oBAAoB,SAAS,wDAAwD,QAAQ;AAC9F,iBAAO,IAAI,kBAAkB,MAAM;AAAA,QACrC,CAAC,EAAE,iBAAiB,SAAS,qDAAqD,QAAQ;AACxF,iBAAO,IAAI,gBAAgB,MAAM;AAAA,QACnC,CAAC,EAAE,eAAe,SAAS,mDAAmD,QAAQ;AACpF,iBAAO,IAAI,YAAY,KAAK,MAAM;AAAA,QACpC,CAAC,EAAE,aAAa,SAAS,iDAAiD,QAAQ;AAChF,iBAAO,IAAI,WAAW,MAAM;AAAA,QAC9B,CAAC,EAAE,SAAS,SAAS,6CAA6C,QAAQ;AACxE,iBAAO,IAAI,yBAAyB,MAAM;AAAA,QAC5C,CAAC,EAAE,WAAW,SAAS,+CAA+C,QAAQ;AAC5E,iBAAO,IAAI,2BAA2B,MAAM;AAAA,QAC9C,CAAC;AACD,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,IAAI,SAAS;AAC3B,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,SAAS,IAAI,WAAW,EAAE,QAAQ,IAAI,MAAM,EAAE,cAAc,IAAI,UAAU,EAAE,cAAc,IAAI,WAAW,EAAE,YAAY,IAAI,SAAS,EAAE,mBAAmB,IAAI,qBAAqB,EAAE,iBAAiB,IAAI,mBAAmB,EAAE,gBAAgB,IAAI,aAAa,EAAE,cAAc,IAAI,WAAW,EAAE,WAAW,IAAI,QAAQ,EAAE,yBAAyB,CAAC,EAAE,cAAc,IAAI,aAAa,QAAQ,IAAI,UAAU,IAAI,CAAC,EAAE,2BAA2B,IAAI,uBAAuB,EAAE,yBAAyB,IAAI,qBAAqB;AAAA,MAC3gB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe;AAAA,IAC9B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,eAAe;AAAA,MACzB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAe;AACrB,IAAM,cAAc;AAKpB,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,wBAAwB,aAAa;AAAA;AAAA,EAErC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,QAAI,gBAAgB,KAAK;AACzB,UAAM,YAAY,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC,KAAK,KAAK,aAAa,MAAM;AACxH,SAAK,cAAc,KAAK,aAAa,UAAU,WAAW,KAAK,SAAS,KAAK,OAAO;AACpF,QAAI,CAAC,oBAAoB,KAAK,cAAc,eAAe,KAAK,aAAa,KAAK,SAAS,KAAK,OAAO,GAAG;AACxG,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,iBAAiB,WAAW;AAC9B,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,WAAK,YAAY,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,IAC5F;AACA,SAAK,iBAAiB,KAAK;AAAA,EAC7B;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,eAAe,IAAI,aAAa;AAAA;AAAA,EAEhC,mBAAmB,IAAI,aAAa;AAAA;AAAA,EAEpC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,iBAAiB,OAAO,cAAc,eAAe,YAAY;AACzE,YAAM,2BAA2B,aAAa;AAAA,IAChD;AACA,SAAK,cAAc,KAAK,aAAa,MAAM;AAAA,EAC7C;AAAA,EACA,qBAAqB;AACnB,SAAK,wBAAwB,KAAK,aAAa,cAAc,KAAK,UAAU,IAAI,CAAC,EAAE,UAAU,MAAM,KAAK,MAAM,CAAC;AAAA,EACjH;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,aAAa,KAAK,aAAa,QAAQ,KAAK,aAAa,MAAM,CAAC;AAMrE,UAAM,aAAa,KAAK,aAAa,QAAQ,KAAK,WAAW;AAC7D,UAAM,gBAAgB,aAAa,gBAAgB,KAAK,cAAc,KAAK,YAAY,KAAK,SAAS,KAAK,OAAO;AACjH,SAAK,SAAS,CAAC;AACf,aAAS,IAAI,GAAG,MAAM,CAAC,GAAG,IAAI,cAAc,KAAK;AAC/C,UAAI,KAAK,gBAAgB,CAAC;AAC1B,UAAI,IAAI,UAAU,aAAa;AAC7B,aAAK,OAAO,KAAK,IAAI,IAAI,UAAQ,KAAK,mBAAmB,IAAI,CAAC,CAAC;AAC/D,cAAM,CAAC;AAAA,MACT;AAAA,IACF;AACA,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,UAAM,OAAO,MAAM;AACnB,UAAM,eAAe,KAAK,aAAa,WAAW,MAAM,GAAG,CAAC;AAC5D,UAAM,eAAe,KAAK,iBAAiB,IAAI;AAC/C,SAAK,aAAa,KAAK,YAAY;AACnC,SAAK,eAAe,KAAK,YAAY;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,OAAO;AACvB,UAAM,OAAO,MAAM;AACnB,UAAM,gBAAgB,KAAK;AAC3B,SAAK,aAAa,KAAK,iBAAiB,IAAI;AAC5C,QAAI,KAAK,aAAa,YAAY,eAAe,KAAK,UAAU,GAAG;AACjE,WAAK,iBAAiB,KAAK,KAAK,UAAU;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA,EAEA,2BAA2B,OAAO;AAChC,UAAM,gBAAgB,KAAK;AAC3B,UAAM,QAAQ,KAAK,OAAO;AAC1B,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,QAAQ,IAAI,EAAE;AACrF;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,QAAQ,KAAK,CAAC;AACrF;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,CAAC,WAAW;AACnF;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,WAAW;AAClF;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,CAAC,gBAAgB,KAAK,cAAc,KAAK,YAAY,KAAK,SAAS,KAAK,OAAO,CAAC;AACvJ;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,eAAe,gBAAgB,KAAK,cAAc,KAAK,YAAY,KAAK,SAAS,KAAK,OAAO,IAAI,CAAC;AACzK;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,MAAM,SAAS,CAAC,eAAe,KAAK,CAAC,YAAY;AACxH;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,MAAM,SAAS,eAAe,KAAK,YAAY;AACtH;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAKH,aAAK,uBAAuB;AAC5B;AAAA,MACF;AAEE;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,YAAY,eAAe,KAAK,UAAU,GAAG;AACjE,WAAK,iBAAiB,KAAK,KAAK,UAAU;AAAA,IAC5C;AACA,SAAK,iCAAiC;AAEtC,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,yBAAyB,OAAO;AAC9B,QAAI,MAAM,YAAY,SAAS,MAAM,YAAY,OAAO;AACtD,UAAI,KAAK,sBAAsB;AAC7B,aAAK,cAAc;AAAA,UACjB,OAAO,KAAK,aAAa,QAAQ,KAAK,WAAW;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,WAAO,gBAAgB,KAAK,cAAc,KAAK,YAAY,KAAK,SAAS,KAAK,OAAO;AAAA,EACvF;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,iBAAiB,iBAAiB;AAAA,EACzC;AAAA;AAAA,EAEA,mCAAmC;AACjC,SAAK,iBAAiB,yCAAyC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM;AACrB,UAAM,cAAc,KAAK,aAAa,SAAS,KAAK,UAAU;AAC9D,UAAM,cAAc,KAAK,aAAa,kBAAkB,KAAK,aAAa,WAAW,MAAM,aAAa,CAAC,CAAC;AAC1G,UAAM,iBAAiB,KAAK,aAAa,WAAW,MAAM,aAAa,KAAK,IAAI,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,WAAW,CAAC;AACxI,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,mBAAmB,MAAM;AACvB,UAAM,OAAO,KAAK,aAAa,WAAW,MAAM,GAAG,CAAC;AACpD,UAAM,WAAW,KAAK,aAAa,YAAY,IAAI;AACnD,UAAM,cAAc,KAAK,YAAY,KAAK,UAAU,MAAM,YAAY,IAAI;AAC1E,WAAO,IAAI,gBAAgB,MAAM,UAAU,UAAU,KAAK,kBAAkB,IAAI,GAAG,WAAW;AAAA,EAChG;AAAA;AAAA,EAEA,kBAAkB,MAAM;AAEtB,QAAI,SAAS,UAAa,SAAS,QAAQ,KAAK,WAAW,OAAO,KAAK,aAAa,QAAQ,KAAK,OAAO,KAAK,KAAK,WAAW,OAAO,KAAK,aAAa,QAAQ,KAAK,OAAO,GAAG;AAC3K,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,aAAa,WAAW,MAAM,GAAG,CAAC;AAE3D,aAAS,OAAO,aAAa,KAAK,aAAa,QAAQ,IAAI,KAAK,MAAM,OAAO,KAAK,aAAa,gBAAgB,MAAM,CAAC,GAAG;AACvH,UAAI,KAAK,WAAW,IAAI,GAAG;AACzB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,QAAQ,KAAK,KAAK,UAAU;AAAA,EAC1C;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,SAAK,gBAAgB;AACrB,QAAI,iBAAiB,WAAW;AAC9B,YAAM,eAAe,MAAM,SAAS,MAAM;AAC1C,UAAI,cAAc;AAChB,aAAK,gBAAgB,KAAK,aAAa,QAAQ,YAAY;AAAA,MAC7D;AAAA,IACF,WAAW,OAAO;AAChB,WAAK,gBAAgB,KAAK,aAAa,QAAQ,KAAK;AAAA,IACtD;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,IACnC,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,CAAC;AAAA,MACnC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU,CAAC,kBAAkB;AAAA,IAC7B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,QAAQ,GAAG,oBAAoB,GAAG,CAAC,eAAe,QAAQ,GAAG,2BAA2B,GAAG,CAAC,WAAW,KAAK,GAAG,mCAAmC,GAAG,CAAC,qBAAqB,IAAI,GAAG,uBAAuB,oBAAoB,SAAS,WAAW,QAAQ,cAAc,cAAc,YAAY,WAAW,mBAAmB,YAAY,CAAC;AAAA,IAC9V,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,IAAI;AACvD,QAAG,UAAU,GAAG,MAAM,CAAC;AACvB,QAAG,aAAa,EAAE;AAClB,QAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,QAAG,WAAW,uBAAuB,SAAS,+DAA+D,QAAQ;AACnH,iBAAO,IAAI,cAAc,MAAM;AAAA,QACjC,CAAC,EAAE,oBAAoB,SAAS,4DAA4D,QAAQ;AAClG,iBAAO,IAAI,kBAAkB,MAAM;AAAA,QACrC,CAAC,EAAE,SAAS,SAAS,iDAAiD,QAAQ;AAC5E,iBAAO,IAAI,yBAAyB,MAAM;AAAA,QAC5C,CAAC,EAAE,WAAW,SAAS,mDAAmD,QAAQ;AAChF,iBAAO,IAAI,2BAA2B,MAAM;AAAA,QAC9C,CAAC;AACD,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,IAAI,MAAM,EAAE,cAAc,IAAI,UAAU,EAAE,cAAc,IAAI,aAAa,EAAE,YAAY,IAAI,aAAa,EAAE,WAAW,CAAC,EAAE,mBAAmB,IAAI,CAAC,EAAE,cAAc,IAAI,eAAe,CAAC;AAAA,MAC5M;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe;AAAA,IAC9B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,eAAe;AAAA,MACzB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,oBAAoB,aAAa,OAAO,OAAO,SAAS,SAAS;AACxE,QAAM,QAAQ,YAAY,QAAQ,KAAK;AACvC,QAAM,QAAQ,YAAY,QAAQ,KAAK;AACvC,QAAM,eAAe,gBAAgB,aAAa,SAAS,OAAO;AAClE,SAAO,KAAK,OAAO,QAAQ,gBAAgB,YAAY,MAAM,KAAK,OAAO,QAAQ,gBAAgB,YAAY;AAC/G;AAMA,SAAS,gBAAgB,aAAa,YAAY,SAAS,SAAS;AAClE,QAAM,aAAa,YAAY,QAAQ,UAAU;AACjD,SAAO,gBAAgB,aAAa,gBAAgB,aAAa,SAAS,OAAO,GAAG,YAAY;AAClG;AAKA,SAAS,gBAAgB,aAAa,SAAS,SAAS;AACtD,MAAI,eAAe;AACnB,MAAI,SAAS;AACX,UAAM,UAAU,YAAY,QAAQ,OAAO;AAC3C,mBAAe,UAAU,eAAe;AAAA,EAC1C,WAAW,SAAS;AAClB,mBAAe,YAAY,QAAQ,OAAO;AAAA,EAC5C;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,UAAQ,IAAI,IAAI,KAAK;AACvB;AAMA,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,eAAe,OAAO,kBAAkB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,wBAAwB,aAAa;AAAA;AAAA,EAErC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,QAAI,gBAAgB,KAAK;AACzB,UAAM,YAAY,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC,KAAK,KAAK,aAAa,MAAM;AACxH,SAAK,cAAc,KAAK,aAAa,UAAU,WAAW,KAAK,SAAS,KAAK,OAAO;AACpF,QAAI,KAAK,aAAa,QAAQ,aAAa,MAAM,KAAK,aAAa,QAAQ,KAAK,WAAW,GAAG;AAC5F,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,iBAAiB,WAAW;AAC9B,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,WAAK,YAAY,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,IAC5F;AACA,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,gBAAgB,IAAI,aAAa;AAAA;AAAA,EAEjC,mBAAmB,IAAI,aAAa;AAAA;AAAA,EAEpC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,cAAc;AACZ,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,2BAA2B,aAAa;AAAA,MAChD;AACA,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,2BAA2B,kBAAkB;AAAA,MACrD;AAAA,IACF;AACA,SAAK,cAAc,KAAK,aAAa,MAAM;AAAA,EAC7C;AAAA,EACA,qBAAqB;AACnB,SAAK,wBAAwB,KAAK,aAAa,cAAc,KAAK,UAAU,IAAI,CAAC,EAAE,UAAU,MAAM,KAAK,MAAM,CAAC;AAAA,EACjH;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,UAAM,QAAQ,MAAM;AACpB,UAAM,gBAAgB,KAAK,aAAa,WAAW,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,OAAO,CAAC;AACvG,SAAK,cAAc,KAAK,aAAa;AACrC,UAAM,eAAe,KAAK,kBAAkB,KAAK;AACjD,SAAK,eAAe,KAAK,YAAY;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,OAAO;AACvB,UAAM,QAAQ,MAAM;AACpB,UAAM,gBAAgB,KAAK;AAC3B,SAAK,aAAa,KAAK,kBAAkB,KAAK;AAC9C,QAAI,KAAK,aAAa,YAAY,eAAe,KAAK,UAAU,GAAG;AACjE,WAAK,iBAAiB,KAAK,KAAK,UAAU;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA,EAEA,2BAA2B,OAAO;AAIhC,UAAM,gBAAgB,KAAK;AAC3B,UAAM,QAAQ,KAAK,OAAO;AAC1B,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,kBAAkB,KAAK,aAAa,QAAQ,IAAI,EAAE;AACtF;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,kBAAkB,KAAK,aAAa,QAAQ,KAAK,CAAC;AACtF;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,kBAAkB,KAAK,aAAa,EAAE;AAC1E;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,kBAAkB,KAAK,aAAa,CAAC;AACzE;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,kBAAkB,KAAK,aAAa,CAAC,KAAK,aAAa,SAAS,KAAK,WAAW,CAAC;AACrH;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,kBAAkB,KAAK,aAAa,KAAK,KAAK,aAAa,SAAS,KAAK,WAAW,CAAC;AACzH;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,MAAM,SAAS,MAAM,EAAE;AAC9F;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK,aAAa,iBAAiB,KAAK,aAAa,MAAM,SAAS,KAAK,CAAC;AAC5F;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAKH,aAAK,uBAAuB;AAC5B;AAAA,MACF;AAEE;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,YAAY,eAAe,KAAK,UAAU,GAAG;AACjE,WAAK,iBAAiB,KAAK,KAAK,UAAU;AAC1C,WAAK,iCAAiC;AAAA,IACxC;AAEA,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,yBAAyB,OAAO;AAC9B,QAAI,MAAM,YAAY,SAAS,MAAM,YAAY,OAAO;AACtD,UAAI,KAAK,sBAAsB;AAC7B,aAAK,eAAe;AAAA,UAClB,OAAO,KAAK,aAAa,SAAS,KAAK,WAAW;AAAA,UAClD;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,kBAAkB,KAAK,QAAQ;AACpC,SAAK,cAAc,KAAK,uBAAuB,KAAK,aAAa,MAAM,CAAC;AACxE,SAAK,aAAa,KAAK,aAAa,YAAY,KAAK,UAAU;AAC/D,QAAI,aAAa,KAAK,aAAa,cAAc,OAAO;AAExD,SAAK,UAAU,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,EAAE,IAAI,SAAO,IAAI,IAAI,WAAS,KAAK,oBAAoB,OAAO,WAAW,KAAK,CAAC,CAAC,CAAC;AAC3I,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,iBAAiB,iBAAiB;AAAA,EACzC;AAAA;AAAA,EAEA,mCAAmC;AACjC,SAAK,iBAAiB,yCAAyC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,MAAM;AAC3B,WAAO,QAAQ,KAAK,aAAa,QAAQ,IAAI,KAAK,KAAK,aAAa,QAAQ,KAAK,UAAU,IAAI,KAAK,aAAa,SAAS,IAAI,IAAI;AAAA,EACpI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,OAAO;AACvB,UAAM,iBAAiB,KAAK,aAAa,WAAW,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,OAAO,CAAC;AACxG,UAAM,cAAc,KAAK,aAAa,kBAAkB,cAAc;AACtE,WAAO,KAAK,aAAa,WAAW,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,OAAO,KAAK,IAAI,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,WAAW,CAAC;AAAA,EAC1J;AAAA;AAAA,EAEA,oBAAoB,OAAO,WAAW;AACpC,UAAM,OAAO,KAAK,aAAa,WAAW,KAAK,aAAa,QAAQ,KAAK,UAAU,GAAG,OAAO,CAAC;AAC9F,UAAM,YAAY,KAAK,aAAa,OAAO,MAAM,KAAK,aAAa,QAAQ,kBAAkB;AAC7F,UAAM,cAAc,KAAK,YAAY,KAAK,UAAU,MAAM,MAAM,IAAI;AACpE,WAAO,IAAI,gBAAgB,OAAO,UAAU,kBAAkB,GAAG,WAAW,KAAK,mBAAmB,KAAK,GAAG,WAAW;AAAA,EACzH;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,UAAM,aAAa,KAAK,aAAa,QAAQ,KAAK,UAAU;AAC5D,QAAI,UAAU,UAAa,UAAU,QAAQ,KAAK,4BAA4B,YAAY,KAAK,KAAK,KAAK,6BAA6B,YAAY,KAAK,GAAG;AACxJ,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,aAAa,WAAW,YAAY,OAAO,CAAC;AAEtE,aAAS,OAAO,cAAc,KAAK,aAAa,SAAS,IAAI,KAAK,OAAO,OAAO,KAAK,aAAa,gBAAgB,MAAM,CAAC,GAAG;AAC1H,UAAI,KAAK,WAAW,IAAI,GAAG;AACzB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,4BAA4B,MAAM,OAAO;AACvC,QAAI,KAAK,SAAS;AAChB,YAAM,UAAU,KAAK,aAAa,QAAQ,KAAK,OAAO;AACtD,YAAM,WAAW,KAAK,aAAa,SAAS,KAAK,OAAO;AACxD,aAAO,OAAO,WAAW,SAAS,WAAW,QAAQ;AAAA,IACvD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B,MAAM,OAAO;AACxC,QAAI,KAAK,SAAS;AAChB,YAAM,UAAU,KAAK,aAAa,QAAQ,KAAK,OAAO;AACtD,YAAM,WAAW,KAAK,aAAa,SAAS,KAAK,OAAO;AACxD,aAAO,OAAO,WAAW,SAAS,WAAW,QAAQ;AAAA,IACvD;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,QAAQ,KAAK,KAAK,UAAU;AAAA,EAC1C;AAAA;AAAA,EAEA,kBAAkB,OAAO;AACvB,QAAI,iBAAiB,WAAW;AAC9B,WAAK,iBAAiB,KAAK,uBAAuB,MAAM,KAAK,KAAK,KAAK,uBAAuB,MAAM,GAAG;AAAA,IACzG,OAAO;AACL,WAAK,iBAAiB,KAAK,uBAAuB,KAAK;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,CAAC;AAAA,MACnC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,QAAQ,GAAG,oBAAoB,GAAG,CAAC,eAAe,QAAQ,GAAG,2BAA2B,GAAG,CAAC,WAAW,KAAK,GAAG,mCAAmC,GAAG,CAAC,qBAAqB,IAAI,GAAG,uBAAuB,oBAAoB,SAAS,WAAW,SAAS,QAAQ,cAAc,cAAc,YAAY,yBAAyB,WAAW,mBAAmB,YAAY,CAAC;AAAA,IAChY,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,IAAI;AACvD,QAAG,UAAU,GAAG,MAAM,CAAC;AACvB,QAAG,aAAa,EAAE;AAClB,QAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,QAAG,WAAW,uBAAuB,SAAS,0DAA0D,QAAQ;AAC9G,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC,EAAE,oBAAoB,SAAS,uDAAuD,QAAQ;AAC7F,iBAAO,IAAI,kBAAkB,MAAM;AAAA,QACrC,CAAC,EAAE,SAAS,SAAS,4CAA4C,QAAQ;AACvE,iBAAO,IAAI,yBAAyB,MAAM;AAAA,QAC5C,CAAC,EAAE,WAAW,SAAS,8CAA8C,QAAQ;AAC3E,iBAAO,IAAI,2BAA2B,MAAM;AAAA,QAC9C,CAAC;AACD,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,SAAS,IAAI,UAAU,EAAE,QAAQ,IAAI,OAAO,EAAE,cAAc,IAAI,WAAW,EAAE,cAAc,IAAI,cAAc,EAAE,YAAY,IAAI,cAAc,EAAE,yBAAyB,CAAC,EAAE,WAAW,CAAC,EAAE,mBAAmB,IAAI,CAAC,EAAE,cAAc,IAAI,aAAa,SAAS,IAAI,UAAU,CAAC;AAAA,MAC1R;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe;AAAA,IAC9B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,eAAe;AAAA,MACzB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,QAAQ,OAAO,iBAAiB;AAAA,EAChC,WAAW,OAAO,WAAW;AAAA,EAC7B,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,kBAAkB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AACzD,UAAM,oBAAoB,OAAO,iBAAiB;AAClD,SAAK,SAAS,aAAa,UAAU,MAAM,kBAAkB,aAAa,CAAC;AAAA,EAC7E;AAAA;AAAA,EAEA,IAAI,mBAAmB;AACrB,QAAI,KAAK,SAAS,eAAe,SAAS;AACxC,aAAO,KAAK,aAAa,OAAO,KAAK,SAAS,YAAY,KAAK,aAAa,QAAQ,cAAc,EAAE,kBAAkB;AAAA,IACxH;AACA,QAAI,KAAK,SAAS,eAAe,QAAQ;AACvC,aAAO,KAAK,aAAa,YAAY,KAAK,SAAS,UAAU;AAAA,IAC/D;AACA,WAAO,KAAK,MAAM,gBAAgB,GAAG,KAAK,2BAA2B,CAAC;AAAA,EACxE;AAAA;AAAA,EAEA,IAAI,0BAA0B;AAC5B,QAAI,KAAK,SAAS,eAAe,SAAS;AACxC,aAAO,KAAK,aAAa,OAAO,KAAK,SAAS,YAAY,KAAK,aAAa,QAAQ,cAAc,EAAE,kBAAkB;AAAA,IACxH;AACA,QAAI,KAAK,SAAS,eAAe,QAAQ;AACvC,aAAO,KAAK,aAAa,YAAY,KAAK,SAAS,UAAU;AAAA,IAC/D;AAGA,WAAO,KAAK,MAAM,qBAAqB,GAAG,KAAK,2BAA2B,CAAC;AAAA,EAC7E;AAAA;AAAA,EAEA,IAAI,oBAAoB;AACtB,WAAO,KAAK,SAAS,eAAe,UAAU,KAAK,MAAM,6BAA6B,KAAK,MAAM;AAAA,EACnG;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO;AAAA,MACL,SAAS,KAAK,MAAM;AAAA,MACpB,QAAQ,KAAK,MAAM;AAAA,MACnB,cAAc,KAAK,MAAM;AAAA,IAC3B,EAAE,KAAK,SAAS,WAAW;AAAA,EAC7B;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO;AAAA,MACL,SAAS,KAAK,MAAM;AAAA,MACpB,QAAQ,KAAK,MAAM;AAAA,MACnB,cAAc,KAAK,MAAM;AAAA,IAC3B,EAAE,KAAK,SAAS,WAAW;AAAA,EAC7B;AAAA;AAAA,EAEA,uBAAuB;AACrB,SAAK,SAAS,cAAc,KAAK,SAAS,eAAe,UAAU,eAAe;AAAA,EACpF;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,SAAS,aAAa,KAAK,SAAS,eAAe,UAAU,KAAK,aAAa,kBAAkB,KAAK,SAAS,YAAY,EAAE,IAAI,KAAK,aAAa,iBAAiB,KAAK,SAAS,YAAY,KAAK,SAAS,eAAe,SAAS,KAAK,CAAC,YAAY;AAAA,EAC7P;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,SAAS,aAAa,KAAK,SAAS,eAAe,UAAU,KAAK,aAAa,kBAAkB,KAAK,SAAS,YAAY,CAAC,IAAI,KAAK,aAAa,iBAAiB,KAAK,SAAS,YAAY,KAAK,SAAS,eAAe,SAAS,IAAI,YAAY;AAAA,EAC1P;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,CAAC,KAAK,SAAS,SAAS;AAC1B,aAAO;AAAA,IACT;AACA,WAAO,CAAC,KAAK,SAAS,WAAW,CAAC,KAAK,YAAY,KAAK,SAAS,YAAY,KAAK,SAAS,OAAO;AAAA,EACpG;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,CAAC,KAAK,SAAS,WAAW,CAAC,KAAK,YAAY,KAAK,SAAS,YAAY,KAAK,SAAS,OAAO;AAAA,EACpG;AAAA;AAAA,EAEA,YAAY,OAAO,OAAO;AACxB,QAAI,KAAK,SAAS,eAAe,SAAS;AACxC,aAAO,KAAK,aAAa,QAAQ,KAAK,KAAK,KAAK,aAAa,QAAQ,KAAK,KAAK,KAAK,aAAa,SAAS,KAAK,KAAK,KAAK,aAAa,SAAS,KAAK;AAAA,IACtJ;AACA,QAAI,KAAK,SAAS,eAAe,QAAQ;AACvC,aAAO,KAAK,aAAa,QAAQ,KAAK,KAAK,KAAK,aAAa,QAAQ,KAAK;AAAA,IAC5E;AAEA,WAAO,oBAAoB,KAAK,cAAc,OAAO,OAAO,KAAK,SAAS,SAAS,KAAK,SAAS,OAAO;AAAA,EAC1G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B;AAI3B,UAAM,aAAa,KAAK,aAAa,QAAQ,KAAK,SAAS,UAAU;AACrE,UAAM,gBAAgB,aAAa,gBAAgB,KAAK,cAAc,KAAK,SAAS,YAAY,KAAK,SAAS,SAAS,KAAK,SAAS,OAAO;AAC5I,UAAM,gBAAgB,gBAAgB,eAAe;AACrD,UAAM,eAAe,KAAK,aAAa,YAAY,KAAK,aAAa,WAAW,eAAe,GAAG,CAAC,CAAC;AACpG,UAAM,eAAe,KAAK,aAAa,YAAY,KAAK,aAAa,WAAW,eAAe,GAAG,CAAC,CAAC;AACpG,WAAO,CAAC,cAAc,YAAY;AAAA,EACpC;AAAA,EACA,uBAAuB,OAAO,YAAY,EAAE,MAAM,4BAA4B;AAAA,EAC9E,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,IACnC,UAAU,CAAC,mBAAmB;AAAA,IAC9B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,aAAa,UAAU,GAAG,uBAAuB,GAAG,IAAI,GAAG,CAAC,cAAc,IAAI,QAAQ,UAAU,GAAG,8BAA8B,GAAG,OAAO,GAAG,CAAC,eAAe,MAAM,GAAG,CAAC,WAAW,YAAY,aAAa,SAAS,eAAe,QAAQ,GAAG,oBAAoB,GAAG,CAAC,UAAU,cAAc,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,gCAAgC,GAAG,SAAS,UAAU,GAAG,CAAC,WAAW,aAAa,aAAa,SAAS,eAAe,MAAM,GAAG,CAAC,KAAK,+CAA+C,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,4BAA4B,GAAG,SAAS,UAAU,GAAG,CAAC,KAAK,gDAAgD,CAAC;AAAA,IAChwB,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,QAAQ,CAAC;AACxD,QAAG,OAAO,CAAC;AACX,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,UAAU,CAAC;AAChC,QAAG,WAAW,SAAS,SAAS,qDAAqD;AACnF,iBAAO,IAAI,qBAAqB;AAAA,QAClC,CAAC;AACD,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa;AAChB,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,WAAW,CAAC;AAC5B,QAAG,aAAa,EAAE;AAClB,QAAG,gBAAgB;AACnB,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,aAAa,EAAE;AAClB,QAAG,eAAe,IAAI,UAAU,CAAC;AACjC,QAAG,WAAW,SAAS,SAAS,sDAAsD;AACpF,iBAAO,IAAI,gBAAgB;AAAA,QAC7B,CAAC;AACD,QAAG,eAAe;AAClB,QAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,QAAG,UAAU,IAAI,QAAQ,EAAE;AAC3B,QAAG,aAAa,EAAE;AAClB,QAAG,gBAAgB;AACnB,QAAG,eAAe,IAAI,UAAU,EAAE;AAClC,QAAG,WAAW,SAAS,SAAS,sDAAsD;AACpF,iBAAO,IAAI,YAAY;AAAA,QACzB,CAAC;AACD,QAAG,eAAe;AAClB,QAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,QAAG,UAAU,IAAI,QAAQ,EAAE;AAC3B,QAAG,aAAa,EAAE,EAAE,EAAE;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,MAAM,IAAI,oBAAoB;AAC5C,QAAG,UAAU;AACb,QAAG,kBAAkB,IAAI,uBAAuB;AAChD,QAAG,UAAU;AACb,QAAG,YAAY,cAAc,IAAI,iBAAiB,EAAE,oBAAoB,IAAI,oBAAoB;AAChG,QAAG,UAAU,CAAC;AACd,QAAG,kBAAkB,IAAI,gBAAgB;AACzC,QAAG,UAAU;AACb,QAAG,YAAY,uBAAuB,IAAI,SAAS,gBAAgB,OAAO;AAC1E,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,YAAY,CAAC,IAAI,gBAAgB,CAAC;AAChD,QAAG,YAAY,cAAc,IAAI,eAAe;AAChD,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,YAAY,CAAC,IAAI,YAAY,CAAC;AAC5C,QAAG,YAAY,cAAc,IAAI,eAAe;AAAA,MAClD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,aAAa;AAAA,IACvC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,WAAW,aAAa;AAAA,MAClC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,kBAAkB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB;AAAA;AAAA,EAEvB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,iBAAiB,WAAW;AAC9B,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,WAAK,YAAY,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,IAC5F;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,eAAe,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,gBAAgB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA,EAIjC,cAAc,IAAI,aAAa,IAAI;AAAA;AAAA,EAEnC,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,gBAAgB,IAAI,aAAa;AAAA;AAAA,EAEjC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,qBAAqB,KAAK,aAAa,UAAU,OAAO,KAAK,SAAS,KAAK,OAAO;AACvF,SAAK,aAAa,KAAK;AACvB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,UAAM,oBAAoB,KAAK,iBAAiB,QAAQ,QAAQ;AAChE,SAAK,eAAe;AACpB,SAAK,uBAAuB;AAC5B,SAAK,mBAAmB,aAAa;AACrC,QAAI,mBAAmB;AACrB,WAAK,YAAY,KAAK,iBAAiB;AAAA,IACzC;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA;AAAA;AAAA,EAId,eAAe,IAAI,QAAQ;AAAA,EAC3B,cAAc;AACZ,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,2BAA2B,aAAa;AAAA,MAChD;AACA,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,2BAA2B,kBAAkB;AAAA,MACrD;AAAA,IACF;AACA,SAAK,eAAe,OAAO,iBAAiB,EAAE,QAAQ,UAAU,MAAM;AACpE,WAAK,mBAAmB,aAAa;AACrC,WAAK,aAAa,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,wBAAwB,IAAI,gBAAgB,KAAK,mBAAmB,iBAAiB;AAC1F,SAAK,aAAa,KAAK,WAAW,KAAK,aAAa,MAAM;AAE1D,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,sBAAsB;AAC7B,WAAK,uBAAuB;AAC5B,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,YAAY;AAC9B,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA,EACA,YAAY,SAAS;AAInB,UAAM,gBAAgB,QAAQ,SAAS,KAAK,CAAC,KAAK,aAAa,SAAS,QAAQ,SAAS,EAAE,eAAe,QAAQ,SAAS,EAAE,YAAY,IAAI,QAAQ,SAAS,IAAI;AAClK,UAAM,gBAAgB,QAAQ,SAAS,KAAK,CAAC,KAAK,aAAa,SAAS,QAAQ,SAAS,EAAE,eAAe,QAAQ,SAAS,EAAE,YAAY,IAAI,QAAQ,SAAS,IAAI;AAClK,UAAM,0BAA0B,iBAAiB,iBAAiB,QAAQ,YAAY;AACtF,QAAI,2BAA2B,CAAC,wBAAwB,aAAa;AACnE,YAAM,OAAO,KAAK,yBAAyB;AAC3C,UAAI,MAAM;AAIR,YAAI,KAAK,YAAY,cAAc,SAAS,kCAAkC,CAAC,GAAG;AAChF,eAAK,uBAAuB;AAAA,QAC9B;AAGA,aAAK,mBAAmB,cAAc;AACtC,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AACA,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,yBAAyB,EAAE,iBAAiB,KAAK;AAAA,EACxD;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,yBAAyB,EAAE,MAAM;AAAA,EACxC;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,UAAM,OAAO,MAAM;AACnB,QAAI,KAAK,oBAAoB,aAAa,QAAQ,CAAC,KAAK,aAAa,SAAS,MAAM,KAAK,QAAQ,GAAG;AAClG,WAAK,eAAe,KAAK,IAAI;AAAA,IAC/B;AACA,SAAK,eAAe,KAAK,KAAK;AAAA,EAChC;AAAA;AAAA,EAEA,6BAA6B,gBAAgB;AAC3C,SAAK,aAAa,KAAK,cAAc;AAAA,EACvC;AAAA;AAAA,EAEA,yBAAyB,iBAAiB;AACxC,SAAK,cAAc,KAAK,eAAe;AAAA,EACzC;AAAA;AAAA,EAEA,gBAAgB,MAAM,MAAM;AAC1B,SAAK,aAAa;AAClB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,QAAI,CAAC,KAAK,YAAa;AACvB,QAAI,MAAM,OAAO;AACf,WAAK,cAAc,KAAK,KAAK;AAAA,IAC/B;AACA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,2BAA2B;AAIzB,WAAO,KAAK,aAAa,KAAK,YAAY,KAAK;AAAA,EACjD;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,cAAc,CAAC;AAC9B,QAAG,YAAY,aAAa,CAAC;AAC7B,QAAG,YAAY,kBAAkB,CAAC;AAAA,MACpC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAChE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,cAAc;AAAA,IAC7B,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB,CAAC,wCAAwC,CAAC,GAAM,oBAAoB;AAAA,IACrG,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,GAAG,CAAC,0BAA0B,IAAI,YAAY,MAAM,GAAG,sBAAsB,GAAG,CAAC,GAAG,cAAc,YAAY,cAAc,WAAW,WAAW,aAAa,mBAAmB,iBAAiB,2BAA2B,yBAAyB,YAAY,GAAG,CAAC,GAAG,cAAc,YAAY,cAAc,WAAW,WAAW,WAAW,GAAG,CAAC,GAAG,oBAAoB,kBAAkB,eAAe,aAAa,cAAc,YAAY,cAAc,WAAW,WAAW,aAAa,mBAAmB,iBAAiB,2BAA2B,yBAAyB,YAAY,GAAG,CAAC,GAAG,oBAAoB,iBAAiB,kBAAkB,cAAc,YAAY,cAAc,WAAW,WAAW,WAAW,GAAG,CAAC,GAAG,oBAAoB,gBAAgB,kBAAkB,cAAc,YAAY,cAAc,WAAW,WAAW,WAAW,CAAC;AAAA,IACv3B,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,oCAAoC,GAAG,GAAG,eAAe,CAAC;AAC3E,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,6BAA6B,GAAG,IAAI,kBAAkB,CAAC,EAAE,GAAG,6BAA6B,GAAG,GAAG,iBAAiB,CAAC,EAAE,GAAG,6BAA6B,GAAG,GAAG,uBAAuB,CAAC;AAClM,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,WAAW,mBAAmB,IAAI,qBAAqB;AAC1D,QAAG,UAAU,CAAC;AACd,QAAG,eAAe,UAAU,IAAI,iBAAiB,UAAU,IAAI,YAAY,SAAS,IAAI,YAAY,eAAe,IAAI,EAAE;AAAA,MAC3H;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAiB,iBAAiB,cAAc,aAAa,gBAAgB;AAAA,IAC5F,QAAQ,CAAC,q3EAAy3E;AAAA,IACl4E,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC,wCAAwC;AAAA,MACpD,SAAS,CAAC,iBAAiB,iBAAiB,cAAc,aAAa,gBAAgB;AAAA,MACvF,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,q3EAAy3E;AAAA,IACp4E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,iCAAiC,IAAI,eAAe,kCAAkC;AAAA,EAC1F,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,EACnD;AACF,CAAC;AAMD,SAAS,uCAAuC,SAAS;AACvD,SAAO,MAAM,QAAQ,iBAAiB,WAAW;AACnD;AAMA,IAAM,kDAAkD;AAAA,EACtD,SAAS;AAAA,EACT,MAAM,CAAC,OAAO;AAAA,EACd,YAAY;AACd;AAQA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc,OAAO,UAAU;AAAA,EAC/B,sBAAsB,OAAO,uBAAuB;AAAA,IAClD,UAAU;AAAA,EACZ,CAAC,MAAM;AAAA,EACP,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,eAAe,OAAO,qBAAqB;AAAA,EAC3C,eAAe,OAAO,WAAW;AAAA,EACjC,UAAU,OAAO,MAAM;AAAA,EACvB,0BAA0B,OAAO,mCAAmC;AAAA,IAClE,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB,IAAI,QAAQ;AAAA;AAAA,EAE7B,eAAe;AAAA;AAAA,EAEf;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB;AAAA,EACA,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AACzD,SAAK,mBAAmB,OAAO,iBAAiB,EAAE;AAClD,QAAI,CAAC,KAAK,qBAAqB;AAC7B,YAAM,UAAU,KAAK,YAAY;AACjC,YAAM,WAAW,OAAO,SAAS;AACjC,WAAK,iBAAiB,KAAK,QAAQ,kBAAkB,MAAM,CAAC,SAAS,OAAO,SAAS,kBAAkB,KAAK,qBAAqB,GAAG,SAAS,OAAO,SAAS,gBAAgB,KAAK,qBAAqB,GAAG,SAAS,OAAO,SAAS,mBAAmB,KAAK,qBAAqB,CAAC,CAAC;AAAA,IACpR;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,gBAAgB,KAAK,WAAW,aAAa,UAAU,MAAM;AAChE,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,SAAK,UAAU,gBAAgB;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,kBAAkB;AACpC,SAAK,gBAAgB,QAAQ,aAAW,QAAQ,CAAC;AACjD,SAAK,eAAe,YAAY;AAChC,SAAK,eAAe,SAAS;AAAA,EAC/B;AAAA,EACA,qBAAqB,OAAO;AAC1B,UAAM,YAAY,KAAK,OAAO;AAC9B,UAAM,QAAQ,MAAM;AACpB,UAAM,UAAU,qBAAqB;AAMrC,QAAI,WAAW,KAAK,yBAAyB;AAC3C,YAAM,eAAe,KAAK,wBAAwB,kBAAkB,OAAO,WAAW,MAAM,KAAK;AACjG,WAAK,OAAO,gBAAgB,cAAc,IAAI;AAAA,IAChD,WAAW,UAAU,WAAW,CAAC,KAAK,aAAa,SAAS,OAAO,SAAS,IAAI;AAC9E,WAAK,OAAO,IAAI,KAAK;AAAA,IACvB;AAEA,SAAK,CAAC,KAAK,UAAU,KAAK,OAAO,WAAW,MAAM,CAAC,KAAK,gBAAgB;AACtE,WAAK,WAAW,MAAM;AAAA,IACxB;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AACzB,SAAK,OAAO,gBAAgB,MAAM,OAAO,IAAI;AAAA,EAC/C;AAAA,EACA,sBAAsB;AACpB,SAAK,YAAY,cAAc,UAAU,IAAI,6BAA6B;AAC1E,QAAI,KAAK,qBAAqB;AAC5B,WAAK,eAAe,KAAK;AAAA,IAC3B,OAAO;AAIL,mBAAa,KAAK,kBAAkB;AACpC,WAAK,qBAAqB,WAAW,MAAM;AACzC,YAAI,CAAC,KAAK,cAAc;AACtB,eAAK,eAAe,KAAK;AAAA,QAC3B;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAAA,EACF;AAAA,EACA,wBAAwB,WAAS;AAC/B,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,MAAM,WAAW,WAAW,CAAC,MAAM,cAAc,WAAW,yBAAyB,GAAG;AAC1F;AAAA,IACF;AACA,iBAAa,KAAK,kBAAkB;AACpC,SAAK,eAAe,MAAM,SAAS;AACnC,YAAQ,UAAU,OAAO,oCAAoC,KAAK,YAAY;AAC9E,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,eAAe;AACb,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA,EAEA,yBAAyB;AACvB,QAAI,KAAK,WAAW,KAAK,cAAc;AACrC,WAAK,aAAa,gBAAgB,KAAK,OAAO,WAAW,IAAI;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,QAAQ,eAAe;AAIpC,SAAK,SAAS,SAAS,KAAK,aAAa,MAAM,IAAI,KAAK;AACxD,SAAK,iBAAiB;AACtB,QAAI,eAAe;AACjB,WAAK,mBAAmB,cAAc;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,wBAAwB,CAAC;AAAA,IACtC,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,aAAa,CAAC;AAAA,MAC/B;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,wBAAwB;AAAA,IACvC,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,YAAY,gCAAgC,IAAI,WAAW,OAAO,EAAE,6CAA6C,CAAC,IAAI,mBAAmB;AAAA,MAC9I;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC,sBAAsB;AAAA,IACjC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,gBAAgB,IAAI,QAAQ,UAAU,GAAG,kCAAkC,GAAG,CAAC,GAAG,gBAAgB,iBAAiB,eAAe,kBAAkB,iBAAiB,MAAM,WAAW,aAAa,WAAW,WAAW,cAAc,mBAAmB,YAAY,aAAa,mBAAmB,iBAAiB,2BAA2B,uBAAuB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,QAAQ,UAAU,qBAAqB,IAAI,GAAG,+BAA+B,GAAG,SAAS,QAAQ,SAAS,OAAO,CAAC;AAAA,IACjgB,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,gBAAgB,CAAC;AACnD,QAAG,WAAW,gBAAgB,SAAS,mEAAmE,QAAQ;AAChH,iBAAO,IAAI,WAAW,YAAY,MAAM;AAAA,QAC1C,CAAC,EAAE,iBAAiB,SAAS,oEAAoE,QAAQ;AACvG,iBAAO,IAAI,WAAW,aAAa,MAAM;AAAA,QAC3C,CAAC,EAAE,eAAe,SAAS,kEAAkE,QAAQ;AACnG,iBAAO,IAAI,WAAW,aAAa,MAAM;AAAA,QAC3C,CAAC,EAAE,kBAAkB,SAAS,qEAAqE,QAAQ;AACzG,iBAAO,IAAI,qBAAqB,MAAM;AAAA,QACxC,CAAC,EAAE,iBAAiB,SAAS,oEAAoE,QAAQ;AACvG,iBAAO,IAAI,oBAAoB,MAAM;AAAA,QACvC,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,eAAe,CAAC;AACpF,QAAG,eAAe,GAAG,UAAU,CAAC;AAChC,QAAG,WAAW,SAAS,SAAS,wDAAwD;AACtF,iBAAO,IAAI,sBAAsB;AAAA,QACnC,CAAC,EAAE,QAAQ,SAAS,uDAAuD;AACzE,iBAAO,IAAI,sBAAsB;AAAA,QACnC,CAAC,EAAE,SAAS,SAAS,wDAAwD;AAC3E,iBAAO,IAAI,WAAW,MAAM;AAAA,QAC9B,CAAC;AACD,QAAG,OAAO,CAAC;AACX,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,YAAY,uDAAuD,IAAI,WAAW,uBAAuB,EAAE,iDAAiD,IAAI,cAAc;AACjL,QAAG,YAAY,cAAc,IAAI,EAAE,oBAAoB,UAAU,IAAI,oBAAoB,QAAQ,YAAY,SAAY,UAAU,MAAS;AAC5I,QAAG,UAAU;AACb,QAAG,WAAW,IAAI,WAAW,UAAU;AACvC,QAAG,WAAW,MAAM,IAAI,WAAW,EAAE,EAAE,WAAW,IAAI,WAAW,OAAO,EAAE,aAAa,IAAI,WAAW,SAAS,EAAE,WAAW,IAAI,WAAW,YAAY,CAAC,EAAE,WAAW,IAAI,WAAW,YAAY,CAAC,EAAE,cAAc,IAAI,WAAW,eAAe,CAAC,EAAE,mBAAmB,IAAI,WAAW,uBAAuB,EAAE,YAAY,IAAI,aAAa,CAAC,EAAE,aAAa,IAAI,WAAW,SAAS,EAAE,mBAAmB,IAAI,eAAe,EAAE,iBAAiB,IAAI,aAAa,EAAE,2BAA2B,IAAI,uBAAuB,EAAE,yBAAyB,IAAI,qBAAqB;AAC1iB,QAAG,UAAU;AACb,QAAG,WAAW,mBAAmB,IAAI,cAAc;AACnD,QAAG,UAAU;AACb,QAAG,YAAY,uBAAuB,CAAC,IAAI,mBAAmB;AAC9D,QAAG,WAAW,SAAS,IAAI,SAAS,SAAS;AAC7C,QAAG,UAAU;AACb,QAAG,kBAAkB,IAAI,gBAAgB;AAAA,MAC3C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,aAAa,iBAAiB,SAAS;AAAA,IACpE,QAAQ,CAAC,ilFAAilF;AAAA,IAC1lF,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,WAAW;AAAA,QACX,wCAAwC;AAAA,QACxC,qDAAqD;AAAA,MACvD;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,cAAc,aAAa,iBAAiB,SAAS;AAAA,MAC/D,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,ilFAAilF;AAAA,IAC5lF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,WAAW,OAAO,OAAO;AAAA,EACzB,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS,OAAO,qBAAqB;AAAA,EACrC,kBAAkB,OAAO,8BAA8B;AAAA,EACvD,qBAAqB,aAAa;AAAA,EAClC,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B;AAAA;AAAA,EAEA,IAAI,UAAU;AAGZ,WAAO,KAAK,aAAa,KAAK,kBAAkB,KAAK,gBAAgB,cAAc,IAAI;AAAA,EACzF;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQZ,IAAI,QAAQ;AACV,WAAO,KAAK,WAAW,KAAK,kBAAkB,KAAK,gBAAgB,gBAAgB,IAAI;AAAA,EACzF;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAAA;AAAA,EAEV,IAAI,WAAW;AACb,WAAO,KAAK,cAAc,UAAa,KAAK,kBAAkB,KAAK,gBAAgB,WAAW,CAAC,CAAC,KAAK;AAAA,EACvG;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,WAAW;AAC5B,WAAK,YAAY;AACjB,WAAK,aAAa,KAAK,MAAS;AAAA,IAClC;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,eAAe,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,gBAAgB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA,EAIjC,cAAc,IAAI,aAAa,IAAI;AAAA;AAAA,EAEnC;AAAA;AAAA,EAEA,eAAe,IAAI,aAAa;AAAA;AAAA,EAEhC,eAAe,IAAI,aAAa;AAAA;AAAA,EAEhC,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc,kBAAkB,KAAK;AAAA,EAC5C;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,QAAI,OAAO;AACT,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,UAAU;AAAA;AAAA,EAEV,KAAK,OAAO,YAAY,EAAE,MAAM,iBAAiB;AAAA;AAAA,EAEjD,cAAc;AACZ,WAAO,KAAK,mBAAmB,KAAK,gBAAgB;AAAA,EACtD;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,mBAAmB,KAAK,gBAAgB;AAAA,EACtD;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,mBAAmB,KAAK,gBAAgB;AAAA,EACtD;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,4BAA4B;AAAA;AAAA,EAE5B,wBAAwB,GAAG,KAAK,EAAE;AAAA;AAAA,EAElC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,eAAe,IAAI,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA,EAC3B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc;AACZ,QAAI,CAAC,KAAK,iBAAiB,OAAO,cAAc,eAAe,YAAY;AACzE,YAAM,2BAA2B,aAAa;AAAA,IAChD;AACA,SAAK,OAAO,iBAAiB,UAAU,MAAM;AAC3C,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,iBAAiB,QAAQ,WAAW,KAAK,QAAQ,WAAW;AAClE,QAAI,kBAAkB,CAAC,eAAe,eAAe,KAAK,aAAa;AACrE,YAAM,mBAAmB,KAAK,YAAY,UAAU,EAAE;AACtD,UAAI,4BAA4B,mCAAmC;AACjE,aAAK,uBAAuB,gBAAgB;AAC5C,YAAI,KAAK,QAAQ;AACf,eAAK,YAAY,eAAe;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,SAAK,aAAa,KAAK,MAAS;AAAA,EAClC;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB;AACrB,SAAK,MAAM;AACX,SAAK,mBAAmB,YAAY;AACpC,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA;AAAA,EAEA,OAAO,MAAM;AACX,SAAK,OAAO,IAAI,IAAI;AAAA,EACtB;AAAA;AAAA,EAEA,YAAY,gBAAgB;AAC1B,SAAK,aAAa,KAAK,cAAc;AAAA,EACvC;AAAA;AAAA,EAEA,aAAa,iBAAiB;AAC5B,SAAK,cAAc,KAAK,eAAe;AAAA,EACzC;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,SAAK,YAAY,KAAK,IAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,OAAO;AACnB,QAAI,KAAK,oBAAoB,OAAO,cAAc,eAAe,YAAY;AAC3E,YAAM,MAAM,6DAA6D;AAAA,IAC3E;AACA,SAAK,mBAAmB,YAAY;AACpC,SAAK,kBAAkB;AACvB,SAAK,qBAAqB,MAAM,aAAa,UAAU,MAAM,KAAK,aAAa,KAAK,MAAS,CAAC;AAC9F,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,QAAQ;AACtB,QAAI,KAAK,mBAAmB,OAAO,cAAc,eAAe,YAAY;AAC1E,YAAM,MAAM,mEAAmE;AAAA,IACjF;AACA,SAAK,iBAAiB;AACtB,SAAK,eAAe,SAAS,eAAe,QAAQ,IAAI;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,QAAQ;AACpB,QAAI,WAAW,KAAK,gBAAgB;AAClC,WAAK,iBAAiB;AACtB,WAAK,eAAe,SAAS,eAAe,MAAM,IAAI;AAAA,IACxD;AAAA,EACF;AAAA;AAAA,EAEA,OAAO;AAGL,QAAI,KAAK,WAAW,KAAK,YAAY,KAAK,eAAe,SAAS,cAAc;AAC9E;AAAA,IACF;AACA,QAAI,CAAC,KAAK,oBAAoB,OAAO,cAAc,eAAe,YAAY;AAC5E,YAAM,MAAM,8DAA8D;AAAA,IAC5E;AACA,SAAK,4BAA4B,kCAAkC;AACnE,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,QAAQ;AAGN,QAAI,CAAC,KAAK,WAAW,KAAK,eAAe,SAAS,cAAc;AAC9D;AAAA,IACF;AACA,UAAM,kBAAkB,KAAK,gBAAgB,KAAK,6BAA6B,OAAO,KAAK,0BAA0B,UAAU;AAC/H,UAAM,gBAAgB,MAAM;AAG1B,UAAI,KAAK,SAAS;AAChB,aAAK,UAAU;AACf,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF;AACA,QAAI,KAAK,eAAe;AACtB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,eAAS,eAAe,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACpD,cAAM,gBAAgB,KAAK,UAAU;AAGrC,YAAI,oBAAoB,CAAC,iBAAiB,kBAAkB,KAAK,UAAU,iBAAiB,SAAS,cAAc,SAAS,aAAa,IAAI;AAC3I,eAAK,0BAA0B,MAAM;AAAA,QACvC;AACA,aAAK,4BAA4B;AACjC,aAAK,gBAAgB;AAAA,MACvB,CAAC;AACD,eAAS,oBAAoB;AAAA,IAC/B;AACA,QAAI,iBAAiB;AAMnB,iBAAW,aAAa;AAAA,IAC1B,OAAO;AACL,oBAAc;AAAA,IAChB;AAAA,EACF;AAAA;AAAA,EAEA,yBAAyB;AACvB,SAAK,eAAe,UAAU,uBAAuB;AAAA,EACvD;AAAA;AAAA,EAEA,sBAAsB,UAAU;AAC9B,aAAS,aAAa;AACtB,aAAS,QAAQ,KAAK;AACtB,aAAS,iBAAiB,KAAK,gBAAgB,kBAAkB;AACjE,aAAS,eAAe,KAAK,gBAAgB,KAAK;AAAA,EACpD;AAAA;AAAA,EAEA,eAAe;AACb,SAAK,gBAAgB;AACrB,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,IAAI,gBAAgB,sBAAsB,KAAK,iBAAiB;AAC/E,UAAM,aAAa,KAAK,cAAc,KAAK,SAAS,OAAO,IAAI,cAAc;AAAA,MAC3E,kBAAkB,WAAW,KAAK,mBAAmB,IAAI,KAAK,qBAAqB;AAAA,MACnF,aAAa;AAAA,MACb,eAAe,CAAC,WAAW,8BAA8B,oCAAoC,KAAK,qBAAqB;AAAA,MACvH,WAAW,KAAK,QAAQ;AAAA,MACxB,gBAAgB,WAAW,KAAK,SAAS,iBAAiB,MAAM,IAAI,KAAK,gBAAgB;AAAA,MACzF,YAAY,kBAAkB,WAAW,WAAW,OAAO;AAAA,IAC7D,CAAC,CAAC;AACF,SAAK,gBAAgB,UAAU,EAAE,UAAU,WAAS;AAClD,UAAI,OAAO;AACT,cAAM,eAAe;AAAA,MACvB;AACA,WAAK,MAAM;AAAA,IACb,CAAC;AAKD,eAAW,cAAc,EAAE,UAAU,WAAS;AAC5C,YAAM,UAAU,MAAM;AACtB,UAAI,YAAY,YAAY,YAAY,cAAc,YAAY,cAAc,YAAY,eAAe,YAAY,WAAW,YAAY,WAAW;AACvJ,cAAM,eAAe;AAAA,MACvB;AAAA,IACF,CAAC;AACD,SAAK,gBAAgB,WAAW,OAAO,MAAM;AAC7C,SAAK,sBAAsB,KAAK,cAAc,QAAQ;AAEtD,QAAI,CAAC,UAAU;AACb,sBAAgB,MAAM;AACpB,mBAAW,eAAe;AAAA,MAC5B,GAAG;AAAA,QACD,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,QAAQ;AACzB,WAAK,cAAc,KAAK,gBAAgB;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,SAAS,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE,iBAAiB;AAAA,EACjF;AAAA;AAAA,EAEA,uBAAuB;AACrB,UAAM,WAAW,KAAK,SAAS,SAAS,EAAE,oBAAoB,KAAK,gBAAgB,0BAA0B,CAAC,EAAE,sBAAsB,yBAAyB,EAAE,uBAAuB,KAAK,EAAE,mBAAmB,CAAC,EAAE,mBAAmB;AACxO,WAAO,KAAK,uBAAuB,QAAQ;AAAA,EAC7C;AAAA;AAAA,EAEA,uBAAuB,UAAU;AAC/B,UAAM,WAAW,KAAK,cAAc,QAAQ,QAAQ;AACpD,UAAM,aAAa,aAAa,UAAU,QAAQ;AAClD,UAAM,WAAW,KAAK,cAAc,UAAU,WAAW;AACzD,UAAM,aAAa,aAAa,QAAQ,WAAW;AACnD,WAAO,SAAS,cAAc,CAAC;AAAA,MAC7B,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,YAAY;AAC1B,UAAM,yBAAyB,CAAC,WAAW,YAAY,SAAS;AAChE,WAAO,MAAM,WAAW,cAAc,GAAG,WAAW,YAAY,GAAG,WAAW,cAAc,EAAE,KAAK,OAAO,WAAS;AAEjH,aAAO,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,KAAK,KAAK,mBAAmB,eAAe,OAAO,QAAQ,KAAK,MAAM,YAAY,YAAY,uBAAuB,MAAM,cAAY,CAAC,eAAe,OAAO,QAAQ,CAAC;AAAA,IACjO,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,yBAAyB;AAAA,MACzB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,OAAO;AAAA,MACP,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,MACnD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,IAClD;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,MACd,eAAe;AAAA,MACf,aAAa;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,IAChB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,uBAAsB,kBAAkB;AAAA,EAC5C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,UAAU,CAAC,eAAe;AAAA,IAC1B,UAAU,CAAI,mBAAmB,CAAC,0CAA0C;AAAA,MAC1E,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,uBAAuB,IAAI,KAAK;AAAA,IAAC;AAAA,IACpD,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,0CAA0C;AAAA,QACpD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,0BAAN,MAA8B;AAAA,EAC5B;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA,YACA,QACA,eAAe;AACb,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,QAAQ,KAAK,OAAO;AAAA,EAC3B;AACF;AAEA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc,OAAO,UAAU;AAAA,EAC/B,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,kBAAkB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,SAAS,KAAK,mBAAmB,KAAK,OAAO,SAAS,IAAI,KAAK;AAAA,EAC7E;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,6BAA6B,KAAK;AAAA,EACzC;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,CAAC,CAAC,KAAK,aAAa,KAAK,gBAAgB;AAAA,EAClD;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,UAAM,WAAW;AACjB,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,KAAK,cAAc,UAAU;AAC/B,WAAK,YAAY;AACjB,WAAK,aAAa,KAAK,MAAS;AAAA,IAClC;AAKA,QAAI,YAAY,KAAK,kBAAkB,QAAQ,MAAM;AAInD,cAAQ,KAAK;AAAA,IACf;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,aAAa,IAAI,aAAa;AAAA;AAAA,EAE9B,YAAY,IAAI,aAAa;AAAA;AAAA,EAE7B,eAAe,IAAI,QAAQ;AAAA,EAC3B,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,qBAAqB,MAAM;AAAA,EAAC;AAAA,EAC5B,eAAe,MAAM;AAAA,EAAC;AAAA,EACtB,4BAA4B,aAAa;AAAA,EACzC,sBAAsB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC;AAAA;AAAA,EAEA,kBAAkB,MAAM;AACtB,WAAO,KAAK,kBAAkB,OAAO;AAAA,MACnC,sBAAsB;AAAA,QACpB,QAAQ,KAAK,YAAY,cAAc;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,aAAW;AAC5B,UAAM,eAAe,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,QAAQ,KAAK,CAAC;AACtG,WAAO,CAAC,gBAAgB,KAAK,eAAe,YAAY,IAAI,OAAO;AAAA,MACjE,uBAAuB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,aAAW;AACzB,UAAM,eAAe,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,QAAQ,KAAK,CAAC;AACtG,UAAM,MAAM,KAAK,YAAY;AAC7B,WAAO,CAAC,OAAO,CAAC,gBAAgB,KAAK,aAAa,YAAY,KAAK,YAAY,KAAK,IAAI,OAAO;AAAA,MAC7F,oBAAoB;AAAA,QAClB,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,aAAW;AACzB,UAAM,eAAe,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,QAAQ,KAAK,CAAC;AACtG,UAAM,MAAM,KAAK,YAAY;AAC7B,WAAO,CAAC,OAAO,CAAC,gBAAgB,KAAK,aAAa,YAAY,KAAK,YAAY,KAAK,IAAI,OAAO;AAAA,MAC7F,oBAAoB;AAAA,QAClB,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,CAAC,KAAK,iBAAiB,KAAK,eAAe,KAAK,eAAe,KAAK,gBAAgB;AAAA,EAC7F;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,SAAK,SAAS;AACd,SAAK,0BAA0B,YAAY;AAC3C,QAAI,KAAK,eAAe;AACtB,WAAK,aAAa,KAAK,aAAa;AAAA,IACtC;AACA,SAAK,4BAA4B,KAAK,OAAO,iBAAiB,UAAU,WAAS;AAC/E,UAAI,KAAK,yBAAyB,KAAK,GAAG;AACxC,cAAM,QAAQ,KAAK,mBAAmB,MAAM,SAAS;AACrD,aAAK,kBAAkB,KAAK,cAAc,KAAK;AAC/C,aAAK,aAAa,KAAK;AACvB,aAAK,WAAW;AAChB,aAAK,aAAa,KAAK;AACvB,aAAK,UAAU,KAAK,IAAI,wBAAwB,MAAM,KAAK,YAAY,aAAa,CAAC;AACrF,aAAK,WAAW,KAAK,IAAI,wBAAwB,MAAM,KAAK,YAAY,aAAa,CAAC;AAAA,MACxF;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,kBAAkB;AAAA,EAClB,cAAc;AACZ,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,2BAA2B,aAAa;AAAA,MAChD;AACA,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,2BAA2B,kBAAkB;AAAA,MACrD;AAAA,IACF;AAEA,SAAK,sBAAsB,KAAK,aAAa,cAAc,UAAU,MAAM;AACzE,WAAK,6BAA6B,KAAK,KAAK;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,sBAAsB,SAAS,KAAK,YAAY,GAAG;AACrD,WAAK,aAAa,KAAK,MAAS;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,0BAA0B,YAAY;AAC3C,SAAK,oBAAoB,YAAY;AACrC,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA;AAAA,EAEA,0BAA0B,IAAI;AAC5B,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,SAAS,GAAG;AACV,WAAO,KAAK,aAAa,KAAK,WAAW,CAAC,IAAI;AAAA,EAChD;AAAA;AAAA,EAEA,WAAW,OAAO;AAChB,SAAK,6BAA6B,KAAK;AAAA,EACzC;AAAA;AAAA,EAEA,iBAAiB,IAAI;AACnB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA,EAEA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,yBAAyB,CAAC,WAAW,YAAY,SAAS;AAChE,UAAM,iBAAiB,eAAe,OAAO,QAAQ,KAAK,MAAM,YAAY,cAAc,uBAAuB,MAAM,cAAY,CAAC,eAAe,OAAO,QAAQ,CAAC;AACnK,QAAI,kBAAkB,CAAC,KAAK,YAAY,cAAc,UAAU;AAC9D,WAAK,WAAW;AAChB,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,SAAS,OAAO;AACd,UAAM,oBAAoB,KAAK;AAC/B,QAAI,OAAO,KAAK,aAAa,MAAM,OAAO,KAAK,aAAa,MAAM,SAAS;AAC3E,SAAK,kBAAkB,KAAK,cAAc,IAAI;AAC9C,WAAO,KAAK,aAAa,mBAAmB,IAAI;AAChD,UAAM,aAAa,CAAC,KAAK,aAAa,SAAS,MAAM,KAAK,KAAK;AAG/D,QAAI,CAAC,QAAQ,YAAY;AACvB,WAAK,aAAa,IAAI;AAAA,IACxB,OAAO;AAGL,UAAI,SAAS,CAAC,KAAK,OAAO;AACxB,aAAK,aAAa,IAAI;AAAA,MACxB;AACA,UAAI,sBAAsB,KAAK,iBAAiB;AAC9C,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,YAAY;AACd,WAAK,aAAa,IAAI;AACtB,WAAK,UAAU,KAAK,IAAI,wBAAwB,MAAM,KAAK,YAAY,aAAa,CAAC;AAAA,IACvF;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK,WAAW,KAAK,IAAI,wBAAwB,MAAM,KAAK,YAAY,aAAa,CAAC;AAAA,EACxF;AAAA;AAAA,EAEA,UAAU;AAER,QAAI,KAAK,OAAO;AACd,WAAK,aAAa,KAAK,KAAK;AAAA,IAC9B;AACA,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,SAAK,YAAY,cAAc,QAAQ,SAAS,OAAO,KAAK,aAAa,OAAO,OAAO,KAAK,aAAa,QAAQ,SAAS,IAAI;AAAA,EAChI;AAAA;AAAA,EAEA,aAAa,OAAO;AAGlB,QAAI,KAAK,QAAQ;AACf,WAAK,oBAAoB,KAAK;AAC9B,WAAK,gBAAgB;AAAA,IACvB,OAAO;AACL,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,WAAO,CAAC,SAAS,KAAK,aAAa,QAAQ,KAAK;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,6BAA6B,OAAO;AAClC,YAAQ,KAAK,aAAa,YAAY,KAAK;AAC3C,SAAK,kBAAkB,KAAK,cAAc,KAAK;AAC/C,YAAQ,KAAK,aAAa,mBAAmB,KAAK;AAClD,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,UAAMA,UAAS,KAAK,eAAe;AACnC,WAAO,CAACA,WAAUA,QAAO,KAAK;AAAA,EAChC;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,SAAS,sBAAsB,SAAS,SAAS;AAC/C,QAAM,OAAO,OAAO,KAAK,OAAO;AAChC,WAAS,OAAO,MAAM;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,QAAQ,GAAG;AACf,QAAI,QAAQ,eAAe,aAAa,KAAK,QAAQ,eAAe,YAAY,GAAG;AACjF,UAAI,CAAC,QAAQ,SAAS,eAAe,YAAY,GAAG;AAClD,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAM,gCAAgC;AAAA,EACpC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,kBAAkB;AAAA,EAChD,OAAO;AACT;AAEA,IAAM,4BAA4B;AAAA,EAChC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,kBAAkB;AAAA,EAChD,OAAO;AACT;AAEA,IAAM,qBAAN,MAAM,4BAA2B,uBAAuB;AAAA,EACtD,aAAa,OAAO,gBAAgB;AAAA,IAClC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,sBAAsB,aAAa;AAAA,EACnC,sBAAsB,aAAa;AAAA;AAAA,EAEnC,IAAI,cAAc,YAAY;AAC5B,QAAI,YAAY;AACd,WAAK,cAAc;AACnB,WAAK,UAAU,IAAI,WAAW,SAAS,WAAW,KAAK,IAAI;AAC3D,WAAK,sBAAsB,WAAW,aAAa,UAAU,MAAM;AACjE,aAAK,WAAW;AAChB,aAAK,UAAU,IAAI,IAAI;AAAA,MACzB,CAAC;AACD,WAAK,sBAAsB,WAAW,aAAa,UAAU,MAAM;AACjE,aAAK,UAAU,IAAI,WAAW,EAAE;AAAA,MAClC,CAAC;AACD,WAAK,eAAe,WAAW,cAAc,IAAI,CAAC;AAAA,IACpD;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,YAAY,OAAO,IAAI;AAAA;AAAA,EAEvB,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,OAAO;AACb,UAAM,aAAa,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAC5F,QAAI,CAAC,KAAK,aAAa,SAAS,YAAY,KAAK,IAAI,GAAG;AACtD,WAAK,OAAO;AACZ,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,OAAO;AACb,UAAM,aAAa,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAC5F,QAAI,CAAC,KAAK,aAAa,SAAS,YAAY,KAAK,IAAI,GAAG;AACtD,WAAK,OAAO;AACZ,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,UAAM,mBAAmB,KAAK,eAAe,KAAK,KAAK;AACvD,SAAK,cAAc;AACnB,QAAI,KAAK,eAAe,KAAK,KAAK,MAAM,kBAAkB;AACxD,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AACZ,UAAM;AACN,SAAK,aAAa,WAAW,QAAQ,MAAM,eAAe,CAAC;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,4BAA4B;AAC1B,WAAO,KAAK,aAAa,KAAK,WAAW,0BAA0B,IAAI,KAAK;AAAA,EAC9E;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK,WAAW,WAAW;AAAA,IACpC;AACA,WAAO,KAAK,YAAY,cAAc,aAAa,iBAAiB;AAAA,EACtE;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,aAAa,KAAK,WAAW,QAAQ;AAAA,EACnD;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,oBAAoB,YAAY;AACrC,SAAK,oBAAoB,YAAY;AAAA,EACvC;AAAA;AAAA,EAEA,aAAa;AACX,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,mBAAmB,YAAY;AAC7B,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,gBAAgB,OAAO,IAAI;AAAA,IACzC;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,yBAAyB,OAAO;AAC9B,WAAO,MAAM,WAAW;AAAA,EAC1B;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,iBAAiB,EAAE,CAAC;AAAA,IAC1C,WAAW,CAAC,GAAG,sBAAsB;AAAA,IACrC,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,4CAA4C,QAAQ;AAClF,iBAAO,IAAI,SAAS,OAAO,OAAO,KAAK;AAAA,QACzC,CAAC,EAAE,UAAU,SAAS,+CAA+C;AACnE,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC,EAAE,QAAQ,SAAS,6CAA6C;AAC/D,iBAAO,IAAI,QAAQ;AAAA,QACrB,CAAC,EAAE,WAAW,SAAS,8CAA8C,QAAQ;AAC3E,iBAAO,IAAI,WAAW,MAAM;AAAA,QAC9B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,YAAY,IAAI,QAAQ;AAC1C,QAAG,YAAY,iBAAiB,IAAI,cAAc,WAAW,IAAI,EAAE,aAAa,IAAI,UAAU,CAAC,EAAE,OAAO,IAAI,MAAM,IAAI,aAAa,UAAU,IAAI,GAAG,IAAI,IAAI,EAAE,OAAO,IAAI,MAAM,IAAI,aAAa,UAAU,IAAI,GAAG,IAAI,IAAI,EAAE,qBAAqB,IAAI,cAAc,IAAI,YAAY,KAAK,IAAI;AAAA,MAC7R;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,eAAe;AAAA,MACf,KAAK;AAAA,MACL,KAAK;AAAA,MACL,YAAY,CAAC,GAAG,uBAAuB,YAAY;AAAA,IACrD;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,mBAAmB,CAAC,+BAA+B,2BAA2B;AAAA,MAC1F,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,+BAA+B,2BAA2B;AAAA,QACpE,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,wBAAwB;AAAA,QACxB,oBAAoB;AAAA,QACpB,cAAc;AAAA,QACd,cAAc;AAAA;AAAA;AAAA,QAGd,4BAA4B;AAAA,QAC5B,cAAc;AAAA,QACd,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,EACjD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,QAAQ,OAAO,iBAAiB;AAAA,EAChC,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,gBAAgB,aAAa;AAAA;AAAA,EAE7B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,QAAI,KAAK,cAAc,UAAa,KAAK,YAAY;AACnD,aAAO,KAAK,WAAW;AAAA,IACzB;AACA,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AACZ,UAAM,kBAAkB,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MACjE,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,iBAAiB,OAAO,eAAe;AAC7C,SAAK,WAAW,kBAAkB,mBAAmB,IAAI,iBAAiB;AAAA,EAC5E;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,YAAY,GAAG;AACzB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,YAAY;AAAA,EACjC;AAAA,EACA,qBAAqB;AACnB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,MAAM,OAAO;AACX,QAAI,KAAK,cAAc,CAAC,KAAK,UAAU;AACrC,WAAK,WAAW,KAAK;AACrB,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,UAAM,yBAAyB,KAAK,aAAa,KAAK,WAAW,eAAe,GAAG;AACnF,UAAM,oBAAoB,KAAK,cAAc,KAAK,WAAW,kBAAkB,KAAK,WAAW,gBAAgB,eAAe,GAAG;AACjI,UAAM,oBAAoB,KAAK,aAAa,MAAM,KAAK,WAAW,cAAc,KAAK,WAAW,YAAY,IAAI,GAAG;AACnH,SAAK,cAAc,YAAY;AAC/B,SAAK,gBAAgB,MAAM,KAAK,MAAM,SAAS,wBAAwB,mBAAmB,iBAAiB,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAAA,EACrK;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,gBAAgB,SAAS,mCAAmC,IAAI,KAAK,UAAU;AAC7E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,yBAAyB,CAAC;AAAA,MACxD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,0BAA0B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,MAChE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,uBAAuB;AAAA,IACtC,UAAU;AAAA,IACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,6CAA6C,QAAQ;AACnF,iBAAO,IAAI,MAAM,MAAM;AAAA,QACzB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,YAAY,IAAI,EAAE,qBAAqB,IAAI,aAAa,IAAI,WAAW,KAAK,IAAI;AAC/F,QAAG,YAAY,gCAAgC,IAAI,cAAc,IAAI,WAAW,MAAM,EAAE,cAAc,IAAI,cAAc,IAAI,WAAW,UAAU,QAAQ,EAAE,YAAY,IAAI,cAAc,IAAI,WAAW,UAAU,MAAM;AAAA,MAC1N;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,OAAO,YAAY;AAAA,MACnC,UAAU;AAAA,MACV,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAC,qBAAqB;AAAA,IAChC,UAAU,CAAI,oBAAoB;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,YAAY,eAAe,GAAG,CAAC,WAAW,aAAa,SAAS,QAAQ,UAAU,QAAQ,QAAQ,gBAAgB,aAAa,SAAS,eAAe,QAAQ,GAAG,oCAAoC,GAAG,CAAC,KAAK,qIAAqI,CAAC;AAAA,IAC3Z,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,UAAU,GAAG,CAAC;AACnC,QAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,YAAY,CAAC;AAChF,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,YAAY,IAAI,QAAQ,EAAE,iBAAiB,IAAI,aAAa;AAC1E,QAAG,YAAY,iBAAiB,IAAI,aAAa,WAAW,IAAI,EAAE,cAAc,IAAI,aAAa,IAAI,MAAM,iBAAiB,EAAE,YAAY,IAAI,WAAW,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,aAAa,IAAI,WAAW,SAAS,IAAI;AAC1O,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,cAAc,IAAI,EAAE;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,QAAQ,CAAC,gVAAgV;AAAA,IACzV,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,mBAAmB;AAAA,QACnB,wCAAwC;AAAA,QACxC,sBAAsB;AAAA,QACtB,oBAAoB;AAAA;AAAA,QAEpB,4BAA4B;AAAA;AAAA;AAAA;AAAA,QAI5B,WAAW;AAAA,MACb;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,aAAa;AAAA,MACvB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,gVAAgV;AAAA,IAC3V,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,KAAK;AAAA,IACd,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,UAAU;AAAA,EAC/B,eAAe,OAAO,aAAa;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,aAAa,OAAO,gBAAgB;AAAA,IAClC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,sBAAsB,aAAa;AAAA,EACnC,sBAAsB,aAAa;AAAA,EACnC;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,SAAS,KAAK,OAAO,YAAY;AAAA,EAC/C;AAAA;AAAA,EAEA,KAAK,OAAO,YAAY,EAAE,MAAM,uBAAuB;AAAA;AAAA,EAEvD,UAAU;AAAA;AAAA,EAEV,IAAI,mBAAmB;AACrB,WAAO,KAAK,WAAW,CAAC,KAAK;AAAA,EAC/B;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,IAAI,cAAc;AAChB,UAAM,QAAQ,KAAK,aAAa,gBAAgB,KAAK;AACrD,UAAM,MAAM,KAAK,WAAW,gBAAgB,KAAK;AACjD,WAAO,SAAS,MAAM,GAAG,KAAK,IAAI,KAAK,SAAS,IAAI,GAAG,KAAK;AAAA,EAC9D;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,aAAa;AAC3B,QAAI,aAAa;AACf,WAAK,SAAS,YAAY,cAAc,IAAI;AAC5C,WAAK,eAAe;AACpB,WAAK,oBAAoB,YAAY;AACrC,WAAK,oBAAoB,YAAY;AACrC,WAAK,UAAU,IAAI,KAAK,YAAY,SAAS,YAAY,KAAK,IAAI;AAClE,WAAK,sBAAsB,YAAY,aAAa,UAAU,MAAM;AAClE,aAAK,aAAa,WAAW;AAC7B,aAAK,WAAW,WAAW;AAC3B,aAAK,UAAU,IAAI,IAAI;AAAA,MACzB,CAAC;AACD,WAAK,sBAAsB,YAAY,aAAa,UAAU,MAAM;AAClE,aAAK,UAAU,IAAI,YAAY,EAAE;AAAA,MACnC,CAAC;AACD,WAAK,eAAe,KAAK,MAAM;AAAA,IACjC;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,YAAY,OAAO,IAAI;AAAA;AAAA,EAEvB,IAAI,WAAW;AACb,WAAO,KAAK,cAAc,KAAK,kBAAkB,IAAI,KAAK,KAAK,kBAAkB,KAAK,WAAW,KAAK,KAAK,kBAAkB,KAAK,SAAS,MAAM;AAAA,EACnJ;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,UAAM,QAAQ,KAAK;AACnB,UAAM,MAAM,KAAK;AACjB,UAAM,mBAAmB,SAAS,MAAM,eAAe,MAAM,KAAK;AAClE,UAAM,iBAAiB,OAAO,IAAI,eAAe,MAAM,KAAK;AAC5D,SAAK,cAAc;AACnB,QAAI,SAAS,MAAM,eAAe,MAAM,KAAK,MAAM,kBAAkB;AACnE,YAAM,mBAAmB;AAAA,IAC3B;AACA,QAAI,OAAO,IAAI,eAAe,IAAI,KAAK,MAAM,gBAAgB;AAC3D,UAAI,mBAAmB;AAAA,IACzB;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,OAAO;AACb,UAAM,aAAa,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAC5F,QAAI,CAAC,KAAK,aAAa,SAAS,YAAY,KAAK,IAAI,GAAG;AACtD,WAAK,OAAO;AACZ,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,OAAO;AACb,UAAM,aAAa,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,KAAK,CAAC;AAC5F,QAAI,CAAC,KAAK,aAAa,SAAS,YAAY,KAAK,IAAI,GAAG;AACtD,WAAK,OAAO;AACZ,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,eAAe,KAAK,YAAY,KAAK,YAAY,YAAY,KAAK,UAAU,WAAW,KAAK;AAAA,EAC1G;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,gBAAgB;AACjC,WAAK,iBAAiB;AACtB,WAAK,aAAa,KAAK,MAAS;AAAA,IAClC;AAAA,EACF;AAAA,EACA,iBAAiB;AAAA;AAAA,EAEjB,IAAI,aAAa;AACf,QAAI,KAAK,eAAe,KAAK,WAAW;AACtC,aAAO,KAAK,YAAY,cAAc,KAAK,UAAU;AAAA,IACvD;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,UAAM,aAAa,KAAK,cAAc,KAAK,YAAY,QAAQ,IAAI;AACnE,UAAM,WAAW,KAAK,YAAY,KAAK,UAAU,QAAQ,IAAI;AAC7D,WAAO,cAAc;AAAA,EACvB;AAAA;AAAA,EAEA,mBAAmB;AAAA;AAAA,EAEnB;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,kBAAkB;AAAA;AAAA,EAElB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB;AAAA;AAAA,EAEA,eAAe,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,2BAA2B;AAAA,EAC3B,cAAc;AACZ,QAAI,CAAC,KAAK,iBAAiB,OAAO,cAAc,eAAe,YAAY;AACzE,YAAM,2BAA2B,aAAa;AAAA,IAChD;AAGA,QAAI,KAAK,YAAY,YAAY,cAAc,UAAU,SAAS,oBAAoB,GAAG;AACvF,WAAK,YAAY,cAAc,UAAU,IAAI,yBAAyB,oCAAoC,uBAAuB;AAAA,IACnI;AAEA,SAAK,YAAY,OAAO,kBAAkB;AAAA,MACxC,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAAK;AACrB,SAAK,mBAAmB,IAAI,SAAS,IAAI,KAAK,GAAG,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK,UAAU;AACnC,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,UAAU,OAAO;AAChD,aAAK,YAAY,MAAM;AAAA,MACzB,OAAO;AACL,aAAK,UAAU,MAAM;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,aAAa;AACrB,cAAM,MAAM,wDAAwD;AAAA,MACtE;AACA,UAAI,CAAC,KAAK,WAAW;AACnB,cAAM,MAAM,sDAAsD;AAAA,MACpE;AAAA,IACF;AACA,QAAI,KAAK,QAAQ;AACf,WAAK,eAAe,KAAK,MAAM;AAAA,IACjC;AAGA,UAAM,KAAK,YAAY,cAAc,KAAK,UAAU,YAAY,EAAE,UAAU,MAAM;AAChF,WAAK,aAAa,KAAK,MAAS;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,sBAAsB,SAAS,KAAK,YAAY,GAAG;AACrD,WAAK,aAAa,KAAK,MAAS;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,oBAAoB,YAAY;AACrC,SAAK,oBAAoB,YAAY;AACrC,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK,QAAQ,KAAK,MAAM,QAAQ;AAAA,EACzC;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,aAAa,KAAK,WAAW,QAAQ;AAAA,EACnD;AAAA;AAAA,EAEA,4BAA4B;AAC1B,WAAO,KAAK,aAAa,KAAK,WAAW,0BAA0B,IAAI,KAAK;AAAA,EAC9E;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,aAAa,KAAK,WAAW,WAAW,IAAI;AAAA,EAC1D;AAAA;AAAA,EAEA,qBAAqB,MAAM;AACzB,UAAM,QAAQ,SAAS,UAAU,KAAK,cAAc,KAAK;AACzD,WAAO,QAAQ,MAAM,eAAe,IAAI;AAAA,EAC1C;AAAA;AAAA,EAEA,0BAA0B;AACxB,WAAO,KAAK,cAAc,CAAC,KAAK,YAAY,QAAQ,IAAI;AAAA,EAC1D;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,aAAa,KAAK,MAAS;AAChC,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,uBAAuB;AACrB,YAAQ,CAAC,KAAK,cAAc,KAAK,WAAW,WAAW,KAAK,CAAC,KAAK,WAAW,kBAAkB,MAAM,KAAK;AAAA,EAC5G;AAAA;AAAA,EAEA,qBAAqB;AACnB,UAAM,YAAY,KAAK;AACvB,WAAO,aAAa,UAAU,kBAAkB,IAAI,UAAU,WAAW;AAAA,EAC3E;AAAA,EACA,8BAA8B;AAC5B,WAAO,KAAK,YAAY,mBAAmB;AAAA,EAC7C;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK,UAAU,mBAAmB;AAAA,EAC3C;AAAA;AAAA,EAEA,aAAa,QAAQ;AACnB,SAAK,UAAU,WAAW;AAC1B,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,mBAAmB;AAAA,IACtC;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,mBAAmB;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,eAAe,KAAK;AAAA,IACvC;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,eAAe,KAAK;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,QAAQ;AACxB,WAAO,QAAQ,WAAW,SAAS,aAAa,WAAW,QAAQ;AAAA,EACrE;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,WAAW,CAAC,QAAQ,SAAS,GAAG,sBAAsB;AAAA,IACtD,UAAU;AAAA,IACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,IAAI,EAAE,EAAE,mBAAmB,IAAI,mBAAmB,CAAC,EAAE,oBAAoB,IAAI,gBAAgB,EAAE,qBAAqB,IAAI,cAAc,IAAI,YAAY,KAAK,IAAI;AACpL,QAAG,YAAY,0CAA0C,IAAI,wBAAwB,CAAC,EAAE,iCAAiC,IAAI,QAAQ;AAAA,MACvI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,YAAY;AAAA,MACZ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,IAC9B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,IAC5B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,0BAA0B,IAAI,GAAG,kCAAkC,GAAG,gBAAgB,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,eAAe,QAAQ,GAAG,6BAA6B,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,gCAAgC,kCAAkC,CAAC;AAAA,IACjT,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,kBAAkB,SAAS,yDAAyD,QAAQ;AACxG,iBAAO,IAAI,aAAa,MAAM;AAAA,QAChC,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa,EAAE;AAClB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,EAAE;AACZ,QAAG,aAAa,EAAE,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,kBAAkB,IAAI,qBAAqB,OAAO,CAAC;AACtD,QAAG,UAAU;AACb,QAAG,YAAY,yCAAyC,IAAI,qBAAqB,CAAC;AAClF,QAAG,UAAU;AACb,QAAG,kBAAkB,IAAI,SAAS;AAClC,QAAG,UAAU,CAAC;AACd,QAAG,kBAAkB,IAAI,qBAAqB,KAAK,CAAC;AAAA,MACtD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe;AAAA,IAC9B,QAAQ,CAAC,yyIAAyyI;AAAA,IAClzI,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,kDAAkD;AAAA,QAClD,yCAAyC;AAAA,QACzC,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA;AAAA;AAAA,QAG3B,4BAA4B;AAAA,MAC9B;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,eAAe;AAAA,MACzB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,yyIAAyyI;AAAA,IACpzI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAkDH,SAAS,2BAA2B,SAAS;AAC3C,SAAO,mCAAmC,SAAS,IAAI;AACzD;AAKA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,KAAK,aAAa,KAAK;AAChC;AAKA,SAAS,0BAA0B,MAAM;AACvC,SAAO,KAAK,aAAa;AAC3B;AAKA,SAAS,6BAA6B,MAAM;AAC1C,SAAO,KAAK,aAAa;AAC3B;AAWA,SAAS,mCAAmC,aAAa,sBAAsB;AAO7E,MAAI,iBAAiB,WAAW,KAAK,sBAAsB;AACzD,UAAM,gBAAgB,YAAY,eAAe,iBAAiB,GAAG,MAAM,MAAM,KAAK,CAAC;AACvF,UAAM,cAAc,cAAc,OAAO,CAAC,UAAU,OAAO;AACzD,YAAM,OAAO,SAAS,eAAe,EAAE;AACvC,UAAI,MAAM;AACR,iBAAS,KAAK,IAAI;AAAA,MACpB;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,QAAI,YAAY,QAAQ;AACtB,aAAO,YAAY,IAAI,WAAS;AAC9B,eAAO,mCAAmC,OAAO,KAAK;AAAA,MACxD,CAAC,EAAE,KAAK,GAAG;AAAA,IACb;AAAA,EACF;AAEA,MAAI,iBAAiB,WAAW,GAAG;AACjC,UAAM,YAAY,YAAY,aAAa,YAAY,GAAG,KAAK;AAC/D,QAAI,WAAW;AACb,aAAO;AAAA,IACT;AAAA,EACF;AAMA,MAAI,0BAA0B,WAAW,KAAK,6BAA6B,WAAW,GAAG;AAEvF,QAAI,YAAY,QAAQ,QAAQ;AAC9B,aAAO,MAAM,KAAK,YAAY,MAAM,EAAE,IAAI,OAAK,mCAAmC,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG;AAAA,IACvG;AAEA,UAAM,cAAc,YAAY,aAAa,aAAa,GAAG,KAAK;AAClE,QAAI,aAAa;AACf,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,YAAY,aAAa,OAAO,GAAG,KAAK;AACtD,QAAI,OAAO;AACT,aAAO;AAAA,IACT;AAAA,EACF;AAcA,UAAQ,YAAY,eAAe,IAAI,QAAQ,QAAQ,GAAG,EAAE,KAAK;AACnE;AAKA,IAAM,4BAAN,MAAM,mCAAkC,uBAAuB;AAAA,EAC7D,cAAc,OAAO,iBAAiB;AAAA,EACtC,cAAc,OAAO,UAAU;AAAA,EAC/B,4BAA4B,OAAO,iBAAiB;AAAA,EACpD,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc,OAAO,QAAQ;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,mBAAmB,OAAO,oBAAoB;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD;AAAA,EACA,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA;AAAA,EAEA,IAAI,oBAAoB;AACtB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,kBAAkB,OAAO;AAC3B,SAAK,mBAAmB,UAAU;AAAA,EACpC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,UAAM;AACN,SAAK,qBAAqB,IAAI,mBAAmB,KAAK,2BAA2B,MAAM,KAAK,kBAAkB,KAAK,aAAa,KAAK,YAAY;AAAA,EACnJ;AAAA,EACA,WAAW;AAOT,UAAM,YAAY,KAAK,UAAU,IAAI,WAAW,MAAM;AAAA,MACpD,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AACD,QAAI,WAAW;AACb,WAAK,YAAY;AACjB,WAAK,mBAAmB,YAAY;AAAA,IACtC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY;AACV,QAAI,KAAK,WAAW;AAIlB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,WAAO,KAAK,YAAY,cAAc,MAAM,WAAW;AAAA,EACzD;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,YAAY,cAAc;AAAA,EACxC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,YAAY,cAAc,MAAM;AAAA,EACvC;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,QAAQ,QAAQ;AACtB,WAAO,MAAM,SAAS,IAAI,QAAQ,QAAQ;AAAA,EAC5C;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,mBAAmB,iBAAiB;AAAA,EAC3C;AAAA;AAAA,EAEA,SAAS,OAAO;AACd,UAAM,SAAS,KAAK;AACpB,SAAK,YAAY,wBAAwB;AAAA,EAC3C;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,YAAY,gBAAgB;AAAA,EACnC;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,yBAAyB;AAAA,IACvB;AAAA,EACF,GAAG;AACD,WAAO,WAAW,KAAK,YAAY,eAAe,WAAW,KAAK,YAAY;AAAA,EAChF;AAAA,EACA,6BAA6B,OAAO;AAClC,UAAM,6BAA6B,KAAK;AACxC,UAAM,WAAW,SAAS,KAAK,YAAY,cAAc,KAAK,YAAY,YAAY,KAAK,YAAY;AACvG,cAAU,mBAAmB;AAAA,EAC/B;AAAA,EACA,aAAa,OAAO;AAClB,UAAM,aAAa,KAAK;AAExB,SAAK,YAAY,wBAAwB;AAAA,EAC3C;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,2BAA2B,KAAK,YAAY,aAAa;AAAA,EAClE;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,IAAM,eAAN,MAAM,sBAAqB,0BAA0B;AAAA;AAAA,EAEnD,kBAAkB,aAAW;AAC3B,UAAM,QAAQ,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,QAAQ,KAAK,CAAC;AAC/F,UAAM,MAAM,KAAK,SAAS,KAAK,OAAO,UAAU,MAAM;AACtD,WAAO,CAAC,SAAS,CAAC,OAAO,KAAK,aAAa,YAAY,OAAO,GAAG,KAAK,IAAI,OAAO;AAAA,MAC/E,uBAAuB;AAAA,QACrB,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,WAAW,QAAQ,CAAC,GAAG,MAAM,eAAe,GAAG,KAAK,eAAe,CAAC;AAAA,EACjF,YAAY;AACV,SAAK,YAAY,cAAc;AAAA,EACjC;AAAA,EACA,mBAAmB,YAAY;AAC7B,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,yBAAyB,QAAQ;AAC/B,QAAI,CAAC,MAAM,yBAAyB,MAAM,GAAG;AAC3C,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC,OAAO,UAAU,QAAQ,CAAC,CAAC,OAAO,UAAU,QAAQ,CAAC,OAAO,UAAU,SAAS,CAAC,CAAC,KAAK,aAAa,YAAY,OAAO,SAAS,OAAO,OAAO,UAAU,KAAK;AAAA,IACtK;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,KAAK,QAAQ;AACf,YAAM,QAAQ,IAAI,UAAU,OAAO,KAAK,OAAO,UAAU,GAAG;AAC5D,WAAK,OAAO,gBAAgB,OAAO,IAAI;AACvC,WAAK,YAAY,wBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,QAAQ,KAAK,MAAM,UAAU;AAGnC,SAAK,MAAM,YAAY,eAAe,SAAS,MAAM,YAAY,cAAc,CAAC,UAAU,QAAQ,mBAAmB,QAAQ,MAAM,UAAU,QAAQ,iBAAiB,QAAQ,MAAM,QAAQ;AAC1L,YAAM,eAAe;AACrB,eAAS,YAAY,cAAc,kBAAkB,GAAG,CAAC;AACzD,eAAS,MAAM;AAAA,IACjB,OAAO;AACL,YAAM,WAAW,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,gBAAgB,EAAE,CAAC;AAAA,IACzC,WAAW,CAAC,QAAQ,QAAQ,GAAG,kBAAkB,4BAA4B;AAAA,IAC7E,UAAU;AAAA,IACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,sCAAsC,QAAQ;AAC5E,iBAAO,IAAI,SAAS,OAAO,OAAO,KAAK;AAAA,QACzC,CAAC,EAAE,UAAU,SAAS,yCAAyC;AAC7D,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC,EAAE,WAAW,SAAS,wCAAwC,QAAQ;AACrE,iBAAO,IAAI,WAAW,MAAM;AAAA,QAC9B,CAAC,EAAE,QAAQ,SAAS,uCAAuC;AACzD,iBAAO,IAAI,QAAQ;AAAA,QACrB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,YAAY,IAAI,QAAQ;AAC1C,QAAG,YAAY,iBAAiB,IAAI,YAAY,cAAc,WAAW,IAAI,EAAE,aAAa,IAAI,YAAY,YAAY,IAAI,YAAY,UAAU,KAAK,IAAI,YAAY,eAAe,OAAO,OAAO,IAAI,YAAY,YAAY,WAAW,IAAI,YAAY,YAAY,MAAM,IAAI,EAAE,OAAO,IAAI,YAAY,IAAI,IAAI,aAAa,UAAU,IAAI,YAAY,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI,YAAY,IAAI,IAAI,aAAa,UAAU,IAAI,YAAY,CAAC,IAAI,IAAI;AAAA,MACrb;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,cAAc;AAAA,QACd,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,wBAAwB;AAAA,QACxB,oBAAoB;AAAA;AAAA;AAAA,QAGpB,cAAc;AAAA,QACd,cAAc;AAAA,QACd,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,CAAC;AAAA;AAAA;AAAA,MAGD,SAAS,CAAC,cAAc,WAAW;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,aAAN,MAAM,oBAAmB,0BAA0B;AAAA;AAAA,EAEjD,gBAAgB,aAAW;AACzB,UAAM,MAAM,KAAK,aAAa,mBAAmB,KAAK,aAAa,YAAY,QAAQ,KAAK,CAAC;AAC7F,UAAM,QAAQ,KAAK,SAAS,KAAK,OAAO,UAAU,QAAQ;AAC1D,WAAO,CAAC,OAAO,CAAC,SAAS,KAAK,aAAa,YAAY,KAAK,KAAK,KAAK,IAAI,OAAO;AAAA,MAC/E,qBAAqB;AAAA,QACnB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK,YAAY,YAAY;AAAA,EAC/B;AAAA,EACA,aAAa,WAAW,QAAQ,CAAC,GAAG,MAAM,eAAe,GAAG,KAAK,aAAa,CAAC;AAAA,EAC/E,mBAAmB,YAAY;AAC7B,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,yBAAyB,QAAQ;AAC/B,QAAI,CAAC,MAAM,yBAAyB,MAAM,GAAG;AAC3C,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC,OAAO,UAAU,MAAM,CAAC,CAAC,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU,OAAO,CAAC,CAAC,KAAK,aAAa,YAAY,OAAO,SAAS,KAAK,OAAO,UAAU,GAAG;AAAA,IAC5J;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,KAAK,QAAQ;AACf,YAAM,QAAQ,IAAI,UAAU,KAAK,OAAO,UAAU,OAAO,KAAK;AAC9D,WAAK,OAAO,gBAAgB,OAAO,IAAI;AAAA,IACzC;AAAA,EACF;AAAA,EACA,8BAA8B;AAC5B,UAAM,aAAa,KAAK,YAAY,YAAY,YAAY;AAC5D,UAAM,QAAQ,WAAW;AACzB,QAAI,MAAM,SAAS,GAAG;AACpB,iBAAW,kBAAkB,MAAM,QAAQ,MAAM,MAAM;AAAA,IACzD;AACA,eAAW,MAAM;AAAA,EACnB;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,QAAQ,KAAK,MAAM,UAAU;AAEnC,QAAI,MAAM,YAAY,aAAa,CAAC,QAAQ,OAAO;AACjD,WAAK,4BAA4B;AAAA,IACnC,YAGU,MAAM,YAAY,cAAc,SAAS,MAAM,YAAY,eAAe,CAAC,UAAU,QAAQ,mBAAmB,KAAK,QAAQ,iBAAiB,GAAG;AACzJ,YAAM,eAAe;AACrB,WAAK,4BAA4B;AAAA,IACnC,OAAO;AACL,YAAM,WAAW,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,cAAc,EAAE,CAAC;AAAA,IACvC,WAAW,CAAC,QAAQ,QAAQ,GAAG,gBAAgB,4BAA4B;AAAA,IAC3E,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,oCAAoC,QAAQ;AAC1E,iBAAO,IAAI,SAAS,OAAO,OAAO,KAAK;AAAA,QACzC,CAAC,EAAE,UAAU,SAAS,uCAAuC;AAC3D,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC,EAAE,WAAW,SAAS,sCAAsC,QAAQ;AACnE,iBAAO,IAAI,WAAW,MAAM;AAAA,QAC9B,CAAC,EAAE,QAAQ,SAAS,qCAAqC;AACvD,iBAAO,IAAI,QAAQ;AAAA,QACrB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,YAAY,IAAI,QAAQ;AAC1C,QAAG,YAAY,iBAAiB,IAAI,YAAY,cAAc,WAAW,IAAI,EAAE,aAAa,IAAI,YAAY,YAAY,IAAI,YAAY,UAAU,KAAK,IAAI,YAAY,eAAe,OAAO,OAAO,IAAI,YAAY,YAAY,WAAW,IAAI,YAAY,YAAY,MAAM,IAAI,EAAE,OAAO,IAAI,YAAY,IAAI,IAAI,aAAa,UAAU,IAAI,YAAY,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI,YAAY,IAAI,IAAI,aAAa,UAAU,IAAI,YAAY,CAAC,IAAI,IAAI;AAAA,MACrb;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,cAAc;AAAA,QACd,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,wBAAwB;AAAA,QACxB,oBAAoB;AAAA;AAAA;AAAA,QAGpB,cAAc;AAAA,QACd,cAAc;AAAA,QACd,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,CAAC;AAAA;AAAA;AAAA,MAGD,SAAS,CAAC,cAAc,WAAW;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,qBAAN,MAAM,4BAA2B,kBAAkB;AAAA,EACjD,sBAAsB,UAAU;AAC9B,UAAM,sBAAsB,QAAQ;AACpC,UAAM,QAAQ,KAAK;AACnB,QAAI,OAAO;AACT,eAAS,kBAAkB,MAAM;AACjC,eAAS,gBAAgB,MAAM;AAC/B,eAAS,0BAA0B,MAAM,4BAA4B;AACrE,eAAS,wBAAwB,MAAM,0BAA0B;AAAA,IACnE;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,mBAAmB,CAAC,yCAAyC,sCAAsC;AAAA,MAC/G,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,4BAA4B,IAAI,KAAK;AAAA,IAAC;AAAA,IACzD,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,yCAAyC,sCAAsC;AAAA,QACzF,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc,OAAO,iBAAiB;AAAA,EACtC,cAAc;AAAA,EAAC;AAAA,EACf,kBAAkB;AAChB,SAAK,YAAY,uBAAuB;AACxC,SAAK,YAAY,MAAM;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,GAAG,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,IAC/E,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,8CAA8C;AAC5E,iBAAO,IAAI,gBAAgB;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc,OAAO,iBAAiB;AAAA,EACtC,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,uBAAuB,EAAE,GAAG,CAAC,IAAI,4BAA4B,EAAE,CAAC;AAAA,IACjF,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,+CAA+C;AAC7E,iBAAO,IAAI,YAAY,MAAM;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc,OAAO,iBAAiB;AAAA,EACtC,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,kBAAkB;AAChB,SAAK,UAAU,IAAI,eAAe,KAAK,WAAW,KAAK,iBAAiB;AACxE,SAAK,YAAY,gBAAgB,KAAK,OAAO;AAAA,EAC/C;AAAA,EACA,cAAc;AACZ,SAAK,YAAY,cAAc,KAAK,OAAO;AAE3C,QAAI,KAAK,WAAW,KAAK,QAAQ,YAAY;AAC3C,WAAK,SAAS,OAAO;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,wBAAwB,GAAG,CAAC,+BAA+B,CAAC;AAAA,IACzE,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,aAAa,CAAC;AAAA,MAC/B;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,MAClE;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,CAAC;AAAA,IACtC,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,aAAa;AAAA,MACnF;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,wSAAwS;AAAA,IACjT,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,QAAQ,CAAC,wSAAwS;AAAA,IACnT,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,eAAe,YAAY,cAAc,iBAAiB,aAAa,iBAAiB,eAAe,sBAAsB,oBAAoB,qBAAqB,yBAAyB,cAAc,aAAa,kBAAkB,mBAAmB,mBAAmB,cAAc,YAAY,oBAAoB,sBAAsB,qBAAqB,kBAAkB;AAAA,IACxZ,SAAS,CAAC,qBAAqB,aAAa,iBAAiB,eAAe,sBAAsB,oBAAoB,qBAAqB,yBAAyB,cAAc,aAAa,kBAAkB,mBAAmB,mBAAmB,cAAc,YAAY,oBAAoB,sBAAsB,qBAAqB,kBAAkB;AAAA,EACpW,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,mBAAmB,+CAA+C;AAAA,IAC9E,SAAS,CAAC,iBAAiB,eAAe,YAAY,cAAc,iBAAiB,sBAAsB,qBAAqB,mBAAmB,mBAAmB;AAAA,EACxK,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,eAAe,YAAY,cAAc,iBAAiB,aAAa,iBAAiB,eAAe,sBAAsB,oBAAoB,qBAAqB,yBAAyB,cAAc,aAAa,kBAAkB,mBAAmB,mBAAmB,cAAc,YAAY,oBAAoB,sBAAsB,qBAAqB,kBAAkB;AAAA,MACxZ,SAAS,CAAC,qBAAqB,aAAa,iBAAiB,eAAe,sBAAsB,oBAAoB,qBAAqB,yBAAyB,cAAc,aAAa,kBAAkB,mBAAmB,mBAAmB,cAAc,YAAY,oBAAoB,sBAAsB,qBAAqB,kBAAkB;AAAA,MAClW,WAAW,CAAC,mBAAmB,+CAA+C;AAAA,IAChF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0B9B,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,OAAO,CAAC;AAAA,YACN,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,WAAW;AAAA,YACb;AAAA,YACA,QAAQ;AAAA,UACV,GAAG;AAAA,YACD,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,WAAW;AAAA,YACb;AAAA,YACA,QAAQ;AAAA,UACV,CAAC;AAAA,QACH;AAAA,QACA,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,OAAO,CAAC;AAAA,YACN,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,WAAW;AAAA,YACb;AAAA,YACA,QAAQ;AAAA,UACV,GAAG;AAAA,YACD,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,WAAW;AAAA,cACX,SAAS;AAAA,YACX;AAAA,YACA,QAAQ;AAAA,UACV,CAAC;AAAA,QACH;AAAA,QACA,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,SAAS;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,SAAS;AAAA,QACX;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,SAAS;AAAA,QACX;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": ["filter"]}