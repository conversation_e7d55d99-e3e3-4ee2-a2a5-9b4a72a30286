﻿using AutoMapper;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using DoorCompany.Service.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Implementations
{
    public class MainactionService : ResponseHandler, IMainActionRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<MainactionService> _logger;
   
        public MainactionService(IUnitOfWorkOfService unitOfWork, IMapper mapper,
                              ILogger<MainactionService> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;

        }
      
        public async Task<ApiResponse<List<BasicActionResponse>>> GetALLMainActionAsync()
        {
            try
            {
                var mainActions = await _unitOfWork.MainActions.GetTableNoTracking()
                    .Where(p => !p.IsDeleted)
                    .ToListAsync();

                if (mainActions.Count == 0)
                    return NotFound<List<BasicActionResponse>>("لا يوجد بيانات");

                var mainActionsDto = _mapper.Map<List<BasicActionResponse>>(mainActions);
                return Success(mainActionsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all mainActions");
                return BadRequest<List<BasicActionResponse>>("حدث خطأ أثناء جلب بيانات ");
            }
        }

        public async Task<ApiResponse<List<BasicActionResponse>>> GetMainActionByActionAsync(int actionParent)
        {
            try
            {
                var mainActions = await _unitOfWork.MainActions.GetTableNoTracking()
                    .Where(p => !p.IsDeleted && p.ParentActionId == actionParent)
                    .ToListAsync();

                if (mainActions.Count == 0)
                    return NotFound<List<BasicActionResponse>>("لا يوجد بيانات");

                var mainActionsDto = _mapper.Map<List<BasicActionResponse>>(mainActions);
                return Success(mainActionsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all mainActions");
                return BadRequest<List<BasicActionResponse>>("حدث خطأ أثناء جلب بيانات ");
            }
        }

        public async Task<ApiResponse<BasicActionResponse>> GetMainActionByIdAsync(int id)
        {
            try
            {
                var mainAction = await _unitOfWork.MainActions.GetTableNoTracking()
                    .Include(p => p.ActionTypes)
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (mainAction == null)
                    return NotFound<BasicActionResponse>("البيانات غير موجود");
           

                var mainActionsDto = _mapper.Map<BasicActionResponse>(mainAction);
                return Success(mainActionsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all mainActions");
                return BadRequest<BasicActionResponse>("حدث خطأ أثناء جلب بيانات ");
            }
        }
    }
}
