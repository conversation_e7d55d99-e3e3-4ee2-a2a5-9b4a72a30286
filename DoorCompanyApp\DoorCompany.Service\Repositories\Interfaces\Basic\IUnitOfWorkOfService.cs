﻿using DoorCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Interfaces.Basic
{
    public interface IUnitOfWorkOfService : IDisposable
    {

        // Company Management
        IGenericRepository<Company> Companies { get; }

        // Action Management
        IGenericRepository<ActionType> ActionTypes { get; }
        IGenericRepository<MainAction> MainActions { get; }


        // Partner Management
        IGenericRepository<Partner> Partners { get; }
        IGenericRepository<PartnerBand> PartnerBands { get; }
        IGenericRepository<PartnerTransation> PartnerTransations { get; }


        // User Management
        IGenericRepository<User> Users { get; }
        IGenericRepository<Role> Roles { get; }
        IGenericRepository<Permission> Permissions { get; }
        IGenericRepository<RolePermission> RolePermissions { get; }
        IGenericRepository<UserRole> UserRoles { get; }
        IGenericRepository<RefreshToken> RefreshTokens { get; }


        //ShareDistribution Management
        IGenericRepository<ShareDistribution> ShareDistributions {get;}
        IGenericRepository<ShareTransfer> ShareTransfers { get;}
        IGenericRepository<ShareHistory> ShareHistories { get;}

        IGenericRepository<FinancialTransaction> FinancialTransactions { get; }

        IGenericRepository<Product> Products { get; }
        IGenericRepository<ItemCategory> ItemCategories { get; }
        IGenericRepository<Unit> Units { get; }
        IGenericRepository<InventoryTransaction> InventoryTransactions { get; }
        IGenericRepository<Party> Parties { get; }
        IGenericRepository<Customer> Customers { get; }
        IGenericRepository<Supplier> Suppliers { get; }
        IGenericRepository<ProductionUnit> ProductionUnits { get; }
        IGenericRepository<InvoiceMaster> InvoiceMasters { get; }
        IGenericRepository<InvoiceItem> InvoiceItems { get; }
        IGenericRepository<ProductInventory> ProductInventorys { get; }

        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
