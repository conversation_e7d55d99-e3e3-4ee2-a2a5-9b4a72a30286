﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Data.Models
{
    /// <summary>
    /// توزيع الاسهم الاولي
    /// </summary>
    public class ShareDistribution : BaseEntity
    {
        public int PartnerId { get; set; }      
        public int SharesCount { get; set; }     
        public DateTime DistributionDate { get; set; }
      
        [Column(TypeName = "decimal(18,2)")]
        public decimal ShareValue { get; set; }
        public string? Description { get; set; }

        [ForeignKey(nameof(PartnerId))]
        public virtual Partner Partners { get; set; } = null!;
    }

    /// <summary>
    /// جدول حركات الاسهم
    /// </summary>
    public class ShareTransfer : BaseEntity
    {

        public DateTime TransfersDate { get; set; }
        public int BuyerId { get; set; }
        public int SellerId { get; set; }
        public int SharesCount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TransferAmount { get; set; }
        public string? Description { get; set; }
        public virtual Partner Buyer { get; set; } = null!;
        public virtual Partner Seller { get; set; } = null!;

    }

    /// <summary>
    /// جدول تاريخ الأسهم
    /// </summary>
    public class ShareHistory : BaseEntity
    {
        public int PartnerId { get; set; }
        public int SharesCount { get; set; }

        [Column(TypeName = "decimal(5,4)")]
        public decimal SharePercentage { get; set; }
        public DateTime RecordDate { get; set; }
        public ShareChangeType ChangeType { get; set; }     
        public string? Description { get; set; }       
        public int? TransactionId { get; set; }

        [ForeignKey(nameof(PartnerId))]
        public virtual Partner Partners { get; set; } = null!;

        [ForeignKey(nameof(TransactionId))]
        public virtual ShareTransfer ShareTransfers { get; set; } = null!;
    }

    public enum ShareChangeType
    {
        [Description("التخصيص الأولي")]
        InitialAllocation = 1,

        [Description("شراء")]
        Purchase = 2,

        [Description("بيع")]
        Sale = 3,
       
        [Description("تحويل")]
        Transfer = 4
    }
}
