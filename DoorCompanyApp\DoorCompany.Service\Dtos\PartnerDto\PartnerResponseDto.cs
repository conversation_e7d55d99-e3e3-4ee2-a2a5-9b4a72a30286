﻿using DoorCompany.Service.Dtos.BasicDto;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos.PartnerDto
{
    public class PartnerResponseDto : BaseNameDto
    {
        public decimal? InitialCapital { get; set; }
        public decimal CurrentCapital { get; set; }
        public decimal TotalInvestments { get; set; }
        public decimal TotalWithdrawals { get; set; }
        public decimal? SharePercentage { get; set; }
        public decimal? InitialShareValue { get; set; }
        public int InitialShareCount { get; set; }
        public int CurrentShare {  get; set; }
    }

    public class CreatePartnerDto : BaseNameCreateDto
    {

         [Column(TypeName = "decimal(18,2)")]
        public decimal? InitialCapital { get; set; } 
    }

    public class UpdatePartnerDto : BaseNameUpdateDto
    {

        [Column(TypeName = "decimal(18,2)")]
        public decimal? InitialCapital { get; set; }
    }

    public class PartnerSharesSummary
    {
        public int PartnerId { get; set; }
        public string PartnerName { get; set; } = string.Empty;
        public int TotalSharesBuyer { get; set; } =0;
        public int TotalSharesSeller { get; set; } = 0;
        public int NetShares { get; set; } = 0;
    }

    public class PartnerDataSummary
    {
        public int PartnerId { get; set; }
        public string PartnerName { get; set; } = string.Empty;       
        public decimal? InitialCapital { get; set; }
        public decimal TotalInvestments { get; set; }
        public decimal TotalWithdrawals { get; set; }
        public decimal CurrentCapital { get; set; }
        public decimal? InitialShareValue { get; set; }
        public int InitialShareCount { get; set; }
        public int TotalSharesBuyer { get; set; } = 0;
        public int TotalSharesSeller { get; set; } = 0;
        public decimal? SharePercentage { get; set; }
        public int NetShares { get; set; }=0;
       
    }
}
