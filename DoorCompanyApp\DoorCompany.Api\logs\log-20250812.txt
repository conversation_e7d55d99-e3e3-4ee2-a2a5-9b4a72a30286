2025-08-12 11:35:14.762 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 11:35:14.861 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 11:35:15.251 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:35:15.276 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:35:15.537 +03:00 [INF] Executed DbCommand (118ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 11:35:15.646 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 11:35:15.720 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 11:35:15.736 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:35:15.746 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:35:15.770 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:35:15.781 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:35:15.792 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:35:15.804 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:35:15.970 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 11:35:16.164 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 11:35:16.291 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 11:35:16.294 +03:00 [INF] Hosting environment: Development
2025-08-12 11:35:16.296 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 11:35:16.694 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 11:35:16.908 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 221.8649ms
2025-08-12 11:35:16.948 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 11:35:16.950 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 11:35:16.962 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 13.7128ms
2025-08-12 11:35:17.008 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 58.2572ms
2025-08-12 11:35:17.518 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 11:35:17.604 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 85.6508ms
2025-08-12 11:37:21.415 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 850
2025-08-12 11:37:21.425 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 11:37:21.500 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:37:22.825 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:37:22.857 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:37:23.154 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Parties] AS [p]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:37:23.170 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 11:37:23.192 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 328.28ms
2025-08-12 11:37:23.194 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:37:23.199 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 404 null application/json; charset=utf-8 1783.3391ms
2025-08-12 11:37:39.219 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 850
2025-08-12 11:37:39.225 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:37:39.227 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:37:39.229 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:37:39.324 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Parties] AS [p]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:37:39.328 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 11:37:39.330 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 98.6812ms
2025-08-12 11:37:39.333 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:37:39.334 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 404 null application/json; charset=utf-8 115.1475ms
2025-08-12 11:38:12.662 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 856
2025-08-12 11:38:12.670 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:38:12.675 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:38:12.677 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:38:12.707 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-08-12 11:38:12.834 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 154.3081ms
2025-08-12 11:38:12.837 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:38:12.838 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 400 null application/problem+json; charset=utf-8 175.86ms
2025-08-12 11:38:42.963 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 852
2025-08-12 11:38:42.972 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:38:42.974 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:38:42.975 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:38:42.978 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-08-12 11:38:42.980 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 3.3231ms
2025-08-12 11:38:42.982 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:38:42.984 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 400 null application/problem+json; charset=utf-8 20.973ms
2025-08-12 11:38:49.473 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 852
2025-08-12 11:38:49.477 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:38:49.478 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:38:49.479 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:38:49.483 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-08-12 11:38:49.485 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 3.1364ms
2025-08-12 11:38:49.487 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:38:49.488 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 400 null application/problem+json; charset=utf-8 14.2824ms
2025-08-12 11:38:55.028 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 850
2025-08-12 11:38:55.032 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:38:55.033 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:38:55.034 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:38:55.045 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Parties] AS [p]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:38:55.054 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 11:38:55.056 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 20.1111ms
2025-08-12 11:38:55.059 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:38:55.060 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 404 null application/json; charset=utf-8 31.9295ms
2025-08-12 11:39:39.247 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 850
2025-08-12 11:39:39.269 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:39:39.271 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:39:39.273 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:39:39.283 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Parties] AS [p]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:39:39.292 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 11:39:39.294 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 18.5376ms
2025-08-12 11:39:39.296 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:39:39.297 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 404 null application/json; charset=utf-8 49.9029ms
2025-08-12 11:39:53.865 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 850
2025-08-12 11:39:53.868 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:39:53.869 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:39:53.870 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:40:01.742 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Parties] AS [p]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:41:22.327 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 11:41:22.396 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 11:41:22.708 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:41:22.731 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:41:22.889 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 11:41:23.021 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 11:41:23.067 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 11:41:23.080 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:41:23.091 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:41:23.101 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:41:23.110 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:41:23.119 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:41:23.128 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:41:23.286 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 11:41:23.455 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 11:41:23.580 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 11:41:23.587 +03:00 [INF] Hosting environment: Development
2025-08-12 11:41:23.588 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 11:41:24.040 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 11:41:24.219 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 187.1305ms
2025-08-12 11:41:24.235 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 11:41:24.242 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.6696ms
2025-08-12 11:41:24.249 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 11:41:24.299 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 50.1736ms
2025-08-12 11:41:24.602 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 11:41:24.668 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 65.877ms
2025-08-12 11:42:19.904 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 851
2025-08-12 11:42:19.912 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 11:42:19.948 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:42:20.012 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:42:20.038 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:42:23.759 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:42:37.441 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:42:47.408 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 11:42:47.432 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 27387.6804ms
2025-08-12 11:42:47.435 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:42:47.438 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 404 null application/json; charset=utf-8 27534.4466ms
2025-08-12 11:43:00.199 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 851
2025-08-12 11:43:00.204 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:43:00.207 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:43:00.225 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:43:04.858 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:43:07.695 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:43:10.218 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__item_ProductId_0
2025-08-12 11:43:16.556 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 11:43:16.559 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 16331.2337ms
2025-08-12 11:43:16.561 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:43:16.562 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 400 null application/json; charset=utf-8 16363.8456ms
2025-08-12 11:43:32.595 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 851
2025-08-12 11:43:32.604 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:43:32.614 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:43:32.616 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:43:36.811 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:43:39.584 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:43:41.128 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__item_ProductId_0
2025-08-12 11:43:45.306 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__prefix_0_startswith='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[InvoiceNumber]
FROM [InvoiceMasters] AS [i]
WHERE [i].[InvoiceNumber] LIKE @__prefix_0_startswith ESCAPE N'\'
ORDER BY [i].[Id] DESC
2025-08-12 11:43:57.043 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 11:43:57.076 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 24458.9627ms
2025-08-12 11:43:57.079 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:43:57.080 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 200 null application/json; charset=utf-8 24486.1982ms
2025-08-12 11:46:30.116 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 11:46:30.188 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 11:46:30.493 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:46:30.520 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:46:30.675 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 11:46:30.773 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 11:46:30.813 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 11:46:30.825 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:46:30.833 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:46:30.841 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:46:30.849 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:46:30.857 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:46:30.865 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:46:31.026 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 11:46:31.205 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 11:46:31.332 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 11:46:31.335 +03:00 [INF] Hosting environment: Development
2025-08-12 11:46:31.337 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 11:46:31.462 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 11:46:31.676 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 222.9644ms
2025-08-12 11:46:31.696 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 11:46:31.702 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.572ms
2025-08-12 11:46:31.709 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 11:46:31.748 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.5499ms
2025-08-12 11:46:31.923 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 11:46:31.972 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 49.0575ms
2025-08-12 11:47:10.305 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 851
2025-08-12 11:47:10.327 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 11:47:10.371 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:47:10.443 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:47:10.471 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:47:13.073 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:47:17.061 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:47:18.724 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__item_ProductId_0
2025-08-12 11:47:26.475 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__prefix_0_startswith='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[InvoiceNumber]
FROM [InvoiceMasters] AS [i]
WHERE [i].[InvoiceNumber] LIKE @__prefix_0_startswith ESCAPE N'\'
ORDER BY [i].[Id] DESC
2025-08-12 11:48:58.998 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 11:48:59.046 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 108563.5952ms
2025-08-12 11:48:59.049 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:48:59.054 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 200 null application/json; charset=utf-8 108748.8872ms
2025-08-12 11:52:38.576 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 11:52:38.645 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 11:52:38.965 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:52:38.989 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:52:39.151 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 11:52:39.281 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 11:52:39.332 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 11:52:39.343 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:52:39.350 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:52:39.358 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:52:39.365 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:52:39.373 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:52:39.382 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 11:52:39.540 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 11:52:39.698 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 11:52:39.864 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 11:52:39.867 +03:00 [INF] Hosting environment: Development
2025-08-12 11:52:39.873 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 11:52:39.892 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 11:52:40.086 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 202.7603ms
2025-08-12 11:52:40.112 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 11:52:40.114 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 11:52:40.119 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.1778ms
2025-08-12 11:52:40.159 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 44.6322ms
2025-08-12 11:52:40.313 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 11:52:40.378 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 65.0468ms
2025-08-12 11:53:28.692 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 851
2025-08-12 11:53:28.702 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 11:53:28.741 +03:00 [INF] CORS policy execution successful.
2025-08-12 11:53:28.804 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:53:28.831 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 11:53:32.710 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:53:32.755 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 11:53:32.777 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__item_ProductId_0
2025-08-12 11:53:32.800 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__prefix_0_startswith='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[InvoiceNumber]
FROM [InvoiceMasters] AS [i]
WHERE [i].[InvoiceNumber] LIKE @__prefix_0_startswith ESCAPE N'\'
ORDER BY [i].[Id] DESC
2025-08-12 11:53:32.817 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 11:53:32.850 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 4013.5767ms
2025-08-12 11:53:32.853 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 11:53:32.857 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 200 null application/json; charset=utf-8 4164.6146ms
2025-08-12 12:00:23.807 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 12:00:23.876 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 12:00:24.176 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:00:24.201 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:00:24.355 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 12:00:24.455 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 12:00:24.494 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 12:00:24.512 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:00:24.519 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:00:24.528 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:00:24.536 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:00:24.548 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:00:24.567 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:00:24.724 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 12:00:24.877 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 12:00:25.012 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 12:00:25.014 +03:00 [INF] Hosting environment: Development
2025-08-12 12:00:25.015 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 12:00:25.093 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 12:00:25.324 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 241.0146ms
2025-08-12 12:00:25.349 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 12:00:25.353 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 12:00:25.358 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.5596ms
2025-08-12 12:00:25.396 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.0271ms
2025-08-12 12:00:25.579 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 12:00:25.644 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 65.799ms
2025-08-12 12:01:04.397 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 851
2025-08-12 12:01:04.405 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 12:01:04.443 +03:00 [INF] CORS policy execution successful.
2025-08-12 12:01:04.517 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:01:04.544 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 12:01:07.485 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:01:07.531 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:01:07.553 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__item_ProductId_0
2025-08-12 12:01:07.580 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__prefix_0_startswith='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[InvoiceNumber]
FROM [InvoiceMasters] AS [i]
WHERE [i].[InvoiceNumber] LIKE @__prefix_0_startswith ESCAPE N'\'
ORDER BY [i].[Id] DESC
2025-08-12 12:01:07.598 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 12:01:07.638 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 3088.9234ms
2025-08-12 12:01:07.640 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:01:07.644 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 200 null application/json; charset=utf-8 3246.899ms
2025-08-12 12:04:18.331 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 1268
2025-08-12 12:04:18.337 +03:00 [INF] CORS policy execution successful.
2025-08-12 12:04:18.344 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:04:18.346 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 12:04:22.407 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:04:24.620 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:04:25.375 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__item_ProductId_0
2025-08-12 12:04:26.709 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:04:27.438 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__item_ProductId_0
2025-08-12 12:04:28.333 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__prefix_0_startswith='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[InvoiceNumber]
FROM [InvoiceMasters] AS [i]
WHERE [i].[InvoiceNumber] LIKE @__prefix_0_startswith ESCAPE N'\'
ORDER BY [i].[Id] DESC
2025-08-12 12:04:29.628 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 12:04:29.631 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 11282.5824ms
2025-08-12 12:04:29.633 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:04:29.636 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 200 null application/json; charset=utf-8 11304.8406ms
2025-08-12 12:07:09.278 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 12:07:09.348 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 12:07:09.657 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:07:09.678 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:07:09.831 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 12:07:09.928 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 12:07:09.981 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 12:07:10.005 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:07:10.015 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:07:10.026 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:07:10.036 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:07:10.043 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:07:10.051 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:07:10.198 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 12:07:10.345 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 12:07:10.536 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 12:07:10.538 +03:00 [INF] Hosting environment: Development
2025-08-12 12:07:10.539 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 12:07:10.565 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 12:07:10.774 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 218.5426ms
2025-08-12 12:07:10.800 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 12:07:10.802 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 12:07:10.807 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.0653ms
2025-08-12 12:07:10.844 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.6611ms
2025-08-12 12:07:11.003 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 12:07:11.053 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 49.4772ms
2025-08-12 12:07:58.263 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 850
2025-08-12 12:07:58.272 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 12:07:58.311 +03:00 [INF] CORS policy execution successful.
2025-08-12 12:07:58.374 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:07:58.400 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 12:08:00.405 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:08:00.450 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:08:00.469 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__item_ProductId_0
2025-08-12 12:08:00.492 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__prefix_0_startswith='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[InvoiceNumber]
FROM [InvoiceMasters] AS [i]
WHERE [i].[InvoiceNumber] LIKE @__prefix_0_startswith ESCAPE N'\'
ORDER BY [i].[Id] DESC
2025-08-12 12:08:00.513 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 12:08:00.550 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 2143.9943ms
2025-08-12 12:08:00.553 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:08:00.556 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 200 null application/json; charset=utf-8 2295.199ms
2025-08-12 12:09:30.577 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 12:09:30.646 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 12:09:30.951 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:09:30.973 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:09:31.123 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 12:09:31.218 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 12:09:31.258 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 12:09:31.269 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:09:31.277 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:09:31.285 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:09:31.293 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:09:31.300 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:09:31.311 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 12:09:31.493 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 12:09:31.643 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 12:09:31.756 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 12:09:31.762 +03:00 [INF] Hosting environment: Development
2025-08-12 12:09:31.763 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 12:09:32.055 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 12:09:32.246 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 199.7907ms
2025-08-12 12:09:32.279 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 12:09:32.279 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 12:09:32.285 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.1141ms
2025-08-12 12:09:32.322 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.3147ms
2025-08-12 12:09:32.479 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 12:09:32.543 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 64.185ms
2025-08-12 12:12:02.763 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 848
2025-08-12 12:12:02.771 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 12:12:02.810 +03:00 [INF] CORS policy execution successful.
2025-08-12 12:12:02.873 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:12:02.900 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 12:12:03.238 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:12:03.287 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:12:03.298 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 12:12:03.321 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 414.317ms
2025-08-12 12:12:03.324 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:12:03.327 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 404 null application/json; charset=utf-8 564.2734ms
2025-08-12 12:12:36.867 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product/invoice - application/json 850
2025-08-12 12:12:36.878 +03:00 [INF] CORS policy execution successful.
2025-08-12 12:12:36.882 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:12:36.884 +03:00 [INF] Route matched with {action = "CreateInvoice", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateInvoiceAsync(DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-12 12:12:36.971 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_PartyId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Address], [p].[CreatedAt], [p].[CreatedBy], [p].[Email], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[Phone], [p].[TaxNumber], [p].[UpdatedAt], [p].[UpdatedBy], [c].[Id], [c].[CreatedAt], [c].[CreatedBy], [c].[CreditLimit], [c].[IsActive], [c].[IsDeleted], [c].[PartyId], [c].[SalesRepresentative], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Parties] AS [p]
LEFT JOIN [Customers] AS [c] ON [p].[Id] = [c].[PartyId]
WHERE [p].[Id] = @__createDto_PartyId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:12:36.983 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Description], [u].[IsActive], [u].[IsDeleted], [u].[Name], [u].[Symbol], [u].[UpdatedAt], [u].[UpdatedBy]
FROM [Products] AS [p]
INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
WHERE [p].[Id] = @__item_ProductId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 12:12:37.003 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__item_ProductId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__item_ProductId_0
2025-08-12 12:12:37.026 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__prefix_0_startswith='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[InvoiceNumber]
FROM [InvoiceMasters] AS [i]
WHERE [i].[InvoiceNumber] LIKE @__prefix_0_startswith ESCAPE N'\'
ORDER BY [i].[Id] DESC
2025-08-12 12:12:37.035 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.InvoiceResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 12:12:37.048 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api) in 161.7715ms
2025-08-12 12:12:37.051 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateInvoiceAsync (DoorCompany.Api)'
2025-08-12 12:12:37.052 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product/invoice - 200 null application/json; charset=utf-8 185.5961ms
2025-08-12 13:37:25.990 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 13:37:26.059 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 13:37:26.364 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 13:37:26.385 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 13:37:26.651 +03:00 [INF] Executed DbCommand (97ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 13:37:26.780 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 13:37:26.826 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 13:37:26.839 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 13:37:26.847 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 13:37:26.855 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 13:37:26.863 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 13:37:26.871 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 13:37:26.880 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 13:37:27.059 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 13:37:27.226 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 13:37:27.371 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 13:37:27.373 +03:00 [INF] Hosting environment: Development
2025-08-12 13:37:27.374 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 13:37:27.861 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 13:37:28.047 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 195.9789ms
2025-08-12 13:37:28.076 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 13:37:28.076 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 13:37:28.082 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.3736ms
2025-08-12 13:37:28.117 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.6079ms
2025-08-12 13:37:28.276 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 13:37:28.339 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 62.2965ms
2025-08-12 15:02:10.513 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 15:02:10.654 +03:00 [INF] Executed DbCommand (57ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 15:02:11.061 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 15:02:11.087 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 15:02:11.306 +03:00 [INF] Executed DbCommand (91ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 15:02:11.413 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 15:02:11.457 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 15:02:11.471 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 15:02:11.481 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 15:02:11.491 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 15:02:11.504 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 15:02:11.515 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 15:02:11.527 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 15:02:11.710 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 15:02:11.913 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 15:02:12.077 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 15:02:12.079 +03:00 [INF] Hosting environment: Development
2025-08-12 15:02:12.084 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 15:02:12.271 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 15:02:12.508 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 267.2285ms
2025-08-12 15:02:12.546 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 15:02:12.553 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.2486ms
2025-08-12 15:02:13.054 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 15:02:13.093 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 38.9696ms
2025-08-12 15:02:13.385 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 15:02:13.446 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 61.3585ms
2025-08-12 15:02:23.426 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 15:02:23.438 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 15:02:24.855 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 15:02:24.878 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 15:02:25.103 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 15:02:25.291 +03:00 [ERR] Failed executing DbCommand (45ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 2 AND [p].[PartnerId] IN (
    SELECT [p0].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p0]
)
GROUP BY [p].[PartnerId]
2025-08-12 15:02:25.400 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:6aa3d661-7c16-47a9-b509-c83444222320
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:6aa3d661-7c16-47a9-b509-c83444222320
Error Number:156,State:1,Class:15
2025-08-12 15:02:25.589 +03:00 [ERR] Error
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.GetAllPartnerDataSummer() in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 1101
ClientConnectionId:6aa3d661-7c16-47a9-b509-c83444222320
Error Number:156,State:1,Class:15
2025-08-12 15:02:25.637 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 15:02:25.668 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 783.0278ms
2025-08-12 15:02:25.670 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 15:02:25.674 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 400 null application/json; charset=utf-8 2248.3771ms
2025-08-12 15:02:50.228 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 15:02:50.255 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 15:02:50.257 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 15:02:55.335 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 15:02:58.704 +03:00 [ERR] Failed executing DbCommand (23ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 2 AND [p].[PartnerId] IN (
    SELECT [p0].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p0]
)
GROUP BY [p].[PartnerId]
2025-08-12 15:02:58.708 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:6aa3d661-7c16-47a9-b509-c83444222320
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:6aa3d661-7c16-47a9-b509-c83444222320
Error Number:156,State:1,Class:15
2025-08-12 17:10:33.470 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 17:10:33.545 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 17:10:33.972 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:10:33.994 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:10:34.161 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 17:10:34.254 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 17:10:34.295 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 17:10:34.315 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:10:34.324 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:10:34.333 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:10:34.342 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:10:34.351 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:10:34.361 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:10:34.518 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 17:10:34.676 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 17:10:34.807 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 17:10:34.809 +03:00 [INF] Hosting environment: Development
2025-08-12 17:10:34.810 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 17:10:34.941 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:10:35.143 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 210.1499ms
2025-08-12 17:10:35.176 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:10:35.182 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:10:35.190 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 14.007ms
2025-08-12 17:10:35.227 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 45.1285ms
2025-08-12 17:10:35.416 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:10:35.483 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 67.6066ms
2025-08-12 17:10:40.876 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:10:40.883 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 17:10:41.013 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:10:41.035 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:10:45.774 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:11:26.208 +03:00 [ERR] Failed executing DbCommand (23ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 2 AND [p].[PartnerId] IN (
    SELECT [p0].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p0]
)
GROUP BY [p].[PartnerId]
2025-08-12 17:11:26.236 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:95be27a9-eea9-4494-8486-b08d62de880f
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:95be27a9-eea9-4494-8486-b08d62de880f
Error Number:156,State:1,Class:15
2025-08-12 17:15:26.451 +03:00 [ERR] Error
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.GetAllPartnerDataSummer() in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 1101
ClientConnectionId:95be27a9-eea9-4494-8486-b08d62de880f
Error Number:156,State:1,Class:15
2025-08-12 17:15:26.507 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:15:26.536 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 285495.592ms
2025-08-12 17:15:26.539 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:15:26.544 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 400 null application/json; charset=utf-8 285667.7396ms
2025-08-12 17:15:48.151 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 17:15:48.221 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 17:15:48.541 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:15:48.561 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:15:48.712 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 17:15:48.811 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 17:15:48.855 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 17:15:48.875 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:15:48.888 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:15:48.905 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:15:48.921 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:15:48.934 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:15:48.947 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:15:49.115 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 17:15:49.284 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 17:15:49.395 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 17:15:49.404 +03:00 [INF] Hosting environment: Development
2025-08-12 17:15:49.405 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 17:15:49.753 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:15:49.959 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 215.2028ms
2025-08-12 17:15:49.984 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:15:49.988 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:15:49.992 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.4313ms
2025-08-12 17:15:50.036 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.428ms
2025-08-12 17:15:50.194 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:15:50.245 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 51.6677ms
2025-08-12 17:16:04.625 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:16:04.633 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 17:16:04.744 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:16:04.766 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:16:07.055 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:16:11.041 +03:00 [ERR] Failed executing DbCommand (6ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 2 AND [p].[PartnerId] IN (
    SELECT [p0].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p0]
)
GROUP BY [p].[PartnerId]
2025-08-12 17:16:11.071 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:006cd0f9-62bc-4ebd-acd6-b25716aeaae9
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:006cd0f9-62bc-4ebd-acd6-b25716aeaae9
Error Number:156,State:1,Class:15
2025-08-12 17:16:11.123 +03:00 [ERR] Error
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.GetAllPartnerDataSummer() in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 1101
ClientConnectionId:006cd0f9-62bc-4ebd-acd6-b25716aeaae9
Error Number:156,State:1,Class:15
2025-08-12 17:16:12.440 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:16:14.379 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 9604.8143ms
2025-08-12 17:16:14.381 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:16:14.386 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 400 null application/json; charset=utf-8 9760.6152ms
2025-08-12 17:16:21.658 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:16:21.685 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:16:21.691 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:16:27.027 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:16:46.404 +03:00 [ERR] Failed executing DbCommand (24ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 2 AND [p].[PartnerId] IN (
    SELECT [p0].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p0]
)
GROUP BY [p].[PartnerId]
2025-08-12 17:16:46.409 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:006cd0f9-62bc-4ebd-acd6-b25716aeaae9
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:006cd0f9-62bc-4ebd-acd6-b25716aeaae9
Error Number:156,State:1,Class:15
2025-08-12 17:19:45.853 +03:00 [ERR] Error
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.GetAllPartnerDataSummer() in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 1101
ClientConnectionId:006cd0f9-62bc-4ebd-acd6-b25716aeaae9
Error Number:156,State:1,Class:15
2025-08-12 17:19:49.059 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:19:49.062 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 207367.8713ms
2025-08-12 17:19:49.064 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:19:49.066 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 400 null application/json; charset=utf-8 207407.253ms
2025-08-12 17:19:57.607 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:19:57.617 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:19:57.619 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:20:03.235 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:20:13.595 +03:00 [ERR] Failed executing DbCommand (20ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 2 AND [p].[PartnerId] IN (
    SELECT [p0].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p0]
)
GROUP BY [p].[PartnerId]
2025-08-12 17:20:13.600 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:006cd0f9-62bc-4ebd-acd6-b25716aeaae9
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:006cd0f9-62bc-4ebd-acd6-b25716aeaae9
Error Number:156,State:1,Class:15
2025-08-12 17:21:40.461 +03:00 [ERR] Error
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.GetAllPartnerDataSummer() in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 1101
ClientConnectionId:006cd0f9-62bc-4ebd-acd6-b25716aeaae9
Error Number:156,State:1,Class:15
2025-08-12 17:21:40.532 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:21:40.534 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 102897.5673ms
2025-08-12 17:21:40.536 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:21:40.538 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 400 null application/json; charset=utf-8 102930.6652ms
2025-08-12 17:27:14.137 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 17:27:14.211 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 17:27:14.577 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:27:14.599 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:27:14.802 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 17:27:14.921 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 17:27:14.965 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 17:27:14.976 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:27:14.982 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:27:14.989 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:27:14.996 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:27:15.004 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:27:15.012 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:27:15.172 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 17:27:15.347 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 17:27:15.456 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 17:27:15.467 +03:00 [INF] Hosting environment: Development
2025-08-12 17:27:15.468 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 17:27:15.821 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:27:16.011 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 198.7245ms
2025-08-12 17:27:16.034 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:27:16.040 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:27:16.047 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 12.3804ms
2025-08-12 17:27:16.082 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.2562ms
2025-08-12 17:27:16.281 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:27:16.344 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 63.4274ms
2025-08-12 17:27:31.676 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:27:31.684 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 17:27:31.795 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:27:31.816 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:27:32.098 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:27:32.216 +03:00 [ERR] Failed executing DbCommand (7ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 2 AND [p].[PartnerId] IN (
    SELECT [p0].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p0]
)
GROUP BY [p].[PartnerId]
2025-08-12 17:27:32.249 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:56758b8c-e490-477f-817c-f8cdc5c11a0e
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:56758b8c-e490-477f-817c-f8cdc5c11a0e
Error Number:156,State:1,Class:15
2025-08-12 17:27:32.344 +03:00 [ERR] Error
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.GetAllPartnerDataSummer() in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 1101
ClientConnectionId:56758b8c-e490-477f-817c-f8cdc5c11a0e
Error Number:156,State:1,Class:15
2025-08-12 17:27:32.380 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:27:32.410 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 586.3033ms
2025-08-12 17:27:32.413 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:27:32.417 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 400 null application/json; charset=utf-8 741.0615ms
2025-08-12 17:27:50.718 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:27:50.739 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:27:50.742 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:27:55.053 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:27:57.457 +03:00 [ERR] Failed executing DbCommand (23ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 2 AND [p].[PartnerId] IN (
    SELECT [p0].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p0]
)
GROUP BY [p].[PartnerId]
2025-08-12 17:27:57.463 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:56758b8c-e490-477f-817c-f8cdc5c11a0e
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:56758b8c-e490-477f-817c-f8cdc5c11a0e
Error Number:156,State:1,Class:15
2025-08-12 17:28:05.579 +03:00 [ERR] Error
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.GetAllPartnerDataSummer() in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 1101
ClientConnectionId:56758b8c-e490-477f-817c-f8cdc5c11a0e
Error Number:156,State:1,Class:15
2025-08-12 17:28:05.592 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:28:05.595 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 14849.7892ms
2025-08-12 17:28:05.597 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:28:05.599 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 400 null application/json; charset=utf-8 14881.4003ms
2025-08-12 17:29:34.956 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 17:29:35.043 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 17:29:35.382 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:29:35.405 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:29:35.593 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 17:29:35.691 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 17:29:35.734 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 17:29:35.745 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:29:35.752 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:29:35.760 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:29:35.768 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:29:35.775 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:29:35.783 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:29:35.934 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 17:29:36.082 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 17:29:36.190 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 17:29:36.196 +03:00 [INF] Hosting environment: Development
2025-08-12 17:29:36.197 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 17:29:36.426 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:29:36.630 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 213.8458ms
2025-08-12 17:29:36.655 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:29:36.658 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:29:36.667 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 12.0074ms
2025-08-12 17:29:36.702 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.1387ms
2025-08-12 17:29:36.872 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:29:36.946 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 73.5093ms
2025-08-12 17:29:43.437 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:29:43.445 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 17:29:43.558 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:29:43.581 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:29:48.419 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:29:51.893 +03:00 [ERR] Failed executing DbCommand (25ms) [Parameters=[@__p_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
INNER JOIN OPENJSON(@__p_0) WITH ([value] int '$') AS [p0] ON [p].[PartnerId] = [p0].[value]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 2
GROUP BY [p].[PartnerId]
2025-08-12 17:29:51.925 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:968fdc70-113b-47d1-8f7a-a86d08c2f011
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:968fdc70-113b-47d1-8f7a-a86d08c2f011
Error Number:156,State:1,Class:15
2025-08-12 17:33:15.419 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 17:33:15.492 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 17:33:15.869 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:33:15.889 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:33:16.047 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 17:33:16.148 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 17:33:16.188 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 17:33:16.200 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:33:16.207 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:33:16.215 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:33:16.222 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:33:16.229 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:33:16.237 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:33:16.415 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 17:33:16.584 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 17:33:16.696 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 17:33:16.705 +03:00 [INF] Hosting environment: Development
2025-08-12 17:33:16.706 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 17:33:17.175 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:33:17.371 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 204.1178ms
2025-08-12 17:33:17.398 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:33:17.402 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:33:17.407 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.5814ms
2025-08-12 17:33:17.448 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 46.434ms
2025-08-12 17:33:17.622 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:33:17.666 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 43.9117ms
2025-08-12 17:33:26.446 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:33:26.456 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 17:33:26.574 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:33:26.595 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:33:30.380 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:33:32.793 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:33:41.293 +03:00 [ERR] Failed executing DbCommand (26ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[PartnerId], COALESCE(SUM([p].[Amount]), 0.0) AS [Total]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[ActionDetailId] = 3 AND [p].[PartnerId] IN (
    SELECT [p0].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p0]
)
GROUP BY [p].[PartnerId]
2025-08-12 17:33:41.324 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:d4be5a48-14d7-4d7b-a322-8f90fa659203
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:d4be5a48-14d7-4d7b-a322-8f90fa659203
Error Number:156,State:1,Class:15
2025-08-12 17:34:17.611 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 17:34:17.687 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 17:34:18.018 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:34:18.042 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:34:18.192 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 17:34:18.291 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 17:34:18.330 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 17:34:18.341 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:34:18.348 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:34:18.357 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:34:18.364 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:34:18.373 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:34:18.381 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:34:18.559 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 17:34:18.711 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 17:34:18.819 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 17:34:18.826 +03:00 [INF] Hosting environment: Development
2025-08-12 17:34:18.827 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 17:34:19.091 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:34:19.288 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 208.7252ms
2025-08-12 17:34:19.318 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:34:19.324 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:34:19.331 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 13.5208ms
2025-08-12 17:34:19.365 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.7552ms
2025-08-12 17:34:19.531 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:34:19.574 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 42.6495ms
2025-08-12 17:34:31.197 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:34:31.206 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 17:34:31.318 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:34:31.342 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:34:33.825 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:34:34.801 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:34:38.404 +03:00 [ERR] Failed executing DbCommand (28ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[BuyerId] AS [PartnerId], COALESCE(SUM([s].[SharesCount]), 0) AS [Total]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[BuyerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
GROUP BY [s].[BuyerId]
2025-08-12 17:34:38.438 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:9c08bbea-d2d0-40f3-aee7-9768d972ebc9
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:9c08bbea-d2d0-40f3-aee7-9768d972ebc9
Error Number:156,State:1,Class:15
2025-08-12 17:35:34.220 +03:00 [ERR] Error
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SplitQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.GetAllPartnerDataSummer()
ClientConnectionId:9c08bbea-d2d0-40f3-aee7-9768d972ebc9
Error Number:156,State:1,Class:15
2025-08-12 17:36:08.812 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 17:36:08.891 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 17:36:09.220 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:36:09.241 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:36:09.459 +03:00 [INF] Executed DbCommand (45ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 17:36:09.560 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 17:36:09.602 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 17:36:09.614 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:36:09.621 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:36:09.630 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:36:09.637 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:36:09.644 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:36:09.652 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:36:09.803 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 17:36:09.956 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 17:36:10.069 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 17:36:10.076 +03:00 [INF] Hosting environment: Development
2025-08-12 17:36:10.077 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 17:36:10.342 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:36:10.580 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 248.8039ms
2025-08-12 17:36:10.608 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:36:10.612 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:36:10.616 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.4375ms
2025-08-12 17:36:10.662 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 49.7907ms
2025-08-12 17:36:10.879 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:36:10.938 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 59.519ms
2025-08-12 17:36:18.062 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:36:18.070 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 17:36:18.171 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:36:18.193 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:36:25.336 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:36:26.366 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:36:28.294 +03:00 [ERR] Failed executing DbCommand (24ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[BuyerId] AS [PartnerId], COALESCE(SUM([s].[SharesCount]), 0) AS [Total]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[BuyerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
GROUP BY [s].[BuyerId]
2025-08-12 17:36:28.324 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:1536667a-50c5-41f8-b180-cd4cc2162a34
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:1536667a-50c5-41f8-b180-cd4cc2162a34
Error Number:156,State:1,Class:15
2025-08-12 17:37:59.068 +03:00 [ERR] Error
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.GetAllPartnerDataSummer()
ClientConnectionId:1536667a-50c5-41f8-b180-cd4cc2162a34
Error Number:156,State:1,Class:15
2025-08-12 17:37:59.115 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:37:59.144 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 100944.337ms
2025-08-12 17:37:59.147 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:37:59.153 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 400 null application/json; charset=utf-8 101091.1857ms
2025-08-12 17:38:00.085 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:38:00.102 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 17.0076ms
2025-08-12 17:38:00.127 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:38:00.127 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:38:00.131 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 3.8099ms
2025-08-12 17:38:00.148 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 20.0754ms
2025-08-12 17:38:00.277 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:38:00.344 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 66.6397ms
2025-08-12 17:38:21.063 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:38:21.084 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:38:21.091 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:38:26.555 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:38:33.044 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:38:45.862 +03:00 [ERR] Failed executing DbCommand (24ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[BuyerId] AS [PartnerId], COALESCE(SUM([s].[SharesCount]), 0) AS [Total]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[BuyerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
GROUP BY [s].[BuyerId]
2025-08-12 17:38:45.869 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:1536667a-50c5-41f8-b180-cd4cc2162a34
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:1536667a-50c5-41f8-b180-cd4cc2162a34
Error Number:156,State:1,Class:15
2025-08-12 17:46:35.106 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 17:46:35.195 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 17:46:35.553 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:46:35.576 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:46:35.744 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 17:46:35.877 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 17:46:35.916 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 17:46:35.928 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:46:35.934 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:46:35.944 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:46:35.951 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:46:35.960 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:46:35.969 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:46:36.137 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 17:46:36.294 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 17:46:36.402 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 17:46:36.409 +03:00 [INF] Hosting environment: Development
2025-08-12 17:46:36.410 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 17:46:36.703 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:46:36.890 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 195.6065ms
2025-08-12 17:46:36.916 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:46:36.923 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:46:36.926 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.0367ms
2025-08-12 17:46:36.963 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 40.5567ms
2025-08-12 17:46:37.134 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:46:37.195 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 60.4565ms
2025-08-12 17:46:51.943 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:46:51.960 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 17:46:52.078 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:46:52.101 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:46:57.221 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:46:59.205 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:47:01.935 +03:00 [ERR] Failed executing DbCommand (26ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[BuyerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
2025-08-12 17:47:01.967 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:c40f2e41-11e5-4efa-b7f4-826cfeb5fe4c
Error Number:156,State:1,Class:15
Microsoft.Data.SqlClient.SqlException (0x80131904): Incorrect syntax near the keyword 'WITH'.
Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:c40f2e41-11e5-4efa-b7f4-826cfeb5fe4c
Error Number:156,State:1,Class:15
2025-08-12 17:49:40.512 +03:00 [INF] Executed DbCommand (934ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
CREATE DATABASE [DoorDb];
2025-08-12 17:49:40.905 +03:00 [INF] Executed DbCommand (335ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
IF SERVERPROPERTY('EngineEdition') <> 5
BEGIN
    ALTER DATABASE [DoorDb] SET READ_COMMITTED_SNAPSHOT ON;
END;
2025-08-12 17:49:40.933 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 17:49:41.340 +03:00 [INF] Executed DbCommand (75ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ActionTypes] (
    [Id] int NOT NULL IDENTITY,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_ActionTypes] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.346 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Companies] (
    [Id] int NOT NULL IDENTITY,
    [CommercialRegister] nvarchar(max) NULL,
    [IndustrialRegister] nvarchar(max) NULL,
    [TaxRegister] nvarchar(max) NULL,
    [EstablishmentDate] datetime2 NULL,
    [TotalShares] int NOT NULL,
    [Code] nvarchar(max) NULL,
    [Symbol] nvarchar(max) NULL,
    [LogoPath] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Companies] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.354 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [FinancialTransactions] (
    [Id] int NOT NULL IDENTITY,
    [TransactionDate] datetime2 NOT NULL,
    [TransactionType] int NOT NULL,
    [AccountType] int NOT NULL,
    [Amount] decimal(18,2) NOT NULL,
    [ReferenceId] int NULL,
    [ReferenceType] int NOT NULL,
    [Description] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_FinancialTransactions] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.365 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ItemCategories] (
    [Id] int NOT NULL IDENTITY,
    [ParentCategoryId] int NULL,
    [Code] nvarchar(max) NULL,
    [Symbol] nvarchar(max) NULL,
    [CategoryTypeId] int NULL,
    [ImageUrl] nvarchar(max) NULL,
    [SortOrder] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_ItemCategories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ItemCategories_ItemCategories_ParentCategoryId] FOREIGN KEY ([ParentCategoryId]) REFERENCES [ItemCategories] ([Id])
);
2025-08-12 17:49:41.372 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Parties] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NULL,
    [Email] nvarchar(max) NULL,
    [Address] nvarchar(max) NULL,
    [TaxNumber] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Parties] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.379 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [PartnerBands] (
    [Id] int NOT NULL IDENTITY,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_PartnerBands] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.384 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Partners] (
    [Id] int NOT NULL IDENTITY,
    [InitialCapital] decimal(18,2) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Partners] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.416 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Permissions] (
    [Id] int NOT NULL IDENTITY,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Permissions] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.422 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.427 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Units] (
    [Id] int NOT NULL IDENTITY,
    [Symbol] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Units] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.437 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Users] (
    [Id] int NOT NULL IDENTITY,
    [UserName] nvarchar(max) NOT NULL,
    [FullName] nvarchar(max) NOT NULL,
    [Password] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NOT NULL,
    [ProfileImage] nvarchar(max) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY ([Id])
);
2025-08-12 17:49:41.445 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [MainActions] (
    [Id] int NOT NULL IDENTITY,
    [ParentActionId] int NULL,
    [ActionTypeId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_MainActions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_MainActions_ActionTypes_ActionTypeId] FOREIGN KEY ([ActionTypeId]) REFERENCES [ActionTypes] ([Id]),
    CONSTRAINT [FK_MainActions_MainActions_ParentActionId] FOREIGN KEY ([ParentActionId]) REFERENCES [MainActions] ([Id])
);
2025-08-12 17:49:41.454 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Customers] (
    [Id] int NOT NULL IDENTITY,
    [PartyId] int NOT NULL,
    [CreditLimit] decimal(18,2) NOT NULL,
    [SalesRepresentative] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Customers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Customers_Parties_PartyId] FOREIGN KEY ([PartyId]) REFERENCES [Parties] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.465 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InvoiceMasters] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceNumber] nvarchar(max) NOT NULL,
    [InvoiceType] int NOT NULL,
    [InvoiceDate] datetime2 NOT NULL,
    [PartyId] int NOT NULL,
    [SubTotal] decimal(18,2) NOT NULL,
    [ItemDiscountAmount] decimal(18,2) NOT NULL,
    [InvoiceDiscountAmount] decimal(18,2) NOT NULL,
    [TaxAmount] decimal(18,2) NOT NULL,
    [TotalAmount] decimal(18,2) NOT NULL,
    [PaidAmount] decimal(18,2) NOT NULL,
    [RemainingAmount] decimal(18,2) NOT NULL,
    [PaymentType] int NOT NULL,
    [IsPaid] bit NOT NULL,
    [Notes] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_InvoiceMasters] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InvoiceMasters_Parties_PartyId] FOREIGN KEY ([PartyId]) REFERENCES [Parties] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.470 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ProductionUnits] (
    [Id] int NOT NULL IDENTITY,
    [PartyId] int NOT NULL,
    [ManagerName] nvarchar(max) NULL,
    [CapacityUnitsPerDay] int NULL,
    [ProductionType] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ProductionUnits] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ProductionUnits_Parties_PartyId] FOREIGN KEY ([PartyId]) REFERENCES [Parties] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.480 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Suppliers] (
    [Id] int NOT NULL IDENTITY,
    [PartyId] int NOT NULL,
    [BankAccount] nvarchar(max) NULL,
    [Website] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Suppliers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Suppliers_Parties_PartyId] FOREIGN KEY ([PartyId]) REFERENCES [Parties] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.497 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ShareDistributions] (
    [Id] int NOT NULL IDENTITY,
    [PartnerId] int NOT NULL,
    [SharesCount] int NOT NULL,
    [DistributionDate] datetime2 NOT NULL,
    [ShareValue] decimal(18,2) NOT NULL,
    [Description] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ShareDistributions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ShareDistributions_Partners_PartnerId] FOREIGN KEY ([PartnerId]) REFERENCES [Partners] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.502 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ShareTransfers] (
    [Id] int NOT NULL IDENTITY,
    [TransfersDate] datetime2 NOT NULL,
    [BuyerId] int NOT NULL,
    [SellerId] int NOT NULL,
    [SharesCount] int NOT NULL,
    [TransferAmount] decimal(18,2) NOT NULL,
    [Description] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ShareTransfers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ShareTransfers_Partners_BuyerId] FOREIGN KEY ([BuyerId]) REFERENCES [Partners] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_ShareTransfers_Partners_SellerId] FOREIGN KEY ([SellerId]) REFERENCES [Partners] ([Id]) ON DELETE NO ACTION
);
2025-08-12 17:49:41.514 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [RolePermissions] (
    [Id] int NOT NULL IDENTITY,
    [RoleId] int NOT NULL,
    [PermissionId] int NOT NULL,
    CONSTRAINT [PK_RolePermissions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_RolePermissions_Permissions_PermissionId] FOREIGN KEY ([PermissionId]) REFERENCES [Permissions] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_RolePermissions_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.525 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Products] (
    [Id] int NOT NULL IDENTITY,
    [Code] nvarchar(max) NOT NULL,
    [Barcode] nvarchar(max) NOT NULL,
    [CategoryId] int NOT NULL,
    [UnitId] int NOT NULL,
    [StandardCost] decimal(18,2) NOT NULL,
    [MinimumStock] decimal(18,2) NULL,
    [MaximumStock] decimal(18,2) NULL,
    [SortOrder] int NULL,
    [ItemType] int NULL,
    [ImagePath] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Products] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Products_ItemCategories_CategoryId] FOREIGN KEY ([CategoryId]) REFERENCES [ItemCategories] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_Products_Units_UnitId] FOREIGN KEY ([UnitId]) REFERENCES [Units] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.536 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [RefreshTokens] (
    [Id] int NOT NULL IDENTITY,
    [Token] nvarchar(max) NOT NULL,
    [UserId] int NOT NULL,
    [ExpiryDate] datetime2 NOT NULL,
    [IsRevoked] bit NOT NULL,
    [RevokedReason] nvarchar(max) NULL,
    [RevokedAt] datetime2 NULL,
    [IpAddress] nvarchar(max) NULL,
    [UserAgent] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_RefreshTokens] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_RefreshTokens_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.547 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UserRoles] (
    [Id] int NOT NULL IDENTITY,
    [UserId] int NOT NULL,
    [RoleId] int NOT NULL,
    CONSTRAINT [PK_UserRoles] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_UserRoles_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_UserRoles_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.565 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [PartnerTransations] (
    [Id] int NOT NULL IDENTITY,
    [TransactionDate] datetime2 NOT NULL,
    [ActionDetailId] int NOT NULL,
    [PartnerId] int NOT NULL,
    [PartnerBandId] int NOT NULL,
    [Amount] decimal(18,2) NOT NULL,
    [Description] nvarchar(max) NULL,
    [Notes] nvarchar(max) NULL,
    [ImagePath] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_PartnerTransations] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_PartnerTransations_MainActions_ActionDetailId] FOREIGN KEY ([ActionDetailId]) REFERENCES [MainActions] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_PartnerTransations_PartnerBands_PartnerBandId] FOREIGN KEY ([PartnerBandId]) REFERENCES [PartnerBands] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_PartnerTransations_Partners_PartnerId] FOREIGN KEY ([PartnerId]) REFERENCES [Partners] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.570 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ShareHistories] (
    [Id] int NOT NULL IDENTITY,
    [PartnerId] int NOT NULL,
    [SharesCount] int NOT NULL,
    [SharePercentage] decimal(5,4) NOT NULL,
    [RecordDate] datetime2 NOT NULL,
    [ChangeType] int NOT NULL,
    [Description] nvarchar(max) NULL,
    [TransactionId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ShareHistories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ShareHistories_Partners_PartnerId] FOREIGN KEY ([PartnerId]) REFERENCES [Partners] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_ShareHistories_ShareTransfers_TransactionId] FOREIGN KEY ([TransactionId]) REFERENCES [ShareTransfers] ([Id])
);
2025-08-12 17:49:41.579 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InventoryTransactions] (
    [Id] int NOT NULL IDENTITY,
    [Type] int NOT NULL,
    [TransactionDate] datetime2 NOT NULL,
    [ProductId] int NOT NULL,
    [Quantity] decimal(18,2) NOT NULL,
    [UnitCost] decimal(18,2) NOT NULL,
    [TotalCost] decimal(18,2) NOT NULL,
    [BalanceAfter] decimal(18,2) NOT NULL,
    [AverageCost] decimal(18,2) NOT NULL,
    [ReferenceNumber] nvarchar(max) NULL,
    [Description] nvarchar(max) NULL,
    [InvoiceMasterId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_InventoryTransactions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InventoryTransactions_InvoiceMasters_InvoiceMasterId] FOREIGN KEY ([InvoiceMasterId]) REFERENCES [InvoiceMasters] ([Id]),
    CONSTRAINT [FK_InventoryTransactions_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.591 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InvoiceItems] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceMasterId] int NOT NULL,
    [ProductId] int NOT NULL,
    [Quantity] decimal(18,2) NOT NULL,
    [UnitPrice] decimal(18,2) NOT NULL,
    [DiscountPercentage] decimal(18,2) NOT NULL,
    [DiscountAmount] decimal(18,2) NOT NULL,
    [TotalPrice] decimal(18,2) NOT NULL,
    [Notes] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_InvoiceItems] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InvoiceItems_InvoiceMasters_InvoiceMasterId] FOREIGN KEY ([InvoiceMasterId]) REFERENCES [InvoiceMasters] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_InvoiceItems_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.599 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ProductInventories] (
    [Id] int NOT NULL IDENTITY,
    [ProductId] int NOT NULL,
    [AverageCost] decimal(18,2) NOT NULL,
    [Balance] decimal(18,2) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_ProductInventories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ProductInventories_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);
2025-08-12 17:49:41.635 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Customers_PartyId] ON [Customers] ([PartyId]);
2025-08-12 17:49:41.640 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryTransactions_InvoiceMasterId] ON [InventoryTransactions] ([InvoiceMasterId]);
2025-08-12 17:49:41.644 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryTransactions_ProductId] ON [InventoryTransactions] ([ProductId]);
2025-08-12 17:49:41.646 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceItems_InvoiceMasterId] ON [InvoiceItems] ([InvoiceMasterId]);
2025-08-12 17:49:41.649 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceItems_ProductId] ON [InvoiceItems] ([ProductId]);
2025-08-12 17:49:41.651 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceMasters_PartyId] ON [InvoiceMasters] ([PartyId]);
2025-08-12 17:49:41.655 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ItemCategories_ParentCategoryId] ON [ItemCategories] ([ParentCategoryId]);
2025-08-12 17:49:41.658 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_MainActions_ActionTypeId] ON [MainActions] ([ActionTypeId]);
2025-08-12 17:49:41.664 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_MainActions_ParentActionId] ON [MainActions] ([ParentActionId]);
2025-08-12 17:49:41.667 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_PartnerTransations_ActionDetailId] ON [PartnerTransations] ([ActionDetailId]);
2025-08-12 17:49:41.669 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_PartnerTransations_PartnerBandId] ON [PartnerTransations] ([PartnerBandId]);
2025-08-12 17:49:41.672 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_PartnerTransations_PartnerId] ON [PartnerTransations] ([PartnerId]);
2025-08-12 17:49:41.679 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_ProductInventories_ProductId] ON [ProductInventories] ([ProductId]);
2025-08-12 17:49:41.683 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_ProductionUnits_PartyId] ON [ProductionUnits] ([PartyId]);
2025-08-12 17:49:41.686 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Products_CategoryId] ON [Products] ([CategoryId]);
2025-08-12 17:49:41.688 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Products_UnitId] ON [Products] ([UnitId]);
2025-08-12 17:49:41.694 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RefreshTokens_UserId] ON [RefreshTokens] ([UserId]);
2025-08-12 17:49:41.697 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RolePermissions_PermissionId] ON [RolePermissions] ([PermissionId]);
2025-08-12 17:49:41.700 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RolePermissions_RoleId] ON [RolePermissions] ([RoleId]);
2025-08-12 17:49:41.702 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareDistributions_PartnerId] ON [ShareDistributions] ([PartnerId]);
2025-08-12 17:49:41.707 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareHistories_PartnerId] ON [ShareHistories] ([PartnerId]);
2025-08-12 17:49:41.713 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareHistories_TransactionId] ON [ShareHistories] ([TransactionId]);
2025-08-12 17:49:41.717 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareTransfers_BuyerId] ON [ShareTransfers] ([BuyerId]);
2025-08-12 17:49:41.720 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ShareTransfers_SellerId] ON [ShareTransfers] ([SellerId]);
2025-08-12 17:49:41.723 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Suppliers_PartyId] ON [Suppliers] ([PartyId]);
2025-08-12 17:49:41.730 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_UserRoles_RoleId] ON [UserRoles] ([RoleId]);
2025-08-12 17:49:41.732 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_UserRoles_UserId] ON [UserRoles] ([UserId]);
2025-08-12 17:49:42.203 +03:00 [INF] Executed DbCommand (164ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:49:42.274 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]
        WHERE [u].[UserName] = N'Admin') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:49:42.561 +03:00 [INF] Executed DbCommand (79ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([CreatedAt], [CreatedBy], [FullName], [IsActive], [IsDeleted], [Password], [Phone], [ProfileImage], [UpdatedAt], [UpdatedBy], [UserName])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-08-12 17:49:42.614 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:49:42.701 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000), @p11='?' (DbType = Boolean), @p12='?' (DbType = Boolean), @p13='?' (Size = 4000), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32), @p18='?' (Size = 4000), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?' (Size = 4000), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = DateTime2), @p25='?' (DbType = Int32), @p26='?' (Size = 4000), @p27='?' (DbType = Boolean), @p28='?' (DbType = Boolean), @p29='?' (Size = 4000), @p30='?' (DbType = DateTime2), @p31='?' (DbType = Int32), @p32='?' (DbType = DateTime2), @p33='?' (DbType = Int32), @p34='?' (Size = 4000), @p35='?' (DbType = Boolean), @p36='?' (DbType = Boolean), @p37='?' (Size = 4000), @p38='?' (DbType = DateTime2), @p39='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [Roles] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, 1),
(@p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, 2),
(@p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, 3),
(@p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, 4)) AS i ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsActive], i.[IsDeleted], i.[Name], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-08-12 17:49:42.789 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 17:49:42.848 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 17:49:42.940 +03:00 [INF] Executed DbCommand (51ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [UserRoles] USING (
VALUES (@p0, @p1, 0),
(@p2, @p3, 1),
(@p4, @p5, 2),
(@p6, @p7, 3),
(@p8, @p9, 4)) AS i ([RoleId], [UserId], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([RoleId], [UserId])
VALUES (i.[RoleId], i.[UserId])
OUTPUT INSERTED.[Id], i._Position;
2025-08-12 17:49:42.955 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:49:43.004 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000), @p11='?' (DbType = Boolean), @p12='?' (DbType = Boolean), @p13='?' (Size = 4000), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32), @p18='?' (Size = 4000), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?' (Size = 4000), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = DateTime2), @p25='?' (DbType = Int32), @p26='?' (Size = 4000), @p27='?' (DbType = Boolean), @p28='?' (DbType = Boolean), @p29='?' (Size = 4000), @p30='?' (DbType = DateTime2), @p31='?' (DbType = Int32), @p32='?' (DbType = DateTime2), @p33='?' (DbType = Int32), @p34='?' (Size = 4000), @p35='?' (DbType = Boolean), @p36='?' (DbType = Boolean), @p37='?' (Size = 4000), @p38='?' (DbType = DateTime2), @p39='?' (DbType = Int32), @p40='?' (DbType = DateTime2), @p41='?' (DbType = Int32), @p42='?' (Size = 4000), @p43='?' (DbType = Boolean), @p44='?' (DbType = Boolean), @p45='?' (Size = 4000), @p46='?' (DbType = DateTime2), @p47='?' (DbType = Int32), @p48='?' (DbType = DateTime2), @p49='?' (DbType = Int32), @p50='?' (Size = 4000), @p51='?' (DbType = Boolean), @p52='?' (DbType = Boolean), @p53='?' (Size = 4000), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (DbType = DateTime2), @p57='?' (DbType = Int32), @p58='?' (Size = 4000), @p59='?' (DbType = Boolean), @p60='?' (DbType = Boolean), @p61='?' (Size = 4000), @p62='?' (DbType = DateTime2), @p63='?' (DbType = Int32), @p64='?' (DbType = DateTime2), @p65='?' (DbType = Int32), @p66='?' (Size = 4000), @p67='?' (DbType = Boolean), @p68='?' (DbType = Boolean), @p69='?' (Size = 4000), @p70='?' (DbType = DateTime2), @p71='?' (DbType = Int32), @p72='?' (DbType = DateTime2), @p73='?' (DbType = Int32), @p74='?' (Size = 4000), @p75='?' (DbType = Boolean), @p76='?' (DbType = Boolean), @p77='?' (Size = 4000), @p78='?' (DbType = DateTime2), @p79='?' (DbType = Int32), @p80='?' (DbType = DateTime2), @p81='?' (DbType = Int32), @p82='?' (Size = 4000), @p83='?' (DbType = Boolean), @p84='?' (DbType = Boolean), @p85='?' (Size = 4000), @p86='?' (DbType = DateTime2), @p87='?' (DbType = Int32), @p88='?' (DbType = DateTime2), @p89='?' (DbType = Int32), @p90='?' (Size = 4000), @p91='?' (DbType = Boolean), @p92='?' (DbType = Boolean), @p93='?' (Size = 4000), @p94='?' (DbType = DateTime2), @p95='?' (DbType = Int32), @p96='?' (DbType = DateTime2), @p97='?' (DbType = Int32), @p98='?' (Size = 4000), @p99='?' (DbType = Boolean), @p100='?' (DbType = Boolean), @p101='?' (Size = 4000), @p102='?' (DbType = DateTime2), @p103='?' (DbType = Int32), @p104='?' (DbType = DateTime2), @p105='?' (DbType = Int32), @p106='?' (Size = 4000), @p107='?' (DbType = Boolean), @p108='?' (DbType = Boolean), @p109='?' (Size = 4000), @p110='?' (DbType = DateTime2), @p111='?' (DbType = Int32), @p112='?' (DbType = DateTime2), @p113='?' (DbType = Int32), @p114='?' (Size = 4000), @p115='?' (DbType = Boolean), @p116='?' (DbType = Boolean), @p117='?' (Size = 4000), @p118='?' (DbType = DateTime2), @p119='?' (DbType = Int32), @p120='?' (DbType = DateTime2), @p121='?' (DbType = Int32), @p122='?' (Size = 4000), @p123='?' (DbType = Boolean), @p124='?' (DbType = Boolean), @p125='?' (Size = 4000), @p126='?' (DbType = DateTime2), @p127='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [Permissions] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, 1),
(@p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, 2),
(@p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, 3),
(@p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, 4),
(@p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, 5),
(@p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, 6),
(@p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, 7),
(@p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, 8),
(@p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79, 9),
(@p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, 10),
(@p88, @p89, @p90, @p91, @p92, @p93, @p94, @p95, 11),
(@p96, @p97, @p98, @p99, @p100, @p101, @p102, @p103, 12),
(@p104, @p105, @p106, @p107, @p108, @p109, @p110, @p111, 13),
(@p112, @p113, @p114, @p115, @p116, @p117, @p118, @p119, 14),
(@p120, @p121, @p122, @p123, @p124, @p125, @p126, @p127, 15)) AS i ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsActive], i.[IsDeleted], i.[Name], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-08-12 17:49:43.043 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[Name] = N'Admin'
2025-08-12 17:49:43.070 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = Int32), @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (DbType = Int32), @p18='?' (DbType = Int32), @p19='?' (DbType = Int32), @p20='?' (DbType = Int32), @p21='?' (DbType = Int32), @p22='?' (DbType = Int32), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = Int32), @p29='?' (DbType = Int32), @p30='?' (DbType = Int32), @p31='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [RolePermissions] USING (
VALUES (@p0, @p1, 0),
(@p2, @p3, 1),
(@p4, @p5, 2),
(@p6, @p7, 3),
(@p8, @p9, 4),
(@p10, @p11, 5),
(@p12, @p13, 6),
(@p14, @p15, 7),
(@p16, @p17, 8),
(@p18, @p19, 9),
(@p20, @p21, 10),
(@p22, @p23, 11),
(@p24, @p25, 12),
(@p26, @p27, 13),
(@p28, @p29, 14),
(@p30, @p31, 15)) AS i ([PermissionId], [RoleId], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([PermissionId], [RoleId])
VALUES (i.[PermissionId], i.[RoleId])
OUTPUT INSERTED.[Id], i._Position;
2025-08-12 17:49:43.082 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:49:43.123 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000), @p11='?' (DbType = Boolean), @p12='?' (DbType = Boolean), @p13='?' (Size = 4000), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32), @p18='?' (Size = 4000), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?' (Size = 4000), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [ActionTypes] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, 1),
(@p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, 2)) AS i ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsActive], i.[IsDeleted], i.[Name], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-08-12 17:49:43.133 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:49:43.227 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Int32), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = DateTime2), @p12='?' (DbType = Int32), @p13='?' (Size = 4000), @p14='?' (DbType = Boolean), @p15='?' (DbType = Boolean), @p16='?' (Size = 4000), @p17='?' (DbType = Int32), @p18='?' (DbType = DateTime2), @p19='?' (DbType = Int32), @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime2), @p22='?' (DbType = Int32), @p23='?' (Size = 4000), @p24='?' (DbType = Boolean), @p25='?' (DbType = Boolean), @p26='?' (Size = 4000), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime2), @p29='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [MainActions] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, 0),
(@p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, 1),
(@p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, 2)) AS i ([ActionTypeId], [CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [ParentActionId], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([ActionTypeId], [CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [ParentActionId], [UpdatedAt], [UpdatedBy])
VALUES (i.[ActionTypeId], i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsActive], i.[IsDeleted], i.[Name], i.[ParentActionId], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-08-12 17:49:43.244 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:49:43.253 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]
        WHERE [c].[Id] = 1) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:49:43.311 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (Size = 4000), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (Size = 4000), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (Size = 4000), @p10='?' (Size = 4000), @p11='?' (Size = 4000), @p12='?' (Size = 4000), @p13='?' (DbType = Int32), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Companies] ([Code], [CommercialRegister], [CreatedAt], [CreatedBy], [Description], [EstablishmentDate], [IndustrialRegister], [IsActive], [IsDeleted], [LogoPath], [Name], [Symbol], [TaxRegister], [TotalShares], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-08-12 17:49:43.322 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 17:49:43.381 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime2), @p10='?' (DbType = Int32), @p11='?' (Size = 4000), @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (Size = 4000), @p15='?' (Size = 4000), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32), @p18='?' (DbType = DateTime2), @p19='?' (DbType = Int32), @p20='?' (Size = 4000), @p21='?' (DbType = Boolean), @p22='?' (DbType = Boolean), @p23='?' (Size = 4000), @p24='?' (Size = 4000), @p25='?' (DbType = DateTime2), @p26='?' (DbType = Int32), @p27='?' (DbType = DateTime2), @p28='?' (DbType = Int32), @p29='?' (Size = 4000), @p30='?' (DbType = Boolean), @p31='?' (DbType = Boolean), @p32='?' (Size = 4000), @p33='?' (Size = 4000), @p34='?' (DbType = DateTime2), @p35='?' (DbType = Int32), @p36='?' (DbType = DateTime2), @p37='?' (DbType = Int32), @p38='?' (Size = 4000), @p39='?' (DbType = Boolean), @p40='?' (DbType = Boolean), @p41='?' (Size = 4000), @p42='?' (Size = 4000), @p43='?' (DbType = DateTime2), @p44='?' (DbType = Int32), @p45='?' (DbType = DateTime2), @p46='?' (DbType = Int32), @p47='?' (Size = 4000), @p48='?' (DbType = Boolean), @p49='?' (DbType = Boolean), @p50='?' (Size = 4000), @p51='?' (Size = 4000), @p52='?' (DbType = DateTime2), @p53='?' (DbType = Int32), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (Size = 4000), @p57='?' (DbType = Boolean), @p58='?' (DbType = Boolean), @p59='?' (Size = 4000), @p60='?' (Size = 4000), @p61='?' (DbType = DateTime2), @p62='?' (DbType = Int32), @p63='?' (DbType = DateTime2), @p64='?' (DbType = Int32), @p65='?' (Size = 4000), @p66='?' (DbType = Boolean), @p67='?' (DbType = Boolean), @p68='?' (Size = 4000), @p69='?' (Size = 4000), @p70='?' (DbType = DateTime2), @p71='?' (DbType = Int32), @p72='?' (DbType = DateTime2), @p73='?' (DbType = Int32), @p74='?' (Size = 4000), @p75='?' (DbType = Boolean), @p76='?' (DbType = Boolean), @p77='?' (Size = 4000), @p78='?' (Size = 4000), @p79='?' (DbType = DateTime2), @p80='?' (DbType = Int32), @p81='?' (DbType = DateTime2), @p82='?' (DbType = Int32), @p83='?' (Size = 4000), @p84='?' (DbType = Boolean), @p85='?' (DbType = Boolean), @p86='?' (Size = 4000), @p87='?' (Size = 4000), @p88='?' (DbType = DateTime2), @p89='?' (DbType = Int32), @p90='?' (DbType = DateTime2), @p91='?' (DbType = Int32), @p92='?' (Size = 4000), @p93='?' (DbType = Boolean), @p94='?' (DbType = Boolean), @p95='?' (Size = 4000), @p96='?' (Size = 4000), @p97='?' (DbType = DateTime2), @p98='?' (DbType = Int32), @p99='?' (DbType = DateTime2), @p100='?' (DbType = Int32), @p101='?' (Size = 4000), @p102='?' (DbType = Boolean), @p103='?' (DbType = Boolean), @p104='?' (Size = 4000), @p105='?' (Size = 4000), @p106='?' (DbType = DateTime2), @p107='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [Units] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, 0),
(@p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, 1),
(@p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, 2),
(@p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, 3),
(@p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, 4),
(@p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, 5),
(@p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, 6),
(@p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, 7),
(@p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, 8),
(@p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89, 9),
(@p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, 10),
(@p99, @p100, @p101, @p102, @p103, @p104, @p105, @p106, @p107, 11)) AS i ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [Symbol], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [Symbol], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsActive], i.[IsDeleted], i.[Name], i.[Symbol], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-08-12 17:49:43.592 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 17:49:43.816 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 17:49:43.934 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 17:49:43.944 +03:00 [INF] Hosting environment: Development
2025-08-12 17:49:43.945 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 17:49:44.232 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 17:49:44.439 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 215.5375ms
2025-08-12 17:49:44.468 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 17:49:44.471 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 17:49:44.481 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 13.307ms
2025-08-12 17:49:44.520 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 49.112ms
2025-08-12 17:49:44.706 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 17:49:44.757 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 50.5388ms
2025-08-12 17:50:00.372 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:50:00.394 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 17:50:00.510 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:50:00.532 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:50:03.285 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:50:12.723 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:50:12.753 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 12214.9363ms
2025-08-12 17:50:12.756 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:50:12.760 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 404 null application/json; charset=utf-8 12388.0366ms
2025-08-12 17:51:11.135 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 197
2025-08-12 17:51:11.142 +03:00 [INF] CORS policy execution successful.
2025-08-12 17:51:11.145 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-12 17:51:11.156 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:51:11.391 +03:00 [INF] Executed DbCommand (109ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:51:11.486 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialCapital], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-12 17:51:11.492 +03:00 [INF] Partner created successfully: جمال هلال
2025-08-12 17:51:11.660 +03:00 [INF] Executed DbCommand (25ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[InitialCapital], [p1].[IsActive], [p1].[IsDeleted], [p1].[Name], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [Partners] AS [p]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [p1]
LEFT JOIN [PartnerTransations] AS [p0] ON [p1].[Id] = [p0].[PartnerId]
ORDER BY [p1].[Id]
2025-08-12 17:51:11.723 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-12 17:51:11.762 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:51:11.782 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-12 17:51:11.874 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[@__partnerId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id] AS [PartnerId], [p].[Name] AS [PartnerName], COALESCE((
    SELECT COALESCE(SUM([s].[SharesCount]), 0)
    FROM [ShareTransfers] AS [s]
    WHERE [p].[Id] = [s].[BuyerId]), 0) AS [TotalSharesBuyer], COALESCE((
    SELECT COALESCE(SUM([s0].[SharesCount]), 0)
    FROM [ShareTransfers] AS [s0]
    WHERE [p].[Id] = [s0].[SellerId]), 0) AS [TotalSharesSeller], COALESCE((
    SELECT COALESCE(SUM([s1].[SharesCount]), 0)
    FROM [ShareTransfers] AS [s1]
    WHERE [p].[Id] = [s1].[BuyerId]), 0) - COALESCE((
    SELECT COALESCE(SUM([s2].[SharesCount]), 0)
    FROM [ShareTransfers] AS [s2]
    WHERE [p].[Id] = [s2].[SellerId]), 0) AS [NetShares]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__partnerId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:51:11.885 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 17:51:11.931 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 771.3828ms
2025-08-12 17:51:11.935 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-12 17:51:11.938 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 802.7202ms
2025-08-12 17:51:37.482 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 199
2025-08-12 17:51:37.510 +03:00 [INF] CORS policy execution successful.
2025-08-12 17:51:37.513 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-12 17:51:37.514 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:51:37.542 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:51:37.551 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialCapital], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-12 17:51:37.563 +03:00 [INF] Partner created successfully: محمود بكر
2025-08-12 17:51:37.572 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[InitialCapital], [p1].[IsActive], [p1].[IsDeleted], [p1].[Name], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [Partners] AS [p]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [p1]
LEFT JOIN [PartnerTransations] AS [p0] ON [p1].[Id] = [p0].[PartnerId]
ORDER BY [p1].[Id]
2025-08-12 17:51:37.579 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-12 17:51:37.585 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:51:37.591 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-12 17:51:37.618 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__partnerId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id] AS [PartnerId], [p].[Name] AS [PartnerName], COALESCE((
    SELECT COALESCE(SUM([s].[SharesCount]), 0)
    FROM [ShareTransfers] AS [s]
    WHERE [p].[Id] = [s].[BuyerId]), 0) AS [TotalSharesBuyer], COALESCE((
    SELECT COALESCE(SUM([s0].[SharesCount]), 0)
    FROM [ShareTransfers] AS [s0]
    WHERE [p].[Id] = [s0].[SellerId]), 0) AS [TotalSharesSeller], COALESCE((
    SELECT COALESCE(SUM([s1].[SharesCount]), 0)
    FROM [ShareTransfers] AS [s1]
    WHERE [p].[Id] = [s1].[BuyerId]), 0) - COALESCE((
    SELECT COALESCE(SUM([s2].[SharesCount]), 0)
    FROM [ShareTransfers] AS [s2]
    WHERE [p].[Id] = [s2].[SellerId]), 0) AS [NetShares]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__partnerId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:51:37.621 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 17:51:37.622 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 106.4655ms
2025-08-12 17:51:37.624 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-12 17:51:37.625 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 143.7028ms
2025-08-12 17:52:04.888 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:52:04.899 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:52:04.901 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:52:08.707 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:52:10.102 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:52:13.044 +03:00 [INF] Executed DbCommand (25ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[BuyerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
2025-08-12 17:52:14.946 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[SellerId] AS [PartnerId], COALESCE(SUM([s].[SharesCount]), 0) AS [Total]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[SellerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
GROUP BY [s].[SellerId]
2025-08-12 17:52:17.357 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:52:19.336 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:52:19.341 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 14437.1865ms
2025-08-12 17:52:19.343 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:52:19.345 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 200 null application/json; charset=utf-8 14457.0023ms
2025-08-12 17:54:17.297 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/PartnerBand - application/json 147
2025-08-12 17:54:17.308 +03:00 [INF] CORS policy execution successful.
2025-08-12 17:54:17.309 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerBandController.CreatePartnerBandsAsycn (DoorCompany.Api)'
2025-08-12 17:54:17.314 +03:00 [INF] Route matched with {action = "CreatePartnerBandsAsycn", controller = "PartnerBand"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerBandsAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerBandDto) on controller DoorCompany.Api.Controllers.PartnerBandController (DoorCompany.Api).
2025-08-12 17:54:17.363 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerBands] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:54:17.395 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [PartnerBands] ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-08-12 17:54:17.400 +03:00 [INF] Partner Band created successfully: سداد
2025-08-12 17:54:17.415 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerBands] AS [p]
WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:54:17.421 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerBandResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 17:54:17.425 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerBandController.CreatePartnerBandsAsycn (DoorCompany.Api) in 108.3613ms
2025-08-12 17:54:17.427 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerBandController.CreatePartnerBandsAsycn (DoorCompany.Api)'
2025-08-12 17:54:17.431 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/PartnerBand - 200 null application/json; charset=utf-8 133.5861ms
2025-08-12 17:54:51.356 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner/transactions - multipart/form-data; boundary=----WebKitFormBoundarye3Kroy5PMAbVjWXY 1280
2025-08-12 17:54:51.359 +03:00 [INF] CORS policy execution successful.
2025-08-12 17:54:51.361 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerTansactionAsycn (DoorCompany.Api)'
2025-08-12 17:54:51.375 +03:00 [INF] Route matched with {action = "CreatePartnerTansactionAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerTansactionAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerTransactionDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:54:51.576 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (Size = 4000), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = DateTime2), @p12='?' (DbType = DateTime2), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [PartnerTransations] ([ActionDetailId], [Amount], [CreatedAt], [CreatedBy], [Description], [ImagePath], [IsActive], [IsDeleted], [Notes], [PartnerBandId], [PartnerId], [TransactionDate], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-08-12 17:54:51.582 +03:00 [INF] PartnerTransactions created successfully: سداد
2025-08-12 17:54:51.647 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy], [p1].[Id], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[IsActive], [p1].[IsDeleted], [p1].[Name], [p1].[UpdatedAt], [p1].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[Name], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy]
FROM [PartnerTransations] AS [p]
INNER JOIN [Partners] AS [p0] ON [p].[PartnerId] = [p0].[Id]
INNER JOIN [PartnerBands] AS [p1] ON [p].[PartnerBandId] = [p1].[Id]
INNER JOIN [MainActions] AS [m] ON [p].[ActionDetailId] = [m].[Id]
WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:54:51.659 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 17:54:51.669 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerTansactionAsycn (DoorCompany.Api) in 288.5575ms
2025-08-12 17:54:51.671 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerTansactionAsycn (DoorCompany.Api)'
2025-08-12 17:54:51.675 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner/transactions - 200 null application/json; charset=utf-8 318.7358ms
2025-08-12 17:54:58.487 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:54:58.490 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:54:58.492 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:55:25.124 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:55:25.698 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:55:26.264 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[BuyerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
2025-08-12 17:55:26.487 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[SellerId] AS [PartnerId], COALESCE(SUM([s].[SharesCount]), 0) AS [Total]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[SellerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
GROUP BY [s].[SellerId]
2025-08-12 17:55:27.490 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:55:28.738 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:55:28.741 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 30246.4222ms
2025-08-12 17:55:28.743 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:55:28.745 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 200 null application/json; charset=utf-8 30257.7625ms
2025-08-12 17:56:14.797 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner/transactions - multipart/form-data; boundary=----WebKitFormBoundaryY41U13cTjzBbLZD8 1281
2025-08-12 17:56:14.806 +03:00 [INF] CORS policy execution successful.
2025-08-12 17:56:14.807 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerTansactionAsycn (DoorCompany.Api)'
2025-08-12 17:56:14.808 +03:00 [INF] Route matched with {action = "CreatePartnerTansactionAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerTansactionAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerTransactionDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:56:14.833 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (Size = 4000), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (DbType = DateTime2), @p12='?' (DbType = DateTime2), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [PartnerTransations] ([ActionDetailId], [Amount], [CreatedAt], [CreatedBy], [Description], [ImagePath], [IsActive], [IsDeleted], [Notes], [PartnerBandId], [PartnerId], [TransactionDate], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-08-12 17:56:14.837 +03:00 [INF] PartnerTransactions created successfully: سداد
2025-08-12 17:56:14.848 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy], [p1].[Id], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[IsActive], [p1].[IsDeleted], [p1].[Name], [p1].[UpdatedAt], [p1].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[Name], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy]
FROM [PartnerTransations] AS [p]
INNER JOIN [Partners] AS [p0] ON [p].[PartnerId] = [p0].[Id]
INNER JOIN [PartnerBands] AS [p1] ON [p].[PartnerBandId] = [p1].[Id]
INNER JOIN [MainActions] AS [m] ON [p].[ActionDetailId] = [m].[Id]
WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:56:14.851 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 17:56:14.853 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerTansactionAsycn (DoorCompany.Api) in 36.8806ms
2025-08-12 17:56:14.855 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerTansactionAsycn (DoorCompany.Api)'
2025-08-12 17:56:14.856 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner/transactions - 200 null application/json; charset=utf-8 59.2286ms
2025-08-12 17:56:19.903 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 17:56:19.907 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:56:19.908 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 17:56:21.353 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:56:21.364 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:56:21.375 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[BuyerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
2025-08-12 17:56:21.385 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__partnerIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[SellerId] AS [PartnerId], COALESCE(SUM([s].[SharesCount]), 0) AS [Total]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND [s].[SellerId] IN (
    SELECT [p].[value]
    FROM OPENJSON(@__partnerIds_0) WITH ([value] int '$') AS [p]
)
GROUP BY [s].[SellerId]
2025-08-12 17:56:21.391 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 17:56:21.397 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 17:56:21.399 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 1488.3264ms
2025-08-12 17:56:21.401 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 17:56:21.403 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 200 null application/json; charset=utf-8 1500.0037ms
2025-08-12 18:21:08.472 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 18:21:08.640 +03:00 [INF] Executed DbCommand (111ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 18:21:09.005 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:21:09.029 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:21:09.188 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 18:21:09.289 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 18:21:09.342 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 18:21:09.356 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:21:09.365 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:21:09.377 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:21:09.387 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:21:09.396 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:21:09.408 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:21:09.568 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 18:21:09.723 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 18:21:09.834 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 18:21:09.840 +03:00 [INF] Hosting environment: Development
2025-08-12 18:21:09.842 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 18:21:10.192 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 18:21:10.372 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 189.7452ms
2025-08-12 18:21:10.394 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 18:21:10.401 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 18:21:10.403 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.8779ms
2025-08-12 18:21:10.441 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.425ms
2025-08-12 18:21:10.609 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 18:21:10.654 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 44.9123ms
2025-08-12 18:21:19.903 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 18:21:19.912 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 18:21:20.016 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 18:21:20.042 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 18:21:20.234 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 18:21:20.272 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 18:21:20.301 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-12 18:21:20.326 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 18:21:20.343 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 18:21:20.378 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 329.9067ms
2025-08-12 18:21:20.381 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 18:21:20.386 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 200 null application/json; charset=utf-8 482.6391ms
2025-08-12 18:22:46.264 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 18:22:46.410 +03:00 [INF] Executed DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 18:22:46.727 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:22:46.753 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:22:46.930 +03:00 [INF] Executed DbCommand (43ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 18:22:47.056 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 18:22:47.288 +03:00 [INF] Executed DbCommand (194ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 18:22:47.304 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:22:47.315 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:22:47.388 +03:00 [INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:22:47.408 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:22:47.431 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:22:47.447 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 18:22:47.599 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 18:22:47.776 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 18:22:47.935 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 18:22:47.944 +03:00 [INF] Hosting environment: Development
2025-08-12 18:22:47.945 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 18:22:48.033 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 18:22:48.252 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 230.4541ms
2025-08-12 18:22:48.268 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 18:22:48.275 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.8964ms
2025-08-12 18:22:48.282 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 18:22:48.319 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 36.4311ms
2025-08-12 18:22:48.489 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 18:22:48.533 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 43.8803ms
2025-08-12 18:22:54.557 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - null null
2025-08-12 18:22:54.566 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 18:22:54.674 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 18:22:54.696 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 18:22:54.879 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 18:22:54.916 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 18:22:54.945 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-12 18:22:54.971 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 18:22:54.989 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 18:22:55.024 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 322.0092ms
2025-08-12 18:22:55.028 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 18:22:55.032 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-Summer - 200 null application/json; charset=utf-8 474.5464ms
2025-08-12 19:06:01.641 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-12 19:06:01.781 +03:00 [INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-12 19:06:02.124 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 19:06:02.152 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 19:06:02.377 +03:00 [INF] Executed DbCommand (91ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-12 19:06:02.477 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-12 19:06:02.563 +03:00 [INF] Executed DbCommand (46ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-12 19:06:02.579 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 19:06:02.589 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 19:06:02.611 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 19:06:02.623 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 19:06:02.635 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 19:06:02.654 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-12 19:06:02.835 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-12 19:06:03.003 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-12 19:06:03.125 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-12 19:06:03.132 +03:00 [INF] Hosting environment: Development
2025-08-12 19:06:03.135 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-12 19:06:03.451 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-12 19:06:03.646 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 205.8149ms
2025-08-12 19:06:03.670 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-12 19:06:03.676 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-12 19:06:03.680 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.3458ms
2025-08-12 19:06:03.722 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 46.5171ms
2025-08-12 19:06:03.904 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-12 19:06:03.969 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 65.7841ms
2025-08-12 19:07:28.468 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/login - null null
2025-08-12 19:07:28.475 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-12 19:07:28.525 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:28.530 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/login - 204 null null 62.0998ms
2025-08-12 19:07:28.540 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 43
2025-08-12 19:07:28.546 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:29.868 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-08-12 19:07:29.909 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-08-12 19:07:30.256 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-12 19:07:30.333 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-08-12 19:07:30.519 +03:00 [INF] Access token generated for user 1
2025-08-12 19:07:30.720 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-08-12 19:07:30.748 +03:00 [INF] User logged in successfully: admin
2025-08-12 19:07:30.758 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:07:30.795 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 879.119ms
2025-08-12 19:07:30.798 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-08-12 19:07:30.802 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 2261.6888ms
2025-08-12 19:07:30.806 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:07:30.809 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:30.820 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-12 19:07:30.827 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-12 19:07:30.830 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 24.0169ms
2025-08-12 19:07:30.843 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-12 19:07:30.849 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:30.851 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 7.6129ms
2025-08-12 19:07:30.856 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:07:30.859 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:30.923 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:07:30.926 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:30.928 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 4.5205ms
2025-08-12 19:07:30.931 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:07:30.933 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:07:30.937 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-12 19:07:30.937 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:30.942 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:07:30.948 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 19:07:30.992 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - null null
2025-08-12 19:07:31.003 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:31.005 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - 204 null null 12.4062ms
2025-08-12 19:07:31.009 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - application/json 107
2025-08-12 19:07:31.012 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:31.013 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-12 19:07:31.018 +03:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] RefreshToken(DoorCompany.Service.Dtos.UserDto.RefreshTokenDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-08-12 19:07:31.057 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:07:31.063 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-12 19:07:31.086 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-12 19:07:31.103 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:07:31.116 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:07:31.134 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 194.208ms
2025-08-12 19:07:31.136 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:07:31.137 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 281.2703ms
2025-08-12 19:07:31.142 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:07:31.168 +03:00 [INF] Executed DbCommand (38ms) [Parameters=[@__refreshTokenDto_RefreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshTokenDto_RefreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-12 19:07:31.168 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:07:31.178 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 19:07:31.193 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 242.5831ms
2025-08-12 19:07:31.195 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:07:31.196 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 263.7823ms
2025-08-12 19:07:31.200 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-12 19:07:31.278 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__refreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-12 19:07:31.331 +03:00 [INF] Access token generated for user 1
2025-08-12 19:07:31.333 +03:00 [INF] Access token refreshed for user 1
2025-08-12 19:07:31.334 +03:00 [INF] Token refreshed successfully for user: 1
2025-08-12 19:07:31.335 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:07:31.337 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api) in 316.201ms
2025-08-12 19:07:31.339 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-12 19:07:31.340 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - 200 null application/json; charset=utf-8 331.5684ms
2025-08-12 19:07:31.344 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:07:31.347 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:07:31.351 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:07:31.353 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-12 19:07:31.362 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-12 19:07:31.366 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:07:31.368 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 12.6569ms
2025-08-12 19:07:31.369 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:07:31.371 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 26.4749ms
2025-08-12 19:08:41.781 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-12 19:08:41.785 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:08:41.786 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 5.9522ms
2025-08-12 19:08:41.795 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:08:41.799 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:08:41.800 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:08:41.802 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-12 19:08:41.807 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:08:41.808 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-12 19:08:41.815 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:08:41.820 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 12.784ms
2025-08-12 19:08:41.820 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:08:41.830 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:08:41.833 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 29.6972ms
2025-08-12 19:08:41.836 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:08:41.837 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:08:41.838 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:08:41.839 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 43.8687ms
2025-08-12 19:08:41.840 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 19:08:41.852 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:08:41.858 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:08:41.871 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:08:41.879 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:08:41.901 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 19:08:41.909 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 64.2402ms
2025-08-12 19:08:41.911 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:08:41.914 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 86.2588ms
2025-08-12 19:10:52.611 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-12 19:10:52.641 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:10:52.648 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:10:52.651 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:10:52.652 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 41.2767ms
2025-08-12 19:10:52.657 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 16.8451ms
2025-08-12 19:10:52.668 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:10:52.672 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:10:52.676 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:10:52.678 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:10:52.680 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:10:52.680 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:10:52.681 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-12 19:10:52.682 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 19:10:52.690 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-12 19:10:52.693 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:10:52.695 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:10:52.699 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:10:52.699 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 14.9253ms
2025-08-12 19:10:52.706 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:10:52.707 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:10:52.711 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 42.6284ms
2025-08-12 19:10:52.711 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:10:52.717 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 19:10:52.720 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 31.2176ms
2025-08-12 19:10:52.722 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:10:52.723 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 51.0172ms
2025-08-12 19:13:10.116 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-12 19:13:10.121 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:13:10.123 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 7.3773ms
2025-08-12 19:13:10.132 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:13:10.135 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:13:10.136 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:13:10.138 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-12 19:13:10.145 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-12 19:13:10.152 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:13:10.161 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 20.7008ms
2025-08-12 19:13:10.163 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:13:10.165 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 32.3669ms
2025-08-12 19:13:10.180 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:13:10.182 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:13:10.183 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 3.3944ms
2025-08-12 19:13:10.187 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:13:10.192 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:13:10.193 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:13:10.195 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 19:13:10.202 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:13:10.211 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:13:10.215 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:13:10.220 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:13:10.223 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 19:13:10.225 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 27.5033ms
2025-08-12 19:13:10.227 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:13:10.228 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 41.3252ms
2025-08-12 19:21:16.604 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-12 19:21:16.608 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:16.609 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 5.1236ms
2025-08-12 19:21:16.612 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:21:16.614 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:16.619 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '12/08/2025 04:09:31 م', Current time (UTC): '12/08/2025 04:21:16 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-12 19:21:16.642 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:21:16.679 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:16.678 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '12/08/2025 04:09:31 م', Current time (UTC): '12/08/2025 04:21:16 م'.
2025-08-12 19:21:16.681 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 38.4304ms
2025-08-12 19:21:16.682 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '12/08/2025 04:09:31 م', Current time (UTC): '12/08/2025 04:21:16 م'.
2025-08-12 19:21:16.686 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:21:16.688 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-12 19:21:16.691 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:16.693 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '12/08/2025 04:09:31 م', Current time (UTC): '12/08/2025 04:21:16 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-12 19:21:16.694 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-12 19:21:16.696 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '12/08/2025 04:09:31 م', Current time (UTC): '12/08/2025 04:21:16 م'.
2025-08-12 19:21:16.697 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 85.2909ms
2025-08-12 19:21:16.698 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '12/08/2025 04:09:31 م', Current time (UTC): '12/08/2025 04:21:16 م'.
2025-08-12 19:21:16.703 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:21:16.704 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - null null
2025-08-12 19:21:16.705 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 19:21:16.708 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:16.710 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - 204 null null 5.9412ms
2025-08-12 19:21:16.718 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - application/json 107
2025-08-12 19:21:16.722 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:16.724 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '12/08/2025 04:09:31 م', Current time (UTC): '12/08/2025 04:21:16 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-12 19:21:16.728 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '12/08/2025 04:09:31 م', Current time (UTC): '12/08/2025 04:21:16 م'.
2025-08-12 19:21:16.729 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '12/08/2025 04:09:31 م', Current time (UTC): '12/08/2025 04:21:16 م'.
2025-08-12 19:21:16.730 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-12 19:21:16.731 +03:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] RefreshToken(DoorCompany.Service.Dtos.UserDto.RefreshTokenDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-08-12 19:21:16.986 +03:00 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:21:17.002 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:21:17.012 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:21:17.031 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:21:17.034 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 19:21:17.036 +03:00 [INF] Executed DbCommand (97ms) [Parameters=[@__refreshTokenDto_RefreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshTokenDto_RefreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-12 19:21:17.038 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 328.5989ms
2025-08-12 19:21:17.043 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:21:17.045 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 358.7992ms
2025-08-12 19:21:17.080 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@__refreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-12 19:21:17.089 +03:00 [INF] Access token generated for user 1
2025-08-12 19:21:17.090 +03:00 [INF] Access token refreshed for user 1
2025-08-12 19:21:17.091 +03:00 [INF] Token refreshed successfully for user: 1
2025-08-12 19:21:17.092 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:21:17.093 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api) in 359.4268ms
2025-08-12 19:21:17.095 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-12 19:21:17.096 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - 200 null application/json; charset=utf-8 378.3364ms
2025-08-12 19:21:17.101 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:21:17.104 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:17.106 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:21:17.107 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-12 19:21:17.122 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-12 19:21:17.125 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:21:17.127 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 18.2721ms
2025-08-12 19:21:17.127 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:21:17.128 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:21:17.131 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:17.132 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 30.949ms
2025-08-12 19:21:17.133 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:21:17.138 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-12 19:21:17.143 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-12 19:21:17.147 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:21:17.149 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 9.3177ms
2025-08-12 19:21:17.151 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:21:17.153 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 25.8918ms
2025-08-12 19:21:35.409 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=true - null null
2025-08-12 19:21:35.412 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:35.414 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=true - 204 null null 4.951ms
2025-08-12 19:21:35.418 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - null null
2025-08-12 19:21:35.421 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:35.422 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-12 19:21:35.428 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 19:21:35.518 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
2025-08-12 19:21:35.529 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 19:21:35.536 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 106.8378ms
2025-08-12 19:21:35.539 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-12 19:21:35.540 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - 200 null application/json; charset=utf-8 121.9195ms
2025-08-12 19:21:38.420 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:21:38.425 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:38.427 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 6.2825ms
2025-08-12 19:21:38.430 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:21:38.432 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:21:38.433 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:21:38.434 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 19:21:38.440 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:21:38.444 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:21:38.447 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:21:38.451 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:21:38.453 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 19:21:38.455 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 18.193ms
2025-08-12 19:21:38.457 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:21:38.458 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 27.9639ms
2025-08-12 19:22:21.278 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-12 19:22:21.288 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:22:21.291 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 12.5949ms
2025-08-12 19:22:21.292 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:22:21.308 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-12 19:22:21.311 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:22:21.314 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:22:21.315 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 23.4438ms
2025-08-12 19:22:21.317 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:22:21.325 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-12 19:22:21.326 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-12 19:22:21.328 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:22:21.332 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:22:21.334 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-12 19:22:21.334 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-12 19:22:21.339 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-12 19:22:21.343 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 12.37ms
2025-08-12 19:22:21.343 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:22:21.345 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-12 19:22:21.348 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 39.7219ms
2025-08-12 19:22:21.349 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:22:21.361 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:22:21.366 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-12 19:22:21.368 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-12 19:22:21.370 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 29.7011ms
2025-08-12 19:22:21.372 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-12 19:22:21.373 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 48.1906ms
2025-08-12 19:26:19.560 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/logout - null null
2025-08-12 19:26:19.569 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:26:19.570 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/logout - 204 null null 10.1524ms
2025-08-12 19:26:19.575 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/logout - application/json 2
2025-08-12 19:26:19.589 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:26:19.591 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-12 19:26:19.593 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-12 19:26:19.595 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/logout - 401 0 null 20.1462ms
2025-08-12 19:26:19.604 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/logout - application/json 2
2025-08-12 19:26:19.608 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:26:19.610 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-12 19:26:19.613 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-12 19:26:19.614 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/logout - 401 0 null 9.7889ms
2025-08-12 19:26:19.694 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/logout - application/json 2
2025-08-12 19:26:19.708 +03:00 [INF] CORS policy execution successful.
2025-08-12 19:26:19.718 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-12 19:26:19.723 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-12 19:26:19.724 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/logout - 499 null null 29.76ms
