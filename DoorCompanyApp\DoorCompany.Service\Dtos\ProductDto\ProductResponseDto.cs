﻿using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos.BasicDto;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos.ProductDto
{
    public class ProductResponseDto : BaseNameDto
    {
        public string Code { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty;
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
       
        [Column(TypeName = "decimal(18,2)")]
        public decimal StandardCost { get; set; }//التكلفة القياسية

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinimumStock { get; set; }//الحد الأدنى للمخزون

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaximumStock { get; set; }// الحد الأقصى للمخزون
        public decimal OpeningBalance { get; set; }
        public decimal? Balance { get; set; }
        public int? SortOrder { get; set; } = 1;
        public int? ItemType { get; set; } = 1;
        public string ImagePath { get; set; } = string.Empty;   
      
    }

    public class CreateProductDto : BaseNameCreateDto {

        [Required(ErrorMessage = "كود مطلوب")]
        public string Code { get; set; } = string.Empty;
        [Required(ErrorMessage = "الباركود مطلوب")]
        public string Barcode { get; set; } = string.Empty;

        [Required(ErrorMessage = "التصنيف مطلوب")]
        public int CategoryId { get; set; }
        [Required(ErrorMessage = "الوحدة مطلوبة")]
        public int UnitId { get; set; }

        [Required(ErrorMessage = "التكلفة القياسية مطلوبة")]
        [Range(0.01, double.MaxValue, ErrorMessage = "التكلفة يجب أن تكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal StandardCost { get; set; }//التكلفة القياسية

       
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinimumStock { get; set; } = 0;//الحد الأدنى للمخزون

       
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaximumStock { get; set; } = 0;// الحد الأقصى للمخزون
      
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;
        public int? SortOrder { get; set; } = 1;
        public int? ItemType { get; set; } = 1;
        public FormFile? ImagePath { get; set; }       
    }




    public class CreateCategoryDto : BaseNameCreateDto
    {
        public int? ParentCategoryId { get; set; }
        public string? Code { get; set; }
        public string? Symbol { get; set; }
        public int? CategoryTypeId { get; set; }
        public IFormFile? ImageFile { get; set; } // للرفع
       public int? SortOrder { get; set; } = 1;
    }

    public class CategoryResponseDto : BaseNameDto
    {
        public int? ParentCategoryId { get; set; }
        public string? ParentCategoryName { get; set; }
        public string? Code { get; set; }
        public string? Symbol { get; set; }
        public int? CategoryTypeId { get; set; }
        public string? ImageUrl { get; set; }
        public int? SortOrder { get; set; }
        public int Level { get; set; } = 0;
        public bool HasChildren { get; set; }
        public List<CategoryResponseDto>? Children { get; set; } = new();
    }

}
