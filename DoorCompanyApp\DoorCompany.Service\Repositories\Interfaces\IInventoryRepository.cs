using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.InventoryDto;

namespace DoorCompany.Service.Repositories.Interfaces
{
    
    public interface IInventoryRepository
    {
        // إضافة حركة مخزون
        Task<ApiResponse<bool>> AddInventoryTransactionAsync(InventoryTransaction transaction);
        
        // إضافة حركات مخزون متعددة (للفواتير)
        Task<ApiResponse<bool>> AddInventoryTransactionsAsync(List<InventoryTransaction> transactions);
        
        // تحديث رصيد المنتج
        Task<ApiResponse<bool>> UpdateProductInventoryAsync(int productId, decimal quantity, decimal unitCost, InventoryTransactionType transactionType);
        
        // الحصول على رصيد منتج
        Task<ApiResponse<ProductInventoryDto>> GetProductInventoryAsync(int productId);
        
        // الحصول على تقرير المخزون
        Task<ApiResponse<PagedResponse<ProductInventoryDto>>> GetInventoryReportAsync(InventoryFilterDto filterDto);
        
        // الحصول على حركات منتج معين
        Task<ApiResponse<PagedResponse<InventoryTransactionDto>>> GetProductTransactionsAsync(int productId, InventoryFilterDto filterDto);
        
        // الحصول على حركات المخزون العامة
        Task<ApiResponse<PagedResponse<InventoryTransactionDto>>> GetInventoryTransactionsAsync(InventoryFilterDto filterDto);
        
        // التحقق من توفر الكمية المطلوبة
        Task<ApiResponse<bool>> CheckProductAvailabilityAsync(int productId, decimal requiredQuantity);
        
        // التحقق من توفر منتجات متعددة
        Task<ApiResponse<List<ProductAvailabilityDto>>> CheckProductsAvailabilityAsync(List<ProductQuantityDto> products);
        
        // حساب متوسط التكلفة للمنتج
        Task<ApiResponse<decimal>> CalculateAverageCostAsync(int productId);
        
        // إعادة حساب أرصدة المخزون (للصيانة)
        Task<ApiResponse<bool>> RecalculateInventoryAsync();
        
        // الحصول على المنتجات التي وصلت للحد الأدنى
        Task<ApiResponse<List<ProductInventoryDto>>> GetLowStockProductsAsync();
        
        // الحصول على المنتجات التي تجاوزت الحد الأقصى
        Task<ApiResponse<List<ProductInventoryDto>>> GetOverStockProductsAsync();
    }

    // DTOs للمخزون
    // public class ProductInventoryDto
    // {
    //     public int ProductId { get; set; }
    //     public string ProductName { get; set; } = string.Empty;
    //     public string ProductCode { get; set; } = string.Empty;
    //     public decimal Balance { get; set; }
    //     public decimal AverageCost { get; set; }
    //     public decimal TotalValue { get; set; }
    //     public decimal? MinimumStock { get; set; }
    //     public decimal? MaximumStock { get; set; }
    //     public bool IsLowStock { get; set; }
    //     public bool IsOverStock { get; set; }
    //     public DateTime LastTransactionDate { get; set; }
    // }

    // public class InventoryTransactionDto
    // {
    //     public int Id { get; set; }
    //     public DateTime TransactionDate { get; set; }
    //     public InventoryTransactionType Type { get; set; }
    //     public string TypeName { get; set; } = string.Empty;
    //     public int ProductId { get; set; }
    //     public string ProductName { get; set; } = string.Empty;
    //     public decimal Quantity { get; set; }
    //     public decimal UnitCost { get; set; }
    //     public decimal TotalCost { get; set; }
    //     public decimal BalanceAfter { get; set; }
    //     public decimal AverageCost { get; set; }
    //     public string? ReferenceNumber { get; set; }
    //     public string? Description { get; set; }
    // }

    // public class InventoryFilterDto
    // {
    //     public int PageNumber { get; set; } = 1;
    //     public int PageSize { get; set; } = 10;
    //     public int? ProductId { get; set; }
    //     public InventoryTransactionType? TransactionType { get; set; }
    //     public DateTime? FromDate { get; set; }
    //     public DateTime? ToDate { get; set; }
    //     public string? SearchTerm { get; set; }
    //     public bool? IsLowStock { get; set; }
    //     public bool? IsOverStock { get; set; }
    // }

    // public class ProductAvailabilityDto
    // {
    //     public int ProductId { get; set; }
    //     public string ProductName { get; set; } = string.Empty;
    //     public decimal RequiredQuantity { get; set; }
    //     public decimal AvailableQuantity { get; set; }
    //     public bool IsAvailable { get; set; }
    //     public decimal ShortageQuantity { get; set; }
    // }

    // public class ProductQuantityDto
    // {
    //     public int ProductId { get; set; }
    //     public decimal Quantity { get; set; }
    // }
}
