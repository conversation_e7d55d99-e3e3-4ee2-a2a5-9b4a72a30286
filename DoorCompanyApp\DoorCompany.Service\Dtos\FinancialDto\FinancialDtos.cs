using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos.BasicDto;
using DoorCompany.Service.Dtos.ProductDto;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DoorCompany.Service.Dtos.FinancialDto
{
    // DTOs للمعاملات المالية
    public class FinancialTransactionDto
    {
        public int Id { get; set; }
        public DateTime TransactionDate { get; set; }
        public TransactionType TransactionType { get; set; }
        public string TransactionTypeName { get; set; } = string.Empty;
        public AccountType AccountType { get; set; }
        public string AccountTypeName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int? ReferenceId { get; set; }
        public ReferenceType ReferenceType { get; set; }
        public string ReferenceTypeName { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public class CreateFinancialTransactionDto
    {
        public DateTime TransactionDate { get; set; } = DateTime.Now;
        public TransactionType TransactionType { get; set; }
        public AccountType AccountType { get; set; }
        public decimal Amount { get; set; }
        public int? ReferenceId { get; set; }
        public ReferenceType ReferenceType { get; set; } = ReferenceType.None;
        public string? Description { get; set; }
    }

    public class UpdateFinancialTransactionDto
    {
        public DateTime TransactionDate { get; set; }
        public TransactionType TransactionType { get; set; }
        public AccountType AccountType { get; set; }
        public decimal Amount { get; set; }
        public int? ReferenceId { get; set; }
        public ReferenceType ReferenceType { get; set; }
        public string? Description { get; set; }
    }

    public class FinancialFilterDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public TransactionType? TransactionType { get; set; }
        public AccountType? AccountType { get; set; }
        public ReferenceType? ReferenceType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public string? SearchTerm { get; set; }
    }

    public class CashBalanceDto
    {
        public decimal CashBalance { get; set; }
        public decimal BankBalance { get; set; }
        public decimal TotalBalance { get; set; }
        public DateTime LastUpdateDate { get; set; }
    }

    public class CashReportDto
    {
        public decimal OpeningBalance { get; set; }
        public decimal TotalIncome { get; set; }
        public decimal TotalExpense { get; set; }
        public decimal NetChange { get; set; }
        public decimal ClosingBalance { get; set; }
        public List<FinancialTransactionDto> Transactions { get; set; } = new();
    }

    public class CustomerBalanceDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal Balance { get; set; }
        public decimal CreditLimit { get; set; }
        public decimal AvailableCredit { get; set; }
    }

    public class SupplierBalanceDto
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal Balance { get; set; }
    }

    public class TransferDto
    {
        public AccountType FromAccount { get; set; }
        public AccountType ToAccount { get; set; }
        public decimal Amount { get; set; }
        public string? Description { get; set; }
        public DateTime TransactionDate { get; set; } = DateTime.Now;
    }
}
