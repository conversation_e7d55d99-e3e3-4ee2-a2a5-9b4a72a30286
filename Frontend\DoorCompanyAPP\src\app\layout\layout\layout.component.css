 .layout-container {
      display: flex;
      height: 100vh;
      direction: rtl;
      font-family: 'Cairo', Arial, sans-serif;
      position: relative;
    }

    /* Mobile Overlay */
    .mobile-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 99;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .mobile-overlay.active {
      opacity: 1;
      visibility: visible;
    }

    .sidebar {
      width: 280px;
      background: #1e293b;
      color: white;
      transition: all 0.3s ease;
    /*   overflow: hidden;/* */
      overflow-y: Scroll;
      position: relative;
      z-index: 100;
      box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    }

    .sidebar.collapsed {
      width: 70px;
    }

    .sidebar-header {
      background: #0f172a;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 64px;
      box-sizing: border-box;
      border-bottom: 1px solid #334155;
    }

    .sidebar-header h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      white-space: nowrap;
      transition: opacity 0.3s ease;
    }

    .toggle-btn {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      padding: 8px;
      border-radius: 6px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .toggle-btn:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    /* Hamburger Icon */
    .hamburger-icon {
      display: flex;
      flex-direction: column;
      width: 20px;
      height: 16px;
      position: relative;
    }

    .hamburger-icon span {
      display: block;
      height: 2px;
      width: 100%;
      background: white;
      border-radius: 1px;
      transition: all 0.3s ease;
      transform-origin: center;
    }

    .hamburger-icon span:nth-child(1) {
      margin-bottom: 5px;
    }

    .hamburger-icon span:nth-child(2) {
      margin-bottom: 5px;
    }

    .nav-list {
      padding: 16px 0;
      overflow-y: auto;
      flex: 1;
    }

    .nav-item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      color: #cbd5e1;
      text-decoration: none;
      transition: all 0.3s ease;
      margin: 2px 8px;
      border-radius: 8px;
      position: relative;
      min-height: 44px;
      box-sizing: border-box;
    }

    .nav-item:hover {
      background: #334155;
      color: white;
      transform: translateX(2px);
    }

    .nav-item.active {
      background: #3b82f6;
      color: white;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }

    .nav-icon {
      font-size: 20px;
      margin-left: 12px;
      min-width: 24px;
      text-align: center;
      transition: all 0.3s ease;
    }

    .nav-text {
      white-space: nowrap;
      font-weight: 700;
      transition: all 0.3s ease;
      font-family: "cairo", Arial, sans-serif;
      font-size: 20px;
    }

    /* Collapsed sidebar styles */
    .sidebar.collapsed .nav-item {
      padding: 12px;
      justify-content: center;
      margin: 2px 4px;
    }

    .sidebar.collapsed .nav-icon {
      margin-left: 0;
      font-size: 22px;
    }

    .nav-divider {
      height: 1px;
      background: #334155;
      margin: 16px 20px;
    }

    .admin-section {
      margin-top: 16px;
    }

    .section-title {
      font-size: 12px;
      font-weight: 600;
      color: #64748b;
      margin: 16px 20px 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .main-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: #f8fafc;
    }

    .top-bar {
      background: white;
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 64px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      z-index: 10;
    }

    .top-bar-left {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .menu-btn {
      background: green;
      border: none;
      font-size: 20px;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: background 0.3s ease;
    }

    .menu-btn:hover {
      background: #f1f5f9;
    }

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
    }

    .top-bar-right {
      position: relative;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: #f8fafc;
      border-radius: 8px;
      cursor: pointer;
      transition: background 0.3s ease;
    }
    
    .user-cover{
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #3b82f6;
    }  


    .user-info:hover {
      background: #e2e8f0;
    }

    .user-name {
      font-weight: 500;
      color: #1e293b;
    }

    .user-avatar {
      font-size: 20px;
    }

    .dropdown-arrow {
      font-size: 12px;
      color: #64748b;
    }

    .user-menu {
      position: absolute;
      top: 100%;
      left: 0;
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      min-width: 200px;
      z-index: 20;
    }

    .menu-item {
      display: block;
      padding: 12px 16px;
      color: #1e293b;
      text-decoration: none;
      transition: background 0.3s ease;
    }

    .menu-item:hover {
      background: #f8fafc;
    }

    .menu-divider {
      height: 1px;
      background: #e2e8f0;
      margin: 8px 0;
    }

    .page-content {
      flex: 1;
      overflow-y: auto;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
      .sidebar {
        width: 260px;
      }

      .sidebar.collapsed {
        width: 60px;
      }
    }

    @media (max-width: 768px) {
      .layout-container {
        overflow: hidden;
      }

      .sidebar {
        position: fixed;
        z-index: 100;
        height: 100vh;
        width: 280px !important;
        right: 0;
        top: 0;
        transform: translateX(100%);
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3);
      }

      .sidebar.mobile-open {
        transform: translateX(0);
      }

      .sidebar.collapsed {
        width: 280px !important;
        transform: translateX(100%);
      }

      .sidebar.collapsed.mobile-open {
        transform: translateX(0);
      }

      .main-container {
        width: 100%;
        margin-right: 0;
      }

      .top-bar {
        padding: 0 16px;
      }

      .page-title {
        font-size: 18px;
      }

      .user-name {
        display: none;
      }

      .nav-item {
        padding: 14px 20px;
        margin: 2px 8px;
      }

      .nav-icon {
        font-size: 22px;
        margin-left: 16px;
      }

      .nav-text {
        font-size: 16px;
      }

      /* تحسين user-menu للـ RTL */
      .user-menu {
        right: 0;
        margin-right: auto;
      }
    }

    @media (max-width: 480px) {
      .top-bar {
        padding: 0 12px;
      }

      .page-title {
        font-size: 16px;
      }

      .sidebar {
        width: 260px !important;
      }

      .nav-item {
        padding: 12px 16px;
      }

      .sidebar-header {
        padding: 12px 16px;
      }

      .sidebar-header h2 {
        font-size: 16px;
      }
    }

    /* Tablet Portrait */
    @media (max-width: 768px) and (orientation: portrait) {
      .sidebar {
        width: 300px !important;
      }
    }

    /* Tablet Landscape */
    @media (max-width: 1024px) and (orientation: landscape) {
      .sidebar {
        width: 240px;
      }

      .sidebar.collapsed {
        width: 60px;
      }
    }
  
  




    /* تنسيق العنصر الذي يفتح القائمة */
.nav-item.dropdown-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

/* سهم التوجيه */
.dropdown-arrow {
  transition: transform 0.3s ease;
  font-size: 0.7em;
}

.dropdown-arrow.open {
  transform: rotate(180deg); /* يدور السهم عند الفتح */
}

/* القائمة المنسدلة */
.dropdown-menu {
  overflow: hidden;
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
    padding-top: 0;
  }
  to {
    max-height: 200px;
    opacity: 1;
    padding-top: 5px;
  }
}

/* العناصر الفرعية */
.nav-item.nested {
  padding: 8px 20px;
  font-size: 0.95em;
  color: #ee1b1b;
}

.nav-item.nested:hover,
.nav-item.nested.active {
  background-color: #1e88e5;
  color: rgb(122, 236, 160);
}