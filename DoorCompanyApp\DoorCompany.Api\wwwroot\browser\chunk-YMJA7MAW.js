import{ga as c}from"./chunk-5FTQYYYZ.js";import{H as p,R as i,ec as a,m as n,w as f}from"./chunk-KFKFGDWN.js";var T=(o,s)=>{let e=i(c),l=i(a),u=e.tokenValue,r=o;return u&&!e.isTokenExpired()&&(r=o.clone({setHeaders:{Authorization:`Bearer ${u}`}})),s(r).pipe(f(t=>t.status===401&&t.error===null?k(r,s,e,l):n(()=>t)))};function k(o,s,e,l){return e.isTokenExpired()?(e.forceLogout(),n(()=>new Error("Session invalid: Token rejected by server"))):e.getRefreshToken()?e.isRefreshing?e.refreshTokenSubject.pipe(p(r=>{if(r){let t=o.clone({setHeaders:{Authorization:`Bearer ${r}`}});return s(t)}else return n(()=>new Error("Refresh token failed"))}),f(r=>(e.forceLogout(),n(()=>r)))):(e.startRefresh(),e.refreshToken().pipe(p(r=>{if(r.succeeded&&r.data?.accessToken){let t=r.data.accessToken;e.endRefresh(t);let d=o.clone({setHeaders:{Authorization:`Bearer ${t}`}});return s(d)}else return e.endRefresh(null),e.forceLogout(),n(()=>new Error("Token refresh failed"))}),f(r=>(e.endRefresh(null),e.forceLogout(),n(()=>r))))):(e.forceLogout(),n(()=>new Error("No refresh token available")))}export{T as a};
