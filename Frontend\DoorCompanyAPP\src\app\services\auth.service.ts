import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, tap, throwError } from 'rxjs';
import { ApiResponse } from '../models/api-response.model';
import { User, LoginRequest, LoginResponse, UserPermissions } from '../models/user.model';
import { ApiService } from './api.service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  
   private currentUserSubject = new BehaviorSubject<User | null>(null);
   private tokenSubject = new BehaviorSubject<string | null>(null);


  public currentUser$ = this.currentUserSubject.asObservable();
  public token$ = this.tokenSubject.asObservable();

   // ✅ لمراقبة حالة التحديث
  isRefreshing = false;
  refreshTokenSubject = new BehaviorSubject<string | null>(null);
  public refreshedtoken$ = this.refreshTokenSubject.asObservable();

  constructor(private apiService: ApiService, private router: Router) {
    // Load user and token from localStorage on service initialization
    if (typeof localStorage !== 'undefined') {
    //  const storedUser = localStorage.getItem('currentUser');
      const storedToken = localStorage.getItem('token');
      const storedRefreshToken = localStorage.getItem('refreshToken');
      if (storedToken && storedRefreshToken) {
        this.currentUserSubject.next(null); // Assuming user is not stored in localStorage
        this.tokenSubject.next(storedToken);
        this.refreshTokenSubject.next(storedRefreshToken);
      }
    }
  }

  get currentUserValue(): User | null {
     return this.currentUserSubject.value;
  }

  get tokenValue(): string | null {
    const token = this.tokenSubject.value;
      return token;
  }

getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }


// ✅ دالة تبدأ التحديث
startRefresh(): void {
  this.isRefreshing = true;
  this.refreshTokenSubject.next(null);
}

// ✅ دالة تُنهي التحديث وتنشر التوكن الجديد
endRefresh(token: string | null): void {
  this.isRefreshing = false;
  this.refreshTokenSubject.next(token);
}



  get isLoggedIn(): boolean {
    return !!this.tokenValue // && !!this.currentUserValue;
  }

  //Login method
  login(loginRequest: LoginRequest) : Observable<ApiResponse<LoginResponse>> {
    const endpoint = 'Auth/login';
    const body = loginRequest;    
    return this.apiService.post<LoginResponse>(endpoint, body)
      .pipe(
        tap(response => {
          if (response.succeeded && response.data) {
            // Store user and token
            if (typeof localStorage !== 'undefined') {
             // localStorage.setItem('currentUser', JSON.stringify(response.data.roles));
              localStorage.setItem('token', response.data.accessToken);
              localStorage.setItem('tokenExpiry', response.data.tokenExpiry.toString());
              localStorage.setItem('refreshToken', response.data.refreshToken.toString());
            }
            // Update subjects
              const decodedToken = this.parseJwt(response.data.accessToken);

              // ✅ استخراج الصلاحيات
              const permissions = decodedToken.permission || [];
              this.getUserPermissions(permissions);
          this.getCurrentUser().subscribe(userResponse => {
            if (userResponse.succeeded && userResponse.data) {
              this.currentUserSubject.next(userResponse.data);
            }
          });
            this.tokenSubject.next(response.data.accessToken);  
            this.refreshTokenSubject.next(response.data.refreshToken);       
          }
          
        })
      );
  }

 
  getCurrentUser(): Observable<ApiResponse<User>> {
    return this.apiService.get<User>(`User/profile`)
       .pipe(
               tap(response => {
          if (response.succeeded && response.data) {       
            this.currentUserSubject.next(response.data);
          }
        })
      );
    }

  logout(): Observable<ApiResponse<boolean>> {
    return this.apiService.post<boolean>(`Auth/logout`, {})
      .pipe(
        tap(() => {
         
        })
      );
  }

  forceLogout(): void {
    this.clearLocalStorage();
    this.router.navigate(['/auth/login']);
    
  }

  private clearLocalStorage(): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('currentUser');
      localStorage.removeItem('token');
      localStorage.removeItem('tokenExpiry');
      localStorage.removeItem('refreshToken');
    }
    this.currentUserSubject.next(null);
    this.tokenSubject.next(null);
    this.refreshTokenSubject.next(null);
  
  }

  isTokenExpired(): boolean {
    if (typeof localStorage === 'undefined') return true;

  const expiryStr = localStorage.getItem('tokenExpiry');
  if (!expiryStr) return true;

  try {
    const expiryDate = new Date(expiryStr);
    return isNaN(expiryDate.getTime()) ? true : expiryDate.getTime() <= new Date().getTime();
  } catch {
    return true;
  }
  }

  hasRole(permission: string): boolean {
    const user = this.currentUserValue;
    return user ? !!user.permissions.find(p => p.userRole === permission) : false;
  }


getUserPermissions(permission : string[]): string[] {
      return permission.map(p => p);
};

viewUserPermissions(): UserPermissions {
 const decodedToken = this.parseJwt(this.tokenValue || '');
   const permissions = decodedToken.permission || [];
    return { permissions };
  }

 hasPermission(permission: string): boolean {
    const permissions = this.viewUserPermissions().permissions;
    return permissions ? !!permissions.find(p => p === permission) : false;
  }
  // hasAnyRole(roles: string[]): boolean {
  //   const user = this.currentUserValue;
  //   return user ? user.permissions.some(p => roles.includes(p.userRole)) : false;
  // }


  refreshToken(): Observable<ApiResponse<LoginResponse>> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
          this.clearLocalStorage();
          this.router.navigate(['/auth/login']);
          return throwError(() => new Error('No refresh token available'));
        }    
    const refreshTokenRequest = { refreshToken };
   
    return this.apiService.post<LoginResponse>(`Auth/refresh-token`, refreshTokenRequest)
      .pipe(
        tap(response => {
          if (response.succeeded && response.statusCode === 200 ) {
            // Update localStorage with new tokens
            if (typeof localStorage !== 'undefined') {
              localStorage.setItem('token', response.data.accessToken);
              localStorage.setItem('tokenExpiry', response.data.tokenExpiry.toString());
              localStorage.setItem('refreshToken', response.data.refreshToken.toString());
            }
            // Update subjects
            this.tokenSubject.next(response.data.accessToken);
            // this.getCurrentUser().subscribe(userResponse => {
            //   if (userResponse.succeeded && userResponse.data) {
            //     this.currentUserSubject.next(userResponse.data);
            //   }
            // });
          }else {
            this.clearLocalStorage();
            this.router.navigate(['/auth/login']);
          }
        })
      );
  }

 parseJwt(token: string): any {
  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(
    atob(base64)
      .split('')
      .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
      .join('')
  );

  return JSON.parse(jsonPayload);
}

}
