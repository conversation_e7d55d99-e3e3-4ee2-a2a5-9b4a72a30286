import './polyfills.server.mjs';
import{ga as f}from"./chunk-UDSHM75V.mjs";import{Ba as n,Fa as c,Ga as m,L as a,kc as p,wa as o,za as s}from"./chunk-JADE4TID.mjs";var h=class t{static \u0275fac=function(e){return new(e||t)};static \u0275mod=c({type:t});static \u0275inj=a({imports:[p]})};var l=class t{constructor(i,e,r){this.templateRef=i;this.viewContainer=e;this.authService=r}hasView=!1;set appHasPermission(i){let e=this.authService.viewUserPermissions(),r=Array.isArray(i)?i.some(u=>e.permissions.includes(u)):e.permissions.includes(i);r&&!this.hasView?(this.viewContainer.createEmbeddedView(this.templateRef),this.hasView=!0):!r&&this.hasView&&(this.viewContainer.clear(),this.hasView=!1)}static \u0275fac=function(e){return new(e||t)(s(o),s(n),s(f))};static \u0275dir=m({type:t,selectors:[["","appHasPermission",""]],inputs:{appHasPermission:"appHasPermission"},standalone:!1})};export{l as a,h as b};
