﻿using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.PartnerDto;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Interfaces
{
    public interface IPartnerRepository
    {
        Task<ApiResponse<List<PartnerResponseDto>>> GetAllPartnersAsync(bool isDelete = false);
        Task<ApiResponse<PartnerResponseDto>> GetPartnerByIdAsync(int id);
        Task<ApiResponse<PartnerResponseDto>> CreatePartnerAsycn(CreatePartnerDto createDto);
        Task<ApiResponse<PartnerResponseDto>> UpdatePartnerAsycn(UpdatePartnerDto Dto);
        Task<ApiResponse<bool>> DeletePartnerAsync(int id);

        //'------------------------------------------------------------------------'
        Task<ApiResponse<List<PartnerBandResponseDto>>> GetAllPartnersBandAsync(bool isDelete = false);
        Task<ApiResponse<PartnerBandResponseDto>> GetPartnerBandByIdAsync(int id);
        Task<ApiResponse<PartnerBandResponseDto>> CreatePartnerBandAsycn(CreatePartnerBandDto createDto);
        Task<ApiResponse<PartnerBandResponseDto>> UpdatePartnerBandAsycn(UpdatePartnerBandDto Dto);
        Task<ApiResponse<bool>> DeletePartnerBandAsync(int id);

        //'------------------------------------------------------------------------'
        Task<ApiResponse<PagedResponse<PartnerTransactionResponseDto>>> GetAllPartnerTransactionAsync(PartnerTransactionRequestDto req);
        Task<ApiResponse<PartnerTransactionResponseDto>> GetPartnerTransactionByIdAsync(int id);
        Task<ApiResponse<PartnerTransactionResponseDto>> CreatePartnerTransactionAsync(CreatePartnerTransactionDto createDto);
        Task<ApiResponse<PartnerTransactionResponseDto>> UpdatePartnerTransactionAsycn(UpdatePartnerTransactionDto Dto);
        Task<ApiResponse<bool>> DeletePartnerTransationAsync(int id);
        Task<ApiResponse<List<PartnerTransactionResponseDto>>> GetReportPartnerTransactionAsync(PartnerTransactionRequestDto req);

        //''''''''''''''''''''''''''                '''''''''''''''''''//

        Task<ApiResponse<List<ShareTransferResponseDto>>> GetAllShareTransactionAsync(ShareTransferRequestDto  requestDto);
        Task<ApiResponse<ShareTransferResponseDto>> GetShareTransferByIdAsync(int id);

        Task<ApiResponse<ShareTransferResponseDto>> CreateShareTransferAsync(CreateShareTransferDto createDto);
        Task<ApiResponse<ShareTransferResponseDto>> UpdateShareTransferAsync(UpdateShareTransferDto createDto);

        Task<ApiResponse<List<PartnerDataSummary>>> GetAllPartnerDataSummer();

    }
}
