using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.FinancialDto;
using DoorCompany.Service.Dtos.ProductDto;

namespace DoorCompany.Service.Repositories.Interfaces
{
    public interface IFinancialRepository
    {
        // إضافة معاملة مالية
        Task<ApiResponse<FinancialTransactionDto>> AddFinancialTransactionAsync(CreateFinancialTransactionDto createDto);
        
        // تحديث معاملة مالية
        Task<ApiResponse<FinancialTransactionDto>> UpdateFinancialTransactionAsync(int transactionId, UpdateFinancialTransactionDto updateDto);
        
        // حذف معاملة مالية
        Task<ApiResponse<bool>> DeleteFinancialTransactionAsync(int transactionId);
        
        // الحصول على معاملة مالية
        Task<ApiResponse<FinancialTransactionDto>> GetFinancialTransactionAsync(int transactionId);
        
        // الحصول على المعاملات المالية
        Task<ApiResponse<PagedResponse<FinancialTransactionDto>>> GetFinancialTransactionsAsync(FinancialFilterDto filterDto);
        
        // تسجيل دفع فاتورة في الخزينة
        Task<ApiResponse<bool>> RecordInvoicePaymentAsync(int invoiceId, decimal amount, PaymentType paymentType, string? notes = null);
        
        // تسجيل سداد لمورد
        Task<ApiResponse<bool>> RecordSupplierPaymentAsync(int supplierId, decimal amount, PaymentType paymentType, string? notes = null);
        
        // تسجيل تحصيل من عميل
        Task<ApiResponse<bool>> RecordCustomerReceiptAsync(int customerId, decimal amount, PaymentType paymentType, string? notes = null);
        
        // الحصول على رصيد الخزينة
        Task<ApiResponse<CashBalanceDto>> GetCashBalanceAsync();
        
        // الحصول على تقرير الخزينة
        Task<ApiResponse<CashReportDto>> GetCashReportAsync(DateTime? fromDate = null, DateTime? toDate = null);
        
        // الحصول على حساب عميل
        Task<ApiResponse<CustomerAccountDto>> GetCustomerAccountAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null);
        
        // الحصول على حساب مورد
        Task<ApiResponse<SupplierAccountDto>> GetSupplierAccountAsync(int supplierId, DateTime? fromDate = null, DateTime? toDate = null);
        
        // الحصول على أرصدة العملاء
        Task<ApiResponse<PagedResponse<CustomerBalanceDto>>> GetCustomersBalancesAsync(int pageNumber = 1, int pageSize = 10);
        
        // الحصول على أرصدة الموردين
        Task<ApiResponse<PagedResponse<SupplierBalanceDto>>> GetSuppliersBalancesAsync(int pageNumber = 1, int pageSize = 10);
        
        // تحويل بين الحسابات
        Task<ApiResponse<bool>> TransferBetweenAccountsAsync(TransferDto transferDto);
    }

    // DTOs للمعاملات المالية
    // public class FinancialTransactionDto
    // {
    //     public int Id { get; set; }
    //     public DateTime TransactionDate { get; set; }
    //     public TransactionType TransactionType { get; set; }
    //     public string TransactionTypeName { get; set; } = string.Empty;
    //     public AccountType AccountType { get; set; }
    //     public string AccountTypeName { get; set; } = string.Empty;
    //     public decimal Amount { get; set; }
    //     public int? ReferenceId { get; set; }
    //     public ReferenceType ReferenceType { get; set; }
    //     public string ReferenceTypeName { get; set; } = string.Empty;
    //     public string? Description { get; set; }
    // }

    // public class CreateFinancialTransactionDto
    // {
    //     public DateTime TransactionDate { get; set; } = DateTime.Now;
    //     public TransactionType TransactionType { get; set; }
    //     public AccountType AccountType { get; set; }
    //     public decimal Amount { get; set; }
    //     public int? ReferenceId { get; set; }
    //     public ReferenceType ReferenceType { get; set; } = ReferenceType.None;
    //     public string? Description { get; set; }
    // }

    // public class UpdateFinancialTransactionDto
    // {
    //     public DateTime TransactionDate { get; set; }
    //     public TransactionType TransactionType { get; set; }
    //     public AccountType AccountType { get; set; }
    //     public decimal Amount { get; set; }
    //     public int? ReferenceId { get; set; }
    //     public ReferenceType ReferenceType { get; set; }
    //     public string? Description { get; set; }
    // }

    // public class FinancialFilterDto
    // {
    //     public int PageNumber { get; set; } = 1;
    //     public int PageSize { get; set; } = 10;
    //     public TransactionType? TransactionType { get; set; }
    //     public AccountType? AccountType { get; set; }
    //     public ReferenceType? ReferenceType { get; set; }
    //     public DateTime? FromDate { get; set; }
    //     public DateTime? ToDate { get; set; }
    //     public decimal? MinAmount { get; set; }
    //     public decimal? MaxAmount { get; set; }
    //     public string? SearchTerm { get; set; }
    // }

    // public class CashBalanceDto
    // {
    //     public decimal CashBalance { get; set; }
    //     public decimal BankBalance { get; set; }
    //     public decimal TotalBalance { get; set; }
    //     public DateTime LastUpdateDate { get; set; }
    // }

    // public class CashReportDto
    // {
    //     public decimal OpeningBalance { get; set; }
    //     public decimal TotalIncome { get; set; }
    //     public decimal TotalExpense { get; set; }
    //     public decimal NetChange { get; set; }
    //     public decimal ClosingBalance { get; set; }
    //     public List<FinancialTransactionDto> Transactions { get; set; } = new();
    // }

    // public class CustomerBalanceDto
    // {
    //     public int CustomerId { get; set; }
    //     public string CustomerName { get; set; } = string.Empty;
    //     public string? Phone { get; set; }
    //     public decimal TotalSales { get; set; }
    //     public decimal TotalPaid { get; set; }
    //     public decimal Balance { get; set; }
    //     public decimal CreditLimit { get; set; }
    //     public decimal AvailableCredit { get; set; }
    // }

    // public class SupplierBalanceDto
    // {
    //     public int SupplierId { get; set; }
    //     public string SupplierName { get; set; } = string.Empty;
    //     public string? Phone { get; set; }
    //     public decimal TotalPurchases { get; set; }
    //     public decimal TotalPaid { get; set; }
    //     public decimal Balance { get; set; }
    // }

    // public class TransferDto
    // {
    //     public AccountType FromAccount { get; set; }
    //     public AccountType ToAccount { get; set; }
    //     public decimal Amount { get; set; }
    //     public string? Description { get; set; }
    //     public DateTime TransactionDate { get; set; } = DateTime.Now;
    // }
}
