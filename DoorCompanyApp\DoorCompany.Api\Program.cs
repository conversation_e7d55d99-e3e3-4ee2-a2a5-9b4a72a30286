﻿using DoorComapany.Service.Authentication;
using DoorCompany.Api.Filters;
using DoorCompany.Data.Context;
using DoorCompany.Data.Seeds;
using DoorCompany.Service.Authentication;
using DoorCompany.Service.Mapping;
using DoorCompany.Service.Repositories.ModuleDependencies;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using System.Text;


// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();



var builder = WebApplication.CreateBuilder(args);

// Add Serilog
builder.Host.UseSerilog();


// Add services to the container.

builder.Services.AddControllers();

//// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
//builder.Services.AddEndpointsApiExplorer();
//builder.Services.AddSwaggerGen();

#region Swagger

// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
//builder.Services.AddEndpointsApiExplorer();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Door Company App API", Version = "v1" });

    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new()
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new()
    {
        {
            new()
            {
                Reference = new()
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });


});

#endregion

#region databaseConnections
//Connection To SQLServer 
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(
          builder.Configuration.GetConnectionString("DefaultConnection"),
          sqlServerOptions => sqlServerOptions.EnableRetryOnFailure(
            maxRetryCount: 5,
            maxRetryDelay: TimeSpan.FromSeconds(30),
            errorNumbersToAdd: null
        )


          ));
#endregion

#region AutoMapper
builder.Services.AddAutoMapper(typeof(MappingProfile));
#endregion

#region JWT Configuration
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));
var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>();

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = jwtSettings?.ValidateIssuerSigningKey ?? true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(jwtSettings?.SecretKey ?? "")),
        ValidateIssuer = jwtSettings?.ValidateIssuer ?? true,
        ValidIssuer = jwtSettings?.Issuer,
        ValidateAudience = jwtSettings?.ValidateAudience ?? true,
        ValidAudience = jwtSettings?.Audience,
        ValidateLifetime = jwtSettings?.ValidateLifetime ?? true,
        ClockSkew = TimeSpan.FromMinutes(jwtSettings?.ClockSkewMinutes ?? 5)
    };

    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            Log.Warning("JWT Authentication failed: {Error}", context.Exception.Message);
            return Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            Log.Debug("JWT Token validated for user: {User}", context.Principal?.Identity?.Name);
            return Task.CompletedTask;
        }
    };
});
#endregion

#region Authentication Services
builder.Services.AddScoped<IJwtTokenService, JwtTokenService>();
builder.Services.AddScoped<IPasswordHashingService, PasswordHashingService>();
//builder.Services.AddScoped<IUserContext, UserContext>();
builder.Services.AddHttpContextAccessor();
#endregion

#region Authorization Services
builder.Services.AddSingleton<IAuthorizationPolicyProvider, PermissionPolicyProvider>();
builder.Services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();
#endregion

#region Dependency injections
builder.Services.AddRepositoriesDependencies();
#endregion 

#region AllowCORS
var CORS = "_cors";
builder.Services.AddCors(options =>
{
    options.AddPolicy(name: CORS,
                      policy =>
                      {
                          policy.AllowAnyHeader();
                          policy.AllowAnyMethod();
                          policy.AllowAnyOrigin();
                      });
});

#endregion

builder.Services.AddHttpContextAccessor();



var app = builder.Build();

// تكوين خط أنابيب الطلبات HTTP
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// تهيئة البيانات الأولية (يجب معالجة الأخطاء)
try
{
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();
        // تنفيذ عمليات التهيئة بشكل متسلسل مع await
        await DefaultUsers.SeedBasicUserAsync(context);
        await DefaultUsers.SeedBasicRolesAsync(context);
        await DefaultUsers.AssignAllRolesToAdminAsync(context);
        await DefaultUsers.SeedBasicPermissionAsync(context);
        await DefaultUsers.SeedBasicMainTypeAsync(context);
        await DefaultUsers.SeedBasicMainActionAsync(context);
        await DefaultUsers.SeedCompanyAsync(context);
        await DefaultUsers.SeedUnitsAsync(context);
    }
}
catch (Exception ex)
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();
    logger.LogError(ex, "حدث خطأ أثناء تهيئة البيانات الأولية");
}

// إعادة توجيه HTTPS (يجب أن يكون أولاً)
app.UseHttpsRedirection();

// ملفات Angular الثابتة (من wwwroot)
app.UseStaticFiles();
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "wwwroot/browser")),
    RequestPath = ""
});
// التوجيه (Routing) - مهم لـ API والتصديق
app.UseRouting();

app.UseCors("_cors");

// المصادقة والتفويض
app.UseAuthentication();
app.UseAuthorization();


app.MapControllers();
//app.MapFallbackToFile("index.html");

app.MapFallbackToFile("index.html", new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "wwwroot/browser"))
});

app.Run();