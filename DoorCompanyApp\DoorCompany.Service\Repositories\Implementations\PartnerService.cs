﻿using AutoMapper;
using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Dtos.UserDto;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using DoorCompany.Service.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Implementations
{
    public class PartnerService : ResponseHandler, IPartnerRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<PartnerService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IFileUploadService _fileUploadService;
        public PartnerService(IUnitOfWorkOfService unitOfWork, IMapper mapper,
                              ILogger<PartnerService> logger, IHttpContextAccessor httpContextAccessor,
                             IFileUploadService fileUploadService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _fileUploadService = fileUploadService;
        }

        #region Partner
        public async Task<ApiResponse<List<PartnerResponseDto>>> GetAllPartnersAsync(bool isDelete = false)
        {
            try
            {
                var query = _unitOfWork.Partners.GetTableNoTracking();


                if (!isDelete)
                    query = query.Where(p => !p.IsDeleted);

                var partners = await query.ToListAsync();

                if (partners.Count == 0)
                    return NotFound<List<PartnerResponseDto>>("لا يوجد شركاء");

                var partnerDto = _mapper.Map<List<PartnerResponseDto>>(partners);
                return Success(partnerDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all partners");
                return BadRequest<List<PartnerResponseDto>>("حدث خطأ أثناء جلب بيانات الشركاء");
            }
        }
        public async Task<ApiResponse<PartnerResponseDto>> GetPartnerByIdAsync(int id)
        {
            try
            {
                var partner = await _unitOfWork.Partners.GetTableNoTracking()
                    .Include(p => p.PartnerTransations)
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (partner == null)
                    return NotFound<PartnerResponseDto>("الشريك غير موجود");

                var allPartners = await _unitOfWork.Partners.GetTableNoTracking()
                   .Where(p => !p.IsDeleted)
                   .Include(p => p.PartnerTransations)
                   .ToListAsync();


                var partnerDto = _mapper.Map<PartnerResponseDto>(partner);


                // Calculate financial data
                var transactions = partner.PartnerTransations.Where(t => !t.IsDeleted).ToList();

                partnerDto.TotalInvestments = transactions.Where(t => t.ActionDetailId == 2).Sum(t => t.Amount);
                partnerDto.TotalWithdrawals = transactions.Where(t => t.ActionDetailId == 3).Sum(t => t.Amount);
                partnerDto.CurrentCapital = (partner.InitialCapital ?? 0) + partnerDto.TotalInvestments - partnerDto.TotalWithdrawals;

                // حساب رأس المال الكلي لجميع الشركاء
                decimal allInitialCapital = Convert.ToDecimal(allPartners.Sum(p => p.InitialCapital));

                //جلب عدد اسهم الشركة المطروحة
                var TotalShareCompany = await GetCountShareCompanyAsync();
                var TotalAllInvestmentCompany = await GetAllInvestmentsAsync();
                decimal ShareValueCompany = TotalAllInvestmentCompany / TotalShareCompany;


                decimal allTotalInvestments = allPartners
                    .SelectMany(p => p.PartnerTransations)
                    .Where(t => !t.IsDeleted && t.ActionDetailId == 2)
                    .Sum(t => t.Amount);

                decimal allTotalWithdrawals = allPartners
                    .SelectMany(p => p.PartnerTransations)
                    .Where(t => !t.IsDeleted && t.ActionDetailId == 3)
                    .Sum(t => t.Amount);

                decimal totalAllCapital = allInitialCapital + allTotalInvestments - allTotalWithdrawals;

                // حساب نسبة الشريك من رأس المال
                partnerDto.SharePercentage = totalAllCapital == 0
                    ? 0
                    : (partnerDto.CurrentCapital / totalAllCapital) * 100;

                partnerDto.InitialShareValue = ShareValueCompany;
                partnerDto.InitialShareCount = Convert.ToInt32(partnerDto.CurrentCapital / Convert.ToDecimal(ShareValueCompany));

                var shares = await GetPartnerSharesSummary(id);

                partnerDto.CurrentShare = partnerDto.InitialShareCount + shares.TotalSharesBuyer - shares.TotalSharesSeller;

                return Success(partnerDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting partner by id: {PartnerId}", id);
                return BadRequest<PartnerResponseDto>("حدث خطأ أثناء جلب بيانات الشريك");
            }
        }
        public async Task<ApiResponse<PartnerResponseDto>> CreatePartnerAsycn(CreatePartnerDto createDto)
        {
            //اولا التحقق من ان اسم الشريك مش موجود
            try
            {

                // Check if username already exists
                var existingPartner = await _unitOfWork.Partners.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.Name == createDto.Name && !u.IsDeleted);

                if (existingPartner != null)
                    return BadRequest<PartnerResponseDto>("اسم الشريك موجود بالفعل");

                var partner = _mapper.Map<Partner>(createDto);


                await _unitOfWork.Partners.AddAsync(partner);

                //var TotalCapital = await GetAllInvestmentsAsync();
                //decimal InitialCapitalPercentage = partner.InitialCapital ?? 0 / Convert.ToDecimal(TotalCapital);
                //var TotalShare = await GetCountShareCompanyAsync();
                ////عمل حسابات الشريك الاوليه
                //var shareCreate = new ShareDistribution()
                //{
                //    DistributionDate = DateTime.Now,
                //    Description = "تخصيص الأسهم الأولي",
                //    PartnerId = partner.Id,
                //    SharesCount = Convert.ToInt32(InitialCapitalPercentage / Convert.ToInt32(TotalShare)),
                //    ShareValue = Convert.ToDecimal(Convert.ToDecimal(TotalCapital) / Convert.ToInt32(TotalShare)),
                //    CreatedAt = DateTime.Now
                //};

                //await _unitOfWork.ShareDistributions.AddAsync(shareCreate);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Partner created successfully: {name}", partner.Name);

                var result = await GetPartnerByIdAsync(partner.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating partner: {name}", createDto.Name);
                return BadRequest<PartnerResponseDto>("حدث خطأ أثناء إنشاء الشريك");
            }
        }
        public async Task<ApiResponse<PartnerResponseDto>> UpdatePartnerAsycn(UpdatePartnerDto Dto)
        {
            try
            {
                var existingpartner = await _unitOfWork.Partners.GetByIdAsync(Dto.Id);
                if (Dto.IsDeleted == true)
                {
                    if (existingpartner == null || !existingpartner.IsDeleted)
                        return NotFound<PartnerResponseDto>("الشريك غير موجود");
                }

                if (existingpartner == null)
                    return NotFound<PartnerResponseDto>("الشريك غير موجود");
                // Check if username is taken by another user
                var duplicateUser = await _unitOfWork.Partners.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.Name == Dto.Name && u.Id != Dto.Id && !u.IsDeleted);

                if (duplicateUser != null)
                    return BadRequest<PartnerResponseDto>("اسم الشريك موجود بالفعل");

                _mapper.Map(Dto, existingpartner);
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Partners.UpdateAsync(existingpartner);

                ////عمل الحسابات 
                //var TotalCapital = await GetAllInvestmentsAsync();
                //decimal InitialCapitalPercentage = Dto.InitialCapital ?? 0 / Convert.ToDecimal(TotalCapital);
                //var TotalShare = await GetCountShareCompanyAsync();


                //var getEntityShare = await _unitOfWork.ShareDistributions.GetTableNoTracking()
                //                    .FirstOrDefaultAsync(a => a.PartnerId == Dto.Id);
                //if (getEntityShare != null) {
                //    getEntityShare.SharesCount = Convert.ToInt32(InitialCapitalPercentage / Convert.ToInt32(TotalShare));
                //    getEntityShare.ShareValue = Convert.ToDecimal(Convert.ToDecimal(TotalCapital) / Convert.ToInt32(TotalShare));
                //    getEntityShare.UpdatedAt = DateTime.Now;
                //    await _unitOfWork.ShareDistributions.UpdateAsync(getEntityShare);
                //}
                //else
                //{
                //    //عمل حسابات الشريك الاوليه
                //    var shareCreate = new ShareDistribution()
                //    {
                //        DistributionDate = DateTime.Now,
                //        Description = "تخصيص الأسهم الأولي",
                //        PartnerId = Dto.Id,
                //        SharesCount = Convert.ToInt32(InitialCapitalPercentage / Convert.ToInt32(TotalShare)),
                //        ShareValue = Convert.ToDecimal(Convert.ToDecimal(TotalCapital) / Convert.ToInt32(TotalShare)),
                //        CreatedAt = DateTime.Now
                //    };

                //    await _unitOfWork.ShareDistributions.AddAsync(shareCreate);
                //  }
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("partner updated successfully: {partnerId}", existingpartner.Id);

                var result = await GetPartnerByIdAsync(existingpartner.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating partner: {partnerId}", Dto.Id);
                return BadRequest<PartnerResponseDto>("حدث خطأ أثناء تحديث الشريك");
            }
        }
        public async Task<ApiResponse<bool>> DeletePartnerAsync(int id)
        {
            try
            {
                var partner = await _unitOfWork.Partners.GetByIdAsync(id);
                if (partner == null || partner.IsDeleted)
                    return NotFound<bool>("الشريك غير موجود");

                // Check if partner has transactions
                var hasTransactions = await _unitOfWork.PartnerTransations.GetTableNoTracking()
                    .AnyAsync(t => t.PartnerId == id && !t.IsDeleted);

                if (hasTransactions)
                {
                    //partner.IsDeleted = true;
                    //partner.UpdatedAt = DateTime.Now;

                    //await _unitOfWork.Partners.UpdateAsync(partner);
                    //await _unitOfWork.SaveChangesAsync();
                    //return Success(true, "تم حذف الشريك بنجاح");
                    return NotFound<bool>("لا يمكنك الحذف له حركات في معاملات الشركاء");
                }

                var hasShareTrans = await _unitOfWork.ShareTransfers.GetTableNoTracking()
                  .AnyAsync(t => t.BuyerId == id && !t.IsDeleted && t.SellerId == id);

                if (hasShareTrans)
                {

                    return NotFound<bool>("لا يمكنك الحذف له حركات في البيع والشراء في معاملات الاسهم");
                    //partner.IsDeleted = true;
                    //partner.UpdatedAt = DateTime.Now;

                    //await _unitOfWork.Partners.UpdateAsync(partner);
                    //await _unitOfWork.SaveChangesAsync();
                    //return Success(true, "تم حذف الشريك بنجاح");
                }
                await _unitOfWork.Partners.DeleteAsync(partner);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم حذف الشريك بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting partner: {PartnerId}", id);
                return BadRequest<bool>("حدث خطأ أثناء حذف الشريك");
            }
        }
        #endregion

        #region Partner Band
        public async Task<ApiResponse<List<PartnerBandResponseDto>>> GetAllPartnersBandAsync(bool isDelete = false)
        {
            try
            {
                var query = _unitOfWork.PartnerBands.GetTableNoTracking();

                if (!isDelete)
                    query = query.Where(p => !p.IsDeleted);

                var partnerBands = await query.ToListAsync();

                if (!partnerBands.Any())
                    return NotFound<List<PartnerBandResponseDto>>("لا يوجد بيانات");

                var partnerBandsDto = _mapper.Map<List<PartnerBandResponseDto>>(partnerBands);
                return Success(partnerBandsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all partner Bands");
                return BadRequest<List<PartnerBandResponseDto>>("حدث خطأ أثناء جلب البيانات");
            }
        }
        public async Task<ApiResponse<PartnerBandResponseDto>> GetPartnerBandByIdAsync(int id)
        {
            try
            {
                var partnerBand = await _unitOfWork.PartnerBands.GetTableNoTracking()
                            .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (partnerBand == null)
                    return NotFound<PartnerBandResponseDto>("البيانات غير موجود");

                var partnerBandDto = _mapper.Map<PartnerBandResponseDto>(partnerBand);
                return Success(partnerBandDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting partnerBand by id: {PartnerBandId}", id);
                return BadRequest<PartnerBandResponseDto>("حدث خطأ أثناء جلب البيانات ");
            }
        }
        public async Task<ApiResponse<PartnerBandResponseDto>> CreatePartnerBandAsycn(CreatePartnerBandDto createDto)
        {
            try
            {
                var existingPartnerBand = await _unitOfWork.PartnerBands.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.Name == createDto.Name && !u.IsDeleted);

                if (existingPartnerBand != null)
                    return BadRequest<PartnerBandResponseDto>("الاسم موجود بالفعل");

                var partnerBand = _mapper.Map<PartnerBand>(createDto);

                await _unitOfWork.PartnerBands.AddAsync(partnerBand);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Partner Band created successfully: {name}", partnerBand.Name);

                var result = await GetPartnerBandByIdAsync(partnerBand.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating partnerBand: {name}", createDto.Name);
                return BadRequest<PartnerBandResponseDto>("حدث خطأ أثناء إنشاء البيانات");
            }
        }
        public async Task<ApiResponse<PartnerBandResponseDto>> UpdatePartnerBandAsycn(UpdatePartnerBandDto Dto)
        {
            try
            {
                var existingpartnerBand = await _unitOfWork.PartnerBands.GetByIdAsync(Dto.Id);
                if (existingpartnerBand == null || existingpartnerBand.IsDeleted)
                    return NotFound<PartnerBandResponseDto>("غير موجود");

                // Check if username is taken by another user
                var duplicateUser = await _unitOfWork.Partners.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.Name == Dto.Name && u.Id != Dto.Id && !u.IsDeleted);

                if (duplicateUser != null)
                    return BadRequest<PartnerBandResponseDto>("الاسم موجود بالفعل");

                _mapper.Map(Dto, existingpartnerBand);
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.PartnerBands.UpdateAsync(existingpartnerBand);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("partnerBand  updated successfully: {partnerId}", existingpartnerBand.Id);

                var result = await GetPartnerBandByIdAsync(existingpartnerBand.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating partnerBand: {BandId}", Dto.Id);
                return BadRequest<PartnerBandResponseDto>("حدث خطأ أثناء تحديث البيانات");
            }
        }
        public async Task<ApiResponse<bool>> DeletePartnerBandAsync(int id)
        {
            try
            {
                var partnerBand = await _unitOfWork.PartnerBands.GetByIdAsync(id);
                if (partnerBand == null || partnerBand.IsDeleted)
                    return NotFound<bool>("غير موجود");

                // Check if partnerBand has transactions
                var hasTransactions = await _unitOfWork.PartnerTransations.GetTableNoTracking()
                    .AnyAsync(t => t.PartnerBandId == id && !t.IsDeleted);

                if (hasTransactions)
                {
                    partnerBand.IsDeleted = true;
                    partnerBand.UpdatedAt = DateTime.Now;

                    await _unitOfWork.PartnerBands.UpdateAsync(partnerBand);
                    await _unitOfWork.SaveChangesAsync();

                    return Success(true, "تم حذف البيانات بنجاح");
                }

                await _unitOfWork.PartnerBands.DeleteAsync(partnerBand);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم حذف البيانات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting partnerBand: {PartnerBandId}", id);
                return BadRequest<bool>("حدث خطأ أثناء حذف البيانات");
            }
        }
        #endregion

        #region Partner Transactions
        public async Task<ApiResponse<PagedResponse<PartnerTransactionResponseDto>>> GetAllPartnerTransactionAsync(PartnerTransactionRequestDto req)
        {
            #region old
            //if (req.fromDate.HasValue && req.toDate.HasValue && req.fromDate > req.toDate)
            //{
            //    return NotFound<PagedResponse<PartnerTransactionResponseDto>>("لا يجب أن يكون (التاريخ من) أكبر من (التاريخ إلى)");
            //}

            //var query = _unitOfWork.PartnerTransations.GetTableNoTracking()
            //                   .Include(p => p.Partners)
            //                   .Include(c => c.PartnerBand)
            //                   .Include(b => b.MainAction)
            //                  .Where(a => !a.IsDeleted);

            //if (query == null)
            //{
            //    return NotFound<PagedResponse<PartnerTransactionResponseDto>>("لا يوجد بيانات لعرضها");
            //}


            //if (req.partnerId.HasValue)
            //{
            //    query = query.Where(p => p.PartnerId == req.partnerId.Value);
            //}

            //if (req.fromDate.HasValue && req.toDate.HasValue)
            //{
            //    query = query.Where(st => st.TransactionDate.Date >= req.fromDate.Value.Date && st.TransactionDate.Date <= req.toDate.Value.Date);
            //}

            ////if (req.fromDate > req.toDate)
            ////    return NotFound<PagedResponse<PartnerTransactionResponseDto>>("لا يجب ان يكون (التاريخ من ) أكبر من (التاريخ الي)");

            //if (!string.IsNullOrEmpty(req.searchTerm))
            //{
            //    query = query.Where(r => (r.Description != null && r.Description.Contains(req.searchTerm)) ||
            //                             (r.Notes != null && r.Notes.Contains(req.searchTerm)));
            //}

            //if (req.bandId.HasValue)
            //{
            //    query = query.Where(p => p.PartnerBandId == req.bandId.Value);
            //}
            #endregion

            // 1. التحقق من التواريخ أولًا
            if (req.fromDate.HasValue && req.toDate.HasValue && req.fromDate > req.toDate)
            {
                return NotFound<PagedResponse<PartnerTransactionResponseDto>>("لا يجب أن يكون (التاريخ من) أكبر من (التاريخ إلى)");
            }

            // 2. بناء الاستعلام
            var query = BuildPartnerTransactionQuery(req);

            // 3. إذا كان الاستعلام غير صالح (مثل تواريخ خاطئة) — تم التحقق مسبقًا، لكن للسلامة
            if (query == null)
            {
                return NotFound<PagedResponse<PartnerTransactionResponseDto>>("لا يوجد بيانات مطابقة.");
            }
            var totalCount = await query.CountAsync();
            var partnerTrans = await query
                .OrderBy(r => r.TransactionDate)
                .Skip((req.pageNumber - 1) * req.pageSize)
                .Take(req.pageSize)
                .ToListAsync();

            var partnerTransDtos = _mapper.Map<List<PartnerTransactionResponseDto>>(partnerTrans, opt =>
            {
                opt.Items["baseUrl"] = $"{_httpContextAccessor.HttpContext!.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}";
            });

            var pagedResponse = new PagedResponse<PartnerTransactionResponseDto>
            {
                Items = partnerTransDtos,
                TotalCount = totalCount,
                PageNumber = req.pageNumber,
                PageSize = req.pageSize
            };

            return Success(pagedResponse);
        }
        public async Task<ApiResponse<PartnerTransactionResponseDto>> GetPartnerTransactionByIdAsync(int id)
        {
            try
            {
                var partnerTran = await _unitOfWork.PartnerTransations.GetTableNoTracking()
                    .Include(p => p.Partners)
                    .Include(c => c.PartnerBand)
                    .Include(b => b.MainAction)
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (partnerTran == null)
                    return NotFound<PartnerTransactionResponseDto>("لا يوجد بيانات");

                var partnerTranDto = _mapper.Map<PartnerTransactionResponseDto>(partnerTran, opt =>
                {
                    opt.Items["baseUrl"] = $"{_httpContextAccessor.HttpContext!.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}";
                });

                return Success(partnerTranDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting partnerTransaction by id: {PartnerTransactionId}", id);
                return BadRequest<PartnerTransactionResponseDto>("حدث خطأ أثناء جلب بيانات ");
            }
        }
        public async Task<ApiResponse<PartnerTransactionResponseDto>> CreatePartnerTransactionAsync(CreatePartnerTransactionDto createDto)
        {
            try
            {
                string? imagePath = null;
                // Handle profile image upload if provided
                if (createDto.ImagePath != null)
                {
                    // Upload new profile image
                    imagePath = await _fileUploadService.UploadImageAsync(createDto.ImagePath, "Partners");
                }

                var partnertrans = _mapper.Map<PartnerTransation>(createDto);

                partnertrans.ImagePath = imagePath;

                await _unitOfWork.PartnerTransations.AddAsync(partnertrans);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("PartnerTransactions created successfully: {Description}", partnertrans.Description);

                var result = await GetPartnerTransactionByIdAsync(partnertrans.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating partner: {Description}", createDto.Description);
                return BadRequest<PartnerTransactionResponseDto>("حدث خطأ أثناء إنشاء الحركة");
            }
        }
        public async Task<ApiResponse<PartnerTransactionResponseDto>> UpdatePartnerTransactionAsycn(UpdatePartnerTransactionDto Dto)
        {
            try
            {
                var existingpartner = await _unitOfWork.PartnerTransations.GetByIdAsync(Dto.Id);
                if (existingpartner == null || existingpartner.IsDeleted)
                    return NotFound<PartnerTransactionResponseDto>(" غير موجود");






                if (Dto.ImagePath != null)
                {

                    // Delete old  image if exists
                    if (!string.IsNullOrEmpty(existingpartner.ImagePath))
                    {
                        await _fileUploadService.DeleteImage(existingpartner.ImagePath);
                    }

                    // Upload new image
                    existingpartner.ImagePath = await _fileUploadService.UploadImageAsync(Dto.ImagePath, "Partners");

                }
                _mapper.Map(Dto, existingpartner);

                await _unitOfWork.PartnerTransations.UpdateAsync(existingpartner);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("partner Transaction updated successfully: {partnerTransationId}", existingpartner.Id);

                var result = await GetPartnerTransactionByIdAsync(existingpartner.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating partner: {partnerId}", Dto.Id);
                return BadRequest<PartnerTransactionResponseDto>("حدث خطأ أثناء تحديث الشريك");
            }
        }
        public async Task<ApiResponse<bool>> DeletePartnerTransationAsync(int id)
        {
            try
            {
                var partnerTrans = await _unitOfWork.PartnerTransations.GetByIdAsync(id);
                if (partnerTrans == null || partnerTrans.IsDeleted)
                    return NotFound<bool>("غير موجود");

                if (partnerTrans.ImagePath != null)
                {
                    // Delete old  image if exists
                    if (!string.IsNullOrEmpty(partnerTrans.ImagePath))
                    {
                        await _fileUploadService.DeleteImage(partnerTrans.ImagePath);
                    }

                }
                await _unitOfWork.PartnerTransations.DeleteAsync(partnerTrans);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم الحذف بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting partnerTranse: {PartnerTranseId}", id);
                return BadRequest<bool>("حدث خطأ أثناء الحذف ");
            }
        }
        private IQueryable<PartnerTransation> BuildPartnerTransactionQuery(PartnerTransactionRequestDto req)
        {
            // التحقق من صحة التواريخ أولًا
            if (req.fromDate.HasValue && req.toDate.HasValue && req.fromDate > req.toDate)
                return null!; // سنُعالج هذا لاحقًا في الخدمة

            var query = _unitOfWork.PartnerTransations.GetTableNoTracking()
                .Include(p => p.Partners)
                .Include(c => c.PartnerBand)
                .Include(b => b.MainAction)
                .Where(a => !a.IsDeleted);

            // تطبيق الفلاتر
            if (req.partnerId.HasValue)
                query = query.Where(p => p.PartnerId == req.partnerId.Value);

            if (req.fromDate.HasValue && req.toDate.HasValue)
                query = query.Where(st =>
                    st.TransactionDate.Date >= req.fromDate.Value.Date &&
                    st.TransactionDate.Date <= req.toDate.Value.Date);

            if (!string.IsNullOrEmpty(req.searchTerm))
                query = query.Where(r =>
                    (r.Description != null && r.Description.Contains(req.searchTerm)) ||
                    (r.Notes != null && r.Notes.Contains(req.searchTerm)));

            if (req.bandId.HasValue)
                query = query.Where(p => p.PartnerBandId == req.bandId.Value);

            return query;
        }
        public async Task<ApiResponse<List<PartnerTransactionResponseDto>>> GetReportPartnerTransactionAsync(PartnerTransactionRequestDto req)
        {


            // 1. التحقق من التواريخ أولًا
            if (req.fromDate.HasValue && req.toDate.HasValue && req.fromDate > req.toDate)
            {
                return NotFound<List<PartnerTransactionResponseDto>>("لا يجب أن يكون (التاريخ من) أكبر من (التاريخ إلى)");
            }

            // 2. بناء الاستعلام
            var query = BuildPartnerTransactionQuery(req);

            // 3. إذا كان الاستعلام غير صالح (مثل تواريخ خاطئة) — تم التحقق مسبقًا، لكن للسلامة
            if (query == null)
            {
                return NotFound<List<PartnerTransactionResponseDto>>("لا يوجد بيانات مطابقة.");
            }
            var totalCount = await query.CountAsync();
            var partnerTrans = await query
                .OrderBy(r => r.TransactionDate)
                .ToListAsync();

            var partnerTransDtos = _mapper.Map<List<PartnerTransactionResponseDto>>(partnerTrans, opt =>
            {
                opt.Items["baseUrl"] = $"{_httpContextAccessor.HttpContext!.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}";
            });


            return Success(partnerTransDtos);
        }
        #endregion

        #region Partner Calculators
        private async Task<decimal> GetAllInvestmentsAsync()
        {

            try
            {
                var allPartners = await _unitOfWork.Partners.GetTableNoTracking()
                      .Where(p => !p.IsDeleted)
                      .Include(p => p.PartnerTransations)
                      .ToListAsync();

                if (!allPartners.Any())
                {

                    _logger.LogError("Error  : {GetAllInvestmentsAsync}", 0);
                    return 0;
                }



                decimal allInitialCapital = Convert.ToDecimal(allPartners.Sum(p => p.InitialCapital ?? 0));

                // Flatten all transactions from all partners
                var allTransactions = allPartners
                    .SelectMany(p => p.PartnerTransations)
                    .Where(t => !t.IsDeleted)
                    .ToList();

                // Calculate total investments (ActionDetailId == 2) and withdrawals (ActionDetailId == 3)
                decimal totalInvestments = allTransactions
                    .Where(t => t.ActionDetailId == 2)
                    .Sum(t => t.Amount); // Assuming Amount is nullable

                decimal totalWithdrawals = allTransactions
                    .Where(t => t.ActionDetailId == 3)
                    .Sum(t => t.Amount);

                // Current capital = initial + investments - withdrawals
                decimal currentCapital = allInitialCapital + totalInvestments - totalWithdrawals;


                return currentCapital;

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting ShareDistributions: {GetAllInvestmentsAsync}", 0);
                return 0;
            }


        }
        private async Task<int> GetCountShareCompanyAsync()
        {
            try
            {
                var company = await _unitOfWork.Companies.GetTableNoTracking()
                    .Where(c => !c.IsDeleted)
                    .FirstOrDefaultAsync();

                if (company == null)
                {
                    return 0;
                }

                return company.TotalShares;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public async Task<PartnerSharesSummary> GetPartnerSharesSummary(int partnerId)
        {
            var partner = await _unitOfWork.Partners.GetTableNoTracking()
                .Where(p => p.Id == partnerId && !p.IsDeleted)
                .Select(p => new PartnerSharesSummary
                {
                    PartnerId = p.Id,
                    PartnerName = p.Name,
                    TotalSharesBuyer = p.BuyTransactions.Sum(t => (int?)t.SharesCount) ?? 0,
                    TotalSharesSeller = p.SellTransactions.Sum(t => (int?)t.SharesCount) ?? 0,
                    NetShares = (p.BuyTransactions.Sum(t => (int?)t.SharesCount) ?? 0) -
                               (p.SellTransactions.Sum(t => (int?)t.SharesCount) ?? 0)
                })
                .FirstOrDefaultAsync() ?? new PartnerSharesSummary();

            return partner;
        }

        public async Task<List<PartnerSharesSummary>> GetPartnersSharesSummaryAlternative()
        {
            var buyers = await _unitOfWork.ShareTransfers.GetTableNoTracking()
                .GroupBy(t => t.BuyerId)
                .Select(g => new
                {
                    PartnerId = g.Key,
                    TotalBought = g.Sum(t => t.SharesCount)
                })
                .ToListAsync();

            var sellers = await _unitOfWork.ShareTransfers.GetTableNoTracking()
                .GroupBy(t => t.SellerId)
                .Select(g => new
                {
                    PartnerId = g.Key,
                    TotalSold = g.Sum(t => t.SharesCount)
                })
                .ToListAsync();

            var partners = await _unitOfWork.Partners.GetTableNoTracking().ToListAsync();

            var result = partners.Select(p => new PartnerSharesSummary
            {
                PartnerId = p.Id,
                PartnerName = p.Name,
                TotalSharesSeller = buyers.FirstOrDefault(b => b.PartnerId == p.Id)?.TotalBought ?? 0,
                TotalSharesBuyer = sellers.FirstOrDefault(s => s.PartnerId == p.Id)?.TotalSold ?? 0
            }).ToList();

            foreach (var item in result)
            {
                item.NetShares = item.TotalSharesBuyer - item.TotalSharesSeller;
            }

            return result;
        }
        #endregion

        public async Task<ApiResponse<List<ShareTransferResponseDto>>> GetAllShareTransactionAsync(ShareTransferRequestDto req)
        {
            // 1. التحقق من التواريخ أولًا
            if (req.fromDate.HasValue && req.toDate.HasValue && req.fromDate > req.toDate)
            {
                return NotFound<List<ShareTransferResponseDto>>("لا يجب أن يكون (التاريخ من) أكبر من (التاريخ إلى)");
            }

            var query = _unitOfWork.ShareTransfers.GetTableNoTracking()
                         .Include(p => p.Buyer)
                         .Include(c => c.Seller)
                         .Where(a => !a.IsDeleted);

            // تطبيق الفلاتر
            if (req.partnerId.HasValue)
                query = query.Where(p => p.BuyerId == req.partnerId.Value || p.SellerId == req.partnerId.Value);

            if (req.fromDate.HasValue && req.toDate.HasValue)
                query = query.Where(st =>
                    st.TransfersDate.Date >= req.fromDate.Value.Date &&
                    st.TransfersDate.Date <= req.toDate.Value.Date);
            if (query == null)
            {
                return NotFound<List<ShareTransferResponseDto>>("لا يوجد بيانات مطابقة.");
            }

            var partnerTrans = await query
                .OrderBy(r => r.TransfersDate)
                .ToListAsync();

            var partnerTransDtos = _mapper.Map<List<ShareTransferResponseDto>>(partnerTrans);


            return Success(partnerTransDtos);
        }

        public async Task<ApiResponse<ShareTransferResponseDto>> GetShareTransferByIdAsync(int id)
        {
            try
            {
                var shareTrans = await _unitOfWork.ShareTransfers.GetTableNoTracking()
                    .Include(p => p.Buyer)
                    .Include(c => c.Seller)
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (shareTrans == null)
                    return NotFound<ShareTransferResponseDto>("لا يوجد بيانات");

                var shareTransDto = _mapper.Map<ShareTransferResponseDto>(shareTrans);

                return Success(shareTransDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting shareTransaction by id: {shareTransactionId}", id);
                return BadRequest<ShareTransferResponseDto>("حدث خطأ أثناء جلب بيانات ");
            }
        }

        public async Task<ApiResponse<ShareTransferResponseDto>> CreateShareTransferAsync(CreateShareTransferDto createDto)
        {
            
            try
            {
                // ✅ تحقق أن البائع والمشتري ليسا نفس الشخص
                if (createDto.SellerId == createDto.BuyerId)
                    return BadRequest<ShareTransferResponseDto>("لا يمكن أن يكون البائع هو نفس المشترى");

                var getSellerHaveShare = await GetPartnerByIdAsync(createDto.SellerId);

                if (getSellerHaveShare.Data == null)
                     return NotFound<ShareTransferResponseDto>("الشريك البائع ليس موجود");              

                if(getSellerHaveShare.Data.CurrentShare < 1)
                    return BadRequest<ShareTransferResponseDto>("الشريك البائع ليس له اسهم للبيع");

                if (getSellerHaveShare.Data.CurrentShare < createDto.SharesCount)
                    return NotFound<ShareTransferResponseDto>($"لا يمكن ان يبيع الشريك اكثر من {getSellerHaveShare.Data.CurrentShare} من الاسهم");
                         
                var shareTransfer = _mapper.Map<ShareTransfer>(createDto);
            
                var getBuyerHaveShare = await GetPartnerByIdAsync(createDto.BuyerId);


                shareTransfer.Description = $"بيع من {getSellerHaveShare.Data.Name} الي {getBuyerHaveShare.Data.Name}";

                await _unitOfWork.ShareTransfers.AddAsync(shareTransfer);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("ShareTransfers created successfully: {Description}", shareTransfer.Description);

                var result = await GetShareTransferByIdAsync(shareTransfer.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Share: {Description}", createDto.Description);
                return BadRequest<ShareTransferResponseDto>("حدث خطأ أثناء إنشاء المعاملة");
            }

        }

        public async Task<ApiResponse<ShareTransferResponseDto>> UpdateShareTransferAsync(UpdateShareTransferDto dto)
        {
            try
            {
                // ✅ تحقق أن البائع والمشتري ليسا نفس الشخص
                if (dto.SellerId == dto.BuyerId)
                    return BadRequest<ShareTransferResponseDto>("لا يمكن أن يكون البائع هو نفس المشترى");

                var existingTransfer = await _unitOfWork.ShareTransfers.GetByIdAsync(dto.Id);
                if (existingTransfer == null || existingTransfer.IsDeleted)
                    return NotFound<ShareTransferResponseDto>("المعاملة غير موجودة");

                bool isSellerChanged = dto.SellerId != existingTransfer.SellerId;
                bool isSharesCountChanged = dto.SharesCount != existingTransfer.SharesCount;

                if (isSellerChanged || isSharesCountChanged)
                {
                    var seller = await GetPartnerByIdAsync(dto.SellerId);
                    if (seller.Data == null)
                        return NotFound<ShareTransferResponseDto>("الشريك البائع غير موجود");

                    if (seller.Data.CurrentShare < 1)
                        return BadRequest<ShareTransferResponseDto>("الشريك البائع لا يمتلك أسهماً");

                    if (seller.Data.CurrentShare < dto.SharesCount)
                        return BadRequest<ShareTransferResponseDto>($"الشريك البائع لا يمكنه بيع أكثر من {seller.Data.CurrentShare} سهم");

                   
                }

                var buyer = await GetPartnerByIdAsync(dto.BuyerId);
                if (buyer.Data == null)
                    return NotFound<ShareTransferResponseDto>("الشريك المشتري غير موجود");

                var sellerl = await GetPartnerByIdAsync(dto.SellerId);
                if (buyer.Data == null)
                    return NotFound<ShareTransferResponseDto>("الشريك المشتري غير موجود");
                // تحديث الكيان بالبيانات الجديدة
                _mapper.Map(dto, existingTransfer);
          
              
                                // تحديث الوصف فقط لو تغيّر البائع أو المشتري
                existingTransfer.Description = $"بيع من {sellerl.Data.Name ?? "?"} إلى {buyer.Data.Name}";

                await _unitOfWork.ShareTransfers.UpdateAsync(existingTransfer);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم تحديث معاملة الأسهم بنجاح: {ShareTransferId}", existingTransfer.Id);

                return await GetShareTransferByIdAsync(existingTransfer.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "حدث خطأ أثناء تحديث معاملة الأسهم: {ShareTransferId}", dto.Id);
                return BadRequest<ShareTransferResponseDto>("حدث خطأ أثناء التحديث");
            }
        }


        //public async Task<ApiResponse<List<PartnerDataSummary>>> GetAllPartnerDataSummer()
        //{
        //    try
        //    {
        //        var allPartners = await _unitOfWork.Partners.GetTableNoTracking()
        //                       .Where(p => !p.IsDeleted)
        //                        .Include(t => t.PartnerTransations)
        //                        .Include(s => s.SellTransactions)
        //                        .Include(b => b.BuyTransactions)
        //                        .ToListAsync();

        //        if(!allPartners.Any()) return NotFound<List<PartnerDataSummary>>("لا يوجد شركاء");


        //        decimal allInitialCapital = Convert.ToDecimal(allPartners.Sum(p => p.InitialCapital));

        //        decimal allTotalInvestments = allPartners
        //          .SelectMany(p => p.PartnerTransations)
        //          .Where(t => !t.IsDeleted && t.ActionDetailId == 2)
        //          .Sum(t => t.Amount);

        //        decimal allTotalWithdrawals = allPartners
        //            .SelectMany(p => p.PartnerTransations)
        //            .Where(t => !t.IsDeleted && t.ActionDetailId == 3)
        //            .Sum(t => t.Amount);

        //        decimal totalAllCapital = allInitialCapital + allTotalInvestments - allTotalWithdrawals;

        //        decimal totalShareCompany = await GetCountShareCompanyAsync();

        //        decimal ShareValueCompany = totalAllCapital / totalShareCompany;


        //        List<PartnerDataSummary> partnerDataSummaries = new List<PartnerDataSummary>();


        //        foreach (var partner in allPartners) { 

        //            var transactions = partner.PartnerTransations.Where(p => !p.IsDeleted).ToList();

        //            var totalInvestments = transactions.Where(t => t.ActionDetailId == 2 && t.PartnerId== partner.Id).Sum(t => t.Amount);

        //            var  totalWithdrawals = transactions.Where(t => t.ActionDetailId == 3 && t.PartnerId == partner.Id).Sum(t => t.Amount);

        //            var CurrentCapital = (partner.InitialCapital ?? 0) + totalInvestments - totalWithdrawals;

        //            var SharePercentage = totalAllCapital == 0?   0   : (CurrentCapital / totalAllCapital) * 100;

        //            var InitialShareCount = Convert.ToInt32(CurrentCapital / Convert.ToDecimal(ShareValueCompany));

        //            var shaersBuy = partner.BuyTransactions.Where(p => !p.IsDeleted && p.BuyerId == partner.Id).Sum(t => t.SharesCount);

        //            var shaersSell = partner.SellTransactions.Where(p => !p.IsDeleted && p.SellerId == partner.Id).Sum(t => t.SharesCount);

        //            var  CurrentShare = InitialShareCount + shaersBuy - shaersSell;
        //            var dto = new PartnerDataSummary()
        //            {
        //                PartnerId = partner.Id,
        //                PartnerName = partner.Name,
        //                InitialCapital = partner.InitialCapital,
        //                TotalInvestments = totalInvestments,
        //                TotalWithdrawals = totalWithdrawals,
        //                CurrentCapital = CurrentCapital,
        //                InitialShareCount = InitialShareCount,
        //                SharePercentage = SharePercentage,
        //                InitialShareValue = ShareValueCompany,
        //                TotalSharesBuyer = shaersBuy,
        //                TotalSharesSeller = shaersSell,
        //                NetShares = CurrentShare
        //            };

        //            partnerDataSummaries.Add(dto);
        //        }

        //        return Success(partnerDataSummaries);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error");
        //        return BadRequest<List<PartnerDataSummary>>("حدث خطأ أثناء جلب بيانات الشريك");
        //    }
        //}

        public async Task<ApiResponse<List<PartnerDataSummary>>> GetAllPartnerDataSummer()
        {
            try
            {
                // جلب بيانات الشركاء الأساسية فقط
                var partners = await _unitOfWork.Partners.GetTableNoTracking()
                    .Where(p => !p.IsDeleted)
                    .Select(p => new
                    {
                        p.Id,
                        p.Name,
                        p.InitialCapital
                    })
                    .ToListAsync();

                if (!partners.Any())
                    return NotFound<List<PartnerDataSummary>>("لا يوجد شركاء");

                var partnerIds = partners.Select(p => p.Id).ToList();

                var partnerTransations = await _unitOfWork.PartnerTransations.GetTableNoTracking()
                .Where(t => !t.IsDeleted)
                 .ToListAsync();

                // إجمالي الاستثمارات
                var investments = partnerTransations
                    .Where(t => t.ActionDetailId == 2 && partnerIds.Contains(t.PartnerId))
                    .GroupBy(t => t.PartnerId)
                    .Select(g => new { PartnerId = g.Key, Total = g.Sum(x => x.Amount) })
                    .ToList();

                // إجمالي السحوبات
                var withdrawals = partnerTransations
                    .Where(t => !t.IsDeleted && t.ActionDetailId == 3 && partnerIds.Contains(t.PartnerId))
                    .GroupBy(t => t.PartnerId)
                    .Select(g => new { PartnerId = g.Key, Total = g.Sum(x => x.Amount) })
                    .ToList();

                var shares = await _unitOfWork.ShareTransfers.GetTableNoTracking()
                    .Where(b => !b.IsDeleted)
                    .ToListAsync();

                // الأسهم المشتراة
                var sharesBuy = shares
                    .Where(b => partnerIds.Contains(b.BuyerId))
                    .GroupBy(b => b.BuyerId)
                    .Select(g => new { PartnerId = g.Key, Total = g.Sum(x => x.SharesCount) })
                    .ToList();
                
                // الأسهم المباعة
                var sharesSell = shares
                    .Where(s => partnerIds.Contains(s.SellerId))
                    .GroupBy(s => s.SellerId)
                    .Select(g => new { PartnerId = g.Key, Total = g.Sum(x => x.SharesCount) })
                    .ToList();

                // إجمالي رأس المال الكلي للشركة
                decimal allInitialCapital = partners.Sum(p => p.InitialCapital ?? 0);
                decimal allTotalInvestments = investments.Sum(x => x.Total);
                decimal allTotalWithdrawals = withdrawals.Sum(x => x.Total);
                decimal totalAllCapital = allInitialCapital + allTotalInvestments - allTotalWithdrawals;

                decimal totalShareCompany = await GetCountShareCompanyAsync();
                decimal shareValueCompany = totalShareCompany == 0 ? 0 : totalAllCapital / totalShareCompany;

                // تجهيز البيانات النهائية
                var result = partners.Select(p =>
                {
                    decimal totalInv = investments.FirstOrDefault(x => x.PartnerId == p.Id)?.Total ?? 0;
                    decimal totalWith = withdrawals.FirstOrDefault(x => x.PartnerId == p.Id)?.Total ?? 0;
                    decimal currentCapital = (p.InitialCapital ?? 0) + totalInv - totalWith;

                    decimal sharePercentage = totalAllCapital == 0 ? 0 : (currentCapital / totalAllCapital) * 100;
                    int initialShareCount = shareValueCompany == 0 ? 0 : Convert.ToInt32(currentCapital / shareValueCompany);

                    int buyShares = sharesBuy.FirstOrDefault(x => x.PartnerId == p.Id)?.Total ?? 0;
                    int sellShares = sharesSell.FirstOrDefault(x => x.PartnerId == p.Id)?.Total ?? 0;

                    int netShares = initialShareCount + buyShares - sellShares;

                    return new PartnerDataSummary
                    {
                        PartnerId = p.Id,
                        PartnerName = p.Name,
                        InitialCapital = p.InitialCapital,
                        TotalInvestments = totalInv,
                        TotalWithdrawals = totalWith,
                        CurrentCapital = currentCapital,
                        InitialShareCount = initialShareCount,
                        SharePercentage = sharePercentage,
                        InitialShareValue = shareValueCompany,
                        TotalSharesBuyer = buyShares,
                        TotalSharesSeller = sellShares,
                        NetShares = netShares
                    };
                }).ToList();

                return Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error");
              
                return BadRequest<List<PartnerDataSummary>>("حدث خطأ أثناء جلب بيانات الشريك");
            }
        }



    }
}
