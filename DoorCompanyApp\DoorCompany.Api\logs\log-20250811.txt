2025-08-11 10:52:04.230 +03:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-11 10:52:04.299 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-08-11 10:52:04.354 +03:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-08-11 10:52:04.491 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-08-11 10:52:04.502 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-11 10:52:04.504 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-11 10:52:04.509 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-11 10:52:04.520 +03:00 [INF] Applying migration '20250811075149_ChangeImagePathProduct'.
2025-08-11 10:52:04.735 +03:00 [INF] Executed DbCommand (120ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @var sysname;
SELECT @var = [d].[name]
FROM [sys].[default_constraints] [d]
INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
WHERE ([d].[parent_object_id] = OBJECT_ID(N'[Products]') AND [c].[name] = N'ImagePath');
IF @var IS NOT NULL EXEC(N'ALTER TABLE [Products] DROP CONSTRAINT [' + @var + '];');
ALTER TABLE [Products] ALTER COLUMN [ImagePath] nvarchar(max) NULL;
2025-08-11 10:52:04.738 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250811075149_ChangeImagePathProduct', N'9.0.7');
2025-08-11 10:52:04.769 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-08-11 10:52:17.650 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-11 10:52:17.840 +03:00 [INF] Executed DbCommand (124ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-11 10:52:18.217 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:52:18.244 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:52:18.459 +03:00 [INF] Executed DbCommand (81ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-11 10:52:18.557 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-11 10:52:18.607 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-11 10:52:18.623 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:52:18.633 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:52:18.646 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:52:18.657 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:52:18.718 +03:00 [INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:52:18.729 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:52:18.900 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-11 10:52:19.105 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-11 10:52:19.223 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-11 10:52:19.230 +03:00 [INF] Hosting environment: Development
2025-08-11 10:52:19.231 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-11 10:52:19.743 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-11 10:52:19.986 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 254.6231ms
2025-08-11 10:52:20.031 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-11 10:52:20.040 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.2073ms
2025-08-11 10:52:20.221 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-11 10:52:20.270 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 49.1692ms
2025-08-11 10:52:20.514 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-11 10:52:20.579 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 65.1513ms
2025-08-11 10:54:11.708 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product - multipart/form-data; boundary=----WebKitFormBoundary6AgCjZbwhqFNreVe 1797
2025-08-11 10:54:11.741 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-11 10:54:11.824 +03:00 [INF] CORS policy execution successful.
2025-08-11 10:54:13.153 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api)'
2025-08-11 10:54:13.201 +03:00 [INF] Route matched with {action = "CreateProduct", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateProductAsync(DoorCompany.Service.Dtos.ProductDto.CreateProductDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-11 10:54:16.255 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Name] = @__createDto_Name_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:54:16.678 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Code] = @__createDto_Code_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:54:17.202 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_Barcode_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Barcode] = @__createDto_Barcode_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 10:54:22.719 +03:00 [INF] Executed DbCommand (51ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (DbType = Int32), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (DbType = Int32), @p10='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p12='?' (Size = 4000), @p13='?' (DbType = Int32), @p14='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Products] ([Barcode], [CategoryId], [Code], [CreatedAt], [CreatedBy], [Description], [ImagePath], [IsActive], [IsDeleted], [ItemType], [MaximumStock], [MinimumStock], [Name], [SortOrder], [StandardCost], [UnitId], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-08-11 10:54:25.276 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__productId_0
2025-08-11 10:54:25.586 +03:00 [INF] Executed DbCommand (258ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (DbType = Int32), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = Int32), @p9='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p10='?' (Size = 4000), @p11='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p12='?' (DbType = DateTime2), @p13='?' (DbType = Int32), @p14='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p15='?' (DbType = DateTime2), @p16='?' (DbType = Int32), @p17='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p18='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p19='?' (DbType = DateTime2), @p20='?' (DbType = Int32), @p21='?' (DbType = Boolean), @p22='?' (DbType = Boolean), @p23='?' (DbType = Int32), @p24='?' (DbType = DateTime2), @p25='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [InventoryTransactions] ([AverageCost], [BalanceAfter], [CreatedAt], [CreatedBy], [Description], [InvoiceMasterId], [IsActive], [IsDeleted], [ProductId], [Quantity], [ReferenceNumber], [TotalCost], [TransactionDate], [Type], [UnitCost], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO [ProductInventories] ([AverageCost], [Balance], [CreatedAt], [CreatedBy], [IsActive], [IsDeleted], [ProductId], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25);
2025-08-11 10:54:27.567 +03:00 [INF] Product created successfully: باب مصفح كلاسيك
2025-08-11 10:54:28.523 +03:00 [INF] Executed DbCommand (251ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Barcode], [s].[CategoryId], [s].[Code], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[ImagePath], [s].[IsActive], [s].[IsDeleted], [s].[ItemType], [s].[MaximumStock], [s].[MinimumStock], [s].[Name], [s].[SortOrder], [s].[StandardCost], [s].[UnitId], [s].[UpdatedAt], [s].[UpdatedBy], [s].[Id0], [s].[CategoryTypeId], [s].[Code0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description0], [s].[ImageUrl], [s].[IsActive0], [s].[IsDeleted0], [s].[Name0], [s].[ParentCategoryId], [s].[SortOrder0], [s].[Symbol], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[Id1], [s].[CreatedAt1], [s].[CreatedBy1], [s].[Description1], [s].[IsActive1], [s].[IsDeleted1], [s].[Name1], [s].[Symbol0], [s].[UpdatedAt1], [s].[UpdatedBy1], [i0].[Id], [i0].[AverageCost], [i0].[BalanceAfter], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[InvoiceMasterId], [i0].[IsActive], [i0].[IsDeleted], [i0].[ProductId], [i0].[Quantity], [i0].[ReferenceNumber], [i0].[TotalCost], [i0].[TransactionDate], [i0].[Type], [i0].[UnitCost], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [i].[Id] AS [Id0], [i].[CategoryTypeId], [i].[Code] AS [Code0], [i].[CreatedAt] AS [CreatedAt0], [i].[CreatedBy] AS [CreatedBy0], [i].[Description] AS [Description0], [i].[ImageUrl], [i].[IsActive] AS [IsActive0], [i].[IsDeleted] AS [IsDeleted0], [i].[Name] AS [Name0], [i].[ParentCategoryId], [i].[SortOrder] AS [SortOrder0], [i].[Symbol], [i].[UpdatedAt] AS [UpdatedAt0], [i].[UpdatedBy] AS [UpdatedBy0], [u].[Id] AS [Id1], [u].[CreatedAt] AS [CreatedAt1], [u].[CreatedBy] AS [CreatedBy1], [u].[Description] AS [Description1], [u].[IsActive] AS [IsActive1], [u].[IsDeleted] AS [IsDeleted1], [u].[Name] AS [Name1], [u].[Symbol] AS [Symbol0], [u].[UpdatedAt] AS [UpdatedAt1], [u].[UpdatedBy] AS [UpdatedBy1]
    FROM [Products] AS [p]
    INNER JOIN [ItemCategories] AS [i] ON [p].[CategoryId] = [i].[Id]
    INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [s]
LEFT JOIN [InventoryTransactions] AS [i0] ON [s].[Id] = [i0].[ProductId]
ORDER BY [s].[Id], [s].[Id0], [s].[Id1]
2025-08-11 10:54:28.565 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__productId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-11 10:54:30.588 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.ProductResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-11 10:54:30.650 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api) in 17443.1717ms
2025-08-11 10:54:30.652 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api)'
2025-08-11 10:54:30.656 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product - 200 null application/json; charset=utf-8 18948.1474ms
2025-08-11 10:55:39.325 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Product/1 - null null
2025-08-11 10:55:39.339 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.GetProductById (DoorCompany.Api)'
2025-08-11 10:55:39.345 +03:00 [INF] Route matched with {action = "GetProductById", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProductById(Int32) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-11 10:55:39.426 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Barcode], [s].[CategoryId], [s].[Code], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[ImagePath], [s].[IsActive], [s].[IsDeleted], [s].[ItemType], [s].[MaximumStock], [s].[MinimumStock], [s].[Name], [s].[SortOrder], [s].[StandardCost], [s].[UnitId], [s].[UpdatedAt], [s].[UpdatedBy], [s].[Id0], [s].[CategoryTypeId], [s].[Code0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description0], [s].[ImageUrl], [s].[IsActive0], [s].[IsDeleted0], [s].[Name0], [s].[ParentCategoryId], [s].[SortOrder0], [s].[Symbol], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[Id1], [s].[CreatedAt1], [s].[CreatedBy1], [s].[Description1], [s].[IsActive1], [s].[IsDeleted1], [s].[Name1], [s].[Symbol0], [s].[UpdatedAt1], [s].[UpdatedBy1], [i0].[Id], [i0].[AverageCost], [i0].[BalanceAfter], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[InvoiceMasterId], [i0].[IsActive], [i0].[IsDeleted], [i0].[ProductId], [i0].[Quantity], [i0].[ReferenceNumber], [i0].[TotalCost], [i0].[TransactionDate], [i0].[Type], [i0].[UnitCost], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [i].[Id] AS [Id0], [i].[CategoryTypeId], [i].[Code] AS [Code0], [i].[CreatedAt] AS [CreatedAt0], [i].[CreatedBy] AS [CreatedBy0], [i].[Description] AS [Description0], [i].[ImageUrl], [i].[IsActive] AS [IsActive0], [i].[IsDeleted] AS [IsDeleted0], [i].[Name] AS [Name0], [i].[ParentCategoryId], [i].[SortOrder] AS [SortOrder0], [i].[Symbol], [i].[UpdatedAt] AS [UpdatedAt0], [i].[UpdatedBy] AS [UpdatedBy0], [u].[Id] AS [Id1], [u].[CreatedAt] AS [CreatedAt1], [u].[CreatedBy] AS [CreatedBy1], [u].[Description] AS [Description1], [u].[IsActive] AS [IsActive1], [u].[IsDeleted] AS [IsDeleted1], [u].[Name] AS [Name1], [u].[Symbol] AS [Symbol0], [u].[UpdatedAt] AS [UpdatedAt1], [u].[UpdatedBy] AS [UpdatedBy1]
    FROM [Products] AS [p]
    INNER JOIN [ItemCategories] AS [i] ON [p].[CategoryId] = [i].[Id]
    INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [s]
LEFT JOIN [InventoryTransactions] AS [i0] ON [s].[Id] = [i0].[ProductId]
ORDER BY [s].[Id], [s].[Id0], [s].[Id1]
2025-08-11 10:55:39.430 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.ProductResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-11 10:55:39.432 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.GetProductById (DoorCompany.Api) in 85.1734ms
2025-08-11 10:55:39.434 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.GetProductById (DoorCompany.Api)'
2025-08-11 10:55:39.435 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Product/1 - 404 null application/json; charset=utf-8 110.5624ms
2025-08-11 10:56:47.071 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Product/3 - null null
2025-08-11 10:56:47.096 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.GetProductById (DoorCompany.Api)'
2025-08-11 10:56:47.097 +03:00 [INF] Route matched with {action = "GetProductById", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProductById(Int32) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-11 10:56:47.111 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Barcode], [s].[CategoryId], [s].[Code], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[ImagePath], [s].[IsActive], [s].[IsDeleted], [s].[ItemType], [s].[MaximumStock], [s].[MinimumStock], [s].[Name], [s].[SortOrder], [s].[StandardCost], [s].[UnitId], [s].[UpdatedAt], [s].[UpdatedBy], [s].[Id0], [s].[CategoryTypeId], [s].[Code0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description0], [s].[ImageUrl], [s].[IsActive0], [s].[IsDeleted0], [s].[Name0], [s].[ParentCategoryId], [s].[SortOrder0], [s].[Symbol], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[Id1], [s].[CreatedAt1], [s].[CreatedBy1], [s].[Description1], [s].[IsActive1], [s].[IsDeleted1], [s].[Name1], [s].[Symbol0], [s].[UpdatedAt1], [s].[UpdatedBy1], [i0].[Id], [i0].[AverageCost], [i0].[BalanceAfter], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[InvoiceMasterId], [i0].[IsActive], [i0].[IsDeleted], [i0].[ProductId], [i0].[Quantity], [i0].[ReferenceNumber], [i0].[TotalCost], [i0].[TransactionDate], [i0].[Type], [i0].[UnitCost], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [i].[Id] AS [Id0], [i].[CategoryTypeId], [i].[Code] AS [Code0], [i].[CreatedAt] AS [CreatedAt0], [i].[CreatedBy] AS [CreatedBy0], [i].[Description] AS [Description0], [i].[ImageUrl], [i].[IsActive] AS [IsActive0], [i].[IsDeleted] AS [IsDeleted0], [i].[Name] AS [Name0], [i].[ParentCategoryId], [i].[SortOrder] AS [SortOrder0], [i].[Symbol], [i].[UpdatedAt] AS [UpdatedAt0], [i].[UpdatedBy] AS [UpdatedBy0], [u].[Id] AS [Id1], [u].[CreatedAt] AS [CreatedAt1], [u].[CreatedBy] AS [CreatedBy1], [u].[Description] AS [Description1], [u].[IsActive] AS [IsActive1], [u].[IsDeleted] AS [IsDeleted1], [u].[Name] AS [Name1], [u].[Symbol] AS [Symbol0], [u].[UpdatedAt] AS [UpdatedAt1], [u].[UpdatedBy] AS [UpdatedBy1]
    FROM [Products] AS [p]
    INNER JOIN [ItemCategories] AS [i] ON [p].[CategoryId] = [i].[Id]
    INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [s]
LEFT JOIN [InventoryTransactions] AS [i0] ON [s].[Id] = [i0].[ProductId]
ORDER BY [s].[Id], [s].[Id0], [s].[Id1]
2025-08-11 10:56:47.117 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__productId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-11 10:56:47.120 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.ProductResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-11 10:56:47.121 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.GetProductById (DoorCompany.Api) in 21.7117ms
2025-08-11 10:56:47.123 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.GetProductById (DoorCompany.Api)'
2025-08-11 10:56:47.124 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Product/3 - 200 null application/json; charset=utf-8 58.9721ms
2025-08-11 11:05:55.090 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-11 11:05:55.160 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-11 11:05:55.463 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:05:55.486 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:05:55.635 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-11 11:05:55.733 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-11 11:05:55.772 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-11 11:05:55.784 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:05:55.793 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:05:55.802 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:05:55.811 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:05:55.818 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:05:55.828 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:05:56.025 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-11 11:05:56.174 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-11 11:05:56.301 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-11 11:05:56.308 +03:00 [INF] Hosting environment: Development
2025-08-11 11:05:56.310 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-11 11:05:56.400 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-11 11:05:56.599 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 208.3298ms
2025-08-11 11:05:56.623 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-11 11:05:56.628 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-11 11:05:56.632 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 9.2236ms
2025-08-11 11:05:56.674 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 45.8531ms
2025-08-11 11:05:56.865 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-11 11:05:56.923 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 57.5285ms
2025-08-11 11:07:14.965 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Product - multipart/form-data; boundary=----WebKitFormBoundaryea1AeCFWwcJD0NGu 1805
2025-08-11 11:07:14.987 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-11 11:07:15.025 +03:00 [INF] CORS policy execution successful.
2025-08-11 11:07:15.090 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api)'
2025-08-11 11:07:15.124 +03:00 [INF] Route matched with {action = "CreateProduct", controller = "Product"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateProductAsync(DoorCompany.Service.Dtos.ProductDto.CreateProductDto) on controller DoorCompany.Api.Controllers.ProductController (DoorCompany.Api).
2025-08-11 11:07:18.165 +03:00 [INF] Executed DbCommand (181ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Name] = @__createDto_Name_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:07:18.887 +03:00 [INF] Executed DbCommand (73ms) [Parameters=[@__createDto_Code_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Code] = @__createDto_Code_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:07:19.209 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[@__createDto_Barcode_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Products] AS [p]
        WHERE [p].[Barcode] = @__createDto_Barcode_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-11 11:07:20.819 +03:00 [INF] Executed DbCommand (38ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (DbType = Int32), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (DbType = Int32), @p10='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p12='?' (Size = 4000), @p13='?' (DbType = Int32), @p14='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Products] ([Barcode], [CategoryId], [Code], [CreatedAt], [CreatedBy], [Description], [ImagePath], [IsActive], [IsDeleted], [ItemType], [MaximumStock], [MinimumStock], [Name], [SortOrder], [StandardCost], [UnitId], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-08-11 11:07:21.969 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__productId_0
2025-08-11 11:07:22.023 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (DbType = Int32), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = Int32), @p9='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p10='?' (Size = 4000), @p11='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p12='?' (DbType = DateTime2), @p13='?' (DbType = Int32), @p14='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p15='?' (DbType = DateTime2), @p16='?' (DbType = Int32), @p17='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p18='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p19='?' (DbType = DateTime2), @p20='?' (DbType = Int32), @p21='?' (DbType = Boolean), @p22='?' (DbType = Boolean), @p23='?' (DbType = Int32), @p24='?' (DbType = DateTime2), @p25='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [InventoryTransactions] ([AverageCost], [BalanceAfter], [CreatedAt], [CreatedBy], [Description], [InvoiceMasterId], [IsActive], [IsDeleted], [ProductId], [Quantity], [ReferenceNumber], [TotalCost], [TransactionDate], [Type], [UnitCost], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO [ProductInventories] ([AverageCost], [Balance], [CreatedAt], [CreatedBy], [IsActive], [IsDeleted], [ProductId], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25);
2025-08-11 11:07:22.563 +03:00 [INF] Product created successfully: باب مصفح مودرن
2025-08-11 11:07:23.685 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Barcode], [s].[CategoryId], [s].[Code], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[ImagePath], [s].[IsActive], [s].[IsDeleted], [s].[ItemType], [s].[MaximumStock], [s].[MinimumStock], [s].[Name], [s].[SortOrder], [s].[StandardCost], [s].[UnitId], [s].[UpdatedAt], [s].[UpdatedBy], [s].[Id0], [s].[CategoryTypeId], [s].[Code0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description0], [s].[ImageUrl], [s].[IsActive0], [s].[IsDeleted0], [s].[Name0], [s].[ParentCategoryId], [s].[SortOrder0], [s].[Symbol], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[Id1], [s].[CreatedAt1], [s].[CreatedBy1], [s].[Description1], [s].[IsActive1], [s].[IsDeleted1], [s].[Name1], [s].[Symbol0], [s].[UpdatedAt1], [s].[UpdatedBy1], [i0].[Id], [i0].[AverageCost], [i0].[BalanceAfter], [i0].[CreatedAt], [i0].[CreatedBy], [i0].[Description], [i0].[InvoiceMasterId], [i0].[IsActive], [i0].[IsDeleted], [i0].[ProductId], [i0].[Quantity], [i0].[ReferenceNumber], [i0].[TotalCost], [i0].[TransactionDate], [i0].[Type], [i0].[UnitCost], [i0].[UpdatedAt], [i0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[Barcode], [p].[CategoryId], [p].[Code], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[ItemType], [p].[MaximumStock], [p].[MinimumStock], [p].[Name], [p].[SortOrder], [p].[StandardCost], [p].[UnitId], [p].[UpdatedAt], [p].[UpdatedBy], [i].[Id] AS [Id0], [i].[CategoryTypeId], [i].[Code] AS [Code0], [i].[CreatedAt] AS [CreatedAt0], [i].[CreatedBy] AS [CreatedBy0], [i].[Description] AS [Description0], [i].[ImageUrl], [i].[IsActive] AS [IsActive0], [i].[IsDeleted] AS [IsDeleted0], [i].[Name] AS [Name0], [i].[ParentCategoryId], [i].[SortOrder] AS [SortOrder0], [i].[Symbol], [i].[UpdatedAt] AS [UpdatedAt0], [i].[UpdatedBy] AS [UpdatedBy0], [u].[Id] AS [Id1], [u].[CreatedAt] AS [CreatedAt1], [u].[CreatedBy] AS [CreatedBy1], [u].[Description] AS [Description1], [u].[IsActive] AS [IsActive1], [u].[IsDeleted] AS [IsDeleted1], [u].[Name] AS [Name1], [u].[Symbol] AS [Symbol0], [u].[UpdatedAt] AS [UpdatedAt1], [u].[UpdatedBy] AS [UpdatedBy1]
    FROM [Products] AS [p]
    INNER JOIN [ItemCategories] AS [i] ON [p].[CategoryId] = [i].[Id]
    INNER JOIN [Units] AS [u] ON [p].[UnitId] = [u].[Id]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [s]
LEFT JOIN [InventoryTransactions] AS [i0] ON [s].[Id] = [i0].[ProductId]
ORDER BY [s].[Id], [s].[Id0], [s].[Id1]
2025-08-11 11:07:23.757 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[AverageCost], [p].[Balance], [p].[CreatedAt], [p].[CreatedBy], [p].[IsActive], [p].[IsDeleted], [p].[ProductId], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [ProductInventories] AS [p]
WHERE [p].[ProductId] = @__productId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-11 11:07:25.438 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.ProductDto.ProductResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-11 11:07:25.482 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api) in 10352.2009ms
2025-08-11 11:07:25.485 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ProductController.CreateProductAsync (DoorCompany.Api)'
2025-08-11 11:07:25.489 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Product - 200 null application/json; charset=utf-8 10523.5823ms
