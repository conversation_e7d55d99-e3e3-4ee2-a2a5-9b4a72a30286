import './polyfills.server.mjs';
import{a as Zo}from"./chunk-HY3M65TY.mjs";import{a as <PERSON>}from"./chunk-MQBUCPND.mjs";import{a as Yo,b as uc}from"./chunk-RBXEQ7AP.mjs";import{a as rc}from"./chunk-LSS2YBGE.mjs";import{a as Jo,c as ec,t as tc}from"./chunk-5SQR5QFE.mjs";import{A as Wo,B as Xo,E as Qo,F as va,ga as ur,h as zo,j as Go,r as $o}from"./chunk-UDSHM75V.mjs";import{$ as ma,$a as Ke,$b as tn,Ac as Bo,Cc as Fo,Db as ho,Dc as Uo,Ea as Jt,Fa as Tt,Fc as un,Gb as po,Gc as Vo,Hb as mo,Hc as rr,Ib as bo,Ic as nn,J as Ps,<PERSON><PERSON> as <PERSON>e,Jb as go,Jc as jo,<PERSON> as tu,<PERSON> as Zu,Kb as vo,Kc as ga,L as Et,La as Js,Lb as en,M as St,Ma as eo,N as ru,Nb as yo,O as qe,Oa as Ku,Ob as wo,P as Hs,Pa as Yu,Pb as wr,Qa as Le,Qb as xo,Rb as ft,S as Bs,Sa as vr,Sb as ht,T as Fs,Ta as to,U as Me,V as De,Va as ro,W as uu,X as pa,Xb as _o,Yb as Eo,Z as nu,Za as X,Zb as So,_ as Us,_a as Z,_b as tr,aa as Vs,ac as To,bc as au,ca as js,cc as No,d as qs,db as yr,eb as uo,fb as Te,ga as zs,gb as er,gc as Ao,ha as $u,hb as no,ia as Gs,ib as ao,ja as Wu,ka as $s,kb as io,la as Ws,lb as so,lc as Co,ma as Xs,mb as oo,mc as ko,na as Qs,nc as Lo,oc as Mo,pb as Ju,pc as Do,qa as Zs,qb as se,qc as Io,rb as co,rc as Oo,sa as Ks,sc as ba,ta as Xu,tc as rn,ua as Qu,va as xe,wc as Ro,xc as qo,ya as Ys,yb as lo,yc as Po,za as Rr,zb as fo,zc as Ho}from"./chunk-JADE4TID.mjs";import{a as ye,b as je,d as Yt,g as Os,h as ze,i as ha,j as Gu,k as gr,l as Rs}from"./chunk-S6KH3LOX.mjs";var lf=Object.getOwnPropertyNames,ne=(t,e)=>function(){return e||(0,t[lf(t)[0]])((e={exports:{}}).exports,e),e.exports},iu=ne({"external/npm/node_modules/domino/lib/Event.js"(t,e){e.exports=r,r.CAPTURING_PHASE=1,r.AT_TARGET=2,r.BUBBLING_PHASE=3;function r(n,u){if(this.type="",this.target=null,this.currentTarget=null,this.eventPhase=r.AT_TARGET,this.bubbles=!1,this.cancelable=!1,this.isTrusted=!1,this.defaultPrevented=!1,this.timeStamp=Date.now(),this._propagationStopped=!1,this._immediatePropagationStopped=!1,this._initialized=!0,this._dispatching=!1,n&&(this.type=n),u)for(var a in u)this[a]=u[a]}r.prototype=Object.create(Object.prototype,{constructor:{value:r},stopPropagation:{value:function(){this._propagationStopped=!0}},stopImmediatePropagation:{value:function(){this._propagationStopped=!0,this._immediatePropagationStopped=!0}},preventDefault:{value:function(){this.cancelable&&(this.defaultPrevented=!0)}},initEvent:{value:function(u,a,i){this._initialized=!0,!this._dispatching&&(this._propagationStopped=!1,this._immediatePropagationStopped=!1,this.defaultPrevented=!1,this.isTrusted=!1,this.target=null,this.type=u,this.bubbles=a,this.cancelable=i)}}})}}),ic=ne({"external/npm/node_modules/domino/lib/UIEvent.js"(t,e){var r=iu();e.exports=n;function n(){r.call(this),this.view=null,this.detail=0}n.prototype=Object.create(r.prototype,{constructor:{value:n},initUIEvent:{value:function(u,a,i,s,l){this.initEvent(u,a,i),this.view=s,this.detail=l}}})}}),sc=ne({"external/npm/node_modules/domino/lib/MouseEvent.js"(t,e){var r=ic();e.exports=n;function n(){r.call(this),this.screenX=this.screenY=this.clientX=this.clientY=0,this.ctrlKey=this.altKey=this.shiftKey=this.metaKey=!1,this.button=0,this.buttons=1,this.relatedTarget=null}n.prototype=Object.create(r.prototype,{constructor:{value:n},initMouseEvent:{value:function(u,a,i,s,l,o,c,d,f,y,v,w,A,S,_){switch(this.initEvent(u,a,i,s,l),this.screenX=o,this.screenY=c,this.clientX=d,this.clientY=f,this.ctrlKey=y,this.altKey=v,this.shiftKey=w,this.metaKey=A,this.button=S,S){case 0:this.buttons=1;break;case 1:this.buttons=4;break;case 2:this.buttons=2;break;default:this.buttons=0;break}this.relatedTarget=_}},getModifierState:{value:function(u){switch(u){case"Alt":return this.altKey;case"Control":return this.ctrlKey;case"Shift":return this.shiftKey;case"Meta":return this.metaKey;default:return!1}}}})}}),ya=ne({"external/npm/node_modules/domino/lib/config.js"(t){t.isApiWritable=!globalThis.__domino_frozen__}}),$e=ne({"external/npm/node_modules/domino/lib/utils.js"(t){var e=ya().isApiWritable;t.NAMESPACE={HTML:"http://www.w3.org/1999/xhtml",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/",MATHML:"http://www.w3.org/1998/Math/MathML",SVG:"http://www.w3.org/2000/svg",XLINK:"http://www.w3.org/1999/xlink"},t.IndexSizeError=()=>{throw new DOMException("The index is not in the allowed range","IndexSizeError")},t.HierarchyRequestError=()=>{throw new DOMException("The node tree hierarchy is not correct","HierarchyRequestError")},t.WrongDocumentError=()=>{throw new DOMException("The object is in the wrong Document","WrongDocumentError")},t.InvalidCharacterError=()=>{throw new DOMException("The string contains invalid characters","InvalidCharacterError")},t.NoModificationAllowedError=()=>{throw new DOMException("The object cannot be modified","NoModificationAllowedError")},t.NotFoundError=()=>{throw new DOMException("The object can not be found here","NotFoundError")},t.NotSupportedError=()=>{throw new DOMException("The operation is not supported","NotSupportedError")},t.InvalidStateError=()=>{throw new DOMException("The object is in an invalid state","InvalidStateError")},t.SyntaxError=()=>{throw new DOMException("The string did not match the expected pattern","SyntaxError")},t.InvalidModificationError=()=>{throw new DOMException("The object can not be modified in this way","InvalidModificationError")},t.NamespaceError=()=>{throw new DOMException("The operation is not allowed by Namespaces in XML","NamespaceError")},t.InvalidAccessError=()=>{throw new DOMException("The object does not support the operation or argument","InvalidAccessError")},t.TypeMismatchError=()=>{throw new DOMException("The type of the object does not match the expected type","TypeMismatchError")},t.SecurityError=()=>{throw new DOMException("The operation is insecure","SecurityError")},t.NetworkError=()=>{throw new DOMException("A network error occurred","NetworkError")},t.AbortError=()=>{throw new DOMException("The operation was aborted","AbortError")},t.UrlMismatchError=()=>{throw new DOMException("The given URL does not match another URL","URLMismatchError")},t.QuotaExceededError=()=>{throw new DOMException("The quota has been exceeded","QuotaExceededError")},t.TimeoutError=()=>{throw new DOMException("The operation timed out","TimeoutError")},t.InvalidNodeTypeError=()=>{throw new DOMException("The node is of an invalid type","InvalidNodeTypeError")},t.DataCloneError=()=>{throw new DOMException("The object can not be cloned","DataCloneError")},t.InUseAttributeError=()=>{throw new DOMException("The attribute is already in use","InUseAttributeError")},t.nyi=function(){throw new Error("NotYetImplemented")},t.shouldOverride=function(){throw new Error("Abstract function; should be overriding in subclass.")},t.assert=function(r,n){if(!r)throw new Error("Assertion failed: "+(n||"")+`
`+new Error().stack)},t.expose=function(r,n){for(var u in r)Object.defineProperty(n.prototype,u,{value:r[u],writable:e})},t.merge=function(r,n){for(var u in n)r[u]=n[u]},t.documentOrder=function(r,n){return 3-(r.compareDocumentPosition(n)&6)},t.toASCIILowerCase=function(r){return r.replace(/[A-Z]+/g,function(n){return n.toLowerCase()})},t.toASCIIUpperCase=function(r){return r.replace(/[a-z]+/g,function(n){return n.toUpperCase()})}}}),oc=ne({"external/npm/node_modules/domino/lib/EventTarget.js"(t,e){var r=iu(),n=sc(),u=$e();e.exports=a;function a(){}a.prototype={addEventListener:function(s,l,o){if(l){o===void 0&&(o=!1),this._listeners||(this._listeners=Object.create(null)),this._listeners[s]||(this._listeners[s]=[]);for(var c=this._listeners[s],d=0,f=c.length;d<f;d++){var y=c[d];if(y.listener===l&&y.capture===o)return}var v={listener:l,capture:o};typeof l=="function"&&(v.f=l),c.push(v)}},removeEventListener:function(s,l,o){if(o===void 0&&(o=!1),this._listeners){var c=this._listeners[s];if(c)for(var d=0,f=c.length;d<f;d++){var y=c[d];if(y.listener===l&&y.capture===o){c.length===1?this._listeners[s]=void 0:c.splice(d,1);return}}}},dispatchEvent:function(s){return this._dispatchEvent(s,!1)},_dispatchEvent:function(s,l){typeof l!="boolean"&&(l=!1);function o(w,A){var S=A.type,_=A.eventPhase;if(A.currentTarget=w,_!==r.CAPTURING_PHASE&&w._handlers&&w._handlers[S]){var x=w._handlers[S],T;if(typeof x=="function")T=x.call(A.currentTarget,A);else{var E=x.handleEvent;if(typeof E!="function")throw new TypeError("handleEvent property of event handler object isnot a function.");T=E.call(x,A)}switch(A.type){case"mouseover":T===!0&&A.preventDefault();break;case"beforeunload":default:T===!1&&A.preventDefault();break}}var O=w._listeners&&w._listeners[S];if(O){O=O.slice();for(var B=0,F=O.length;B<F;B++){if(A._immediatePropagationStopped)return;var q=O[B];if(!(_===r.CAPTURING_PHASE&&!q.capture||_===r.BUBBLING_PHASE&&q.capture))if(q.f)q.f.call(A.currentTarget,A);else{var C=q.listener.handleEvent;if(typeof C!="function")throw new TypeError("handleEvent property of event listener object is not a function.");C.call(q.listener,A)}}}}(!s._initialized||s._dispatching)&&u.InvalidStateError(),s.isTrusted=l,s._dispatching=!0,s.target=this;for(var c=[],d=this.parentNode;d;d=d.parentNode)c.push(d);s.eventPhase=r.CAPTURING_PHASE;for(var f=c.length-1;f>=0&&(o(c[f],s),!s._propagationStopped);f--);if(s._propagationStopped||(s.eventPhase=r.AT_TARGET,o(this,s)),s.bubbles&&!s._propagationStopped){s.eventPhase=r.BUBBLING_PHASE;for(var y=0,v=c.length;y<v&&(o(c[y],s),!s._propagationStopped);y++);}if(s._dispatching=!1,s.eventPhase=r.AT_TARGET,s.currentTarget=null,l&&!s.defaultPrevented&&s instanceof n)switch(s.type){case"mousedown":this._armed={x:s.clientX,y:s.clientY,t:s.timeStamp};break;case"mouseout":case"mouseover":this._armed=null;break;case"mouseup":this._isClick(s)&&this._doClick(s),this._armed=null;break}return!s.defaultPrevented},_isClick:function(i){return this._armed!==null&&i.type==="mouseup"&&i.isTrusted&&i.button===0&&i.timeStamp-this._armed.t<1e3&&Math.abs(i.clientX-this._armed.x)<10&&Math.abs(i.clientY-this._armed.Y)<10},_doClick:function(i){if(!this._click_in_progress){this._click_in_progress=!0;for(var s=this;s&&!s._post_click_activation_steps;)s=s.parentNode;s&&s._pre_click_activation_steps&&s._pre_click_activation_steps();var l=this.ownerDocument.createEvent("MouseEvent");l.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,i.screenX,i.screenY,i.clientX,i.clientY,i.ctrlKey,i.altKey,i.shiftKey,i.metaKey,i.button,null);var o=this._dispatchEvent(l,!0);s&&(o?s._post_click_activation_steps&&s._post_click_activation_steps(l):s._cancelled_activation_steps&&s._cancelled_activation_steps())}},_setEventHandler:function(s,l){this._handlers||(this._handlers=Object.create(null)),this._handlers[s]=l},_getEventHandler:function(s){return this._handlers&&this._handlers[s]||null}}}}),cc=ne({"external/npm/node_modules/domino/lib/LinkedList.js"(t,e){var r=$e(),n=e.exports={valid:function(u){return r.assert(u,"list falsy"),r.assert(u._previousSibling,"previous falsy"),r.assert(u._nextSibling,"next falsy"),!0},insertBefore:function(u,a){r.assert(n.valid(u)&&n.valid(a));var i=u,s=u._previousSibling,l=a,o=a._previousSibling;i._previousSibling=o,s._nextSibling=l,o._nextSibling=i,l._previousSibling=s,r.assert(n.valid(u)&&n.valid(a))},replace:function(u,a){r.assert(n.valid(u)&&(a===null||n.valid(a))),a!==null&&n.insertBefore(a,u),n.remove(u),r.assert(n.valid(u)&&(a===null||n.valid(a)))},remove:function(u){r.assert(n.valid(u));var a=u._previousSibling;if(a!==u){var i=u._nextSibling;a._nextSibling=i,i._previousSibling=a,u._previousSibling=u._nextSibling=u,r.assert(n.valid(u))}}}}}),lc=ne({"external/npm/node_modules/domino/lib/NodeUtils.js"(t,e){e.exports={serializeOne:A,\u0275escapeMatchingClosingTag:f,\u0275escapeClosingCommentTag:v,\u0275escapeProcessingInstructionContent:w};var r=$e(),n=r.NAMESPACE,u={STYLE:!0,SCRIPT:!0,XMP:!0,IFRAME:!0,NOEMBED:!0,NOFRAMES:!0,PLAINTEXT:!0},a={area:!0,base:!0,basefont:!0,bgsound:!0,br:!0,col:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},i={},s=/[&<>\u00A0]/g,l=/[&"<>\u00A0]/g;function o(S){return s.test(S)?S.replace(s,_=>{switch(_){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case"\xA0":return"&nbsp;"}}):S}function c(S){return l.test(S)?S.replace(l,_=>{switch(_){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case'"':return"&quot;";case"\xA0":return"&nbsp;"}}):S}function d(S){var _=S.namespaceURI;return _?_===n.XML?"xml:"+S.localName:_===n.XLINK?"xlink:"+S.localName:_===n.XMLNS?S.localName==="xmlns"?"xmlns":"xmlns:"+S.localName:S.name:S.localName}function f(S,_){let x="</"+_;if(!S.toLowerCase().includes(x))return S;let T=[...S],E=S.matchAll(new RegExp(x,"ig"));for(let O of E)T[O.index]="&lt;";return T.join("")}var y=/--!?>/;function v(S){return y.test(S)?S.replace(/(--\!?)>/g,"$1&gt;"):S}function w(S){return S.includes(">")?S.replaceAll(">","&gt;"):S}function A(S,_){var x="";switch(S.nodeType){case 1:var T=S.namespaceURI,E=T===n.HTML,O=E||T===n.SVG||T===n.MATHML?S.localName:S.tagName;x+="<"+O;for(var B=0,F=S._numattrs;B<F;B++){var q=S._attr(B);x+=" "+d(q),q.value!==void 0&&(x+='="'+c(q.value)+'"')}if(x+=">",!(E&&a[O])){var C=S.serialize();u[O.toUpperCase()]&&(C=f(C,O)),E&&i[O]&&C.charAt(0)===`
`&&(x+=`
`),x+=C,x+="</"+O+">"}break;case 3:case 4:var P;_.nodeType===1&&_.namespaceURI===n.HTML?P=_.tagName:P="",u[P]||P==="NOSCRIPT"&&_.ownerDocument._scripting_enabled?x+=S.data:x+=o(S.data);break;case 8:x+="<!--"+v(S.data)+"-->";break;case 7:let W=w(S.data);x+="<?"+S.target+" "+W+"?>";break;case 10:x+="<!DOCTYPE "+S.name,x+=">";break;default:r.InvalidStateError()}return x}}}),tt=ne({"external/npm/node_modules/domino/lib/Node.js"(t,e){e.exports=i;var r=oc(),n=cc(),u=lc(),a=$e();function i(){r.call(this),this.parentNode=null,this._nextSibling=this._previousSibling=this,this._index=void 0}var s=i.ELEMENT_NODE=1,l=i.ATTRIBUTE_NODE=2,o=i.TEXT_NODE=3,c=i.CDATA_SECTION_NODE=4,d=i.ENTITY_REFERENCE_NODE=5,f=i.ENTITY_NODE=6,y=i.PROCESSING_INSTRUCTION_NODE=7,v=i.COMMENT_NODE=8,w=i.DOCUMENT_NODE=9,A=i.DOCUMENT_TYPE_NODE=10,S=i.DOCUMENT_FRAGMENT_NODE=11,_=i.NOTATION_NODE=12,x=i.DOCUMENT_POSITION_DISCONNECTED=1,T=i.DOCUMENT_POSITION_PRECEDING=2,E=i.DOCUMENT_POSITION_FOLLOWING=4,O=i.DOCUMENT_POSITION_CONTAINS=8,B=i.DOCUMENT_POSITION_CONTAINED_BY=16,F=i.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC=32;i.prototype=Object.create(r.prototype,{baseURI:{get:a.nyi},parentElement:{get:function(){return this.parentNode&&this.parentNode.nodeType===s?this.parentNode:null}},hasChildNodes:{value:a.shouldOverride},firstChild:{get:a.shouldOverride},lastChild:{get:a.shouldOverride},isConnected:{get:function(){let q=this;for(;q!=null;){if(q.nodeType===i.DOCUMENT_NODE)return!0;q=q.parentNode,q!=null&&q.nodeType===i.DOCUMENT_FRAGMENT_NODE&&(q=q.host)}return!1}},previousSibling:{get:function(){var q=this.parentNode;return!q||this===q.firstChild?null:this._previousSibling}},nextSibling:{get:function(){var q=this.parentNode,C=this._nextSibling;return!q||C===q.firstChild?null:C}},textContent:{get:function(){return null},set:function(q){}},innerText:{get:function(){return null},set:function(q){}},_countChildrenOfType:{value:function(q){for(var C=0,P=this.firstChild;P!==null;P=P.nextSibling)P.nodeType===q&&C++;return C}},_ensureInsertValid:{value:function(C,P,W){var h=this,m,g;if(!C.nodeType)throw new TypeError("not a node");switch(h.nodeType){case w:case S:case s:break;default:a.HierarchyRequestError()}switch(C.isAncestor(h)&&a.HierarchyRequestError(),(P!==null||!W)&&P.parentNode!==h&&a.NotFoundError(),C.nodeType){case S:case A:case s:case o:case y:case v:break;default:a.HierarchyRequestError()}if(h.nodeType===w)switch(C.nodeType){case o:a.HierarchyRequestError();break;case S:switch(C._countChildrenOfType(o)>0&&a.HierarchyRequestError(),C._countChildrenOfType(s)){case 0:break;case 1:if(P!==null)for(W&&P.nodeType===A&&a.HierarchyRequestError(),g=P.nextSibling;g!==null;g=g.nextSibling)g.nodeType===A&&a.HierarchyRequestError();m=h._countChildrenOfType(s),W?m>0&&a.HierarchyRequestError():(m>1||m===1&&P.nodeType!==s)&&a.HierarchyRequestError();break;default:a.HierarchyRequestError()}break;case s:if(P!==null)for(W&&P.nodeType===A&&a.HierarchyRequestError(),g=P.nextSibling;g!==null;g=g.nextSibling)g.nodeType===A&&a.HierarchyRequestError();m=h._countChildrenOfType(s),W?m>0&&a.HierarchyRequestError():(m>1||m===1&&P.nodeType!==s)&&a.HierarchyRequestError();break;case A:if(P===null)h._countChildrenOfType(s)&&a.HierarchyRequestError();else for(g=h.firstChild;g!==null&&g!==P;g=g.nextSibling)g.nodeType===s&&a.HierarchyRequestError();m=h._countChildrenOfType(A),W?m>0&&a.HierarchyRequestError():(m>1||m===1&&P.nodeType!==A)&&a.HierarchyRequestError();break}else C.nodeType===A&&a.HierarchyRequestError()}},insertBefore:{value:function(C,P){var W=this;W._ensureInsertValid(C,P,!0);var h=P;return h===C&&(h=C.nextSibling),W.doc.adoptNode(C),C._insertOrReplace(W,h,!1),C}},appendChild:{value:function(q){return this.insertBefore(q,null)}},_appendChild:{value:function(q){q._insertOrReplace(this,null,!1)}},removeChild:{value:function(C){var P=this;if(!C.nodeType)throw new TypeError("not a node");return C.parentNode!==P&&a.NotFoundError(),C.remove(),C}},replaceChild:{value:function(C,P){var W=this;return W._ensureInsertValid(C,P,!1),C.doc!==W.doc&&W.doc.adoptNode(C),C._insertOrReplace(W,P,!0),P}},contains:{value:function(C){return C===null?!1:this===C?!0:(this.compareDocumentPosition(C)&B)!==0}},compareDocumentPosition:{value:function(C){if(this===C)return 0;if(this.doc!==C.doc||this.rooted!==C.rooted)return x+F;for(var P=[],W=[],h=this;h!==null;h=h.parentNode)P.push(h);for(h=C;h!==null;h=h.parentNode)W.push(h);if(P.reverse(),W.reverse(),P[0]!==W[0])return x+F;h=Math.min(P.length,W.length);for(var m=1;m<h;m++)if(P[m]!==W[m])return P[m].index<W[m].index?E:T;return P.length<W.length?E+B:T+O}},isSameNode:{value:function(C){return this===C}},isEqualNode:{value:function(C){if(!C||C.nodeType!==this.nodeType||!this.isEqual(C))return!1;for(var P=this.firstChild,W=C.firstChild;P&&W;P=P.nextSibling,W=W.nextSibling)if(!P.isEqualNode(W))return!1;return P===null&&W===null}},cloneNode:{value:function(q){var C=this.clone();if(q)for(var P=this.firstChild;P!==null;P=P.nextSibling)C._appendChild(P.cloneNode(!0));return C}},lookupPrefix:{value:function(C){var P;if(C===""||C===null||C===void 0)return null;switch(this.nodeType){case s:return this._lookupNamespacePrefix(C,this);case w:return P=this.documentElement,P?P.lookupPrefix(C):null;case f:case _:case S:case A:return null;case l:return P=this.ownerElement,P?P.lookupPrefix(C):null;default:return P=this.parentElement,P?P.lookupPrefix(C):null}}},lookupNamespaceURI:{value:function(C){(C===""||C===void 0)&&(C=null);var P;switch(this.nodeType){case s:return a.shouldOverride();case w:return P=this.documentElement,P?P.lookupNamespaceURI(C):null;case f:case _:case A:case S:return null;case l:return P=this.ownerElement,P?P.lookupNamespaceURI(C):null;default:return P=this.parentElement,P?P.lookupNamespaceURI(C):null}}},isDefaultNamespace:{value:function(C){(C===""||C===void 0)&&(C=null);var P=this.lookupNamespaceURI(null);return P===C}},index:{get:function(){var q=this.parentNode;if(this===q.firstChild)return 0;var C=q.childNodes;if(this._index===void 0||C[this._index]!==this){for(var P=0;P<C.length;P++)C[P]._index=P;a.assert(C[this._index]===this)}return this._index}},isAncestor:{value:function(q){if(this.doc!==q.doc||this.rooted!==q.rooted)return!1;for(var C=q;C;C=C.parentNode)if(C===this)return!0;return!1}},ensureSameDoc:{value:function(q){q.ownerDocument===null?q.ownerDocument=this.doc:q.ownerDocument!==this.doc&&a.WrongDocumentError()}},removeChildren:{value:a.shouldOverride},_insertOrReplace:{value:function(C,P,W){var h=this,m,g;if(h.nodeType===S&&h.rooted&&a.HierarchyRequestError(),C._childNodes&&(m=P===null?C._childNodes.length:P.index,h.parentNode===C)){var M=h.index;M<m&&m--}W&&(P.rooted&&P.doc.mutateRemove(P),P.parentNode=null);var H=P;H===null&&(H=C.firstChild);var $=h.rooted&&C.rooted;if(h.nodeType===S){for(var J=[0,W?1:0],de,pe=h.firstChild;pe!==null;pe=de)de=pe.nextSibling,J.push(pe),pe.parentNode=C;var k=J.length;if(W?n.replace(H,k>2?J[2]:null):k>2&&H!==null&&n.insertBefore(J[2],H),C._childNodes)for(J[0]=P===null?C._childNodes.length:P._index,C._childNodes.splice.apply(C._childNodes,J),g=2;g<k;g++)J[g]._index=J[0]+(g-2);else C._firstChild===P&&(k>2?C._firstChild=J[2]:W&&(C._firstChild=null));if(h._childNodes?h._childNodes.length=0:h._firstChild=null,C.rooted)for(C.modify(),g=2;g<k;g++)C.doc.mutateInsert(J[g])}else{if(P===h)return;$?h._remove():h.parentNode&&h.remove(),h.parentNode=C,W?(n.replace(H,h),C._childNodes?(h._index=m,C._childNodes[m]=h):C._firstChild===P&&(C._firstChild=h)):(H!==null&&n.insertBefore(h,H),C._childNodes?(h._index=m,C._childNodes.splice(m,0,h)):C._firstChild===P&&(C._firstChild=h)),$?(C.modify(),C.doc.mutateMove(h)):C.rooted&&(C.modify(),C.doc.mutateInsert(h))}}},lastModTime:{get:function(){return this._lastModTime||(this._lastModTime=this.doc.modclock),this._lastModTime}},modify:{value:function(){if(this.doc.modclock)for(var q=++this.doc.modclock,C=this;C;C=C.parentElement)C._lastModTime&&(C._lastModTime=q)}},doc:{get:function(){return this.ownerDocument||this}},rooted:{get:function(){return!!this._nid}},normalize:{value:function(){for(var q,C=this.firstChild;C!==null;C=q)if(q=C.nextSibling,C.normalize&&C.normalize(),C.nodeType===i.TEXT_NODE){if(C.nodeValue===""){this.removeChild(C);continue}var P=C.previousSibling;P!==null&&P.nodeType===i.TEXT_NODE&&(P.appendData(C.nodeValue),this.removeChild(C))}}},serialize:{value:function(){if(this._innerHTML)return this._innerHTML;for(var q="",C=this.firstChild;C!==null;C=C.nextSibling)q+=u.serializeOne(C,this);return q}},outerHTML:{get:function(){return u.serializeOne(this,{nodeType:0})},set:a.nyi},ELEMENT_NODE:{value:s},ATTRIBUTE_NODE:{value:l},TEXT_NODE:{value:o},CDATA_SECTION_NODE:{value:c},ENTITY_REFERENCE_NODE:{value:d},ENTITY_NODE:{value:f},PROCESSING_INSTRUCTION_NODE:{value:y},COMMENT_NODE:{value:v},DOCUMENT_NODE:{value:w},DOCUMENT_TYPE_NODE:{value:A},DOCUMENT_FRAGMENT_NODE:{value:S},NOTATION_NODE:{value:_},DOCUMENT_POSITION_DISCONNECTED:{value:x},DOCUMENT_POSITION_PRECEDING:{value:T},DOCUMENT_POSITION_FOLLOWING:{value:E},DOCUMENT_POSITION_CONTAINS:{value:O},DOCUMENT_POSITION_CONTAINED_BY:{value:B},DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:{value:F}})}}),df=ne({"external/npm/node_modules/domino/lib/NodeList.es6.js"(t,e){e.exports=class extends Array{constructor(n){if(super(n&&n.length||0),n)for(var u in n)this[u]=n[u]}item(n){return this[n]||null}}}}),ff=ne({"external/npm/node_modules/domino/lib/NodeList.es5.js"(t,e){function r(u){return this[u]||null}function n(u){return u||(u=[]),u.item=r,u}e.exports=n}}),qr=ne({"external/npm/node_modules/domino/lib/NodeList.js"(t,e){var r;try{r=df()}catch{r=ff()}e.exports=r}}),wa=ne({"external/npm/node_modules/domino/lib/ContainerNode.js"(t,e){e.exports=u;var r=tt(),n=qr();function u(){r.call(this),this._firstChild=this._childNodes=null}u.prototype=Object.create(r.prototype,{hasChildNodes:{value:function(){return this._childNodes?this._childNodes.length>0:this._firstChild!==null}},childNodes:{get:function(){return this._ensureChildNodes(),this._childNodes}},firstChild:{get:function(){return this._childNodes?this._childNodes.length===0?null:this._childNodes[0]:this._firstChild}},lastChild:{get:function(){var a=this._childNodes,i;return a?a.length===0?null:a[a.length-1]:(i=this._firstChild,i===null?null:i._previousSibling)}},_ensureChildNodes:{value:function(){if(!this._childNodes){var a=this._firstChild,i=a,s=this._childNodes=new n;if(a)do s.push(i),i=i._nextSibling;while(i!==a);this._firstChild=null}}},removeChildren:{value:function(){for(var i=this.rooted?this.ownerDocument:null,s=this.firstChild,l;s!==null;)l=s,s=l.nextSibling,i&&i.mutateRemove(l),l.parentNode=null;this._childNodes?this._childNodes.length=0:this._firstChild=null,this.modify()}}})}}),xa=ne({"external/npm/node_modules/domino/lib/xmlnames.js"(t){t.isValidName=w,t.isValidQName=A;var e=/^[_:A-Za-z][-.:\w]+$/,r=/^([_A-Za-z][-.\w]+|[_A-Za-z][-.\w]+:[_A-Za-z][-.\w]+)$/,n="_A-Za-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",u="-._A-Za-z0-9\xB7\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0300-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",a="["+n+"]["+u+"]*",i=n+":",s=u+":",l=new RegExp("^["+i+"]["+s+"]*$"),o=new RegExp("^("+a+"|"+a+":"+a+")$"),c=/[\uD800-\uDB7F\uDC00-\uDFFF]/,d=/[\uD800-\uDB7F\uDC00-\uDFFF]/g,f=/[\uD800-\uDB7F][\uDC00-\uDFFF]/g;n+="\uD800-\u{EFC00}-\uDFFF",u+="\uD800-\u{EFC00}-\uDFFF",a="["+n+"]["+u+"]*",i=n+":",s=u+":";var y=new RegExp("^["+i+"]["+s+"]*$"),v=new RegExp("^("+a+"|"+a+":"+a+")$");function w(S){if(e.test(S)||l.test(S))return!0;if(!c.test(S)||!y.test(S))return!1;var _=S.match(d),x=S.match(f);return x!==null&&2*x.length===_.length}function A(S){if(r.test(S)||o.test(S))return!0;if(!c.test(S)||!v.test(S))return!1;var _=S.match(d),x=S.match(f);return x!==null&&2*x.length===_.length}}}),dc=ne({"external/npm/node_modules/domino/lib/attributes.js"(t){var e=$e();t.property=function(n){if(Array.isArray(n.type)){var u=Object.create(null);n.type.forEach(function(s){u[s.value||s]=s.alias||s});var a=n.missing;a===void 0&&(a=null);var i=n.invalid;return i===void 0&&(i=a),{get:function(){var s=this._getattr(n.name);return s===null?a:(s=u[s.toLowerCase()],s!==void 0?s:i!==null?i:s)},set:function(s){this._setattr(n.name,s)}}}else{if(n.type===Boolean)return{get:function(){return this.hasAttribute(n.name)},set:function(s){s?this._setattr(n.name,""):this.removeAttribute(n.name)}};if(n.type===Number||n.type==="long"||n.type==="unsigned long"||n.type==="limited unsigned long with fallback")return r(n);if(!n.type||n.type===String)return{get:function(){return this._getattr(n.name)||""},set:function(s){n.treatNullAsEmptyString&&s===null&&(s=""),this._setattr(n.name,s)}};if(typeof n.type=="function")return n.type(n.name,n)}throw new Error("Invalid attribute definition")};function r(n){var u;typeof n.default=="function"?u=n.default:typeof n.default=="number"?u=function(){return n.default}:u=function(){e.assert(!1,typeof n.default)};var a=n.type==="unsigned long",i=n.type==="long",s=n.type==="limited unsigned long with fallback",l=n.min,o=n.max,c=n.setmin;return l===void 0&&(a&&(l=0),i&&(l=-2147483648),s&&(l=1)),o===void 0&&(a||i||s)&&(o=2147483647),{get:function(){var d=this._getattr(n.name),f=n.float?parseFloat(d):parseInt(d,10);if(d===null||!isFinite(f)||l!==void 0&&f<l||o!==void 0&&f>o)return u.call(this);if(a||i||s){if(!/^[ \t\n\f\r]*[-+]?[0-9]/.test(d))return u.call(this);f=f|0}return f},set:function(d){n.float||(d=Math.floor(d)),c!==void 0&&d<c&&e.IndexSizeError(n.name+" set to "+d),a?d=d<0||d>2147483647?u.call(this):d|0:s?d=d<1||d>2147483647?u.call(this):d|0:i&&(d=d<-2147483648||d>2147483647?u.call(this):d|0),this._setattr(n.name,String(d))}}}t.registerChangeHandler=function(n,u,a){var i=n.prototype;Object.prototype.hasOwnProperty.call(i,"_attributeChangeHandlers")||(i._attributeChangeHandlers=Object.create(i._attributeChangeHandlers||null)),i._attributeChangeHandlers[u]=a}}}),hf=ne({"external/npm/node_modules/domino/lib/FilteredElementList.js"(t,e){e.exports=n;var r=tt();function n(u,a){this.root=u,this.filter=a,this.lastModTime=u.lastModTime,this.done=!1,this.cache=[],this.traverse()}n.prototype=Object.create(Object.prototype,{length:{get:function(){return this.checkcache(),this.done||this.traverse(),this.cache.length}},item:{value:function(u){return this.checkcache(),!this.done&&u>=this.cache.length&&this.traverse(),this.cache[u]}},checkcache:{value:function(){if(this.lastModTime!==this.root.lastModTime){for(var u=this.cache.length-1;u>=0;u--)this[u]=void 0;this.cache.length=0,this.done=!1,this.lastModTime=this.root.lastModTime}}},traverse:{value:function(u){u!==void 0&&u++;for(var a;(a=this.next())!==null;)if(this[this.cache.length]=a,this.cache.push(a),u&&this.cache.length===u)return;this.done=!0}},next:{value:function(){var u=this.cache.length===0?this.root:this.cache[this.cache.length-1],a;for(u.nodeType===r.DOCUMENT_NODE?a=u.documentElement:a=u.nextElement(this.root);a;){if(this.filter(a))return a;a=a.nextElement(this.root)}return null}}})}}),fc=ne({"external/npm/node_modules/domino/lib/DOMTokenList.js"(t,e){var r=$e();e.exports=n;function n(l,o){this._getString=l,this._setString=o,this._length=0,this._lastStringValue="",this._update()}Object.defineProperties(n.prototype,{length:{get:function(){return this._length}},item:{value:function(l){var o=s(this);return l<0||l>=o.length?null:o[l]}},contains:{value:function(l){l=String(l);var o=s(this);return o.indexOf(l)>-1}},add:{value:function(){for(var l=s(this),o=0,c=arguments.length;o<c;o++){var d=a(arguments[o]);l.indexOf(d)<0&&l.push(d)}this._update(l)}},remove:{value:function(){for(var l=s(this),o=0,c=arguments.length;o<c;o++){var d=a(arguments[o]),f=l.indexOf(d);f>-1&&l.splice(f,1)}this._update(l)}},toggle:{value:function(o,c){return o=a(o),this.contains(o)?c===void 0||c===!1?(this.remove(o),!1):!0:c===void 0||c===!0?(this.add(o),!0):!1}},replace:{value:function(o,c){String(c)===""&&r.SyntaxError(),o=a(o),c=a(c);var d=s(this),f=d.indexOf(o);if(f<0)return!1;var y=d.indexOf(c);return y<0?d[f]=c:f<y?(d[f]=c,d.splice(y,1)):d.splice(f,1),this._update(d),!0}},toString:{value:function(){return this._getString()}},value:{get:function(){return this._getString()},set:function(l){this._setString(l),this._update()}},_update:{value:function(l){l?(u(this,l),this._setString(l.join(" ").trim())):u(this,s(this)),this._lastStringValue=this._getString()}}});function u(l,o){var c=l._length,d;for(l._length=o.length,d=0;d<o.length;d++)l[d]=o[d];for(;d<c;d++)l[d]=void 0}function a(l){return l=String(l),l===""&&r.SyntaxError(),/[ \t\r\n\f]/.test(l)&&r.InvalidCharacterError(),l}function i(l){for(var o=l._length,c=Array(o),d=0;d<o;d++)c[d]=l[d];return c}function s(l){var o=l._getString();if(o===l._lastStringValue)return i(l);var c=o.replace(/(^[ \t\r\n\f]+)|([ \t\r\n\f]+$)/g,"");if(c==="")return[];var d=Object.create(null);return c.split(/[ \t\r\n\f]+/g).filter(function(f){var y="$"+f;return d[y]?!1:(d[y]=!0,!0)})}}}),_a=ne({"external/npm/node_modules/domino/lib/select.js"(t,e){var r=Object.create(null,{location:{get:function(){throw new Error("window.location is not supported.")}}}),n=function(h,m){return h.compareDocumentPosition(m)},u=function(h,m){return n(h,m)&2?1:-1},a=function(h){for(;(h=h.nextSibling)&&h.nodeType!==1;);return h},i=function(h){for(;(h=h.previousSibling)&&h.nodeType!==1;);return h},s=function(h){if(h=h.firstChild)for(;h.nodeType!==1&&(h=h.nextSibling););return h},l=function(h){if(h=h.lastChild)for(;h.nodeType!==1&&(h=h.previousSibling););return h},o=function(h){if(!h.parentNode)return!1;var m=h.parentNode.nodeType;return m===1||m===9},c=function(h){if(!h)return h;var m=h[0];return m==='"'||m==="'"?(h[h.length-1]===m?h=h.slice(1,-1):h=h.slice(1),h.replace(E.str_escape,function(g){var M=/^\\(?:([0-9A-Fa-f]+)|([\r\n\f]+))/.exec(g);if(!M)return g.slice(1);if(M[2])return"";var H=parseInt(M[1],16);return String.fromCodePoint?String.fromCodePoint(H):String.fromCharCode(H)})):E.ident.test(h)?d(h):h},d=function(h){return h.replace(E.escape,function(m){var g=/^\\([0-9A-Fa-f]+)/.exec(m);if(!g)return m[1];var M=parseInt(g[1],16);return String.fromCodePoint?String.fromCodePoint(M):String.fromCharCode(M)})},f=function(){return Array.prototype.indexOf?Array.prototype.indexOf:function(h,m){for(var g=this.length;g--;)if(this[g]===m)return g;return-1}}(),y=function(h,m){var g=E.inside.source.replace(/</g,h).replace(/>/g,m);return new RegExp(g)},v=function(h,m,g){return h=h.source,h=h.replace(m,g.source||g),new RegExp(h)},w=function(h,m){return h.replace(/^(?:\w+:\/\/|\/+)/,"").replace(/(?:\/+|\/*#.*?)$/,"").split("/",m).join("/")},A=function(h,m){var g=h.replace(/\s+/g,""),M;return g==="even"?g="2n+0":g==="odd"?g="2n+1":g.indexOf("n")===-1&&(g="0n"+g),M=/^([+-])?(\d+)?n([+-])?(\d+)?$/.exec(g),{group:M[1]==="-"?-(M[2]||1):+(M[2]||1),offset:M[4]?M[3]==="-"?-M[4]:+M[4]:0}},S=function(h,m,g){var M=A(h),H=M.group,$=M.offset,J=g?l:s,de=g?i:a;return function(pe){if(o(pe))for(var k=J(pe.parentNode),D=0;k;){if(m(k,pe)&&D++,k===pe)return D-=$,H&&D?D%H===0&&D<0==H<0:!D;k=de(k)}}},_={"*":function(){return function(){return!0}}(),type:function(h){return h=h.toLowerCase(),function(m){return m.nodeName.toLowerCase()===h}},attr:function(h,m,g,M){return m=x[m],function(H){var $;switch(h){case"for":$=H.htmlFor;break;case"class":$=H.className,$===""&&H.getAttribute("class")==null&&($=null);break;case"href":case"src":$=H.getAttribute(h,2);break;case"title":$=H.getAttribute("title")||null;break;case"id":case"lang":case"dir":case"accessKey":case"hidden":case"tabIndex":case"style":if(H.getAttribute){$=H.getAttribute(h);break}default:if(H.hasAttribute&&!H.hasAttribute(h))break;$=H[h]!=null?H[h]:H.getAttribute&&H.getAttribute(h);break}if($!=null)return $=$+"",M&&($=$.toLowerCase(),g=g.toLowerCase()),m($,g)}},":first-child":function(h){return!i(h)&&o(h)},":last-child":function(h){return!a(h)&&o(h)},":only-child":function(h){return!i(h)&&!a(h)&&o(h)},":nth-child":function(h,m){return S(h,function(){return!0},m)},":nth-last-child":function(h){return _[":nth-child"](h,!0)},":root":function(h){return h.ownerDocument.documentElement===h},":empty":function(h){return!h.firstChild},":not":function(h){var m=P(h);return function(g){return!m(g)}},":first-of-type":function(h){if(o(h)){for(var m=h.nodeName;h=i(h);)if(h.nodeName===m)return;return!0}},":last-of-type":function(h){if(o(h)){for(var m=h.nodeName;h=a(h);)if(h.nodeName===m)return;return!0}},":only-of-type":function(h){return _[":first-of-type"](h)&&_[":last-of-type"](h)},":nth-of-type":function(h,m){return S(h,function(g,M){return g.nodeName===M.nodeName},m)},":nth-last-of-type":function(h){return _[":nth-of-type"](h,!0)},":checked":function(h){return!!(h.checked||h.selected)},":indeterminate":function(h){return!_[":checked"](h)},":enabled":function(h){return!h.disabled&&h.type!=="hidden"},":disabled":function(h){return!!h.disabled},":target":function(h){return h.id===r.location.hash.substring(1)},":focus":function(h){return h===h.ownerDocument.activeElement},":is":function(h){return P(h)},":matches":function(h){return _[":is"](h)},":nth-match":function(h,m){var g=h.split(/\s*,\s*/),M=g.shift(),H=P(g.join(","));return S(M,H,m)},":nth-last-match":function(h){return _[":nth-match"](h,!0)},":links-here":function(h){return h+""==r.location+""},":lang":function(h){return function(m){for(;m;){if(m.lang)return m.lang.indexOf(h)===0;m=m.parentNode}}},":dir":function(h){return function(m){for(;m;){if(m.dir)return m.dir===h;m=m.parentNode}}},":scope":function(h,m){var g=m||h.ownerDocument;return g.nodeType===9?h===g.documentElement:h===g},":any-link":function(h){return typeof h.href=="string"},":local-link":function(h){if(h.nodeName)return h.href&&h.host===r.location.host;var m=+h+1;return function(g){if(g.href){var M=r.location+"",H=g+"";return w(M,m)===w(H,m)}}},":default":function(h){return!!h.defaultSelected},":valid":function(h){return h.willValidate||h.validity&&h.validity.valid},":invalid":function(h){return!_[":valid"](h)},":in-range":function(h){return h.value>h.min&&h.value<=h.max},":out-of-range":function(h){return!_[":in-range"](h)},":required":function(h){return!!h.required},":optional":function(h){return!h.required},":read-only":function(h){if(h.readOnly)return!0;var m=h.getAttribute("contenteditable"),g=h.contentEditable,M=h.nodeName.toLowerCase();return M=M!=="input"&&M!=="textarea",(M||h.disabled)&&m==null&&g!=="true"},":read-write":function(h){return!_[":read-only"](h)},":hover":function(){throw new Error(":hover is not supported.")},":active":function(){throw new Error(":active is not supported.")},":link":function(){throw new Error(":link is not supported.")},":visited":function(){throw new Error(":visited is not supported.")},":column":function(){throw new Error(":column is not supported.")},":nth-column":function(){throw new Error(":nth-column is not supported.")},":nth-last-column":function(){throw new Error(":nth-last-column is not supported.")},":current":function(){throw new Error(":current is not supported.")},":past":function(){throw new Error(":past is not supported.")},":future":function(){throw new Error(":future is not supported.")},":contains":function(h){return function(m){var g=m.innerText||m.textContent||m.value||"";return g.indexOf(h)!==-1}},":has":function(h){return function(m){return W(h,m).length>0}}},x={"-":function(){return!0},"=":function(h,m){return h===m},"*=":function(h,m){return h.indexOf(m)!==-1},"~=":function(h,m){var g,M,H,$;for(M=0;;M=g+1){if(g=h.indexOf(m,M),g===-1)return!1;if(H=h[g-1],$=h[g+m.length],(!H||H===" ")&&(!$||$===" "))return!0}},"|=":function(h,m){var g=h.indexOf(m),M;if(g===0)return M=h[g+m.length],M==="-"||!M},"^=":function(h,m){return h.indexOf(m)===0},"$=":function(h,m){var g=h.lastIndexOf(m);return g!==-1&&g+m.length===h.length},"!=":function(h,m){return h!==m}},T={" ":function(h){return function(m){for(;m=m.parentNode;)if(h(m))return m}},">":function(h){return function(m){if(m=m.parentNode)return h(m)&&m}},"+":function(h){return function(m){if(m=i(m))return h(m)&&m}},"~":function(h){return function(m){for(;m=i(m);)if(h(m))return m}},noop:function(h){return function(m){return h(m)&&m}},ref:function(h,m){var g;function M(H){for(var $=H.ownerDocument,J=$.getElementsByTagName("*"),de=J.length;de--;)if(g=J[de],M.test(H))return g=null,!0;g=null}return M.combinator=function(H){if(!(!g||!g.getAttribute)){var $=g.getAttribute(m)||"";if($[0]==="#"&&($=$.substring(1)),$===H.id&&h(g))return g}},M}},E={escape:/\\(?:[^0-9A-Fa-f\r\n]|[0-9A-Fa-f]{1,6}[\r\n\t ]?)/g,str_escape:/(escape)|\\(\n|\r\n?|\f)/g,nonascii:/[\u00A0-\uFFFF]/,cssid:/(?:(?!-?[0-9])(?:escape|nonascii|[-_a-zA-Z0-9])+)/,qname:/^ *(cssid|\*)/,simple:/^(?:([.#]cssid)|pseudo|attr)/,ref:/^ *\/(cssid)\/ */,combinator:/^(?: +([^ \w*.#\\]) +|( )+|([^ \w*.#\\]))(?! *$)/,attr:/^\[(cssid)(?:([^\w]?=)(inside))?\]/,pseudo:/^(:cssid)(?:\((inside)\))?/,inside:/(?:"(?:\\"|[^"])*"|'(?:\\'|[^'])*'|<[^"'>]*>|\\["'>]|[^"'>])*/,ident:/^(cssid)$/};E.cssid=v(E.cssid,"nonascii",E.nonascii),E.cssid=v(E.cssid,"escape",E.escape),E.qname=v(E.qname,"cssid",E.cssid),E.simple=v(E.simple,"cssid",E.cssid),E.ref=v(E.ref,"cssid",E.cssid),E.attr=v(E.attr,"cssid",E.cssid),E.pseudo=v(E.pseudo,"cssid",E.cssid),E.inside=v(E.inside,`[^"'>]*`,E.inside),E.attr=v(E.attr,"inside",y("\\[","\\]")),E.pseudo=v(E.pseudo,"inside",y("\\(","\\)")),E.simple=v(E.simple,"pseudo",E.pseudo),E.simple=v(E.simple,"attr",E.attr),E.ident=v(E.ident,"cssid",E.cssid),E.str_escape=v(E.str_escape,"escape",E.escape);var O=function(h){for(var m=h.replace(/^\s+|\s+$/g,""),g,M=[],H=[],$,J,de,pe,k;m;){if(de=E.qname.exec(m))m=m.substring(de[0].length),J=d(de[1]),H.push(B(J,!0));else if(de=E.simple.exec(m))m=m.substring(de[0].length),J="*",H.push(B(J,!0)),H.push(B(de));else throw new SyntaxError("Invalid selector.");for(;de=E.simple.exec(m);)m=m.substring(de[0].length),H.push(B(de));if(m[0]==="!"&&(m=m.substring(1),$=C(),$.qname=J,H.push($.simple)),de=E.ref.exec(m)){m=m.substring(de[0].length),k=T.ref(F(H),d(de[1])),M.push(k.combinator),H=[];continue}if(de=E.combinator.exec(m)){if(m=m.substring(de[0].length),pe=de[1]||de[2]||de[3],pe===","){M.push(T.noop(F(H)));break}}else pe="noop";if(!T[pe])throw new SyntaxError("Bad combinator.");M.push(T[pe](F(H))),H=[]}return g=q(M),g.qname=J,g.sel=m,$&&($.lname=g.qname,$.test=g,$.qname=$.qname,$.sel=g.sel,g=$),k&&(k.test=g,k.qname=g.qname,k.sel=g.sel,g=k),g},B=function(h,m){if(m)return h==="*"?_["*"]:_.type(h);if(h[1])return h[1][0]==="."?_.attr("class","~=",d(h[1].substring(1)),!1):_.attr("id","=",d(h[1].substring(1)),!1);if(h[2])return h[3]?_[d(h[2])](c(h[3])):_[d(h[2])];if(h[4]){var g=h[6],M=/["'\s]\s*I$/i.test(g);return M&&(g=g.replace(/\s*I$/i,"")),_.attr(d(h[4]),h[5]||"-",c(g),M)}throw new SyntaxError("Unknown Selector.")},F=function(h){var m=h.length,g;return m<2?h[0]:function(M){if(M){for(g=0;g<m;g++)if(!h[g](M))return;return!0}}},q=function(h){return h.length<2?function(m){return!!h[0](m)}:function(m){for(var g=h.length;g--;)if(!(m=h[g](m)))return;return!0}},C=function(){var h;function m(g){for(var M=g.ownerDocument,H=M.getElementsByTagName(m.lname),$=H.length;$--;)if(m.test(H[$])&&h===g)return h=null,!0;h=null}return m.simple=function(g){return h=g,!0},m},P=function(h){for(var m=O(h),g=[m];m.sel;)m=O(m.sel),g.push(m);return g.length<2?m:function(M){for(var H=g.length,$=0;$<H;$++)if(g[$](M))return!0}},W=function(h,m){for(var g=[],M=O(h),H=m.getElementsByTagName(M.qname),$=0,J;J=H[$++];)M(J)&&g.push(J);if(M.sel){for(;M.sel;)for(M=O(M.sel),H=m.getElementsByTagName(M.qname),$=0;J=H[$++];)M(J)&&f.call(g,J)===-1&&g.push(J);g.sort(u)}return g};e.exports=t=function(h,m){var g,M;if(m.nodeType!==11&&h.indexOf(" ")===-1){if(h[0]==="#"&&m.rooted&&/^#[A-Z_][-A-Z0-9_]*$/i.test(h)&&m.doc._hasMultipleElementsWithId&&(g=h.substring(1),!m.doc._hasMultipleElementsWithId(g)))return M=m.doc.getElementById(g),M?[M]:[];if(h[0]==="."&&/^\.\w+$/.test(h))return m.getElementsByClassName(h.substring(1));if(/^\w+$/.test(h))return m.getElementsByTagName(h)}return W(h,m)},t.selectors=_,t.operators=x,t.combinators=T,t.matches=function(h,m){var g={sel:m};do if(g=O(g.sel),g(h))return!0;while(g.sel);return!1}}}),Ea=ne({"external/npm/node_modules/domino/lib/ChildNode.js"(t,e){var r=tt(),n=cc(),u=function(i,s){for(var l=i.createDocumentFragment(),o=0;o<s.length;o++){var c=s[o],d=c instanceof r;l.appendChild(d?c:i.createTextNode(String(c)))}return l},a={after:{value:function(){var s=Array.prototype.slice.call(arguments),l=this.parentNode,o=this.nextSibling;if(l!==null){for(;o&&s.some(function(d){return d===o});)o=o.nextSibling;var c=u(this.doc,s);l.insertBefore(c,o)}}},before:{value:function(){var s=Array.prototype.slice.call(arguments),l=this.parentNode,o=this.previousSibling;if(l!==null){for(;o&&s.some(function(f){return f===o});)o=o.previousSibling;var c=u(this.doc,s),d=o?o.nextSibling:l.firstChild;l.insertBefore(c,d)}}},remove:{value:function(){this.parentNode!==null&&(this.doc&&(this.doc._preremoveNodeIterators(this),this.rooted&&this.doc.mutateRemove(this)),this._remove(),this.parentNode=null)}},_remove:{value:function(){var s=this.parentNode;s!==null&&(s._childNodes?s._childNodes.splice(this.index,1):s._firstChild===this&&(this._nextSibling===this?s._firstChild=null:s._firstChild=this._nextSibling),n.remove(this),s.modify())}},replaceWith:{value:function(){var s=Array.prototype.slice.call(arguments),l=this.parentNode,o=this.nextSibling;if(l!==null){for(;o&&s.some(function(d){return d===o});)o=o.nextSibling;var c=u(this.doc,s);this.parentNode===l?l.replaceChild(c,this):l.insertBefore(c,o)}}}};e.exports=a}}),hc=ne({"external/npm/node_modules/domino/lib/NonDocumentTypeChildNode.js"(t,e){var r=tt(),n={nextElementSibling:{get:function(){if(this.parentNode){for(var u=this.nextSibling;u!==null;u=u.nextSibling)if(u.nodeType===r.ELEMENT_NODE)return u}return null}},previousElementSibling:{get:function(){if(this.parentNode){for(var u=this.previousSibling;u!==null;u=u.previousSibling)if(u.nodeType===r.ELEMENT_NODE)return u}return null}}};e.exports=n}}),pc=ne({"external/npm/node_modules/domino/lib/NamedNodeMap.js"(t,e){e.exports=n;var r=$e();function n(u){this.element=u}Object.defineProperties(n.prototype,{length:{get:r.shouldOverride},item:{value:r.shouldOverride},getNamedItem:{value:function(a){return this.element.getAttributeNode(a)}},getNamedItemNS:{value:function(a,i){return this.element.getAttributeNodeNS(a,i)}},setNamedItem:{value:r.nyi},setNamedItemNS:{value:r.nyi},removeNamedItem:{value:function(a){var i=this.element.getAttributeNode(a);if(i)return this.element.removeAttribute(a),i;r.NotFoundError()}},removeNamedItemNS:{value:function(a,i){var s=this.element.getAttributeNodeNS(a,i);if(s)return this.element.removeAttributeNS(a,i),s;r.NotFoundError()}}})}}),su=ne({"external/npm/node_modules/domino/lib/Element.js"(t,e){e.exports=S;var r=xa(),n=$e(),u=n.NAMESPACE,a=dc(),i=tt(),s=qr(),l=lc(),o=hf(),c=fc(),d=_a(),f=wa(),y=Ea(),v=hc(),w=pc(),A=Object.create(null);function S(h,m,g,M){f.call(this),this.nodeType=i.ELEMENT_NODE,this.ownerDocument=h,this.localName=m,this.namespaceURI=g,this.prefix=M,this._tagName=void 0,this._attrsByQName=Object.create(null),this._attrsByLName=Object.create(null),this._attrKeys=[]}function _(h,m){if(h.nodeType===i.TEXT_NODE)m.push(h._data);else for(var g=0,M=h.childNodes.length;g<M;g++)_(h.childNodes[g],m)}S.prototype=Object.create(f.prototype,{isHTML:{get:function(){return this.namespaceURI===u.HTML&&this.ownerDocument.isHTML}},tagName:{get:function(){if(this._tagName===void 0){var m;if(this.prefix===null?m=this.localName:m=this.prefix+":"+this.localName,this.isHTML){var g=A[m];g||(A[m]=g=n.toASCIIUpperCase(m)),m=g}this._tagName=m}return this._tagName}},nodeName:{get:function(){return this.tagName}},nodeValue:{get:function(){return null},set:function(){}},textContent:{get:function(){var h=[];return _(this,h),h.join("")},set:function(h){this.removeChildren(),h!=null&&h!==""&&this._appendChild(this.ownerDocument.createTextNode(h))}},innerText:{get:function(){var h=[];return _(this,h),h.join("").replace(/[ \t\n\f\r]+/g," ").trim()},set:function(h){this.removeChildren(),h!=null&&h!==""&&this._appendChild(this.ownerDocument.createTextNode(h))}},innerHTML:{get:function(){return this.serialize()},set:n.nyi},outerHTML:{get:function(){return l.serializeOne(this,{nodeType:0})},set:function(h){var m=this.ownerDocument,g=this.parentNode;if(g!==null){g.nodeType===i.DOCUMENT_NODE&&n.NoModificationAllowedError(),g.nodeType===i.DOCUMENT_FRAGMENT_NODE&&(g=g.ownerDocument.createElement("body"));var M=m.implementation.mozHTMLParser(m._address,g);M.parse(h===null?"":String(h),!0),this.replaceWith(M._asDocumentFragment())}}},_insertAdjacent:{value:function(m,g){var M=!1;switch(m){case"beforebegin":M=!0;case"afterend":var H=this.parentNode;return H===null?null:H.insertBefore(g,M?this:this.nextSibling);case"afterbegin":M=!0;case"beforeend":return this.insertBefore(g,M?this.firstChild:null);default:return n.SyntaxError()}}},insertAdjacentElement:{value:function(m,g){if(g.nodeType!==i.ELEMENT_NODE)throw new TypeError("not an element");return m=n.toASCIILowerCase(String(m)),this._insertAdjacent(m,g)}},insertAdjacentText:{value:function(m,g){var M=this.ownerDocument.createTextNode(g);m=n.toASCIILowerCase(String(m)),this._insertAdjacent(m,M)}},insertAdjacentHTML:{value:function(m,g){m=n.toASCIILowerCase(String(m)),g=String(g);var M;switch(m){case"beforebegin":case"afterend":M=this.parentNode,(M===null||M.nodeType===i.DOCUMENT_NODE)&&n.NoModificationAllowedError();break;case"afterbegin":case"beforeend":M=this;break;default:n.SyntaxError()}(!(M instanceof S)||M.ownerDocument.isHTML&&M.localName==="html"&&M.namespaceURI===u.HTML)&&(M=M.ownerDocument.createElementNS(u.HTML,"body"));var H=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,M);H.parse(g,!0),this._insertAdjacent(m,H._asDocumentFragment())}},children:{get:function(){return this._children||(this._children=new O(this)),this._children}},attributes:{get:function(){return this._attributes||(this._attributes=new T(this)),this._attributes}},firstElementChild:{get:function(){for(var h=this.firstChild;h!==null;h=h.nextSibling)if(h.nodeType===i.ELEMENT_NODE)return h;return null}},lastElementChild:{get:function(){for(var h=this.lastChild;h!==null;h=h.previousSibling)if(h.nodeType===i.ELEMENT_NODE)return h;return null}},childElementCount:{get:function(){return this.children.length}},nextElement:{value:function(h){h||(h=this.ownerDocument.documentElement);var m=this.firstElementChild;if(!m){if(this===h)return null;m=this.nextElementSibling}if(m)return m;for(var g=this.parentElement;g&&g!==h;g=g.parentElement)if(m=g.nextElementSibling,m)return m;return null}},getElementsByTagName:{value:function(m){var g;return m?(m==="*"?g=function(){return!0}:this.isHTML?g=F(m):g=B(m),new o(this,g)):new s}},getElementsByTagNameNS:{value:function(m,g){var M;return m==="*"&&g==="*"?M=function(){return!0}:m==="*"?M=B(g):g==="*"?M=q(m):M=C(m,g),new o(this,M)}},getElementsByClassName:{value:function(m){if(m=String(m).trim(),m===""){var g=new s;return g}return m=m.split(/[ \t\r\n\f]+/),new o(this,P(m))}},getElementsByName:{value:function(m){return new o(this,W(String(m)))}},clone:{value:function(){var m;this.namespaceURI!==u.HTML||this.prefix||!this.ownerDocument.isHTML?m=this.ownerDocument.createElementNS(this.namespaceURI,this.prefix!==null?this.prefix+":"+this.localName:this.localName):m=this.ownerDocument.createElement(this.localName);for(var g=0,M=this._attrKeys.length;g<M;g++){var H=this._attrKeys[g],$=this._attrsByLName[H],J=$.cloneNode();J._setOwnerElement(m),m._attrsByLName[H]=J,m._addQName(J)}return m._attrKeys=this._attrKeys.concat(),m}},isEqual:{value:function(m){if(this.localName!==m.localName||this.namespaceURI!==m.namespaceURI||this.prefix!==m.prefix||this._numattrs!==m._numattrs)return!1;for(var g=0,M=this._numattrs;g<M;g++){var H=this._attr(g);if(!m.hasAttributeNS(H.namespaceURI,H.localName)||m.getAttributeNS(H.namespaceURI,H.localName)!==H.value)return!1}return!0}},_lookupNamespacePrefix:{value:function(m,g){if(this.namespaceURI&&this.namespaceURI===m&&this.prefix!==null&&g.lookupNamespaceURI(this.prefix)===m)return this.prefix;for(var M=0,H=this._numattrs;M<H;M++){var $=this._attr(M);if($.prefix==="xmlns"&&$.value===m&&g.lookupNamespaceURI($.localName)===m)return $.localName}var J=this.parentElement;return J?J._lookupNamespacePrefix(m,g):null}},lookupNamespaceURI:{value:function(m){if((m===""||m===void 0)&&(m=null),this.namespaceURI!==null&&this.prefix===m)return this.namespaceURI;for(var g=0,M=this._numattrs;g<M;g++){var H=this._attr(g);if(H.namespaceURI===u.XMLNS&&(H.prefix==="xmlns"&&H.localName===m||m===null&&H.prefix===null&&H.localName==="xmlns"))return H.value||null}var $=this.parentElement;return $?$.lookupNamespaceURI(m):null}},getAttribute:{value:function(m){var g=this.getAttributeNode(m);return g?g.value:null}},getAttributeNS:{value:function(m,g){var M=this.getAttributeNodeNS(m,g);return M?M.value:null}},getAttributeNode:{value:function(m){m=String(m),/[A-Z]/.test(m)&&this.isHTML&&(m=n.toASCIILowerCase(m));var g=this._attrsByQName[m];return g?(Array.isArray(g)&&(g=g[0]),g):null}},getAttributeNodeNS:{value:function(m,g){m=m==null?"":String(m),g=String(g);var M=this._attrsByLName[m+"|"+g];return M||null}},hasAttribute:{value:function(m){return m=String(m),/[A-Z]/.test(m)&&this.isHTML&&(m=n.toASCIILowerCase(m)),this._attrsByQName[m]!==void 0}},hasAttributeNS:{value:function(m,g){m=m==null?"":String(m),g=String(g);var M=m+"|"+g;return this._attrsByLName[M]!==void 0}},hasAttributes:{value:function(){return this._numattrs>0}},toggleAttribute:{value:function(m,g){m=String(m),r.isValidName(m)||n.InvalidCharacterError(),/[A-Z]/.test(m)&&this.isHTML&&(m=n.toASCIILowerCase(m));var M=this._attrsByQName[m];return M===void 0?g===void 0||g===!0?(this._setAttribute(m,""),!0):!1:g===void 0||g===!1?(this.removeAttribute(m),!1):!0}},_setAttribute:{value:function(m,g){var M=this._attrsByQName[m],H;M?Array.isArray(M)&&(M=M[0]):(M=this._newattr(m),H=!0),M.value=g,this._attributes&&(this._attributes[m]=M),H&&this._newattrhook&&this._newattrhook(m,g)}},setAttribute:{value:function(m,g){m=String(m),r.isValidName(m)||n.InvalidCharacterError(),/[A-Z]/.test(m)&&this.isHTML&&(m=n.toASCIILowerCase(m)),this._setAttribute(m,String(g))}},_setAttributeNS:{value:function(m,g,M){var H=g.indexOf(":"),$,J;H<0?($=null,J=g):($=g.substring(0,H),J=g.substring(H+1)),(m===""||m===void 0)&&(m=null);var de=(m===null?"":m)+"|"+J,pe=this._attrsByLName[de],k;pe||(pe=new x(this,J,$,m),k=!0,this._attrsByLName[de]=pe,this._attributes&&(this._attributes[this._attrKeys.length]=pe),this._attrKeys.push(de),this._addQName(pe)),pe.value=M,k&&this._newattrhook&&this._newattrhook(g,M)}},setAttributeNS:{value:function(m,g,M){m=m==null||m===""?null:String(m),g=String(g),r.isValidQName(g)||n.InvalidCharacterError();var H=g.indexOf(":"),$=H<0?null:g.substring(0,H);($!==null&&m===null||$==="xml"&&m!==u.XML||(g==="xmlns"||$==="xmlns")&&m!==u.XMLNS||m===u.XMLNS&&!(g==="xmlns"||$==="xmlns"))&&n.NamespaceError(),this._setAttributeNS(m,g,String(M))}},setAttributeNode:{value:function(m){m.ownerElement!==null&&m.ownerElement!==this&&n.InUseAttributeError();var g=null,M=this._attrsByQName[m.name];if(M){if(Array.isArray(M)||(M=[M]),M.some(function(H){return H===m}))return m;m.ownerElement!==null&&n.InUseAttributeError(),M.forEach(function(H){this.removeAttributeNode(H)},this),g=M[0]}return this.setAttributeNodeNS(m),g}},setAttributeNodeNS:{value:function(m){m.ownerElement!==null&&n.InUseAttributeError();var g=m.namespaceURI,M=(g===null?"":g)+"|"+m.localName,H=this._attrsByLName[M];return H&&this.removeAttributeNode(H),m._setOwnerElement(this),this._attrsByLName[M]=m,this._attributes&&(this._attributes[this._attrKeys.length]=m),this._attrKeys.push(M),this._addQName(m),this._newattrhook&&this._newattrhook(m.name,m.value),H||null}},removeAttribute:{value:function(m){m=String(m),/[A-Z]/.test(m)&&this.isHTML&&(m=n.toASCIILowerCase(m));var g=this._attrsByQName[m];if(g){Array.isArray(g)?g.length>2?g=g.shift():(this._attrsByQName[m]=g[1],g=g[0]):this._attrsByQName[m]=void 0;var M=g.namespaceURI,H=(M===null?"":M)+"|"+g.localName;this._attrsByLName[H]=void 0;var $=this._attrKeys.indexOf(H);this._attributes&&(Array.prototype.splice.call(this._attributes,$,1),this._attributes[m]=void 0),this._attrKeys.splice($,1);var J=g.onchange;g._setOwnerElement(null),J&&J.call(g,this,g.localName,g.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(g)}}},removeAttributeNS:{value:function(m,g){m=m==null?"":String(m),g=String(g);var M=m+"|"+g,H=this._attrsByLName[M];if(H){this._attrsByLName[M]=void 0;var $=this._attrKeys.indexOf(M);this._attributes&&Array.prototype.splice.call(this._attributes,$,1),this._attrKeys.splice($,1),this._removeQName(H);var J=H.onchange;H._setOwnerElement(null),J&&J.call(H,this,H.localName,H.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(H)}}},removeAttributeNode:{value:function(m){var g=m.namespaceURI,M=(g===null?"":g)+"|"+m.localName;return this._attrsByLName[M]!==m&&n.NotFoundError(),this.removeAttributeNS(g,m.localName),m}},getAttributeNames:{value:function(){var m=this;return this._attrKeys.map(function(g){return m._attrsByLName[g].name})}},_getattr:{value:function(m){var g=this._attrsByQName[m];return g?g.value:null}},_setattr:{value:function(m,g){var M=this._attrsByQName[m],H;M||(M=this._newattr(m),H=!0),M.value=String(g),this._attributes&&(this._attributes[m]=M),H&&this._newattrhook&&this._newattrhook(m,g)}},_newattr:{value:function(m){var g=new x(this,m,null,null),M="|"+m;return this._attrsByQName[m]=g,this._attrsByLName[M]=g,this._attributes&&(this._attributes[this._attrKeys.length]=g),this._attrKeys.push(M),g}},_addQName:{value:function(h){var m=h.name,g=this._attrsByQName[m];g?Array.isArray(g)?g.push(h):this._attrsByQName[m]=[g,h]:this._attrsByQName[m]=h,this._attributes&&(this._attributes[m]=h)}},_removeQName:{value:function(h){var m=h.name,g=this._attrsByQName[m];if(Array.isArray(g)){var M=g.indexOf(h);n.assert(M!==-1),g.length===2?(this._attrsByQName[m]=g[1-M],this._attributes&&(this._attributes[m]=this._attrsByQName[m])):(g.splice(M,1),this._attributes&&this._attributes[m]===h&&(this._attributes[m]=g[0]))}else n.assert(g===h),this._attrsByQName[m]=void 0,this._attributes&&(this._attributes[m]=void 0)}},_numattrs:{get:function(){return this._attrKeys.length}},_attr:{value:function(h){return this._attrsByLName[this._attrKeys[h]]}},id:a.property({name:"id"}),className:a.property({name:"class"}),classList:{get:function(){var h=this;if(this._classList)return this._classList;var m=new c(function(){return h.className||""},function(g){h.className=g});return this._classList=m,m},set:function(h){this.className=h}},matches:{value:function(h){return d.matches(this,h)}},closest:{value:function(h){var m=this;do{if(m.matches&&m.matches(h))return m;m=m.parentElement||m.parentNode}while(m!==null&&m.nodeType===i.ELEMENT_NODE);return null}},querySelector:{value:function(h){return d(h,this)[0]}},querySelectorAll:{value:function(h){var m=d(h,this);return m.item?m:new s(m)}}}),Object.defineProperties(S.prototype,y),Object.defineProperties(S.prototype,v),a.registerChangeHandler(S,"id",function(h,m,g,M){h.rooted&&(g&&h.ownerDocument.delId(g,h),M&&h.ownerDocument.addId(M,h))}),a.registerChangeHandler(S,"class",function(h,m,g,M){h._classList&&h._classList._update()});function x(h,m,g,M,H){this.localName=m,this.prefix=g===null||g===""?null:""+g,this.namespaceURI=M===null||M===""?null:""+M,this.data=H,this._setOwnerElement(h)}x.prototype=Object.create(Object.prototype,{ownerElement:{get:function(){return this._ownerElement}},_setOwnerElement:{value:function(m){this._ownerElement=m,this.prefix===null&&this.namespaceURI===null&&m?this.onchange=m._attributeChangeHandlers[this.localName]:this.onchange=null}},name:{get:function(){return this.prefix?this.prefix+":"+this.localName:this.localName}},specified:{get:function(){return!0}},value:{get:function(){return this.data},set:function(h){var m=this.data;h=h===void 0?"":h+"",h!==m&&(this.data=h,this.ownerElement&&(this.onchange&&this.onchange(this.ownerElement,this.localName,m,h),this.ownerElement.rooted&&this.ownerElement.ownerDocument.mutateAttr(this,m)))}},cloneNode:{value:function(m){return new x(null,this.localName,this.prefix,this.namespaceURI,this.data)}},nodeType:{get:function(){return i.ATTRIBUTE_NODE}},nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return this.value},set:function(h){this.value=h}},textContent:{get:function(){return this.value},set:function(h){h==null&&(h=""),this.value=h}},innerText:{get:function(){return this.value},set:function(h){h==null&&(h=""),this.value=h}}}),S._Attr=x;function T(h){w.call(this,h);for(var m in h._attrsByQName)this[m]=h._attrsByQName[m];for(var g=0;g<h._attrKeys.length;g++)this[g]=h._attrsByLName[h._attrKeys[g]]}T.prototype=Object.create(w.prototype,{length:{get:function(){return this.element._attrKeys.length},set:function(){}},item:{value:function(h){return h=h>>>0,h>=this.length?null:this.element._attrsByLName[this.element._attrKeys[h]]}}});var E;(E=globalThis.Symbol)!=null&&E.iterator&&(T.prototype[globalThis.Symbol.iterator]=function(){var h=0,m=this.length,g=this;return{next:function(){return h<m?{value:g.item(h++)}:{done:!0}}}});function O(h){this.element=h,this.updateCache()}O.prototype=Object.create(Object.prototype,{length:{get:function(){return this.updateCache(),this.childrenByNumber.length}},item:{value:function(m){return this.updateCache(),this.childrenByNumber[m]||null}},namedItem:{value:function(m){return this.updateCache(),this.childrenByName[m]||null}},namedItems:{get:function(){return this.updateCache(),this.childrenByName}},updateCache:{value:function(){var m=/^(a|applet|area|embed|form|frame|frameset|iframe|img|object)$/;if(this.lastModTime!==this.element.lastModTime){this.lastModTime=this.element.lastModTime;for(var g=this.childrenByNumber&&this.childrenByNumber.length||0,M=0;M<g;M++)this[M]=void 0;this.childrenByNumber=[],this.childrenByName=Object.create(null);for(var H=this.element.firstChild;H!==null;H=H.nextSibling)if(H.nodeType===i.ELEMENT_NODE){this[this.childrenByNumber.length]=H,this.childrenByNumber.push(H);var $=H.getAttribute("id");$&&!this.childrenByName[$]&&(this.childrenByName[$]=H);var J=H.getAttribute("name");J&&this.element.namespaceURI===u.HTML&&m.test(this.element.localName)&&!this.childrenByName[J]&&(this.childrenByName[$]=H)}}}}});function B(h){return function(m){return m.localName===h}}function F(h){var m=n.toASCIILowerCase(h);return m===h?B(h):function(g){return g.isHTML?g.localName===m:g.localName===h}}function q(h){return function(m){return m.namespaceURI===h}}function C(h,m){return function(g){return g.namespaceURI===h&&g.localName===m}}function P(h){return function(m){return h.every(function(g){return m.classList.contains(g)})}}function W(h){return function(m){return m.namespaceURI!==u.HTML?!1:m.getAttribute("name")===h}}}}),mc=ne({"external/npm/node_modules/domino/lib/Leaf.js"(t,e){e.exports=s;var r=tt(),n=qr(),u=$e(),a=u.HierarchyRequestError,i=u.NotFoundError;function s(){r.call(this)}s.prototype=Object.create(r.prototype,{hasChildNodes:{value:function(){return!1}},firstChild:{value:null},lastChild:{value:null},insertBefore:{value:function(l,o){if(!l.nodeType)throw new TypeError("not a node");a()}},replaceChild:{value:function(l,o){if(!l.nodeType)throw new TypeError("not a node");a()}},removeChild:{value:function(l){if(!l.nodeType)throw new TypeError("not a node");i()}},removeChildren:{value:function(){}},childNodes:{get:function(){return this._childNodes||(this._childNodes=new n),this._childNodes}}})}}),sn=ne({"external/npm/node_modules/domino/lib/CharacterData.js"(t,e){e.exports=i;var r=mc(),n=$e(),u=Ea(),a=hc();function i(){r.call(this)}i.prototype=Object.create(r.prototype,{substringData:{value:function(l,o){if(arguments.length<2)throw new TypeError("Not enough arguments");return l=l>>>0,o=o>>>0,(l>this.data.length||l<0||o<0)&&n.IndexSizeError(),this.data.substring(l,l+o)}},appendData:{value:function(l){if(arguments.length<1)throw new TypeError("Not enough arguments");this.data+=String(l)}},insertData:{value:function(l,o){return this.replaceData(l,0,o)}},deleteData:{value:function(l,o){return this.replaceData(l,o,"")}},replaceData:{value:function(l,o,c){var d=this.data,f=d.length;l=l>>>0,o=o>>>0,c=String(c),(l>f||l<0)&&n.IndexSizeError(),l+o>f&&(o=f-l);var y=d.substring(0,l),v=d.substring(l+o);this.data=y+c+v}},isEqual:{value:function(l){return this._data===l._data}},length:{get:function(){return this.data.length}}}),Object.defineProperties(i.prototype,u),Object.defineProperties(i.prototype,a)}}),bc=ne({"external/npm/node_modules/domino/lib/Text.js"(t,e){e.exports=a;var r=$e(),n=tt(),u=sn();function a(s,l){u.call(this),this.nodeType=n.TEXT_NODE,this.ownerDocument=s,this._data=l,this._index=void 0}var i={get:function(){return this._data},set:function(s){s==null?s="":s=String(s),s!==this._data&&(this._data=s,this.rooted&&this.ownerDocument.mutateValue(this),this.parentNode&&this.parentNode._textchangehook&&this.parentNode._textchangehook(this))}};a.prototype=Object.create(u.prototype,{nodeName:{value:"#text"},nodeValue:i,textContent:i,innerText:i,data:{get:i.get,set:function(s){i.set.call(this,s===null?"":String(s))}},splitText:{value:function(l){(l>this._data.length||l<0)&&r.IndexSizeError();var o=this._data.substring(l),c=this.ownerDocument.createTextNode(o);this.data=this.data.substring(0,l);var d=this.parentNode;return d!==null&&d.insertBefore(c,this.nextSibling),c}},wholeText:{get:function(){for(var l=this.textContent,o=this.nextSibling;o&&o.nodeType===n.TEXT_NODE;o=o.nextSibling)l+=o.textContent;return l}},replaceWholeText:{value:r.nyi},clone:{value:function(){return new a(this.ownerDocument,this._data)}}})}}),gc=ne({"external/npm/node_modules/domino/lib/Comment.js"(t,e){e.exports=u;var r=tt(),n=sn();function u(i,s){n.call(this),this.nodeType=r.COMMENT_NODE,this.ownerDocument=i,this._data=s}var a={get:function(){return this._data},set:function(i){i==null?i="":i=String(i),this._data=i,this.rooted&&this.ownerDocument.mutateValue(this)}};u.prototype=Object.create(n.prototype,{nodeName:{value:"#comment"},nodeValue:a,textContent:a,innerText:a,data:{get:a.get,set:function(i){a.set.call(this,i===null?"":String(i))}},clone:{value:function(){return new u(this.ownerDocument,this._data)}}})}}),vc=ne({"external/npm/node_modules/domino/lib/DocumentFragment.js"(t,e){e.exports=l;var r=tt(),n=qr(),u=wa(),a=su(),i=_a(),s=$e();function l(o){u.call(this),this.nodeType=r.DOCUMENT_FRAGMENT_NODE,this.ownerDocument=o}l.prototype=Object.create(u.prototype,{nodeName:{value:"#document-fragment"},nodeValue:{get:function(){return null},set:function(){}},textContent:Object.getOwnPropertyDescriptor(a.prototype,"textContent"),innerText:Object.getOwnPropertyDescriptor(a.prototype,"innerText"),querySelector:{value:function(o){var c=this.querySelectorAll(o);return c.length?c[0]:null}},querySelectorAll:{value:function(o){var c=Object.create(this);c.isHTML=!0,c.getElementsByTagName=a.prototype.getElementsByTagName,c.nextElement=Object.getOwnPropertyDescriptor(a.prototype,"firstElementChild").get;var d=i(o,c);return d.item?d:new n(d)}},clone:{value:function(){return new l(this.ownerDocument)}},isEqual:{value:function(c){return!0}},innerHTML:{get:function(){return this.serialize()},set:s.nyi},outerHTML:{get:function(){return this.serialize()},set:s.nyi}})}}),yc=ne({"external/npm/node_modules/domino/lib/ProcessingInstruction.js"(t,e){e.exports=u;var r=tt(),n=sn();function u(i,s,l){n.call(this),this.nodeType=r.PROCESSING_INSTRUCTION_NODE,this.ownerDocument=i,this.target=s,this._data=l}var a={get:function(){return this._data},set:function(i){i==null?i="":i=String(i),this._data=i,this.rooted&&this.ownerDocument.mutateValue(this)}};u.prototype=Object.create(n.prototype,{nodeName:{get:function(){return this.target}},nodeValue:a,textContent:a,innerText:a,data:{get:a.get,set:function(i){a.set.call(this,i===null?"":String(i))}},clone:{value:function(){return new u(this.ownerDocument,this.target,this._data)}},isEqual:{value:function(s){return this.target===s.target&&this._data===s._data}}})}}),on=ne({"external/npm/node_modules/domino/lib/NodeFilter.js"(t,e){var r={FILTER_ACCEPT:1,FILTER_REJECT:2,FILTER_SKIP:3,SHOW_ALL:4294967295,SHOW_ELEMENT:1,SHOW_ATTRIBUTE:2,SHOW_TEXT:4,SHOW_CDATA_SECTION:8,SHOW_ENTITY_REFERENCE:16,SHOW_ENTITY:32,SHOW_PROCESSING_INSTRUCTION:64,SHOW_COMMENT:128,SHOW_DOCUMENT:256,SHOW_DOCUMENT_TYPE:512,SHOW_DOCUMENT_FRAGMENT:1024,SHOW_NOTATION:2048};e.exports=r.constructor=r.prototype=r}}),wc=ne({"external/npm/node_modules/domino/lib/NodeTraversal.js"(t,e){e.exports={nextSkippingChildren:r,nextAncestorSibling:n,next:u,previous:i,deepLastChild:a};function r(s,l){return s===l?null:s.nextSibling!==null?s.nextSibling:n(s,l)}function n(s,l){for(s=s.parentNode;s!==null;s=s.parentNode){if(s===l)return null;if(s.nextSibling!==null)return s.nextSibling}return null}function u(s,l){var o;return o=s.firstChild,o!==null?o:s===l?null:(o=s.nextSibling,o!==null?o:n(s,l))}function a(s){for(;s.lastChild;)s=s.lastChild;return s}function i(s,l){var o;return o=s.previousSibling,o!==null?a(o):(o=s.parentNode,o===l?null:o)}}}),pf=ne({"external/npm/node_modules/domino/lib/TreeWalker.js"(t,e){e.exports=c;var r=tt(),n=on(),u=wc(),a=$e(),i={first:"firstChild",last:"lastChild",next:"firstChild",previous:"lastChild"},s={first:"nextSibling",last:"previousSibling",next:"nextSibling",previous:"previousSibling"};function l(d,f){var y,v,w,A,S;for(v=d._currentNode[i[f]];v!==null;){if(A=d._internalFilter(v),A===n.FILTER_ACCEPT)return d._currentNode=v,v;if(A===n.FILTER_SKIP&&(y=v[i[f]],y!==null)){v=y;continue}for(;v!==null;){if(S=v[s[f]],S!==null){v=S;break}if(w=v.parentNode,w===null||w===d.root||w===d._currentNode)return null;v=w}}return null}function o(d,f){var y,v,w;if(y=d._currentNode,y===d.root)return null;for(;;){for(w=y[s[f]];w!==null;){if(y=w,v=d._internalFilter(y),v===n.FILTER_ACCEPT)return d._currentNode=y,y;w=y[i[f]],(v===n.FILTER_REJECT||w===null)&&(w=y[s[f]])}if(y=y.parentNode,y===null||y===d.root||d._internalFilter(y)===n.FILTER_ACCEPT)return null}}function c(d,f,y){(!d||!d.nodeType)&&a.NotSupportedError(),this._root=d,this._whatToShow=Number(f)||0,this._filter=y||null,this._active=!1,this._currentNode=d}Object.defineProperties(c.prototype,{root:{get:function(){return this._root}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},currentNode:{get:function(){return this._currentNode},set:function(f){if(!(f instanceof r))throw new TypeError("Not a Node");this._currentNode=f}},_internalFilter:{value:function(f){var y,v;if(this._active&&a.InvalidStateError(),!(1<<f.nodeType-1&this._whatToShow))return n.FILTER_SKIP;if(v=this._filter,v===null)y=n.FILTER_ACCEPT;else{this._active=!0;try{typeof v=="function"?y=v(f):y=v.acceptNode(f)}finally{this._active=!1}}return+y}},parentNode:{value:function(){for(var f=this._currentNode;f!==this.root;){if(f=f.parentNode,f===null)return null;if(this._internalFilter(f)===n.FILTER_ACCEPT)return this._currentNode=f,f}return null}},firstChild:{value:function(){return l(this,"first")}},lastChild:{value:function(){return l(this,"last")}},previousSibling:{value:function(){return o(this,"previous")}},nextSibling:{value:function(){return o(this,"next")}},previousNode:{value:function(){var f,y,v,w;for(f=this._currentNode;f!==this._root;){for(v=f.previousSibling;v;v=f.previousSibling)if(f=v,y=this._internalFilter(f),y!==n.FILTER_REJECT){for(w=f.lastChild;w&&(f=w,y=this._internalFilter(f),y!==n.FILTER_REJECT);w=f.lastChild);if(y===n.FILTER_ACCEPT)return this._currentNode=f,f}if(f===this.root||f.parentNode===null)return null;if(f=f.parentNode,this._internalFilter(f)===n.FILTER_ACCEPT)return this._currentNode=f,f}return null}},nextNode:{value:function(){var f,y,v,w;f=this._currentNode,y=n.FILTER_ACCEPT;e:for(;;){for(v=f.firstChild;v;v=f.firstChild){if(f=v,y=this._internalFilter(f),y===n.FILTER_ACCEPT)return this._currentNode=f,f;if(y===n.FILTER_REJECT)break}for(w=u.nextSkippingChildren(f,this.root);w;w=u.nextSkippingChildren(f,this.root)){if(f=w,y=this._internalFilter(f),y===n.FILTER_ACCEPT)return this._currentNode=f,f;if(y===n.FILTER_SKIP)continue e}return null}}},toString:{value:function(){return"[object TreeWalker]"}}})}}),mf=ne({"external/npm/node_modules/domino/lib/NodeIterator.js"(t,e){e.exports=l;var r=on(),n=wc(),u=$e();function a(o,c,d){return d?n.next(o,c):o===c?null:n.previous(o,null)}function i(o,c){for(;c;c=c.parentNode)if(o===c)return!0;return!1}function s(o,c){var d,f;for(d=o._referenceNode,f=o._pointerBeforeReferenceNode;;){if(f===c)f=!f;else if(d=a(d,o._root,c),d===null)return null;var y=o._internalFilter(d);if(y===r.FILTER_ACCEPT)break}return o._referenceNode=d,o._pointerBeforeReferenceNode=f,d}function l(o,c,d){(!o||!o.nodeType)&&u.NotSupportedError(),this._root=o,this._referenceNode=o,this._pointerBeforeReferenceNode=!0,this._whatToShow=Number(c)||0,this._filter=d||null,this._active=!1,o.doc._attachNodeIterator(this)}Object.defineProperties(l.prototype,{root:{get:function(){return this._root}},referenceNode:{get:function(){return this._referenceNode}},pointerBeforeReferenceNode:{get:function(){return this._pointerBeforeReferenceNode}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},_internalFilter:{value:function(c){var d,f;if(this._active&&u.InvalidStateError(),!(1<<c.nodeType-1&this._whatToShow))return r.FILTER_SKIP;if(f=this._filter,f===null)d=r.FILTER_ACCEPT;else{this._active=!0;try{typeof f=="function"?d=f(c):d=f.acceptNode(c)}finally{this._active=!1}}return+d}},_preremove:{value:function(c){if(!i(c,this._root)&&i(c,this._referenceNode)){if(this._pointerBeforeReferenceNode){for(var d=c;d.lastChild;)d=d.lastChild;if(d=n.next(d,this.root),d){this._referenceNode=d;return}this._pointerBeforeReferenceNode=!1}if(c.previousSibling===null)this._referenceNode=c.parentNode;else{this._referenceNode=c.previousSibling;var f;for(f=this._referenceNode.lastChild;f;f=this._referenceNode.lastChild)this._referenceNode=f}}}},nextNode:{value:function(){return s(this,!0)}},previousNode:{value:function(){return s(this,!1)}},detach:{value:function(){}},toString:{value:function(){return"[object NodeIterator]"}}})}}),Sa=ne({"external/npm/node_modules/domino/lib/URL.js"(t,e){e.exports=r;function r(n){if(!n)return Object.create(r.prototype);this.url=n.replace(/^[ \t\n\r\f]+|[ \t\n\r\f]+$/g,"");var u=r.pattern.exec(this.url);if(u){if(u[2]&&(this.scheme=u[2]),u[4]){var a=u[4].match(r.userinfoPattern);if(a&&(this.username=a[1],this.password=a[3],u[4]=u[4].substring(a[0].length)),u[4].match(r.portPattern)){var i=u[4].lastIndexOf(":");this.host=u[4].substring(0,i),this.port=u[4].substring(i+1)}else this.host=u[4]}u[5]&&(this.path=u[5]),u[6]&&(this.query=u[7]),u[8]&&(this.fragment=u[9])}}r.pattern=/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/,r.userinfoPattern=/^([^@:]*)(:([^@]*))?@/,r.portPattern=/:\d+$/,r.authorityPattern=/^[^:\/?#]+:\/\//,r.hierarchyPattern=/^[^:\/?#]+:\//,r.percentEncode=function(u){var a=u.charCodeAt(0);if(a<256)return"%"+a.toString(16);throw Error("can't percent-encode codepoints > 255 yet")},r.prototype={constructor:r,isAbsolute:function(){return!!this.scheme},isAuthorityBased:function(){return r.authorityPattern.test(this.url)},isHierarchical:function(){return r.hierarchyPattern.test(this.url)},toString:function(){var n="";return this.scheme!==void 0&&(n+=this.scheme+":"),this.isAbsolute()&&(n+="//",(this.username||this.password)&&(n+=this.username||"",this.password&&(n+=":"+this.password),n+="@"),this.host&&(n+=this.host)),this.port!==void 0&&(n+=":"+this.port),this.path!==void 0&&(n+=this.path),this.query!==void 0&&(n+="?"+this.query),this.fragment!==void 0&&(n+="#"+this.fragment),n},resolve:function(n){var u=this,a=new r(n),i=new r;return a.scheme!==void 0?(i.scheme=a.scheme,i.username=a.username,i.password=a.password,i.host=a.host,i.port=a.port,i.path=l(a.path),i.query=a.query):(i.scheme=u.scheme,a.host!==void 0?(i.username=a.username,i.password=a.password,i.host=a.host,i.port=a.port,i.path=l(a.path),i.query=a.query):(i.username=u.username,i.password=u.password,i.host=u.host,i.port=u.port,a.path?(a.path.charAt(0)==="/"?i.path=l(a.path):(i.path=s(u.path,a.path),i.path=l(i.path)),i.query=a.query):(i.path=u.path,a.query!==void 0?i.query=a.query:i.query=u.query))),i.fragment=a.fragment,i.toString();function s(o,c){if(u.host!==void 0&&!u.path)return"/"+c;var d=o.lastIndexOf("/");return d===-1?c:o.substring(0,d+1)+c}function l(o){if(!o)return o;for(var c="";o.length>0;){if(o==="."||o===".."){o="";break}var d=o.substring(0,2),f=o.substring(0,3),y=o.substring(0,4);if(f==="../")o=o.substring(3);else if(d==="./")o=o.substring(2);else if(f==="/./")o="/"+o.substring(3);else if(d==="/."&&o.length===2)o="/";else if(y==="/../"||f==="/.."&&o.length===3)o="/"+o.substring(4),c=c.replace(/\/?[^\/]*$/,"");else{var v=o.match(/(\/?([^\/]*))/)[0];c+=v,o=o.substring(v.length)}}return c}}}}}),bf=ne({"external/npm/node_modules/domino/lib/CustomEvent.js"(t,e){e.exports=n;var r=iu();function n(u,a){r.call(this,u,a)}n.prototype=Object.create(r.prototype,{constructor:{value:n}})}}),xc=ne({"external/npm/node_modules/domino/lib/events.js"(t,e){e.exports={Event:iu(),UIEvent:ic(),MouseEvent:sc(),CustomEvent:bf()}}}),gf=ne({"external/npm/node_modules/domino/lib/style_parser.js"(t){Object.defineProperty(t,"__esModule",{value:!0}),t.hyphenate=t.parse=void 0;function e(n){let u=[],a=0,i=0,s=0,l=0,o=0,c=null;for(;a<n.length;)switch(n.charCodeAt(a++)){case 40:i++;break;case 41:i--;break;case 39:s===0?s=39:s===39&&n.charCodeAt(a-1)!==92&&(s=0);break;case 34:s===0?s=34:s===34&&n.charCodeAt(a-1)!==92&&(s=0);break;case 58:!c&&i===0&&s===0&&(c=r(n.substring(o,a-1).trim()),l=a);break;case 59:if(c&&l>0&&i===0&&s===0){let f=n.substring(l,a-1).trim();u.push(c,f),o=a,l=0,c=null}break}if(c&&l){let d=n.slice(l).trim();u.push(c,d)}return u}t.parse=e;function r(n){return n.replace(/[a-z][A-Z]/g,u=>u.charAt(0)+"-"+u.charAt(1)).toLowerCase()}t.hyphenate=r}}),Ta=ne({"external/npm/node_modules/domino/lib/CSSStyleDeclaration.js"(t,e){var{parse:r}=gf();e.exports=function(l){let o=new u(l),c={get:function(d,f){return f in d?d[f]:d.getPropertyValue(n(f))},has:function(d,f){return!0},set:function(d,f,y){return f in d?d[f]=y:d.setProperty(n(f),y??void 0),!0}};return new Proxy(o,c)};function n(l){return l.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function u(l){this._element=l}var a="!important";function i(l){let o={property:{},priority:{}};if(!l)return o;let c=r(l);if(c.length<2)return o;for(let d=0;d<c.length;d+=2){let f=c[d],y=c[d+1];y.endsWith(a)&&(o.priority[f]="important",y=y.slice(0,-a.length).trim()),o.property[f]=y}return o}var s={};u.prototype=Object.create(Object.prototype,{_parsed:{get:function(){if(!this._parsedStyles||this.cssText!==this._lastParsedText){var l=this.cssText;this._parsedStyles=i(l),this._lastParsedText=l,delete this._names}return this._parsedStyles}},_serialize:{value:function(){var l=this._parsed,o="";for(var c in l.property)o&&(o+=" "),o+=c+": "+l.property[c],l.priority[c]&&(o+=" !"+l.priority[c]),o+=";";this.cssText=o,this._lastParsedText=o,delete this._names}},cssText:{get:function(){return this._element.getAttribute("style")},set:function(l){this._element.setAttribute("style",l)}},length:{get:function(){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names.length}},item:{value:function(l){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names[l]}},getPropertyValue:{value:function(l){return l=l.toLowerCase(),this._parsed.property[l]||""}},getPropertyPriority:{value:function(l){return l=l.toLowerCase(),this._parsed.priority[l]||""}},setProperty:{value:function(l,o,c){if(l=l.toLowerCase(),o==null&&(o=""),c==null&&(c=""),o!==s&&(o=""+o),o=o.trim(),o===""){this.removeProperty(l);return}if(!(c!==""&&c!==s&&!/^important$/i.test(c))){var d=this._parsed;if(o===s){if(!d.property[l])return;c!==""?d.priority[l]="important":delete d.priority[l]}else{if(o.includes(";")&&!o.includes("data:"))return;var f=i(l+":"+o);if(Object.getOwnPropertyNames(f.property).length===0||Object.getOwnPropertyNames(f.priority).length!==0)return;for(var y in f.property)d.property[y]=f.property[y],c!==s&&(c!==""?d.priority[y]="important":d.priority[y]&&delete d.priority[y])}this._serialize()}}},setPropertyValue:{value:function(l,o){return this.setProperty(l,o,s)}},setPropertyPriority:{value:function(l,o){return this.setProperty(l,s,o)}},removeProperty:{value:function(l){l=l.toLowerCase();var o=this._parsed;l in o.property&&(delete o.property[l],delete o.priority[l],this._serialize())}}})}}),_c=ne({"external/npm/node_modules/domino/lib/URLUtils.js"(t,e){var r=Sa();e.exports=n;function n(){}n.prototype=Object.create(Object.prototype,{_url:{get:function(){return new r(this.href)}},protocol:{get:function(){var u=this._url;return u&&u.scheme?u.scheme+":":":"},set:function(u){var a=this.href,i=new r(a);i.isAbsolute()&&(u=u.replace(/:+$/,""),u=u.replace(/[^-+\.a-zA-Z0-9]/g,r.percentEncode),u.length>0&&(i.scheme=u,a=i.toString())),this.href=a}},host:{get:function(){var u=this._url;return u.isAbsolute()&&u.isAuthorityBased()?u.host+(u.port?":"+u.port:""):""},set:function(u){var a=this.href,i=new r(a);i.isAbsolute()&&i.isAuthorityBased()&&(u=u.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,r.percentEncode),u.length>0&&(i.host=u,delete i.port,a=i.toString())),this.href=a}},hostname:{get:function(){var u=this._url;return u.isAbsolute()&&u.isAuthorityBased()?u.host:""},set:function(u){var a=this.href,i=new r(a);i.isAbsolute()&&i.isAuthorityBased()&&(u=u.replace(/^\/+/,""),u=u.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,r.percentEncode),u.length>0&&(i.host=u,a=i.toString())),this.href=a}},port:{get:function(){var u=this._url;return u.isAbsolute()&&u.isAuthorityBased()&&u.port!==void 0?u.port:""},set:function(u){var a=this.href,i=new r(a);i.isAbsolute()&&i.isAuthorityBased()&&(u=""+u,u=u.replace(/[^0-9].*$/,""),u=u.replace(/^0+/,""),u.length===0&&(u="0"),parseInt(u,10)<=65535&&(i.port=u,a=i.toString())),this.href=a}},pathname:{get:function(){var u=this._url;return u.isAbsolute()&&u.isHierarchical()?u.path:""},set:function(u){var a=this.href,i=new r(a);i.isAbsolute()&&i.isHierarchical()&&(u.charAt(0)!=="/"&&(u="/"+u),u=u.replace(/[^-+\._~!$&'()*,;:=@\/a-zA-Z0-9]/g,r.percentEncode),i.path=u,a=i.toString()),this.href=a}},search:{get:function(){var u=this._url;return u.isAbsolute()&&u.isHierarchical()&&u.query!==void 0?"?"+u.query:""},set:function(u){var a=this.href,i=new r(a);i.isAbsolute()&&i.isHierarchical()&&(u.charAt(0)==="?"&&(u=u.substring(1)),u=u.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,r.percentEncode),i.query=u,a=i.toString()),this.href=a}},hash:{get:function(){var u=this._url;return u==null||u.fragment==null||u.fragment===""?"":"#"+u.fragment},set:function(u){var a=this.href,i=new r(a);u.charAt(0)==="#"&&(u=u.substring(1)),u=u.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,r.percentEncode),i.fragment=u,a=i.toString(),this.href=a}},username:{get:function(){var u=this._url;return u.username||""},set:function(u){var a=this.href,i=new r(a);i.isAbsolute()&&(u=u.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\:]/g,r.percentEncode),i.username=u,a=i.toString()),this.href=a}},password:{get:function(){var u=this._url;return u.password||""},set:function(u){var a=this.href,i=new r(a);i.isAbsolute()&&(u===""?i.password=null:(u=u.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\]/g,r.percentEncode),i.password=u),a=i.toString()),this.href=a}},origin:{get:function(){var u=this._url;if(u==null)return"";var a=function(i){var s=[u.scheme,u.host,+u.port||i];return s[0]+"://"+s[1]+(s[2]===i?"":":"+s[2])};switch(u.scheme){case"ftp":return a(21);case"gopher":return a(70);case"http":case"ws":return a(80);case"https":case"wss":return a(443);default:return u.scheme+"://"}}}}),n._inherit=function(u){Object.getOwnPropertyNames(n.prototype).forEach(function(a){if(!(a==="constructor"||a==="href")){var i=Object.getOwnPropertyDescriptor(n.prototype,a);Object.defineProperty(u,a,i)}})}}}),Ec=ne({"external/npm/node_modules/domino/lib/defineElement.js"(t,e){var r=dc(),n=ya().isApiWritable;e.exports=function(s,l,o,c){var d=s.ctor;if(d){var f=s.props||{};if(s.attributes)for(var y in s.attributes){var v=s.attributes[y];(typeof v!="object"||Array.isArray(v))&&(v={type:v}),v.name||(v.name=y.toLowerCase()),f[y]=r.property(v)}f.constructor={value:d,writable:n},d.prototype=Object.create((s.superclass||l).prototype,f),s.events&&i(d,s.events),o[s.name]=d}else d=l;return(s.tags||s.tag&&[s.tag]||[]).forEach(function(w){c[w]=d}),d};function u(s,l,o,c){this.body=s,this.document=l,this.form=o,this.element=c}u.prototype.build=function(){return()=>{}};function a(s,l,o,c){var d=s.ownerDocument||Object.create(null),f=s.form||Object.create(null);s[l]=new u(c,d,f,s).build()}function i(s,l){var o=s.prototype;l.forEach(function(c){Object.defineProperty(o,"on"+c,{get:function(){return this._getEventHandler(c)},set:function(d){this._setEventHandler(c,d)}}),r.registerChangeHandler(s,"on"+c,a)})}}}),Na=ne({"external/npm/node_modules/domino/lib/htmlelts.js"(t){var e=tt(),r=su(),n=Ta(),u=$e(),a=_c(),i=Ec(),s=t.elements={},l=Object.create(null);t.createElement=function(_,x,T){var E=l[x]||A;return new E(_,x,T)};function o(_){return i(_,w,s,l)}function c(_){return{get:function(){var x=this._getattr(_);if(x===null)return"";var T=this.doc._resolve(x);return T===null?x:T},set:function(x){this._setattr(_,x)}}}function d(_){return{get:function(){var x=this._getattr(_);return x===null?null:x.toLowerCase()==="use-credentials"?"use-credentials":"anonymous"},set:function(x){x==null?this.removeAttribute(_):this._setattr(_,x)}}}var f={type:["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"],missing:""},y={A:!0,LINK:!0,BUTTON:!0,INPUT:!0,SELECT:!0,TEXTAREA:!0,COMMAND:!0},v=function(_,x,T){w.call(this,_,x,T),this._form=null},w=t.HTMLElement=o({superclass:r,name:"HTMLElement",ctor:function(x,T,E){r.call(this,x,T,u.NAMESPACE.HTML,E)},props:{dangerouslySetInnerHTML:{set:function(_){this._innerHTML=_}},innerHTML:{get:function(){return this.serialize()},set:function(_){var x=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,this);x.parse(_===null?"":String(_),!0);for(var T=this instanceof l.template?this.content:this;T.hasChildNodes();)T.removeChild(T.firstChild);T.appendChild(x._asDocumentFragment())}},style:{get:function(){return this._style||(this._style=new n(this)),this._style},set:function(_){_==null&&(_=""),this._setattr("style",String(_))}},blur:{value:function(){}},focus:{value:function(){}},forceSpellCheck:{value:function(){}},click:{value:function(){if(!this._click_in_progress){this._click_in_progress=!0;try{this._pre_click_activation_steps&&this._pre_click_activation_steps();var _=this.ownerDocument.createEvent("MouseEvent");_.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,0,0,0,0,!1,!1,!1,!1,0,null);var x=this.dispatchEvent(_);x?this._post_click_activation_steps&&this._post_click_activation_steps(_):this._cancelled_activation_steps&&this._cancelled_activation_steps()}finally{this._click_in_progress=!1}}}},submit:{value:u.nyi}},attributes:{title:String,lang:String,dir:{type:["ltr","rtl","auto"],missing:""},draggable:{type:["true","false"],treatNullAsEmptyString:!0},spellcheck:{type:["true","false"],missing:""},enterKeyHint:{type:["enter","done","go","next","previous","search","send"],missing:""},autoCapitalize:{type:["off","on","none","sentences","words","characters"],missing:""},autoFocus:Boolean,accessKey:String,nonce:String,hidden:Boolean,translate:{type:["no","yes"],missing:""},tabIndex:{type:"long",default:function(){return this.tagName in y||this.contentEditable?0:-1}}},events:["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"]}),A=o({name:"HTMLUnknownElement",ctor:function(x,T,E){w.call(this,x,T,E)}}),S={form:{get:function(){return this._form}}};o({tag:"a",name:"HTMLAnchorElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{_post_click_activation_steps:{value:function(_){this.href&&(this.ownerDocument.defaultView.location=this.href)}}},attributes:{href:c,ping:String,download:String,target:String,rel:String,media:String,hreflang:String,type:String,referrerPolicy:f,coords:String,charset:String,name:String,rev:String,shape:String}}),a._inherit(l.a.prototype),o({tag:"area",name:"HTMLAreaElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{alt:String,target:String,download:String,rel:String,media:String,href:c,hreflang:String,type:String,shape:String,coords:String,ping:String,referrerPolicy:f,noHref:Boolean}}),a._inherit(l.area.prototype),o({tag:"br",name:"HTMLBRElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{clear:String}}),o({tag:"base",name:"HTMLBaseElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{target:String}}),o({tag:"body",name:"HTMLBodyElement",ctor:function(x,T,E){w.call(this,x,T,E)},events:["afterprint","beforeprint","beforeunload","blur","error","focus","hashchange","load","message","offline","online","pagehide","pageshow","popstate","resize","scroll","storage","unload"],attributes:{text:{type:String,treatNullAsEmptyString:!0},link:{type:String,treatNullAsEmptyString:!0},vLink:{type:String,treatNullAsEmptyString:!0},aLink:{type:String,treatNullAsEmptyString:!0},bgColor:{type:String,treatNullAsEmptyString:!0},background:String}}),o({tag:"button",name:"HTMLButtonElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:S,attributes:{name:String,value:String,disabled:Boolean,autofocus:Boolean,type:{type:["submit","reset","button","menu"],missing:"submit"},formTarget:String,formAction:c,formNoValidate:Boolean,formMethod:{type:["get","post","dialog"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""}}}),o({tag:"dl",name:"HTMLDListElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{compact:Boolean}}),o({tag:"data",name:"HTMLDataElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{value:String}}),o({tag:"datalist",name:"HTMLDataListElement",ctor:function(x,T,E){w.call(this,x,T,E)}}),o({tag:"details",name:"HTMLDetailsElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{open:Boolean}}),o({tag:"div",name:"HTMLDivElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{align:String}}),o({tag:"embed",name:"HTMLEmbedElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{src:c,type:String,width:String,height:String,align:String,name:String}}),o({tag:"fieldset",name:"HTMLFieldSetElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:S,attributes:{disabled:Boolean,name:String}}),o({tag:"form",name:"HTMLFormElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{action:String,autocomplete:{type:["on","off"],missing:"on"},name:String,acceptCharset:{name:"accept-charset"},target:String,noValidate:Boolean,method:{type:["get","post","dialog"],invalid:"get",missing:"get"},enctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"},encoding:{name:"enctype",type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"}}}),o({tag:"hr",name:"HTMLHRElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{align:String,color:String,noShade:Boolean,size:String,width:String}}),o({tag:"head",name:"HTMLHeadElement",ctor:function(x,T,E){w.call(this,x,T,E)}}),o({tags:["h1","h2","h3","h4","h5","h6"],name:"HTMLHeadingElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{align:String}}),o({tag:"html",name:"HTMLHtmlElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{xmlns:c,version:String}}),o({tag:"iframe",name:"HTMLIFrameElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{src:c,srcdoc:String,name:String,width:String,height:String,seamless:Boolean,allow:Boolean,allowFullscreen:Boolean,allowUserMedia:Boolean,allowPaymentRequest:Boolean,referrerPolicy:f,loading:{type:["eager","lazy"],treatNullAsEmptyString:!0},align:String,scrolling:String,frameBorder:String,longDesc:c,marginHeight:{type:String,treatNullAsEmptyString:!0},marginWidth:{type:String,treatNullAsEmptyString:!0}}}),o({tag:"img",name:"HTMLImageElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{alt:String,src:c,srcset:String,crossOrigin:d,useMap:String,isMap:Boolean,sizes:String,height:{type:"unsigned long",default:0},width:{type:"unsigned long",default:0},referrerPolicy:f,loading:{type:["eager","lazy"],missing:""},name:String,lowsrc:c,align:String,hspace:{type:"unsigned long",default:0},vspace:{type:"unsigned long",default:0},longDesc:c,border:{type:String,treatNullAsEmptyString:!0}}}),o({tag:"input",name:"HTMLInputElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:{form:S.form,_post_click_activation_steps:{value:function(_){if(this.type==="checkbox")this.checked=!this.checked;else if(this.type==="radio")for(var x=this.form.getElementsByName(this.name),T=x.length-1;T>=0;T--){var E=x[T];E.checked=E===this}}}},attributes:{name:String,disabled:Boolean,autofocus:Boolean,accept:String,alt:String,max:String,min:String,pattern:String,placeholder:String,step:String,dirName:String,defaultValue:{name:"value"},multiple:Boolean,required:Boolean,readOnly:Boolean,checked:Boolean,value:String,src:c,defaultChecked:{name:"checked",type:Boolean},size:{type:"unsigned long",default:20,min:1,setmin:1},width:{type:"unsigned long",min:0,setmin:0,default:0},height:{type:"unsigned long",min:0,setmin:0,default:0},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},autocomplete:String,type:{type:["text","hidden","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"],missing:"text"},formTarget:String,formNoValidate:Boolean,formMethod:{type:["get","post"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""},align:String,useMap:String}}),o({tag:"keygen",name:"HTMLKeygenElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:S,attributes:{name:String,disabled:Boolean,autofocus:Boolean,challenge:String,keytype:{type:["rsa"],missing:""}}}),o({tag:"li",name:"HTMLLIElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{value:{type:"long",default:0},type:String}}),o({tag:"label",name:"HTMLLabelElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:S,attributes:{htmlFor:{name:"for",type:String}}}),o({tag:"legend",name:"HTMLLegendElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{align:String}}),o({tag:"link",name:"HTMLLinkElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{href:c,rel:String,media:String,hreflang:String,type:String,crossOrigin:d,nonce:String,integrity:String,referrerPolicy:f,imageSizes:String,imageSrcset:String,charset:String,rev:String,target:String}}),o({tag:"map",name:"HTMLMapElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{name:String}}),o({tag:"menu",name:"HTMLMenuElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{type:{type:["context","popup","toolbar"],missing:"toolbar"},label:String,compact:Boolean}}),o({tag:"meta",name:"HTMLMetaElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{name:String,content:String,httpEquiv:{name:"http-equiv",type:String},scheme:String}}),o({tag:"meter",name:"HTMLMeterElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:S}),o({tags:["ins","del"],name:"HTMLModElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{cite:c,dateTime:String}}),o({tag:"ol",name:"HTMLOListElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{_numitems:{get:function(){var _=0;return this.childNodes.forEach(function(x){x.nodeType===e.ELEMENT_NODE&&x.tagName==="LI"&&_++}),_}}},attributes:{type:String,reversed:Boolean,start:{type:"long",default:function(){return this.reversed?this._numitems:1}},compact:Boolean}}),o({tag:"object",name:"HTMLObjectElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:S,attributes:{data:c,type:String,name:String,useMap:String,typeMustMatch:Boolean,width:String,height:String,align:String,archive:String,code:String,declare:Boolean,hspace:{type:"unsigned long",default:0},standby:String,vspace:{type:"unsigned long",default:0},codeBase:c,codeType:String,border:{type:String,treatNullAsEmptyString:!0}}}),o({tag:"optgroup",name:"HTMLOptGroupElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{disabled:Boolean,label:String}}),o({tag:"option",name:"HTMLOptionElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{form:{get:function(){for(var _=this.parentNode;_&&_.nodeType===e.ELEMENT_NODE;){if(_.localName==="select")return _.form;_=_.parentNode}}},value:{get:function(){return this._getattr("value")||this.text},set:function(_){this._setattr("value",_)}},text:{get:function(){return this.textContent.replace(/[ \t\n\f\r]+/g," ").trim()},set:function(_){this.textContent=_}}},attributes:{disabled:Boolean,defaultSelected:{name:"selected",type:Boolean},label:String}}),o({tag:"output",name:"HTMLOutputElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:S,attributes:{name:String}}),o({tag:"p",name:"HTMLParagraphElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{align:String}}),o({tag:"param",name:"HTMLParamElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{name:String,value:String,type:String,valueType:String}}),o({tags:["pre","listing","xmp"],name:"HTMLPreElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{width:{type:"long",default:0}}}),o({tag:"progress",name:"HTMLProgressElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:S,attributes:{max:{type:Number,float:!0,default:1,min:0}}}),o({tags:["q","blockquote"],name:"HTMLQuoteElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{cite:c}}),o({tag:"script",name:"HTMLScriptElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{text:{get:function(){for(var _="",x=0,T=this.childNodes.length;x<T;x++){var E=this.childNodes[x];E.nodeType===e.TEXT_NODE&&(_+=E._data)}return _},set:function(_){this.removeChildren(),_!==null&&_!==""&&this.appendChild(this.ownerDocument.createTextNode(_))}}},attributes:{src:c,type:String,charset:String,referrerPolicy:f,defer:Boolean,async:Boolean,nomodule:Boolean,crossOrigin:d,nonce:String,integrity:String}}),o({tag:"select",name:"HTMLSelectElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:{form:S.form,options:{get:function(){return this.getElementsByTagName("option")}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,multiple:Boolean,required:Boolean,size:{type:"unsigned long",default:0}}}),o({tag:"span",name:"HTMLSpanElement",ctor:function(x,T,E){w.call(this,x,T,E)}}),o({tag:"style",name:"HTMLStyleElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{media:String,type:String,scoped:Boolean}}),o({tag:"caption",name:"HTMLTableCaptionElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{align:String}}),o({name:"HTMLTableCellElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{colSpan:{type:"unsigned long",default:1},rowSpan:{type:"unsigned long",default:1},scope:{type:["row","col","rowgroup","colgroup"],missing:""},abbr:String,align:String,axis:String,height:String,width:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},noWrap:Boolean,vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),o({tags:["col","colgroup"],name:"HTMLTableColElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{span:{type:"limited unsigned long with fallback",default:1,min:1},align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,width:String}}),o({tag:"table",name:"HTMLTableElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,border:String,frame:String,rules:String,summary:String,width:String,bgColor:{type:String,treatNullAsEmptyString:!0},cellPadding:{type:String,treatNullAsEmptyString:!0},cellSpacing:{type:String,treatNullAsEmptyString:!0}}}),o({tag:"template",name:"HTMLTemplateElement",ctor:function(x,T,E){w.call(this,x,T,E),this._contentFragment=x._templateDoc.createDocumentFragment()},props:{content:{get:function(){return this._contentFragment}},serialize:{value:function(){return this.content.serialize()}}}}),o({tag:"tr",name:"HTMLTableRowElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{cells:{get:function(){return this.querySelectorAll("td,th")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),o({tags:["thead","tfoot","tbody"],name:"HTMLTableSectionElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String}}),o({tag:"textarea",name:"HTMLTextAreaElement",ctor:function(x,T,E){v.call(this,x,T,E)},props:{form:S.form,type:{get:function(){return"textarea"}},defaultValue:{get:function(){return this.textContent},set:function(_){this.textContent=_}},value:{get:function(){return this.defaultValue},set:function(_){this.defaultValue=_}},textLength:{get:function(){return this.value.length}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,placeholder:String,wrap:String,dirName:String,required:Boolean,readOnly:Boolean,rows:{type:"limited unsigned long with fallback",default:2},cols:{type:"limited unsigned long with fallback",default:20},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""}}}),o({tag:"time",name:"HTMLTimeElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{dateTime:String,pubDate:Boolean}}),o({tag:"title",name:"HTMLTitleElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{text:{get:function(){return this.textContent}}}}),o({tag:"ul",name:"HTMLUListElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{type:String,compact:Boolean}}),o({name:"HTMLMediaElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{src:c,crossOrigin:d,preload:{type:["metadata","none","auto",{value:"",alias:"auto"}],missing:"auto"},loop:Boolean,autoplay:Boolean,mediaGroup:String,controls:Boolean,defaultMuted:{name:"muted",type:Boolean}}}),o({name:"HTMLAudioElement",tag:"audio",superclass:s.HTMLMediaElement,ctor:function(x,T,E){s.HTMLMediaElement.call(this,x,T,E)}}),o({name:"HTMLVideoElement",tag:"video",superclass:s.HTMLMediaElement,ctor:function(x,T,E){s.HTMLMediaElement.call(this,x,T,E)},attributes:{poster:c,width:{type:"unsigned long",min:0,default:0},height:{type:"unsigned long",min:0,default:0}}}),o({tag:"td",name:"HTMLTableDataCellElement",superclass:s.HTMLTableCellElement,ctor:function(x,T,E){s.HTMLTableCellElement.call(this,x,T,E)}}),o({tag:"th",name:"HTMLTableHeaderCellElement",superclass:s.HTMLTableCellElement,ctor:function(x,T,E){s.HTMLTableCellElement.call(this,x,T,E)}}),o({tag:"frameset",name:"HTMLFrameSetElement",ctor:function(x,T,E){w.call(this,x,T,E)}}),o({tag:"frame",name:"HTMLFrameElement",ctor:function(x,T,E){w.call(this,x,T,E)}}),o({tag:"canvas",name:"HTMLCanvasElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{getContext:{value:u.nyi},probablySupportsContext:{value:u.nyi},setContext:{value:u.nyi},transferControlToProxy:{value:u.nyi},toDataURL:{value:u.nyi},toBlob:{value:u.nyi}},attributes:{width:{type:"unsigned long",default:300},height:{type:"unsigned long",default:150}}}),o({tag:"dialog",name:"HTMLDialogElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{show:{value:u.nyi},showModal:{value:u.nyi},close:{value:u.nyi}},attributes:{open:Boolean,returnValue:String}}),o({tag:"menuitem",name:"HTMLMenuItemElement",ctor:function(x,T,E){w.call(this,x,T,E)},props:{_label:{get:function(){var _=this._getattr("label");return _!==null&&_!==""?_:(_=this.textContent,_.replace(/[ \t\n\f\r]+/g," ").trim())}},label:{get:function(){var _=this._getattr("label");return _!==null?_:this._label},set:function(_){this._setattr("label",_)}}},attributes:{type:{type:["command","checkbox","radio"],missing:"command"},icon:c,disabled:Boolean,checked:Boolean,radiogroup:String,default:Boolean}}),o({tag:"source",name:"HTMLSourceElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{srcset:String,sizes:String,media:String,src:c,type:String,width:String,height:String}}),o({tag:"track",name:"HTMLTrackElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{src:c,srclang:String,label:String,default:Boolean,kind:{type:["subtitles","captions","descriptions","chapters","metadata"],missing:"subtitles",invalid:"metadata"}},props:{NONE:{get:function(){return 0}},LOADING:{get:function(){return 1}},LOADED:{get:function(){return 2}},ERROR:{get:function(){return 3}},readyState:{get:u.nyi},track:{get:u.nyi}}}),o({tag:"font",name:"HTMLFontElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{color:{type:String,treatNullAsEmptyString:!0},face:{type:String},size:{type:String}}}),o({tag:"dir",name:"HTMLDirectoryElement",ctor:function(x,T,E){w.call(this,x,T,E)},attributes:{compact:Boolean}}),o({tags:["abbr","address","article","aside","b","bdi","bdo","cite","content","code","dd","dfn","dt","em","figcaption","figure","footer","header","hgroup","i","kbd","main","mark","nav","noscript","rb","rp","rt","rtc","ruby","s","samp","section","small","strong","sub","summary","sup","u","var","wbr","acronym","basefont","big","center","nobr","noembed","noframes","plaintext","strike","tt"]})}}),Sc=ne({"external/npm/node_modules/domino/lib/svg.js"(t){var e=su(),r=Ec(),n=$e(),u=Ta(),a=t.elements={},i=Object.create(null);t.createElement=function(o,c,d){var f=i[c]||l;return new f(o,c,d)};function s(o){return r(o,l,a,i)}var l=s({superclass:e,name:"SVGElement",ctor:function(c,d,f){e.call(this,c,d,n.NAMESPACE.SVG,f)},props:{style:{get:function(){return this._style||(this._style=new u(this)),this._style}}}});s({name:"SVGSVGElement",ctor:function(c,d,f){l.call(this,c,d,f)},tag:"svg",props:{createSVGRect:{value:function(){return t.createElement(this.ownerDocument,"rect",null)}}}}),s({tags:["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","hkern","image","line","linearGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"]})}}),vf=ne({"external/npm/node_modules/domino/lib/MutationConstants.js"(t,e){e.exports={VALUE:1,ATTR:2,REMOVE_ATTR:3,REMOVE:4,MOVE:5,INSERT:6}}}),Aa=ne({"external/npm/node_modules/domino/lib/Document.js"(t,e){e.exports=q;var r=tt(),n=qr(),u=wa(),a=su(),i=bc(),s=gc(),l=iu(),o=vc(),c=yc(),d=cn(),f=pf(),y=mf(),v=on(),w=Sa(),A=_a(),S=xc(),_=xa(),x=Na(),T=Sc(),E=$e(),O=vf(),B=E.NAMESPACE,F=ya().isApiWritable;function q(k,D){u.call(this),this.nodeType=r.DOCUMENT_NODE,this.isHTML=k,this._address=D||"about:blank",this.readyState="loading",this.implementation=new d(this),this.ownerDocument=null,this._contentType=k?"text/html":"application/xml",this.doctype=null,this.documentElement=null,this._templateDocCache=null,this._nodeIterators=null,this._nid=1,this._nextnid=2,this._nodes=[null,this],this.byId=Object.create(null),this.modclock=0}var C={event:"Event",customevent:"CustomEvent",uievent:"UIEvent",mouseevent:"MouseEvent"},P={events:"event",htmlevents:"event",mouseevents:"mouseevent",mutationevents:"mutationevent",uievents:"uievent"},W=function(k,D,K){return{get:function(){var me=k.call(this);return me?me[D]:K},set:function(me){var rt=k.call(this);rt&&(rt[D]=me)}}};function h(k,D){var K,me,rt;return k===""&&(k=null),_.isValidQName(D)||E.InvalidCharacterError(),K=null,me=D,rt=D.indexOf(":"),rt>=0&&(K=D.substring(0,rt),me=D.substring(rt+1)),K!==null&&k===null&&E.NamespaceError(),K==="xml"&&k!==B.XML&&E.NamespaceError(),(K==="xmlns"||D==="xmlns")&&k!==B.XMLNS&&E.NamespaceError(),k===B.XMLNS&&!(K==="xmlns"||D==="xmlns")&&E.NamespaceError(),{namespace:k,prefix:K,localName:me}}q.prototype=Object.create(u.prototype,{_setMutationHandler:{value:function(k){this.mutationHandler=k}},_dispatchRendererEvent:{value:function(k,D,K){var me=this._nodes[k];me&&me._dispatchEvent(new l(D,K),!0)}},nodeName:{value:"#document"},nodeValue:{get:function(){return null},set:function(){}},documentURI:{get:function(){return this._address},set:E.nyi},compatMode:{get:function(){return this._quirks?"BackCompat":"CSS1Compat"}},createTextNode:{value:function(k){return new i(this,String(k))}},createComment:{value:function(k){return new s(this,k)}},createDocumentFragment:{value:function(){return new o(this)}},createProcessingInstruction:{value:function(k,D){return(!_.isValidName(k)||D.indexOf("?>")!==-1)&&E.InvalidCharacterError(),new c(this,k,D)}},createAttribute:{value:function(k){return k=String(k),_.isValidName(k)||E.InvalidCharacterError(),this.isHTML&&(k=E.toASCIILowerCase(k)),new a._Attr(null,k,null,null,"")}},createAttributeNS:{value:function(k,D){k=k==null||k===""?null:String(k),D=String(D);var K=h(k,D);return new a._Attr(null,K.localName,K.prefix,K.namespace,"")}},createElement:{value:function(k){return k=String(k),_.isValidName(k)||E.InvalidCharacterError(),this.isHTML?(/[A-Z]/.test(k)&&(k=E.toASCIILowerCase(k)),x.createElement(this,k,null)):this.contentType==="application/xhtml+xml"?x.createElement(this,k,null):new a(this,k,null,null)},writable:F},createElementNS:{value:function(k,D){k=k==null||k===""?null:String(k),D=String(D);var K=h(k,D);return this._createElementNS(K.localName,K.namespace,K.prefix)},writable:F},_createElementNS:{value:function(k,D,K){return D===B.HTML?x.createElement(this,k,K):D===B.SVG?T.createElement(this,k,K):new a(this,k,D,K)}},createEvent:{value:function(D){D=D.toLowerCase();var K=P[D]||D,me=S[C[K]];if(me){var rt=new me;return rt._initialized=!1,rt}else E.NotSupportedError()}},createTreeWalker:{value:function(k,D,K){if(!k)throw new TypeError("root argument is required");if(!(k instanceof r))throw new TypeError("root not a node");return D=D===void 0?v.SHOW_ALL:+D,K=K===void 0?null:K,new f(k,D,K)}},createNodeIterator:{value:function(k,D,K){if(!k)throw new TypeError("root argument is required");if(!(k instanceof r))throw new TypeError("root not a node");return D=D===void 0?v.SHOW_ALL:+D,K=K===void 0?null:K,new y(k,D,K)}},_attachNodeIterator:{value:function(k){this._nodeIterators||(this._nodeIterators=[]),this._nodeIterators.push(k)}},_detachNodeIterator:{value:function(k){var D=this._nodeIterators.indexOf(k);this._nodeIterators.splice(D,1)}},_preremoveNodeIterators:{value:function(k){this._nodeIterators&&this._nodeIterators.forEach(function(D){D._preremove(k)})}},_updateDocTypeElement:{value:function(){this.doctype=this.documentElement=null;for(var D=this.firstChild;D!==null;D=D.nextSibling)D.nodeType===r.DOCUMENT_TYPE_NODE?this.doctype=D:D.nodeType===r.ELEMENT_NODE&&(this.documentElement=D)}},insertBefore:{value:function(D,K){return r.prototype.insertBefore.call(this,D,K),this._updateDocTypeElement(),D}},replaceChild:{value:function(D,K){return r.prototype.replaceChild.call(this,D,K),this._updateDocTypeElement(),K}},removeChild:{value:function(D){return r.prototype.removeChild.call(this,D),this._updateDocTypeElement(),D}},getElementById:{value:function(k){var D=this.byId[k];return D?D instanceof pe?D.getFirst():D:null}},_hasMultipleElementsWithId:{value:function(k){return this.byId[k]instanceof pe}},getElementsByName:{value:a.prototype.getElementsByName},getElementsByTagName:{value:a.prototype.getElementsByTagName},getElementsByTagNameNS:{value:a.prototype.getElementsByTagNameNS},getElementsByClassName:{value:a.prototype.getElementsByClassName},adoptNode:{value:function(D){return D.nodeType===r.DOCUMENT_NODE&&E.NotSupportedError(),D.nodeType===r.ATTRIBUTE_NODE||(D.parentNode&&D.parentNode.removeChild(D),D.ownerDocument!==this&&de(D,this)),D}},importNode:{value:function(D,K){return this.adoptNode(D.cloneNode(K))},writable:F},origin:{get:function(){return null}},characterSet:{get:function(){return"UTF-8"}},contentType:{get:function(){return this._contentType}},URL:{get:function(){return this._address}},domain:{get:E.nyi,set:E.nyi},referrer:{get:E.nyi},cookie:{get:E.nyi,set:E.nyi},lastModified:{get:E.nyi},location:{get:function(){return this.defaultView?this.defaultView.location:null},set:E.nyi},_titleElement:{get:function(){return this.getElementsByTagName("title").item(0)||null}},title:{get:function(){var k=this._titleElement,D=k?k.textContent:"";return D.replace(/[ \t\n\r\f]+/g," ").replace(/(^ )|( $)/g,"")},set:function(k){var D=this._titleElement,K=this.head;!D&&!K||(D||(D=this.createElement("title"),K.appendChild(D)),D.textContent=k)}},dir:W(function(){var k=this.documentElement;if(k&&k.tagName==="HTML")return k},"dir",""),fgColor:W(function(){return this.body},"text",""),linkColor:W(function(){return this.body},"link",""),vlinkColor:W(function(){return this.body},"vLink",""),alinkColor:W(function(){return this.body},"aLink",""),bgColor:W(function(){return this.body},"bgColor",""),charset:{get:function(){return this.characterSet}},inputEncoding:{get:function(){return this.characterSet}},scrollingElement:{get:function(){return this._quirks?this.body:this.documentElement}},body:{get:function(){return g(this.documentElement,"body")},set:E.nyi},head:{get:function(){return g(this.documentElement,"head")}},images:{get:E.nyi},embeds:{get:E.nyi},plugins:{get:E.nyi},links:{get:E.nyi},forms:{get:E.nyi},scripts:{get:E.nyi},applets:{get:function(){return[]}},activeElement:{get:function(){return null}},innerHTML:{get:function(){return this.serialize()},set:E.nyi},outerHTML:{get:function(){return this.serialize()},set:E.nyi},write:{value:function(k){if(this.isHTML||E.InvalidStateError(),!!this._parser){var D=arguments.join("");this._parser.parse(D)}}},writeln:{value:function(D){this.write(Array.prototype.join.call(arguments,"")+`
`)}},open:{value:function(){this.documentElement=null}},close:{value:function(){this.readyState="interactive",this._dispatchEvent(new l("readystatechange"),!0),this._dispatchEvent(new l("DOMContentLoaded"),!0),this.readyState="complete",this._dispatchEvent(new l("readystatechange"),!0),this.defaultView&&this.defaultView._dispatchEvent(new l("load"),!0)}},clone:{value:function(){var D=new q(this.isHTML,this._address);return D._quirks=this._quirks,D._contentType=this._contentType,D}},cloneNode:{value:function(D){var K=r.prototype.cloneNode.call(this,!1);if(D)for(var me=this.firstChild;me!==null;me=me.nextSibling)K._appendChild(K.importNode(me,!0));return K._updateDocTypeElement(),K}},isEqual:{value:function(D){return!0}},mutateValue:{value:function(k){this.mutationHandler&&this.mutationHandler({type:O.VALUE,target:k,data:k.data})}},mutateAttr:{value:function(k,D){this.mutationHandler&&this.mutationHandler({type:O.ATTR,target:k.ownerElement,attr:k})}},mutateRemoveAttr:{value:function(k){this.mutationHandler&&this.mutationHandler({type:O.REMOVE_ATTR,target:k.ownerElement,attr:k})}},mutateRemove:{value:function(k){this.mutationHandler&&this.mutationHandler({type:O.REMOVE,target:k.parentNode,node:k}),J(k)}},mutateInsert:{value:function(k){$(k),this.mutationHandler&&this.mutationHandler({type:O.INSERT,target:k.parentNode,node:k})}},mutateMove:{value:function(k){this.mutationHandler&&this.mutationHandler({type:O.MOVE,target:k})}},addId:{value:function(D,K){var me=this.byId[D];me?(me instanceof pe||(me=new pe(me),this.byId[D]=me),me.add(K)):this.byId[D]=K}},delId:{value:function(D,K){var me=this.byId[D];E.assert(me),me instanceof pe?(me.del(K),me.length===1&&(this.byId[D]=me.downgrade())):this.byId[D]=void 0}},_resolve:{value:function(k){return new w(this._documentBaseURL).resolve(k)}},_documentBaseURL:{get:function(){var k=this._address;k==="about:blank"&&(k="/");var D=this.querySelector("base[href]");return D?new w(k).resolve(D.getAttribute("href")):k}},_templateDoc:{get:function(){if(!this._templateDocCache){var k=new q(this.isHTML,this._address);this._templateDocCache=k._templateDocCache=k}return this._templateDocCache}},querySelector:{value:function(k){return A(k,this)[0]}},querySelectorAll:{value:function(k){var D=A(k,this);return D.item?D:new n(D)}}});var m=["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"];m.forEach(function(k){Object.defineProperty(q.prototype,"on"+k,{get:function(){return this._getEventHandler(k)},set:function(D){this._setEventHandler(k,D)}})});function g(k,D){if(k&&k.isHTML){for(var K=k.firstChild;K!==null;K=K.nextSibling)if(K.nodeType===r.ELEMENT_NODE&&K.localName===D&&K.namespaceURI===B.HTML)return K}return null}function M(k){if(k._nid=k.ownerDocument._nextnid++,k.ownerDocument._nodes[k._nid]=k,k.nodeType===r.ELEMENT_NODE){var D=k.getAttribute("id");D&&k.ownerDocument.addId(D,k),k._roothook&&k._roothook()}}function H(k){if(k.nodeType===r.ELEMENT_NODE){var D=k.getAttribute("id");D&&k.ownerDocument.delId(D,k)}k.ownerDocument._nodes[k._nid]=void 0,k._nid=void 0}function $(k){if(M(k),k.nodeType===r.ELEMENT_NODE)for(var D=k.firstChild;D!==null;D=D.nextSibling)$(D)}function J(k){H(k);for(var D=k.firstChild;D!==null;D=D.nextSibling)J(D)}function de(k,D){k.ownerDocument=D,k._lastModTime=void 0,Object.prototype.hasOwnProperty.call(k,"_tagName")&&(k._tagName=void 0);for(var K=k.firstChild;K!==null;K=K.nextSibling)de(K,D)}function pe(k){this.nodes=Object.create(null),this.nodes[k._nid]=k,this.length=1,this.firstNode=void 0}pe.prototype.add=function(k){this.nodes[k._nid]||(this.nodes[k._nid]=k,this.length++,this.firstNode=void 0)},pe.prototype.del=function(k){this.nodes[k._nid]&&(delete this.nodes[k._nid],this.length--,this.firstNode=void 0)},pe.prototype.getFirst=function(){if(!this.firstNode){var k;for(k in this.nodes)(this.firstNode===void 0||this.firstNode.compareDocumentPosition(this.nodes[k])&r.DOCUMENT_POSITION_PRECEDING)&&(this.firstNode=this.nodes[k])}return this.firstNode},pe.prototype.downgrade=function(){if(this.length===1){var k;for(k in this.nodes)return this.nodes[k]}return this}}}),Ca=ne({"external/npm/node_modules/domino/lib/DocumentType.js"(t,e){e.exports=a;var r=tt(),n=mc(),u=Ea();function a(i,s,l,o){n.call(this),this.nodeType=r.DOCUMENT_TYPE_NODE,this.ownerDocument=i||null,this.name=s,this.publicId=l||"",this.systemId=o||""}a.prototype=Object.create(n.prototype,{nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return null},set:function(){}},clone:{value:function(){return new a(this.ownerDocument,this.name,this.publicId,this.systemId)}},isEqual:{value:function(s){return this.name===s.name&&this.publicId===s.publicId&&this.systemId===s.systemId}}}),Object.defineProperties(a.prototype,u)}}),ka=ne({"external/npm/node_modules/domino/lib/HTMLParser.js"(t,e){e.exports=we;var r=Aa(),n=Ca(),u=tt(),a=$e().NAMESPACE,i=Na(),s=i.elements,l=Function.prototype.apply.bind(Array.prototype.push),o=-1,c=1,d=2,f=3,y=4,v=5,w=[],A=/^HTML$|^-\/\/W3O\/\/DTD W3 HTML Strict 3\.0\/\/EN\/\/$|^-\/W3C\/DTD HTML 4\.0 Transitional\/EN$|^\+\/\/Silmaril\/\/dtd html Pro v0r11 19970101\/\/|^-\/\/AdvaSoft Ltd\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/AS\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict\/\/|^-\/\/IETF\/\/DTD HTML 2\.0\/\/|^-\/\/IETF\/\/DTD HTML 2\.1E\/\/|^-\/\/IETF\/\/DTD HTML 3\.0\/\/|^-\/\/IETF\/\/DTD HTML 3\.2 Final\/\/|^-\/\/IETF\/\/DTD HTML 3\.2\/\/|^-\/\/IETF\/\/DTD HTML 3\/\/|^-\/\/IETF\/\/DTD HTML Level 0\/\/|^-\/\/IETF\/\/DTD HTML Level 1\/\/|^-\/\/IETF\/\/DTD HTML Level 2\/\/|^-\/\/IETF\/\/DTD HTML Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 0\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict\/\/|^-\/\/IETF\/\/DTD HTML\/\/|^-\/\/Metrius\/\/DTD Metrius Presentational\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 Tables\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 Tables\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD HTML\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD Strict HTML\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML 2\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended 1\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended Relaxed 1\.0\/\/|^-\/\/SoftQuad Software\/\/DTD HoTMetaL PRO 6\.0::19990601::extensions to HTML 4\.0\/\/|^-\/\/SoftQuad\/\/DTD HoTMetaL PRO 4\.0::19971010::extensions to HTML 4\.0\/\/|^-\/\/Spyglass\/\/DTD HTML 2\.0 Extended\/\/|^-\/\/SQ\/\/DTD HTML 2\.0 HoTMetaL \+ extensions\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava HTML\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava Strict HTML\/\/|^-\/\/W3C\/\/DTD HTML 3 1995-03-24\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Draft\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Final\/\/|^-\/\/W3C\/\/DTD HTML 3\.2\/\/|^-\/\/W3C\/\/DTD HTML 3\.2S Draft\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Transitional\/\/|^-\/\/W3C\/\/DTD HTML Experimental 19960712\/\/|^-\/\/W3C\/\/DTD HTML Experimental 970421\/\/|^-\/\/W3C\/\/DTD W3 HTML\/\/|^-\/\/W3O\/\/DTD W3 HTML 3\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML 2\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML\/\//i,S="http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd",_=/^-\/\/W3C\/\/DTD HTML 4\.01 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.01 Transitional\/\//i,x=/^-\/\/W3C\/\/DTD XHTML 1\.0 Frameset\/\/|^-\/\/W3C\/\/DTD XHTML 1\.0 Transitional\/\//i,T=Object.create(null);T[a.HTML]={__proto__:null,address:!0,applet:!0,area:!0,article:!0,aside:!0,base:!0,basefont:!0,bgsound:!0,blockquote:!0,body:!0,br:!0,button:!0,caption:!0,center:!0,col:!0,colgroup:!0,dd:!0,details:!0,dir:!0,div:!0,dl:!0,dt:!0,embed:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,frame:!0,frameset:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,head:!0,header:!0,hgroup:!0,hr:!0,html:!0,iframe:!0,img:!0,input:!0,li:!0,link:!0,listing:!0,main:!0,marquee:!0,menu:!0,meta:!0,nav:!0,noembed:!0,noframes:!0,noscript:!0,object:!0,ol:!0,p:!0,param:!0,plaintext:!0,pre:!0,script:!0,section:!0,select:!0,source:!0,style:!0,summary:!0,table:!0,tbody:!0,td:!0,template:!0,textarea:!0,tfoot:!0,th:!0,thead:!0,title:!0,tr:!0,track:!0,ul:!0,wbr:!0,xmp:!0},T[a.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0},T[a.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0};var E=Object.create(null);E[a.HTML]={__proto__:null,address:!0,div:!0,p:!0};var O=Object.create(null);O[a.HTML]={__proto__:null,dd:!0,dt:!0};var B=Object.create(null);B[a.HTML]={__proto__:null,table:!0,thead:!0,tbody:!0,tfoot:!0,tr:!0};var F=Object.create(null);F[a.HTML]={__proto__:null,dd:!0,dt:!0,li:!0,menuitem:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0};var q=Object.create(null);q[a.HTML]={__proto__:null,caption:!0,colgroup:!0,dd:!0,dt:!0,li:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0,tbody:!0,td:!0,tfoot:!0,th:!0,thead:!0,tr:!0};var C=Object.create(null);C[a.HTML]={__proto__:null,table:!0,template:!0,html:!0};var P=Object.create(null);P[a.HTML]={__proto__:null,tbody:!0,tfoot:!0,thead:!0,template:!0,html:!0};var W=Object.create(null);W[a.HTML]={__proto__:null,tr:!0,template:!0,html:!0};var h=Object.create(null);h[a.HTML]={__proto__:null,button:!0,fieldset:!0,input:!0,keygen:!0,object:!0,output:!0,select:!0,textarea:!0,img:!0};var m=Object.create(null);m[a.HTML]={__proto__:null,applet:!0,caption:!0,html:!0,table:!0,td:!0,th:!0,marquee:!0,object:!0,template:!0},m[a.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0},m[a.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var g=Object.create(m);g[a.HTML]=Object.create(m[a.HTML]),g[a.HTML].ol=!0,g[a.HTML].ul=!0;var M=Object.create(m);M[a.HTML]=Object.create(m[a.HTML]),M[a.HTML].button=!0;var H=Object.create(null);H[a.HTML]={__proto__:null,html:!0,table:!0,template:!0};var $=Object.create(null);$[a.HTML]={__proto__:null,optgroup:!0,option:!0};var J=Object.create(null);J[a.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0};var de=Object.create(null);de[a.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var pe={__proto__:null,"xlink:actuate":a.XLINK,"xlink:arcrole":a.XLINK,"xlink:href":a.XLINK,"xlink:role":a.XLINK,"xlink:show":a.XLINK,"xlink:title":a.XLINK,"xlink:type":a.XLINK,"xml:base":a.XML,"xml:lang":a.XML,"xml:space":a.XML,xmlns:a.XMLNS,"xmlns:xlink":a.XMLNS},k={__proto__:null,attributename:"attributeName",attributetype:"attributeType",basefrequency:"baseFrequency",baseprofile:"baseProfile",calcmode:"calcMode",clippathunits:"clipPathUnits",diffuseconstant:"diffuseConstant",edgemode:"edgeMode",filterunits:"filterUnits",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",limitingconeangle:"limitingConeAngle",markerheight:"markerHeight",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textlength:"textLength",viewbox:"viewBox",viewtarget:"viewTarget",xchannelselector:"xChannelSelector",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"},D={__proto__:null,altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient",textpath:"textPath"},K={__proto__:null,0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376},me={__proto__:null,AElig:198,"AElig;":198,AMP:38,"AMP;":38,Aacute:193,"Aacute;":193,"Abreve;":258,Acirc:194,"Acirc;":194,"Acy;":1040,"Afr;":[55349,56580],Agrave:192,"Agrave;":192,"Alpha;":913,"Amacr;":256,"And;":10835,"Aogon;":260,"Aopf;":[55349,56632],"ApplyFunction;":8289,Aring:197,"Aring;":197,"Ascr;":[55349,56476],"Assign;":8788,Atilde:195,"Atilde;":195,Auml:196,"Auml;":196,"Backslash;":8726,"Barv;":10983,"Barwed;":8966,"Bcy;":1041,"Because;":8757,"Bernoullis;":8492,"Beta;":914,"Bfr;":[55349,56581],"Bopf;":[55349,56633],"Breve;":728,"Bscr;":8492,"Bumpeq;":8782,"CHcy;":1063,COPY:169,"COPY;":169,"Cacute;":262,"Cap;":8914,"CapitalDifferentialD;":8517,"Cayleys;":8493,"Ccaron;":268,Ccedil:199,"Ccedil;":199,"Ccirc;":264,"Cconint;":8752,"Cdot;":266,"Cedilla;":184,"CenterDot;":183,"Cfr;":8493,"Chi;":935,"CircleDot;":8857,"CircleMinus;":8854,"CirclePlus;":8853,"CircleTimes;":8855,"ClockwiseContourIntegral;":8754,"CloseCurlyDoubleQuote;":8221,"CloseCurlyQuote;":8217,"Colon;":8759,"Colone;":10868,"Congruent;":8801,"Conint;":8751,"ContourIntegral;":8750,"Copf;":8450,"Coproduct;":8720,"CounterClockwiseContourIntegral;":8755,"Cross;":10799,"Cscr;":[55349,56478],"Cup;":8915,"CupCap;":8781,"DD;":8517,"DDotrahd;":10513,"DJcy;":1026,"DScy;":1029,"DZcy;":1039,"Dagger;":8225,"Darr;":8609,"Dashv;":10980,"Dcaron;":270,"Dcy;":1044,"Del;":8711,"Delta;":916,"Dfr;":[55349,56583],"DiacriticalAcute;":180,"DiacriticalDot;":729,"DiacriticalDoubleAcute;":733,"DiacriticalGrave;":96,"DiacriticalTilde;":732,"Diamond;":8900,"DifferentialD;":8518,"Dopf;":[55349,56635],"Dot;":168,"DotDot;":8412,"DotEqual;":8784,"DoubleContourIntegral;":8751,"DoubleDot;":168,"DoubleDownArrow;":8659,"DoubleLeftArrow;":8656,"DoubleLeftRightArrow;":8660,"DoubleLeftTee;":10980,"DoubleLongLeftArrow;":10232,"DoubleLongLeftRightArrow;":10234,"DoubleLongRightArrow;":10233,"DoubleRightArrow;":8658,"DoubleRightTee;":8872,"DoubleUpArrow;":8657,"DoubleUpDownArrow;":8661,"DoubleVerticalBar;":8741,"DownArrow;":8595,"DownArrowBar;":10515,"DownArrowUpArrow;":8693,"DownBreve;":785,"DownLeftRightVector;":10576,"DownLeftTeeVector;":10590,"DownLeftVector;":8637,"DownLeftVectorBar;":10582,"DownRightTeeVector;":10591,"DownRightVector;":8641,"DownRightVectorBar;":10583,"DownTee;":8868,"DownTeeArrow;":8615,"Downarrow;":8659,"Dscr;":[55349,56479],"Dstrok;":272,"ENG;":330,ETH:208,"ETH;":208,Eacute:201,"Eacute;":201,"Ecaron;":282,Ecirc:202,"Ecirc;":202,"Ecy;":1069,"Edot;":278,"Efr;":[55349,56584],Egrave:200,"Egrave;":200,"Element;":8712,"Emacr;":274,"EmptySmallSquare;":9723,"EmptyVerySmallSquare;":9643,"Eogon;":280,"Eopf;":[55349,56636],"Epsilon;":917,"Equal;":10869,"EqualTilde;":8770,"Equilibrium;":8652,"Escr;":8496,"Esim;":10867,"Eta;":919,Euml:203,"Euml;":203,"Exists;":8707,"ExponentialE;":8519,"Fcy;":1060,"Ffr;":[55349,56585],"FilledSmallSquare;":9724,"FilledVerySmallSquare;":9642,"Fopf;":[55349,56637],"ForAll;":8704,"Fouriertrf;":8497,"Fscr;":8497,"GJcy;":1027,GT:62,"GT;":62,"Gamma;":915,"Gammad;":988,"Gbreve;":286,"Gcedil;":290,"Gcirc;":284,"Gcy;":1043,"Gdot;":288,"Gfr;":[55349,56586],"Gg;":8921,"Gopf;":[55349,56638],"GreaterEqual;":8805,"GreaterEqualLess;":8923,"GreaterFullEqual;":8807,"GreaterGreater;":10914,"GreaterLess;":8823,"GreaterSlantEqual;":10878,"GreaterTilde;":8819,"Gscr;":[55349,56482],"Gt;":8811,"HARDcy;":1066,"Hacek;":711,"Hat;":94,"Hcirc;":292,"Hfr;":8460,"HilbertSpace;":8459,"Hopf;":8461,"HorizontalLine;":9472,"Hscr;":8459,"Hstrok;":294,"HumpDownHump;":8782,"HumpEqual;":8783,"IEcy;":1045,"IJlig;":306,"IOcy;":1025,Iacute:205,"Iacute;":205,Icirc:206,"Icirc;":206,"Icy;":1048,"Idot;":304,"Ifr;":8465,Igrave:204,"Igrave;":204,"Im;":8465,"Imacr;":298,"ImaginaryI;":8520,"Implies;":8658,"Int;":8748,"Integral;":8747,"Intersection;":8898,"InvisibleComma;":8291,"InvisibleTimes;":8290,"Iogon;":302,"Iopf;":[55349,56640],"Iota;":921,"Iscr;":8464,"Itilde;":296,"Iukcy;":1030,Iuml:207,"Iuml;":207,"Jcirc;":308,"Jcy;":1049,"Jfr;":[55349,56589],"Jopf;":[55349,56641],"Jscr;":[55349,56485],"Jsercy;":1032,"Jukcy;":1028,"KHcy;":1061,"KJcy;":1036,"Kappa;":922,"Kcedil;":310,"Kcy;":1050,"Kfr;":[55349,56590],"Kopf;":[55349,56642],"Kscr;":[55349,56486],"LJcy;":1033,LT:60,"LT;":60,"Lacute;":313,"Lambda;":923,"Lang;":10218,"Laplacetrf;":8466,"Larr;":8606,"Lcaron;":317,"Lcedil;":315,"Lcy;":1051,"LeftAngleBracket;":10216,"LeftArrow;":8592,"LeftArrowBar;":8676,"LeftArrowRightArrow;":8646,"LeftCeiling;":8968,"LeftDoubleBracket;":10214,"LeftDownTeeVector;":10593,"LeftDownVector;":8643,"LeftDownVectorBar;":10585,"LeftFloor;":8970,"LeftRightArrow;":8596,"LeftRightVector;":10574,"LeftTee;":8867,"LeftTeeArrow;":8612,"LeftTeeVector;":10586,"LeftTriangle;":8882,"LeftTriangleBar;":10703,"LeftTriangleEqual;":8884,"LeftUpDownVector;":10577,"LeftUpTeeVector;":10592,"LeftUpVector;":8639,"LeftUpVectorBar;":10584,"LeftVector;":8636,"LeftVectorBar;":10578,"Leftarrow;":8656,"Leftrightarrow;":8660,"LessEqualGreater;":8922,"LessFullEqual;":8806,"LessGreater;":8822,"LessLess;":10913,"LessSlantEqual;":10877,"LessTilde;":8818,"Lfr;":[55349,56591],"Ll;":8920,"Lleftarrow;":8666,"Lmidot;":319,"LongLeftArrow;":10229,"LongLeftRightArrow;":10231,"LongRightArrow;":10230,"Longleftarrow;":10232,"Longleftrightarrow;":10234,"Longrightarrow;":10233,"Lopf;":[55349,56643],"LowerLeftArrow;":8601,"LowerRightArrow;":8600,"Lscr;":8466,"Lsh;":8624,"Lstrok;":321,"Lt;":8810,"Map;":10501,"Mcy;":1052,"MediumSpace;":8287,"Mellintrf;":8499,"Mfr;":[55349,56592],"MinusPlus;":8723,"Mopf;":[55349,56644],"Mscr;":8499,"Mu;":924,"NJcy;":1034,"Nacute;":323,"Ncaron;":327,"Ncedil;":325,"Ncy;":1053,"NegativeMediumSpace;":8203,"NegativeThickSpace;":8203,"NegativeThinSpace;":8203,"NegativeVeryThinSpace;":8203,"NestedGreaterGreater;":8811,"NestedLessLess;":8810,"NewLine;":10,"Nfr;":[55349,56593],"NoBreak;":8288,"NonBreakingSpace;":160,"Nopf;":8469,"Not;":10988,"NotCongruent;":8802,"NotCupCap;":8813,"NotDoubleVerticalBar;":8742,"NotElement;":8713,"NotEqual;":8800,"NotEqualTilde;":[8770,824],"NotExists;":8708,"NotGreater;":8815,"NotGreaterEqual;":8817,"NotGreaterFullEqual;":[8807,824],"NotGreaterGreater;":[8811,824],"NotGreaterLess;":8825,"NotGreaterSlantEqual;":[10878,824],"NotGreaterTilde;":8821,"NotHumpDownHump;":[8782,824],"NotHumpEqual;":[8783,824],"NotLeftTriangle;":8938,"NotLeftTriangleBar;":[10703,824],"NotLeftTriangleEqual;":8940,"NotLess;":8814,"NotLessEqual;":8816,"NotLessGreater;":8824,"NotLessLess;":[8810,824],"NotLessSlantEqual;":[10877,824],"NotLessTilde;":8820,"NotNestedGreaterGreater;":[10914,824],"NotNestedLessLess;":[10913,824],"NotPrecedes;":8832,"NotPrecedesEqual;":[10927,824],"NotPrecedesSlantEqual;":8928,"NotReverseElement;":8716,"NotRightTriangle;":8939,"NotRightTriangleBar;":[10704,824],"NotRightTriangleEqual;":8941,"NotSquareSubset;":[8847,824],"NotSquareSubsetEqual;":8930,"NotSquareSuperset;":[8848,824],"NotSquareSupersetEqual;":8931,"NotSubset;":[8834,8402],"NotSubsetEqual;":8840,"NotSucceeds;":8833,"NotSucceedsEqual;":[10928,824],"NotSucceedsSlantEqual;":8929,"NotSucceedsTilde;":[8831,824],"NotSuperset;":[8835,8402],"NotSupersetEqual;":8841,"NotTilde;":8769,"NotTildeEqual;":8772,"NotTildeFullEqual;":8775,"NotTildeTilde;":8777,"NotVerticalBar;":8740,"Nscr;":[55349,56489],Ntilde:209,"Ntilde;":209,"Nu;":925,"OElig;":338,Oacute:211,"Oacute;":211,Ocirc:212,"Ocirc;":212,"Ocy;":1054,"Odblac;":336,"Ofr;":[55349,56594],Ograve:210,"Ograve;":210,"Omacr;":332,"Omega;":937,"Omicron;":927,"Oopf;":[55349,56646],"OpenCurlyDoubleQuote;":8220,"OpenCurlyQuote;":8216,"Or;":10836,"Oscr;":[55349,56490],Oslash:216,"Oslash;":216,Otilde:213,"Otilde;":213,"Otimes;":10807,Ouml:214,"Ouml;":214,"OverBar;":8254,"OverBrace;":9182,"OverBracket;":9140,"OverParenthesis;":9180,"PartialD;":8706,"Pcy;":1055,"Pfr;":[55349,56595],"Phi;":934,"Pi;":928,"PlusMinus;":177,"Poincareplane;":8460,"Popf;":8473,"Pr;":10939,"Precedes;":8826,"PrecedesEqual;":10927,"PrecedesSlantEqual;":8828,"PrecedesTilde;":8830,"Prime;":8243,"Product;":8719,"Proportion;":8759,"Proportional;":8733,"Pscr;":[55349,56491],"Psi;":936,QUOT:34,"QUOT;":34,"Qfr;":[55349,56596],"Qopf;":8474,"Qscr;":[55349,56492],"RBarr;":10512,REG:174,"REG;":174,"Racute;":340,"Rang;":10219,"Rarr;":8608,"Rarrtl;":10518,"Rcaron;":344,"Rcedil;":342,"Rcy;":1056,"Re;":8476,"ReverseElement;":8715,"ReverseEquilibrium;":8651,"ReverseUpEquilibrium;":10607,"Rfr;":8476,"Rho;":929,"RightAngleBracket;":10217,"RightArrow;":8594,"RightArrowBar;":8677,"RightArrowLeftArrow;":8644,"RightCeiling;":8969,"RightDoubleBracket;":10215,"RightDownTeeVector;":10589,"RightDownVector;":8642,"RightDownVectorBar;":10581,"RightFloor;":8971,"RightTee;":8866,"RightTeeArrow;":8614,"RightTeeVector;":10587,"RightTriangle;":8883,"RightTriangleBar;":10704,"RightTriangleEqual;":8885,"RightUpDownVector;":10575,"RightUpTeeVector;":10588,"RightUpVector;":8638,"RightUpVectorBar;":10580,"RightVector;":8640,"RightVectorBar;":10579,"Rightarrow;":8658,"Ropf;":8477,"RoundImplies;":10608,"Rrightarrow;":8667,"Rscr;":8475,"Rsh;":8625,"RuleDelayed;":10740,"SHCHcy;":1065,"SHcy;":1064,"SOFTcy;":1068,"Sacute;":346,"Sc;":10940,"Scaron;":352,"Scedil;":350,"Scirc;":348,"Scy;":1057,"Sfr;":[55349,56598],"ShortDownArrow;":8595,"ShortLeftArrow;":8592,"ShortRightArrow;":8594,"ShortUpArrow;":8593,"Sigma;":931,"SmallCircle;":8728,"Sopf;":[55349,56650],"Sqrt;":8730,"Square;":9633,"SquareIntersection;":8851,"SquareSubset;":8847,"SquareSubsetEqual;":8849,"SquareSuperset;":8848,"SquareSupersetEqual;":8850,"SquareUnion;":8852,"Sscr;":[55349,56494],"Star;":8902,"Sub;":8912,"Subset;":8912,"SubsetEqual;":8838,"Succeeds;":8827,"SucceedsEqual;":10928,"SucceedsSlantEqual;":8829,"SucceedsTilde;":8831,"SuchThat;":8715,"Sum;":8721,"Sup;":8913,"Superset;":8835,"SupersetEqual;":8839,"Supset;":8913,THORN:222,"THORN;":222,"TRADE;":8482,"TSHcy;":1035,"TScy;":1062,"Tab;":9,"Tau;":932,"Tcaron;":356,"Tcedil;":354,"Tcy;":1058,"Tfr;":[55349,56599],"Therefore;":8756,"Theta;":920,"ThickSpace;":[8287,8202],"ThinSpace;":8201,"Tilde;":8764,"TildeEqual;":8771,"TildeFullEqual;":8773,"TildeTilde;":8776,"Topf;":[55349,56651],"TripleDot;":8411,"Tscr;":[55349,56495],"Tstrok;":358,Uacute:218,"Uacute;":218,"Uarr;":8607,"Uarrocir;":10569,"Ubrcy;":1038,"Ubreve;":364,Ucirc:219,"Ucirc;":219,"Ucy;":1059,"Udblac;":368,"Ufr;":[55349,56600],Ugrave:217,"Ugrave;":217,"Umacr;":362,"UnderBar;":95,"UnderBrace;":9183,"UnderBracket;":9141,"UnderParenthesis;":9181,"Union;":8899,"UnionPlus;":8846,"Uogon;":370,"Uopf;":[55349,56652],"UpArrow;":8593,"UpArrowBar;":10514,"UpArrowDownArrow;":8645,"UpDownArrow;":8597,"UpEquilibrium;":10606,"UpTee;":8869,"UpTeeArrow;":8613,"Uparrow;":8657,"Updownarrow;":8661,"UpperLeftArrow;":8598,"UpperRightArrow;":8599,"Upsi;":978,"Upsilon;":933,"Uring;":366,"Uscr;":[55349,56496],"Utilde;":360,Uuml:220,"Uuml;":220,"VDash;":8875,"Vbar;":10987,"Vcy;":1042,"Vdash;":8873,"Vdashl;":10982,"Vee;":8897,"Verbar;":8214,"Vert;":8214,"VerticalBar;":8739,"VerticalLine;":124,"VerticalSeparator;":10072,"VerticalTilde;":8768,"VeryThinSpace;":8202,"Vfr;":[55349,56601],"Vopf;":[55349,56653],"Vscr;":[55349,56497],"Vvdash;":8874,"Wcirc;":372,"Wedge;":8896,"Wfr;":[55349,56602],"Wopf;":[55349,56654],"Wscr;":[55349,56498],"Xfr;":[55349,56603],"Xi;":926,"Xopf;":[55349,56655],"Xscr;":[55349,56499],"YAcy;":1071,"YIcy;":1031,"YUcy;":1070,Yacute:221,"Yacute;":221,"Ycirc;":374,"Ycy;":1067,"Yfr;":[55349,56604],"Yopf;":[55349,56656],"Yscr;":[55349,56500],"Yuml;":376,"ZHcy;":1046,"Zacute;":377,"Zcaron;":381,"Zcy;":1047,"Zdot;":379,"ZeroWidthSpace;":8203,"Zeta;":918,"Zfr;":8488,"Zopf;":8484,"Zscr;":[55349,56501],aacute:225,"aacute;":225,"abreve;":259,"ac;":8766,"acE;":[8766,819],"acd;":8767,acirc:226,"acirc;":226,acute:180,"acute;":180,"acy;":1072,aelig:230,"aelig;":230,"af;":8289,"afr;":[55349,56606],agrave:224,"agrave;":224,"alefsym;":8501,"aleph;":8501,"alpha;":945,"amacr;":257,"amalg;":10815,amp:38,"amp;":38,"and;":8743,"andand;":10837,"andd;":10844,"andslope;":10840,"andv;":10842,"ang;":8736,"ange;":10660,"angle;":8736,"angmsd;":8737,"angmsdaa;":10664,"angmsdab;":10665,"angmsdac;":10666,"angmsdad;":10667,"angmsdae;":10668,"angmsdaf;":10669,"angmsdag;":10670,"angmsdah;":10671,"angrt;":8735,"angrtvb;":8894,"angrtvbd;":10653,"angsph;":8738,"angst;":197,"angzarr;":9084,"aogon;":261,"aopf;":[55349,56658],"ap;":8776,"apE;":10864,"apacir;":10863,"ape;":8778,"apid;":8779,"apos;":39,"approx;":8776,"approxeq;":8778,aring:229,"aring;":229,"ascr;":[55349,56502],"ast;":42,"asymp;":8776,"asympeq;":8781,atilde:227,"atilde;":227,auml:228,"auml;":228,"awconint;":8755,"awint;":10769,"bNot;":10989,"backcong;":8780,"backepsilon;":1014,"backprime;":8245,"backsim;":8765,"backsimeq;":8909,"barvee;":8893,"barwed;":8965,"barwedge;":8965,"bbrk;":9141,"bbrktbrk;":9142,"bcong;":8780,"bcy;":1073,"bdquo;":8222,"becaus;":8757,"because;":8757,"bemptyv;":10672,"bepsi;":1014,"bernou;":8492,"beta;":946,"beth;":8502,"between;":8812,"bfr;":[55349,56607],"bigcap;":8898,"bigcirc;":9711,"bigcup;":8899,"bigodot;":10752,"bigoplus;":10753,"bigotimes;":10754,"bigsqcup;":10758,"bigstar;":9733,"bigtriangledown;":9661,"bigtriangleup;":9651,"biguplus;":10756,"bigvee;":8897,"bigwedge;":8896,"bkarow;":10509,"blacklozenge;":10731,"blacksquare;":9642,"blacktriangle;":9652,"blacktriangledown;":9662,"blacktriangleleft;":9666,"blacktriangleright;":9656,"blank;":9251,"blk12;":9618,"blk14;":9617,"blk34;":9619,"block;":9608,"bne;":[61,8421],"bnequiv;":[8801,8421],"bnot;":8976,"bopf;":[55349,56659],"bot;":8869,"bottom;":8869,"bowtie;":8904,"boxDL;":9559,"boxDR;":9556,"boxDl;":9558,"boxDr;":9555,"boxH;":9552,"boxHD;":9574,"boxHU;":9577,"boxHd;":9572,"boxHu;":9575,"boxUL;":9565,"boxUR;":9562,"boxUl;":9564,"boxUr;":9561,"boxV;":9553,"boxVH;":9580,"boxVL;":9571,"boxVR;":9568,"boxVh;":9579,"boxVl;":9570,"boxVr;":9567,"boxbox;":10697,"boxdL;":9557,"boxdR;":9554,"boxdl;":9488,"boxdr;":9484,"boxh;":9472,"boxhD;":9573,"boxhU;":9576,"boxhd;":9516,"boxhu;":9524,"boxminus;":8863,"boxplus;":8862,"boxtimes;":8864,"boxuL;":9563,"boxuR;":9560,"boxul;":9496,"boxur;":9492,"boxv;":9474,"boxvH;":9578,"boxvL;":9569,"boxvR;":9566,"boxvh;":9532,"boxvl;":9508,"boxvr;":9500,"bprime;":8245,"breve;":728,brvbar:166,"brvbar;":166,"bscr;":[55349,56503],"bsemi;":8271,"bsim;":8765,"bsime;":8909,"bsol;":92,"bsolb;":10693,"bsolhsub;":10184,"bull;":8226,"bullet;":8226,"bump;":8782,"bumpE;":10926,"bumpe;":8783,"bumpeq;":8783,"cacute;":263,"cap;":8745,"capand;":10820,"capbrcup;":10825,"capcap;":10827,"capcup;":10823,"capdot;":10816,"caps;":[8745,65024],"caret;":8257,"caron;":711,"ccaps;":10829,"ccaron;":269,ccedil:231,"ccedil;":231,"ccirc;":265,"ccups;":10828,"ccupssm;":10832,"cdot;":267,cedil:184,"cedil;":184,"cemptyv;":10674,cent:162,"cent;":162,"centerdot;":183,"cfr;":[55349,56608],"chcy;":1095,"check;":10003,"checkmark;":10003,"chi;":967,"cir;":9675,"cirE;":10691,"circ;":710,"circeq;":8791,"circlearrowleft;":8634,"circlearrowright;":8635,"circledR;":174,"circledS;":9416,"circledast;":8859,"circledcirc;":8858,"circleddash;":8861,"cire;":8791,"cirfnint;":10768,"cirmid;":10991,"cirscir;":10690,"clubs;":9827,"clubsuit;":9827,"colon;":58,"colone;":8788,"coloneq;":8788,"comma;":44,"commat;":64,"comp;":8705,"compfn;":8728,"complement;":8705,"complexes;":8450,"cong;":8773,"congdot;":10861,"conint;":8750,"copf;":[55349,56660],"coprod;":8720,copy:169,"copy;":169,"copysr;":8471,"crarr;":8629,"cross;":10007,"cscr;":[55349,56504],"csub;":10959,"csube;":10961,"csup;":10960,"csupe;":10962,"ctdot;":8943,"cudarrl;":10552,"cudarrr;":10549,"cuepr;":8926,"cuesc;":8927,"cularr;":8630,"cularrp;":10557,"cup;":8746,"cupbrcap;":10824,"cupcap;":10822,"cupcup;":10826,"cupdot;":8845,"cupor;":10821,"cups;":[8746,65024],"curarr;":8631,"curarrm;":10556,"curlyeqprec;":8926,"curlyeqsucc;":8927,"curlyvee;":8910,"curlywedge;":8911,curren:164,"curren;":164,"curvearrowleft;":8630,"curvearrowright;":8631,"cuvee;":8910,"cuwed;":8911,"cwconint;":8754,"cwint;":8753,"cylcty;":9005,"dArr;":8659,"dHar;":10597,"dagger;":8224,"daleth;":8504,"darr;":8595,"dash;":8208,"dashv;":8867,"dbkarow;":10511,"dblac;":733,"dcaron;":271,"dcy;":1076,"dd;":8518,"ddagger;":8225,"ddarr;":8650,"ddotseq;":10871,deg:176,"deg;":176,"delta;":948,"demptyv;":10673,"dfisht;":10623,"dfr;":[55349,56609],"dharl;":8643,"dharr;":8642,"diam;":8900,"diamond;":8900,"diamondsuit;":9830,"diams;":9830,"die;":168,"digamma;":989,"disin;":8946,"div;":247,divide:247,"divide;":247,"divideontimes;":8903,"divonx;":8903,"djcy;":1106,"dlcorn;":8990,"dlcrop;":8973,"dollar;":36,"dopf;":[55349,56661],"dot;":729,"doteq;":8784,"doteqdot;":8785,"dotminus;":8760,"dotplus;":8724,"dotsquare;":8865,"doublebarwedge;":8966,"downarrow;":8595,"downdownarrows;":8650,"downharpoonleft;":8643,"downharpoonright;":8642,"drbkarow;":10512,"drcorn;":8991,"drcrop;":8972,"dscr;":[55349,56505],"dscy;":1109,"dsol;":10742,"dstrok;":273,"dtdot;":8945,"dtri;":9663,"dtrif;":9662,"duarr;":8693,"duhar;":10607,"dwangle;":10662,"dzcy;":1119,"dzigrarr;":10239,"eDDot;":10871,"eDot;":8785,eacute:233,"eacute;":233,"easter;":10862,"ecaron;":283,"ecir;":8790,ecirc:234,"ecirc;":234,"ecolon;":8789,"ecy;":1101,"edot;":279,"ee;":8519,"efDot;":8786,"efr;":[55349,56610],"eg;":10906,egrave:232,"egrave;":232,"egs;":10902,"egsdot;":10904,"el;":10905,"elinters;":9191,"ell;":8467,"els;":10901,"elsdot;":10903,"emacr;":275,"empty;":8709,"emptyset;":8709,"emptyv;":8709,"emsp13;":8196,"emsp14;":8197,"emsp;":8195,"eng;":331,"ensp;":8194,"eogon;":281,"eopf;":[55349,56662],"epar;":8917,"eparsl;":10723,"eplus;":10865,"epsi;":949,"epsilon;":949,"epsiv;":1013,"eqcirc;":8790,"eqcolon;":8789,"eqsim;":8770,"eqslantgtr;":10902,"eqslantless;":10901,"equals;":61,"equest;":8799,"equiv;":8801,"equivDD;":10872,"eqvparsl;":10725,"erDot;":8787,"erarr;":10609,"escr;":8495,"esdot;":8784,"esim;":8770,"eta;":951,eth:240,"eth;":240,euml:235,"euml;":235,"euro;":8364,"excl;":33,"exist;":8707,"expectation;":8496,"exponentiale;":8519,"fallingdotseq;":8786,"fcy;":1092,"female;":9792,"ffilig;":64259,"fflig;":64256,"ffllig;":64260,"ffr;":[55349,56611],"filig;":64257,"fjlig;":[102,106],"flat;":9837,"fllig;":64258,"fltns;":9649,"fnof;":402,"fopf;":[55349,56663],"forall;":8704,"fork;":8916,"forkv;":10969,"fpartint;":10765,frac12:189,"frac12;":189,"frac13;":8531,frac14:188,"frac14;":188,"frac15;":8533,"frac16;":8537,"frac18;":8539,"frac23;":8532,"frac25;":8534,frac34:190,"frac34;":190,"frac35;":8535,"frac38;":8540,"frac45;":8536,"frac56;":8538,"frac58;":8541,"frac78;":8542,"frasl;":8260,"frown;":8994,"fscr;":[55349,56507],"gE;":8807,"gEl;":10892,"gacute;":501,"gamma;":947,"gammad;":989,"gap;":10886,"gbreve;":287,"gcirc;":285,"gcy;":1075,"gdot;":289,"ge;":8805,"gel;":8923,"geq;":8805,"geqq;":8807,"geqslant;":10878,"ges;":10878,"gescc;":10921,"gesdot;":10880,"gesdoto;":10882,"gesdotol;":10884,"gesl;":[8923,65024],"gesles;":10900,"gfr;":[55349,56612],"gg;":8811,"ggg;":8921,"gimel;":8503,"gjcy;":1107,"gl;":8823,"glE;":10898,"gla;":10917,"glj;":10916,"gnE;":8809,"gnap;":10890,"gnapprox;":10890,"gne;":10888,"gneq;":10888,"gneqq;":8809,"gnsim;":8935,"gopf;":[55349,56664],"grave;":96,"gscr;":8458,"gsim;":8819,"gsime;":10894,"gsiml;":10896,gt:62,"gt;":62,"gtcc;":10919,"gtcir;":10874,"gtdot;":8919,"gtlPar;":10645,"gtquest;":10876,"gtrapprox;":10886,"gtrarr;":10616,"gtrdot;":8919,"gtreqless;":8923,"gtreqqless;":10892,"gtrless;":8823,"gtrsim;":8819,"gvertneqq;":[8809,65024],"gvnE;":[8809,65024],"hArr;":8660,"hairsp;":8202,"half;":189,"hamilt;":8459,"hardcy;":1098,"harr;":8596,"harrcir;":10568,"harrw;":8621,"hbar;":8463,"hcirc;":293,"hearts;":9829,"heartsuit;":9829,"hellip;":8230,"hercon;":8889,"hfr;":[55349,56613],"hksearow;":10533,"hkswarow;":10534,"hoarr;":8703,"homtht;":8763,"hookleftarrow;":8617,"hookrightarrow;":8618,"hopf;":[55349,56665],"horbar;":8213,"hscr;":[55349,56509],"hslash;":8463,"hstrok;":295,"hybull;":8259,"hyphen;":8208,iacute:237,"iacute;":237,"ic;":8291,icirc:238,"icirc;":238,"icy;":1080,"iecy;":1077,iexcl:161,"iexcl;":161,"iff;":8660,"ifr;":[55349,56614],igrave:236,"igrave;":236,"ii;":8520,"iiiint;":10764,"iiint;":8749,"iinfin;":10716,"iiota;":8489,"ijlig;":307,"imacr;":299,"image;":8465,"imagline;":8464,"imagpart;":8465,"imath;":305,"imof;":8887,"imped;":437,"in;":8712,"incare;":8453,"infin;":8734,"infintie;":10717,"inodot;":305,"int;":8747,"intcal;":8890,"integers;":8484,"intercal;":8890,"intlarhk;":10775,"intprod;":10812,"iocy;":1105,"iogon;":303,"iopf;":[55349,56666],"iota;":953,"iprod;":10812,iquest:191,"iquest;":191,"iscr;":[55349,56510],"isin;":8712,"isinE;":8953,"isindot;":8949,"isins;":8948,"isinsv;":8947,"isinv;":8712,"it;":8290,"itilde;":297,"iukcy;":1110,iuml:239,"iuml;":239,"jcirc;":309,"jcy;":1081,"jfr;":[55349,56615],"jmath;":567,"jopf;":[55349,56667],"jscr;":[55349,56511],"jsercy;":1112,"jukcy;":1108,"kappa;":954,"kappav;":1008,"kcedil;":311,"kcy;":1082,"kfr;":[55349,56616],"kgreen;":312,"khcy;":1093,"kjcy;":1116,"kopf;":[55349,56668],"kscr;":[55349,56512],"lAarr;":8666,"lArr;":8656,"lAtail;":10523,"lBarr;":10510,"lE;":8806,"lEg;":10891,"lHar;":10594,"lacute;":314,"laemptyv;":10676,"lagran;":8466,"lambda;":955,"lang;":10216,"langd;":10641,"langle;":10216,"lap;":10885,laquo:171,"laquo;":171,"larr;":8592,"larrb;":8676,"larrbfs;":10527,"larrfs;":10525,"larrhk;":8617,"larrlp;":8619,"larrpl;":10553,"larrsim;":10611,"larrtl;":8610,"lat;":10923,"latail;":10521,"late;":10925,"lates;":[10925,65024],"lbarr;":10508,"lbbrk;":10098,"lbrace;":123,"lbrack;":91,"lbrke;":10635,"lbrksld;":10639,"lbrkslu;":10637,"lcaron;":318,"lcedil;":316,"lceil;":8968,"lcub;":123,"lcy;":1083,"ldca;":10550,"ldquo;":8220,"ldquor;":8222,"ldrdhar;":10599,"ldrushar;":10571,"ldsh;":8626,"le;":8804,"leftarrow;":8592,"leftarrowtail;":8610,"leftharpoondown;":8637,"leftharpoonup;":8636,"leftleftarrows;":8647,"leftrightarrow;":8596,"leftrightarrows;":8646,"leftrightharpoons;":8651,"leftrightsquigarrow;":8621,"leftthreetimes;":8907,"leg;":8922,"leq;":8804,"leqq;":8806,"leqslant;":10877,"les;":10877,"lescc;":10920,"lesdot;":10879,"lesdoto;":10881,"lesdotor;":10883,"lesg;":[8922,65024],"lesges;":10899,"lessapprox;":10885,"lessdot;":8918,"lesseqgtr;":8922,"lesseqqgtr;":10891,"lessgtr;":8822,"lesssim;":8818,"lfisht;":10620,"lfloor;":8970,"lfr;":[55349,56617],"lg;":8822,"lgE;":10897,"lhard;":8637,"lharu;":8636,"lharul;":10602,"lhblk;":9604,"ljcy;":1113,"ll;":8810,"llarr;":8647,"llcorner;":8990,"llhard;":10603,"lltri;":9722,"lmidot;":320,"lmoust;":9136,"lmoustache;":9136,"lnE;":8808,"lnap;":10889,"lnapprox;":10889,"lne;":10887,"lneq;":10887,"lneqq;":8808,"lnsim;":8934,"loang;":10220,"loarr;":8701,"lobrk;":10214,"longleftarrow;":10229,"longleftrightarrow;":10231,"longmapsto;":10236,"longrightarrow;":10230,"looparrowleft;":8619,"looparrowright;":8620,"lopar;":10629,"lopf;":[55349,56669],"loplus;":10797,"lotimes;":10804,"lowast;":8727,"lowbar;":95,"loz;":9674,"lozenge;":9674,"lozf;":10731,"lpar;":40,"lparlt;":10643,"lrarr;":8646,"lrcorner;":8991,"lrhar;":8651,"lrhard;":10605,"lrm;":8206,"lrtri;":8895,"lsaquo;":8249,"lscr;":[55349,56513],"lsh;":8624,"lsim;":8818,"lsime;":10893,"lsimg;":10895,"lsqb;":91,"lsquo;":8216,"lsquor;":8218,"lstrok;":322,lt:60,"lt;":60,"ltcc;":10918,"ltcir;":10873,"ltdot;":8918,"lthree;":8907,"ltimes;":8905,"ltlarr;":10614,"ltquest;":10875,"ltrPar;":10646,"ltri;":9667,"ltrie;":8884,"ltrif;":9666,"lurdshar;":10570,"luruhar;":10598,"lvertneqq;":[8808,65024],"lvnE;":[8808,65024],"mDDot;":8762,macr:175,"macr;":175,"male;":9794,"malt;":10016,"maltese;":10016,"map;":8614,"mapsto;":8614,"mapstodown;":8615,"mapstoleft;":8612,"mapstoup;":8613,"marker;":9646,"mcomma;":10793,"mcy;":1084,"mdash;":8212,"measuredangle;":8737,"mfr;":[55349,56618],"mho;":8487,micro:181,"micro;":181,"mid;":8739,"midast;":42,"midcir;":10992,middot:183,"middot;":183,"minus;":8722,"minusb;":8863,"minusd;":8760,"minusdu;":10794,"mlcp;":10971,"mldr;":8230,"mnplus;":8723,"models;":8871,"mopf;":[55349,56670],"mp;":8723,"mscr;":[55349,56514],"mstpos;":8766,"mu;":956,"multimap;":8888,"mumap;":8888,"nGg;":[8921,824],"nGt;":[8811,8402],"nGtv;":[8811,824],"nLeftarrow;":8653,"nLeftrightarrow;":8654,"nLl;":[8920,824],"nLt;":[8810,8402],"nLtv;":[8810,824],"nRightarrow;":8655,"nVDash;":8879,"nVdash;":8878,"nabla;":8711,"nacute;":324,"nang;":[8736,8402],"nap;":8777,"napE;":[10864,824],"napid;":[8779,824],"napos;":329,"napprox;":8777,"natur;":9838,"natural;":9838,"naturals;":8469,nbsp:160,"nbsp;":160,"nbump;":[8782,824],"nbumpe;":[8783,824],"ncap;":10819,"ncaron;":328,"ncedil;":326,"ncong;":8775,"ncongdot;":[10861,824],"ncup;":10818,"ncy;":1085,"ndash;":8211,"ne;":8800,"neArr;":8663,"nearhk;":10532,"nearr;":8599,"nearrow;":8599,"nedot;":[8784,824],"nequiv;":8802,"nesear;":10536,"nesim;":[8770,824],"nexist;":8708,"nexists;":8708,"nfr;":[55349,56619],"ngE;":[8807,824],"nge;":8817,"ngeq;":8817,"ngeqq;":[8807,824],"ngeqslant;":[10878,824],"nges;":[10878,824],"ngsim;":8821,"ngt;":8815,"ngtr;":8815,"nhArr;":8654,"nharr;":8622,"nhpar;":10994,"ni;":8715,"nis;":8956,"nisd;":8954,"niv;":8715,"njcy;":1114,"nlArr;":8653,"nlE;":[8806,824],"nlarr;":8602,"nldr;":8229,"nle;":8816,"nleftarrow;":8602,"nleftrightarrow;":8622,"nleq;":8816,"nleqq;":[8806,824],"nleqslant;":[10877,824],"nles;":[10877,824],"nless;":8814,"nlsim;":8820,"nlt;":8814,"nltri;":8938,"nltrie;":8940,"nmid;":8740,"nopf;":[55349,56671],not:172,"not;":172,"notin;":8713,"notinE;":[8953,824],"notindot;":[8949,824],"notinva;":8713,"notinvb;":8951,"notinvc;":8950,"notni;":8716,"notniva;":8716,"notnivb;":8958,"notnivc;":8957,"npar;":8742,"nparallel;":8742,"nparsl;":[11005,8421],"npart;":[8706,824],"npolint;":10772,"npr;":8832,"nprcue;":8928,"npre;":[10927,824],"nprec;":8832,"npreceq;":[10927,824],"nrArr;":8655,"nrarr;":8603,"nrarrc;":[10547,824],"nrarrw;":[8605,824],"nrightarrow;":8603,"nrtri;":8939,"nrtrie;":8941,"nsc;":8833,"nsccue;":8929,"nsce;":[10928,824],"nscr;":[55349,56515],"nshortmid;":8740,"nshortparallel;":8742,"nsim;":8769,"nsime;":8772,"nsimeq;":8772,"nsmid;":8740,"nspar;":8742,"nsqsube;":8930,"nsqsupe;":8931,"nsub;":8836,"nsubE;":[10949,824],"nsube;":8840,"nsubset;":[8834,8402],"nsubseteq;":8840,"nsubseteqq;":[10949,824],"nsucc;":8833,"nsucceq;":[10928,824],"nsup;":8837,"nsupE;":[10950,824],"nsupe;":8841,"nsupset;":[8835,8402],"nsupseteq;":8841,"nsupseteqq;":[10950,824],"ntgl;":8825,ntilde:241,"ntilde;":241,"ntlg;":8824,"ntriangleleft;":8938,"ntrianglelefteq;":8940,"ntriangleright;":8939,"ntrianglerighteq;":8941,"nu;":957,"num;":35,"numero;":8470,"numsp;":8199,"nvDash;":8877,"nvHarr;":10500,"nvap;":[8781,8402],"nvdash;":8876,"nvge;":[8805,8402],"nvgt;":[62,8402],"nvinfin;":10718,"nvlArr;":10498,"nvle;":[8804,8402],"nvlt;":[60,8402],"nvltrie;":[8884,8402],"nvrArr;":10499,"nvrtrie;":[8885,8402],"nvsim;":[8764,8402],"nwArr;":8662,"nwarhk;":10531,"nwarr;":8598,"nwarrow;":8598,"nwnear;":10535,"oS;":9416,oacute:243,"oacute;":243,"oast;":8859,"ocir;":8858,ocirc:244,"ocirc;":244,"ocy;":1086,"odash;":8861,"odblac;":337,"odiv;":10808,"odot;":8857,"odsold;":10684,"oelig;":339,"ofcir;":10687,"ofr;":[55349,56620],"ogon;":731,ograve:242,"ograve;":242,"ogt;":10689,"ohbar;":10677,"ohm;":937,"oint;":8750,"olarr;":8634,"olcir;":10686,"olcross;":10683,"oline;":8254,"olt;":10688,"omacr;":333,"omega;":969,"omicron;":959,"omid;":10678,"ominus;":8854,"oopf;":[55349,56672],"opar;":10679,"operp;":10681,"oplus;":8853,"or;":8744,"orarr;":8635,"ord;":10845,"order;":8500,"orderof;":8500,ordf:170,"ordf;":170,ordm:186,"ordm;":186,"origof;":8886,"oror;":10838,"orslope;":10839,"orv;":10843,"oscr;":8500,oslash:248,"oslash;":248,"osol;":8856,otilde:245,"otilde;":245,"otimes;":8855,"otimesas;":10806,ouml:246,"ouml;":246,"ovbar;":9021,"par;":8741,para:182,"para;":182,"parallel;":8741,"parsim;":10995,"parsl;":11005,"part;":8706,"pcy;":1087,"percnt;":37,"period;":46,"permil;":8240,"perp;":8869,"pertenk;":8241,"pfr;":[55349,56621],"phi;":966,"phiv;":981,"phmmat;":8499,"phone;":9742,"pi;":960,"pitchfork;":8916,"piv;":982,"planck;":8463,"planckh;":8462,"plankv;":8463,"plus;":43,"plusacir;":10787,"plusb;":8862,"pluscir;":10786,"plusdo;":8724,"plusdu;":10789,"pluse;":10866,plusmn:177,"plusmn;":177,"plussim;":10790,"plustwo;":10791,"pm;":177,"pointint;":10773,"popf;":[55349,56673],pound:163,"pound;":163,"pr;":8826,"prE;":10931,"prap;":10935,"prcue;":8828,"pre;":10927,"prec;":8826,"precapprox;":10935,"preccurlyeq;":8828,"preceq;":10927,"precnapprox;":10937,"precneqq;":10933,"precnsim;":8936,"precsim;":8830,"prime;":8242,"primes;":8473,"prnE;":10933,"prnap;":10937,"prnsim;":8936,"prod;":8719,"profalar;":9006,"profline;":8978,"profsurf;":8979,"prop;":8733,"propto;":8733,"prsim;":8830,"prurel;":8880,"pscr;":[55349,56517],"psi;":968,"puncsp;":8200,"qfr;":[55349,56622],"qint;":10764,"qopf;":[55349,56674],"qprime;":8279,"qscr;":[55349,56518],"quaternions;":8461,"quatint;":10774,"quest;":63,"questeq;":8799,quot:34,"quot;":34,"rAarr;":8667,"rArr;":8658,"rAtail;":10524,"rBarr;":10511,"rHar;":10596,"race;":[8765,817],"racute;":341,"radic;":8730,"raemptyv;":10675,"rang;":10217,"rangd;":10642,"range;":10661,"rangle;":10217,raquo:187,"raquo;":187,"rarr;":8594,"rarrap;":10613,"rarrb;":8677,"rarrbfs;":10528,"rarrc;":10547,"rarrfs;":10526,"rarrhk;":8618,"rarrlp;":8620,"rarrpl;":10565,"rarrsim;":10612,"rarrtl;":8611,"rarrw;":8605,"ratail;":10522,"ratio;":8758,"rationals;":8474,"rbarr;":10509,"rbbrk;":10099,"rbrace;":125,"rbrack;":93,"rbrke;":10636,"rbrksld;":10638,"rbrkslu;":10640,"rcaron;":345,"rcedil;":343,"rceil;":8969,"rcub;":125,"rcy;":1088,"rdca;":10551,"rdldhar;":10601,"rdquo;":8221,"rdquor;":8221,"rdsh;":8627,"real;":8476,"realine;":8475,"realpart;":8476,"reals;":8477,"rect;":9645,reg:174,"reg;":174,"rfisht;":10621,"rfloor;":8971,"rfr;":[55349,56623],"rhard;":8641,"rharu;":8640,"rharul;":10604,"rho;":961,"rhov;":1009,"rightarrow;":8594,"rightarrowtail;":8611,"rightharpoondown;":8641,"rightharpoonup;":8640,"rightleftarrows;":8644,"rightleftharpoons;":8652,"rightrightarrows;":8649,"rightsquigarrow;":8605,"rightthreetimes;":8908,"ring;":730,"risingdotseq;":8787,"rlarr;":8644,"rlhar;":8652,"rlm;":8207,"rmoust;":9137,"rmoustache;":9137,"rnmid;":10990,"roang;":10221,"roarr;":8702,"robrk;":10215,"ropar;":10630,"ropf;":[55349,56675],"roplus;":10798,"rotimes;":10805,"rpar;":41,"rpargt;":10644,"rppolint;":10770,"rrarr;":8649,"rsaquo;":8250,"rscr;":[55349,56519],"rsh;":8625,"rsqb;":93,"rsquo;":8217,"rsquor;":8217,"rthree;":8908,"rtimes;":8906,"rtri;":9657,"rtrie;":8885,"rtrif;":9656,"rtriltri;":10702,"ruluhar;":10600,"rx;":8478,"sacute;":347,"sbquo;":8218,"sc;":8827,"scE;":10932,"scap;":10936,"scaron;":353,"sccue;":8829,"sce;":10928,"scedil;":351,"scirc;":349,"scnE;":10934,"scnap;":10938,"scnsim;":8937,"scpolint;":10771,"scsim;":8831,"scy;":1089,"sdot;":8901,"sdotb;":8865,"sdote;":10854,"seArr;":8664,"searhk;":10533,"searr;":8600,"searrow;":8600,sect:167,"sect;":167,"semi;":59,"seswar;":10537,"setminus;":8726,"setmn;":8726,"sext;":10038,"sfr;":[55349,56624],"sfrown;":8994,"sharp;":9839,"shchcy;":1097,"shcy;":1096,"shortmid;":8739,"shortparallel;":8741,shy:173,"shy;":173,"sigma;":963,"sigmaf;":962,"sigmav;":962,"sim;":8764,"simdot;":10858,"sime;":8771,"simeq;":8771,"simg;":10910,"simgE;":10912,"siml;":10909,"simlE;":10911,"simne;":8774,"simplus;":10788,"simrarr;":10610,"slarr;":8592,"smallsetminus;":8726,"smashp;":10803,"smeparsl;":10724,"smid;":8739,"smile;":8995,"smt;":10922,"smte;":10924,"smtes;":[10924,65024],"softcy;":1100,"sol;":47,"solb;":10692,"solbar;":9023,"sopf;":[55349,56676],"spades;":9824,"spadesuit;":9824,"spar;":8741,"sqcap;":8851,"sqcaps;":[8851,65024],"sqcup;":8852,"sqcups;":[8852,65024],"sqsub;":8847,"sqsube;":8849,"sqsubset;":8847,"sqsubseteq;":8849,"sqsup;":8848,"sqsupe;":8850,"sqsupset;":8848,"sqsupseteq;":8850,"squ;":9633,"square;":9633,"squarf;":9642,"squf;":9642,"srarr;":8594,"sscr;":[55349,56520],"ssetmn;":8726,"ssmile;":8995,"sstarf;":8902,"star;":9734,"starf;":9733,"straightepsilon;":1013,"straightphi;":981,"strns;":175,"sub;":8834,"subE;":10949,"subdot;":10941,"sube;":8838,"subedot;":10947,"submult;":10945,"subnE;":10955,"subne;":8842,"subplus;":10943,"subrarr;":10617,"subset;":8834,"subseteq;":8838,"subseteqq;":10949,"subsetneq;":8842,"subsetneqq;":10955,"subsim;":10951,"subsub;":10965,"subsup;":10963,"succ;":8827,"succapprox;":10936,"succcurlyeq;":8829,"succeq;":10928,"succnapprox;":10938,"succneqq;":10934,"succnsim;":8937,"succsim;":8831,"sum;":8721,"sung;":9834,sup1:185,"sup1;":185,sup2:178,"sup2;":178,sup3:179,"sup3;":179,"sup;":8835,"supE;":10950,"supdot;":10942,"supdsub;":10968,"supe;":8839,"supedot;":10948,"suphsol;":10185,"suphsub;":10967,"suplarr;":10619,"supmult;":10946,"supnE;":10956,"supne;":8843,"supplus;":10944,"supset;":8835,"supseteq;":8839,"supseteqq;":10950,"supsetneq;":8843,"supsetneqq;":10956,"supsim;":10952,"supsub;":10964,"supsup;":10966,"swArr;":8665,"swarhk;":10534,"swarr;":8601,"swarrow;":8601,"swnwar;":10538,szlig:223,"szlig;":223,"target;":8982,"tau;":964,"tbrk;":9140,"tcaron;":357,"tcedil;":355,"tcy;":1090,"tdot;":8411,"telrec;":8981,"tfr;":[55349,56625],"there4;":8756,"therefore;":8756,"theta;":952,"thetasym;":977,"thetav;":977,"thickapprox;":8776,"thicksim;":8764,"thinsp;":8201,"thkap;":8776,"thksim;":8764,thorn:254,"thorn;":254,"tilde;":732,times:215,"times;":215,"timesb;":8864,"timesbar;":10801,"timesd;":10800,"tint;":8749,"toea;":10536,"top;":8868,"topbot;":9014,"topcir;":10993,"topf;":[55349,56677],"topfork;":10970,"tosa;":10537,"tprime;":8244,"trade;":8482,"triangle;":9653,"triangledown;":9663,"triangleleft;":9667,"trianglelefteq;":8884,"triangleq;":8796,"triangleright;":9657,"trianglerighteq;":8885,"tridot;":9708,"trie;":8796,"triminus;":10810,"triplus;":10809,"trisb;":10701,"tritime;":10811,"trpezium;":9186,"tscr;":[55349,56521],"tscy;":1094,"tshcy;":1115,"tstrok;":359,"twixt;":8812,"twoheadleftarrow;":8606,"twoheadrightarrow;":8608,"uArr;":8657,"uHar;":10595,uacute:250,"uacute;":250,"uarr;":8593,"ubrcy;":1118,"ubreve;":365,ucirc:251,"ucirc;":251,"ucy;":1091,"udarr;":8645,"udblac;":369,"udhar;":10606,"ufisht;":10622,"ufr;":[55349,56626],ugrave:249,"ugrave;":249,"uharl;":8639,"uharr;":8638,"uhblk;":9600,"ulcorn;":8988,"ulcorner;":8988,"ulcrop;":8975,"ultri;":9720,"umacr;":363,uml:168,"uml;":168,"uogon;":371,"uopf;":[55349,56678],"uparrow;":8593,"updownarrow;":8597,"upharpoonleft;":8639,"upharpoonright;":8638,"uplus;":8846,"upsi;":965,"upsih;":978,"upsilon;":965,"upuparrows;":8648,"urcorn;":8989,"urcorner;":8989,"urcrop;":8974,"uring;":367,"urtri;":9721,"uscr;":[55349,56522],"utdot;":8944,"utilde;":361,"utri;":9653,"utrif;":9652,"uuarr;":8648,uuml:252,"uuml;":252,"uwangle;":10663,"vArr;":8661,"vBar;":10984,"vBarv;":10985,"vDash;":8872,"vangrt;":10652,"varepsilon;":1013,"varkappa;":1008,"varnothing;":8709,"varphi;":981,"varpi;":982,"varpropto;":8733,"varr;":8597,"varrho;":1009,"varsigma;":962,"varsubsetneq;":[8842,65024],"varsubsetneqq;":[10955,65024],"varsupsetneq;":[8843,65024],"varsupsetneqq;":[10956,65024],"vartheta;":977,"vartriangleleft;":8882,"vartriangleright;":8883,"vcy;":1074,"vdash;":8866,"vee;":8744,"veebar;":8891,"veeeq;":8794,"vellip;":8942,"verbar;":124,"vert;":124,"vfr;":[55349,56627],"vltri;":8882,"vnsub;":[8834,8402],"vnsup;":[8835,8402],"vopf;":[55349,56679],"vprop;":8733,"vrtri;":8883,"vscr;":[55349,56523],"vsubnE;":[10955,65024],"vsubne;":[8842,65024],"vsupnE;":[10956,65024],"vsupne;":[8843,65024],"vzigzag;":10650,"wcirc;":373,"wedbar;":10847,"wedge;":8743,"wedgeq;":8793,"weierp;":8472,"wfr;":[55349,56628],"wopf;":[55349,56680],"wp;":8472,"wr;":8768,"wreath;":8768,"wscr;":[55349,56524],"xcap;":8898,"xcirc;":9711,"xcup;":8899,"xdtri;":9661,"xfr;":[55349,56629],"xhArr;":10234,"xharr;":10231,"xi;":958,"xlArr;":10232,"xlarr;":10229,"xmap;":10236,"xnis;":8955,"xodot;":10752,"xopf;":[55349,56681],"xoplus;":10753,"xotime;":10754,"xrArr;":10233,"xrarr;":10230,"xscr;":[55349,56525],"xsqcup;":10758,"xuplus;":10756,"xutri;":9651,"xvee;":8897,"xwedge;":8896,yacute:253,"yacute;":253,"yacy;":1103,"ycirc;":375,"ycy;":1099,yen:165,"yen;":165,"yfr;":[55349,56630],"yicy;":1111,"yopf;":[55349,56682],"yscr;":[55349,56526],"yucy;":1102,yuml:255,"yuml;":255,"zacute;":378,"zcaron;":382,"zcy;":1079,"zdot;":380,"zeetrf;":8488,"zeta;":950,"zfr;":[55349,56631],"zhcy;":1078,"zigrarr;":8669,"zopf;":[55349,56683],"zscr;":[55349,56527],"zwj;":8205,"zwnj;":8204},rt=/(A(?:Elig;?|MP;?|acute;?|breve;|c(?:irc;?|y;)|fr;|grave;?|lpha;|macr;|nd;|o(?:gon;|pf;)|pplyFunction;|ring;?|s(?:cr;|sign;)|tilde;?|uml;?)|B(?:a(?:ckslash;|r(?:v;|wed;))|cy;|e(?:cause;|rnoullis;|ta;)|fr;|opf;|reve;|scr;|umpeq;)|C(?:Hcy;|OPY;?|a(?:cute;|p(?:;|italDifferentialD;)|yleys;)|c(?:aron;|edil;?|irc;|onint;)|dot;|e(?:dilla;|nterDot;)|fr;|hi;|ircle(?:Dot;|Minus;|Plus;|Times;)|lo(?:ckwiseContourIntegral;|seCurly(?:DoubleQuote;|Quote;))|o(?:lon(?:;|e;)|n(?:gruent;|int;|tourIntegral;)|p(?:f;|roduct;)|unterClockwiseContourIntegral;)|ross;|scr;|up(?:;|Cap;))|D(?:D(?:;|otrahd;)|Jcy;|Scy;|Zcy;|a(?:gger;|rr;|shv;)|c(?:aron;|y;)|el(?:;|ta;)|fr;|i(?:a(?:critical(?:Acute;|Do(?:t;|ubleAcute;)|Grave;|Tilde;)|mond;)|fferentialD;)|o(?:pf;|t(?:;|Dot;|Equal;)|uble(?:ContourIntegral;|Do(?:t;|wnArrow;)|L(?:eft(?:Arrow;|RightArrow;|Tee;)|ong(?:Left(?:Arrow;|RightArrow;)|RightArrow;))|Right(?:Arrow;|Tee;)|Up(?:Arrow;|DownArrow;)|VerticalBar;)|wn(?:Arrow(?:;|Bar;|UpArrow;)|Breve;|Left(?:RightVector;|TeeVector;|Vector(?:;|Bar;))|Right(?:TeeVector;|Vector(?:;|Bar;))|Tee(?:;|Arrow;)|arrow;))|s(?:cr;|trok;))|E(?:NG;|TH;?|acute;?|c(?:aron;|irc;?|y;)|dot;|fr;|grave;?|lement;|m(?:acr;|pty(?:SmallSquare;|VerySmallSquare;))|o(?:gon;|pf;)|psilon;|qu(?:al(?:;|Tilde;)|ilibrium;)|s(?:cr;|im;)|ta;|uml;?|x(?:ists;|ponentialE;))|F(?:cy;|fr;|illed(?:SmallSquare;|VerySmallSquare;)|o(?:pf;|rAll;|uriertrf;)|scr;)|G(?:Jcy;|T;?|amma(?:;|d;)|breve;|c(?:edil;|irc;|y;)|dot;|fr;|g;|opf;|reater(?:Equal(?:;|Less;)|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|scr;|t;)|H(?:ARDcy;|a(?:cek;|t;)|circ;|fr;|ilbertSpace;|o(?:pf;|rizontalLine;)|s(?:cr;|trok;)|ump(?:DownHump;|Equal;))|I(?:Ecy;|Jlig;|Ocy;|acute;?|c(?:irc;?|y;)|dot;|fr;|grave;?|m(?:;|a(?:cr;|ginaryI;)|plies;)|n(?:t(?:;|e(?:gral;|rsection;))|visible(?:Comma;|Times;))|o(?:gon;|pf;|ta;)|scr;|tilde;|u(?:kcy;|ml;?))|J(?:c(?:irc;|y;)|fr;|opf;|s(?:cr;|ercy;)|ukcy;)|K(?:Hcy;|Jcy;|appa;|c(?:edil;|y;)|fr;|opf;|scr;)|L(?:Jcy;|T;?|a(?:cute;|mbda;|ng;|placetrf;|rr;)|c(?:aron;|edil;|y;)|e(?:ft(?:A(?:ngleBracket;|rrow(?:;|Bar;|RightArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|Right(?:Arrow;|Vector;)|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;|rightarrow;)|ss(?:EqualGreater;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;))|fr;|l(?:;|eftarrow;)|midot;|o(?:ng(?:Left(?:Arrow;|RightArrow;)|RightArrow;|left(?:arrow;|rightarrow;)|rightarrow;)|pf;|wer(?:LeftArrow;|RightArrow;))|s(?:cr;|h;|trok;)|t;)|M(?:ap;|cy;|e(?:diumSpace;|llintrf;)|fr;|inusPlus;|opf;|scr;|u;)|N(?:Jcy;|acute;|c(?:aron;|edil;|y;)|e(?:gative(?:MediumSpace;|Thi(?:ckSpace;|nSpace;)|VeryThinSpace;)|sted(?:GreaterGreater;|LessLess;)|wLine;)|fr;|o(?:Break;|nBreakingSpace;|pf;|t(?:;|C(?:ongruent;|upCap;)|DoubleVerticalBar;|E(?:lement;|qual(?:;|Tilde;)|xists;)|Greater(?:;|Equal;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|Hump(?:DownHump;|Equal;)|Le(?:ftTriangle(?:;|Bar;|Equal;)|ss(?:;|Equal;|Greater;|Less;|SlantEqual;|Tilde;))|Nested(?:GreaterGreater;|LessLess;)|Precedes(?:;|Equal;|SlantEqual;)|R(?:everseElement;|ightTriangle(?:;|Bar;|Equal;))|S(?:quareSu(?:bset(?:;|Equal;)|perset(?:;|Equal;))|u(?:bset(?:;|Equal;)|cceeds(?:;|Equal;|SlantEqual;|Tilde;)|perset(?:;|Equal;)))|Tilde(?:;|Equal;|FullEqual;|Tilde;)|VerticalBar;))|scr;|tilde;?|u;)|O(?:Elig;|acute;?|c(?:irc;?|y;)|dblac;|fr;|grave;?|m(?:acr;|ega;|icron;)|opf;|penCurly(?:DoubleQuote;|Quote;)|r;|s(?:cr;|lash;?)|ti(?:lde;?|mes;)|uml;?|ver(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;))|P(?:artialD;|cy;|fr;|hi;|i;|lusMinus;|o(?:incareplane;|pf;)|r(?:;|ecedes(?:;|Equal;|SlantEqual;|Tilde;)|ime;|o(?:duct;|portion(?:;|al;)))|s(?:cr;|i;))|Q(?:UOT;?|fr;|opf;|scr;)|R(?:Barr;|EG;?|a(?:cute;|ng;|rr(?:;|tl;))|c(?:aron;|edil;|y;)|e(?:;|verse(?:E(?:lement;|quilibrium;)|UpEquilibrium;))|fr;|ho;|ight(?:A(?:ngleBracket;|rrow(?:;|Bar;|LeftArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;)|o(?:pf;|undImplies;)|rightarrow;|s(?:cr;|h;)|uleDelayed;)|S(?:H(?:CHcy;|cy;)|OFTcy;|acute;|c(?:;|aron;|edil;|irc;|y;)|fr;|hort(?:DownArrow;|LeftArrow;|RightArrow;|UpArrow;)|igma;|mallCircle;|opf;|q(?:rt;|uare(?:;|Intersection;|Su(?:bset(?:;|Equal;)|perset(?:;|Equal;))|Union;))|scr;|tar;|u(?:b(?:;|set(?:;|Equal;))|c(?:ceeds(?:;|Equal;|SlantEqual;|Tilde;)|hThat;)|m;|p(?:;|erset(?:;|Equal;)|set;)))|T(?:HORN;?|RADE;|S(?:Hcy;|cy;)|a(?:b;|u;)|c(?:aron;|edil;|y;)|fr;|h(?:e(?:refore;|ta;)|i(?:ckSpace;|nSpace;))|ilde(?:;|Equal;|FullEqual;|Tilde;)|opf;|ripleDot;|s(?:cr;|trok;))|U(?:a(?:cute;?|rr(?:;|ocir;))|br(?:cy;|eve;)|c(?:irc;?|y;)|dblac;|fr;|grave;?|macr;|n(?:der(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;)|ion(?:;|Plus;))|o(?:gon;|pf;)|p(?:Arrow(?:;|Bar;|DownArrow;)|DownArrow;|Equilibrium;|Tee(?:;|Arrow;)|arrow;|downarrow;|per(?:LeftArrow;|RightArrow;)|si(?:;|lon;))|ring;|scr;|tilde;|uml;?)|V(?:Dash;|bar;|cy;|dash(?:;|l;)|e(?:e;|r(?:bar;|t(?:;|ical(?:Bar;|Line;|Separator;|Tilde;))|yThinSpace;))|fr;|opf;|scr;|vdash;)|W(?:circ;|edge;|fr;|opf;|scr;)|X(?:fr;|i;|opf;|scr;)|Y(?:Acy;|Icy;|Ucy;|acute;?|c(?:irc;|y;)|fr;|opf;|scr;|uml;)|Z(?:Hcy;|acute;|c(?:aron;|y;)|dot;|e(?:roWidthSpace;|ta;)|fr;|opf;|scr;)|a(?:acute;?|breve;|c(?:;|E;|d;|irc;?|ute;?|y;)|elig;?|f(?:;|r;)|grave;?|l(?:e(?:fsym;|ph;)|pha;)|m(?:a(?:cr;|lg;)|p;?)|n(?:d(?:;|and;|d;|slope;|v;)|g(?:;|e;|le;|msd(?:;|a(?:a;|b;|c;|d;|e;|f;|g;|h;))|rt(?:;|vb(?:;|d;))|s(?:ph;|t;)|zarr;))|o(?:gon;|pf;)|p(?:;|E;|acir;|e;|id;|os;|prox(?:;|eq;))|ring;?|s(?:cr;|t;|ymp(?:;|eq;))|tilde;?|uml;?|w(?:conint;|int;))|b(?:Not;|a(?:ck(?:cong;|epsilon;|prime;|sim(?:;|eq;))|r(?:vee;|wed(?:;|ge;)))|brk(?:;|tbrk;)|c(?:ong;|y;)|dquo;|e(?:caus(?:;|e;)|mptyv;|psi;|rnou;|t(?:a;|h;|ween;))|fr;|ig(?:c(?:ap;|irc;|up;)|o(?:dot;|plus;|times;)|s(?:qcup;|tar;)|triangle(?:down;|up;)|uplus;|vee;|wedge;)|karow;|l(?:a(?:ck(?:lozenge;|square;|triangle(?:;|down;|left;|right;))|nk;)|k(?:1(?:2;|4;)|34;)|ock;)|n(?:e(?:;|quiv;)|ot;)|o(?:pf;|t(?:;|tom;)|wtie;|x(?:D(?:L;|R;|l;|r;)|H(?:;|D;|U;|d;|u;)|U(?:L;|R;|l;|r;)|V(?:;|H;|L;|R;|h;|l;|r;)|box;|d(?:L;|R;|l;|r;)|h(?:;|D;|U;|d;|u;)|minus;|plus;|times;|u(?:L;|R;|l;|r;)|v(?:;|H;|L;|R;|h;|l;|r;)))|prime;|r(?:eve;|vbar;?)|s(?:cr;|emi;|im(?:;|e;)|ol(?:;|b;|hsub;))|u(?:ll(?:;|et;)|mp(?:;|E;|e(?:;|q;))))|c(?:a(?:cute;|p(?:;|and;|brcup;|c(?:ap;|up;)|dot;|s;)|r(?:et;|on;))|c(?:a(?:ps;|ron;)|edil;?|irc;|ups(?:;|sm;))|dot;|e(?:dil;?|mptyv;|nt(?:;|erdot;|))|fr;|h(?:cy;|eck(?:;|mark;)|i;)|ir(?:;|E;|c(?:;|eq;|le(?:arrow(?:left;|right;)|d(?:R;|S;|ast;|circ;|dash;)))|e;|fnint;|mid;|scir;)|lubs(?:;|uit;)|o(?:lon(?:;|e(?:;|q;))|m(?:ma(?:;|t;)|p(?:;|fn;|le(?:ment;|xes;)))|n(?:g(?:;|dot;)|int;)|p(?:f;|rod;|y(?:;|sr;|)))|r(?:arr;|oss;)|s(?:cr;|u(?:b(?:;|e;)|p(?:;|e;)))|tdot;|u(?:darr(?:l;|r;)|e(?:pr;|sc;)|larr(?:;|p;)|p(?:;|brcap;|c(?:ap;|up;)|dot;|or;|s;)|r(?:arr(?:;|m;)|ly(?:eq(?:prec;|succ;)|vee;|wedge;)|ren;?|vearrow(?:left;|right;))|vee;|wed;)|w(?:conint;|int;)|ylcty;)|d(?:Arr;|Har;|a(?:gger;|leth;|rr;|sh(?:;|v;))|b(?:karow;|lac;)|c(?:aron;|y;)|d(?:;|a(?:gger;|rr;)|otseq;)|e(?:g;?|lta;|mptyv;)|f(?:isht;|r;)|har(?:l;|r;)|i(?:am(?:;|ond(?:;|suit;)|s;)|e;|gamma;|sin;|v(?:;|ide(?:;|ontimes;|)|onx;))|jcy;|lc(?:orn;|rop;)|o(?:llar;|pf;|t(?:;|eq(?:;|dot;)|minus;|plus;|square;)|ublebarwedge;|wn(?:arrow;|downarrows;|harpoon(?:left;|right;)))|r(?:bkarow;|c(?:orn;|rop;))|s(?:c(?:r;|y;)|ol;|trok;)|t(?:dot;|ri(?:;|f;))|u(?:arr;|har;)|wangle;|z(?:cy;|igrarr;))|e(?:D(?:Dot;|ot;)|a(?:cute;?|ster;)|c(?:aron;|ir(?:;|c;?)|olon;|y;)|dot;|e;|f(?:Dot;|r;)|g(?:;|rave;?|s(?:;|dot;))|l(?:;|inters;|l;|s(?:;|dot;))|m(?:acr;|pty(?:;|set;|v;)|sp(?:1(?:3;|4;)|;))|n(?:g;|sp;)|o(?:gon;|pf;)|p(?:ar(?:;|sl;)|lus;|si(?:;|lon;|v;))|q(?:c(?:irc;|olon;)|s(?:im;|lant(?:gtr;|less;))|u(?:als;|est;|iv(?:;|DD;))|vparsl;)|r(?:Dot;|arr;)|s(?:cr;|dot;|im;)|t(?:a;|h;?)|u(?:ml;?|ro;)|x(?:cl;|ist;|p(?:ectation;|onentiale;)))|f(?:allingdotseq;|cy;|emale;|f(?:ilig;|l(?:ig;|lig;)|r;)|ilig;|jlig;|l(?:at;|lig;|tns;)|nof;|o(?:pf;|r(?:all;|k(?:;|v;)))|partint;|r(?:a(?:c(?:1(?:2;?|3;|4;?|5;|6;|8;)|2(?:3;|5;)|3(?:4;?|5;|8;)|45;|5(?:6;|8;)|78;)|sl;)|own;)|scr;)|g(?:E(?:;|l;)|a(?:cute;|mma(?:;|d;)|p;)|breve;|c(?:irc;|y;)|dot;|e(?:;|l;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|l;))|l(?:;|es;)))|fr;|g(?:;|g;)|imel;|jcy;|l(?:;|E;|a;|j;)|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|opf;|rave;|s(?:cr;|im(?:;|e;|l;))|t(?:;|c(?:c;|ir;)|dot;|lPar;|quest;|r(?:a(?:pprox;|rr;)|dot;|eq(?:less;|qless;)|less;|sim;)|)|v(?:ertneqq;|nE;))|h(?:Arr;|a(?:irsp;|lf;|milt;|r(?:dcy;|r(?:;|cir;|w;)))|bar;|circ;|e(?:arts(?:;|uit;)|llip;|rcon;)|fr;|ks(?:earow;|warow;)|o(?:arr;|mtht;|ok(?:leftarrow;|rightarrow;)|pf;|rbar;)|s(?:cr;|lash;|trok;)|y(?:bull;|phen;))|i(?:acute;?|c(?:;|irc;?|y;)|e(?:cy;|xcl;?)|f(?:f;|r;)|grave;?|i(?:;|i(?:int;|nt;)|nfin;|ota;)|jlig;|m(?:a(?:cr;|g(?:e;|line;|part;)|th;)|of;|ped;)|n(?:;|care;|fin(?:;|tie;)|odot;|t(?:;|cal;|e(?:gers;|rcal;)|larhk;|prod;))|o(?:cy;|gon;|pf;|ta;)|prod;|quest;?|s(?:cr;|in(?:;|E;|dot;|s(?:;|v;)|v;))|t(?:;|ilde;)|u(?:kcy;|ml;?))|j(?:c(?:irc;|y;)|fr;|math;|opf;|s(?:cr;|ercy;)|ukcy;)|k(?:appa(?:;|v;)|c(?:edil;|y;)|fr;|green;|hcy;|jcy;|opf;|scr;)|l(?:A(?:arr;|rr;|tail;)|Barr;|E(?:;|g;)|Har;|a(?:cute;|emptyv;|gran;|mbda;|ng(?:;|d;|le;)|p;|quo;?|rr(?:;|b(?:;|fs;)|fs;|hk;|lp;|pl;|sim;|tl;)|t(?:;|ail;|e(?:;|s;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|quo(?:;|r;)|r(?:dhar;|ushar;)|sh;)|e(?:;|ft(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|leftarrows;|right(?:arrow(?:;|s;)|harpoons;|squigarrow;)|threetimes;)|g;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|r;))|g(?:;|es;)|s(?:approx;|dot;|eq(?:gtr;|qgtr;)|gtr;|sim;)))|f(?:isht;|loor;|r;)|g(?:;|E;)|h(?:ar(?:d;|u(?:;|l;))|blk;)|jcy;|l(?:;|arr;|corner;|hard;|tri;)|m(?:idot;|oust(?:;|ache;))|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|o(?:a(?:ng;|rr;)|brk;|ng(?:left(?:arrow;|rightarrow;)|mapsto;|rightarrow;)|oparrow(?:left;|right;)|p(?:ar;|f;|lus;)|times;|w(?:ast;|bar;)|z(?:;|enge;|f;))|par(?:;|lt;)|r(?:arr;|corner;|har(?:;|d;)|m;|tri;)|s(?:aquo;|cr;|h;|im(?:;|e;|g;)|q(?:b;|uo(?:;|r;))|trok;)|t(?:;|c(?:c;|ir;)|dot;|hree;|imes;|larr;|quest;|r(?:Par;|i(?:;|e;|f;))|)|ur(?:dshar;|uhar;)|v(?:ertneqq;|nE;))|m(?:DDot;|a(?:cr;?|l(?:e;|t(?:;|ese;))|p(?:;|sto(?:;|down;|left;|up;))|rker;)|c(?:omma;|y;)|dash;|easuredangle;|fr;|ho;|i(?:cro;?|d(?:;|ast;|cir;|dot;?)|nus(?:;|b;|d(?:;|u;)))|l(?:cp;|dr;)|nplus;|o(?:dels;|pf;)|p;|s(?:cr;|tpos;)|u(?:;|ltimap;|map;))|n(?:G(?:g;|t(?:;|v;))|L(?:eft(?:arrow;|rightarrow;)|l;|t(?:;|v;))|Rightarrow;|V(?:Dash;|dash;)|a(?:bla;|cute;|ng;|p(?:;|E;|id;|os;|prox;)|tur(?:;|al(?:;|s;)))|b(?:sp;?|ump(?:;|e;))|c(?:a(?:p;|ron;)|edil;|ong(?:;|dot;)|up;|y;)|dash;|e(?:;|Arr;|ar(?:hk;|r(?:;|ow;))|dot;|quiv;|s(?:ear;|im;)|xist(?:;|s;))|fr;|g(?:E;|e(?:;|q(?:;|q;|slant;)|s;)|sim;|t(?:;|r;))|h(?:Arr;|arr;|par;)|i(?:;|s(?:;|d;)|v;)|jcy;|l(?:Arr;|E;|arr;|dr;|e(?:;|ft(?:arrow;|rightarrow;)|q(?:;|q;|slant;)|s(?:;|s;))|sim;|t(?:;|ri(?:;|e;)))|mid;|o(?:pf;|t(?:;|in(?:;|E;|dot;|v(?:a;|b;|c;))|ni(?:;|v(?:a;|b;|c;))|))|p(?:ar(?:;|allel;|sl;|t;)|olint;|r(?:;|cue;|e(?:;|c(?:;|eq;))))|r(?:Arr;|arr(?:;|c;|w;)|ightarrow;|tri(?:;|e;))|s(?:c(?:;|cue;|e;|r;)|hort(?:mid;|parallel;)|im(?:;|e(?:;|q;))|mid;|par;|qsu(?:be;|pe;)|u(?:b(?:;|E;|e;|set(?:;|eq(?:;|q;)))|cc(?:;|eq;)|p(?:;|E;|e;|set(?:;|eq(?:;|q;)))))|t(?:gl;|ilde;?|lg;|riangle(?:left(?:;|eq;)|right(?:;|eq;)))|u(?:;|m(?:;|ero;|sp;))|v(?:Dash;|Harr;|ap;|dash;|g(?:e;|t;)|infin;|l(?:Arr;|e;|t(?:;|rie;))|r(?:Arr;|trie;)|sim;)|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|near;))|o(?:S;|a(?:cute;?|st;)|c(?:ir(?:;|c;?)|y;)|d(?:ash;|blac;|iv;|ot;|sold;)|elig;|f(?:cir;|r;)|g(?:on;|rave;?|t;)|h(?:bar;|m;)|int;|l(?:arr;|c(?:ir;|ross;)|ine;|t;)|m(?:acr;|ega;|i(?:cron;|d;|nus;))|opf;|p(?:ar;|erp;|lus;)|r(?:;|arr;|d(?:;|er(?:;|of;)|f;?|m;?)|igof;|or;|slope;|v;)|s(?:cr;|lash;?|ol;)|ti(?:lde;?|mes(?:;|as;))|uml;?|vbar;)|p(?:ar(?:;|a(?:;|llel;|)|s(?:im;|l;)|t;)|cy;|er(?:cnt;|iod;|mil;|p;|tenk;)|fr;|h(?:i(?:;|v;)|mmat;|one;)|i(?:;|tchfork;|v;)|l(?:an(?:ck(?:;|h;)|kv;)|us(?:;|acir;|b;|cir;|d(?:o;|u;)|e;|mn;?|sim;|two;))|m;|o(?:intint;|pf;|und;?)|r(?:;|E;|ap;|cue;|e(?:;|c(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;))|ime(?:;|s;)|n(?:E;|ap;|sim;)|o(?:d;|f(?:alar;|line;|surf;)|p(?:;|to;))|sim;|urel;)|s(?:cr;|i;)|uncsp;)|q(?:fr;|int;|opf;|prime;|scr;|u(?:at(?:ernions;|int;)|est(?:;|eq;)|ot;?))|r(?:A(?:arr;|rr;|tail;)|Barr;|Har;|a(?:c(?:e;|ute;)|dic;|emptyv;|ng(?:;|d;|e;|le;)|quo;?|rr(?:;|ap;|b(?:;|fs;)|c;|fs;|hk;|lp;|pl;|sim;|tl;|w;)|t(?:ail;|io(?:;|nals;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|ldhar;|quo(?:;|r;)|sh;)|e(?:al(?:;|ine;|part;|s;)|ct;|g;?)|f(?:isht;|loor;|r;)|h(?:ar(?:d;|u(?:;|l;))|o(?:;|v;))|i(?:ght(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|left(?:arrows;|harpoons;)|rightarrows;|squigarrow;|threetimes;)|ng;|singdotseq;)|l(?:arr;|har;|m;)|moust(?:;|ache;)|nmid;|o(?:a(?:ng;|rr;)|brk;|p(?:ar;|f;|lus;)|times;)|p(?:ar(?:;|gt;)|polint;)|rarr;|s(?:aquo;|cr;|h;|q(?:b;|uo(?:;|r;)))|t(?:hree;|imes;|ri(?:;|e;|f;|ltri;))|uluhar;|x;)|s(?:acute;|bquo;|c(?:;|E;|a(?:p;|ron;)|cue;|e(?:;|dil;)|irc;|n(?:E;|ap;|sim;)|polint;|sim;|y;)|dot(?:;|b;|e;)|e(?:Arr;|ar(?:hk;|r(?:;|ow;))|ct;?|mi;|swar;|tm(?:inus;|n;)|xt;)|fr(?:;|own;)|h(?:arp;|c(?:hcy;|y;)|ort(?:mid;|parallel;)|y;?)|i(?:gma(?:;|f;|v;)|m(?:;|dot;|e(?:;|q;)|g(?:;|E;)|l(?:;|E;)|ne;|plus;|rarr;))|larr;|m(?:a(?:llsetminus;|shp;)|eparsl;|i(?:d;|le;)|t(?:;|e(?:;|s;)))|o(?:ftcy;|l(?:;|b(?:;|ar;))|pf;)|pa(?:des(?:;|uit;)|r;)|q(?:c(?:ap(?:;|s;)|up(?:;|s;))|su(?:b(?:;|e;|set(?:;|eq;))|p(?:;|e;|set(?:;|eq;)))|u(?:;|ar(?:e;|f;)|f;))|rarr;|s(?:cr;|etmn;|mile;|tarf;)|t(?:ar(?:;|f;)|r(?:aight(?:epsilon;|phi;)|ns;))|u(?:b(?:;|E;|dot;|e(?:;|dot;)|mult;|n(?:E;|e;)|plus;|rarr;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;)))|cc(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;)|m;|ng;|p(?:1;?|2;?|3;?|;|E;|d(?:ot;|sub;)|e(?:;|dot;)|hs(?:ol;|ub;)|larr;|mult;|n(?:E;|e;)|plus;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;))))|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|nwar;)|zlig;?)|t(?:a(?:rget;|u;)|brk;|c(?:aron;|edil;|y;)|dot;|elrec;|fr;|h(?:e(?:re(?:4;|fore;)|ta(?:;|sym;|v;))|i(?:ck(?:approx;|sim;)|nsp;)|k(?:ap;|sim;)|orn;?)|i(?:lde;|mes(?:;|b(?:;|ar;)|d;|)|nt;)|o(?:ea;|p(?:;|bot;|cir;|f(?:;|ork;))|sa;)|prime;|r(?:ade;|i(?:angle(?:;|down;|left(?:;|eq;)|q;|right(?:;|eq;))|dot;|e;|minus;|plus;|sb;|time;)|pezium;)|s(?:c(?:r;|y;)|hcy;|trok;)|w(?:ixt;|ohead(?:leftarrow;|rightarrow;)))|u(?:Arr;|Har;|a(?:cute;?|rr;)|br(?:cy;|eve;)|c(?:irc;?|y;)|d(?:arr;|blac;|har;)|f(?:isht;|r;)|grave;?|h(?:ar(?:l;|r;)|blk;)|l(?:c(?:orn(?:;|er;)|rop;)|tri;)|m(?:acr;|l;?)|o(?:gon;|pf;)|p(?:arrow;|downarrow;|harpoon(?:left;|right;)|lus;|si(?:;|h;|lon;)|uparrows;)|r(?:c(?:orn(?:;|er;)|rop;)|ing;|tri;)|scr;|t(?:dot;|ilde;|ri(?:;|f;))|u(?:arr;|ml;?)|wangle;)|v(?:Arr;|Bar(?:;|v;)|Dash;|a(?:ngrt;|r(?:epsilon;|kappa;|nothing;|p(?:hi;|i;|ropto;)|r(?:;|ho;)|s(?:igma;|u(?:bsetneq(?:;|q;)|psetneq(?:;|q;)))|t(?:heta;|riangle(?:left;|right;))))|cy;|dash;|e(?:e(?:;|bar;|eq;)|llip;|r(?:bar;|t;))|fr;|ltri;|nsu(?:b;|p;)|opf;|prop;|rtri;|s(?:cr;|u(?:bn(?:E;|e;)|pn(?:E;|e;)))|zigzag;)|w(?:circ;|e(?:d(?:bar;|ge(?:;|q;))|ierp;)|fr;|opf;|p;|r(?:;|eath;)|scr;)|x(?:c(?:ap;|irc;|up;)|dtri;|fr;|h(?:Arr;|arr;)|i;|l(?:Arr;|arr;)|map;|nis;|o(?:dot;|p(?:f;|lus;)|time;)|r(?:Arr;|arr;)|s(?:cr;|qcup;)|u(?:plus;|tri;)|vee;|wedge;)|y(?:ac(?:ute;?|y;)|c(?:irc;|y;)|en;?|fr;|icy;|opf;|scr;|u(?:cy;|ml;?))|z(?:acute;|c(?:aron;|y;)|dot;|e(?:etrf;|ta;)|fr;|hcy;|igrarr;|opf;|scr;|w(?:j;|nj;)))|[\s\S]/g,jr=/[^\r"&\u0000]+/g,zn=/[^\r'&\u0000]+/g,Gn=/[^\r\t\n\f &>\u0000]+/g,$n=/[^\r\t\n\f \/>A-Z\u0000]+/g,qt=/[^\r\t\n\f \/=>A-Z\u0000]+/g,zr=/[^\]\r\u0000\uffff]*/g,vu=/[^&<\r\u0000\uffff]*/g,ss=/[^<\r\u0000\uffff]*/g,ud=/[^\r\u0000\uffff]*/g,os=/(?:(\/)?([a-z]+)>)|[\s\S]/g,cs=/(?:([-a-z]+)[ \t\n\f]*=[ \t\n\f]*('[^'&\r\u0000]*'|"[^"&\r\u0000]*"|[^\t\n\r\f "&'\u0000>][^&> \t\n\r\f\u0000]*[ \t\n\f]))|[\s\S]/g,yu=/[^\x09\x0A\x0C\x0D\x20]/,Wn=/[^\x09\x0A\x0C\x0D\x20]/g,nd=/[^\x00\x09\x0A\x0C\x0D\x20]/,ir=/^[\x09\x0A\x0C\x0D\x20]+/,wu=/\x00/g;function et(V){var z=16384;if(V.length<z)return String.fromCharCode.apply(String,V);for(var ue="",ee=0;ee<V.length;ee+=z)ue+=String.fromCharCode.apply(String,V.slice(ee,ee+z));return ue}function ad(V){for(var z=[],ue=0;ue<V.length;ue++)z[ue]=V.charCodeAt(ue);return z}function Ne(V,z){if(typeof z=="string")return V.namespaceURI===a.HTML&&V.localName===z;var ue=z[V.namespaceURI];return ue&&ue[V.localName]}function ls(V){return Ne(V,J)}function ds(V){if(Ne(V,de))return!0;if(V.namespaceURI===a.MATHML&&V.localName==="annotation-xml"){var z=V.getAttribute("encoding");if(z&&(z=z.toLowerCase()),z==="text/html"||z==="application/xhtml+xml")return!0}return!1}function id(V){return V in D?D[V]:V}function fs(V){for(var z=0,ue=V.length;z<ue;z++)V[z][0]in k&&(V[z][0]=k[V[z][0]])}function hs(V){for(var z=0,ue=V.length;z<ue;z++)if(V[z][0]==="definitionurl"){V[z][0]="definitionURL";break}}function Xn(V){for(var z=0,ue=V.length;z<ue;z++)V[z][0]in pe&&V[z].push(pe[V[z][0]])}function ps(V,z){for(var ue=0,ee=V.length;ue<ee;ue++){var Re=V[ue][0],te=V[ue][1];z.hasAttribute(Re)||z._setAttribute(Re,te)}}we.ElementStack=function(){this.elements=[],this.top=null},we.ElementStack.prototype.push=function(V){this.elements.push(V),this.top=V},we.ElementStack.prototype.pop=function(V){this.elements.pop(),this.top=this.elements[this.elements.length-1]},we.ElementStack.prototype.popTag=function(V){for(var z=this.elements.length-1;z>0;z--){var ue=this.elements[z];if(Ne(ue,V))break}this.elements.length=z,this.top=this.elements[z-1]},we.ElementStack.prototype.popElementType=function(V){for(var z=this.elements.length-1;z>0&&!(this.elements[z]instanceof V);z--);this.elements.length=z,this.top=this.elements[z-1]},we.ElementStack.prototype.popElement=function(V){for(var z=this.elements.length-1;z>0&&this.elements[z]!==V;z--);this.elements.length=z,this.top=this.elements[z-1]},we.ElementStack.prototype.removeElement=function(V){if(this.top===V)this.pop();else{var z=this.elements.lastIndexOf(V);z!==-1&&this.elements.splice(z,1)}},we.ElementStack.prototype.clearToContext=function(V){for(var z=this.elements.length-1;z>0&&!Ne(this.elements[z],V);z--);this.elements.length=z+1,this.top=this.elements[z]},we.ElementStack.prototype.contains=function(V){return this.inSpecificScope(V,Object.create(null))},we.ElementStack.prototype.inSpecificScope=function(V,z){for(var ue=this.elements.length-1;ue>=0;ue--){var ee=this.elements[ue];if(Ne(ee,V))return!0;if(Ne(ee,z))return!1}return!1},we.ElementStack.prototype.elementInSpecificScope=function(V,z){for(var ue=this.elements.length-1;ue>=0;ue--){var ee=this.elements[ue];if(ee===V)return!0;if(Ne(ee,z))return!1}return!1},we.ElementStack.prototype.elementTypeInSpecificScope=function(V,z){for(var ue=this.elements.length-1;ue>=0;ue--){var ee=this.elements[ue];if(ee instanceof V)return!0;if(Ne(ee,z))return!1}return!1},we.ElementStack.prototype.inScope=function(V){return this.inSpecificScope(V,m)},we.ElementStack.prototype.elementInScope=function(V){return this.elementInSpecificScope(V,m)},we.ElementStack.prototype.elementTypeInScope=function(V){return this.elementTypeInSpecificScope(V,m)},we.ElementStack.prototype.inButtonScope=function(V){return this.inSpecificScope(V,M)},we.ElementStack.prototype.inListItemScope=function(V){return this.inSpecificScope(V,g)},we.ElementStack.prototype.inTableScope=function(V){return this.inSpecificScope(V,H)},we.ElementStack.prototype.inSelectScope=function(V){for(var z=this.elements.length-1;z>=0;z--){var ue=this.elements[z];if(ue.namespaceURI!==a.HTML)return!1;var ee=ue.localName;if(ee===V)return!0;if(ee!=="optgroup"&&ee!=="option")return!1}return!1},we.ElementStack.prototype.generateImpliedEndTags=function(V,z){for(var ue=z?q:F,ee=this.elements.length-1;ee>=0;ee--){var Re=this.elements[ee];if(V&&Ne(Re,V)||!Ne(this.elements[ee],ue))break}this.elements.length=ee+1,this.top=this.elements[ee]},we.ActiveFormattingElements=function(){this.list=[],this.attrs=[]},we.ActiveFormattingElements.prototype.MARKER={localName:"|"},we.ActiveFormattingElements.prototype.insertMarker=function(){this.list.push(this.MARKER),this.attrs.push(this.MARKER)},we.ActiveFormattingElements.prototype.push=function(V,z){for(var ue=0,ee=this.list.length-1;ee>=0&&this.list[ee]!==this.MARKER;ee--)if(sr(V,this.list[ee],this.attrs[ee])&&(ue++,ue===3)){this.list.splice(ee,1),this.attrs.splice(ee,1);break}this.list.push(V);for(var Re=[],te=0;te<z.length;te++)Re[te]=z[te];this.attrs.push(Re);function sr(Pt,or,Nt){if(Pt.localName!==or.localName||Pt._numattrs!==Nt.length)return!1;for(var nt=0,xu=Nt.length;nt<xu;nt++){var cr=Nt[nt][0],I=Nt[nt][1];if(!Pt.hasAttribute(cr)||Pt.getAttribute(cr)!==I)return!1}return!0}},we.ActiveFormattingElements.prototype.clearToMarker=function(){for(var V=this.list.length-1;V>=0&&this.list[V]!==this.MARKER;V--);V<0&&(V=0),this.list.length=V,this.attrs.length=V},we.ActiveFormattingElements.prototype.findElementByTag=function(V){for(var z=this.list.length-1;z>=0;z--){var ue=this.list[z];if(ue===this.MARKER)break;if(ue.localName===V)return ue}return null},we.ActiveFormattingElements.prototype.indexOf=function(V){return this.list.lastIndexOf(V)},we.ActiveFormattingElements.prototype.remove=function(V){var z=this.list.lastIndexOf(V);z!==-1&&(this.list.splice(z,1),this.attrs.splice(z,1))},we.ActiveFormattingElements.prototype.replace=function(V,z,ue){var ee=this.list.lastIndexOf(V);ee!==-1&&(this.list[ee]=z,this.attrs[ee]=ue)},we.ActiveFormattingElements.prototype.insertAfter=function(V,z){var ue=this.list.lastIndexOf(V);ue!==-1&&(this.list.splice(ue,0,z),this.attrs.splice(ue,0,z))};function we(V,z,ue){var ee=null,Re=0,te=0,sr=!1,Pt=!1,or=0,Nt=[],nt="",xu=!0,cr=0,I=ge,Ht,Fe,Ae="",_u="",Ce=[],st="",at="",Oe=[],Bt=[],Ft=[],Ut=[],pt=[],Eu=!1,j=uf,At=null,Ct=[],L=new we.ElementStack,be=new we.ActiveFormattingElements,lr=z!==void 0,Su=null,kt=null,Tu=!0;z&&(Tu=z.ownerDocument._scripting_enabled),ue&&ue.scripting_enabled===!1&&(Tu=!1);var Ue=!0,Qn=!1,Nu,Zn,Q=[],Vt=!1,dr=!1,Au={document:function(){return _e},_asDocumentFragment:function(){for(var p=_e.createDocumentFragment(),b=_e.firstChild;b.hasChildNodes();)p.appendChild(b.firstChild);return p},pause:function(){cr++},resume:function(){cr--,this.parse("")},parse:function(p,b,N){var R;return cr>0?(nt+=p,!0):(or===0?(nt&&(p=nt+p,nt=""),b&&(p+="\uFFFF",sr=!0),ee=p,Re=p.length,te=0,xu&&(xu=!1,ee.charCodeAt(0)===65279&&(te=1)),or++,R=bs(N),nt=ee.substring(te,Re),or--):(or++,Nt.push(ee,Re,te),ee=p,Re=p.length,te=0,bs(),R=!1,nt=ee.substring(te,Re),te=Nt.pop(),Re=Nt.pop(),ee=Nt.pop(),nt&&(ee=nt+ee.substring(te),Re=ee.length,te=0,nt=""),or--),R)}},_e=new r(!0,V);if(_e._parser=Au,_e._scripting_enabled=Tu,z){if(z.ownerDocument._quirks&&(_e._quirks=!0),z.ownerDocument._limitedQuirks&&(_e._limitedQuirks=!0),z.namespaceURI===a.HTML)switch(z.localName){case"title":case"textarea":I=$t;break;case"style":case"xmp":case"iframe":case"noembed":case"noframes":case"script":case"plaintext":I=ta;break}var ms=_e.createElement("html");_e._appendChild(ms),L.push(ms),z instanceof s.HTMLTemplateElement&&Ct.push(da),Zr();for(var Gr=z;Gr!==null;Gr=Gr.parentElement)if(Gr instanceof s.HTMLFormElement){kt=Gr;break}}function bs(p){for(var b,N,R,U;te<Re;){if(cr>0||p&&p())return!0;switch(typeof I.lookahead){case"undefined":if(b=ee.charCodeAt(te++),Pt&&(Pt=!1,b===10)){te++;continue}switch(b){case 13:te<Re?ee.charCodeAt(te)===10&&te++:Pt=!0,I(10);break;case 65535:if(sr&&te===Re){I(o);break}default:I(b);break}break;case"number":b=ee.charCodeAt(te);var Y=I.lookahead,ie=!0;if(Y<0&&(ie=!1,Y=-Y),Y<Re-te)N=ie?ee.substring(te,te+Y):null,U=!1;else if(sr)N=ie?ee.substring(te,Re):null,U=!0,b===65535&&te===Re-1&&(b=o);else return!0;I(b,N,U);break;case"string":b=ee.charCodeAt(te),R=I.lookahead;var ve=ee.indexOf(R,te);if(ve!==-1)N=ee.substring(te,ve+R.length),U=!1;else{if(!sr)return!0;N=ee.substring(te,Re),b===65535&&te===Re-1&&(b=o),U=!0}I(b,N,U);break}}return!1}function jt(p,b){for(var N=0;N<pt.length;N++)if(pt[N][0]===p)return;b!==void 0?pt.push([p,b]):pt.push([p])}function sd(){cs.lastIndex=te-1;var p=cs.exec(ee);if(!p)throw new Error("should never happen");var b=p[1];if(!b)return!1;var N=p[2],R=N.length;switch(N[0]){case'"':case"'":N=N.substring(1,R-1),te+=p[0].length-1,I=aa;break;default:I=_t,te+=p[0].length-1,N=N.substring(0,R-1);break}for(var U=0;U<pt.length;U++)if(pt[U][0]===b)return!0;return pt.push([b,N]),!0}function od(){Eu=!1,Ae="",pt.length=0}function $r(){Eu=!0,Ae="",pt.length=0}function Lt(){Ce.length=0}function Kn(){st=""}function Yn(){at=""}function gs(){Oe.length=0}function Dr(){Bt.length=0,Ft=null,Ut=null}function Cu(){Ft=[]}function zt(){Ut=[]}function Ee(){Qn=!0}function cd(){return L.top&&L.top.namespaceURI!=="http://www.w3.org/1999/xhtml"}function ct(p){return _u===p}function Ir(){if(Q.length>0){var p=et(Q);if(Q.length=0,dr&&(dr=!1,p[0]===`
`&&(p=p.substring(1)),p.length===0))return;Ge(c,p),Vt=!1}dr=!1}function Wr(p){p.lastIndex=te-1;var b=p.exec(ee);if(b&&b.index===te-1)return b=b[0],te+=b.length-1,sr&&te===Re&&(b=b.slice(0,-1),te--),b;throw new Error("should never happen")}function Xr(p){p.lastIndex=te-1;var b=p.exec(ee)[0];return b?(ld(b),te+=b.length-1,!0):!1}function ld(p){Q.length>0&&Ir(),!(dr&&(dr=!1,p[0]===`
`&&(p=p.substring(1)),p.length===0))&&Ge(c,p)}function Mt(){if(Eu)Ge(f,Ae);else{var p=Ae;Ae="",_u=p,Ge(d,p,pt)}}function dd(){if(te===Re)return!1;os.lastIndex=te;var p=os.exec(ee);if(!p)throw new Error("should never happen");var b=p[2];if(!b)return!1;var N=p[1];return N?(te+=b.length+2,Ge(f,b)):(te+=b.length+1,_u=b,Ge(d,b,w)),!0}function fd(){Eu?Ge(f,Ae,null,!0):Ge(d,Ae,pt,!0)}function Se(){Ge(v,et(Bt),Ft?et(Ft):void 0,Ut?et(Ut):void 0)}function fe(){Ir(),j(o),_e.modclock=1}var Ge=Au.insertToken=function(b,N,R,U){Ir();var Y=L.top;!Y||Y.namespaceURI===a.HTML?j(b,N,R,U):b!==d&&b!==c?Is(b,N,R,U):ls(Y)&&(b===c||b===d&&N!=="mglyph"&&N!=="malignmark")||b===d&&N==="svg"&&Y.namespaceURI===a.MATHML&&Y.localName==="annotation-xml"||ds(Y)?(Zn=!0,j(b,N,R,U),Zn=!1):Is(b,N,R,U)};function yt(p){var b=L.top;Gt&&Ne(b,B)?Lu(function(N){return N.createComment(p)}):(b instanceof s.HTMLTemplateElement&&(b=b.content),b._appendChild(b.ownerDocument.createComment(p)))}function wt(p){var b=L.top;if(Gt&&Ne(b,B))Lu(function(R){return R.createTextNode(p)});else{b instanceof s.HTMLTemplateElement&&(b=b.content);var N=b.lastChild;N&&N.nodeType===u.TEXT_NODE?N.appendData(p):b._appendChild(b.ownerDocument.createTextNode(p))}}function Qr(p,b,N){var R=i.createElement(p,b,null);if(N)for(var U=0,Y=N.length;U<Y;U++)R._setAttribute(N[U][0],N[U][1]);return R}var Gt=!1;function le(p,b){var N=ku(function(R){return Qr(R,p,b)});return Ne(N,h)&&(N._form=kt),N}function ku(p){var b;return Gt&&Ne(L.top,B)?b=Lu(p):L.top instanceof s.HTMLTemplateElement?(b=p(L.top.content.ownerDocument),L.top.content._appendChild(b)):(b=p(L.top.ownerDocument),L.top._appendChild(b)),L.push(b),b}function Jn(p,b,N){return ku(function(R){var U=R._createElementNS(p,N,null);if(b)for(var Y=0,ie=b.length;Y<ie;Y++){var ve=b[Y];ve.length===2?U._setAttribute(ve[0],ve[1]):U._setAttributeNS(ve[2],ve[0],ve[1])}return U})}function vs(p){for(var b=L.elements.length-1;b>=0;b--)if(L.elements[b]instanceof p)return b;return-1}function Lu(p){var b,N,R=-1,U=-1,Y;if(R=vs(s.HTMLTableElement),U=vs(s.HTMLTemplateElement),U>=0&&(R<0||U>R)?b=L.elements[U]:R>=0&&(b=L.elements[R].parentNode,b?N=L.elements[R]:b=L.elements[R-1]),b||(b=L.elements[0]),b instanceof s.HTMLTemplateElement&&(b=b.content),Y=p(b.ownerDocument),Y.nodeType===u.TEXT_NODE){var ie;if(N?ie=N.previousSibling:ie=b.lastChild,ie&&ie.nodeType===u.TEXT_NODE)return ie.appendData(Y.data),Y}return N?b.insertBefore(Y,N):b._appendChild(Y),Y}function Zr(){for(var p=!1,b=L.elements.length-1;b>=0;b--){var N=L.elements[b];if(b===0&&(p=!0,lr&&(N=z)),N.namespaceURI===a.HTML){var R=N.localName;switch(R){case"select":for(var U=b;U>0;){var Y=L.elements[--U];if(Y instanceof s.HTMLTemplateElement)break;if(Y instanceof s.HTMLTableElement){j=zu;return}}j=Dt;return;case"tr":j=Jr;return;case"tbody":case"tfoot":case"thead":j=mr;return;case"caption":j=la;return;case"colgroup":j=ju;return;case"table":j=lt;return;case"template":j=Ct[Ct.length-1];return;case"body":j=ae;return;case"frameset":j=fa;return;case"html":Su===null?j=Uu:j=ca;return;default:if(!p){if(R==="head"){j=Ve;return}if(R==="td"||R==="th"){j=Or;return}}}}if(p){j=ae;return}}}function Mu(p,b){le(p,b),I=Kr,At=j,j=Vu}function hd(p,b){le(p,b),I=$t,At=j,j=Vu}function ea(p,b){return{elt:Qr(p,be.list[b].localName,be.attrs[b]),attrs:be.attrs[b]}}function ut(){if(be.list.length!==0){var p=be.list[be.list.length-1];if(p!==be.MARKER&&L.elements.lastIndexOf(p)===-1){for(var b=be.list.length-2;b>=0&&(p=be.list[b],!(p===be.MARKER||L.elements.lastIndexOf(p)!==-1));b--);for(b=b+1;b<be.list.length;b++){var N=ku(function(R){return ea(R,b).elt});be.list[b]=N}}}}var Du={localName:"BM"};function pd(p){if(Ne(L.top,p)&&be.indexOf(L.top)===-1)return L.pop(),!0;for(var b=0;b<8;){b++;var N=be.findElementByTag(p);if(!N)return!1;var R=L.elements.lastIndexOf(N);if(R===-1)return be.remove(N),!0;if(!L.elementInScope(N))return!0;for(var U=null,Y,ie=R+1;ie<L.elements.length;ie++)if(Ne(L.elements[ie],T)){U=L.elements[ie],Y=ie;break}if(U){var ve=L.elements[R-1];be.insertAfter(N,Du);for(var Be=U,Ze=U,dt=Y,mt,br=0;br++,Be=L.elements[--dt],Be!==N;){if(mt=be.indexOf(Be),br>3&&mt!==-1&&(be.remove(Be),mt=-1),mt===-1){L.removeElement(Be);continue}var Kt=ea(ve.ownerDocument,mt);be.replace(Be,Kt.elt,Kt.attrs),L.elements[dt]=Kt.elt,Be=Kt.elt,Ze===U&&(be.remove(Du),be.insertAfter(Kt.elt,Du)),Be._appendChild(Ze),Ze=Be}Gt&&Ne(ve,B)?Lu(function(){return Ze}):ve instanceof s.HTMLTemplateElement?ve.content._appendChild(Ze):ve._appendChild(Ze);for(var eu=ea(U.ownerDocument,be.indexOf(N));U.hasChildNodes();)eu.elt._appendChild(U.firstChild);U._appendChild(eu.elt),be.remove(N),be.replace(Du,eu.elt,eu.attrs),L.removeElement(N);var cf=L.elements.lastIndexOf(U);L.elements.splice(cf+1,0,eu.elt)}else return L.popElement(N),be.remove(N),!0}return!0}function md(){L.pop(),j=At}function fr(){delete _e._parser,L.elements.length=0,_e.defaultView&&_e.defaultView.dispatchEvent(new s.Event("load",{}))}function re(p,b){I=b,te--}function ge(p){switch(p){case 38:Ht=ge,I=Yr;break;case 60:if(dd())break;I=bd;break;case 0:Q.push(p),Vt=!0;break;case-1:fe();break;default:Xr(vu)||Q.push(p);break}}function $t(p){switch(p){case 38:Ht=$t,I=Yr;break;case 60:I=vd;break;case 0:Q.push(65533),Vt=!0;break;case-1:fe();break;default:Q.push(p);break}}function Kr(p){switch(p){case 60:I=xd;break;case 0:Q.push(65533);break;case-1:fe();break;default:Xr(ss)||Q.push(p);break}}function Wt(p){switch(p){case 60:I=Sd;break;case 0:Q.push(65533);break;case-1:fe();break;default:Xr(ss)||Q.push(p);break}}function ta(p){switch(p){case 0:Q.push(65533);break;case-1:fe();break;default:Xr(ud)||Q.push(p);break}}function bd(p){switch(p){case 33:I=_s;break;case 47:I=gd;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:od(),re(p,ys);break;case 63:re(p,qu);break;default:Q.push(60),re(p,ge);break}}function gd(p){switch(p){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:$r(),re(p,ys);break;case 62:I=ge;break;case-1:Q.push(60),Q.push(47),fe();break;default:re(p,qu);break}}function ys(p){switch(p){case 9:case 10:case 12:case 32:I=_t;break;case 47:I=Qt;break;case 62:I=ge,Mt();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ae+=String.fromCharCode(p+32);break;case 0:Ae+="\uFFFD";break;case-1:fe();break;default:Ae+=Wr($n);break}}function vd(p){p===47?(Lt(),I=yd):(Q.push(60),re(p,$t))}function yd(p){switch(p){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:$r(),re(p,wd);break;default:Q.push(60),Q.push(47),re(p,$t);break}}function wd(p){switch(p){case 9:case 10:case 12:case 32:if(ct(Ae)){I=_t;return}break;case 47:if(ct(Ae)){I=Qt;return}break;case 62:if(ct(Ae)){I=ge,Mt();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ae+=String.fromCharCode(p+32),Ce.push(p);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ae+=String.fromCharCode(p),Ce.push(p);return}Q.push(60),Q.push(47),l(Q,Ce),re(p,$t)}function xd(p){p===47?(Lt(),I=_d):(Q.push(60),re(p,Kr))}function _d(p){switch(p){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:$r(),re(p,Ed);break;default:Q.push(60),Q.push(47),re(p,Kr);break}}function Ed(p){switch(p){case 9:case 10:case 12:case 32:if(ct(Ae)){I=_t;return}break;case 47:if(ct(Ae)){I=Qt;return}break;case 62:if(ct(Ae)){I=ge,Mt();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ae+=String.fromCharCode(p+32),Ce.push(p);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ae+=String.fromCharCode(p),Ce.push(p);return}Q.push(60),Q.push(47),l(Q,Ce),re(p,Kr)}function Sd(p){switch(p){case 47:Lt(),I=Td;break;case 33:I=Ad,Q.push(60),Q.push(33);break;default:Q.push(60),re(p,Wt);break}}function Td(p){switch(p){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:$r(),re(p,Nd);break;default:Q.push(60),Q.push(47),re(p,Wt);break}}function Nd(p){switch(p){case 9:case 10:case 12:case 32:if(ct(Ae)){I=_t;return}break;case 47:if(ct(Ae)){I=Qt;return}break;case 62:if(ct(Ae)){I=ge,Mt();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ae+=String.fromCharCode(p+32),Ce.push(p);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ae+=String.fromCharCode(p),Ce.push(p);return}Q.push(60),Q.push(47),l(Q,Ce),re(p,Wt)}function Ad(p){p===45?(I=Cd,Q.push(45)):re(p,Wt)}function Cd(p){p===45?(I=ws,Q.push(45)):re(p,Wt)}function xt(p){switch(p){case 45:I=kd,Q.push(45);break;case 60:I=ra;break;case 0:Q.push(65533);break;case-1:fe();break;default:Q.push(p);break}}function kd(p){switch(p){case 45:I=ws,Q.push(45);break;case 60:I=ra;break;case 0:I=xt,Q.push(65533);break;case-1:fe();break;default:I=xt,Q.push(p);break}}function ws(p){switch(p){case 45:Q.push(45);break;case 60:I=ra;break;case 62:I=Wt,Q.push(62);break;case 0:I=xt,Q.push(65533);break;case-1:fe();break;default:I=xt,Q.push(p);break}}function ra(p){switch(p){case 47:Lt(),I=Ld;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Lt(),Q.push(60),re(p,Dd);break;default:Q.push(60),re(p,xt);break}}function Ld(p){switch(p){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:$r(),re(p,Md);break;default:Q.push(60),Q.push(47),re(p,xt);break}}function Md(p){switch(p){case 9:case 10:case 12:case 32:if(ct(Ae)){I=_t;return}break;case 47:if(ct(Ae)){I=Qt;return}break;case 62:if(ct(Ae)){I=ge,Mt();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ae+=String.fromCharCode(p+32),Ce.push(p);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ae+=String.fromCharCode(p),Ce.push(p);return}Q.push(60),Q.push(47),l(Q,Ce),re(p,xt)}function Dd(p){switch(p){case 9:case 10:case 12:case 32:case 47:case 62:et(Ce)==="script"?I=Xt:I=xt,Q.push(p);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ce.push(p+32),Q.push(p);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ce.push(p),Q.push(p);break;default:re(p,xt);break}}function Xt(p){switch(p){case 45:I=Id,Q.push(45);break;case 60:I=ua,Q.push(60);break;case 0:Q.push(65533);break;case-1:fe();break;default:Q.push(p);break}}function Id(p){switch(p){case 45:I=Od,Q.push(45);break;case 60:I=ua,Q.push(60);break;case 0:I=Xt,Q.push(65533);break;case-1:fe();break;default:I=Xt,Q.push(p);break}}function Od(p){switch(p){case 45:Q.push(45);break;case 60:I=ua,Q.push(60);break;case 62:I=Wt,Q.push(62);break;case 0:I=Xt,Q.push(65533);break;case-1:fe();break;default:I=Xt,Q.push(p);break}}function ua(p){p===47?(Lt(),I=Rd,Q.push(47)):re(p,Xt)}function Rd(p){switch(p){case 9:case 10:case 12:case 32:case 47:case 62:et(Ce)==="script"?I=xt:I=Xt,Q.push(p);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ce.push(p+32),Q.push(p);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ce.push(p),Q.push(p);break;default:re(p,Xt);break}}function _t(p){switch(p){case 9:case 10:case 12:case 32:break;case 47:I=Qt;break;case 62:I=ge,Mt();break;case-1:fe();break;case 61:Kn(),st+=String.fromCharCode(p),I=na;break;default:if(sd())break;Kn(),re(p,na);break}}function na(p){switch(p){case 9:case 10:case 12:case 32:case 47:case 62:case-1:re(p,qd);break;case 61:I=xs;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:st+=String.fromCharCode(p+32);break;case 0:st+="\uFFFD";break;case 34:case 39:case 60:default:st+=Wr(qt);break}}function qd(p){switch(p){case 9:case 10:case 12:case 32:break;case 47:jt(st),I=Qt;break;case 61:I=xs;break;case 62:I=ge,jt(st),Mt();break;case-1:jt(st),fe();break;default:jt(st),Kn(),re(p,na);break}}function xs(p){switch(p){case 9:case 10:case 12:case 32:break;case 34:Yn(),I=Iu;break;case 39:Yn(),I=Ou;break;case 62:default:Yn(),re(p,Ru);break}}function Iu(p){switch(p){case 34:jt(st,at),I=aa;break;case 38:Ht=Iu,I=Yr;break;case 0:at+="\uFFFD";break;case-1:fe();break;case 10:at+=String.fromCharCode(p);break;default:at+=Wr(jr);break}}function Ou(p){switch(p){case 39:jt(st,at),I=aa;break;case 38:Ht=Ou,I=Yr;break;case 0:at+="\uFFFD";break;case-1:fe();break;case 10:at+=String.fromCharCode(p);break;default:at+=Wr(zn);break}}function Ru(p){switch(p){case 9:case 10:case 12:case 32:jt(st,at),I=_t;break;case 38:Ht=Ru,I=Yr;break;case 62:jt(st,at),I=ge,Mt();break;case 0:at+="\uFFFD";break;case-1:te--,I=ge;break;case 34:case 39:case 60:case 61:case 96:default:at+=Wr(Gn);break}}function aa(p){switch(p){case 9:case 10:case 12:case 32:I=_t;break;case 47:I=Qt;break;case 62:I=ge,Mt();break;case-1:fe();break;default:re(p,_t);break}}function Qt(p){switch(p){case 62:I=ge,fd();break;case-1:fe();break;default:re(p,_t);break}}function qu(p,b,N){var R=b.length;N?te+=R-1:te+=R;var U=b.substring(0,R-1);U=U.replace(/\u0000/g,"\uFFFD"),U=U.replace(/\u000D\u000A/g,`
`),U=U.replace(/\u000D/g,`
`),Ge(y,U),I=ge}qu.lookahead=">";function _s(p,b,N){if(b[0]==="-"&&b[1]==="-"){te+=2,gs(),I=Pd;return}b.toUpperCase()==="DOCTYPE"?(te+=7,I=zd):b==="[CDATA["&&cd()?(te+=7,I=oa):I=qu}_s.lookahead=7;function Pd(p){switch(gs(),p){case 45:I=Hd;break;case 62:I=ge,Ge(y,et(Oe));break;default:re(p,hr);break}}function Hd(p){switch(p){case 45:I=Pu;break;case 62:I=ge,Ge(y,et(Oe));break;case-1:Ge(y,et(Oe)),fe();break;default:Oe.push(45),re(p,hr);break}}function hr(p){switch(p){case 60:Oe.push(p),I=Bd;break;case 45:I=ia;break;case 0:Oe.push(65533);break;case-1:Ge(y,et(Oe)),fe();break;default:Oe.push(p);break}}function Bd(p){switch(p){case 33:Oe.push(p),I=Fd;break;case 60:Oe.push(p);break;default:re(p,hr);break}}function Fd(p){switch(p){case 45:I=Ud;break;default:re(p,hr);break}}function Ud(p){switch(p){case 45:I=Vd;break;default:re(p,ia);break}}function Vd(p){switch(p){case 62:case-1:re(p,Pu);break;default:re(p,Pu);break}}function ia(p){switch(p){case 45:I=Pu;break;case-1:Ge(y,et(Oe)),fe();break;default:Oe.push(45),re(p,hr);break}}function Pu(p){switch(p){case 62:I=ge,Ge(y,et(Oe));break;case 33:I=jd;break;case 45:Oe.push(45);break;case-1:Ge(y,et(Oe)),fe();break;default:Oe.push(45),Oe.push(45),re(p,hr);break}}function jd(p){switch(p){case 45:Oe.push(45),Oe.push(45),Oe.push(33),I=ia;break;case 62:I=ge,Ge(y,et(Oe));break;case-1:Ge(y,et(Oe)),fe();break;default:Oe.push(45),Oe.push(45),Oe.push(33),re(p,hr);break}}function zd(p){switch(p){case 9:case 10:case 12:case 32:I=Es;break;case-1:Dr(),Ee(),Se(),fe();break;default:re(p,Es);break}}function Es(p){switch(p){case 9:case 10:case 12:case 32:break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Dr(),Bt.push(p+32),I=sa;break;case 0:Dr(),Bt.push(65533),I=sa;break;case 62:Dr(),Ee(),I=ge,Se();break;case-1:Dr(),Ee(),Se(),fe();break;default:Dr(),Bt.push(p),I=sa;break}}function sa(p){switch(p){case 9:case 10:case 12:case 32:I=Ss;break;case 62:I=ge,Se();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Bt.push(p+32);break;case 0:Bt.push(65533);break;case-1:Ee(),Se(),fe();break;default:Bt.push(p);break}}function Ss(p,b,N){switch(p){case 9:case 10:case 12:case 32:te+=1;break;case 62:I=ge,te+=1,Se();break;case-1:Ee(),Se(),fe();break;default:b=b.toUpperCase(),b==="PUBLIC"?(te+=6,I=Gd):b==="SYSTEM"?(te+=6,I=Xd):(Ee(),I=Zt);break}}Ss.lookahead=6;function Gd(p){switch(p){case 9:case 10:case 12:case 32:I=$d;break;case 34:Cu(),I=Ts;break;case 39:Cu(),I=Ns;break;case 62:Ee(),I=ge,Se();break;case-1:Ee(),Se(),fe();break;default:Ee(),I=Zt;break}}function $d(p){switch(p){case 9:case 10:case 12:case 32:break;case 34:Cu(),I=Ts;break;case 39:Cu(),I=Ns;break;case 62:Ee(),I=ge,Se();break;case-1:Ee(),Se(),fe();break;default:Ee(),I=Zt;break}}function Ts(p){switch(p){case 34:I=As;break;case 0:Ft.push(65533);break;case 62:Ee(),I=ge,Se();break;case-1:Ee(),Se(),fe();break;default:Ft.push(p);break}}function Ns(p){switch(p){case 39:I=As;break;case 0:Ft.push(65533);break;case 62:Ee(),I=ge,Se();break;case-1:Ee(),Se(),fe();break;default:Ft.push(p);break}}function As(p){switch(p){case 9:case 10:case 12:case 32:I=Wd;break;case 62:I=ge,Se();break;case 34:zt(),I=Hu;break;case 39:zt(),I=Bu;break;case-1:Ee(),Se(),fe();break;default:Ee(),I=Zt;break}}function Wd(p){switch(p){case 9:case 10:case 12:case 32:break;case 62:I=ge,Se();break;case 34:zt(),I=Hu;break;case 39:zt(),I=Bu;break;case-1:Ee(),Se(),fe();break;default:Ee(),I=Zt;break}}function Xd(p){switch(p){case 9:case 10:case 12:case 32:I=Qd;break;case 34:zt(),I=Hu;break;case 39:zt(),I=Bu;break;case 62:Ee(),I=ge,Se();break;case-1:Ee(),Se(),fe();break;default:Ee(),I=Zt;break}}function Qd(p){switch(p){case 9:case 10:case 12:case 32:break;case 34:zt(),I=Hu;break;case 39:zt(),I=Bu;break;case 62:Ee(),I=ge,Se();break;case-1:Ee(),Se(),fe();break;default:Ee(),I=Zt;break}}function Hu(p){switch(p){case 34:I=Cs;break;case 0:Ut.push(65533);break;case 62:Ee(),I=ge,Se();break;case-1:Ee(),Se(),fe();break;default:Ut.push(p);break}}function Bu(p){switch(p){case 39:I=Cs;break;case 0:Ut.push(65533);break;case 62:Ee(),I=ge,Se();break;case-1:Ee(),Se(),fe();break;default:Ut.push(p);break}}function Cs(p){switch(p){case 9:case 10:case 12:case 32:break;case 62:I=ge,Se();break;case-1:Ee(),Se(),fe();break;default:I=Zt;break}}function Zt(p){switch(p){case 62:I=ge,Se();break;case-1:Se(),fe();break}}function oa(p){switch(p){case 93:I=Zd;break;case-1:fe();break;case 0:Vt=!0;default:Xr(zr)||Q.push(p);break}}function Zd(p){switch(p){case 93:I=Kd;break;default:Q.push(93),re(p,oa);break}}function Kd(p){switch(p){case 93:Q.push(93);break;case 62:Ir(),I=ge;break;default:Q.push(93),Q.push(93),re(p,oa);break}}function Yr(p){switch(Lt(),Ce.push(38),p){case 9:case 10:case 12:case 32:case 60:case 38:case-1:re(p,pr);break;case 35:Ce.push(p),I=Yd;break;default:re(p,ks);break}}function ks(p){rt.lastIndex=te;var b=rt.exec(ee);if(!b)throw new Error("should never happen");var N=b[1];if(!N){I=pr;return}switch(te+=N.length,l(Ce,ad(N)),Ht){case Iu:case Ou:case Ru:if(N[N.length-1]!==";"&&/[=A-Za-z0-9]/.test(ee[te])){I=pr;return}break}Lt();var R=me[N];typeof R=="number"?Ce.push(R):l(Ce,R),I=pr}ks.lookahead=-32;function Yd(p){switch(Fe=0,p){case 120:case 88:Ce.push(p),I=Jd;break;default:re(p,ef);break}}function Jd(p){switch(p){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 65:case 66:case 67:case 68:case 69:case 70:case 97:case 98:case 99:case 100:case 101:case 102:re(p,tf);break;default:re(p,pr);break}}function ef(p){switch(p){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:re(p,rf);break;default:re(p,pr);break}}function tf(p){switch(p){case 65:case 66:case 67:case 68:case 69:case 70:Fe*=16,Fe+=p-55;break;case 97:case 98:case 99:case 100:case 101:case 102:Fe*=16,Fe+=p-87;break;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:Fe*=16,Fe+=p-48;break;case 59:I=Fu;break;default:re(p,Fu);break}}function rf(p){switch(p){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:Fe*=10,Fe+=p-48;break;case 59:I=Fu;break;default:re(p,Fu);break}}function Fu(p){Fe in K?Fe=K[Fe]:(Fe>1114111||Fe>=55296&&Fe<57344)&&(Fe=65533),Lt(),Fe<=65535?Ce.push(Fe):(Fe=Fe-65536,Ce.push(55296+(Fe>>10)),Ce.push(56320+(Fe&1023))),re(p,pr)}function pr(p){switch(Ht){case Iu:case Ou:case Ru:at+=et(Ce);break;default:l(Q,Ce);break}re(p,Ht)}function uf(p,b,N,R){switch(p){case 1:if(b=b.replace(ir,""),b.length===0)return;break;case 4:_e._appendChild(_e.createComment(b));return;case 5:var U=b,Y=N,ie=R;_e.appendChild(new n(_e,U,Y,ie)),Qn||U.toLowerCase()!=="html"||A.test(Y)||ie&&ie.toLowerCase()===S||ie===void 0&&_.test(Y)?_e._quirks=!0:(x.test(Y)||ie!==void 0&&_.test(Y))&&(_e._limitedQuirks=!0),j=Ls;return}_e._quirks=!0,j=Ls,j(p,b,N,R)}function Ls(p,b,N,R){var U;switch(p){case 1:if(b=b.replace(ir,""),b.length===0)return;break;case 5:return;case 4:_e._appendChild(_e.createComment(b));return;case 2:if(b==="html"){U=Qr(_e,b,N),L.push(U),_e.appendChild(U),j=Uu;return}break;case 3:switch(b){case"html":case"head":case"body":case"br":break;default:return}}U=Qr(_e,"html",null),L.push(U),_e.appendChild(U),j=Uu,j(p,b,N,R)}function Uu(p,b,N,R){switch(p){case 1:if(b=b.replace(ir,""),b.length===0)return;break;case 5:return;case 4:yt(b);return;case 2:switch(b){case"html":ae(p,b,N,R);return;case"head":var U=le(b,N);Su=U,j=Ve;return}break;case 3:switch(b){case"html":case"head":case"body":case"br":break;default:return}}Uu(d,"head",null),j(p,b,N,R)}function Ve(p,b,N,R){switch(p){case 1:var U=b.match(ir);if(U&&(wt(U[0]),b=b.substring(U[0].length)),b.length===0)return;break;case 4:yt(b);return;case 5:return;case 2:switch(b){case"html":ae(p,b,N,R);return;case"meta":case"base":case"basefont":case"bgsound":case"link":le(b,N),L.pop();return;case"title":hd(b,N);return;case"noscript":if(!Tu){le(b,N),j=Ms;return}case"noframes":case"style":Mu(b,N);return;case"script":ku(function(Y){var ie=Qr(Y,b,N);return ie._parser_inserted=!0,ie._force_async=!1,lr&&(ie._already_started=!0),Ir(),ie}),I=Wt,At=j,j=Vu;return;case"template":le(b,N),be.insertMarker(),Ue=!1,j=da,Ct.push(j);return;case"head":return}break;case 3:switch(b){case"head":L.pop(),j=ca;return;case"body":case"html":case"br":break;case"template":if(!L.contains("template"))return;L.generateImpliedEndTags(null,"thorough"),L.popTag("template"),be.clearToMarker(),Ct.pop(),Zr();return;default:return}break}Ve(f,"head",null),j(p,b,N,R)}function Ms(p,b,N,R){switch(p){case 5:return;case 4:Ve(p,b);return;case 1:var U=b.match(ir);if(U&&(Ve(p,U[0]),b=b.substring(U[0].length)),b.length===0)return;break;case 2:switch(b){case"html":ae(p,b,N,R);return;case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"style":Ve(p,b,N);return;case"head":case"noscript":return}break;case 3:switch(b){case"noscript":L.pop(),j=Ve;return;case"br":break;default:return}break}Ms(f,"noscript",null),j(p,b,N,R)}function ca(p,b,N,R){switch(p){case 1:var U=b.match(ir);if(U&&(wt(U[0]),b=b.substring(U[0].length)),b.length===0)return;break;case 4:yt(b);return;case 5:return;case 2:switch(b){case"html":ae(p,b,N,R);return;case"body":le(b,N),Ue=!1,j=ae;return;case"frameset":le(b,N),j=fa;return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":L.push(Su),Ve(d,b,N),L.removeElement(Su);return;case"head":return}break;case 3:switch(b){case"template":return Ve(p,b,N,R);case"body":case"html":case"br":break;default:return}break}ca(d,"body",null),Ue=!0,j(p,b,N,R)}function ae(p,b,N,R){var U,Y,ie,ve;switch(p){case 1:if(Vt&&(b=b.replace(wu,""),b.length===0))return;Ue&&yu.test(b)&&(Ue=!1),ut(),wt(b);return;case 5:return;case 4:yt(b);return;case-1:if(Ct.length)return da(p);fr();return;case 2:switch(b){case"html":if(L.contains("template"))return;ps(N,L.elements[0]);return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":Ve(d,b,N);return;case"body":if(U=L.elements[1],!U||!(U instanceof s.HTMLBodyElement)||L.contains("template"))return;Ue=!1,ps(N,U);return;case"frameset":if(!Ue||(U=L.elements[1],!U||!(U instanceof s.HTMLBodyElement)))return;for(U.parentNode&&U.parentNode.removeChild(U);!(L.top instanceof s.HTMLHtmlElement);)L.pop();le(b,N),j=fa;return;case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"nav":case"ol":case"p":case"section":case"summary":case"ul":L.inButtonScope("p")&&ae(f,"p"),le(b,N);return;case"menu":L.inButtonScope("p")&&ae(f,"p"),Ne(L.top,"menuitem")&&L.pop(),le(b,N);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":L.inButtonScope("p")&&ae(f,"p"),L.top instanceof s.HTMLHeadingElement&&L.pop(),le(b,N);return;case"pre":case"listing":L.inButtonScope("p")&&ae(f,"p"),le(b,N),dr=!0,Ue=!1;return;case"form":if(kt&&!L.contains("template"))return;L.inButtonScope("p")&&ae(f,"p"),ve=le(b,N),L.contains("template")||(kt=ve);return;case"li":for(Ue=!1,Y=L.elements.length-1;Y>=0;Y--){if(ie=L.elements[Y],ie instanceof s.HTMLLIElement){ae(f,"li");break}if(Ne(ie,T)&&!Ne(ie,E))break}L.inButtonScope("p")&&ae(f,"p"),le(b,N);return;case"dd":case"dt":for(Ue=!1,Y=L.elements.length-1;Y>=0;Y--){if(ie=L.elements[Y],Ne(ie,O)){ae(f,ie.localName);break}if(Ne(ie,T)&&!Ne(ie,E))break}L.inButtonScope("p")&&ae(f,"p"),le(b,N);return;case"plaintext":L.inButtonScope("p")&&ae(f,"p"),le(b,N),I=ta;return;case"button":L.inScope("button")?(ae(f,"button"),j(p,b,N,R)):(ut(),le(b,N),Ue=!1);return;case"a":var Be=be.findElementByTag("a");Be&&(ae(f,b),be.remove(Be),L.removeElement(Be));case"b":case"big":case"code":case"em":case"font":case"i":case"s":case"small":case"strike":case"strong":case"tt":case"u":ut(),be.push(le(b,N),N);return;case"nobr":ut(),L.inScope(b)&&(ae(f,b),ut()),be.push(le(b,N),N);return;case"applet":case"marquee":case"object":ut(),le(b,N),be.insertMarker(),Ue=!1;return;case"table":!_e._quirks&&L.inButtonScope("p")&&ae(f,"p"),le(b,N),Ue=!1,j=lt;return;case"area":case"br":case"embed":case"img":case"keygen":case"wbr":ut(),le(b,N),L.pop(),Ue=!1;return;case"input":ut(),ve=le(b,N),L.pop();var Ze=ve.getAttribute("type");(!Ze||Ze.toLowerCase()!=="hidden")&&(Ue=!1);return;case"param":case"source":case"track":le(b,N),L.pop();return;case"hr":L.inButtonScope("p")&&ae(f,"p"),Ne(L.top,"menuitem")&&L.pop(),le(b,N),L.pop(),Ue=!1;return;case"image":ae(d,"img",N,R);return;case"textarea":le(b,N),dr=!0,Ue=!1,I=$t,At=j,j=Vu;return;case"xmp":L.inButtonScope("p")&&ae(f,"p"),ut(),Ue=!1,Mu(b,N);return;case"iframe":Ue=!1,Mu(b,N);return;case"noembed":Mu(b,N);return;case"select":ut(),le(b,N),Ue=!1,j===lt||j===la||j===mr||j===Jr||j===Or?j=zu:j=Dt;return;case"optgroup":case"option":L.top instanceof s.HTMLOptionElement&&ae(f,"option"),ut(),le(b,N);return;case"menuitem":Ne(L.top,"menuitem")&&L.pop(),ut(),le(b,N);return;case"rb":case"rtc":L.inScope("ruby")&&L.generateImpliedEndTags(),le(b,N);return;case"rp":case"rt":L.inScope("ruby")&&L.generateImpliedEndTags("rtc"),le(b,N);return;case"math":ut(),hs(N),Xn(N),Jn(b,N,a.MATHML),R&&L.pop();return;case"svg":ut(),fs(N),Xn(N),Jn(b,N,a.SVG),R&&L.pop();return;case"caption":case"col":case"colgroup":case"frame":case"head":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}ut(),le(b,N);return;case 3:switch(b){case"template":Ve(f,b,N);return;case"body":if(!L.inScope("body"))return;j=Ds;return;case"html":if(!L.inScope("body"))return;j=Ds,j(p,b,N);return;case"address":case"article":case"aside":case"blockquote":case"button":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"listing":case"main":case"menu":case"nav":case"ol":case"pre":case"section":case"summary":case"ul":if(!L.inScope(b))return;L.generateImpliedEndTags(),L.popTag(b);return;case"form":if(L.contains("template")){if(!L.inScope("form"))return;L.generateImpliedEndTags(),L.popTag("form")}else{var dt=kt;if(kt=null,!dt||!L.elementInScope(dt))return;L.generateImpliedEndTags(),L.removeElement(dt)}return;case"p":L.inButtonScope(b)?(L.generateImpliedEndTags(b),L.popTag(b)):(ae(d,b,null),j(p,b,N,R));return;case"li":if(!L.inListItemScope(b))return;L.generateImpliedEndTags(b),L.popTag(b);return;case"dd":case"dt":if(!L.inScope(b))return;L.generateImpliedEndTags(b),L.popTag(b);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":if(!L.elementTypeInScope(s.HTMLHeadingElement))return;L.generateImpliedEndTags(),L.popElementType(s.HTMLHeadingElement);return;case"sarcasm":break;case"a":case"b":case"big":case"code":case"em":case"font":case"i":case"nobr":case"s":case"small":case"strike":case"strong":case"tt":case"u":var mt=pd(b);if(mt)return;break;case"applet":case"marquee":case"object":if(!L.inScope(b))return;L.generateImpliedEndTags(),L.popTag(b),be.clearToMarker();return;case"br":ae(d,b,null);return}for(Y=L.elements.length-1;Y>=0;Y--)if(ie=L.elements[Y],Ne(ie,b)){L.generateImpliedEndTags(b),L.popElement(ie);break}else if(Ne(ie,T))return;return}}function Vu(p,b,N,R){switch(p){case 1:wt(b);return;case-1:L.top instanceof s.HTMLScriptElement&&(L.top._already_started=!0),L.pop(),j=At,j(p);return;case 3:b==="script"?md():(L.pop(),j=At);return;default:return}}function lt(p,b,N,R){function U(ie){for(var ve=0,Be=ie.length;ve<Be;ve++)if(ie[ve][0]==="type")return ie[ve][1].toLowerCase();return null}switch(p){case 1:if(Zn){ae(p,b,N,R);return}else if(Ne(L.top,B)){Nu=[],At=j,j=nf,j(p,b,N,R);return}break;case 4:yt(b);return;case 5:return;case 2:switch(b){case"caption":L.clearToContext(C),be.insertMarker(),le(b,N),j=la;return;case"colgroup":L.clearToContext(C),le(b,N),j=ju;return;case"col":lt(d,"colgroup",null),j(p,b,N,R);return;case"tbody":case"tfoot":case"thead":L.clearToContext(C),le(b,N),j=mr;return;case"td":case"th":case"tr":lt(d,"tbody",null),j(p,b,N,R);return;case"table":if(!L.inTableScope(b))return;lt(f,b),j(p,b,N,R);return;case"style":case"script":case"template":Ve(p,b,N,R);return;case"input":var Y=U(N);if(Y!=="hidden")break;le(b,N),L.pop();return;case"form":if(kt||L.contains("template"))return;kt=le(b,N),L.popElement(kt);return}break;case 3:switch(b){case"table":if(!L.inTableScope(b))return;L.popTag(b),Zr();return;case"body":case"caption":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return;case"template":Ve(p,b,N,R);return}break;case-1:ae(p,b,N,R);return}Gt=!0,ae(p,b,N,R),Gt=!1}function nf(p,b,N,R){if(p===c){if(Vt&&(b=b.replace(wu,""),b.length===0))return;Nu.push(b)}else{var U=Nu.join("");Nu.length=0,yu.test(U)?(Gt=!0,ae(c,U),Gt=!1):wt(U),j=At,j(p,b,N,R)}}function la(p,b,N,R){function U(){return L.inTableScope("caption")?(L.generateImpliedEndTags(),L.popTag("caption"),be.clearToMarker(),j=lt,!0):!1}switch(p){case 2:switch(b){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":U()&&j(p,b,N,R);return}break;case 3:switch(b){case"caption":U();return;case"table":U()&&j(p,b,N,R);return;case"body":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}break}ae(p,b,N,R)}function ju(p,b,N,R){switch(p){case 1:var U=b.match(ir);if(U&&(wt(U[0]),b=b.substring(U[0].length)),b.length===0)return;break;case 4:yt(b);return;case 5:return;case 2:switch(b){case"html":ae(p,b,N,R);return;case"col":le(b,N),L.pop();return;case"template":Ve(p,b,N,R);return}break;case 3:switch(b){case"colgroup":if(!Ne(L.top,"colgroup"))return;L.pop(),j=lt;return;case"col":return;case"template":Ve(p,b,N,R);return}break;case-1:ae(p,b,N,R);return}Ne(L.top,"colgroup")&&(ju(f,"colgroup"),j(p,b,N,R))}function mr(p,b,N,R){function U(){!L.inTableScope("tbody")&&!L.inTableScope("thead")&&!L.inTableScope("tfoot")||(L.clearToContext(P),mr(f,L.top.localName,null),j(p,b,N,R))}switch(p){case 2:switch(b){case"tr":L.clearToContext(P),le(b,N),j=Jr;return;case"th":case"td":mr(d,"tr",null),j(p,b,N,R);return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":U();return}break;case 3:switch(b){case"table":U();return;case"tbody":case"tfoot":case"thead":L.inTableScope(b)&&(L.clearToContext(P),L.pop(),j=lt);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":case"tr":return}break}lt(p,b,N,R)}function Jr(p,b,N,R){function U(){return L.inTableScope("tr")?(L.clearToContext(W),L.pop(),j=mr,!0):!1}switch(p){case 2:switch(b){case"th":case"td":L.clearToContext(W),le(b,N),j=Or,be.insertMarker();return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":case"tr":U()&&j(p,b,N,R);return}break;case 3:switch(b){case"tr":U();return;case"table":U()&&j(p,b,N,R);return;case"tbody":case"tfoot":case"thead":L.inTableScope(b)&&U()&&j(p,b,N,R);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":return}break}lt(p,b,N,R)}function Or(p,b,N,R){switch(p){case 2:switch(b){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":L.inTableScope("td")?(Or(f,"td"),j(p,b,N,R)):L.inTableScope("th")&&(Or(f,"th"),j(p,b,N,R));return}break;case 3:switch(b){case"td":case"th":if(!L.inTableScope(b))return;L.generateImpliedEndTags(),L.popTag(b),be.clearToMarker(),j=Jr;return;case"body":case"caption":case"col":case"colgroup":case"html":return;case"table":case"tbody":case"tfoot":case"thead":case"tr":if(!L.inTableScope(b))return;Or(f,L.inTableScope("td")?"td":"th"),j(p,b,N,R);return}break}ae(p,b,N,R)}function Dt(p,b,N,R){switch(p){case 1:if(Vt&&(b=b.replace(wu,""),b.length===0))return;wt(b);return;case 4:yt(b);return;case 5:return;case-1:ae(p,b,N,R);return;case 2:switch(b){case"html":ae(p,b,N,R);return;case"option":L.top instanceof s.HTMLOptionElement&&Dt(f,b),le(b,N);return;case"optgroup":L.top instanceof s.HTMLOptionElement&&Dt(f,"option"),L.top instanceof s.HTMLOptGroupElement&&Dt(f,b),le(b,N);return;case"select":Dt(f,b);return;case"input":case"keygen":case"textarea":if(!L.inSelectScope("select"))return;Dt(f,"select"),j(p,b,N,R);return;case"script":case"template":Ve(p,b,N,R);return}break;case 3:switch(b){case"optgroup":L.top instanceof s.HTMLOptionElement&&L.elements[L.elements.length-2]instanceof s.HTMLOptGroupElement&&Dt(f,"option"),L.top instanceof s.HTMLOptGroupElement&&L.pop();return;case"option":L.top instanceof s.HTMLOptionElement&&L.pop();return;case"select":if(!L.inSelectScope(b))return;L.popTag(b),Zr();return;case"template":Ve(p,b,N,R);return}break}}function zu(p,b,N,R){switch(b){case"caption":case"table":case"tbody":case"tfoot":case"thead":case"tr":case"td":case"th":switch(p){case 2:zu(f,"select"),j(p,b,N,R);return;case 3:L.inTableScope(b)&&(zu(f,"select"),j(p,b,N,R));return}}Dt(p,b,N,R)}function da(p,b,N,R){function U(Y){j=Y,Ct[Ct.length-1]=j,j(p,b,N,R)}switch(p){case 1:case 4:case 5:ae(p,b,N,R);return;case-1:L.contains("template")?(L.popTag("template"),be.clearToMarker(),Ct.pop(),Zr(),j(p,b,N,R)):fr();return;case 2:switch(b){case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":Ve(p,b,N,R);return;case"caption":case"colgroup":case"tbody":case"tfoot":case"thead":U(lt);return;case"col":U(ju);return;case"tr":U(mr);return;case"td":case"th":U(Jr);return}U(ae);return;case 3:switch(b){case"template":Ve(p,b,N,R);return;default:return}}}function Ds(p,b,N,R){switch(p){case 1:if(yu.test(b))break;ae(p,b);return;case 4:L.elements[0]._appendChild(_e.createComment(b));return;case 5:return;case-1:fr();return;case 2:if(b==="html"){ae(p,b,N,R);return}break;case 3:if(b==="html"){if(lr)return;j=sf;return}break}j=ae,j(p,b,N,R)}function fa(p,b,N,R){switch(p){case 1:b=b.replace(Wn,""),b.length>0&&wt(b);return;case 4:yt(b);return;case 5:return;case-1:fr();return;case 2:switch(b){case"html":ae(p,b,N,R);return;case"frameset":le(b,N);return;case"frame":le(b,N),L.pop();return;case"noframes":Ve(p,b,N,R);return}break;case 3:if(b==="frameset"){if(lr&&L.top instanceof s.HTMLHtmlElement)return;L.pop(),!lr&&!(L.top instanceof s.HTMLFrameSetElement)&&(j=af);return}break}}function af(p,b,N,R){switch(p){case 1:b=b.replace(Wn,""),b.length>0&&wt(b);return;case 4:yt(b);return;case 5:return;case-1:fr();return;case 2:switch(b){case"html":ae(p,b,N,R);return;case"noframes":Ve(p,b,N,R);return}break;case 3:if(b==="html"){j=of;return}break}}function sf(p,b,N,R){switch(p){case 1:if(yu.test(b))break;ae(p,b,N,R);return;case 4:_e._appendChild(_e.createComment(b));return;case 5:ae(p,b,N,R);return;case-1:fr();return;case 2:if(b==="html"){ae(p,b,N,R);return}break}j=ae,j(p,b,N,R)}function of(p,b,N,R){switch(p){case 1:b=b.replace(Wn,""),b.length>0&&ae(p,b,N,R);return;case 4:_e._appendChild(_e.createComment(b));return;case 5:ae(p,b,N,R);return;case-1:fr();return;case 2:switch(b){case"html":ae(p,b,N,R);return;case"noframes":Ve(p,b,N,R);return}break}}function Is(p,b,N,R){function U(Be){for(var Ze=0,dt=Be.length;Ze<dt;Ze++)switch(Be[Ze][0]){case"color":case"face":case"size":return!0}return!1}var Y;switch(p){case 1:Ue&&nd.test(b)&&(Ue=!1),Vt&&(b=b.replace(wu,"\uFFFD")),wt(b);return;case 4:yt(b);return;case 5:return;case 2:switch(b){case"font":if(!U(N))break;case"b":case"big":case"blockquote":case"body":case"br":case"center":case"code":case"dd":case"div":case"dl":case"dt":case"em":case"embed":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":case"head":case"hr":case"i":case"img":case"li":case"listing":case"menu":case"meta":case"nobr":case"ol":case"p":case"pre":case"ruby":case"s":case"small":case"span":case"strong":case"strike":case"sub":case"sup":case"table":case"tt":case"u":case"ul":case"var":if(lr)break;do L.pop(),Y=L.top;while(Y.namespaceURI!==a.HTML&&!ls(Y)&&!ds(Y));Ge(p,b,N,R);return}Y=L.elements.length===1&&lr?z:L.top,Y.namespaceURI===a.MATHML?hs(N):Y.namespaceURI===a.SVG&&(b=id(b),fs(N)),Xn(N),Jn(b,N,Y.namespaceURI),R&&L.pop();return;case 3:if(Y=L.top,b==="script"&&Y.namespaceURI===a.SVG&&Y.localName==="script")L.pop();else for(var ie=L.elements.length-1,ve=L.elements[ie];;){if(ve.localName.toLowerCase()===b){L.popElement(ve);break}if(ve=L.elements[--ie],ve.namespaceURI===a.HTML){j(p,b,N,R);break}}return}}return Au.testTokenizer=function(p,b,N,R){var U=[];switch(b){case"PCDATA state":I=ge;break;case"RCDATA state":I=$t;break;case"RAWTEXT state":I=Kr;break;case"PLAINTEXT state":I=ta;break}if(N&&(_u=N),Ge=function(ie,ve,Be,Ze){switch(Ir(),ie){case 1:U.length>0&&U[U.length-1][0]==="Character"?U[U.length-1][1]+=ve:U.push(["Character",ve]);break;case 4:U.push(["Comment",ve]);break;case 5:U.push(["DOCTYPE",ve,Be===void 0?null:Be,Ze===void 0?null:Ze,!Qn]);break;case 2:for(var dt=Object.create(null),mt=0;mt<Be.length;mt++){var br=Be[mt];br.length===1?dt[br[0]]="":dt[br[0]]=br[1]}var Kt=["StartTag",ve,dt];Ze&&Kt.push(!0),U.push(Kt);break;case 3:U.push(["EndTag",ve]);break}},!R)this.parse(p,!0);else{for(var Y=0;Y<p.length;Y++)this.parse(p[Y]);this.parse("",!0)}return U},Au}}}),cn=ne({"external/npm/node_modules/domino/lib/DOMImplementation.js"(t,e){e.exports=s;var r=Aa(),n=Ca(),u=ka(),a=$e(),i=xa();function s(o){this.contextObject=o}var l={xml:{"":!0,"1.0":!0,"2.0":!0},core:{"":!0,"2.0":!0},html:{"":!0,"1.0":!0,"2.0":!0},xhtml:{"":!0,"1.0":!0,"2.0":!0}};s.prototype={hasFeature:function(c,d){var f=l[(c||"").toLowerCase()];return f&&f[d||""]||!1},createDocumentType:function(c,d,f){return i.isValidQName(c)||a.InvalidCharacterError(),new n(this.contextObject,c,d,f)},createDocument:function(c,d,f){var y=new r(!1,null),v;return d?v=y.createElementNS(c,d):v=null,f&&y.appendChild(f),v&&y.appendChild(v),c===a.NAMESPACE.HTML?y._contentType="application/xhtml+xml":c===a.NAMESPACE.SVG?y._contentType="image/svg+xml":y._contentType="application/xml",y},createHTMLDocument:function(c){var d=new r(!0,null);d.appendChild(new n(d,"html"));var f=d.createElement("html");d.appendChild(f);var y=d.createElement("head");if(f.appendChild(y),c!==void 0){var v=d.createElement("title");y.appendChild(v),v.appendChild(d.createTextNode(c))}return f.appendChild(d.createElement("body")),d.modclock=1,d},mozSetOutputMutationHandler:function(o,c){o.mutationHandler=c},mozGetInputMutationHandler:function(o){a.nyi()},mozHTMLParser:u}}}),yf=ne({"external/npm/node_modules/domino/lib/Location.js"(t,e){var r=Sa(),n=_c();e.exports=u;function u(a,i){this._window=a,this._href=i}u.prototype=Object.create(n.prototype,{constructor:{value:u},href:{get:function(){return this._href},set:function(a){this.assign(a)}},assign:{value:function(a){var i=new r(this._href),s=i.resolve(a);this._href=s}},replace:{value:function(a){this.assign(a)}},reload:{value:function(){this.assign(this.href)}},toString:{value:function(){return this.href}}})}}),wf=ne({"external/npm/node_modules/domino/lib/NavigatorID.js"(t,e){var r=Object.create(null,{appCodeName:{value:"Mozilla"},appName:{value:"Netscape"},appVersion:{value:"4.0"},platform:{value:""},product:{value:"Gecko"},productSub:{value:"20100101"},userAgent:{value:""},vendor:{value:""},vendorSub:{value:""},taintEnabled:{value:function(){return!1}}});e.exports=r}}),xf=ne({"external/npm/node_modules/domino/lib/WindowTimers.js"(t,e){var r={setTimeout,clearTimeout,setInterval,clearInterval};e.exports=r}}),Tc=ne({"external/npm/node_modules/domino/lib/impl.js"(t,e){var r=$e();t=e.exports={CSSStyleDeclaration:Ta(),CharacterData:sn(),Comment:gc(),DOMImplementation:cn(),DOMTokenList:fc(),Document:Aa(),DocumentFragment:vc(),DocumentType:Ca(),Element:su(),HTMLParser:ka(),NamedNodeMap:pc(),Node:tt(),NodeList:qr(),NodeFilter:on(),ProcessingInstruction:yc(),Text:bc(),Window:Nc()},r.merge(t,xc()),r.merge(t,Na().elements),r.merge(t,Sc().elements)}}),Nc=ne({"external/npm/node_modules/domino/lib/Window.js"(t,e){var r=cn(),n=oc(),u=yf(),a=$e();e.exports=i;function i(s){this.document=s||new r(null).createHTMLDocument(""),this.document._scripting_enabled=!0,this.document.defaultView=this,this.location=new u(this,this.document._address||"about:blank")}i.prototype=Object.create(n.prototype,{console:{value:console},history:{value:{back:a.nyi,forward:a.nyi,go:a.nyi}},navigator:{value:wf()},window:{get:function(){return this}},self:{get:function(){return this}},frames:{get:function(){return this}},parent:{get:function(){return this}},top:{get:function(){return this}},length:{value:0},frameElement:{value:null},opener:{value:null},onload:{get:function(){return this._getEventHandler("load")},set:function(s){this._setEventHandler("load",s)}},getComputedStyle:{value:function(l){return l.style}}}),a.expose(xf(),i),a.expose(Tc(),i)}}),_f=ne({"external/npm/node_modules/domino/lib/index.js"(t){var e=cn(),r=ka();Nc();var n=Tc();t.createDOMImplementation=function(){return new e(null)},t.createDocument=function(u,a){if(u||a){var i=new r;return i.parse(u||"",!0),i.document()}return new e(null).createHTMLDocument("")},t.createIncrementalHTMLParser=function(){var u=new r;return{write:function(a){a.length>0&&u.parse(a,!1,function(){return!0})},end:function(a){u.parse(a||"",!0,function(){return!0})},process:function(a){return u.parse("",!1,a)},document:function(){return u.document()}}},t.createWindow=function(u,a){var i=t.createDocument(u);return a!==void 0&&(i._address=a),new n.Window(i)},t.impl=n}}),an=_f();function Ef(){Object.assign(globalThis,an.impl),globalThis.KeyboardEvent=an.impl.Event}function Ac(t,e="/"){return an.createWindow(t,e).document}function Sf(t){return t.serialize()}var Cc=(()=>{class t extends ba{static makeCurrent(){Ef(),To(new t)}supportsDOMEvents=!1;static defaultDoc;createHtmlDocument(){return Ac("<html><head><title>fakeTitle</title></head><body></body></html>")}getDefaultDocument(){return t.defaultDoc||(t.defaultDoc=an.createDocument()),t.defaultDoc}isElementNode(r){return r?r.nodeType===t.defaultDoc.ELEMENT_NODE:!1}isShadowRoot(r){return r.shadowRoot==r}getGlobalEventTarget(r,n){return n==="window"?r.defaultView:n==="document"?r:n==="body"?r.body:null}getBaseHref(r){let n=r.head.children.length;for(let u=0;u<n;u++){let a=r.head.children[u];if(a.tagName==="BASE")return a.getAttribute("href")||""}return""}dispatchEvent(r,n){r.dispatchEvent(n);let a=(r.ownerDocument||r).defaultView;a&&a.dispatchEvent(n)}getUserAgent(){return"Fake user agent"}getCookie(r){throw new Error("getCookie has not been implemented")}}return t})(),_r=new St("Server.INITIAL_CONFIG"),La=new St("Server.RENDER_MODULE_HOOK"),kc=new St("ENABLE_DOM_EMULATION"),Ma=(()=>{class t{_doc;_enableDomEmulation=Da(qe(nu));constructor(r){this._doc=r}renderToString(){let r="renderToString";ft(r);let n=this._enableDomEmulation?Sf(this._doc):this._doc.documentElement.outerHTML;return ht(r),n}getDocument(){return this._doc}static \u0275fac=function(n){return new(n||t)(ru(tr))};static \u0275prov=tu({token:t,factory:t.\u0275fac})}return t})();function Da(t){return t.get(kc,!0)}var Tf=(()=>{class t{xhrImpl;\u0275loadImpl(){return ze(this,null,function*(){if(!this.xhrImpl){let{default:r}=yield import("./chunk-FE3ZEMPP.mjs");this.xhrImpl=r}})}build(){let r=this.xhrImpl;if(!r)throw new Error("Unexpected state in ServerXhr: XHR implementation is not loaded.");return new r.XMLHttpRequest}static \u0275fac=function(n){return new(n||t)};static \u0275prov=tu({token:t,factory:t.\u0275fac})}return t})();function Nf(t,e){let r=qe(au),{href:n,protocol:u,hostname:a,port:i}=r;if(!u.startsWith("http"))return e(t);let s=`${u}//${a}`;i&&(s+=`:${i}`);let l=r.getBaseHrefFromDOM()||n,o=new URL(l,s),c=new URL(t.url,o).toString();return e(t.clone({url:c}))}var Af=[{provide:Lo,useClass:Tf},{provide:Ro,useValue:Nf,multi:!0}],nc="resolve:";function ac(t){let{hostname:e,protocol:r,port:n,pathname:u,search:a,hash:i}=new URL(t,nc+"//");return{hostname:e,protocol:r===nc?"":r,port:n,pathname:u,search:a,hash:i}}var Cf=(()=>{class t{_doc;href="/";hostname="/";protocol="/";port="/";pathname="/";search="";hash="";_hashUpdate=new qs;constructor(r,n){this._doc=r;let u=n;if(u&&u.url){let a=ac(u.url);this.protocol=a.protocol,this.hostname=a.hostname,this.port=a.port,this.pathname=a.pathname,this.search=a.search,this.hash=a.hash,this.href=r.location.href}}getBaseHrefFromDOM(){return tn().getBaseHref(this._doc)}onPopState(r){return()=>{}}onHashChange(r){let n=this._hashUpdate.subscribe(r);return()=>n.unsubscribe()}get url(){return`${this.pathname}${this.search}${this.hash}`}setHash(r,n){if(this.hash===r)return;this.hash=r;let u=this.url;queueMicrotask(()=>this._hashUpdate.next({type:"hashchange",state:null,oldUrl:n,newUrl:u}))}replaceState(r,n,u){let a=this.url,i=ac(u);this.pathname=i.pathname,this.search=i.search,this.setHash(i.hash,a)}pushState(r,n,u){this.replaceState(r,n,u)}forward(){throw new Error("Not implemented")}back(){throw new Error("Not implemented")}getState(){}static \u0275fac=function(n){return new(n||t)(ru(tr),ru(_r,8))};static \u0275prov=tu({token:t,factory:t.\u0275fac})}return t})(),kf=(()=>{class t extends Oo{doc;constructor(r){super(r),this.doc=r}supports(r){return!0}addEventListener(r,n,u,a){return tn().onAndCancel(r,n,u,a)}static \u0275fac=function(n){return new(n||t)(ru(tr))};static \u0275prov=tu({token:t,factory:t.\u0275fac})}return t})();var Lf=[{provide:La,useFactory:Mf,multi:!0}];function Ia(t,e,r){let n=t.createElement("script");return n.textContent=e,r&&n.setAttribute("nonce",r),n}function Mf(){let t=qe(tr),e=qe($u),r=qe(Xs),n=qe(nu);return()=>{let u="serializeTransferStateFactory";ft(u);let a=r.toJson();if(r.isEmpty)return;let i=Ia(t,a,null);i.id=e+"-state",i.setAttribute("type","application/json"),t.body.appendChild(i),ht(u)}}var Lc=[{provide:tr,useFactory:If,deps:[nu]},{provide:Wu,useValue:Co},{provide:Gs,useFactory:Df,multi:!0,deps:[nu]},{provide:au,useClass:Cf,deps:[tr,[Hs,_r]]},{provide:Ma,deps:[tr]},{provide:go,useValue:!0}];function Df(t){let e=Da(t);return()=>{e?Cc.makeCurrent():ba.makeCurrent()}}var Mc=[{provide:Io,multi:!0,useClass:kf}],Dc=[Lf,Mc,Af,{provide:eo,useValue:null},{provide:Js,useValue:null},{provide:Mo,useClass:Do}],Oa=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275mod=Tt({type:t});static \u0275inj=Et({providers:Dc,imports:[rn]})}return t})();function If(t){let e=t.get(_r,null),r=Da(t),n;return e&&e.document?n=typeof e.document=="string"?r?Ac(e.document,e.url):window.document:e.document:n=tn().createHtmlDocument(),zs(n),n}function ln(t){return vo(yo,"server",Lc)(t)}var Rf="ng-event-dispatch-contract";function Oc(t){let e=t.platformProviders??[],r="createServerPlatform";ft(r);let n=ln([{provide:_r,useValue:{document:t.document,url:t.url}},e]);return ht(r),n}function Rc(t){return t.getElementById(Rf)}function Ic(t){Rc(t)?.remove()}function qf(t,e){let r="prepareForHydration";ft(r);let n=e.injector,u=t.getDocument();if(!n.get(Qs,!1)){Ic(u);return}Pf(u);let a=wo(e,u);a.regular.size||a.capture.size?Bf(n.get($u),u,a,n.get(Ws,null)):Ic(u),ht(r)}function Pf(t){let e=t.createComment(Zs);t.body.firstChild?t.body.insertBefore(e,t.body.firstChild):t.body.append(e)}function Hf(t){let e=t.injector,r=Ff(e.get(Ra,Hc));t.components.forEach(n=>{let u=n.injector.get(Ys),a=n.location.nativeElement;a&&u.setAttribute(a,"ng-server-context",r)})}function Bf(t,e,r,n){let u="insertEventRecordScript";ft(u);let{regular:a,capture:i}=r,s=Rc(e);if(s){let l=`window.__jsaction_bootstrap(document.body,"${t}",${JSON.stringify(Array.from(a))},${JSON.stringify(Array.from(i))});`,o=Ia(e,l,n);s.after(o)}ht(u)}function qc(t,e){return ze(this,null,function*(){let r=t.injector.get(Ma);qf(r,e),Hf(e);let u=e.injector.get(La,null);if(u){let a=[];for(let i of u)try{let s=i();s&&a.push(s)}catch(s){console.warn("Ignoring BEFORE_APP_SERIALIZED Exception: ",s)}if(a.length)for(let i of yield Promise.allSettled(a))i.status==="rejected"&&console.warn("Ignoring BEFORE_APP_SERIALIZED Exception: ",i.reason)}return r.renderToString()})}function Pc(t){return new Promise(e=>{setTimeout(()=>{t.destroy(),e()},0)})}var Hc="other",Ra=new St("SERVER_CONTEXT");function Ff(t){let e=t.replace(/[^a-zA-Z0-9\-]/g,"");return e.length>0?e:Hc}function Bc(t,e){return ze(this,null,function*(){let{document:r,url:n,extraProviders:u}=e,a=Oc({document:r,url:n,platformProviders:u});try{let s=(yield a.bootstrapModule(t)).injector.get(Ku),l="whenStable";return ft(l),yield s.whenStable(),ht(l),yield qc(a,s)}finally{yield Pc(a)}})}function Fc(t,e){return ze(this,null,function*(){let r="renderApplication",n="bootstrap",u="_render";ft(r);let a=Oc(e);try{ft(n);let i=yield t();ht(n),ft(u);let s="whenStable";ft(s),yield i.whenStable(),ht(s);let l=yield qc(a,i);return ht(u),l}finally{yield Pc(a),ht(r)}})}function Uf(t){throw new Error(`[unenv] ${t} is not implemented yet!`)}function Vf(t){return Object.assign(()=>{throw Uf(t)},{__unenv__:!0})}var jf=Vf("fs.readFile");function zf(t){let e=function(...r){let n=r.pop();t().catch(u=>n(u)).then(u=>n(void 0,u))};return e.__promisify__=t,e.native=e,e}var Gf=zf(jf),$f=/^[A-Za-z]:\//;function ar(t=""){return t&&t.replace(/\\/g,"/").replace($f,e=>e.toUpperCase())}var Wf=/^[/\\]{2}/,Xf=/^[/\\](?![/\\])|^[/\\]{2}(?!\.)|^[A-Za-z]:[/\\]/,j0=/^[A-Za-z]:$/,Uc=/^\/([A-Za-z]:)?$/,z0="/",G0=":",Mi=function(t){if(t.length===0)return".";t=ar(t);let e=t.match(Wf),r=Tr(t),n=t[t.length-1]==="/";return t=An(t,!r),t.length===0?r?"/":n?"./":".":(n&&(t+="/"),j0.test(t)&&(t+="/"),e?r?`//${t}`:`//./${t}`:r&&!Tr(t)?`/${t}`:t)},$0=function(...t){if(t.length===0)return".";let e;for(let r of t)r&&r.length>0&&(e===void 0?e=r:e+=`/${r}`);return e===void 0?".":Mi(e.replace(/\/\/+/g,"/"))};function Qf(){return typeof process<"u"&&typeof process.cwd=="function"?process.cwd().replace(/\\/g,"/"):"/"}var du=function(...t){t=t.map(n=>ar(n));let e="",r=!1;for(let n=t.length-1;n>=-1&&!r;n--){let u=n>=0?t[n]:Qf();!u||u.length===0||(e=`${u}/${e}`,r=Tr(u))}return e=An(e,!r),r&&!Tr(e)?`/${e}`:e.length>0?e:"."};function An(t,e){let r="",n=0,u=-1,a=0,i=null;for(let s=0;s<=t.length;++s){if(s<t.length)i=t[s];else{if(i==="/")break;i="/"}if(i==="/"){if(!(u===s-1||a===1))if(a===2){if(r.length<2||n!==2||r[r.length-1]!=="."||r[r.length-2]!=="."){if(r.length>2){let l=r.lastIndexOf("/");l===-1?(r="",n=0):(r=r.slice(0,l),n=r.length-1-r.lastIndexOf("/")),u=s,a=0;continue}else if(r.length>0){r="",n=0,u=s,a=0;continue}}e&&(r+=r.length>0?"/..":"..",n=2)}else r.length>0?r+=`/${t.slice(u+1,s)}`:r=t.slice(u+1,s),n=s-u-1;u=s,a=0}else i==="."&&a!==-1?++a:a=-1}return r}var Tr=function(t){return Xf.test(t)},W0=function(t){return ar(t)},Zf=/.(\.[^./]+)$/,Di=function(t){let e=Zf.exec(ar(t));return e&&e[1]||""},X0=function(t,e){let r=du(t).replace(Uc,"$1").split("/"),n=du(e).replace(Uc,"$1").split("/");if(n[0][1]===":"&&r[0][1]===":"&&r[0]!==n[0])return n.join("/");let u=[...r];for(let a of u){if(n[0]!==a)break;r.shift(),n.shift()}return[...r.map(()=>".."),...n].join("/")},Ii=function(t){let e=ar(t).replace(/\/$/,"").split("/").slice(0,-1);return e.length===1&&j0.test(e[0])&&(e[0]+="/"),e.join("/")||(Tr(t)?"/":".")},Q0=function(t){let e=[t.root,t.dir,t.base??t.name+t.ext].filter(Boolean);return ar(t.root?du(...e):e.join("/"))},Oi=function(t,e){let r=ar(t).split("/").pop();return e&&r.endsWith(e)?r.slice(0,-e.length):r},Z0=function(t){let e=ar(t).split("/").shift()||"/",r=Oi(t),n=Di(r);return{root:e,dir:Ii(t),base:r,ext:n,name:r.slice(0,r.length-n.length)}},Kf={__proto__:null,basename:Oi,delimiter:G0,dirname:Ii,extname:Di,format:Q0,isAbsolute:Tr,join:$0,normalize:Mi,normalizeString:An,parse:Z0,relative:X0,resolve:du,sep:z0,toNamespacedPath:W0},Yf=Object.freeze({__proto__:null,basename:Oi,default:Kf,delimiter:G0,dirname:Ii,extname:Di,format:Q0,isAbsolute:Tr,join:$0,normalize:Mi,normalizeString:An,parse:Z0,relative:X0,resolve:du,sep:z0,toNamespacedPath:W0}),Br=je(ye({},Yf),{platform:"posix",posix:void 0,win32:void 0});Br.posix=Br;Br.win32=Br;function Cn(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Jf(t){if(Object.prototype.hasOwnProperty.call(t,"__esModule"))return t;var e=t.default;if(typeof e=="function"){var r=function n(){return this instanceof n?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};r.prototype=e.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach(function(n){var u=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(r,n,u.get?u:{enumerable:!0,get:function(){return t[n]}})}),r}var dn={exports:{}},Vc;function K0(){if(Vc)return dn.exports;Vc=1;var t=String,e=function(){return{isColorSupported:!1,reset:t,bold:t,dim:t,italic:t,underline:t,inverse:t,hidden:t,strikethrough:t,black:t,red:t,green:t,yellow:t,blue:t,magenta:t,cyan:t,white:t,gray:t,bgBlack:t,bgRed:t,bgGreen:t,bgYellow:t,bgBlue:t,bgMagenta:t,bgCyan:t,bgWhite:t,blackBright:t,redBright:t,greenBright:t,yellowBright:t,blueBright:t,magentaBright:t,cyanBright:t,whiteBright:t,bgBlackBright:t,bgRedBright:t,bgGreenBright:t,bgYellowBright:t,bgBlueBright:t,bgMagentaBright:t,bgCyanBright:t,bgWhiteBright:t}};return dn.exports=e(),dn.exports.createColors=e,dn.exports}var e1={},t1=Object.freeze({__proto__:null,default:e1}),gt=Jf(t1),qa,jc;function Ri(){if(jc)return qa;jc=1;let t=K0(),e=gt;class r extends Error{constructor(u,a,i,s,l,o){super(u),this.name="CssSyntaxError",this.reason=u,l&&(this.file=l),s&&(this.source=s),o&&(this.plugin=o),typeof a<"u"&&typeof i<"u"&&(typeof a=="number"?(this.line=a,this.column=i):(this.line=a.line,this.column=a.column,this.endLine=i.line,this.endColumn=i.column)),this.setMessage(),Error.captureStackTrace&&Error.captureStackTrace(this,r)}setMessage(){this.message=this.plugin?this.plugin+": ":"",this.message+=this.file?this.file:"<css input>",typeof this.line<"u"&&(this.message+=":"+this.line+":"+this.column),this.message+=": "+this.reason}showSourceCode(u){if(!this.source)return"";let a=this.source;u==null&&(u=t.isColorSupported);let i=y=>y,s=y=>y,l=y=>y;if(u){let{bold:y,gray:v,red:w}=t.createColors(!0);s=A=>y(w(A)),i=A=>v(A),e&&(l=A=>e(A))}let o=a.split(/\r?\n/),c=Math.max(this.line-3,0),d=Math.min(this.line+2,o.length),f=String(d).length;return o.slice(c,d).map((y,v)=>{let w=c+1+v,A=" "+(" "+w).slice(-f)+" | ";if(w===this.line){if(y.length>160){let _=20,x=Math.max(0,this.column-_),T=Math.max(this.column+_,this.endColumn+_),E=y.slice(x,T),O=i(A.replace(/\d/g," "))+y.slice(0,Math.min(this.column-1,_-1)).replace(/[^\t]/g," ");return s(">")+i(A)+l(E)+`
 `+O+s("^")}let S=i(A.replace(/\d/g," "))+y.slice(0,this.column-1).replace(/[^\t]/g," ");return s(">")+i(A)+l(y)+`
 `+S+s("^")}return" "+i(A)+l(y)}).join(`
`)}toString(){let u=this.showSourceCode();return u&&(u=`

`+u+`
`),this.name+": "+this.message+u}}return qa=r,r.default=r,qa}var Pa,zc;function Y0(){if(zc)return Pa;zc=1;let t={after:`
`,beforeClose:`
`,beforeComment:`
`,beforeDecl:`
`,beforeOpen:" ",beforeRule:`
`,colon:": ",commentLeft:" ",commentRight:" ",emptyBody:"",indent:"    ",semicolon:!1};function e(n){return n[0].toUpperCase()+n.slice(1)}class r{constructor(u){this.builder=u}atrule(u,a){let i="@"+u.name,s=u.params?this.rawValue(u,"params"):"";if(typeof u.raws.afterName<"u"?i+=u.raws.afterName:s&&(i+=" "),u.nodes)this.block(u,i+s);else{let l=(u.raws.between||"")+(a?";":"");this.builder(i+s+l,u)}}beforeAfter(u,a){let i;u.type==="decl"?i=this.raw(u,null,"beforeDecl"):u.type==="comment"?i=this.raw(u,null,"beforeComment"):a==="before"?i=this.raw(u,null,"beforeRule"):i=this.raw(u,null,"beforeClose");let s=u.parent,l=0;for(;s&&s.type!=="root";)l+=1,s=s.parent;if(i.includes(`
`)){let o=this.raw(u,null,"indent");if(o.length)for(let c=0;c<l;c++)i+=o}return i}block(u,a){let i=this.raw(u,"between","beforeOpen");this.builder(a+i+"{",u,"start");let s;u.nodes&&u.nodes.length?(this.body(u),s=this.raw(u,"after")):s=this.raw(u,"after","emptyBody"),s&&this.builder(s),this.builder("}",u,"end")}body(u){let a=u.nodes.length-1;for(;a>0&&u.nodes[a].type==="comment";)a-=1;let i=this.raw(u,"semicolon");for(let s=0;s<u.nodes.length;s++){let l=u.nodes[s],o=this.raw(l,"before");o&&this.builder(o),this.stringify(l,a!==s||i)}}comment(u){let a=this.raw(u,"left","commentLeft"),i=this.raw(u,"right","commentRight");this.builder("/*"+a+u.text+i+"*/",u)}decl(u,a){let i=this.raw(u,"between","colon"),s=u.prop+i+this.rawValue(u,"value");u.important&&(s+=u.raws.important||" !important"),a&&(s+=";"),this.builder(s,u)}document(u){this.body(u)}raw(u,a,i){let s;if(i||(i=a),a&&(s=u.raws[a],typeof s<"u"))return s;let l=u.parent;if(i==="before"&&(!l||l.type==="root"&&l.first===u||l&&l.type==="document"))return"";if(!l)return t[i];let o=u.root();if(o.rawCache||(o.rawCache={}),typeof o.rawCache[i]<"u")return o.rawCache[i];if(i==="before"||i==="after")return this.beforeAfter(u,i);{let c="raw"+e(i);this[c]?s=this[c](o,u):o.walk(d=>{if(s=d.raws[a],typeof s<"u")return!1})}return typeof s>"u"&&(s=t[i]),o.rawCache[i]=s,s}rawBeforeClose(u){let a;return u.walk(i=>{if(i.nodes&&i.nodes.length>0&&typeof i.raws.after<"u")return a=i.raws.after,a.includes(`
`)&&(a=a.replace(/[^\n]+$/,"")),!1}),a&&(a=a.replace(/\S/g,"")),a}rawBeforeComment(u,a){let i;return u.walkComments(s=>{if(typeof s.raws.before<"u")return i=s.raws.before,i.includes(`
`)&&(i=i.replace(/[^\n]+$/,"")),!1}),typeof i>"u"?i=this.raw(a,null,"beforeDecl"):i&&(i=i.replace(/\S/g,"")),i}rawBeforeDecl(u,a){let i;return u.walkDecls(s=>{if(typeof s.raws.before<"u")return i=s.raws.before,i.includes(`
`)&&(i=i.replace(/[^\n]+$/,"")),!1}),typeof i>"u"?i=this.raw(a,null,"beforeRule"):i&&(i=i.replace(/\S/g,"")),i}rawBeforeOpen(u){let a;return u.walk(i=>{if(i.type!=="decl"&&(a=i.raws.between,typeof a<"u"))return!1}),a}rawBeforeRule(u){let a;return u.walk(i=>{if(i.nodes&&(i.parent!==u||u.first!==i)&&typeof i.raws.before<"u")return a=i.raws.before,a.includes(`
`)&&(a=a.replace(/[^\n]+$/,"")),!1}),a&&(a=a.replace(/\S/g,"")),a}rawColon(u){let a;return u.walkDecls(i=>{if(typeof i.raws.between<"u")return a=i.raws.between.replace(/[^\s:]/g,""),!1}),a}rawEmptyBody(u){let a;return u.walk(i=>{if(i.nodes&&i.nodes.length===0&&(a=i.raws.after,typeof a<"u"))return!1}),a}rawIndent(u){if(u.raws.indent)return u.raws.indent;let a;return u.walk(i=>{let s=i.parent;if(s&&s!==u&&s.parent&&s.parent===u&&typeof i.raws.before<"u"){let l=i.raws.before.split(`
`);return a=l[l.length-1],a=a.replace(/\S/g,""),!1}}),a}rawSemicolon(u){let a;return u.walk(i=>{if(i.nodes&&i.nodes.length&&i.last.type==="decl"&&(a=i.raws.semicolon,typeof a<"u"))return!1}),a}rawValue(u,a){let i=u[a],s=u.raws[a];return s&&s.value===i?s.raw:i}root(u){this.body(u),u.raws.after&&this.builder(u.raws.after)}rule(u){this.block(u,this.rawValue(u,"selector")),u.raws.ownSemicolon&&this.builder(u.raws.ownSemicolon,u,"end")}stringify(u,a){if(!this[u.type])throw new Error("Unknown AST node type "+u.type+". Maybe you need to change PostCSS stringifier.");this[u.type](u,a)}}return Pa=r,r.default=r,Pa}var Ha,Gc;function kn(){if(Gc)return Ha;Gc=1;let t=Y0();function e(r,n){new t(n).stringify(r)}return Ha=e,e.default=e,Ha}var fn={},$c;function qi(){return $c||($c=1,fn.isClean=Symbol("isClean"),fn.my=Symbol("my")),fn}var Ba,Wc;function Ln(){if(Wc)return Ba;Wc=1;let t=Ri(),e=Y0(),r=kn(),{isClean:n,my:u}=qi();function a(l,o){let c=new l.constructor;for(let d in l){if(!Object.prototype.hasOwnProperty.call(l,d)||d==="proxyCache")continue;let f=l[d],y=typeof f;d==="parent"&&y==="object"?o&&(c[d]=o):d==="source"?c[d]=f:Array.isArray(f)?c[d]=f.map(v=>a(v,c)):(y==="object"&&f!==null&&(f=a(f)),c[d]=f)}return c}function i(l,o){if(o&&typeof o.offset<"u")return o.offset;let c=1,d=1,f=0;for(let y=0;y<l.length;y++){if(d===o.line&&c===o.column){f=y;break}l[y]===`
`?(c=1,d+=1):c+=1}return f}class s{constructor(o={}){this.raws={},this[n]=!1,this[u]=!0;for(let c in o)if(c==="nodes"){this.nodes=[];for(let d of o[c])typeof d.clone=="function"?this.append(d.clone()):this.append(d)}else this[c]=o[c]}addToError(o){if(o.postcssNode=this,o.stack&&this.source&&/\n\s{4}at /.test(o.stack)){let c=this.source;o.stack=o.stack.replace(/\n\s{4}at /,`$&${c.input.from}:${c.start.line}:${c.start.column}$&`)}return o}after(o){return this.parent.insertAfter(this,o),this}assign(o={}){for(let c in o)this[c]=o[c];return this}before(o){return this.parent.insertBefore(this,o),this}cleanRaws(o){delete this.raws.before,delete this.raws.after,o||delete this.raws.between}clone(o={}){let c=a(this);for(let d in o)c[d]=o[d];return c}cloneAfter(o={}){let c=this.clone(o);return this.parent.insertAfter(this,c),c}cloneBefore(o={}){let c=this.clone(o);return this.parent.insertBefore(this,c),c}error(o,c={}){if(this.source){let{end:d,start:f}=this.rangeBy(c);return this.source.input.error(o,{column:f.column,line:f.line},{column:d.column,line:d.line},c)}return new t(o)}getProxyProcessor(){return{get(o,c){return c==="proxyOf"?o:c==="root"?()=>o.root().toProxy():o[c]},set(o,c,d){return o[c]===d||(o[c]=d,(c==="prop"||c==="value"||c==="name"||c==="params"||c==="important"||c==="text")&&o.markDirty()),!0}}}markClean(){this[n]=!0}markDirty(){if(this[n]){this[n]=!1;let o=this;for(;o=o.parent;)o[n]=!1}}next(){if(!this.parent)return;let o=this.parent.index(this);return this.parent.nodes[o+1]}positionBy(o){let c=this.source.start;if(o.index)c=this.positionInside(o.index);else if(o.word){let d="document"in this.source.input?this.source.input.document:this.source.input.css,y=d.slice(i(d,this.source.start),i(d,this.source.end)).indexOf(o.word);y!==-1&&(c=this.positionInside(y))}return c}positionInside(o){let c=this.source.start.column,d=this.source.start.line,f="document"in this.source.input?this.source.input.document:this.source.input.css,y=i(f,this.source.start),v=y+o;for(let w=y;w<v;w++)f[w]===`
`?(c=1,d+=1):c+=1;return{column:c,line:d}}prev(){if(!this.parent)return;let o=this.parent.index(this);return this.parent.nodes[o-1]}rangeBy(o){let c={column:this.source.start.column,line:this.source.start.line},d=this.source.end?{column:this.source.end.column+1,line:this.source.end.line}:{column:c.column+1,line:c.line};if(o.word){let f="document"in this.source.input?this.source.input.document:this.source.input.css,v=f.slice(i(f,this.source.start),i(f,this.source.end)).indexOf(o.word);v!==-1&&(c=this.positionInside(v),d=this.positionInside(v+o.word.length))}else o.start?c={column:o.start.column,line:o.start.line}:o.index&&(c=this.positionInside(o.index)),o.end?d={column:o.end.column,line:o.end.line}:typeof o.endIndex=="number"?d=this.positionInside(o.endIndex):o.index&&(d=this.positionInside(o.index+1));return(d.line<c.line||d.line===c.line&&d.column<=c.column)&&(d={column:c.column+1,line:c.line}),{end:d,start:c}}raw(o,c){return new e().raw(this,o,c)}remove(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this}replaceWith(...o){if(this.parent){let c=this,d=!1;for(let f of o)f===this?d=!0:d?(this.parent.insertAfter(c,f),c=f):this.parent.insertBefore(c,f);d||this.remove()}return this}root(){let o=this;for(;o.parent&&o.parent.type!=="document";)o=o.parent;return o}toJSON(o,c){let d={},f=c==null;c=c||new Map;let y=0;for(let v in this){if(!Object.prototype.hasOwnProperty.call(this,v)||v==="parent"||v==="proxyCache")continue;let w=this[v];if(Array.isArray(w))d[v]=w.map(A=>typeof A=="object"&&A.toJSON?A.toJSON(null,c):A);else if(typeof w=="object"&&w.toJSON)d[v]=w.toJSON(null,c);else if(v==="source"){let A=c.get(w.input);A==null&&(A=y,c.set(w.input,y),y++),d[v]={end:w.end,inputId:A,start:w.start}}else d[v]=w}return f&&(d.inputs=[...c.keys()].map(v=>v.toJSON())),d}toProxy(){return this.proxyCache||(this.proxyCache=new Proxy(this,this.getProxyProcessor())),this.proxyCache}toString(o=r){o.stringify&&(o=o.stringify);let c="";return o(this,d=>{c+=d}),c}warn(o,c,d){let f={node:this};for(let y in d)f[y]=d[y];return o.warn(c,f)}get proxyOf(){return this}}return Ba=s,s.default=s,Ba}var Fa,Xc;function Mn(){if(Xc)return Fa;Xc=1;let t=Ln();class e extends t{constructor(n){super(n),this.type="comment"}}return Fa=e,e.default=e,Fa}var Ua,Qc;function Dn(){if(Qc)return Ua;Qc=1;let t=Ln();class e extends t{constructor(n){n&&typeof n.value<"u"&&typeof n.value!="string"&&(n=je(ye({},n),{value:String(n.value)})),super(n),this.type="decl"}get variable(){return this.prop.startsWith("--")||this.prop[0]==="$"}}return Ua=e,e.default=e,Ua}var Va,Zc;function kr(){if(Zc)return Va;Zc=1;let t=Mn(),e=Dn(),r=Ln(),{isClean:n,my:u}=qi(),a,i,s,l;function o(f){return f.map(y=>(y.nodes&&(y.nodes=o(y.nodes)),delete y.source,y))}function c(f){if(f[n]=!1,f.proxyOf.nodes)for(let y of f.proxyOf.nodes)c(y)}let d=(()=>{class f extends r{append(...v){for(let w of v){let A=this.normalize(w,this.last);for(let S of A)this.proxyOf.nodes.push(S)}return this.markDirty(),this}cleanRaws(v){if(super.cleanRaws(v),this.nodes)for(let w of this.nodes)w.cleanRaws(v)}each(v){if(!this.proxyOf.nodes)return;let w=this.getIterator(),A,S;for(;this.indexes[w]<this.proxyOf.nodes.length&&(A=this.indexes[w],S=v(this.proxyOf.nodes[A],A),S!==!1);)this.indexes[w]+=1;return delete this.indexes[w],S}every(v){return this.nodes.every(v)}getIterator(){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach+=1;let v=this.lastEach;return this.indexes[v]=0,v}getProxyProcessor(){return{get(v,w){return w==="proxyOf"?v:v[w]?w==="each"||typeof w=="string"&&w.startsWith("walk")?(...A)=>v[w](...A.map(S=>typeof S=="function"?(_,x)=>S(_.toProxy(),x):S)):w==="every"||w==="some"?A=>v[w]((S,..._)=>A(S.toProxy(),..._)):w==="root"?()=>v.root().toProxy():w==="nodes"?v.nodes.map(A=>A.toProxy()):w==="first"||w==="last"?v[w].toProxy():v[w]:v[w]},set(v,w,A){return v[w]===A||(v[w]=A,(w==="name"||w==="params"||w==="selector")&&v.markDirty()),!0}}}index(v){return typeof v=="number"?v:(v.proxyOf&&(v=v.proxyOf),this.proxyOf.nodes.indexOf(v))}insertAfter(v,w){let A=this.index(v),S=this.normalize(w,this.proxyOf.nodes[A]).reverse();A=this.index(v);for(let x of S)this.proxyOf.nodes.splice(A+1,0,x);let _;for(let x in this.indexes)_=this.indexes[x],A<_&&(this.indexes[x]=_+S.length);return this.markDirty(),this}insertBefore(v,w){let A=this.index(v),S=A===0?"prepend":!1,_=this.normalize(w,this.proxyOf.nodes[A],S).reverse();A=this.index(v);for(let T of _)this.proxyOf.nodes.splice(A,0,T);let x;for(let T in this.indexes)x=this.indexes[T],A<=x&&(this.indexes[T]=x+_.length);return this.markDirty(),this}normalize(v,w){if(typeof v=="string")v=o(i(v).nodes);else if(typeof v>"u")v=[];else if(Array.isArray(v)){v=v.slice(0);for(let S of v)S.parent&&S.parent.removeChild(S,"ignore")}else if(v.type==="root"&&this.type!=="document"){v=v.nodes.slice(0);for(let S of v)S.parent&&S.parent.removeChild(S,"ignore")}else if(v.type)v=[v];else if(v.prop){if(typeof v.value>"u")throw new Error("Value field is missed in node creation");typeof v.value!="string"&&(v.value=String(v.value)),v=[new e(v)]}else if(v.selector||v.selectors)v=[new l(v)];else if(v.name)v=[new a(v)];else if(v.text)v=[new t(v)];else throw new Error("Unknown node type in node creation");return v.map(S=>(S[u]||f.rebuild(S),S=S.proxyOf,S.parent&&S.parent.removeChild(S),S[n]&&c(S),S.raws||(S.raws={}),typeof S.raws.before>"u"&&w&&typeof w.raws.before<"u"&&(S.raws.before=w.raws.before.replace(/\S/g,"")),S.parent=this.proxyOf,S))}prepend(...v){v=v.reverse();for(let w of v){let A=this.normalize(w,this.first,"prepend").reverse();for(let S of A)this.proxyOf.nodes.unshift(S);for(let S in this.indexes)this.indexes[S]=this.indexes[S]+A.length}return this.markDirty(),this}push(v){return v.parent=this,this.proxyOf.nodes.push(v),this}removeAll(){for(let v of this.proxyOf.nodes)v.parent=void 0;return this.proxyOf.nodes=[],this.markDirty(),this}removeChild(v){v=this.index(v),this.proxyOf.nodes[v].parent=void 0,this.proxyOf.nodes.splice(v,1);let w;for(let A in this.indexes)w=this.indexes[A],w>=v&&(this.indexes[A]=w-1);return this.markDirty(),this}replaceValues(v,w,A){return A||(A=w,w={}),this.walkDecls(S=>{w.props&&!w.props.includes(S.prop)||w.fast&&!S.value.includes(w.fast)||(S.value=S.value.replace(v,A))}),this.markDirty(),this}some(v){return this.nodes.some(v)}walk(v){return this.each((w,A)=>{let S;try{S=v(w,A)}catch(_){throw w.addToError(_)}return S!==!1&&w.walk&&(S=w.walk(v)),S})}walkAtRules(v,w){return w?v instanceof RegExp?this.walk((A,S)=>{if(A.type==="atrule"&&v.test(A.name))return w(A,S)}):this.walk((A,S)=>{if(A.type==="atrule"&&A.name===v)return w(A,S)}):(w=v,this.walk((A,S)=>{if(A.type==="atrule")return w(A,S)}))}walkComments(v){return this.walk((w,A)=>{if(w.type==="comment")return v(w,A)})}walkDecls(v,w){return w?v instanceof RegExp?this.walk((A,S)=>{if(A.type==="decl"&&v.test(A.prop))return w(A,S)}):this.walk((A,S)=>{if(A.type==="decl"&&A.prop===v)return w(A,S)}):(w=v,this.walk((A,S)=>{if(A.type==="decl")return w(A,S)}))}walkRules(v,w){return w?v instanceof RegExp?this.walk((A,S)=>{if(A.type==="rule"&&v.test(A.selector))return w(A,S)}):this.walk((A,S)=>{if(A.type==="rule"&&A.selector===v)return w(A,S)}):(w=v,this.walk((A,S)=>{if(A.type==="rule")return w(A,S)}))}get first(){if(this.proxyOf.nodes)return this.proxyOf.nodes[0]}get last(){if(this.proxyOf.nodes)return this.proxyOf.nodes[this.proxyOf.nodes.length-1]}}return f.registerParse=y=>{i=y},f.registerRule=y=>{l=y},f.registerAtRule=y=>{a=y},f.registerRoot=y=>{s=y},f})();return Va=d,d.default=d,d.rebuild=f=>{f.type==="atrule"?Object.setPrototypeOf(f,a.prototype):f.type==="rule"?Object.setPrototypeOf(f,l.prototype):f.type==="decl"?Object.setPrototypeOf(f,e.prototype):f.type==="comment"?Object.setPrototypeOf(f,t.prototype):f.type==="root"&&Object.setPrototypeOf(f,s.prototype),f[u]=!0,f.nodes&&f.nodes.forEach(y=>{d.rebuild(y)})},Va}var ja,Kc;function Pi(){if(Kc)return ja;Kc=1;let t=kr();class e extends t{constructor(n){super(n),this.type="atrule"}append(...n){return this.proxyOf.nodes||(this.nodes=[]),super.append(...n)}prepend(...n){return this.proxyOf.nodes||(this.nodes=[]),super.prepend(...n)}}return ja=e,e.default=e,t.registerAtRule(e),ja}var za,Yc;function Hi(){if(Yc)return za;Yc=1;let t=kr(),e,r,n=(()=>{class u extends t{constructor(i){super(ye({type:"document"},i)),this.nodes||(this.nodes=[])}toResult(i={}){return new e(new r,this,i).stringify()}}return u.registerLazyResult=a=>{e=a},u.registerProcessor=a=>{r=a},u})();return za=n,n.default=n,za}var Ga,Jc;function r1(){if(Jc)return Ga;Jc=1;let t="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";return Ga={nanoid:(n=21)=>{let u="",a=n|0;for(;a--;)u+=t[Math.random()*64|0];return u},customAlphabet:(n,u=21)=>(a=u)=>{let i="",s=a|0;for(;s--;)i+=n[Math.random()*n.length|0];return i}},Ga}var $a,e0;function J0(){if(e0)return $a;e0=1;let{existsSync:t,readFileSync:e}=gt,{dirname:r,join:n}=gt,{SourceMapConsumer:u,SourceMapGenerator:a}=gt;function i(l){return Buffer?Buffer.from(l,"base64").toString():window.atob(l)}class s{constructor(o,c){if(c.map===!1)return;this.loadAnnotation(o),this.inline=this.startWith(this.annotation,"data:");let d=c.map?c.map.prev:void 0,f=this.loadMap(c.from,d);!this.mapFile&&c.from&&(this.mapFile=c.from),this.mapFile&&(this.root=r(this.mapFile)),f&&(this.text=f)}consumer(){return this.consumerCache||(this.consumerCache=new u(this.text)),this.consumerCache}decodeInline(o){let c=/^data:application\/json;charset=utf-?8;base64,/,d=/^data:application\/json;base64,/,f=/^data:application\/json;charset=utf-?8,/,y=/^data:application\/json,/,v=o.match(f)||o.match(y);if(v)return decodeURIComponent(o.substr(v[0].length));let w=o.match(c)||o.match(d);if(w)return i(o.substr(w[0].length));let A=o.match(/data:application\/json;([^,]+),/)[1];throw new Error("Unsupported source map encoding "+A)}getAnnotationURL(o){return o.replace(/^\/\*\s*# sourceMappingURL=/,"").trim()}isMap(o){return typeof o!="object"?!1:typeof o.mappings=="string"||typeof o._mappings=="string"||Array.isArray(o.sections)}loadAnnotation(o){let c=o.match(/\/\*\s*# sourceMappingURL=/g);if(!c)return;let d=o.lastIndexOf(c.pop()),f=o.indexOf("*/",d);d>-1&&f>-1&&(this.annotation=this.getAnnotationURL(o.substring(d,f)))}loadFile(o){if(this.root=r(o),t(o))return this.mapFile=o,e(o,"utf-8").toString().trim()}loadMap(o,c){if(c===!1)return!1;if(c){if(typeof c=="string")return c;if(typeof c=="function"){let d=c(o);if(d){let f=this.loadFile(d);if(!f)throw new Error("Unable to load previous source map: "+d.toString());return f}}else{if(c instanceof u)return a.fromSourceMap(c).toString();if(c instanceof a)return c.toString();if(this.isMap(c))return JSON.stringify(c);throw new Error("Unsupported previous source map format: "+c.toString())}}else{if(this.inline)return this.decodeInline(this.annotation);if(this.annotation){let d=this.annotation;return o&&(d=n(r(o),d)),this.loadFile(d)}}}startWith(o,c){return o?o.substr(0,c.length)===c:!1}withContent(){return!!(this.consumer().sourcesContent&&this.consumer().sourcesContent.length>0)}}return $a=s,s.default=s,$a}var Wa,t0;function In(){if(t0)return Wa;t0=1;let{nanoid:t}=r1(),{isAbsolute:e,resolve:r}=gt,{SourceMapConsumer:n,SourceMapGenerator:u}=gt,{fileURLToPath:a,pathToFileURL:i}=gt,s=Ri(),l=J0(),o=gt,c=Symbol("fromOffsetCache"),d=!!(n&&u),f=!!(r&&e);class y{constructor(w,A={}){if(w===null||typeof w>"u"||typeof w=="object"&&!w.toString)throw new Error(`PostCSS received ${w} instead of CSS string`);if(this.css=w.toString(),this.css[0]==="\uFEFF"||this.css[0]==="\uFFFE"?(this.hasBOM=!0,this.css=this.css.slice(1)):this.hasBOM=!1,this.document=this.css,A.document&&(this.document=A.document.toString()),A.from&&(!f||/^\w+:\/\//.test(A.from)||e(A.from)?this.file=A.from:this.file=r(A.from)),f&&d){let S=new l(this.css,A);if(S.text){this.map=S;let _=S.consumer().file;!this.file&&_&&(this.file=this.mapResolve(_))}}this.file||(this.id="<input css "+t(6)+">"),this.map&&(this.map.file=this.from)}error(w,A,S,_={}){let x,T,E;if(A&&typeof A=="object"){let B=A,F=S;if(typeof B.offset=="number"){let q=this.fromOffset(B.offset);A=q.line,S=q.col}else A=B.line,S=B.column;if(typeof F.offset=="number"){let q=this.fromOffset(F.offset);T=q.line,x=q.col}else T=F.line,x=F.column}else if(!S){let B=this.fromOffset(A);A=B.line,S=B.col}let O=this.origin(A,S,T,x);return O?E=new s(w,O.endLine===void 0?O.line:{column:O.column,line:O.line},O.endLine===void 0?O.column:{column:O.endColumn,line:O.endLine},O.source,O.file,_.plugin):E=new s(w,T===void 0?A:{column:S,line:A},T===void 0?S:{column:x,line:T},this.css,this.file,_.plugin),E.input={column:S,endColumn:x,endLine:T,line:A,source:this.css},this.file&&(i&&(E.input.url=i(this.file).toString()),E.input.file=this.file),E}fromOffset(w){let A,S;if(this[c])S=this[c];else{let x=this.css.split(`
`);S=new Array(x.length);let T=0;for(let E=0,O=x.length;E<O;E++)S[E]=T,T+=x[E].length+1;this[c]=S}A=S[S.length-1];let _=0;if(w>=A)_=S.length-1;else{let x=S.length-2,T;for(;_<x;)if(T=_+(x-_>>1),w<S[T])x=T-1;else if(w>=S[T+1])_=T+1;else{_=T;break}}return{col:w-S[_]+1,line:_+1}}mapResolve(w){return/^\w+:\/\//.test(w)?w:r(this.map.consumer().sourceRoot||this.map.root||".",w)}origin(w,A,S,_){if(!this.map)return!1;let x=this.map.consumer(),T=x.originalPositionFor({column:A,line:w});if(!T.source)return!1;let E;typeof S=="number"&&(E=x.originalPositionFor({column:_,line:S}));let O;e(T.source)?O=i(T.source):O=new URL(T.source,this.map.consumer().sourceRoot||i(this.map.mapFile));let B={column:T.column,endColumn:E&&E.column,endLine:E&&E.line,line:T.line,url:O.toString()};if(O.protocol==="file:")if(a)B.file=a(O);else throw new Error("file: protocol is not available in this PostCSS build");let F=x.sourceContentFor(T.source);return F&&(B.source=F),B}toJSON(){let w={};for(let A of["hasBOM","css","file","id"])this[A]!=null&&(w[A]=this[A]);return this.map&&(w.map=ye({},this.map),w.map.consumerCache&&(w.map.consumerCache=void 0)),w}get from(){return this.file||this.id}}return Wa=y,y.default=y,o&&o.registerInput&&o.registerInput(y),Wa}var Xa,r0;function mu(){if(r0)return Xa;r0=1;let t=kr(),e,r,n=(()=>{class u extends t{constructor(i){super(i),this.type="root",this.nodes||(this.nodes=[])}normalize(i,s,l){let o=super.normalize(i);if(s){if(l==="prepend")this.nodes.length>1?s.raws.before=this.nodes[1].raws.before:delete s.raws.before;else if(this.first!==s)for(let c of o)c.raws.before=s.raws.before}return o}removeChild(i,s){let l=this.index(i);return!s&&l===0&&this.nodes.length>1&&(this.nodes[1].raws.before=this.nodes[l].raws.before),super.removeChild(i)}toResult(i={}){return new e(new r,this,i).stringify()}}return u.registerLazyResult=a=>{e=a},u.registerProcessor=a=>{r=a},u})();return Xa=n,n.default=n,t.registerRoot(n),Xa}var Qa,u0;function el(){if(u0)return Qa;u0=1;let t={comma(e){return t.split(e,[","],!0)},space(e){let r=[" ",`
`,"	"];return t.split(e,r)},split(e,r,n){let u=[],a="",i=!1,s=0,l=!1,o="",c=!1;for(let d of e)c?c=!1:d==="\\"?c=!0:l?d===o&&(l=!1):d==='"'||d==="'"?(l=!0,o=d):d==="("?s+=1:d===")"?s>0&&(s-=1):s===0&&r.includes(d)&&(i=!0),i?(a!==""&&u.push(a.trim()),a="",i=!1):a+=d;return(n||a!=="")&&u.push(a.trim()),u}};return Qa=t,t.default=t,Qa}var Za,n0;function Bi(){if(n0)return Za;n0=1;let t=kr(),e=el();class r extends t{constructor(u){super(u),this.type="rule",this.nodes||(this.nodes=[])}get selectors(){return e.comma(this.selector)}set selectors(u){let a=this.selector?this.selector.match(/,\s*/):null,i=a?a[0]:","+this.raw("between","beforeOpen");this.selector=u.join(i)}}return Za=r,r.default=r,t.registerRule(r),Za}var Ka,a0;function u1(){if(a0)return Ka;a0=1;let t=Pi(),e=Mn(),r=Dn(),n=In(),u=J0(),a=mu(),i=Bi();function s(l,o){if(Array.isArray(l))return l.map(v=>s(v));let f=l,{inputs:c}=f,d=Yt(f,["inputs"]);if(c){o=[];for(let v of c){let w=je(ye({},v),{__proto__:n.prototype});w.map&&(w.map=je(ye({},w.map),{__proto__:u.prototype})),o.push(w)}}if(d.nodes&&(d.nodes=l.nodes.map(v=>s(v,o))),d.source){let y=d.source,{inputId:v}=y,w=Yt(y,["inputId"]);d.source=w,v!=null&&(d.source.input=o[v])}if(d.type==="root")return new a(d);if(d.type==="decl")return new r(d);if(d.type==="rule")return new i(d);if(d.type==="comment")return new e(d);if(d.type==="atrule")return new t(d);throw new Error("Unknown node type: "+l.type)}return Ka=s,s.default=s,Ka}var Ya,i0;function tl(){if(i0)return Ya;i0=1;let{dirname:t,relative:e,resolve:r,sep:n}=gt,{SourceMapConsumer:u,SourceMapGenerator:a}=gt,{pathToFileURL:i}=gt,s=In(),l=!!(u&&a),o=!!(t&&r&&e&&n);class c{constructor(f,y,v,w){this.stringify=f,this.mapOpts=v.map||{},this.root=y,this.opts=v,this.css=w,this.originalCSS=w,this.usesFileUrls=!this.mapOpts.from&&this.mapOpts.absolute,this.memoizedFileURLs=new Map,this.memoizedPaths=new Map,this.memoizedURLs=new Map}addAnnotation(){let f;this.isInline()?f="data:application/json;base64,"+this.toBase64(this.map.toString()):typeof this.mapOpts.annotation=="string"?f=this.mapOpts.annotation:typeof this.mapOpts.annotation=="function"?f=this.mapOpts.annotation(this.opts.to,this.root):f=this.outputFile()+".map";let y=`
`;this.css.includes(`\r
`)&&(y=`\r
`),this.css+=y+"/*# sourceMappingURL="+f+" */"}applyPrevMaps(){for(let f of this.previous()){let y=this.toUrl(this.path(f.file)),v=f.root||t(f.file),w;this.mapOpts.sourcesContent===!1?(w=new u(f.text),w.sourcesContent&&(w.sourcesContent=null)):w=f.consumer(),this.map.applySourceMap(w,y,this.toUrl(this.path(v)))}}clearAnnotation(){if(this.mapOpts.annotation!==!1)if(this.root){let f;for(let y=this.root.nodes.length-1;y>=0;y--)f=this.root.nodes[y],f.type==="comment"&&f.text.startsWith("# sourceMappingURL=")&&this.root.removeChild(y)}else this.css&&(this.css=this.css.replace(/\n*\/\*#[\S\s]*?\*\/$/gm,""))}generate(){if(this.clearAnnotation(),o&&l&&this.isMap())return this.generateMap();{let f="";return this.stringify(this.root,y=>{f+=y}),[f]}}generateMap(){if(this.root)this.generateString();else if(this.previous().length===1){let f=this.previous()[0].consumer();f.file=this.outputFile(),this.map=a.fromSourceMap(f,{ignoreInvalidMapping:!0})}else this.map=new a({file:this.outputFile(),ignoreInvalidMapping:!0}),this.map.addMapping({generated:{column:0,line:1},original:{column:0,line:1},source:this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>"});return this.isSourcesContent()&&this.setSourcesContent(),this.root&&this.previous().length>0&&this.applyPrevMaps(),this.isAnnotation()&&this.addAnnotation(),this.isInline()?[this.css]:[this.css,this.map]}generateString(){this.css="",this.map=new a({file:this.outputFile(),ignoreInvalidMapping:!0});let f=1,y=1,v="<no source>",w={generated:{column:0,line:0},original:{column:0,line:0},source:""},A,S;this.stringify(this.root,(_,x,T)=>{if(this.css+=_,x&&T!=="end"&&(w.generated.line=f,w.generated.column=y-1,x.source&&x.source.start?(w.source=this.sourcePath(x),w.original.line=x.source.start.line,w.original.column=x.source.start.column-1,this.map.addMapping(w)):(w.source=v,w.original.line=1,w.original.column=0,this.map.addMapping(w))),S=_.match(/\n/g),S?(f+=S.length,A=_.lastIndexOf(`
`),y=_.length-A):y+=_.length,x&&T!=="start"){let E=x.parent||{raws:{}};(!(x.type==="decl"||x.type==="atrule"&&!x.nodes)||x!==E.last||E.raws.semicolon)&&(x.source&&x.source.end?(w.source=this.sourcePath(x),w.original.line=x.source.end.line,w.original.column=x.source.end.column-1,w.generated.line=f,w.generated.column=y-2,this.map.addMapping(w)):(w.source=v,w.original.line=1,w.original.column=0,w.generated.line=f,w.generated.column=y-1,this.map.addMapping(w)))}})}isAnnotation(){return this.isInline()?!0:typeof this.mapOpts.annotation<"u"?this.mapOpts.annotation:this.previous().length?this.previous().some(f=>f.annotation):!0}isInline(){if(typeof this.mapOpts.inline<"u")return this.mapOpts.inline;let f=this.mapOpts.annotation;return typeof f<"u"&&f!==!0?!1:this.previous().length?this.previous().some(y=>y.inline):!0}isMap(){return typeof this.opts.map<"u"?!!this.opts.map:this.previous().length>0}isSourcesContent(){return typeof this.mapOpts.sourcesContent<"u"?this.mapOpts.sourcesContent:this.previous().length?this.previous().some(f=>f.withContent()):!0}outputFile(){return this.opts.to?this.path(this.opts.to):this.opts.from?this.path(this.opts.from):"to.css"}path(f){if(this.mapOpts.absolute||f.charCodeAt(0)===60||/^\w+:\/\//.test(f))return f;let y=this.memoizedPaths.get(f);if(y)return y;let v=this.opts.to?t(this.opts.to):".";typeof this.mapOpts.annotation=="string"&&(v=t(r(v,this.mapOpts.annotation)));let w=e(v,f);return this.memoizedPaths.set(f,w),w}previous(){if(!this.previousMaps)if(this.previousMaps=[],this.root)this.root.walk(f=>{if(f.source&&f.source.input.map){let y=f.source.input.map;this.previousMaps.includes(y)||this.previousMaps.push(y)}});else{let f=new s(this.originalCSS,this.opts);f.map&&this.previousMaps.push(f.map)}return this.previousMaps}setSourcesContent(){let f={};if(this.root)this.root.walk(y=>{if(y.source){let v=y.source.input.from;if(v&&!f[v]){f[v]=!0;let w=this.usesFileUrls?this.toFileUrl(v):this.toUrl(this.path(v));this.map.setSourceContent(w,y.source.input.css)}}});else if(this.css){let y=this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>";this.map.setSourceContent(y,this.css)}}sourcePath(f){return this.mapOpts.from?this.toUrl(this.mapOpts.from):this.usesFileUrls?this.toFileUrl(f.source.input.from):this.toUrl(this.path(f.source.input.from))}toBase64(f){return Buffer?Buffer.from(f).toString("base64"):window.btoa(unescape(encodeURIComponent(f)))}toFileUrl(f){let y=this.memoizedFileURLs.get(f);if(y)return y;if(i){let v=i(f).toString();return this.memoizedFileURLs.set(f,v),v}else throw new Error("`map.absolute` option is not available in this PostCSS build")}toUrl(f){let y=this.memoizedURLs.get(f);if(y)return y;n==="\\"&&(f=f.replace(/\\/g,"/"));let v=encodeURI(f).replace(/[#?]/g,encodeURIComponent);return this.memoizedURLs.set(f,v),v}}return Ya=c,Ya}var Ja,s0;function n1(){if(s0)return Ja;s0=1;let t=39,e=34,r=92,n=47,u=10,a=32,i=12,s=9,l=13,o=91,c=93,d=40,f=41,y=123,v=125,w=59,A=42,S=58,_=64,x=/[\t\n\f\r "#'()/;[\\\]{}]/g,T=/[\t\n\f\r !"#'():;@[\\\]{}]|\/(?=\*)/g,E=/.[\r\n"'(/\\]/,O=/[\da-f]/i;return Ja=function(F,q={}){let C=F.css.valueOf(),P=q.ignoreErrors,W,h,m,g,M,H,$,J,de,pe,k=C.length,D=0,K=[],me=[];function rt(){return D}function jr(qt){throw F.error("Unclosed "+qt,D)}function zn(){return me.length===0&&D>=k}function Gn(qt){if(me.length)return me.pop();if(D>=k)return;let zr=qt?qt.ignoreUnclosed:!1;switch(W=C.charCodeAt(D),W){case u:case a:case s:case l:case i:{g=D;do g+=1,W=C.charCodeAt(g);while(W===a||W===u||W===s||W===l||W===i);H=["space",C.slice(D,g)],D=g-1;break}case o:case c:case y:case v:case S:case w:case f:{let vu=String.fromCharCode(W);H=[vu,vu,D];break}case d:{if(pe=K.length?K.pop()[1]:"",de=C.charCodeAt(D+1),pe==="url"&&de!==t&&de!==e&&de!==a&&de!==u&&de!==s&&de!==i&&de!==l){g=D;do{if($=!1,g=C.indexOf(")",g+1),g===-1)if(P||zr){g=D;break}else jr("bracket");for(J=g;C.charCodeAt(J-1)===r;)J-=1,$=!$}while($);H=["brackets",C.slice(D,g+1),D,g],D=g}else g=C.indexOf(")",D+1),h=C.slice(D,g+1),g===-1||E.test(h)?H=["(","(",D]:(H=["brackets",h,D,g],D=g);break}case t:case e:{M=W===t?"'":'"',g=D;do{if($=!1,g=C.indexOf(M,g+1),g===-1)if(P||zr){g=D+1;break}else jr("string");for(J=g;C.charCodeAt(J-1)===r;)J-=1,$=!$}while($);H=["string",C.slice(D,g+1),D,g],D=g;break}case _:{x.lastIndex=D+1,x.test(C),x.lastIndex===0?g=C.length-1:g=x.lastIndex-2,H=["at-word",C.slice(D,g+1),D,g],D=g;break}case r:{for(g=D,m=!0;C.charCodeAt(g+1)===r;)g+=1,m=!m;if(W=C.charCodeAt(g+1),m&&W!==n&&W!==a&&W!==u&&W!==s&&W!==l&&W!==i&&(g+=1,O.test(C.charAt(g)))){for(;O.test(C.charAt(g+1));)g+=1;C.charCodeAt(g+1)===a&&(g+=1)}H=["word",C.slice(D,g+1),D,g],D=g;break}default:{W===n&&C.charCodeAt(D+1)===A?(g=C.indexOf("*/",D+2)+1,g===0&&(P||zr?g=C.length:jr("comment")),H=["comment",C.slice(D,g+1),D,g],D=g):(T.lastIndex=D+1,T.test(C),T.lastIndex===0?g=C.length-1:g=T.lastIndex-2,H=["word",C.slice(D,g+1),D,g],K.push(H),D=g);break}}return D++,H}function $n(qt){me.push(qt)}return{back:$n,endOfFile:zn,nextToken:Gn,position:rt}},Ja}var ei,o0;function a1(){if(o0)return ei;o0=1;let t=Pi(),e=Mn(),r=Dn(),n=mu(),u=Bi(),a=n1(),i={empty:!0,space:!0};function s(o){for(let c=o.length-1;c>=0;c--){let d=o[c],f=d[3]||d[2];if(f)return f}}class l{constructor(c){this.input=c,this.root=new n,this.current=this.root,this.spaces="",this.semicolon=!1,this.createTokenizer(),this.root.source={input:c,start:{column:1,line:1,offset:0}}}atrule(c){let d=new t;d.name=c[1].slice(1),d.name===""&&this.unnamedAtrule(d,c),this.init(d,c[2]);let f,y,v,w=!1,A=!1,S=[],_=[];for(;!this.tokenizer.endOfFile();){if(c=this.tokenizer.nextToken(),f=c[0],f==="("||f==="["?_.push(f==="("?")":"]"):f==="{"&&_.length>0?_.push("}"):f===_[_.length-1]&&_.pop(),_.length===0)if(f===";"){d.source.end=this.getPosition(c[2]),d.source.end.offset++,this.semicolon=!0;break}else if(f==="{"){A=!0;break}else if(f==="}"){if(S.length>0){for(v=S.length-1,y=S[v];y&&y[0]==="space";)y=S[--v];y&&(d.source.end=this.getPosition(y[3]||y[2]),d.source.end.offset++)}this.end(c);break}else S.push(c);else S.push(c);if(this.tokenizer.endOfFile()){w=!0;break}}d.raws.between=this.spacesAndCommentsFromEnd(S),S.length?(d.raws.afterName=this.spacesAndCommentsFromStart(S),this.raw(d,"params",S),w&&(c=S[S.length-1],d.source.end=this.getPosition(c[3]||c[2]),d.source.end.offset++,this.spaces=d.raws.between,d.raws.between="")):(d.raws.afterName="",d.params=""),A&&(d.nodes=[],this.current=d)}checkMissedSemicolon(c){let d=this.colon(c);if(d===!1)return;let f=0,y;for(let v=d-1;v>=0&&(y=c[v],!(y[0]!=="space"&&(f+=1,f===2)));v--);throw this.input.error("Missed semicolon",y[0]==="word"?y[3]+1:y[2])}colon(c){let d=0,f,y,v;for(let[w,A]of c.entries()){if(y=A,v=y[0],v==="("&&(d+=1),v===")"&&(d-=1),d===0&&v===":")if(!f)this.doubleColon(y);else{if(f[0]==="word"&&f[1]==="progid")continue;return w}f=y}return!1}comment(c){let d=new e;this.init(d,c[2]),d.source.end=this.getPosition(c[3]||c[2]),d.source.end.offset++;let f=c[1].slice(2,-2);if(/^\s*$/.test(f))d.text="",d.raws.left=f,d.raws.right="";else{let y=f.match(/^(\s*)([^]*\S)(\s*)$/);d.text=y[2],d.raws.left=y[1],d.raws.right=y[3]}}createTokenizer(){this.tokenizer=a(this.input)}decl(c,d){let f=new r;this.init(f,c[0][2]);let y=c[c.length-1];for(y[0]===";"&&(this.semicolon=!0,c.pop()),f.source.end=this.getPosition(y[3]||y[2]||s(c)),f.source.end.offset++;c[0][0]!=="word";)c.length===1&&this.unknownWord(c),f.raws.before+=c.shift()[1];for(f.source.start=this.getPosition(c[0][2]),f.prop="";c.length;){let _=c[0][0];if(_===":"||_==="space"||_==="comment")break;f.prop+=c.shift()[1]}f.raws.between="";let v;for(;c.length;)if(v=c.shift(),v[0]===":"){f.raws.between+=v[1];break}else v[0]==="word"&&/\w/.test(v[1])&&this.unknownWord([v]),f.raws.between+=v[1];(f.prop[0]==="_"||f.prop[0]==="*")&&(f.raws.before+=f.prop[0],f.prop=f.prop.slice(1));let w=[],A;for(;c.length&&(A=c[0][0],!(A!=="space"&&A!=="comment"));)w.push(c.shift());this.precheckMissedSemicolon(c);for(let _=c.length-1;_>=0;_--){if(v=c[_],v[1].toLowerCase()==="!important"){f.important=!0;let x=this.stringFrom(c,_);x=this.spacesFromEnd(c)+x,x!==" !important"&&(f.raws.important=x);break}else if(v[1].toLowerCase()==="important"){let x=c.slice(0),T="";for(let E=_;E>0;E--){let O=x[E][0];if(T.trim().startsWith("!")&&O!=="space")break;T=x.pop()[1]+T}T.trim().startsWith("!")&&(f.important=!0,f.raws.important=T,c=x)}if(v[0]!=="space"&&v[0]!=="comment")break}c.some(_=>_[0]!=="space"&&_[0]!=="comment")&&(f.raws.between+=w.map(_=>_[1]).join(""),w=[]),this.raw(f,"value",w.concat(c),d),f.value.includes(":")&&!d&&this.checkMissedSemicolon(c)}doubleColon(c){throw this.input.error("Double colon",{offset:c[2]},{offset:c[2]+c[1].length})}emptyRule(c){let d=new u;this.init(d,c[2]),d.selector="",d.raws.between="",this.current=d}end(c){this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.semicolon=!1,this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.spaces="",this.current.parent?(this.current.source.end=this.getPosition(c[2]),this.current.source.end.offset++,this.current=this.current.parent):this.unexpectedClose(c)}endFile(){this.current.parent&&this.unclosedBlock(),this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.root.source.end=this.getPosition(this.tokenizer.position())}freeSemicolon(c){if(this.spaces+=c[1],this.current.nodes){let d=this.current.nodes[this.current.nodes.length-1];d&&d.type==="rule"&&!d.raws.ownSemicolon&&(d.raws.ownSemicolon=this.spaces,this.spaces="",d.source.end=this.getPosition(c[2]),d.source.end.offset+=d.raws.ownSemicolon.length)}}getPosition(c){let d=this.input.fromOffset(c);return{column:d.col,line:d.line,offset:c}}init(c,d){this.current.push(c),c.source={input:this.input,start:this.getPosition(d)},c.raws.before=this.spaces,this.spaces="",c.type!=="comment"&&(this.semicolon=!1)}other(c){let d=!1,f=null,y=!1,v=null,w=[],A=c[1].startsWith("--"),S=[],_=c;for(;_;){if(f=_[0],S.push(_),f==="("||f==="[")v||(v=_),w.push(f==="("?")":"]");else if(A&&y&&f==="{")v||(v=_),w.push("}");else if(w.length===0)if(f===";")if(y){this.decl(S,A);return}else break;else if(f==="{"){this.rule(S);return}else if(f==="}"){this.tokenizer.back(S.pop()),d=!0;break}else f===":"&&(y=!0);else f===w[w.length-1]&&(w.pop(),w.length===0&&(v=null));_=this.tokenizer.nextToken()}if(this.tokenizer.endOfFile()&&(d=!0),w.length>0&&this.unclosedBracket(v),d&&y){if(!A)for(;S.length&&(_=S[S.length-1][0],!(_!=="space"&&_!=="comment"));)this.tokenizer.back(S.pop());this.decl(S,A)}else this.unknownWord(S)}parse(){let c;for(;!this.tokenizer.endOfFile();)switch(c=this.tokenizer.nextToken(),c[0]){case"space":this.spaces+=c[1];break;case";":this.freeSemicolon(c);break;case"}":this.end(c);break;case"comment":this.comment(c);break;case"at-word":this.atrule(c);break;case"{":this.emptyRule(c);break;default:this.other(c);break}this.endFile()}precheckMissedSemicolon(){}raw(c,d,f,y){let v,w,A=f.length,S="",_=!0,x,T;for(let E=0;E<A;E+=1)v=f[E],w=v[0],w==="space"&&E===A-1&&!y?_=!1:w==="comment"?(T=f[E-1]?f[E-1][0]:"empty",x=f[E+1]?f[E+1][0]:"empty",!i[T]&&!i[x]?S.slice(-1)===","?_=!1:S+=v[1]:_=!1):S+=v[1];if(!_){let E=f.reduce((O,B)=>O+B[1],"");c.raws[d]={raw:E,value:S}}c[d]=S}rule(c){c.pop();let d=new u;this.init(d,c[0][2]),d.raws.between=this.spacesAndCommentsFromEnd(c),this.raw(d,"selector",c),this.current=d}spacesAndCommentsFromEnd(c){let d,f="";for(;c.length&&(d=c[c.length-1][0],!(d!=="space"&&d!=="comment"));)f=c.pop()[1]+f;return f}spacesAndCommentsFromStart(c){let d,f="";for(;c.length&&(d=c[0][0],!(d!=="space"&&d!=="comment"));)f+=c.shift()[1];return f}spacesFromEnd(c){let d,f="";for(;c.length&&(d=c[c.length-1][0],d==="space");)f=c.pop()[1]+f;return f}stringFrom(c,d){let f="";for(let y=d;y<c.length;y++)f+=c[y][1];return c.splice(d,c.length-d),f}unclosedBlock(){let c=this.current.source.start;throw this.input.error("Unclosed block",c.line,c.column)}unclosedBracket(c){throw this.input.error("Unclosed bracket",{offset:c[2]},{offset:c[2]+1})}unexpectedClose(c){throw this.input.error("Unexpected }",{offset:c[2]},{offset:c[2]+1})}unknownWord(c){throw this.input.error("Unknown word",{offset:c[0][2]},{offset:c[0][2]+c[0][1].length})}unnamedAtrule(c,d){throw this.input.error("At-rule without name",{offset:d[2]},{offset:d[2]+d[1].length})}}return ei=l,ei}var ti,c0;function Fi(){if(c0)return ti;c0=1;let t=kr(),e=In(),r=a1();function n(u,a){let i=new e(u,a),s=new r(i);try{s.parse()}catch(l){throw process.env.NODE_ENV!=="production"&&l.name==="CssSyntaxError"&&a&&a.from&&(/\.scss$/i.test(a.from)?l.message+=`
You tried to parse SCSS with the standard CSS parser; try again with the postcss-scss parser`:/\.sass/i.test(a.from)?l.message+=`
You tried to parse Sass with the standard CSS parser; try again with the postcss-sass parser`:/\.less$/i.test(a.from)&&(l.message+=`
You tried to parse Less with the standard CSS parser; try again with the postcss-less parser`)),l}return s.root}return ti=n,n.default=n,t.registerParse(n),ti}var ri,l0;function rl(){if(l0)return ri;l0=1;class t{constructor(r,n={}){if(this.type="warning",this.text=r,n.node&&n.node.source){let u=n.node.rangeBy(n);this.line=u.start.line,this.column=u.start.column,this.endLine=u.end.line,this.endColumn=u.end.column}for(let u in n)this[u]=n[u]}toString(){return this.node?this.node.error(this.text,{index:this.index,plugin:this.plugin,word:this.word}).message:this.plugin?this.plugin+": "+this.text:this.text}}return ri=t,t.default=t,ri}var ui,d0;function Ui(){if(d0)return ui;d0=1;let t=rl();class e{constructor(n,u,a){this.processor=n,this.messages=[],this.root=u,this.opts=a,this.css=void 0,this.map=void 0}toString(){return this.css}warn(n,u={}){u.plugin||this.lastPlugin&&this.lastPlugin.postcssPlugin&&(u.plugin=this.lastPlugin.postcssPlugin);let a=new t(n,u);return this.messages.push(a),a}warnings(){return this.messages.filter(n=>n.type==="warning")}get content(){return this.css}}return ui=e,e.default=e,ui}var ni,f0;function ul(){if(f0)return ni;f0=1;let t={};return ni=function(r){t[r]||(t[r]=!0,typeof console<"u"&&console.warn&&console.warn(r))},ni}var ai,h0;function nl(){if(h0)return ai;h0=1;let t=kr(),e=Hi(),r=tl(),n=Fi(),u=Ui(),a=mu(),i=kn(),{isClean:s,my:l}=qi(),o=ul(),c={atrule:"AtRule",comment:"Comment",decl:"Declaration",document:"Document",root:"Root",rule:"Rule"},d={AtRule:!0,AtRuleExit:!0,Comment:!0,CommentExit:!0,Declaration:!0,DeclarationExit:!0,Document:!0,DocumentExit:!0,Once:!0,OnceExit:!0,postcssPlugin:!0,prepare:!0,Root:!0,RootExit:!0,Rule:!0,RuleExit:!0},f={Once:!0,postcssPlugin:!0,prepare:!0},y=0;function v(T){return typeof T=="object"&&typeof T.then=="function"}function w(T){let E=!1,O=c[T.type];return T.type==="decl"?E=T.prop.toLowerCase():T.type==="atrule"&&(E=T.name.toLowerCase()),E&&T.append?[O,O+"-"+E,y,O+"Exit",O+"Exit-"+E]:E?[O,O+"-"+E,O+"Exit",O+"Exit-"+E]:T.append?[O,y,O+"Exit"]:[O,O+"Exit"]}function A(T){let E;return T.type==="document"?E=["Document",y,"DocumentExit"]:T.type==="root"?E=["Root",y,"RootExit"]:E=w(T),{eventIndex:0,events:E,iterator:0,node:T,visitorIndex:0,visitors:[]}}function S(T){return T[s]=!1,T.nodes&&T.nodes.forEach(E=>S(E)),T}let _={},x=(()=>{class T{constructor(O,B,F){this.stringified=!1,this.processed=!1;let q;if(typeof B=="object"&&B!==null&&(B.type==="root"||B.type==="document"))q=S(B);else if(B instanceof T||B instanceof u)q=S(B.root),B.map&&(typeof F.map>"u"&&(F.map={}),F.map.inline||(F.map.inline=!1),F.map.prev=B.map);else{let C=n;F.syntax&&(C=F.syntax.parse),F.parser&&(C=F.parser),C.parse&&(C=C.parse);try{q=C(B,F)}catch(P){this.processed=!0,this.error=P}q&&!q[l]&&t.rebuild(q)}this.result=new u(O,q,F),this.helpers=je(ye({},_),{postcss:_,result:this.result}),this.plugins=this.processor.plugins.map(C=>typeof C=="object"&&C.prepare?ye(ye({},C),C.prepare(this.result)):C)}async(){return this.error?Promise.reject(this.error):this.processed?Promise.resolve(this.result):(this.processing||(this.processing=this.runAsync()),this.processing)}catch(O){return this.async().catch(O)}finally(O){return this.async().then(O,O)}getAsyncError(){throw new Error("Use process(css).then(cb) to work with async plugins")}handleError(O,B){let F=this.result.lastPlugin;try{if(B&&B.addToError(O),this.error=O,O.name==="CssSyntaxError"&&!O.plugin)O.plugin=F.postcssPlugin,O.setMessage();else if(F.postcssVersion&&process.env.NODE_ENV!=="production"){let q=F.postcssPlugin,C=F.postcssVersion,P=this.result.processor.version,W=C.split("."),h=P.split(".");(W[0]!==h[0]||parseInt(W[1])>parseInt(h[1]))&&console.error("Unknown error from PostCSS plugin. Your current PostCSS version is "+P+", but "+q+" uses "+C+". Perhaps this is the source of the error below.")}}catch(q){console&&console.error&&console.error(q)}return O}prepareVisitors(){this.listeners={};let O=(B,F,q)=>{this.listeners[F]||(this.listeners[F]=[]),this.listeners[F].push([B,q])};for(let B of this.plugins)if(typeof B=="object")for(let F in B){if(!d[F]&&/^[A-Z]/.test(F))throw new Error(`Unknown event ${F} in ${B.postcssPlugin}. Try to update PostCSS (${this.processor.version} now).`);if(!f[F])if(typeof B[F]=="object")for(let q in B[F])q==="*"?O(B,F,B[F][q]):O(B,F+"-"+q.toLowerCase(),B[F][q]);else typeof B[F]=="function"&&O(B,F,B[F])}this.hasListener=Object.keys(this.listeners).length>0}runAsync(){return ze(this,null,function*(){this.plugin=0;for(let O=0;O<this.plugins.length;O++){let B=this.plugins[O],F=this.runOnRoot(B);if(v(F))try{yield F}catch(q){throw this.handleError(q)}}if(this.prepareVisitors(),this.hasListener){let O=this.result.root;for(;!O[s];){O[s]=!0;let B=[A(O)];for(;B.length>0;){let F=this.visitTick(B);if(v(F))try{yield F}catch(q){let C=B[B.length-1].node;throw this.handleError(q,C)}}}if(this.listeners.OnceExit)for(let[B,F]of this.listeners.OnceExit){this.result.lastPlugin=B;try{if(O.type==="document"){let q=O.nodes.map(C=>F(C,this.helpers));yield Promise.all(q)}else yield F(O,this.helpers)}catch(q){throw this.handleError(q)}}}return this.processed=!0,this.stringify()})}runOnRoot(O){this.result.lastPlugin=O;try{if(typeof O=="object"&&O.Once){if(this.result.root.type==="document"){let B=this.result.root.nodes.map(F=>O.Once(F,this.helpers));return v(B[0])?Promise.all(B):B}return O.Once(this.result.root,this.helpers)}else if(typeof O=="function")return O(this.result.root,this.result)}catch(B){throw this.handleError(B)}}stringify(){if(this.error)throw this.error;if(this.stringified)return this.result;this.stringified=!0,this.sync();let O=this.result.opts,B=i;O.syntax&&(B=O.syntax.stringify),O.stringifier&&(B=O.stringifier),B.stringify&&(B=B.stringify);let q=new r(B,this.result.root,this.result.opts).generate();return this.result.css=q[0],this.result.map=q[1],this.result}sync(){if(this.error)throw this.error;if(this.processed)return this.result;if(this.processed=!0,this.processing)throw this.getAsyncError();for(let O of this.plugins){let B=this.runOnRoot(O);if(v(B))throw this.getAsyncError()}if(this.prepareVisitors(),this.hasListener){let O=this.result.root;for(;!O[s];)O[s]=!0,this.walkSync(O);if(this.listeners.OnceExit)if(O.type==="document")for(let B of O.nodes)this.visitSync(this.listeners.OnceExit,B);else this.visitSync(this.listeners.OnceExit,O)}return this.result}then(O,B){return process.env.NODE_ENV!=="production"&&("from"in this.opts||o("Without `from` option PostCSS could generate wrong source map and will not find Browserslist config. Set it to CSS file path or to `undefined` to prevent this warning.")),this.async().then(O,B)}toString(){return this.css}visitSync(O,B){for(let[F,q]of O){this.result.lastPlugin=F;let C;try{C=q(B,this.helpers)}catch(P){throw this.handleError(P,B.proxyOf)}if(B.type!=="root"&&B.type!=="document"&&!B.parent)return!0;if(v(C))throw this.getAsyncError()}}visitTick(O){let B=O[O.length-1],{node:F,visitors:q}=B;if(F.type!=="root"&&F.type!=="document"&&!F.parent){O.pop();return}if(q.length>0&&B.visitorIndex<q.length){let[P,W]=q[B.visitorIndex];B.visitorIndex+=1,B.visitorIndex===q.length&&(B.visitors=[],B.visitorIndex=0),this.result.lastPlugin=P;try{return W(F.toProxy(),this.helpers)}catch(h){throw this.handleError(h,F)}}if(B.iterator!==0){let P=B.iterator,W;for(;W=F.nodes[F.indexes[P]];)if(F.indexes[P]+=1,!W[s]){W[s]=!0,O.push(A(W));return}B.iterator=0,delete F.indexes[P]}let C=B.events;for(;B.eventIndex<C.length;){let P=C[B.eventIndex];if(B.eventIndex+=1,P===y){F.nodes&&F.nodes.length&&(F[s]=!0,B.iterator=F.getIterator());return}else if(this.listeners[P]){B.visitors=this.listeners[P];return}}O.pop()}walkSync(O){O[s]=!0;let B=w(O);for(let F of B)if(F===y)O.nodes&&O.each(q=>{q[s]||this.walkSync(q)});else{let q=this.listeners[F];if(q&&this.visitSync(q,O.toProxy()))return}}warnings(){return this.sync().warnings()}get content(){return this.stringify().content}get css(){return this.stringify().css}get map(){return this.stringify().map}get messages(){return this.sync().messages}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){return this.sync().root}get[Symbol.toStringTag](){return"LazyResult"}}return T.registerPostcss=E=>{_=E},T})();return ai=x,x.default=x,a.registerLazyResult(x),e.registerLazyResult(x),ai}var ii,p0;function i1(){if(p0)return ii;p0=1;let t=tl(),e=Fi(),r=Ui(),n=kn(),u=ul();class a{constructor(s,l,o){l=l.toString(),this.stringified=!1,this._processor=s,this._css=l,this._opts=o,this._map=void 0;let c,d=n;this.result=new r(this._processor,c,this._opts),this.result.css=l;let f=this;Object.defineProperty(this.result,"root",{get(){return f.root}});let y=new t(d,c,this._opts,l);if(y.isMap()){let[v,w]=y.generate();v&&(this.result.css=v),w&&(this.result.map=w)}else y.clearAnnotation(),this.result.css=y.css}async(){return this.error?Promise.reject(this.error):Promise.resolve(this.result)}catch(s){return this.async().catch(s)}finally(s){return this.async().then(s,s)}sync(){if(this.error)throw this.error;return this.result}then(s,l){return process.env.NODE_ENV!=="production"&&("from"in this._opts||u("Without `from` option PostCSS could generate wrong source map and will not find Browserslist config. Set it to CSS file path or to `undefined` to prevent this warning.")),this.async().then(s,l)}toString(){return this._css}warnings(){return[]}get content(){return this.result.css}get css(){return this.result.css}get map(){return this.result.map}get messages(){return[]}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){if(this._root)return this._root;let s,l=e;try{s=l(this._css,this._opts)}catch(o){this.error=o}if(this.error)throw this.error;return this._root=s,s}get[Symbol.toStringTag](){return"NoWorkResult"}}return ii=a,a.default=a,ii}var si,m0;function s1(){if(m0)return si;m0=1;let t=Hi(),e=nl(),r=i1(),n=mu();class u{constructor(i=[]){this.version="8.5.2",this.plugins=this.normalize(i)}normalize(i){let s=[];for(let l of i)if(l.postcss===!0?l=l():l.postcss&&(l=l.postcss),typeof l=="object"&&Array.isArray(l.plugins))s=s.concat(l.plugins);else if(typeof l=="object"&&l.postcssPlugin)s.push(l);else if(typeof l=="function")s.push(l);else if(typeof l=="object"&&(l.parse||l.stringify)){if(process.env.NODE_ENV!=="production")throw new Error("PostCSS syntaxes cannot be used as plugins. Instead, please use one of the syntax/parser/stringifier options as outlined in your PostCSS runner documentation.")}else throw new Error(l+" is not a PostCSS plugin");return s}process(i,s={}){return!this.plugins.length&&!s.parser&&!s.stringifier&&!s.syntax?new r(this,i,s):new e(this,i,s)}use(i){return this.plugins=this.plugins.concat(this.normalize([i])),this}}return si=u,u.default=u,n.registerProcessor(u),t.registerProcessor(u),si}var oi,b0;function o1(){if(b0)return oi;b0=1;let t=Pi(),e=Mn(),r=kr(),n=Ri(),u=Dn(),a=Hi(),i=u1(),s=In(),l=nl(),o=el(),c=Ln(),d=Fi(),f=s1(),y=Ui(),v=mu(),w=Bi(),A=kn(),S=rl();function _(...x){return x.length===1&&Array.isArray(x[0])&&(x=x[0]),new f(x)}return _.plugin=function(T,E){let O=!1;function B(...q){console&&console.warn&&!O&&(O=!0,console.warn(T+`: postcss.plugin was deprecated. Migration guide:
https://evilmartians.com/chronicles/postcss-8-plugin-migration`),process.env.LANG&&process.env.LANG.startsWith("cn")&&console.warn(T+`: \u91CC\u9762 postcss.plugin \u88AB\u5F03\u7528. \u8FC1\u79FB\u6307\u5357:
https://www.w3ctech.com/topic/2226`));let C=E(...q);return C.postcssPlugin=T,C.postcssVersion=new f().version,C}let F;return Object.defineProperty(B,"postcss",{get(){return F||(F=B()),F}}),B.process=function(q,C,P){return _([B(P)]).process(q,C)},B},_.stringify=A,_.parse=d,_.fromJSON=i,_.list=o,_.comment=x=>new e(x),_.atRule=x=>new t(x),_.decl=x=>new u(x),_.rule=x=>new w(x),_.root=x=>new v(x),_.document=x=>new a(x),_.CssSyntaxError=n,_.Declaration=u,_.Container=r,_.Processor=f,_.Document=a,_.Comment=e,_.Warning=S,_.AtRule=t,_.Result=y,_.Input=s,_.Rule=w,_.Root=v,_.Node=c,l.registerPostcss(_),oi=_,_.default=_,oi}var c1=o1(),He=Cn(c1),l1=He.stringify;He.fromJSON;He.plugin;var d1=He.parse;He.list;He.document;He.comment;He.atRule;He.rule;He.decl;He.root;He.CssSyntaxError;He.Declaration;He.Container;He.Processor;He.Document;He.Comment;He.Warning;He.AtRule;He.Result;He.Input;He.Rule;He.Root;He.Node;var hn={},pn={},mn={},g0;function al(){if(g0)return mn;g0=1,Object.defineProperty(mn,"__esModule",{value:!0});function t(e){this.after=e.after,this.before=e.before,this.type=e.type,this.value=e.value,this.sourceIndex=e.sourceIndex}return mn.default=t,mn}var v0;function il(){if(v0)return pn;v0=1,Object.defineProperty(pn,"__esModule",{value:!0});var t=al(),e=r(t);function r(u){return u&&u.__esModule?u:{default:u}}function n(u){var a=this;this.constructor(u),this.nodes=u.nodes,this.after===void 0&&(this.after=this.nodes.length>0?this.nodes[this.nodes.length-1].after:""),this.before===void 0&&(this.before=this.nodes.length>0?this.nodes[0].before:""),this.sourceIndex===void 0&&(this.sourceIndex=this.before.length),this.nodes.forEach(function(i){i.parent=a})}return n.prototype=Object.create(e.default.prototype),n.constructor=e.default,n.prototype.walk=function(a,i){for(var s=typeof a=="string"||a instanceof RegExp,l=s?i:a,o=typeof a=="string"?new RegExp(a):a,c=0;c<this.nodes.length;c++){var d=this.nodes[c],f=s?o.test(d.type):!0;if(f&&l&&l(d,c,this.nodes)===!1||d.nodes&&d.walk(a,i)===!1)return!1}return!0},n.prototype.each=function(){for(var a=arguments.length<=0||arguments[0]===void 0?function(){}:arguments[0],i=0;i<this.nodes.length;i++){var s=this.nodes[i];if(a(s,i,this.nodes)===!1)return!1}return!0},pn.default=n,pn}var Pr={},y0;function f1(){if(y0)return Pr;y0=1,Object.defineProperty(Pr,"__esModule",{value:!0}),Pr.parseMediaFeature=a,Pr.parseMediaQuery=i,Pr.parseMediaList=s;var t=al(),e=u(t),r=il(),n=u(r);function u(l){return l&&l.__esModule?l:{default:l}}function a(l){var o=arguments.length<=1||arguments[1]===void 0?0:arguments[1],c=[{mode:"normal",character:null}],d=[],f=0,y="",v=null,w=null,A=o,S=l;l[0]==="("&&l[l.length-1]===")"&&(S=l.substring(1,l.length-1),A++);for(var _=0;_<S.length;_++){var x=S[_];if((x==="'"||x==='"')&&(c[f].isCalculationEnabled===!0?(c.push({mode:"string",isCalculationEnabled:!1,character:x}),f++):c[f].mode==="string"&&c[f].character===x&&S[_-1]!=="\\"&&(c.pop(),f--)),x==="{"?(c.push({mode:"interpolation",isCalculationEnabled:!0}),f++):x==="}"&&(c.pop(),f--),c[f].mode==="normal"&&x===":"){var T=S.substring(_+1);w={type:"value",before:/^(\s*)/.exec(T)[1],after:/(\s*)$/.exec(T)[1],value:T.trim()},w.sourceIndex=w.before.length+_+1+A,v={type:"colon",sourceIndex:_+A,after:w.before,value:":"};break}y+=x}return y={type:"media-feature",before:/^(\s*)/.exec(y)[1],after:/(\s*)$/.exec(y)[1],value:y.trim()},y.sourceIndex=y.before.length+A,d.push(y),v!==null&&(v.before=y.after,d.push(v)),w!==null&&d.push(w),d}function i(l){var o=arguments.length<=1||arguments[1]===void 0?0:arguments[1],c=[],d=0,f=!1,y=void 0;function v(){return{before:"",after:"",value:""}}y=v();for(var w=0;w<l.length;w++){var A=l[w];f?(y.value+=A,(A==="{"||A==="(")&&d++,(A===")"||A==="}")&&d--):A.search(/\s/)!==-1?y.before+=A:(A==="("&&(y.type="media-feature-expression",d++),y.value=A,y.sourceIndex=o+w,f=!0),f&&d===0&&(A===")"||w===l.length-1||l[w+1].search(/\s/)!==-1)&&(["not","only","and"].indexOf(y.value)!==-1&&(y.type="keyword"),y.type==="media-feature-expression"&&(y.nodes=a(y.value,y.sourceIndex)),c.push(Array.isArray(y.nodes)?new n.default(y):new e.default(y)),y=v(),f=!1)}for(var S=0;S<c.length;S++)if(y=c[S],S>0&&(c[S-1].after=y.before),y.type===void 0){if(S>0){if(c[S-1].type==="media-feature-expression"){y.type="keyword";continue}if(c[S-1].value==="not"||c[S-1].value==="only"){y.type="media-type";continue}if(c[S-1].value==="and"){y.type="media-feature-expression";continue}c[S-1].type==="media-type"&&(c[S+1]?y.type=c[S+1].type==="media-feature-expression"?"keyword":"media-feature-expression":y.type="media-feature-expression")}if(S===0){if(!c[S+1]){y.type="media-type";continue}if(c[S+1]&&(c[S+1].type==="media-feature-expression"||c[S+1].type==="keyword")){y.type="media-type";continue}if(c[S+2]){if(c[S+2].type==="media-feature-expression"){y.type="media-type",c[S+1].type="keyword";continue}if(c[S+2].type==="keyword"){y.type="keyword",c[S+1].type="media-type";continue}}if(c[S+3]&&c[S+3].type==="media-feature-expression"){y.type="keyword",c[S+1].type="media-type",c[S+2].type="keyword";continue}}}return c}function s(l){var o=[],c=0,d=0,f=/^(\s*)url\s*\(/.exec(l);if(f!==null){for(var y=f[0].length,v=1;v>0;){var w=l[y];w==="("&&v++,w===")"&&v--,y++}o.unshift(new e.default({type:"url",value:l.substring(0,y).trim(),sourceIndex:f[1].length,before:f[1],after:/^(\s*)/.exec(l.substring(y))[1]})),c=y}for(var A=c;A<l.length;A++){var S=l[A];if(S==="("&&d++,S===")"&&d--,d===0&&S===","){var _=l.substring(c,A),x=/^(\s*)/.exec(_)[1];o.push(new n.default({type:"media-query",value:_.trim(),sourceIndex:c+x.length,nodes:i(_,c),before:x,after:/(\s*)$/.exec(_)[1]})),c=A+1}}var T=l.substring(c),E=/^(\s*)/.exec(T)[1];return o.push(new n.default({type:"media-query",value:T.trim(),sourceIndex:c+E.length,nodes:i(T,c),before:E,after:/(\s*)$/.exec(T)[1]})),o}return Pr}var w0;function h1(){if(w0)return hn;w0=1,Object.defineProperty(hn,"__esModule",{value:!0}),hn.default=u;var t=il(),e=n(t),r=f1();function n(a){return a&&a.__esModule?a:{default:a}}function u(a){return new e.default({nodes:(0,r.parseMediaList)(a),type:"media-query-list",value:a.trim()})}return hn}var p1=h1(),ci=Cn(p1),ke=function(t){return t.Root="root",t.Text="text",t.Directive="directive",t.Comment="comment",t.Script="script",t.Style="style",t.Tag="tag",t.CDATA="cdata",t.Doctype="doctype",t}(ke||{});function m1(t){return t.type===ke.Tag||t.type===ke.Script||t.type===ke.Style}var b1=ke.Root,g1=ke.Text,v1=ke.Directive,y1=ke.Comment,w1=ke.Script,x1=ke.Style,_1=ke.Tag,E1=ke.CDATA,S1=ke.Doctype,yn=class{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(e){this.parent=e}get previousSibling(){return this.prev}set previousSibling(e){this.prev=e}get nextSibling(){return this.next}set nextSibling(e){this.next=e}cloneNode(e=!1){return ol(this,e)}},fu=class extends yn{constructor(e){super(),this.data=e}get nodeValue(){return this.data}set nodeValue(e){this.data=e}},Nr=class extends fu{constructor(){super(...arguments),this.type=ke.Text}get nodeType(){return 3}},wn=class extends fu{constructor(){super(...arguments),this.type=ke.Comment}get nodeType(){return 8}},xn=class extends fu{constructor(e,r){super(r),this.name=e,this.type=ke.Directive}get nodeType(){return 1}},hu=class extends yn{constructor(e){super(),this.children=e}get firstChild(){var e;return(e=this.children[0])!==null&&e!==void 0?e:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(e){this.children=e}},_n=class extends hu{constructor(){super(...arguments),this.type=ke.CDATA}get nodeType(){return 4}},pu=class extends hu{constructor(){super(...arguments),this.type=ke.Root}get nodeType(){return 9}},Fr=class extends hu{constructor(e,r,n=[],u=e==="script"?ke.Script:e==="style"?ke.Style:ke.Tag){super(n),this.name=e,this.attribs=r,this.type=u}get nodeType(){return 1}get tagName(){return this.name}set tagName(e){this.name=e}get attributes(){return Object.keys(this.attribs).map(e=>{var r,n;return{name:e,value:this.attribs[e],namespace:(r=this["x-attribsNamespace"])===null||r===void 0?void 0:r[e],prefix:(n=this["x-attribsPrefix"])===null||n===void 0?void 0:n[e]}})}};function ot(t){return m1(t)}function On(t){return t.type===ke.CDATA}function Ar(t){return t.type===ke.Text}function Vi(t){return t.type===ke.Comment}function T1(t){return t.type===ke.Directive}function sl(t){return t.type===ke.Root}function vt(t){return Object.prototype.hasOwnProperty.call(t,"children")}function ol(t,e=!1){let r;if(Ar(t))r=new Nr(t.data);else if(Vi(t))r=new wn(t.data);else if(ot(t)){let n=e?li(t.children):[],u=new Fr(t.name,ye({},t.attribs),n);n.forEach(a=>a.parent=u),t.namespace!=null&&(u.namespace=t.namespace),t["x-attribsNamespace"]&&(u["x-attribsNamespace"]=ye({},t["x-attribsNamespace"])),t["x-attribsPrefix"]&&(u["x-attribsPrefix"]=ye({},t["x-attribsPrefix"])),r=u}else if(On(t)){let n=e?li(t.children):[],u=new _n(n);n.forEach(a=>a.parent=u),r=u}else if(sl(t)){let n=e?li(t.children):[],u=new pu(n);n.forEach(a=>a.parent=u),t["x-mode"]&&(u["x-mode"]=t["x-mode"]),r=u}else if(T1(t)){let n=new xn(t.name,t.data);t["x-name"]!=null&&(n["x-name"]=t["x-name"],n["x-publicId"]=t["x-publicId"],n["x-systemId"]=t["x-systemId"]),r=n}else throw new Error(`Not implemented yet: ${t.type}`);return r.startIndex=t.startIndex,r.endIndex=t.endIndex,t.sourceCodeLocation!=null&&(r.sourceCodeLocation=t.sourceCodeLocation),r}function li(t){let e=t.map(r=>ol(r,!0));for(let r=1;r<e.length;r++)e[r].prev=e[r-1],e[r-1].next=e[r];return e}var x0={withStartIndices:!1,withEndIndices:!1,xmlMode:!1},wi=class{constructor(e,r,n){this.dom=[],this.root=new pu(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,typeof r=="function"&&(n=r,r=x0),typeof e=="object"&&(r=e,e=void 0),this.callback=e??null,this.options=r??x0,this.elementCB=n??null}onparserinit(e){this.parser=e}onreset(){this.dom=[],this.root=new pu(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(e){this.handleCallback(e)}onclosetag(){this.lastNode=null;let e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)}onopentag(e,r){let n=this.options.xmlMode?ke.Tag:void 0,u=new Fr(e,r,void 0,n);this.addNode(u),this.tagStack.push(u)}ontext(e){let{lastNode:r}=this;if(r&&r.type===ke.Text)r.data+=e,this.options.withEndIndices&&(r.endIndex=this.parser.endIndex);else{let n=new Nr(e);this.addNode(n),this.lastNode=n}}oncomment(e){if(this.lastNode&&this.lastNode.type===ke.Comment){this.lastNode.data+=e;return}let r=new wn(e);this.addNode(r),this.lastNode=r}oncommentend(){this.lastNode=null}oncdatastart(){let e=new Nr(""),r=new _n([e]);this.addNode(r),e.parent=r,this.lastNode=e}oncdataend(){this.lastNode=null}onprocessinginstruction(e,r){let n=new xn(e,r);this.addNode(n)}handleCallback(e){if(typeof this.callback=="function")this.callback(e,this.dom);else if(e)throw e}addNode(e){let r=this.tagStack[this.tagStack.length-1],n=r.children[r.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),r.children.push(e),n&&(e.prev=n,n.next=e),e.parent=r,this.lastNode=null}};var di;var Im=(di=String.fromCodePoint)!==null&&di!==void 0?di:function(t){let e="";return t>65535&&(t-=65536,e+=String.fromCharCode(t>>>10&1023|55296),t=56320|t&1023),e+=String.fromCharCode(t),e};var _0=/["&'<>$\x80-\uFFFF]/g,N1=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]),A1=String.prototype.codePointAt!=null?(t,e)=>t.codePointAt(e):(t,e)=>(t.charCodeAt(e)&64512)===55296?(t.charCodeAt(e)-55296)*1024+t.charCodeAt(e+1)-56320+65536:t.charCodeAt(e);function cl(t){let e="",r=0,n;for(;(n=_0.exec(t))!==null;){let u=n.index,a=t.charCodeAt(u),i=N1.get(a);i!==void 0?(e+=t.substring(r,u)+i,r=u+1):(e+=`${t.substring(r,u)}&#x${A1(t,u).toString(16)};`,r=_0.lastIndex+=+((a&64512)===55296))}return e+t.substr(r)}function ll(t,e){return function(n){let u,a=0,i="";for(;u=t.exec(n);)a!==u.index&&(i+=n.substring(a,u.index)),i+=e.get(u[0].charCodeAt(0)),a=u.index+1;return i+n.substring(a)}}var C1=ll(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),k1=ll(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]]));var L1=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(t=>[t.toLowerCase(),t])),M1=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(t=>[t.toLowerCase(),t])),D1=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function I1(t){return t.replace(/"/g,"&quot;")}function O1(t,e){var r;if(!t)return;let n=((r=e.encodeEntities)!==null&&r!==void 0?r:e.decodeEntities)===!1?I1:e.xmlMode||e.encodeEntities!=="utf8"?cl:C1;return Object.keys(t).map(u=>{var a,i;let s=(a=t[u])!==null&&a!==void 0?a:"";return e.xmlMode==="foreign"&&(u=(i=M1.get(u))!==null&&i!==void 0?i:u),!e.emptyAttrs&&!e.xmlMode&&s===""?u:`${u}="${n(s)}"`}).join(" ")}var E0=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function Rn(t,e={}){let r="length"in t?t:[t],n="";for(let u=0;u<r.length;u++)n+=R1(r[u],e);return n}function R1(t,e){switch(t.type){case b1:return Rn(t.children,e);case S1:case v1:return B1(t);case y1:return V1(t);case E1:return U1(t);case w1:case x1:case _1:return H1(t,e);case g1:return F1(t,e)}}var q1=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),P1=new Set(["svg","math"]);function H1(t,e){var r;e.xmlMode==="foreign"&&(t.name=(r=L1.get(t.name))!==null&&r!==void 0?r:t.name,t.parent&&q1.has(t.parent.name)&&(e=je(ye({},e),{xmlMode:!1}))),!e.xmlMode&&P1.has(t.name)&&(e=je(ye({},e),{xmlMode:"foreign"}));let n=`<${t.name}`,u=O1(t.attribs,e);return u&&(n+=` ${u}`),t.children.length===0&&(e.xmlMode?e.selfClosingTags!==!1:e.selfClosingTags&&E0.has(t.name))?(e.xmlMode||(n+=" "),n+="/>"):(n+=">",t.children.length>0&&(n+=Rn(t.children,e)),(e.xmlMode||!E0.has(t.name))&&(n+=`</${t.name}>`)),n}function B1(t){return`<${t.data}>`}function F1(t,e){var r;let n=t.data||"";return((r=e.encodeEntities)!==null&&r!==void 0?r:e.decodeEntities)!==!1&&!(!e.xmlMode&&t.parent&&D1.has(t.parent.name))&&(n=e.xmlMode||e.encodeEntities!=="utf8"?cl(n):k1(n)),n}function U1(t){return`<![CDATA[${t.children[0].data}]]>`}function V1(t){return`<!--${t.data}-->`}function dl(t,e){return Rn(t,e)}function j1(t,e){return vt(t)?t.children.map(r=>dl(r,e)).join(""):""}function lu(t){return Array.isArray(t)?t.map(lu).join(""):ot(t)?t.name==="br"?`
`:lu(t.children):On(t)?lu(t.children):Ar(t)?t.data:""}function En(t){return Array.isArray(t)?t.map(En).join(""):vt(t)&&!Vi(t)?En(t.children):Ar(t)?t.data:""}function xi(t){return Array.isArray(t)?t.map(xi).join(""):vt(t)&&(t.type===ke.Tag||On(t))?xi(t.children):Ar(t)?t.data:""}function fl(t){return vt(t)?t.children:[]}function hl(t){return t.parent||null}function z1(t){let e=hl(t);if(e!=null)return fl(e);let r=[t],{prev:n,next:u}=t;for(;n!=null;)r.unshift(n),{prev:n}=n;for(;u!=null;)r.push(u),{next:u}=u;return r}function G1(t,e){var r;return(r=t.attribs)===null||r===void 0?void 0:r[e]}function $1(t,e){return t.attribs!=null&&Object.prototype.hasOwnProperty.call(t.attribs,e)&&t.attribs[e]!=null}function W1(t){return t.name}function X1(t){let{next:e}=t;for(;e!==null&&!ot(e);)({next:e}=e);return e}function Q1(t){let{prev:e}=t;for(;e!==null&&!ot(e);)({prev:e}=e);return e}function Cr(t){if(t.prev&&(t.prev.next=t.next),t.next&&(t.next.prev=t.prev),t.parent){let e=t.parent.children,r=e.lastIndexOf(t);r>=0&&e.splice(r,1)}t.next=null,t.prev=null,t.parent=null}function Z1(t,e){let r=e.prev=t.prev;r&&(r.next=e);let n=e.next=t.next;n&&(n.prev=e);let u=e.parent=t.parent;if(u){let a=u.children;a[a.lastIndexOf(t)]=e,t.parent=null}}function _i(t,e){if(Cr(e),e.next=null,e.parent=t,t.children.push(e)>1){let r=t.children[t.children.length-2];r.next=e,e.prev=r}else e.prev=null}function K1(t,e){Cr(e);let{parent:r}=t,n=t.next;if(e.next=n,e.prev=t,t.next=e,e.parent=r,n){if(n.prev=e,r){let u=r.children;u.splice(u.lastIndexOf(n),0,e)}}else r&&r.children.push(e)}function Y1(t,e){if(Cr(e),e.parent=t,e.prev=null,t.children.unshift(e)!==1){let r=t.children[1];r.prev=e,e.next=r}else e.next=null}function pl(t,e){Cr(e);let{parent:r}=t;if(r){let n=r.children;n.splice(n.indexOf(t),0,e)}t.prev&&(t.prev.next=e),e.parent=r,e.prev=t.prev,e.next=t,t.prev=e}function bu(t,e,r=!0,n=1/0){return ml(t,Array.isArray(e)?e:[e],r,n)}function ml(t,e,r,n){let u=[],a=[Array.isArray(e)?e:[e]],i=[0];for(;;){if(i[0]>=a[0].length){if(i.length===1)return u;a.shift(),i.shift();continue}let s=a[0][i[0]++];if(t(s)&&(u.push(s),--n<=0))return u;r&&vt(s)&&s.children.length>0&&(i.unshift(0),a.unshift(s.children))}}function J1(t,e){return e.find(t)}function ji(t,e,r=!0){let n=Array.isArray(e)?e:[e];for(let u=0;u<n.length;u++){let a=n[u];if(ot(a)&&t(a))return a;if(r&&vt(a)&&a.children.length>0){let i=ji(t,a.children,!0);if(i)return i}}return null}function bl(t,e){return(Array.isArray(e)?e:[e]).some(r=>ot(r)&&t(r)||vt(r)&&bl(t,r.children))}function eh(t,e){let r=[],n=[Array.isArray(e)?e:[e]],u=[0];for(;;){if(u[0]>=n[0].length){if(n.length===1)return r;n.shift(),u.shift();continue}let a=n[0][u[0]++];ot(a)&&t(a)&&r.push(a),vt(a)&&a.children.length>0&&(u.unshift(0),n.unshift(a.children))}}var Sn={tag_name(t){return typeof t=="function"?e=>ot(e)&&t(e.name):t==="*"?ot:e=>ot(e)&&e.name===t},tag_type(t){return typeof t=="function"?e=>t(e.type):e=>e.type===t},tag_contains(t){return typeof t=="function"?e=>Ar(e)&&t(e.data):e=>Ar(e)&&e.data===t}};function zi(t,e){return typeof e=="function"?r=>ot(r)&&e(r.attribs[t]):r=>ot(r)&&r.attribs[t]===e}function th(t,e){return r=>t(r)||e(r)}function gl(t){let e=Object.keys(t).map(r=>{let n=t[r];return Object.prototype.hasOwnProperty.call(Sn,r)?Sn[r](n):zi(r,n)});return e.length===0?null:e.reduce(th)}function rh(t,e){let r=gl(t);return r?r(e):!0}function uh(t,e,r,n=1/0){let u=gl(t);return u?bu(u,e,r,n):[]}function nh(t,e,r=!0){return Array.isArray(e)||(e=[e]),ji(zi("id",t),e,r)}function Ur(t,e,r=!0,n=1/0){return bu(Sn.tag_name(t),e,r,n)}function ah(t,e,r=!0,n=1/0){return bu(zi("class",t),e,r,n)}function ih(t,e,r=!0,n=1/0){return bu(Sn.tag_type(t),e,r,n)}function sh(t){let e=t.length;for(;--e>=0;){let r=t[e];if(e>0&&t.lastIndexOf(r,e-1)>=0){t.splice(e,1);continue}for(let n=r.parent;n;n=n.parent)if(t.includes(n)){t.splice(e,1);break}}return t}var bt=function(t){return t[t.DISCONNECTED=1]="DISCONNECTED",t[t.PRECEDING=2]="PRECEDING",t[t.FOLLOWING=4]="FOLLOWING",t[t.CONTAINS=8]="CONTAINS",t[t.CONTAINED_BY=16]="CONTAINED_BY",t}(bt||{});function vl(t,e){let r=[],n=[];if(t===e)return 0;let u=vt(t)?t:t.parent;for(;u;)r.unshift(u),u=u.parent;for(u=vt(e)?e:e.parent;u;)n.unshift(u),u=u.parent;let a=Math.min(r.length,n.length),i=0;for(;i<a&&r[i]===n[i];)i++;if(i===0)return bt.DISCONNECTED;let s=r[i-1],l=s.children,o=r[i],c=n[i];return l.indexOf(o)>l.indexOf(c)?s===e?bt.FOLLOWING|bt.CONTAINED_BY:bt.FOLLOWING:s===t?bt.PRECEDING|bt.CONTAINS:bt.PRECEDING}function oh(t){return t=t.filter((e,r,n)=>!n.includes(e,r+1)),t.sort((e,r)=>{let n=vl(e,r);return n&bt.PRECEDING?-1:n&bt.FOLLOWING?1:0}),t}function ch(t){let e=Tn(ph,t);return e?e.name==="feed"?lh(e):dh(e):null}function lh(t){var e;let r=t.children,n={type:"atom",items:Ur("entry",r).map(i=>{var s;let{children:l}=i,o={media:yl(l)};it(o,"id","id",l),it(o,"title","title",l);let c=(s=Tn("link",l))===null||s===void 0?void 0:s.attribs.href;c&&(o.link=c);let d=nr("summary",l)||nr("content",l);d&&(o.description=d);let f=nr("updated",l);return f&&(o.pubDate=new Date(f)),o})};it(n,"id","id",r),it(n,"title","title",r);let u=(e=Tn("link",r))===null||e===void 0?void 0:e.attribs.href;u&&(n.link=u),it(n,"description","subtitle",r);let a=nr("updated",r);return a&&(n.updated=new Date(a)),it(n,"author","email",r,!0),n}function dh(t){var e,r;let n=(r=(e=Tn("channel",t.children))===null||e===void 0?void 0:e.children)!==null&&r!==void 0?r:[],u={type:t.name.substr(0,3),id:"",items:Ur("item",t.children).map(i=>{let{children:s}=i,l={media:yl(s)};it(l,"id","guid",s),it(l,"title","title",s),it(l,"link","link",s),it(l,"description","description",s);let o=nr("pubDate",s)||nr("dc:date",s);return o&&(l.pubDate=new Date(o)),l})};it(u,"title","title",n),it(u,"link","link",n),it(u,"description","description",n);let a=nr("lastBuildDate",n);return a&&(u.updated=new Date(a)),it(u,"author","managingEditor",n,!0),u}var fh=["url","type","lang"],hh=["fileSize","bitrate","framerate","samplingrate","channels","duration","height","width"];function yl(t){return Ur("media:content",t).map(e=>{let{attribs:r}=e,n={medium:r.medium,isDefault:!!r.isDefault};for(let u of fh)r[u]&&(n[u]=r[u]);for(let u of hh)r[u]&&(n[u]=parseInt(r[u],10));return r.expression&&(n.expression=r.expression),n})}function Tn(t,e){return Ur(t,e,!0,1)[0]}function nr(t,e,r=!1){return En(Ur(t,e,r,1)).trim()}function it(t,e,r,n,u=!1){let a=nr(r,n,u);a&&(t[e]=a)}function ph(t){return t==="rss"||t==="feed"||t==="rdf:RDF"}var wl=Object.freeze({__proto__:null,get DocumentPosition(){return bt},append:K1,appendChild:_i,compareDocumentPosition:vl,existsOne:bl,filter:bu,find:ml,findAll:eh,findOne:ji,findOneChild:J1,getAttributeValue:G1,getChildren:fl,getElementById:nh,getElements:uh,getElementsByClassName:ah,getElementsByTagName:Ur,getElementsByTagType:ih,getFeed:ch,getInnerHTML:j1,getName:W1,getOuterHTML:dl,getParent:hl,getSiblings:z1,getText:lu,hasAttrib:$1,hasChildren:vt,innerText:xi,isCDATA:On,isComment:Vi,isDocument:sl,isTag:ot,isText:Ar,nextElementSibling:X1,prepend:pl,prependChild:Y1,prevElementSibling:Q1,removeElement:Cr,removeSubsets:sh,replaceElement:Z1,testElement:rh,textContent:En,uniqueSort:oh}),fi,S0;function mh(){return S0||(S0=1,fi={trueFunc:function(){return!0},falseFunc:function(){return!1}}),fi}var bh=mh(),he=Cn(bh),ce=function(t){return t.Attribute="attribute",t.Pseudo="pseudo",t.PseudoElement="pseudo-element",t.Tag="tag",t.Universal="universal",t.Adjacent="adjacent",t.Child="child",t.Descendant="descendant",t.Parent="parent",t.Sibling="sibling",t.ColumnCombinator="column-combinator",t}(ce||{}),Qe=function(t){return t.Any="any",t.Element="element",t.End="end",t.Equals="equals",t.Exists="exists",t.Hyphen="hyphen",t.Not="not",t.Start="start",t}(Qe||{}),T0=/^[^\\#]?(?:\\(?:[\da-f]{1,6}\s?|.)|[\w\-\u00b0-\uFFFF])+/,gh=/\\([\da-f]{1,6}\s?|(\s)|.)/gi,vh=new Map([[126,Qe.Element],[94,Qe.Start],[36,Qe.End],[42,Qe.Any],[33,Qe.Not],[124,Qe.Hyphen]]),yh=new Set(["has","not","matches","is","where","host","host-context"]);function wh(t){switch(t.type){case ce.Adjacent:case ce.Child:case ce.Descendant:case ce.Parent:case ce.Sibling:case ce.ColumnCombinator:return!0;default:return!1}}var xh=new Set(["contains","icontains"]);function _h(t,e,r){let n=parseInt(e,16)-65536;return n!==n||r?e:n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,n&1023|56320)}function ou(t){return t.replace(gh,_h)}function hi(t){return t===39||t===34}function N0(t){return t===32||t===9||t===10||t===12||t===13}function Gi(t){let e=[],r=xl(e,`${t}`,0);if(r<t.length)throw new Error(`Unmatched selector: ${t.slice(r)}`);return e}function xl(t,e,r){let n=[];function u(f){let y=e.slice(r+f).match(T0);if(!y)throw new Error(`Expected name, found ${e.slice(r)}`);let[v]=y;return r+=f+v.length,ou(v)}function a(f){for(r+=f;r<e.length&&N0(e.charCodeAt(r));)r++}function i(){r+=1;let f=r,y=1;for(;y>0&&r<e.length;r++)e.charCodeAt(r)===40&&!s(r)?y++:e.charCodeAt(r)===41&&!s(r)&&y--;if(y)throw new Error("Parenthesis not matched");return ou(e.slice(f,r-1))}function s(f){let y=0;for(;e.charCodeAt(--f)===92;)y++;return(y&1)===1}function l(){if(n.length>0&&wh(n[n.length-1]))throw new Error("Did not expect successive traversals.")}function o(f){if(n.length>0&&n[n.length-1].type===ce.Descendant){n[n.length-1].type=f;return}l(),n.push({type:f})}function c(f,y){n.push({type:ce.Attribute,name:f,action:y,value:u(1),namespace:null,ignoreCase:"quirks"})}function d(){if(n.length&&n[n.length-1].type===ce.Descendant&&n.pop(),n.length===0)throw new Error("Empty sub-selector");t.push(n)}if(a(0),e.length===r)return r;e:for(;r<e.length;){let f=e.charCodeAt(r);switch(f){case 32:case 9:case 10:case 12:case 13:{(n.length===0||n[0].type!==ce.Descendant)&&(l(),n.push({type:ce.Descendant})),a(1);break}case 62:{o(ce.Child),a(1);break}case 60:{o(ce.Parent),a(1);break}case 126:{o(ce.Sibling),a(1);break}case 43:{o(ce.Adjacent),a(1);break}case 46:{c("class",Qe.Element);break}case 35:{c("id",Qe.Equals);break}case 91:{a(1);let y,v=null;e.charCodeAt(r)===124?y=u(1):e.startsWith("*|",r)?(v="*",y=u(2)):(y=u(0),e.charCodeAt(r)===124&&e.charCodeAt(r+1)!==61&&(v=y,y=u(1))),a(0);let w=Qe.Exists,A=vh.get(e.charCodeAt(r));if(A){if(w=A,e.charCodeAt(r+1)!==61)throw new Error("Expected `=`");a(2)}else e.charCodeAt(r)===61&&(w=Qe.Equals,a(1));let S="",_=null;if(w!=="exists"){if(hi(e.charCodeAt(r))){let E=e.charCodeAt(r),O=r+1;for(;O<e.length&&(e.charCodeAt(O)!==E||s(O));)O+=1;if(e.charCodeAt(O)!==E)throw new Error("Attribute value didn't end");S=ou(e.slice(r+1,O)),r=O+1}else{let E=r;for(;r<e.length&&(!N0(e.charCodeAt(r))&&e.charCodeAt(r)!==93||s(r));)r+=1;S=ou(e.slice(E,r))}a(0);let T=e.charCodeAt(r)|32;T===115?(_=!1,a(1)):T===105&&(_=!0,a(1))}if(e.charCodeAt(r)!==93)throw new Error("Attribute selector didn't terminate");r+=1;let x={type:ce.Attribute,name:y,action:w,value:S,namespace:v,ignoreCase:_};n.push(x);break}case 58:{if(e.charCodeAt(r+1)===58){n.push({type:ce.PseudoElement,name:u(2).toLowerCase(),data:e.charCodeAt(r)===40?i():null});continue}let y=u(1).toLowerCase(),v=null;if(e.charCodeAt(r)===40)if(yh.has(y)){if(hi(e.charCodeAt(r+1)))throw new Error(`Pseudo-selector ${y} cannot be quoted`);if(v=[],r=xl(v,e,r+1),e.charCodeAt(r)!==41)throw new Error(`Missing closing parenthesis in :${y} (${e})`);r+=1}else{if(v=i(),xh.has(y)){let w=v.charCodeAt(0);w===v.charCodeAt(v.length-1)&&hi(w)&&(v=v.slice(1,-1))}v=ou(v)}n.push({type:ce.Pseudo,name:y,data:v});break}case 44:{d(),n=[],a(1);break}default:{if(e.startsWith("/*",r)){let w=e.indexOf("*/",r+2);if(w<0)throw new Error("Comment was not terminated");r=w+2,n.length===0&&a(0);break}let y=null,v;if(f===42)r+=1,v="*";else if(f===124){if(v="",e.charCodeAt(r+1)===124){o(ce.ColumnCombinator),a(2);break}}else if(T0.test(e.slice(r)))v=u(0);else break e;e.charCodeAt(r)===124&&e.charCodeAt(r+1)!==124&&(y=v,e.charCodeAt(r+1)===42?(v="*",r+=2):v=u(1)),n.push(v==="*"?{type:ce.Universal,namespace:y}:{type:ce.Tag,name:v,namespace:y})}}}return d(),r}var Eh=["\\",'"'],Om=[...Eh,"(",")"],_l=new Map([[ce.Universal,50],[ce.Tag,30],[ce.Attribute,1],[ce.Pseudo,0]]);function $i(t){return!_l.has(t.type)}var Sh=new Map([[Qe.Exists,10],[Qe.Equals,8],[Qe.Not,7],[Qe.Start,6],[Qe.End,6],[Qe.Any,5]]);function Th(t){let e=t.map(El);for(let r=1;r<t.length;r++){let n=e[r];if(!(n<0))for(let u=r-1;u>=0&&n<e[u];u--){let a=t[u+1];t[u+1]=t[u],t[u]=a,e[u+1]=e[u],e[u]=n}}}function El(t){var e,r;let n=(e=_l.get(t.type))!==null&&e!==void 0?e:-1;return t.type===ce.Attribute?(n=(r=Sh.get(t.action))!==null&&r!==void 0?r:4,t.action===Qe.Equals&&t.name==="id"&&(n=9),t.ignoreCase&&(n>>=1)):t.type===ce.Pseudo&&(t.data?t.name==="has"||t.name==="contains"?n=0:Array.isArray(t.data)?(n=Math.min(...t.data.map(u=>Math.min(...u.map(El)))),n<0&&(n=0)):n=2:n=3),n}var Nh=/[-[\]{}()*+?.,\\^$|#\s]/g;function A0(t){return t.replace(Nh,"\\$&")}var Ah=new Set(["accept","accept-charset","align","alink","axis","bgcolor","charset","checked","clear","codetype","color","compact","declare","defer","dir","direction","disabled","enctype","face","frame","hreflang","http-equiv","lang","language","link","media","method","multiple","nohref","noresize","noshade","nowrap","readonly","rel","rev","rules","scope","scrolling","selected","shape","target","text","type","valign","valuetype","vlink"]);function Er(t,e){return typeof t.ignoreCase=="boolean"?t.ignoreCase:t.ignoreCase==="quirks"?!!e.quirksMode:!e.xmlMode&&Ah.has(t.name)}var Ch={equals(t,e,r){let{adapter:n}=r,{name:u}=e,{value:a}=e;return Er(e,r)?(a=a.toLowerCase(),i=>{let s=n.getAttributeValue(i,u);return s!=null&&s.length===a.length&&s.toLowerCase()===a&&t(i)}):i=>n.getAttributeValue(i,u)===a&&t(i)},hyphen(t,e,r){let{adapter:n}=r,{name:u}=e,{value:a}=e,i=a.length;return Er(e,r)?(a=a.toLowerCase(),function(l){let o=n.getAttributeValue(l,u);return o!=null&&(o.length===i||o.charAt(i)==="-")&&o.substr(0,i).toLowerCase()===a&&t(l)}):function(l){let o=n.getAttributeValue(l,u);return o!=null&&(o.length===i||o.charAt(i)==="-")&&o.substr(0,i)===a&&t(l)}},element(t,e,r){let{adapter:n}=r,{name:u,value:a}=e;if(/\s/.test(a))return he.falseFunc;let i=new RegExp(`(?:^|\\s)${A0(a)}(?:$|\\s)`,Er(e,r)?"i":"");return function(l){let o=n.getAttributeValue(l,u);return o!=null&&o.length>=a.length&&i.test(o)&&t(l)}},exists(t,{name:e},{adapter:r}){return n=>r.hasAttrib(n,e)&&t(n)},start(t,e,r){let{adapter:n}=r,{name:u}=e,{value:a}=e,i=a.length;return i===0?he.falseFunc:Er(e,r)?(a=a.toLowerCase(),s=>{let l=n.getAttributeValue(s,u);return l!=null&&l.length>=i&&l.substr(0,i).toLowerCase()===a&&t(s)}):s=>{var l;return!!(!((l=n.getAttributeValue(s,u))===null||l===void 0)&&l.startsWith(a))&&t(s)}},end(t,e,r){let{adapter:n}=r,{name:u}=e,{value:a}=e,i=-a.length;return i===0?he.falseFunc:Er(e,r)?(a=a.toLowerCase(),s=>{var l;return((l=n.getAttributeValue(s,u))===null||l===void 0?void 0:l.substr(i).toLowerCase())===a&&t(s)}):s=>{var l;return!!(!((l=n.getAttributeValue(s,u))===null||l===void 0)&&l.endsWith(a))&&t(s)}},any(t,e,r){let{adapter:n}=r,{name:u,value:a}=e;if(a==="")return he.falseFunc;if(Er(e,r)){let i=new RegExp(A0(a),"i");return function(l){let o=n.getAttributeValue(l,u);return o!=null&&o.length>=a.length&&i.test(o)&&t(l)}}return i=>{var s;return!!(!((s=n.getAttributeValue(i,u))===null||s===void 0)&&s.includes(a))&&t(i)}},not(t,e,r){let{adapter:n}=r,{name:u}=e,{value:a}=e;return a===""?i=>!!n.getAttributeValue(i,u)&&t(i):Er(e,r)?(a=a.toLowerCase(),i=>{let s=n.getAttributeValue(i,u);return(s==null||s.length!==a.length||s.toLowerCase()!==a)&&t(i)}):i=>n.getAttributeValue(i,u)!==a&&t(i)}},kh=new Set([9,10,12,13,32]),C0=48,Lh=57;function Mh(t){if(t=t.trim().toLowerCase(),t==="even")return[2,0];if(t==="odd")return[2,1];let e=0,r=0,n=a(),u=i();if(e<t.length&&t.charAt(e)==="n"&&(e++,r=n*(u??1),s(),e<t.length?(n=a(),s(),u=i()):n=u=0),u===null||e<t.length)throw new Error(`n-th rule couldn't be parsed ('${t}')`);return[r,n*u];function a(){return t.charAt(e)==="-"?(e++,-1):(t.charAt(e)==="+"&&e++,1)}function i(){let l=e,o=0;for(;e<t.length&&t.charCodeAt(e)>=C0&&t.charCodeAt(e)<=Lh;)o=o*10+(t.charCodeAt(e)-C0),e++;return e===l?null:o}function s(){for(;e<t.length&&kh.has(t.charCodeAt(e));)e++}}function Dh(t){let e=t[0],r=t[1]-1;if(r<0&&e<=0)return he.falseFunc;if(e===-1)return a=>a<=r;if(e===0)return a=>a===r;if(e===1)return r<0?he.trueFunc:a=>a>=r;let n=Math.abs(e),u=(r%n+n)%n;return e>1?a=>a>=r&&a%n===u:a=>a<=r&&a%n===u}function bn(t){return Dh(Mh(t))}function gn(t,e){return r=>{let n=e.getParent(r);return n!=null&&e.isTag(n)&&t(r)}}var Ei={contains(t,e,{adapter:r}){return function(u){return t(u)&&r.getText(u).includes(e)}},icontains(t,e,{adapter:r}){let n=e.toLowerCase();return function(a){return t(a)&&r.getText(a).toLowerCase().includes(n)}},"nth-child"(t,e,{adapter:r,equals:n}){let u=bn(e);return u===he.falseFunc?he.falseFunc:u===he.trueFunc?gn(t,r):function(i){let s=r.getSiblings(i),l=0;for(let o=0;o<s.length&&!n(i,s[o]);o++)r.isTag(s[o])&&l++;return u(l)&&t(i)}},"nth-last-child"(t,e,{adapter:r,equals:n}){let u=bn(e);return u===he.falseFunc?he.falseFunc:u===he.trueFunc?gn(t,r):function(i){let s=r.getSiblings(i),l=0;for(let o=s.length-1;o>=0&&!n(i,s[o]);o--)r.isTag(s[o])&&l++;return u(l)&&t(i)}},"nth-of-type"(t,e,{adapter:r,equals:n}){let u=bn(e);return u===he.falseFunc?he.falseFunc:u===he.trueFunc?gn(t,r):function(i){let s=r.getSiblings(i),l=0;for(let o=0;o<s.length;o++){let c=s[o];if(n(i,c))break;r.isTag(c)&&r.getName(c)===r.getName(i)&&l++}return u(l)&&t(i)}},"nth-last-of-type"(t,e,{adapter:r,equals:n}){let u=bn(e);return u===he.falseFunc?he.falseFunc:u===he.trueFunc?gn(t,r):function(i){let s=r.getSiblings(i),l=0;for(let o=s.length-1;o>=0;o--){let c=s[o];if(n(i,c))break;r.isTag(c)&&r.getName(c)===r.getName(i)&&l++}return u(l)&&t(i)}},root(t,e,{adapter:r}){return n=>{let u=r.getParent(n);return(u==null||!r.isTag(u))&&t(n)}},scope(t,e,r,n){let{equals:u}=r;return!n||n.length===0?Ei.root(t,e,r):n.length===1?a=>u(n[0],a)&&t(a):a=>n.includes(a)&&t(a)},hover:pi("isHovered"),visited:pi("isVisited"),active:pi("isActive")};function pi(t){return function(r,n,{adapter:u}){let a=u[t];return typeof a!="function"?he.falseFunc:function(s){return a(s)&&r(s)}}}var k0={empty(t,{adapter:e}){return!e.getChildren(t).some(r=>e.isTag(r)||e.getText(r)!=="")},"first-child"(t,{adapter:e,equals:r}){if(e.prevElementSibling)return e.prevElementSibling(t)==null;let n=e.getSiblings(t).find(u=>e.isTag(u));return n!=null&&r(t,n)},"last-child"(t,{adapter:e,equals:r}){let n=e.getSiblings(t);for(let u=n.length-1;u>=0;u--){if(r(t,n[u]))return!0;if(e.isTag(n[u]))break}return!1},"first-of-type"(t,{adapter:e,equals:r}){let n=e.getSiblings(t),u=e.getName(t);for(let a=0;a<n.length;a++){let i=n[a];if(r(t,i))return!0;if(e.isTag(i)&&e.getName(i)===u)break}return!1},"last-of-type"(t,{adapter:e,equals:r}){let n=e.getSiblings(t),u=e.getName(t);for(let a=n.length-1;a>=0;a--){let i=n[a];if(r(t,i))return!0;if(e.isTag(i)&&e.getName(i)===u)break}return!1},"only-of-type"(t,{adapter:e,equals:r}){let n=e.getName(t);return e.getSiblings(t).every(u=>r(t,u)||!e.isTag(u)||e.getName(u)!==n)},"only-child"(t,{adapter:e,equals:r}){return e.getSiblings(t).every(n=>r(t,n)||!e.isTag(n))}};function L0(t,e,r,n){if(r===null){if(t.length>n)throw new Error(`Pseudo-class :${e} requires an argument`)}else if(t.length===n)throw new Error(`Pseudo-class :${e} doesn't have any arguments`)}var Ih={"any-link":":is(a, area, link)[href]",link:":any-link:not(:visited)",disabled:`:is(
        :is(button, input, select, textarea, optgroup, option)[disabled],
        optgroup[disabled] > option,
        fieldset[disabled]:not(fieldset[disabled] legend:first-of-type *)
    )`,enabled:":not(:disabled)",checked:":is(:is(input[type=radio], input[type=checkbox])[checked], option:selected)",required:":is(input, select, textarea)[required]",optional:":is(input, select, textarea):not([required])",selected:"option:is([selected], select:not([multiple]):not(:has(> option[selected])) > :first-of-type)",checkbox:"[type=checkbox]",file:"[type=file]",password:"[type=password]",radio:"[type=radio]",reset:"[type=reset]",image:"[type=image]",submit:"[type=submit]",parent:":not(:empty)",header:":is(h1, h2, h3, h4, h5, h6)",button:":is(button, input[type=button])",input:":is(input, textarea, select, button)",text:"input:is(:not([type!='']), [type=text])"},Sl={};function Oh(t,e){return t===he.falseFunc?he.falseFunc:r=>e.isTag(r)&&t(r)}function Tl(t,e){let r=e.getSiblings(t);if(r.length<=1)return[];let n=r.indexOf(t);return n<0||n===r.length-1?[]:r.slice(n+1).filter(e.isTag)}function Si(t){return{xmlMode:!!t.xmlMode,lowerCaseAttributeNames:!!t.lowerCaseAttributeNames,lowerCaseTags:!!t.lowerCaseTags,quirksMode:!!t.quirksMode,cacheResults:!!t.cacheResults,pseudos:t.pseudos,adapter:t.adapter,equals:t.equals}}var mi=(t,e,r,n,u)=>{let a=u(e,Si(r),n);return a===he.trueFunc?t:a===he.falseFunc?he.falseFunc:i=>a(i)&&t(i)},bi={is:mi,matches:mi,where:mi,not(t,e,r,n,u){let a=u(e,Si(r),n);return a===he.falseFunc?t:a===he.trueFunc?he.falseFunc:i=>!a(i)&&t(i)},has(t,e,r,n,u){let{adapter:a}=r,i=Si(r);i.relativeSelector=!0;let s=e.some(c=>c.some($i))?[Sl]:void 0,l=u(e,i,s);if(l===he.falseFunc)return he.falseFunc;let o=Oh(l,a);if(s&&l!==he.trueFunc){let{shouldTestNextSiblings:c=!1}=l;return d=>{if(!t(d))return!1;s[0]=d;let f=a.getChildren(d),y=c?[...f,...Tl(d,a)]:f;return a.existsOne(o,y)}}return c=>t(c)&&a.existsOne(o,a.getChildren(c))}};function Rh(t,e,r,n,u){var a;let{name:i,data:s}=e;if(Array.isArray(s)){if(!(i in bi))throw new Error(`Unknown pseudo-class :${i}(${s})`);return bi[i](t,s,r,n,u)}let l=(a=r.pseudos)===null||a===void 0?void 0:a[i],o=typeof l=="string"?l:Ih[i];if(typeof o=="string"){if(s!=null)throw new Error(`Pseudo ${i} doesn't have any arguments`);let c=Gi(o);return bi.is(t,c,r,n,u)}if(typeof l=="function")return L0(l,i,s,1),c=>l(c,s)&&t(c);if(i in Ei)return Ei[i](t,s,r,n);if(i in k0){let c=k0[i];return L0(c,i,s,2),d=>c(d,r,s)&&t(d)}throw new Error(`Unknown pseudo-class :${i}`)}function gi(t,e){let r=e.getParent(t);return r&&e.isTag(r)?r:null}function qh(t,e,r,n,u){let{adapter:a,equals:i}=r;switch(e.type){case ce.PseudoElement:throw new Error("Pseudo-elements are not supported by css-select");case ce.ColumnCombinator:throw new Error("Column combinators are not yet supported by css-select");case ce.Attribute:{if(e.namespace!=null)throw new Error("Namespaced attributes are not yet supported by css-select");return(!r.xmlMode||r.lowerCaseAttributeNames)&&(e.name=e.name.toLowerCase()),Ch[e.action](t,e,r)}case ce.Pseudo:return Rh(t,e,r,n,u);case ce.Tag:{if(e.namespace!=null)throw new Error("Namespaced tag names are not yet supported by css-select");let{name:s}=e;return(!r.xmlMode||r.lowerCaseTags)&&(s=s.toLowerCase()),function(o){return a.getName(o)===s&&t(o)}}case ce.Descendant:{if(r.cacheResults===!1||typeof WeakSet>"u")return function(o){let c=o;for(;c=gi(c,a);)if(t(c))return!0;return!1};let s=new WeakSet;return function(o){let c=o;for(;c=gi(c,a);)if(!s.has(c)){if(a.isTag(c)&&t(c))return!0;s.add(c)}return!1}}case"_flexibleDescendant":return function(l){let o=l;do if(t(o))return!0;while(o=gi(o,a));return!1};case ce.Parent:return function(l){return a.getChildren(l).some(o=>a.isTag(o)&&t(o))};case ce.Child:return function(l){let o=a.getParent(l);return o!=null&&a.isTag(o)&&t(o)};case ce.Sibling:return function(l){let o=a.getSiblings(l);for(let c=0;c<o.length;c++){let d=o[c];if(i(l,d))break;if(a.isTag(d)&&t(d))return!0}return!1};case ce.Adjacent:return a.prevElementSibling?function(l){let o=a.prevElementSibling(l);return o!=null&&t(o)}:function(l){let o=a.getSiblings(l),c;for(let d=0;d<o.length;d++){let f=o[d];if(i(l,f))break;a.isTag(f)&&(c=f)}return!!c&&t(c)};case ce.Universal:{if(e.namespace!=null&&e.namespace!=="*")throw new Error("Namespaced universal selectors are not yet supported by css-select");return t}}}function Ph(t,e,r){let n=typeof t=="string"?Gi(t):t;return Al(n,e,r)}function Nl(t){return t.type===ce.Pseudo&&(t.name==="scope"||Array.isArray(t.data)&&t.data.some(e=>e.some(Nl)))}var Hh={type:ce.Descendant},Bh={type:"_flexibleDescendant"},Fh={type:ce.Pseudo,name:"scope",data:null};function Uh(t,{adapter:e},r){let n=!!r?.every(u=>{let a=e.isTag(u)&&e.getParent(u);return u===Sl||a&&e.isTag(a)});for(let u of t){if(!(u.length>0&&$i(u[0])&&u[0].type!==ce.Descendant))if(n&&!u.some(Nl))u.unshift(Hh);else continue;u.unshift(Fh)}}function Al(t,e,r){var n;t.forEach(Th),r=(n=e.context)!==null&&n!==void 0?n:r;let u=Array.isArray(r),a=r&&(Array.isArray(r)?r:[r]);if(e.relativeSelector!==!1)Uh(t,e,a);else if(t.some(l=>l.length>0&&$i(l[0])))throw new Error("Relative selectors are not allowed when the `relativeSelector` option is disabled");let i=!1,s=t.map(l=>{if(l.length>=2){let[o,c]=l;o.type!==ce.Pseudo||o.name!=="scope"||(u&&c.type===ce.Descendant?l[1]=Bh:(c.type===ce.Adjacent||c.type===ce.Sibling)&&(i=!0))}return Vh(l,e,a)}).reduce(jh,he.falseFunc);return s.shouldTestNextSiblings=i,s}function Vh(t,e,r){var n;return t.reduce((u,a)=>u===he.falseFunc?he.falseFunc:qh(u,a,e,r,Al),(n=e.rootFunc)!==null&&n!==void 0?n:he.trueFunc)}function jh(t,e){return e===he.falseFunc||t===he.trueFunc?t:t===he.falseFunc||e===he.trueFunc?e:function(n){return t(n)||e(n)}}var Cl=(t,e)=>t===e,zh={adapter:wl,equals:Cl};function Gh(t){var e,r,n,u;let a=t??zh;return(e=a.adapter)!==null&&e!==void 0||(a.adapter=wl),(r=a.equals)!==null&&r!==void 0||(a.equals=(u=(n=a.adapter)===null||n===void 0?void 0:n.equals)!==null&&u!==void 0?u:Cl),a}function kl(t){return function(r,n,u){let a=Gh(u);typeof r!="function"&&(r=Ph(r,a,n));let i=$h(n,a.adapter,r.shouldTestNextSiblings);return t(r,i,a)}}function $h(t,e,r=!1){return r&&(t=Wh(t,e)),Array.isArray(t)?e.removeSubsets(t):e.getChildren(t)}function Wh(t,e){let r=Array.isArray(t)?t.slice(0):[t],n=r.length;for(let u=0;u<n;u++){let a=Tl(r[u],e);r.push(...a)}return r}var Ll=kl((t,e,r)=>t===he.falseFunc||!e||e.length===0?[]:r.adapter.findAll(t,e)),Wi=kl((t,e,r)=>t===he.falseFunc||!e||e.length===0?null:r.adapter.findOne(t,e)),Xh=new Uint16Array('\u1D41<\xD5\u0131\u028A\u049D\u057B\u05D0\u0675\u06DE\u07A2\u07D6\u080F\u0A4A\u0A91\u0DA1\u0E6D\u0F09\u0F26\u10CA\u1228\u12E1\u1415\u149D\u14C3\u14DF\u1525\0\0\0\0\0\0\u156B\u16CD\u198D\u1C12\u1DDD\u1F7E\u2060\u21B0\u228D\u23C0\u23FB\u2442\u2824\u2912\u2D08\u2E48\u2FCE\u3016\u32BA\u3639\u37AC\u38FE\u3A28\u3A71\u3AE0\u3B2E\u0800EMabcfglmnoprstu\\bfms\x7F\x84\x8B\x90\x95\x98\xA6\xB3\xB9\xC8\xCFlig\u803B\xC6\u40C6P\u803B&\u4026cute\u803B\xC1\u40C1reve;\u4102\u0100iyx}rc\u803B\xC2\u40C2;\u4410r;\uC000\u{1D504}rave\u803B\xC0\u40C0pha;\u4391acr;\u4100d;\u6A53\u0100gp\x9D\xA1on;\u4104f;\uC000\u{1D538}plyFunction;\u6061ing\u803B\xC5\u40C5\u0100cs\xBE\xC3r;\uC000\u{1D49C}ign;\u6254ilde\u803B\xC3\u40C3ml\u803B\xC4\u40C4\u0400aceforsu\xE5\xFB\xFE\u0117\u011C\u0122\u0127\u012A\u0100cr\xEA\xF2kslash;\u6216\u0176\xF6\xF8;\u6AE7ed;\u6306y;\u4411\u0180crt\u0105\u010B\u0114ause;\u6235noullis;\u612Ca;\u4392r;\uC000\u{1D505}pf;\uC000\u{1D539}eve;\u42D8c\xF2\u0113mpeq;\u624E\u0700HOacdefhilorsu\u014D\u0151\u0156\u0180\u019E\u01A2\u01B5\u01B7\u01BA\u01DC\u0215\u0273\u0278\u027Ecy;\u4427PY\u803B\xA9\u40A9\u0180cpy\u015D\u0162\u017Aute;\u4106\u0100;i\u0167\u0168\u62D2talDifferentialD;\u6145leys;\u612D\u0200aeio\u0189\u018E\u0194\u0198ron;\u410Cdil\u803B\xC7\u40C7rc;\u4108nint;\u6230ot;\u410A\u0100dn\u01A7\u01ADilla;\u40B8terDot;\u40B7\xF2\u017Fi;\u43A7rcle\u0200DMPT\u01C7\u01CB\u01D1\u01D6ot;\u6299inus;\u6296lus;\u6295imes;\u6297o\u0100cs\u01E2\u01F8kwiseContourIntegral;\u6232eCurly\u0100DQ\u0203\u020FoubleQuote;\u601Duote;\u6019\u0200lnpu\u021E\u0228\u0247\u0255on\u0100;e\u0225\u0226\u6237;\u6A74\u0180git\u022F\u0236\u023Aruent;\u6261nt;\u622FourIntegral;\u622E\u0100fr\u024C\u024E;\u6102oduct;\u6210nterClockwiseContourIntegral;\u6233oss;\u6A2Fcr;\uC000\u{1D49E}p\u0100;C\u0284\u0285\u62D3ap;\u624D\u0580DJSZacefios\u02A0\u02AC\u02B0\u02B4\u02B8\u02CB\u02D7\u02E1\u02E6\u0333\u048D\u0100;o\u0179\u02A5trahd;\u6911cy;\u4402cy;\u4405cy;\u440F\u0180grs\u02BF\u02C4\u02C7ger;\u6021r;\u61A1hv;\u6AE4\u0100ay\u02D0\u02D5ron;\u410E;\u4414l\u0100;t\u02DD\u02DE\u6207a;\u4394r;\uC000\u{1D507}\u0100af\u02EB\u0327\u0100cm\u02F0\u0322ritical\u0200ADGT\u0300\u0306\u0316\u031Ccute;\u40B4o\u0174\u030B\u030D;\u42D9bleAcute;\u42DDrave;\u4060ilde;\u42DCond;\u62C4ferentialD;\u6146\u0470\u033D\0\0\0\u0342\u0354\0\u0405f;\uC000\u{1D53B}\u0180;DE\u0348\u0349\u034D\u40A8ot;\u60DCqual;\u6250ble\u0300CDLRUV\u0363\u0372\u0382\u03CF\u03E2\u03F8ontourIntegra\xEC\u0239o\u0274\u0379\0\0\u037B\xBB\u0349nArrow;\u61D3\u0100eo\u0387\u03A4ft\u0180ART\u0390\u0396\u03A1rrow;\u61D0ightArrow;\u61D4e\xE5\u02CAng\u0100LR\u03AB\u03C4eft\u0100AR\u03B3\u03B9rrow;\u67F8ightArrow;\u67FAightArrow;\u67F9ight\u0100AT\u03D8\u03DErrow;\u61D2ee;\u62A8p\u0241\u03E9\0\0\u03EFrrow;\u61D1ownArrow;\u61D5erticalBar;\u6225n\u0300ABLRTa\u0412\u042A\u0430\u045E\u047F\u037Crrow\u0180;BU\u041D\u041E\u0422\u6193ar;\u6913pArrow;\u61F5reve;\u4311eft\u02D2\u043A\0\u0446\0\u0450ightVector;\u6950eeVector;\u695Eector\u0100;B\u0459\u045A\u61BDar;\u6956ight\u01D4\u0467\0\u0471eeVector;\u695Fector\u0100;B\u047A\u047B\u61C1ar;\u6957ee\u0100;A\u0486\u0487\u62A4rrow;\u61A7\u0100ct\u0492\u0497r;\uC000\u{1D49F}rok;\u4110\u0800NTacdfglmopqstux\u04BD\u04C0\u04C4\u04CB\u04DE\u04E2\u04E7\u04EE\u04F5\u0521\u052F\u0536\u0552\u055D\u0560\u0565G;\u414AH\u803B\xD0\u40D0cute\u803B\xC9\u40C9\u0180aiy\u04D2\u04D7\u04DCron;\u411Arc\u803B\xCA\u40CA;\u442Dot;\u4116r;\uC000\u{1D508}rave\u803B\xC8\u40C8ement;\u6208\u0100ap\u04FA\u04FEcr;\u4112ty\u0253\u0506\0\0\u0512mallSquare;\u65FBerySmallSquare;\u65AB\u0100gp\u0526\u052Aon;\u4118f;\uC000\u{1D53C}silon;\u4395u\u0100ai\u053C\u0549l\u0100;T\u0542\u0543\u6A75ilde;\u6242librium;\u61CC\u0100ci\u0557\u055Ar;\u6130m;\u6A73a;\u4397ml\u803B\xCB\u40CB\u0100ip\u056A\u056Fsts;\u6203onentialE;\u6147\u0280cfios\u0585\u0588\u058D\u05B2\u05CCy;\u4424r;\uC000\u{1D509}lled\u0253\u0597\0\0\u05A3mallSquare;\u65FCerySmallSquare;\u65AA\u0370\u05BA\0\u05BF\0\0\u05C4f;\uC000\u{1D53D}All;\u6200riertrf;\u6131c\xF2\u05CB\u0600JTabcdfgorst\u05E8\u05EC\u05EF\u05FA\u0600\u0612\u0616\u061B\u061D\u0623\u066C\u0672cy;\u4403\u803B>\u403Emma\u0100;d\u05F7\u05F8\u4393;\u43DCreve;\u411E\u0180eiy\u0607\u060C\u0610dil;\u4122rc;\u411C;\u4413ot;\u4120r;\uC000\u{1D50A};\u62D9pf;\uC000\u{1D53E}eater\u0300EFGLST\u0635\u0644\u064E\u0656\u065B\u0666qual\u0100;L\u063E\u063F\u6265ess;\u62DBullEqual;\u6267reater;\u6AA2ess;\u6277lantEqual;\u6A7Eilde;\u6273cr;\uC000\u{1D4A2};\u626B\u0400Aacfiosu\u0685\u068B\u0696\u069B\u069E\u06AA\u06BE\u06CARDcy;\u442A\u0100ct\u0690\u0694ek;\u42C7;\u405Eirc;\u4124r;\u610ClbertSpace;\u610B\u01F0\u06AF\0\u06B2f;\u610DizontalLine;\u6500\u0100ct\u06C3\u06C5\xF2\u06A9rok;\u4126mp\u0144\u06D0\u06D8ownHum\xF0\u012Fqual;\u624F\u0700EJOacdfgmnostu\u06FA\u06FE\u0703\u0707\u070E\u071A\u071E\u0721\u0728\u0744\u0778\u078B\u078F\u0795cy;\u4415lig;\u4132cy;\u4401cute\u803B\xCD\u40CD\u0100iy\u0713\u0718rc\u803B\xCE\u40CE;\u4418ot;\u4130r;\u6111rave\u803B\xCC\u40CC\u0180;ap\u0720\u072F\u073F\u0100cg\u0734\u0737r;\u412AinaryI;\u6148lie\xF3\u03DD\u01F4\u0749\0\u0762\u0100;e\u074D\u074E\u622C\u0100gr\u0753\u0758ral;\u622Bsection;\u62C2isible\u0100CT\u076C\u0772omma;\u6063imes;\u6062\u0180gpt\u077F\u0783\u0788on;\u412Ef;\uC000\u{1D540}a;\u4399cr;\u6110ilde;\u4128\u01EB\u079A\0\u079Ecy;\u4406l\u803B\xCF\u40CF\u0280cfosu\u07AC\u07B7\u07BC\u07C2\u07D0\u0100iy\u07B1\u07B5rc;\u4134;\u4419r;\uC000\u{1D50D}pf;\uC000\u{1D541}\u01E3\u07C7\0\u07CCr;\uC000\u{1D4A5}rcy;\u4408kcy;\u4404\u0380HJacfos\u07E4\u07E8\u07EC\u07F1\u07FD\u0802\u0808cy;\u4425cy;\u440Cppa;\u439A\u0100ey\u07F6\u07FBdil;\u4136;\u441Ar;\uC000\u{1D50E}pf;\uC000\u{1D542}cr;\uC000\u{1D4A6}\u0580JTaceflmost\u0825\u0829\u082C\u0850\u0863\u09B3\u09B8\u09C7\u09CD\u0A37\u0A47cy;\u4409\u803B<\u403C\u0280cmnpr\u0837\u083C\u0841\u0844\u084Dute;\u4139bda;\u439Bg;\u67EAlacetrf;\u6112r;\u619E\u0180aey\u0857\u085C\u0861ron;\u413Ddil;\u413B;\u441B\u0100fs\u0868\u0970t\u0500ACDFRTUVar\u087E\u08A9\u08B1\u08E0\u08E6\u08FC\u092F\u095B\u0390\u096A\u0100nr\u0883\u088FgleBracket;\u67E8row\u0180;BR\u0899\u089A\u089E\u6190ar;\u61E4ightArrow;\u61C6eiling;\u6308o\u01F5\u08B7\0\u08C3bleBracket;\u67E6n\u01D4\u08C8\0\u08D2eeVector;\u6961ector\u0100;B\u08DB\u08DC\u61C3ar;\u6959loor;\u630Aight\u0100AV\u08EF\u08F5rrow;\u6194ector;\u694E\u0100er\u0901\u0917e\u0180;AV\u0909\u090A\u0910\u62A3rrow;\u61A4ector;\u695Aiangle\u0180;BE\u0924\u0925\u0929\u62B2ar;\u69CFqual;\u62B4p\u0180DTV\u0937\u0942\u094CownVector;\u6951eeVector;\u6960ector\u0100;B\u0956\u0957\u61BFar;\u6958ector\u0100;B\u0965\u0966\u61BCar;\u6952ight\xE1\u039Cs\u0300EFGLST\u097E\u098B\u0995\u099D\u09A2\u09ADqualGreater;\u62DAullEqual;\u6266reater;\u6276ess;\u6AA1lantEqual;\u6A7Dilde;\u6272r;\uC000\u{1D50F}\u0100;e\u09BD\u09BE\u62D8ftarrow;\u61DAidot;\u413F\u0180npw\u09D4\u0A16\u0A1Bg\u0200LRlr\u09DE\u09F7\u0A02\u0A10eft\u0100AR\u09E6\u09ECrrow;\u67F5ightArrow;\u67F7ightArrow;\u67F6eft\u0100ar\u03B3\u0A0Aight\xE1\u03BFight\xE1\u03CAf;\uC000\u{1D543}er\u0100LR\u0A22\u0A2CeftArrow;\u6199ightArrow;\u6198\u0180cht\u0A3E\u0A40\u0A42\xF2\u084C;\u61B0rok;\u4141;\u626A\u0400acefiosu\u0A5A\u0A5D\u0A60\u0A77\u0A7C\u0A85\u0A8B\u0A8Ep;\u6905y;\u441C\u0100dl\u0A65\u0A6FiumSpace;\u605Flintrf;\u6133r;\uC000\u{1D510}nusPlus;\u6213pf;\uC000\u{1D544}c\xF2\u0A76;\u439C\u0480Jacefostu\u0AA3\u0AA7\u0AAD\u0AC0\u0B14\u0B19\u0D91\u0D97\u0D9Ecy;\u440Acute;\u4143\u0180aey\u0AB4\u0AB9\u0ABEron;\u4147dil;\u4145;\u441D\u0180gsw\u0AC7\u0AF0\u0B0Eative\u0180MTV\u0AD3\u0ADF\u0AE8ediumSpace;\u600Bhi\u0100cn\u0AE6\u0AD8\xEB\u0AD9eryThi\xEE\u0AD9ted\u0100GL\u0AF8\u0B06reaterGreate\xF2\u0673essLes\xF3\u0A48Line;\u400Ar;\uC000\u{1D511}\u0200Bnpt\u0B22\u0B28\u0B37\u0B3Areak;\u6060BreakingSpace;\u40A0f;\u6115\u0680;CDEGHLNPRSTV\u0B55\u0B56\u0B6A\u0B7C\u0BA1\u0BEB\u0C04\u0C5E\u0C84\u0CA6\u0CD8\u0D61\u0D85\u6AEC\u0100ou\u0B5B\u0B64ngruent;\u6262pCap;\u626DoubleVerticalBar;\u6226\u0180lqx\u0B83\u0B8A\u0B9Bement;\u6209ual\u0100;T\u0B92\u0B93\u6260ilde;\uC000\u2242\u0338ists;\u6204reater\u0380;EFGLST\u0BB6\u0BB7\u0BBD\u0BC9\u0BD3\u0BD8\u0BE5\u626Fqual;\u6271ullEqual;\uC000\u2267\u0338reater;\uC000\u226B\u0338ess;\u6279lantEqual;\uC000\u2A7E\u0338ilde;\u6275ump\u0144\u0BF2\u0BFDownHump;\uC000\u224E\u0338qual;\uC000\u224F\u0338e\u0100fs\u0C0A\u0C27tTriangle\u0180;BE\u0C1A\u0C1B\u0C21\u62EAar;\uC000\u29CF\u0338qual;\u62ECs\u0300;EGLST\u0C35\u0C36\u0C3C\u0C44\u0C4B\u0C58\u626Equal;\u6270reater;\u6278ess;\uC000\u226A\u0338lantEqual;\uC000\u2A7D\u0338ilde;\u6274ested\u0100GL\u0C68\u0C79reaterGreater;\uC000\u2AA2\u0338essLess;\uC000\u2AA1\u0338recedes\u0180;ES\u0C92\u0C93\u0C9B\u6280qual;\uC000\u2AAF\u0338lantEqual;\u62E0\u0100ei\u0CAB\u0CB9verseElement;\u620CghtTriangle\u0180;BE\u0CCB\u0CCC\u0CD2\u62EBar;\uC000\u29D0\u0338qual;\u62ED\u0100qu\u0CDD\u0D0CuareSu\u0100bp\u0CE8\u0CF9set\u0100;E\u0CF0\u0CF3\uC000\u228F\u0338qual;\u62E2erset\u0100;E\u0D03\u0D06\uC000\u2290\u0338qual;\u62E3\u0180bcp\u0D13\u0D24\u0D4Eset\u0100;E\u0D1B\u0D1E\uC000\u2282\u20D2qual;\u6288ceeds\u0200;EST\u0D32\u0D33\u0D3B\u0D46\u6281qual;\uC000\u2AB0\u0338lantEqual;\u62E1ilde;\uC000\u227F\u0338erset\u0100;E\u0D58\u0D5B\uC000\u2283\u20D2qual;\u6289ilde\u0200;EFT\u0D6E\u0D6F\u0D75\u0D7F\u6241qual;\u6244ullEqual;\u6247ilde;\u6249erticalBar;\u6224cr;\uC000\u{1D4A9}ilde\u803B\xD1\u40D1;\u439D\u0700Eacdfgmoprstuv\u0DBD\u0DC2\u0DC9\u0DD5\u0DDB\u0DE0\u0DE7\u0DFC\u0E02\u0E20\u0E22\u0E32\u0E3F\u0E44lig;\u4152cute\u803B\xD3\u40D3\u0100iy\u0DCE\u0DD3rc\u803B\xD4\u40D4;\u441Eblac;\u4150r;\uC000\u{1D512}rave\u803B\xD2\u40D2\u0180aei\u0DEE\u0DF2\u0DF6cr;\u414Cga;\u43A9cron;\u439Fpf;\uC000\u{1D546}enCurly\u0100DQ\u0E0E\u0E1AoubleQuote;\u601Cuote;\u6018;\u6A54\u0100cl\u0E27\u0E2Cr;\uC000\u{1D4AA}ash\u803B\xD8\u40D8i\u016C\u0E37\u0E3Cde\u803B\xD5\u40D5es;\u6A37ml\u803B\xD6\u40D6er\u0100BP\u0E4B\u0E60\u0100ar\u0E50\u0E53r;\u603Eac\u0100ek\u0E5A\u0E5C;\u63DEet;\u63B4arenthesis;\u63DC\u0480acfhilors\u0E7F\u0E87\u0E8A\u0E8F\u0E92\u0E94\u0E9D\u0EB0\u0EFCrtialD;\u6202y;\u441Fr;\uC000\u{1D513}i;\u43A6;\u43A0usMinus;\u40B1\u0100ip\u0EA2\u0EADncareplan\xE5\u069Df;\u6119\u0200;eio\u0EB9\u0EBA\u0EE0\u0EE4\u6ABBcedes\u0200;EST\u0EC8\u0EC9\u0ECF\u0EDA\u627Aqual;\u6AAFlantEqual;\u627Cilde;\u627Eme;\u6033\u0100dp\u0EE9\u0EEEuct;\u620Fortion\u0100;a\u0225\u0EF9l;\u621D\u0100ci\u0F01\u0F06r;\uC000\u{1D4AB};\u43A8\u0200Ufos\u0F11\u0F16\u0F1B\u0F1FOT\u803B"\u4022r;\uC000\u{1D514}pf;\u611Acr;\uC000\u{1D4AC}\u0600BEacefhiorsu\u0F3E\u0F43\u0F47\u0F60\u0F73\u0FA7\u0FAA\u0FAD\u1096\u10A9\u10B4\u10BEarr;\u6910G\u803B\xAE\u40AE\u0180cnr\u0F4E\u0F53\u0F56ute;\u4154g;\u67EBr\u0100;t\u0F5C\u0F5D\u61A0l;\u6916\u0180aey\u0F67\u0F6C\u0F71ron;\u4158dil;\u4156;\u4420\u0100;v\u0F78\u0F79\u611Cerse\u0100EU\u0F82\u0F99\u0100lq\u0F87\u0F8Eement;\u620Builibrium;\u61CBpEquilibrium;\u696Fr\xBB\u0F79o;\u43A1ght\u0400ACDFTUVa\u0FC1\u0FEB\u0FF3\u1022\u1028\u105B\u1087\u03D8\u0100nr\u0FC6\u0FD2gleBracket;\u67E9row\u0180;BL\u0FDC\u0FDD\u0FE1\u6192ar;\u61E5eftArrow;\u61C4eiling;\u6309o\u01F5\u0FF9\0\u1005bleBracket;\u67E7n\u01D4\u100A\0\u1014eeVector;\u695Dector\u0100;B\u101D\u101E\u61C2ar;\u6955loor;\u630B\u0100er\u102D\u1043e\u0180;AV\u1035\u1036\u103C\u62A2rrow;\u61A6ector;\u695Biangle\u0180;BE\u1050\u1051\u1055\u62B3ar;\u69D0qual;\u62B5p\u0180DTV\u1063\u106E\u1078ownVector;\u694FeeVector;\u695Cector\u0100;B\u1082\u1083\u61BEar;\u6954ector\u0100;B\u1091\u1092\u61C0ar;\u6953\u0100pu\u109B\u109Ef;\u611DndImplies;\u6970ightarrow;\u61DB\u0100ch\u10B9\u10BCr;\u611B;\u61B1leDelayed;\u69F4\u0680HOacfhimoqstu\u10E4\u10F1\u10F7\u10FD\u1119\u111E\u1151\u1156\u1161\u1167\u11B5\u11BB\u11BF\u0100Cc\u10E9\u10EEHcy;\u4429y;\u4428FTcy;\u442Ccute;\u415A\u0280;aeiy\u1108\u1109\u110E\u1113\u1117\u6ABCron;\u4160dil;\u415Erc;\u415C;\u4421r;\uC000\u{1D516}ort\u0200DLRU\u112A\u1134\u113E\u1149ownArrow\xBB\u041EeftArrow\xBB\u089AightArrow\xBB\u0FDDpArrow;\u6191gma;\u43A3allCircle;\u6218pf;\uC000\u{1D54A}\u0272\u116D\0\0\u1170t;\u621Aare\u0200;ISU\u117B\u117C\u1189\u11AF\u65A1ntersection;\u6293u\u0100bp\u118F\u119Eset\u0100;E\u1197\u1198\u628Fqual;\u6291erset\u0100;E\u11A8\u11A9\u6290qual;\u6292nion;\u6294cr;\uC000\u{1D4AE}ar;\u62C6\u0200bcmp\u11C8\u11DB\u1209\u120B\u0100;s\u11CD\u11CE\u62D0et\u0100;E\u11CD\u11D5qual;\u6286\u0100ch\u11E0\u1205eeds\u0200;EST\u11ED\u11EE\u11F4\u11FF\u627Bqual;\u6AB0lantEqual;\u627Dilde;\u627FTh\xE1\u0F8C;\u6211\u0180;es\u1212\u1213\u1223\u62D1rset\u0100;E\u121C\u121D\u6283qual;\u6287et\xBB\u1213\u0580HRSacfhiors\u123E\u1244\u1249\u1255\u125E\u1271\u1276\u129F\u12C2\u12C8\u12D1ORN\u803B\xDE\u40DEADE;\u6122\u0100Hc\u124E\u1252cy;\u440By;\u4426\u0100bu\u125A\u125C;\u4009;\u43A4\u0180aey\u1265\u126A\u126Fron;\u4164dil;\u4162;\u4422r;\uC000\u{1D517}\u0100ei\u127B\u1289\u01F2\u1280\0\u1287efore;\u6234a;\u4398\u0100cn\u128E\u1298kSpace;\uC000\u205F\u200ASpace;\u6009lde\u0200;EFT\u12AB\u12AC\u12B2\u12BC\u623Cqual;\u6243ullEqual;\u6245ilde;\u6248pf;\uC000\u{1D54B}ipleDot;\u60DB\u0100ct\u12D6\u12DBr;\uC000\u{1D4AF}rok;\u4166\u0AE1\u12F7\u130E\u131A\u1326\0\u132C\u1331\0\0\0\0\0\u1338\u133D\u1377\u1385\0\u13FF\u1404\u140A\u1410\u0100cr\u12FB\u1301ute\u803B\xDA\u40DAr\u0100;o\u1307\u1308\u619Fcir;\u6949r\u01E3\u1313\0\u1316y;\u440Eve;\u416C\u0100iy\u131E\u1323rc\u803B\xDB\u40DB;\u4423blac;\u4170r;\uC000\u{1D518}rave\u803B\xD9\u40D9acr;\u416A\u0100di\u1341\u1369er\u0100BP\u1348\u135D\u0100ar\u134D\u1350r;\u405Fac\u0100ek\u1357\u1359;\u63DFet;\u63B5arenthesis;\u63DDon\u0100;P\u1370\u1371\u62C3lus;\u628E\u0100gp\u137B\u137Fon;\u4172f;\uC000\u{1D54C}\u0400ADETadps\u1395\u13AE\u13B8\u13C4\u03E8\u13D2\u13D7\u13F3rrow\u0180;BD\u1150\u13A0\u13A4ar;\u6912ownArrow;\u61C5ownArrow;\u6195quilibrium;\u696Eee\u0100;A\u13CB\u13CC\u62A5rrow;\u61A5own\xE1\u03F3er\u0100LR\u13DE\u13E8eftArrow;\u6196ightArrow;\u6197i\u0100;l\u13F9\u13FA\u43D2on;\u43A5ing;\u416Ecr;\uC000\u{1D4B0}ilde;\u4168ml\u803B\xDC\u40DC\u0480Dbcdefosv\u1427\u142C\u1430\u1433\u143E\u1485\u148A\u1490\u1496ash;\u62ABar;\u6AEBy;\u4412ash\u0100;l\u143B\u143C\u62A9;\u6AE6\u0100er\u1443\u1445;\u62C1\u0180bty\u144C\u1450\u147Aar;\u6016\u0100;i\u144F\u1455cal\u0200BLST\u1461\u1465\u146A\u1474ar;\u6223ine;\u407Ceparator;\u6758ilde;\u6240ThinSpace;\u600Ar;\uC000\u{1D519}pf;\uC000\u{1D54D}cr;\uC000\u{1D4B1}dash;\u62AA\u0280cefos\u14A7\u14AC\u14B1\u14B6\u14BCirc;\u4174dge;\u62C0r;\uC000\u{1D51A}pf;\uC000\u{1D54E}cr;\uC000\u{1D4B2}\u0200fios\u14CB\u14D0\u14D2\u14D8r;\uC000\u{1D51B};\u439Epf;\uC000\u{1D54F}cr;\uC000\u{1D4B3}\u0480AIUacfosu\u14F1\u14F5\u14F9\u14FD\u1504\u150F\u1514\u151A\u1520cy;\u442Fcy;\u4407cy;\u442Ecute\u803B\xDD\u40DD\u0100iy\u1509\u150Drc;\u4176;\u442Br;\uC000\u{1D51C}pf;\uC000\u{1D550}cr;\uC000\u{1D4B4}ml;\u4178\u0400Hacdefos\u1535\u1539\u153F\u154B\u154F\u155D\u1560\u1564cy;\u4416cute;\u4179\u0100ay\u1544\u1549ron;\u417D;\u4417ot;\u417B\u01F2\u1554\0\u155BoWidt\xE8\u0AD9a;\u4396r;\u6128pf;\u6124cr;\uC000\u{1D4B5}\u0BE1\u1583\u158A\u1590\0\u15B0\u15B6\u15BF\0\0\0\0\u15C6\u15DB\u15EB\u165F\u166D\0\u1695\u169B\u16B2\u16B9\0\u16BEcute\u803B\xE1\u40E1reve;\u4103\u0300;Ediuy\u159C\u159D\u15A1\u15A3\u15A8\u15AD\u623E;\uC000\u223E\u0333;\u623Frc\u803B\xE2\u40E2te\u80BB\xB4\u0306;\u4430lig\u803B\xE6\u40E6\u0100;r\xB2\u15BA;\uC000\u{1D51E}rave\u803B\xE0\u40E0\u0100ep\u15CA\u15D6\u0100fp\u15CF\u15D4sym;\u6135\xE8\u15D3ha;\u43B1\u0100ap\u15DFc\u0100cl\u15E4\u15E7r;\u4101g;\u6A3F\u0264\u15F0\0\0\u160A\u0280;adsv\u15FA\u15FB\u15FF\u1601\u1607\u6227nd;\u6A55;\u6A5Clope;\u6A58;\u6A5A\u0380;elmrsz\u1618\u1619\u161B\u161E\u163F\u164F\u1659\u6220;\u69A4e\xBB\u1619sd\u0100;a\u1625\u1626\u6221\u0461\u1630\u1632\u1634\u1636\u1638\u163A\u163C\u163E;\u69A8;\u69A9;\u69AA;\u69AB;\u69AC;\u69AD;\u69AE;\u69AFt\u0100;v\u1645\u1646\u621Fb\u0100;d\u164C\u164D\u62BE;\u699D\u0100pt\u1654\u1657h;\u6222\xBB\xB9arr;\u637C\u0100gp\u1663\u1667on;\u4105f;\uC000\u{1D552}\u0380;Eaeiop\u12C1\u167B\u167D\u1682\u1684\u1687\u168A;\u6A70cir;\u6A6F;\u624Ad;\u624Bs;\u4027rox\u0100;e\u12C1\u1692\xF1\u1683ing\u803B\xE5\u40E5\u0180cty\u16A1\u16A6\u16A8r;\uC000\u{1D4B6};\u402Amp\u0100;e\u12C1\u16AF\xF1\u0288ilde\u803B\xE3\u40E3ml\u803B\xE4\u40E4\u0100ci\u16C2\u16C8onin\xF4\u0272nt;\u6A11\u0800Nabcdefiklnoprsu\u16ED\u16F1\u1730\u173C\u1743\u1748\u1778\u177D\u17E0\u17E6\u1839\u1850\u170D\u193D\u1948\u1970ot;\u6AED\u0100cr\u16F6\u171Ek\u0200ceps\u1700\u1705\u170D\u1713ong;\u624Cpsilon;\u43F6rime;\u6035im\u0100;e\u171A\u171B\u623Dq;\u62CD\u0176\u1722\u1726ee;\u62BDed\u0100;g\u172C\u172D\u6305e\xBB\u172Drk\u0100;t\u135C\u1737brk;\u63B6\u0100oy\u1701\u1741;\u4431quo;\u601E\u0280cmprt\u1753\u175B\u1761\u1764\u1768aus\u0100;e\u010A\u0109ptyv;\u69B0s\xE9\u170Cno\xF5\u0113\u0180ahw\u176F\u1771\u1773;\u43B2;\u6136een;\u626Cr;\uC000\u{1D51F}g\u0380costuvw\u178D\u179D\u17B3\u17C1\u17D5\u17DB\u17DE\u0180aiu\u1794\u1796\u179A\xF0\u0760rc;\u65EFp\xBB\u1371\u0180dpt\u17A4\u17A8\u17ADot;\u6A00lus;\u6A01imes;\u6A02\u0271\u17B9\0\0\u17BEcup;\u6A06ar;\u6605riangle\u0100du\u17CD\u17D2own;\u65BDp;\u65B3plus;\u6A04e\xE5\u1444\xE5\u14ADarow;\u690D\u0180ako\u17ED\u1826\u1835\u0100cn\u17F2\u1823k\u0180lst\u17FA\u05AB\u1802ozenge;\u69EBriangle\u0200;dlr\u1812\u1813\u1818\u181D\u65B4own;\u65BEeft;\u65C2ight;\u65B8k;\u6423\u01B1\u182B\0\u1833\u01B2\u182F\0\u1831;\u6592;\u65914;\u6593ck;\u6588\u0100eo\u183E\u184D\u0100;q\u1843\u1846\uC000=\u20E5uiv;\uC000\u2261\u20E5t;\u6310\u0200ptwx\u1859\u185E\u1867\u186Cf;\uC000\u{1D553}\u0100;t\u13CB\u1863om\xBB\u13CCtie;\u62C8\u0600DHUVbdhmptuv\u1885\u1896\u18AA\u18BB\u18D7\u18DB\u18EC\u18FF\u1905\u190A\u1910\u1921\u0200LRlr\u188E\u1890\u1892\u1894;\u6557;\u6554;\u6556;\u6553\u0280;DUdu\u18A1\u18A2\u18A4\u18A6\u18A8\u6550;\u6566;\u6569;\u6564;\u6567\u0200LRlr\u18B3\u18B5\u18B7\u18B9;\u655D;\u655A;\u655C;\u6559\u0380;HLRhlr\u18CA\u18CB\u18CD\u18CF\u18D1\u18D3\u18D5\u6551;\u656C;\u6563;\u6560;\u656B;\u6562;\u655Fox;\u69C9\u0200LRlr\u18E4\u18E6\u18E8\u18EA;\u6555;\u6552;\u6510;\u650C\u0280;DUdu\u06BD\u18F7\u18F9\u18FB\u18FD;\u6565;\u6568;\u652C;\u6534inus;\u629Flus;\u629Eimes;\u62A0\u0200LRlr\u1919\u191B\u191D\u191F;\u655B;\u6558;\u6518;\u6514\u0380;HLRhlr\u1930\u1931\u1933\u1935\u1937\u1939\u193B\u6502;\u656A;\u6561;\u655E;\u653C;\u6524;\u651C\u0100ev\u0123\u1942bar\u803B\xA6\u40A6\u0200ceio\u1951\u1956\u195A\u1960r;\uC000\u{1D4B7}mi;\u604Fm\u0100;e\u171A\u171Cl\u0180;bh\u1968\u1969\u196B\u405C;\u69C5sub;\u67C8\u016C\u1974\u197El\u0100;e\u1979\u197A\u6022t\xBB\u197Ap\u0180;Ee\u012F\u1985\u1987;\u6AAE\u0100;q\u06DC\u06DB\u0CE1\u19A7\0\u19E8\u1A11\u1A15\u1A32\0\u1A37\u1A50\0\0\u1AB4\0\0\u1AC1\0\0\u1B21\u1B2E\u1B4D\u1B52\0\u1BFD\0\u1C0C\u0180cpr\u19AD\u19B2\u19DDute;\u4107\u0300;abcds\u19BF\u19C0\u19C4\u19CA\u19D5\u19D9\u6229nd;\u6A44rcup;\u6A49\u0100au\u19CF\u19D2p;\u6A4Bp;\u6A47ot;\u6A40;\uC000\u2229\uFE00\u0100eo\u19E2\u19E5t;\u6041\xEE\u0693\u0200aeiu\u19F0\u19FB\u1A01\u1A05\u01F0\u19F5\0\u19F8s;\u6A4Don;\u410Ddil\u803B\xE7\u40E7rc;\u4109ps\u0100;s\u1A0C\u1A0D\u6A4Cm;\u6A50ot;\u410B\u0180dmn\u1A1B\u1A20\u1A26il\u80BB\xB8\u01ADptyv;\u69B2t\u8100\xA2;e\u1A2D\u1A2E\u40A2r\xE4\u01B2r;\uC000\u{1D520}\u0180cei\u1A3D\u1A40\u1A4Dy;\u4447ck\u0100;m\u1A47\u1A48\u6713ark\xBB\u1A48;\u43C7r\u0380;Ecefms\u1A5F\u1A60\u1A62\u1A6B\u1AA4\u1AAA\u1AAE\u65CB;\u69C3\u0180;el\u1A69\u1A6A\u1A6D\u42C6q;\u6257e\u0261\u1A74\0\0\u1A88rrow\u0100lr\u1A7C\u1A81eft;\u61BAight;\u61BB\u0280RSacd\u1A92\u1A94\u1A96\u1A9A\u1A9F\xBB\u0F47;\u64C8st;\u629Birc;\u629Aash;\u629Dnint;\u6A10id;\u6AEFcir;\u69C2ubs\u0100;u\u1ABB\u1ABC\u6663it\xBB\u1ABC\u02EC\u1AC7\u1AD4\u1AFA\0\u1B0Aon\u0100;e\u1ACD\u1ACE\u403A\u0100;q\xC7\xC6\u026D\u1AD9\0\0\u1AE2a\u0100;t\u1ADE\u1ADF\u402C;\u4040\u0180;fl\u1AE8\u1AE9\u1AEB\u6201\xEE\u1160e\u0100mx\u1AF1\u1AF6ent\xBB\u1AE9e\xF3\u024D\u01E7\u1AFE\0\u1B07\u0100;d\u12BB\u1B02ot;\u6A6Dn\xF4\u0246\u0180fry\u1B10\u1B14\u1B17;\uC000\u{1D554}o\xE4\u0254\u8100\xA9;s\u0155\u1B1Dr;\u6117\u0100ao\u1B25\u1B29rr;\u61B5ss;\u6717\u0100cu\u1B32\u1B37r;\uC000\u{1D4B8}\u0100bp\u1B3C\u1B44\u0100;e\u1B41\u1B42\u6ACF;\u6AD1\u0100;e\u1B49\u1B4A\u6AD0;\u6AD2dot;\u62EF\u0380delprvw\u1B60\u1B6C\u1B77\u1B82\u1BAC\u1BD4\u1BF9arr\u0100lr\u1B68\u1B6A;\u6938;\u6935\u0270\u1B72\0\0\u1B75r;\u62DEc;\u62DFarr\u0100;p\u1B7F\u1B80\u61B6;\u693D\u0300;bcdos\u1B8F\u1B90\u1B96\u1BA1\u1BA5\u1BA8\u622Arcap;\u6A48\u0100au\u1B9B\u1B9Ep;\u6A46p;\u6A4Aot;\u628Dr;\u6A45;\uC000\u222A\uFE00\u0200alrv\u1BB5\u1BBF\u1BDE\u1BE3rr\u0100;m\u1BBC\u1BBD\u61B7;\u693Cy\u0180evw\u1BC7\u1BD4\u1BD8q\u0270\u1BCE\0\0\u1BD2re\xE3\u1B73u\xE3\u1B75ee;\u62CEedge;\u62CFen\u803B\xA4\u40A4earrow\u0100lr\u1BEE\u1BF3eft\xBB\u1B80ight\xBB\u1BBDe\xE4\u1BDD\u0100ci\u1C01\u1C07onin\xF4\u01F7nt;\u6231lcty;\u632D\u0980AHabcdefhijlorstuwz\u1C38\u1C3B\u1C3F\u1C5D\u1C69\u1C75\u1C8A\u1C9E\u1CAC\u1CB7\u1CFB\u1CFF\u1D0D\u1D7B\u1D91\u1DAB\u1DBB\u1DC6\u1DCDr\xF2\u0381ar;\u6965\u0200glrs\u1C48\u1C4D\u1C52\u1C54ger;\u6020eth;\u6138\xF2\u1133h\u0100;v\u1C5A\u1C5B\u6010\xBB\u090A\u016B\u1C61\u1C67arow;\u690Fa\xE3\u0315\u0100ay\u1C6E\u1C73ron;\u410F;\u4434\u0180;ao\u0332\u1C7C\u1C84\u0100gr\u02BF\u1C81r;\u61CAtseq;\u6A77\u0180glm\u1C91\u1C94\u1C98\u803B\xB0\u40B0ta;\u43B4ptyv;\u69B1\u0100ir\u1CA3\u1CA8sht;\u697F;\uC000\u{1D521}ar\u0100lr\u1CB3\u1CB5\xBB\u08DC\xBB\u101E\u0280aegsv\u1CC2\u0378\u1CD6\u1CDC\u1CE0m\u0180;os\u0326\u1CCA\u1CD4nd\u0100;s\u0326\u1CD1uit;\u6666amma;\u43DDin;\u62F2\u0180;io\u1CE7\u1CE8\u1CF8\u40F7de\u8100\xF7;o\u1CE7\u1CF0ntimes;\u62C7n\xF8\u1CF7cy;\u4452c\u026F\u1D06\0\0\u1D0Arn;\u631Eop;\u630D\u0280lptuw\u1D18\u1D1D\u1D22\u1D49\u1D55lar;\u4024f;\uC000\u{1D555}\u0280;emps\u030B\u1D2D\u1D37\u1D3D\u1D42q\u0100;d\u0352\u1D33ot;\u6251inus;\u6238lus;\u6214quare;\u62A1blebarwedg\xE5\xFAn\u0180adh\u112E\u1D5D\u1D67ownarrow\xF3\u1C83arpoon\u0100lr\u1D72\u1D76ef\xF4\u1CB4igh\xF4\u1CB6\u0162\u1D7F\u1D85karo\xF7\u0F42\u026F\u1D8A\0\0\u1D8Ern;\u631Fop;\u630C\u0180cot\u1D98\u1DA3\u1DA6\u0100ry\u1D9D\u1DA1;\uC000\u{1D4B9};\u4455l;\u69F6rok;\u4111\u0100dr\u1DB0\u1DB4ot;\u62F1i\u0100;f\u1DBA\u1816\u65BF\u0100ah\u1DC0\u1DC3r\xF2\u0429a\xF2\u0FA6angle;\u69A6\u0100ci\u1DD2\u1DD5y;\u445Fgrarr;\u67FF\u0900Dacdefglmnopqrstux\u1E01\u1E09\u1E19\u1E38\u0578\u1E3C\u1E49\u1E61\u1E7E\u1EA5\u1EAF\u1EBD\u1EE1\u1F2A\u1F37\u1F44\u1F4E\u1F5A\u0100Do\u1E06\u1D34o\xF4\u1C89\u0100cs\u1E0E\u1E14ute\u803B\xE9\u40E9ter;\u6A6E\u0200aioy\u1E22\u1E27\u1E31\u1E36ron;\u411Br\u0100;c\u1E2D\u1E2E\u6256\u803B\xEA\u40EAlon;\u6255;\u444Dot;\u4117\u0100Dr\u1E41\u1E45ot;\u6252;\uC000\u{1D522}\u0180;rs\u1E50\u1E51\u1E57\u6A9Aave\u803B\xE8\u40E8\u0100;d\u1E5C\u1E5D\u6A96ot;\u6A98\u0200;ils\u1E6A\u1E6B\u1E72\u1E74\u6A99nters;\u63E7;\u6113\u0100;d\u1E79\u1E7A\u6A95ot;\u6A97\u0180aps\u1E85\u1E89\u1E97cr;\u4113ty\u0180;sv\u1E92\u1E93\u1E95\u6205et\xBB\u1E93p\u01001;\u1E9D\u1EA4\u0133\u1EA1\u1EA3;\u6004;\u6005\u6003\u0100gs\u1EAA\u1EAC;\u414Bp;\u6002\u0100gp\u1EB4\u1EB8on;\u4119f;\uC000\u{1D556}\u0180als\u1EC4\u1ECE\u1ED2r\u0100;s\u1ECA\u1ECB\u62D5l;\u69E3us;\u6A71i\u0180;lv\u1EDA\u1EDB\u1EDF\u43B5on\xBB\u1EDB;\u43F5\u0200csuv\u1EEA\u1EF3\u1F0B\u1F23\u0100io\u1EEF\u1E31rc\xBB\u1E2E\u0269\u1EF9\0\0\u1EFB\xED\u0548ant\u0100gl\u1F02\u1F06tr\xBB\u1E5Dess\xBB\u1E7A\u0180aei\u1F12\u1F16\u1F1Als;\u403Dst;\u625Fv\u0100;D\u0235\u1F20D;\u6A78parsl;\u69E5\u0100Da\u1F2F\u1F33ot;\u6253rr;\u6971\u0180cdi\u1F3E\u1F41\u1EF8r;\u612Fo\xF4\u0352\u0100ah\u1F49\u1F4B;\u43B7\u803B\xF0\u40F0\u0100mr\u1F53\u1F57l\u803B\xEB\u40EBo;\u60AC\u0180cip\u1F61\u1F64\u1F67l;\u4021s\xF4\u056E\u0100eo\u1F6C\u1F74ctatio\xEE\u0559nential\xE5\u0579\u09E1\u1F92\0\u1F9E\0\u1FA1\u1FA7\0\0\u1FC6\u1FCC\0\u1FD3\0\u1FE6\u1FEA\u2000\0\u2008\u205Allingdotse\xF1\u1E44y;\u4444male;\u6640\u0180ilr\u1FAD\u1FB3\u1FC1lig;\u8000\uFB03\u0269\u1FB9\0\0\u1FBDg;\u8000\uFB00ig;\u8000\uFB04;\uC000\u{1D523}lig;\u8000\uFB01lig;\uC000fj\u0180alt\u1FD9\u1FDC\u1FE1t;\u666Dig;\u8000\uFB02ns;\u65B1of;\u4192\u01F0\u1FEE\0\u1FF3f;\uC000\u{1D557}\u0100ak\u05BF\u1FF7\u0100;v\u1FFC\u1FFD\u62D4;\u6AD9artint;\u6A0D\u0100ao\u200C\u2055\u0100cs\u2011\u2052\u03B1\u201A\u2030\u2038\u2045\u2048\0\u2050\u03B2\u2022\u2025\u2027\u202A\u202C\0\u202E\u803B\xBD\u40BD;\u6153\u803B\xBC\u40BC;\u6155;\u6159;\u615B\u01B3\u2034\0\u2036;\u6154;\u6156\u02B4\u203E\u2041\0\0\u2043\u803B\xBE\u40BE;\u6157;\u615C5;\u6158\u01B6\u204C\0\u204E;\u615A;\u615D8;\u615El;\u6044wn;\u6322cr;\uC000\u{1D4BB}\u0880Eabcdefgijlnorstv\u2082\u2089\u209F\u20A5\u20B0\u20B4\u20F0\u20F5\u20FA\u20FF\u2103\u2112\u2138\u0317\u213E\u2152\u219E\u0100;l\u064D\u2087;\u6A8C\u0180cmp\u2090\u2095\u209Dute;\u41F5ma\u0100;d\u209C\u1CDA\u43B3;\u6A86reve;\u411F\u0100iy\u20AA\u20AErc;\u411D;\u4433ot;\u4121\u0200;lqs\u063E\u0642\u20BD\u20C9\u0180;qs\u063E\u064C\u20C4lan\xF4\u0665\u0200;cdl\u0665\u20D2\u20D5\u20E5c;\u6AA9ot\u0100;o\u20DC\u20DD\u6A80\u0100;l\u20E2\u20E3\u6A82;\u6A84\u0100;e\u20EA\u20ED\uC000\u22DB\uFE00s;\u6A94r;\uC000\u{1D524}\u0100;g\u0673\u061Bmel;\u6137cy;\u4453\u0200;Eaj\u065A\u210C\u210E\u2110;\u6A92;\u6AA5;\u6AA4\u0200Eaes\u211B\u211D\u2129\u2134;\u6269p\u0100;p\u2123\u2124\u6A8Arox\xBB\u2124\u0100;q\u212E\u212F\u6A88\u0100;q\u212E\u211Bim;\u62E7pf;\uC000\u{1D558}\u0100ci\u2143\u2146r;\u610Am\u0180;el\u066B\u214E\u2150;\u6A8E;\u6A90\u8300>;cdlqr\u05EE\u2160\u216A\u216E\u2173\u2179\u0100ci\u2165\u2167;\u6AA7r;\u6A7Aot;\u62D7Par;\u6995uest;\u6A7C\u0280adels\u2184\u216A\u2190\u0656\u219B\u01F0\u2189\0\u218Epro\xF8\u209Er;\u6978q\u0100lq\u063F\u2196les\xF3\u2088i\xED\u066B\u0100en\u21A3\u21ADrtneqq;\uC000\u2269\uFE00\xC5\u21AA\u0500Aabcefkosy\u21C4\u21C7\u21F1\u21F5\u21FA\u2218\u221D\u222F\u2268\u227Dr\xF2\u03A0\u0200ilmr\u21D0\u21D4\u21D7\u21DBrs\xF0\u1484f\xBB\u2024il\xF4\u06A9\u0100dr\u21E0\u21E4cy;\u444A\u0180;cw\u08F4\u21EB\u21EFir;\u6948;\u61ADar;\u610Firc;\u4125\u0180alr\u2201\u220E\u2213rts\u0100;u\u2209\u220A\u6665it\xBB\u220Alip;\u6026con;\u62B9r;\uC000\u{1D525}s\u0100ew\u2223\u2229arow;\u6925arow;\u6926\u0280amopr\u223A\u223E\u2243\u225E\u2263rr;\u61FFtht;\u623Bk\u0100lr\u2249\u2253eftarrow;\u61A9ightarrow;\u61AAf;\uC000\u{1D559}bar;\u6015\u0180clt\u226F\u2274\u2278r;\uC000\u{1D4BD}as\xE8\u21F4rok;\u4127\u0100bp\u2282\u2287ull;\u6043hen\xBB\u1C5B\u0AE1\u22A3\0\u22AA\0\u22B8\u22C5\u22CE\0\u22D5\u22F3\0\0\u22F8\u2322\u2367\u2362\u237F\0\u2386\u23AA\u23B4cute\u803B\xED\u40ED\u0180;iy\u0771\u22B0\u22B5rc\u803B\xEE\u40EE;\u4438\u0100cx\u22BC\u22BFy;\u4435cl\u803B\xA1\u40A1\u0100fr\u039F\u22C9;\uC000\u{1D526}rave\u803B\xEC\u40EC\u0200;ino\u073E\u22DD\u22E9\u22EE\u0100in\u22E2\u22E6nt;\u6A0Ct;\u622Dfin;\u69DCta;\u6129lig;\u4133\u0180aop\u22FE\u231A\u231D\u0180cgt\u2305\u2308\u2317r;\u412B\u0180elp\u071F\u230F\u2313in\xE5\u078Ear\xF4\u0720h;\u4131f;\u62B7ed;\u41B5\u0280;cfot\u04F4\u232C\u2331\u233D\u2341are;\u6105in\u0100;t\u2338\u2339\u621Eie;\u69DDdo\xF4\u2319\u0280;celp\u0757\u234C\u2350\u235B\u2361al;\u62BA\u0100gr\u2355\u2359er\xF3\u1563\xE3\u234Darhk;\u6A17rod;\u6A3C\u0200cgpt\u236F\u2372\u2376\u237By;\u4451on;\u412Ff;\uC000\u{1D55A}a;\u43B9uest\u803B\xBF\u40BF\u0100ci\u238A\u238Fr;\uC000\u{1D4BE}n\u0280;Edsv\u04F4\u239B\u239D\u23A1\u04F3;\u62F9ot;\u62F5\u0100;v\u23A6\u23A7\u62F4;\u62F3\u0100;i\u0777\u23AElde;\u4129\u01EB\u23B8\0\u23BCcy;\u4456l\u803B\xEF\u40EF\u0300cfmosu\u23CC\u23D7\u23DC\u23E1\u23E7\u23F5\u0100iy\u23D1\u23D5rc;\u4135;\u4439r;\uC000\u{1D527}ath;\u4237pf;\uC000\u{1D55B}\u01E3\u23EC\0\u23F1r;\uC000\u{1D4BF}rcy;\u4458kcy;\u4454\u0400acfghjos\u240B\u2416\u2422\u2427\u242D\u2431\u2435\u243Bppa\u0100;v\u2413\u2414\u43BA;\u43F0\u0100ey\u241B\u2420dil;\u4137;\u443Ar;\uC000\u{1D528}reen;\u4138cy;\u4445cy;\u445Cpf;\uC000\u{1D55C}cr;\uC000\u{1D4C0}\u0B80ABEHabcdefghjlmnoprstuv\u2470\u2481\u2486\u248D\u2491\u250E\u253D\u255A\u2580\u264E\u265E\u2665\u2679\u267D\u269A\u26B2\u26D8\u275D\u2768\u278B\u27C0\u2801\u2812\u0180art\u2477\u247A\u247Cr\xF2\u09C6\xF2\u0395ail;\u691Barr;\u690E\u0100;g\u0994\u248B;\u6A8Bar;\u6962\u0963\u24A5\0\u24AA\0\u24B1\0\0\0\0\0\u24B5\u24BA\0\u24C6\u24C8\u24CD\0\u24F9ute;\u413Amptyv;\u69B4ra\xEE\u084Cbda;\u43BBg\u0180;dl\u088E\u24C1\u24C3;\u6991\xE5\u088E;\u6A85uo\u803B\xAB\u40ABr\u0400;bfhlpst\u0899\u24DE\u24E6\u24E9\u24EB\u24EE\u24F1\u24F5\u0100;f\u089D\u24E3s;\u691Fs;\u691D\xEB\u2252p;\u61ABl;\u6939im;\u6973l;\u61A2\u0180;ae\u24FF\u2500\u2504\u6AABil;\u6919\u0100;s\u2509\u250A\u6AAD;\uC000\u2AAD\uFE00\u0180abr\u2515\u2519\u251Drr;\u690Crk;\u6772\u0100ak\u2522\u252Cc\u0100ek\u2528\u252A;\u407B;\u405B\u0100es\u2531\u2533;\u698Bl\u0100du\u2539\u253B;\u698F;\u698D\u0200aeuy\u2546\u254B\u2556\u2558ron;\u413E\u0100di\u2550\u2554il;\u413C\xEC\u08B0\xE2\u2529;\u443B\u0200cqrs\u2563\u2566\u256D\u257Da;\u6936uo\u0100;r\u0E19\u1746\u0100du\u2572\u2577har;\u6967shar;\u694Bh;\u61B2\u0280;fgqs\u258B\u258C\u0989\u25F3\u25FF\u6264t\u0280ahlrt\u2598\u25A4\u25B7\u25C2\u25E8rrow\u0100;t\u0899\u25A1a\xE9\u24F6arpoon\u0100du\u25AF\u25B4own\xBB\u045Ap\xBB\u0966eftarrows;\u61C7ight\u0180ahs\u25CD\u25D6\u25DErrow\u0100;s\u08F4\u08A7arpoon\xF3\u0F98quigarro\xF7\u21F0hreetimes;\u62CB\u0180;qs\u258B\u0993\u25FAlan\xF4\u09AC\u0280;cdgs\u09AC\u260A\u260D\u261D\u2628c;\u6AA8ot\u0100;o\u2614\u2615\u6A7F\u0100;r\u261A\u261B\u6A81;\u6A83\u0100;e\u2622\u2625\uC000\u22DA\uFE00s;\u6A93\u0280adegs\u2633\u2639\u263D\u2649\u264Bppro\xF8\u24C6ot;\u62D6q\u0100gq\u2643\u2645\xF4\u0989gt\xF2\u248C\xF4\u099Bi\xED\u09B2\u0180ilr\u2655\u08E1\u265Asht;\u697C;\uC000\u{1D529}\u0100;E\u099C\u2663;\u6A91\u0161\u2669\u2676r\u0100du\u25B2\u266E\u0100;l\u0965\u2673;\u696Alk;\u6584cy;\u4459\u0280;acht\u0A48\u2688\u268B\u2691\u2696r\xF2\u25C1orne\xF2\u1D08ard;\u696Bri;\u65FA\u0100io\u269F\u26A4dot;\u4140ust\u0100;a\u26AC\u26AD\u63B0che\xBB\u26AD\u0200Eaes\u26BB\u26BD\u26C9\u26D4;\u6268p\u0100;p\u26C3\u26C4\u6A89rox\xBB\u26C4\u0100;q\u26CE\u26CF\u6A87\u0100;q\u26CE\u26BBim;\u62E6\u0400abnoptwz\u26E9\u26F4\u26F7\u271A\u272F\u2741\u2747\u2750\u0100nr\u26EE\u26F1g;\u67ECr;\u61FDr\xEB\u08C1g\u0180lmr\u26FF\u270D\u2714eft\u0100ar\u09E6\u2707ight\xE1\u09F2apsto;\u67FCight\xE1\u09FDparrow\u0100lr\u2725\u2729ef\xF4\u24EDight;\u61AC\u0180afl\u2736\u2739\u273Dr;\u6985;\uC000\u{1D55D}us;\u6A2Dimes;\u6A34\u0161\u274B\u274Fst;\u6217\xE1\u134E\u0180;ef\u2757\u2758\u1800\u65CAnge\xBB\u2758ar\u0100;l\u2764\u2765\u4028t;\u6993\u0280achmt\u2773\u2776\u277C\u2785\u2787r\xF2\u08A8orne\xF2\u1D8Car\u0100;d\u0F98\u2783;\u696D;\u600Eri;\u62BF\u0300achiqt\u2798\u279D\u0A40\u27A2\u27AE\u27BBquo;\u6039r;\uC000\u{1D4C1}m\u0180;eg\u09B2\u27AA\u27AC;\u6A8D;\u6A8F\u0100bu\u252A\u27B3o\u0100;r\u0E1F\u27B9;\u601Arok;\u4142\u8400<;cdhilqr\u082B\u27D2\u2639\u27DC\u27E0\u27E5\u27EA\u27F0\u0100ci\u27D7\u27D9;\u6AA6r;\u6A79re\xE5\u25F2mes;\u62C9arr;\u6976uest;\u6A7B\u0100Pi\u27F5\u27F9ar;\u6996\u0180;ef\u2800\u092D\u181B\u65C3r\u0100du\u2807\u280Dshar;\u694Ahar;\u6966\u0100en\u2817\u2821rtneqq;\uC000\u2268\uFE00\xC5\u281E\u0700Dacdefhilnopsu\u2840\u2845\u2882\u288E\u2893\u28A0\u28A5\u28A8\u28DA\u28E2\u28E4\u0A83\u28F3\u2902Dot;\u623A\u0200clpr\u284E\u2852\u2863\u287Dr\u803B\xAF\u40AF\u0100et\u2857\u2859;\u6642\u0100;e\u285E\u285F\u6720se\xBB\u285F\u0100;s\u103B\u2868to\u0200;dlu\u103B\u2873\u2877\u287Bow\xEE\u048Cef\xF4\u090F\xF0\u13D1ker;\u65AE\u0100oy\u2887\u288Cmma;\u6A29;\u443Cash;\u6014asuredangle\xBB\u1626r;\uC000\u{1D52A}o;\u6127\u0180cdn\u28AF\u28B4\u28C9ro\u803B\xB5\u40B5\u0200;acd\u1464\u28BD\u28C0\u28C4s\xF4\u16A7ir;\u6AF0ot\u80BB\xB7\u01B5us\u0180;bd\u28D2\u1903\u28D3\u6212\u0100;u\u1D3C\u28D8;\u6A2A\u0163\u28DE\u28E1p;\u6ADB\xF2\u2212\xF0\u0A81\u0100dp\u28E9\u28EEels;\u62A7f;\uC000\u{1D55E}\u0100ct\u28F8\u28FDr;\uC000\u{1D4C2}pos\xBB\u159D\u0180;lm\u2909\u290A\u290D\u43BCtimap;\u62B8\u0C00GLRVabcdefghijlmoprstuvw\u2942\u2953\u297E\u2989\u2998\u29DA\u29E9\u2A15\u2A1A\u2A58\u2A5D\u2A83\u2A95\u2AA4\u2AA8\u2B04\u2B07\u2B44\u2B7F\u2BAE\u2C34\u2C67\u2C7C\u2CE9\u0100gt\u2947\u294B;\uC000\u22D9\u0338\u0100;v\u2950\u0BCF\uC000\u226B\u20D2\u0180elt\u295A\u2972\u2976ft\u0100ar\u2961\u2967rrow;\u61CDightarrow;\u61CE;\uC000\u22D8\u0338\u0100;v\u297B\u0C47\uC000\u226A\u20D2ightarrow;\u61CF\u0100Dd\u298E\u2993ash;\u62AFash;\u62AE\u0280bcnpt\u29A3\u29A7\u29AC\u29B1\u29CCla\xBB\u02DEute;\u4144g;\uC000\u2220\u20D2\u0280;Eiop\u0D84\u29BC\u29C0\u29C5\u29C8;\uC000\u2A70\u0338d;\uC000\u224B\u0338s;\u4149ro\xF8\u0D84ur\u0100;a\u29D3\u29D4\u666El\u0100;s\u29D3\u0B38\u01F3\u29DF\0\u29E3p\u80BB\xA0\u0B37mp\u0100;e\u0BF9\u0C00\u0280aeouy\u29F4\u29FE\u2A03\u2A10\u2A13\u01F0\u29F9\0\u29FB;\u6A43on;\u4148dil;\u4146ng\u0100;d\u0D7E\u2A0Aot;\uC000\u2A6D\u0338p;\u6A42;\u443Dash;\u6013\u0380;Aadqsx\u0B92\u2A29\u2A2D\u2A3B\u2A41\u2A45\u2A50rr;\u61D7r\u0100hr\u2A33\u2A36k;\u6924\u0100;o\u13F2\u13F0ot;\uC000\u2250\u0338ui\xF6\u0B63\u0100ei\u2A4A\u2A4Ear;\u6928\xED\u0B98ist\u0100;s\u0BA0\u0B9Fr;\uC000\u{1D52B}\u0200Eest\u0BC5\u2A66\u2A79\u2A7C\u0180;qs\u0BBC\u2A6D\u0BE1\u0180;qs\u0BBC\u0BC5\u2A74lan\xF4\u0BE2i\xED\u0BEA\u0100;r\u0BB6\u2A81\xBB\u0BB7\u0180Aap\u2A8A\u2A8D\u2A91r\xF2\u2971rr;\u61AEar;\u6AF2\u0180;sv\u0F8D\u2A9C\u0F8C\u0100;d\u2AA1\u2AA2\u62FC;\u62FAcy;\u445A\u0380AEadest\u2AB7\u2ABA\u2ABE\u2AC2\u2AC5\u2AF6\u2AF9r\xF2\u2966;\uC000\u2266\u0338rr;\u619Ar;\u6025\u0200;fqs\u0C3B\u2ACE\u2AE3\u2AEFt\u0100ar\u2AD4\u2AD9rro\xF7\u2AC1ightarro\xF7\u2A90\u0180;qs\u0C3B\u2ABA\u2AEAlan\xF4\u0C55\u0100;s\u0C55\u2AF4\xBB\u0C36i\xED\u0C5D\u0100;r\u0C35\u2AFEi\u0100;e\u0C1A\u0C25i\xE4\u0D90\u0100pt\u2B0C\u2B11f;\uC000\u{1D55F}\u8180\xAC;in\u2B19\u2B1A\u2B36\u40ACn\u0200;Edv\u0B89\u2B24\u2B28\u2B2E;\uC000\u22F9\u0338ot;\uC000\u22F5\u0338\u01E1\u0B89\u2B33\u2B35;\u62F7;\u62F6i\u0100;v\u0CB8\u2B3C\u01E1\u0CB8\u2B41\u2B43;\u62FE;\u62FD\u0180aor\u2B4B\u2B63\u2B69r\u0200;ast\u0B7B\u2B55\u2B5A\u2B5Flle\xEC\u0B7Bl;\uC000\u2AFD\u20E5;\uC000\u2202\u0338lint;\u6A14\u0180;ce\u0C92\u2B70\u2B73u\xE5\u0CA5\u0100;c\u0C98\u2B78\u0100;e\u0C92\u2B7D\xF1\u0C98\u0200Aait\u2B88\u2B8B\u2B9D\u2BA7r\xF2\u2988rr\u0180;cw\u2B94\u2B95\u2B99\u619B;\uC000\u2933\u0338;\uC000\u219D\u0338ghtarrow\xBB\u2B95ri\u0100;e\u0CCB\u0CD6\u0380chimpqu\u2BBD\u2BCD\u2BD9\u2B04\u0B78\u2BE4\u2BEF\u0200;cer\u0D32\u2BC6\u0D37\u2BC9u\xE5\u0D45;\uC000\u{1D4C3}ort\u026D\u2B05\0\0\u2BD6ar\xE1\u2B56m\u0100;e\u0D6E\u2BDF\u0100;q\u0D74\u0D73su\u0100bp\u2BEB\u2BED\xE5\u0CF8\xE5\u0D0B\u0180bcp\u2BF6\u2C11\u2C19\u0200;Ees\u2BFF\u2C00\u0D22\u2C04\u6284;\uC000\u2AC5\u0338et\u0100;e\u0D1B\u2C0Bq\u0100;q\u0D23\u2C00c\u0100;e\u0D32\u2C17\xF1\u0D38\u0200;Ees\u2C22\u2C23\u0D5F\u2C27\u6285;\uC000\u2AC6\u0338et\u0100;e\u0D58\u2C2Eq\u0100;q\u0D60\u2C23\u0200gilr\u2C3D\u2C3F\u2C45\u2C47\xEC\u0BD7lde\u803B\xF1\u40F1\xE7\u0C43iangle\u0100lr\u2C52\u2C5Ceft\u0100;e\u0C1A\u2C5A\xF1\u0C26ight\u0100;e\u0CCB\u2C65\xF1\u0CD7\u0100;m\u2C6C\u2C6D\u43BD\u0180;es\u2C74\u2C75\u2C79\u4023ro;\u6116p;\u6007\u0480DHadgilrs\u2C8F\u2C94\u2C99\u2C9E\u2CA3\u2CB0\u2CB6\u2CD3\u2CE3ash;\u62ADarr;\u6904p;\uC000\u224D\u20D2ash;\u62AC\u0100et\u2CA8\u2CAC;\uC000\u2265\u20D2;\uC000>\u20D2nfin;\u69DE\u0180Aet\u2CBD\u2CC1\u2CC5rr;\u6902;\uC000\u2264\u20D2\u0100;r\u2CCA\u2CCD\uC000<\u20D2ie;\uC000\u22B4\u20D2\u0100At\u2CD8\u2CDCrr;\u6903rie;\uC000\u22B5\u20D2im;\uC000\u223C\u20D2\u0180Aan\u2CF0\u2CF4\u2D02rr;\u61D6r\u0100hr\u2CFA\u2CFDk;\u6923\u0100;o\u13E7\u13E5ear;\u6927\u1253\u1A95\0\0\0\0\0\0\0\0\0\0\0\0\0\u2D2D\0\u2D38\u2D48\u2D60\u2D65\u2D72\u2D84\u1B07\0\0\u2D8D\u2DAB\0\u2DC8\u2DCE\0\u2DDC\u2E19\u2E2B\u2E3E\u2E43\u0100cs\u2D31\u1A97ute\u803B\xF3\u40F3\u0100iy\u2D3C\u2D45r\u0100;c\u1A9E\u2D42\u803B\xF4\u40F4;\u443E\u0280abios\u1AA0\u2D52\u2D57\u01C8\u2D5Alac;\u4151v;\u6A38old;\u69BClig;\u4153\u0100cr\u2D69\u2D6Dir;\u69BF;\uC000\u{1D52C}\u036F\u2D79\0\0\u2D7C\0\u2D82n;\u42DBave\u803B\xF2\u40F2;\u69C1\u0100bm\u2D88\u0DF4ar;\u69B5\u0200acit\u2D95\u2D98\u2DA5\u2DA8r\xF2\u1A80\u0100ir\u2D9D\u2DA0r;\u69BEoss;\u69BBn\xE5\u0E52;\u69C0\u0180aei\u2DB1\u2DB5\u2DB9cr;\u414Dga;\u43C9\u0180cdn\u2DC0\u2DC5\u01CDron;\u43BF;\u69B6pf;\uC000\u{1D560}\u0180ael\u2DD4\u2DD7\u01D2r;\u69B7rp;\u69B9\u0380;adiosv\u2DEA\u2DEB\u2DEE\u2E08\u2E0D\u2E10\u2E16\u6228r\xF2\u1A86\u0200;efm\u2DF7\u2DF8\u2E02\u2E05\u6A5Dr\u0100;o\u2DFE\u2DFF\u6134f\xBB\u2DFF\u803B\xAA\u40AA\u803B\xBA\u40BAgof;\u62B6r;\u6A56lope;\u6A57;\u6A5B\u0180clo\u2E1F\u2E21\u2E27\xF2\u2E01ash\u803B\xF8\u40F8l;\u6298i\u016C\u2E2F\u2E34de\u803B\xF5\u40F5es\u0100;a\u01DB\u2E3As;\u6A36ml\u803B\xF6\u40F6bar;\u633D\u0AE1\u2E5E\0\u2E7D\0\u2E80\u2E9D\0\u2EA2\u2EB9\0\0\u2ECB\u0E9C\0\u2F13\0\0\u2F2B\u2FBC\0\u2FC8r\u0200;ast\u0403\u2E67\u2E72\u0E85\u8100\xB6;l\u2E6D\u2E6E\u40B6le\xEC\u0403\u0269\u2E78\0\0\u2E7Bm;\u6AF3;\u6AFDy;\u443Fr\u0280cimpt\u2E8B\u2E8F\u2E93\u1865\u2E97nt;\u4025od;\u402Eil;\u6030enk;\u6031r;\uC000\u{1D52D}\u0180imo\u2EA8\u2EB0\u2EB4\u0100;v\u2EAD\u2EAE\u43C6;\u43D5ma\xF4\u0A76ne;\u660E\u0180;tv\u2EBF\u2EC0\u2EC8\u43C0chfork\xBB\u1FFD;\u43D6\u0100au\u2ECF\u2EDFn\u0100ck\u2ED5\u2EDDk\u0100;h\u21F4\u2EDB;\u610E\xF6\u21F4s\u0480;abcdemst\u2EF3\u2EF4\u1908\u2EF9\u2EFD\u2F04\u2F06\u2F0A\u2F0E\u402Bcir;\u6A23ir;\u6A22\u0100ou\u1D40\u2F02;\u6A25;\u6A72n\u80BB\xB1\u0E9Dim;\u6A26wo;\u6A27\u0180ipu\u2F19\u2F20\u2F25ntint;\u6A15f;\uC000\u{1D561}nd\u803B\xA3\u40A3\u0500;Eaceinosu\u0EC8\u2F3F\u2F41\u2F44\u2F47\u2F81\u2F89\u2F92\u2F7E\u2FB6;\u6AB3p;\u6AB7u\xE5\u0ED9\u0100;c\u0ECE\u2F4C\u0300;acens\u0EC8\u2F59\u2F5F\u2F66\u2F68\u2F7Eppro\xF8\u2F43urlye\xF1\u0ED9\xF1\u0ECE\u0180aes\u2F6F\u2F76\u2F7Approx;\u6AB9qq;\u6AB5im;\u62E8i\xED\u0EDFme\u0100;s\u2F88\u0EAE\u6032\u0180Eas\u2F78\u2F90\u2F7A\xF0\u2F75\u0180dfp\u0EEC\u2F99\u2FAF\u0180als\u2FA0\u2FA5\u2FAAlar;\u632Eine;\u6312urf;\u6313\u0100;t\u0EFB\u2FB4\xEF\u0EFBrel;\u62B0\u0100ci\u2FC0\u2FC5r;\uC000\u{1D4C5};\u43C8ncsp;\u6008\u0300fiopsu\u2FDA\u22E2\u2FDF\u2FE5\u2FEB\u2FF1r;\uC000\u{1D52E}pf;\uC000\u{1D562}rime;\u6057cr;\uC000\u{1D4C6}\u0180aeo\u2FF8\u3009\u3013t\u0100ei\u2FFE\u3005rnion\xF3\u06B0nt;\u6A16st\u0100;e\u3010\u3011\u403F\xF1\u1F19\xF4\u0F14\u0A80ABHabcdefhilmnoprstux\u3040\u3051\u3055\u3059\u30E0\u310E\u312B\u3147\u3162\u3172\u318E\u3206\u3215\u3224\u3229\u3258\u326E\u3272\u3290\u32B0\u32B7\u0180art\u3047\u304A\u304Cr\xF2\u10B3\xF2\u03DDail;\u691Car\xF2\u1C65ar;\u6964\u0380cdenqrt\u3068\u3075\u3078\u307F\u308F\u3094\u30CC\u0100eu\u306D\u3071;\uC000\u223D\u0331te;\u4155i\xE3\u116Emptyv;\u69B3g\u0200;del\u0FD1\u3089\u308B\u308D;\u6992;\u69A5\xE5\u0FD1uo\u803B\xBB\u40BBr\u0580;abcfhlpstw\u0FDC\u30AC\u30AF\u30B7\u30B9\u30BC\u30BE\u30C0\u30C3\u30C7\u30CAp;\u6975\u0100;f\u0FE0\u30B4s;\u6920;\u6933s;\u691E\xEB\u225D\xF0\u272El;\u6945im;\u6974l;\u61A3;\u619D\u0100ai\u30D1\u30D5il;\u691Ao\u0100;n\u30DB\u30DC\u6236al\xF3\u0F1E\u0180abr\u30E7\u30EA\u30EEr\xF2\u17E5rk;\u6773\u0100ak\u30F3\u30FDc\u0100ek\u30F9\u30FB;\u407D;\u405D\u0100es\u3102\u3104;\u698Cl\u0100du\u310A\u310C;\u698E;\u6990\u0200aeuy\u3117\u311C\u3127\u3129ron;\u4159\u0100di\u3121\u3125il;\u4157\xEC\u0FF2\xE2\u30FA;\u4440\u0200clqs\u3134\u3137\u313D\u3144a;\u6937dhar;\u6969uo\u0100;r\u020E\u020Dh;\u61B3\u0180acg\u314E\u315F\u0F44l\u0200;ips\u0F78\u3158\u315B\u109Cn\xE5\u10BBar\xF4\u0FA9t;\u65AD\u0180ilr\u3169\u1023\u316Esht;\u697D;\uC000\u{1D52F}\u0100ao\u3177\u3186r\u0100du\u317D\u317F\xBB\u047B\u0100;l\u1091\u3184;\u696C\u0100;v\u318B\u318C\u43C1;\u43F1\u0180gns\u3195\u31F9\u31FCht\u0300ahlrst\u31A4\u31B0\u31C2\u31D8\u31E4\u31EErrow\u0100;t\u0FDC\u31ADa\xE9\u30C8arpoon\u0100du\u31BB\u31BFow\xEE\u317Ep\xBB\u1092eft\u0100ah\u31CA\u31D0rrow\xF3\u0FEAarpoon\xF3\u0551ightarrows;\u61C9quigarro\xF7\u30CBhreetimes;\u62CCg;\u42DAingdotse\xF1\u1F32\u0180ahm\u320D\u3210\u3213r\xF2\u0FEAa\xF2\u0551;\u600Foust\u0100;a\u321E\u321F\u63B1che\xBB\u321Fmid;\u6AEE\u0200abpt\u3232\u323D\u3240\u3252\u0100nr\u3237\u323Ag;\u67EDr;\u61FEr\xEB\u1003\u0180afl\u3247\u324A\u324Er;\u6986;\uC000\u{1D563}us;\u6A2Eimes;\u6A35\u0100ap\u325D\u3267r\u0100;g\u3263\u3264\u4029t;\u6994olint;\u6A12ar\xF2\u31E3\u0200achq\u327B\u3280\u10BC\u3285quo;\u603Ar;\uC000\u{1D4C7}\u0100bu\u30FB\u328Ao\u0100;r\u0214\u0213\u0180hir\u3297\u329B\u32A0re\xE5\u31F8mes;\u62CAi\u0200;efl\u32AA\u1059\u1821\u32AB\u65B9tri;\u69CEluhar;\u6968;\u611E\u0D61\u32D5\u32DB\u32DF\u332C\u3338\u3371\0\u337A\u33A4\0\0\u33EC\u33F0\0\u3428\u3448\u345A\u34AD\u34B1\u34CA\u34F1\0\u3616\0\0\u3633cute;\u415Bqu\xEF\u27BA\u0500;Eaceinpsy\u11ED\u32F3\u32F5\u32FF\u3302\u330B\u330F\u331F\u3326\u3329;\u6AB4\u01F0\u32FA\0\u32FC;\u6AB8on;\u4161u\xE5\u11FE\u0100;d\u11F3\u3307il;\u415Frc;\u415D\u0180Eas\u3316\u3318\u331B;\u6AB6p;\u6ABAim;\u62E9olint;\u6A13i\xED\u1204;\u4441ot\u0180;be\u3334\u1D47\u3335\u62C5;\u6A66\u0380Aacmstx\u3346\u334A\u3357\u335B\u335E\u3363\u336Drr;\u61D8r\u0100hr\u3350\u3352\xEB\u2228\u0100;o\u0A36\u0A34t\u803B\xA7\u40A7i;\u403Bwar;\u6929m\u0100in\u3369\xF0nu\xF3\xF1t;\u6736r\u0100;o\u3376\u2055\uC000\u{1D530}\u0200acoy\u3382\u3386\u3391\u33A0rp;\u666F\u0100hy\u338B\u338Fcy;\u4449;\u4448rt\u026D\u3399\0\0\u339Ci\xE4\u1464ara\xEC\u2E6F\u803B\xAD\u40AD\u0100gm\u33A8\u33B4ma\u0180;fv\u33B1\u33B2\u33B2\u43C3;\u43C2\u0400;deglnpr\u12AB\u33C5\u33C9\u33CE\u33D6\u33DE\u33E1\u33E6ot;\u6A6A\u0100;q\u12B1\u12B0\u0100;E\u33D3\u33D4\u6A9E;\u6AA0\u0100;E\u33DB\u33DC\u6A9D;\u6A9Fe;\u6246lus;\u6A24arr;\u6972ar\xF2\u113D\u0200aeit\u33F8\u3408\u340F\u3417\u0100ls\u33FD\u3404lsetm\xE9\u336Ahp;\u6A33parsl;\u69E4\u0100dl\u1463\u3414e;\u6323\u0100;e\u341C\u341D\u6AAA\u0100;s\u3422\u3423\u6AAC;\uC000\u2AAC\uFE00\u0180flp\u342E\u3433\u3442tcy;\u444C\u0100;b\u3438\u3439\u402F\u0100;a\u343E\u343F\u69C4r;\u633Ff;\uC000\u{1D564}a\u0100dr\u344D\u0402es\u0100;u\u3454\u3455\u6660it\xBB\u3455\u0180csu\u3460\u3479\u349F\u0100au\u3465\u346Fp\u0100;s\u1188\u346B;\uC000\u2293\uFE00p\u0100;s\u11B4\u3475;\uC000\u2294\uFE00u\u0100bp\u347F\u348F\u0180;es\u1197\u119C\u3486et\u0100;e\u1197\u348D\xF1\u119D\u0180;es\u11A8\u11AD\u3496et\u0100;e\u11A8\u349D\xF1\u11AE\u0180;af\u117B\u34A6\u05B0r\u0165\u34AB\u05B1\xBB\u117Car\xF2\u1148\u0200cemt\u34B9\u34BE\u34C2\u34C5r;\uC000\u{1D4C8}tm\xEE\xF1i\xEC\u3415ar\xE6\u11BE\u0100ar\u34CE\u34D5r\u0100;f\u34D4\u17BF\u6606\u0100an\u34DA\u34EDight\u0100ep\u34E3\u34EApsilo\xEE\u1EE0h\xE9\u2EAFs\xBB\u2852\u0280bcmnp\u34FB\u355E\u1209\u358B\u358E\u0480;Edemnprs\u350E\u350F\u3511\u3515\u351E\u3523\u352C\u3531\u3536\u6282;\u6AC5ot;\u6ABD\u0100;d\u11DA\u351Aot;\u6AC3ult;\u6AC1\u0100Ee\u3528\u352A;\u6ACB;\u628Alus;\u6ABFarr;\u6979\u0180eiu\u353D\u3552\u3555t\u0180;en\u350E\u3545\u354Bq\u0100;q\u11DA\u350Feq\u0100;q\u352B\u3528m;\u6AC7\u0100bp\u355A\u355C;\u6AD5;\u6AD3c\u0300;acens\u11ED\u356C\u3572\u3579\u357B\u3326ppro\xF8\u32FAurlye\xF1\u11FE\xF1\u11F3\u0180aes\u3582\u3588\u331Bppro\xF8\u331Aq\xF1\u3317g;\u666A\u0680123;Edehlmnps\u35A9\u35AC\u35AF\u121C\u35B2\u35B4\u35C0\u35C9\u35D5\u35DA\u35DF\u35E8\u35ED\u803B\xB9\u40B9\u803B\xB2\u40B2\u803B\xB3\u40B3;\u6AC6\u0100os\u35B9\u35BCt;\u6ABEub;\u6AD8\u0100;d\u1222\u35C5ot;\u6AC4s\u0100ou\u35CF\u35D2l;\u67C9b;\u6AD7arr;\u697Bult;\u6AC2\u0100Ee\u35E4\u35E6;\u6ACC;\u628Blus;\u6AC0\u0180eiu\u35F4\u3609\u360Ct\u0180;en\u121C\u35FC\u3602q\u0100;q\u1222\u35B2eq\u0100;q\u35E7\u35E4m;\u6AC8\u0100bp\u3611\u3613;\u6AD4;\u6AD6\u0180Aan\u361C\u3620\u362Drr;\u61D9r\u0100hr\u3626\u3628\xEB\u222E\u0100;o\u0A2B\u0A29war;\u692Alig\u803B\xDF\u40DF\u0BE1\u3651\u365D\u3660\u12CE\u3673\u3679\0\u367E\u36C2\0\0\0\0\0\u36DB\u3703\0\u3709\u376C\0\0\0\u3787\u0272\u3656\0\0\u365Bget;\u6316;\u43C4r\xEB\u0E5F\u0180aey\u3666\u366B\u3670ron;\u4165dil;\u4163;\u4442lrec;\u6315r;\uC000\u{1D531}\u0200eiko\u3686\u369D\u36B5\u36BC\u01F2\u368B\0\u3691e\u01004f\u1284\u1281a\u0180;sv\u3698\u3699\u369B\u43B8ym;\u43D1\u0100cn\u36A2\u36B2k\u0100as\u36A8\u36AEppro\xF8\u12C1im\xBB\u12ACs\xF0\u129E\u0100as\u36BA\u36AE\xF0\u12C1rn\u803B\xFE\u40FE\u01EC\u031F\u36C6\u22E7es\u8180\xD7;bd\u36CF\u36D0\u36D8\u40D7\u0100;a\u190F\u36D5r;\u6A31;\u6A30\u0180eps\u36E1\u36E3\u3700\xE1\u2A4D\u0200;bcf\u0486\u36EC\u36F0\u36F4ot;\u6336ir;\u6AF1\u0100;o\u36F9\u36FC\uC000\u{1D565}rk;\u6ADA\xE1\u3362rime;\u6034\u0180aip\u370F\u3712\u3764d\xE5\u1248\u0380adempst\u3721\u374D\u3740\u3751\u3757\u375C\u375Fngle\u0280;dlqr\u3730\u3731\u3736\u3740\u3742\u65B5own\xBB\u1DBBeft\u0100;e\u2800\u373E\xF1\u092E;\u625Cight\u0100;e\u32AA\u374B\xF1\u105Aot;\u65ECinus;\u6A3Alus;\u6A39b;\u69CDime;\u6A3Bezium;\u63E2\u0180cht\u3772\u377D\u3781\u0100ry\u3777\u377B;\uC000\u{1D4C9};\u4446cy;\u445Brok;\u4167\u0100io\u378B\u378Ex\xF4\u1777head\u0100lr\u3797\u37A0eftarro\xF7\u084Fightarrow\xBB\u0F5D\u0900AHabcdfghlmoprstuw\u37D0\u37D3\u37D7\u37E4\u37F0\u37FC\u380E\u381C\u3823\u3834\u3851\u385D\u386B\u38A9\u38CC\u38D2\u38EA\u38F6r\xF2\u03EDar;\u6963\u0100cr\u37DC\u37E2ute\u803B\xFA\u40FA\xF2\u1150r\u01E3\u37EA\0\u37EDy;\u445Eve;\u416D\u0100iy\u37F5\u37FArc\u803B\xFB\u40FB;\u4443\u0180abh\u3803\u3806\u380Br\xF2\u13ADlac;\u4171a\xF2\u13C3\u0100ir\u3813\u3818sht;\u697E;\uC000\u{1D532}rave\u803B\xF9\u40F9\u0161\u3827\u3831r\u0100lr\u382C\u382E\xBB\u0957\xBB\u1083lk;\u6580\u0100ct\u3839\u384D\u026F\u383F\0\0\u384Arn\u0100;e\u3845\u3846\u631Cr\xBB\u3846op;\u630Fri;\u65F8\u0100al\u3856\u385Acr;\u416B\u80BB\xA8\u0349\u0100gp\u3862\u3866on;\u4173f;\uC000\u{1D566}\u0300adhlsu\u114B\u3878\u387D\u1372\u3891\u38A0own\xE1\u13B3arpoon\u0100lr\u3888\u388Cef\xF4\u382Digh\xF4\u382Fi\u0180;hl\u3899\u389A\u389C\u43C5\xBB\u13FAon\xBB\u389Aparrows;\u61C8\u0180cit\u38B0\u38C4\u38C8\u026F\u38B6\0\0\u38C1rn\u0100;e\u38BC\u38BD\u631Dr\xBB\u38BDop;\u630Eng;\u416Fri;\u65F9cr;\uC000\u{1D4CA}\u0180dir\u38D9\u38DD\u38E2ot;\u62F0lde;\u4169i\u0100;f\u3730\u38E8\xBB\u1813\u0100am\u38EF\u38F2r\xF2\u38A8l\u803B\xFC\u40FCangle;\u69A7\u0780ABDacdeflnoprsz\u391C\u391F\u3929\u392D\u39B5\u39B8\u39BD\u39DF\u39E4\u39E8\u39F3\u39F9\u39FD\u3A01\u3A20r\xF2\u03F7ar\u0100;v\u3926\u3927\u6AE8;\u6AE9as\xE8\u03E1\u0100nr\u3932\u3937grt;\u699C\u0380eknprst\u34E3\u3946\u394B\u3952\u395D\u3964\u3996app\xE1\u2415othin\xE7\u1E96\u0180hir\u34EB\u2EC8\u3959op\xF4\u2FB5\u0100;h\u13B7\u3962\xEF\u318D\u0100iu\u3969\u396Dgm\xE1\u33B3\u0100bp\u3972\u3984setneq\u0100;q\u397D\u3980\uC000\u228A\uFE00;\uC000\u2ACB\uFE00setneq\u0100;q\u398F\u3992\uC000\u228B\uFE00;\uC000\u2ACC\uFE00\u0100hr\u399B\u399Fet\xE1\u369Ciangle\u0100lr\u39AA\u39AFeft\xBB\u0925ight\xBB\u1051y;\u4432ash\xBB\u1036\u0180elr\u39C4\u39D2\u39D7\u0180;be\u2DEA\u39CB\u39CFar;\u62BBq;\u625Alip;\u62EE\u0100bt\u39DC\u1468a\xF2\u1469r;\uC000\u{1D533}tr\xE9\u39AEsu\u0100bp\u39EF\u39F1\xBB\u0D1C\xBB\u0D59pf;\uC000\u{1D567}ro\xF0\u0EFBtr\xE9\u39B4\u0100cu\u3A06\u3A0Br;\uC000\u{1D4CB}\u0100bp\u3A10\u3A18n\u0100Ee\u3980\u3A16\xBB\u397En\u0100Ee\u3992\u3A1E\xBB\u3990igzag;\u699A\u0380cefoprs\u3A36\u3A3B\u3A56\u3A5B\u3A54\u3A61\u3A6Airc;\u4175\u0100di\u3A40\u3A51\u0100bg\u3A45\u3A49ar;\u6A5Fe\u0100;q\u15FA\u3A4F;\u6259erp;\u6118r;\uC000\u{1D534}pf;\uC000\u{1D568}\u0100;e\u1479\u3A66at\xE8\u1479cr;\uC000\u{1D4CC}\u0AE3\u178E\u3A87\0\u3A8B\0\u3A90\u3A9B\0\0\u3A9D\u3AA8\u3AAB\u3AAF\0\0\u3AC3\u3ACE\0\u3AD8\u17DC\u17DFtr\xE9\u17D1r;\uC000\u{1D535}\u0100Aa\u3A94\u3A97r\xF2\u03C3r\xF2\u09F6;\u43BE\u0100Aa\u3AA1\u3AA4r\xF2\u03B8r\xF2\u09EBa\xF0\u2713is;\u62FB\u0180dpt\u17A4\u3AB5\u3ABE\u0100fl\u3ABA\u17A9;\uC000\u{1D569}im\xE5\u17B2\u0100Aa\u3AC7\u3ACAr\xF2\u03CEr\xF2\u0A01\u0100cq\u3AD2\u17B8r;\uC000\u{1D4CD}\u0100pt\u17D6\u3ADCr\xE9\u17D4\u0400acefiosu\u3AF0\u3AFD\u3B08\u3B0C\u3B11\u3B15\u3B1B\u3B21c\u0100uy\u3AF6\u3AFBte\u803B\xFD\u40FD;\u444F\u0100iy\u3B02\u3B06rc;\u4177;\u444Bn\u803B\xA5\u40A5r;\uC000\u{1D536}cy;\u4457pf;\uC000\u{1D56A}cr;\uC000\u{1D4CE}\u0100cm\u3B26\u3B29y;\u444El\u803B\xFF\u40FF\u0500acdefhiosw\u3B42\u3B48\u3B54\u3B58\u3B64\u3B69\u3B6D\u3B74\u3B7A\u3B80cute;\u417A\u0100ay\u3B4D\u3B52ron;\u417E;\u4437ot;\u417C\u0100et\u3B5D\u3B61tr\xE6\u155Fa;\u43B6r;\uC000\u{1D537}cy;\u4436grarr;\u61DDpf;\uC000\u{1D56B}cr;\uC000\u{1D4CF}\u0100jn\u3B85\u3B87;\u600Dj;\u600C'.split("").map(t=>t.charCodeAt(0))),Qh=new Uint16Array("\u0200aglq	\x1B\u026D\0\0p;\u4026os;\u4027t;\u403Et;\u403Cuot;\u4022".split("").map(t=>t.charCodeAt(0))),vi,Zh=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),M0=(vi=String.fromCodePoint)!==null&&vi!==void 0?vi:function(t){let e="";return t>65535&&(t-=65536,e+=String.fromCharCode(t>>>10&1023|55296),t=56320|t&1023),e+=String.fromCharCode(t),e};function Kh(t){var e;return t>=55296&&t<=57343||t>1114111?65533:(e=Zh.get(t))!==null&&e!==void 0?e:t}var Je=function(t){return t[t.NUM=35]="NUM",t[t.SEMI=59]="SEMI",t[t.EQUALS=61]="EQUALS",t[t.ZERO=48]="ZERO",t[t.NINE=57]="NINE",t[t.LOWER_A=97]="LOWER_A",t[t.LOWER_F=102]="LOWER_F",t[t.LOWER_X=120]="LOWER_X",t[t.LOWER_Z=122]="LOWER_Z",t[t.UPPER_A=65]="UPPER_A",t[t.UPPER_F=70]="UPPER_F",t[t.UPPER_Z=90]="UPPER_Z",t}(Je||{}),Yh=32,Sr=function(t){return t[t.VALUE_LENGTH=49152]="VALUE_LENGTH",t[t.BRANCH_LENGTH=16256]="BRANCH_LENGTH",t[t.JUMP_TABLE=127]="JUMP_TABLE",t}(Sr||{});function Ti(t){return t>=Je.ZERO&&t<=Je.NINE}function Jh(t){return t>=Je.UPPER_A&&t<=Je.UPPER_F||t>=Je.LOWER_A&&t<=Je.LOWER_F}function ep(t){return t>=Je.UPPER_A&&t<=Je.UPPER_Z||t>=Je.LOWER_A&&t<=Je.LOWER_Z||Ti(t)}function tp(t){return t===Je.EQUALS||ep(t)}var Ye=function(t){return t[t.EntityStart=0]="EntityStart",t[t.NumericStart=1]="NumericStart",t[t.NumericDecimal=2]="NumericDecimal",t[t.NumericHex=3]="NumericHex",t[t.NamedEntity=4]="NamedEntity",t}(Ye||{}),Ot=function(t){return t[t.Legacy=0]="Legacy",t[t.Strict=1]="Strict",t[t.Attribute=2]="Attribute",t}(Ot||{}),Ni=class{constructor(e,r,n){this.decodeTree=e,this.emitCodePoint=r,this.errors=n,this.state=Ye.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=Ot.Strict}startEntity(e){this.decodeMode=e,this.state=Ye.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,r){switch(this.state){case Ye.EntityStart:return e.charCodeAt(r)===Je.NUM?(this.state=Ye.NumericStart,this.consumed+=1,this.stateNumericStart(e,r+1)):(this.state=Ye.NamedEntity,this.stateNamedEntity(e,r));case Ye.NumericStart:return this.stateNumericStart(e,r);case Ye.NumericDecimal:return this.stateNumericDecimal(e,r);case Ye.NumericHex:return this.stateNumericHex(e,r);case Ye.NamedEntity:return this.stateNamedEntity(e,r)}}stateNumericStart(e,r){return r>=e.length?-1:(e.charCodeAt(r)|Yh)===Je.LOWER_X?(this.state=Ye.NumericHex,this.consumed+=1,this.stateNumericHex(e,r+1)):(this.state=Ye.NumericDecimal,this.stateNumericDecimal(e,r))}addToNumericResult(e,r,n,u){if(r!==n){let a=n-r;this.result=this.result*Math.pow(u,a)+Number.parseInt(e.substr(r,a),u),this.consumed+=a}}stateNumericHex(e,r){let n=r;for(;r<e.length;){let u=e.charCodeAt(r);if(Ti(u)||Jh(u))r+=1;else return this.addToNumericResult(e,n,r,16),this.emitNumericEntity(u,3)}return this.addToNumericResult(e,n,r,16),-1}stateNumericDecimal(e,r){let n=r;for(;r<e.length;){let u=e.charCodeAt(r);if(Ti(u))r+=1;else return this.addToNumericResult(e,n,r,10),this.emitNumericEntity(u,2)}return this.addToNumericResult(e,n,r,10),-1}emitNumericEntity(e,r){var n;if(this.consumed<=r)return(n=this.errors)===null||n===void 0||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===Je.SEMI)this.consumed+=1;else if(this.decodeMode===Ot.Strict)return 0;return this.emitCodePoint(Kh(this.result),this.consumed),this.errors&&(e!==Je.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,r){let{decodeTree:n}=this,u=n[this.treeIndex],a=(u&Sr.VALUE_LENGTH)>>14;for(;r<e.length;r++,this.excess++){let i=e.charCodeAt(r);if(this.treeIndex=rp(n,u,this.treeIndex+Math.max(1,a),i),this.treeIndex<0)return this.result===0||this.decodeMode===Ot.Attribute&&(a===0||tp(i))?0:this.emitNotTerminatedNamedEntity();if(u=n[this.treeIndex],a=(u&Sr.VALUE_LENGTH)>>14,a!==0){if(i===Je.SEMI)return this.emitNamedEntityData(this.treeIndex,a,this.consumed+this.excess);this.decodeMode!==Ot.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;let{result:r,decodeTree:n}=this,u=(n[r]&Sr.VALUE_LENGTH)>>14;return this.emitNamedEntityData(r,u,this.consumed),(e=this.errors)===null||e===void 0||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,r,n){let{decodeTree:u}=this;return this.emitCodePoint(r===1?u[e]&~Sr.VALUE_LENGTH:u[e+1],n),r===3&&this.emitCodePoint(u[e+2],n),n}end(){var e;switch(this.state){case Ye.NamedEntity:return this.result!==0&&(this.decodeMode!==Ot.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case Ye.NumericDecimal:return this.emitNumericEntity(0,2);case Ye.NumericHex:return this.emitNumericEntity(0,3);case Ye.NumericStart:return(e=this.errors)===null||e===void 0||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case Ye.EntityStart:return 0}}};function rp(t,e,r,n){let u=(e&Sr.BRANCH_LENGTH)>>7,a=e&Sr.JUMP_TABLE;if(u===0)return a!==0&&n===a?r:-1;if(a){let l=n-a;return l<0||l>=u?-1:t[r+l]-1}let i=r,s=i+u-1;for(;i<=s;){let l=i+s>>>1,o=t[l];if(o<n)i=l+1;else if(o>n)s=l-1;else return t[l+u]}return-1}var oe=function(t){return t[t.Tab=9]="Tab",t[t.NewLine=10]="NewLine",t[t.FormFeed=12]="FormFeed",t[t.CarriageReturn=13]="CarriageReturn",t[t.Space=32]="Space",t[t.ExclamationMark=33]="ExclamationMark",t[t.Number=35]="Number",t[t.Amp=38]="Amp",t[t.SingleQuote=39]="SingleQuote",t[t.DoubleQuote=34]="DoubleQuote",t[t.Dash=45]="Dash",t[t.Slash=47]="Slash",t[t.Zero=48]="Zero",t[t.Nine=57]="Nine",t[t.Semi=59]="Semi",t[t.Lt=60]="Lt",t[t.Eq=61]="Eq",t[t.Gt=62]="Gt",t[t.Questionmark=63]="Questionmark",t[t.UpperA=65]="UpperA",t[t.LowerA=97]="LowerA",t[t.UpperF=70]="UpperF",t[t.LowerF=102]="LowerF",t[t.UpperZ=90]="UpperZ",t[t.LowerZ=122]="LowerZ",t[t.LowerX=120]="LowerX",t[t.OpeningSquareBracket=91]="OpeningSquareBracket",t}(oe||{}),G=function(t){return t[t.Text=1]="Text",t[t.BeforeTagName=2]="BeforeTagName",t[t.InTagName=3]="InTagName",t[t.InSelfClosingTag=4]="InSelfClosingTag",t[t.BeforeClosingTagName=5]="BeforeClosingTagName",t[t.InClosingTagName=6]="InClosingTagName",t[t.AfterClosingTagName=7]="AfterClosingTagName",t[t.BeforeAttributeName=8]="BeforeAttributeName",t[t.InAttributeName=9]="InAttributeName",t[t.AfterAttributeName=10]="AfterAttributeName",t[t.BeforeAttributeValue=11]="BeforeAttributeValue",t[t.InAttributeValueDq=12]="InAttributeValueDq",t[t.InAttributeValueSq=13]="InAttributeValueSq",t[t.InAttributeValueNq=14]="InAttributeValueNq",t[t.BeforeDeclaration=15]="BeforeDeclaration",t[t.InDeclaration=16]="InDeclaration",t[t.InProcessingInstruction=17]="InProcessingInstruction",t[t.BeforeComment=18]="BeforeComment",t[t.CDATASequence=19]="CDATASequence",t[t.InSpecialComment=20]="InSpecialComment",t[t.InCommentLike=21]="InCommentLike",t[t.BeforeSpecialS=22]="BeforeSpecialS",t[t.BeforeSpecialT=23]="BeforeSpecialT",t[t.SpecialStartSequence=24]="SpecialStartSequence",t[t.InSpecialTag=25]="InSpecialTag",t[t.InEntity=26]="InEntity",t}(G||{});function It(t){return t===oe.Space||t===oe.NewLine||t===oe.Tab||t===oe.FormFeed||t===oe.CarriageReturn}function vn(t){return t===oe.Slash||t===oe.Gt||It(t)}function up(t){return t>=oe.LowerA&&t<=oe.LowerZ||t>=oe.UpperA&&t<=oe.UpperZ}var Rt=function(t){return t[t.NoValue=0]="NoValue",t[t.Unquoted=1]="Unquoted",t[t.Single=2]="Single",t[t.Double=3]="Double",t}(Rt||{}),We={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97]),XmpEnd:new Uint8Array([60,47,120,109,112])},Ai=class{constructor({xmlMode:e=!1,decodeEntities:r=!0},n){this.cbs=n,this.state=G.Text,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=G.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.xmlMode=e,this.decodeEntities=r,this.entityDecoder=new Ni(e?Qh:Xh,(u,a)=>this.emitCodePoint(u,a))}reset(){this.state=G.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=G.Text,this.currentSequence=void 0,this.running=!0,this.offset=0}write(e){this.offset+=this.buffer.length,this.buffer=e,this.parse()}end(){this.running&&this.finish()}pause(){this.running=!1}resume(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()}stateText(e){e===oe.Lt||!this.decodeEntities&&this.fastForwardTo(oe.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=G.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&e===oe.Amp&&this.startEntity()}stateSpecialStartSequence(e){let r=this.sequenceIndex===this.currentSequence.length;if(!(r?vn(e):(e|32)===this.currentSequence[this.sequenceIndex]))this.isSpecial=!1;else if(!r){this.sequenceIndex++;return}this.sequenceIndex=0,this.state=G.InTagName,this.stateInTagName(e)}stateInSpecialTag(e){if(this.sequenceIndex===this.currentSequence.length){if(e===oe.Gt||It(e)){let r=this.index-this.currentSequence.length;if(this.sectionStart<r){let n=this.index;this.index=r,this.cbs.ontext(this.sectionStart,r),this.index=n}this.isSpecial=!1,this.sectionStart=r+2,this.stateInClosingTagName(e);return}this.sequenceIndex=0}(e|32)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:this.sequenceIndex===0?this.currentSequence===We.TitleEnd?this.decodeEntities&&e===oe.Amp&&this.startEntity():this.fastForwardTo(oe.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=+(e===oe.Lt)}stateCDATASequence(e){e===We.Cdata[this.sequenceIndex]?++this.sequenceIndex===We.Cdata.length&&(this.state=G.InCommentLike,this.currentSequence=We.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=G.InDeclaration,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===e)return!0;return this.index=this.buffer.length+this.offset-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===We.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=G.Text):this.sequenceIndex===0?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}isTagStartChar(e){return this.xmlMode?!vn(e):up(e)}startSpecial(e,r){this.isSpecial=!0,this.currentSequence=e,this.sequenceIndex=r,this.state=G.SpecialStartSequence}stateBeforeTagName(e){if(e===oe.ExclamationMark)this.state=G.BeforeDeclaration,this.sectionStart=this.index+1;else if(e===oe.Questionmark)this.state=G.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(e)){let r=e|32;this.sectionStart=this.index,this.xmlMode?this.state=G.InTagName:r===We.ScriptEnd[2]?this.state=G.BeforeSpecialS:r===We.TitleEnd[2]||r===We.XmpEnd[2]?this.state=G.BeforeSpecialT:this.state=G.InTagName}else e===oe.Slash?this.state=G.BeforeClosingTagName:(this.state=G.Text,this.stateText(e))}stateInTagName(e){vn(e)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=G.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateBeforeClosingTagName(e){It(e)||(e===oe.Gt?this.state=G.Text:(this.state=this.isTagStartChar(e)?G.InClosingTagName:G.InSpecialComment,this.sectionStart=this.index))}stateInClosingTagName(e){(e===oe.Gt||It(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=G.AfterClosingTagName,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){(e===oe.Gt||this.fastForwardTo(oe.Gt))&&(this.state=G.Text,this.sectionStart=this.index+1)}stateBeforeAttributeName(e){e===oe.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=G.InSpecialTag,this.sequenceIndex=0):this.state=G.Text,this.sectionStart=this.index+1):e===oe.Slash?this.state=G.InSelfClosingTag:It(e)||(this.state=G.InAttributeName,this.sectionStart=this.index)}stateInSelfClosingTag(e){e===oe.Gt?(this.cbs.onselfclosingtag(this.index),this.state=G.Text,this.sectionStart=this.index+1,this.isSpecial=!1):It(e)||(this.state=G.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateInAttributeName(e){(e===oe.Eq||vn(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=this.index,this.state=G.AfterAttributeName,this.stateAfterAttributeName(e))}stateAfterAttributeName(e){e===oe.Eq?this.state=G.BeforeAttributeValue:e===oe.Slash||e===oe.Gt?(this.cbs.onattribend(Rt.NoValue,this.sectionStart),this.sectionStart=-1,this.state=G.BeforeAttributeName,this.stateBeforeAttributeName(e)):It(e)||(this.cbs.onattribend(Rt.NoValue,this.sectionStart),this.state=G.InAttributeName,this.sectionStart=this.index)}stateBeforeAttributeValue(e){e===oe.DoubleQuote?(this.state=G.InAttributeValueDq,this.sectionStart=this.index+1):e===oe.SingleQuote?(this.state=G.InAttributeValueSq,this.sectionStart=this.index+1):It(e)||(this.sectionStart=this.index,this.state=G.InAttributeValueNq,this.stateInAttributeValueNoQuotes(e))}handleInAttributeValue(e,r){e===r||!this.decodeEntities&&this.fastForwardTo(r)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(r===oe.DoubleQuote?Rt.Double:Rt.Single,this.index+1),this.state=G.BeforeAttributeName):this.decodeEntities&&e===oe.Amp&&this.startEntity()}stateInAttributeValueDoubleQuotes(e){this.handleInAttributeValue(e,oe.DoubleQuote)}stateInAttributeValueSingleQuotes(e){this.handleInAttributeValue(e,oe.SingleQuote)}stateInAttributeValueNoQuotes(e){It(e)||e===oe.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(Rt.Unquoted,this.index),this.state=G.BeforeAttributeName,this.stateBeforeAttributeName(e)):this.decodeEntities&&e===oe.Amp&&this.startEntity()}stateBeforeDeclaration(e){e===oe.OpeningSquareBracket?(this.state=G.CDATASequence,this.sequenceIndex=0):this.state=e===oe.Dash?G.BeforeComment:G.InDeclaration}stateInDeclaration(e){(e===oe.Gt||this.fastForwardTo(oe.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=G.Text,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(e===oe.Gt||this.fastForwardTo(oe.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=G.Text,this.sectionStart=this.index+1)}stateBeforeComment(e){e===oe.Dash?(this.state=G.InCommentLike,this.currentSequence=We.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=G.InDeclaration}stateInSpecialComment(e){(e===oe.Gt||this.fastForwardTo(oe.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=G.Text,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){let r=e|32;r===We.ScriptEnd[3]?this.startSpecial(We.ScriptEnd,4):r===We.StyleEnd[3]?this.startSpecial(We.StyleEnd,4):(this.state=G.InTagName,this.stateInTagName(e))}stateBeforeSpecialT(e){switch(e|32){case We.TitleEnd[3]:{this.startSpecial(We.TitleEnd,4);break}case We.TextareaEnd[3]:{this.startSpecial(We.TextareaEnd,4);break}case We.XmpEnd[3]:{this.startSpecial(We.XmpEnd,4);break}default:this.state=G.InTagName,this.stateInTagName(e)}}startEntity(){this.baseState=this.state,this.state=G.InEntity,this.entityStart=this.index,this.entityDecoder.startEntity(this.xmlMode?Ot.Strict:this.baseState===G.Text||this.baseState===G.InSpecialTag?Ot.Legacy:Ot.Attribute)}stateInEntity(){let e=this.entityDecoder.write(this.buffer,this.index-this.offset);e>=0?(this.state=this.baseState,e===0&&(this.index=this.entityStart)):this.index=this.offset+this.buffer.length-1}cleanup(){this.running&&this.sectionStart!==this.index&&(this.state===G.Text||this.state===G.InSpecialTag&&this.sequenceIndex===0?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):(this.state===G.InAttributeValueDq||this.state===G.InAttributeValueSq||this.state===G.InAttributeValueNq)&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}shouldContinue(){return this.index<this.buffer.length+this.offset&&this.running}parse(){for(;this.shouldContinue();){let e=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case G.Text:{this.stateText(e);break}case G.SpecialStartSequence:{this.stateSpecialStartSequence(e);break}case G.InSpecialTag:{this.stateInSpecialTag(e);break}case G.CDATASequence:{this.stateCDATASequence(e);break}case G.InAttributeValueDq:{this.stateInAttributeValueDoubleQuotes(e);break}case G.InAttributeName:{this.stateInAttributeName(e);break}case G.InCommentLike:{this.stateInCommentLike(e);break}case G.InSpecialComment:{this.stateInSpecialComment(e);break}case G.BeforeAttributeName:{this.stateBeforeAttributeName(e);break}case G.InTagName:{this.stateInTagName(e);break}case G.InClosingTagName:{this.stateInClosingTagName(e);break}case G.BeforeTagName:{this.stateBeforeTagName(e);break}case G.AfterAttributeName:{this.stateAfterAttributeName(e);break}case G.InAttributeValueSq:{this.stateInAttributeValueSingleQuotes(e);break}case G.BeforeAttributeValue:{this.stateBeforeAttributeValue(e);break}case G.BeforeClosingTagName:{this.stateBeforeClosingTagName(e);break}case G.AfterClosingTagName:{this.stateAfterClosingTagName(e);break}case G.BeforeSpecialS:{this.stateBeforeSpecialS(e);break}case G.BeforeSpecialT:{this.stateBeforeSpecialT(e);break}case G.InAttributeValueNq:{this.stateInAttributeValueNoQuotes(e);break}case G.InSelfClosingTag:{this.stateInSelfClosingTag(e);break}case G.InDeclaration:{this.stateInDeclaration(e);break}case G.BeforeDeclaration:{this.stateBeforeDeclaration(e);break}case G.BeforeComment:{this.stateBeforeComment(e);break}case G.InProcessingInstruction:{this.stateInProcessingInstruction(e);break}case G.InEntity:{this.stateInEntity();break}}this.index++}this.cleanup()}finish(){this.state===G.InEntity&&(this.entityDecoder.end(),this.state=this.baseState),this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){let e=this.buffer.length+this.offset;this.sectionStart>=e||(this.state===G.InCommentLike?this.currentSequence===We.CdataEnd?this.cbs.oncdata(this.sectionStart,e,0):this.cbs.oncomment(this.sectionStart,e,0):this.state===G.InTagName||this.state===G.BeforeAttributeName||this.state===G.BeforeAttributeValue||this.state===G.AfterAttributeName||this.state===G.InAttributeName||this.state===G.InAttributeValueSq||this.state===G.InAttributeValueDq||this.state===G.InAttributeValueNq||this.state===G.InClosingTagName||this.cbs.ontext(this.sectionStart,e))}emitCodePoint(e,r){this.baseState!==G.Text&&this.baseState!==G.InSpecialTag?(this.sectionStart<this.entityStart&&this.cbs.onattribdata(this.sectionStart,this.entityStart),this.sectionStart=this.entityStart+r,this.index=this.sectionStart-1,this.cbs.onattribentity(e)):(this.sectionStart<this.entityStart&&this.cbs.ontext(this.sectionStart,this.entityStart),this.sectionStart=this.entityStart+r,this.index=this.sectionStart-1,this.cbs.ontextentity(e,this.sectionStart))}},Hr=new Set(["input","option","optgroup","select","button","datalist","textarea"]),Ie=new Set(["p"]),D0=new Set(["thead","tbody"]),I0=new Set(["dd","dt"]),O0=new Set(["rt","rp"]),np=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",Ie],["h1",Ie],["h2",Ie],["h3",Ie],["h4",Ie],["h5",Ie],["h6",Ie],["select",Hr],["input",Hr],["output",Hr],["button",Hr],["datalist",Hr],["textarea",Hr],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",I0],["dt",I0],["address",Ie],["article",Ie],["aside",Ie],["blockquote",Ie],["details",Ie],["div",Ie],["dl",Ie],["fieldset",Ie],["figcaption",Ie],["figure",Ie],["footer",Ie],["form",Ie],["header",Ie],["hr",Ie],["main",Ie],["nav",Ie],["ol",Ie],["pre",Ie],["section",Ie],["table",Ie],["ul",Ie],["rt",O0],["rp",O0],["tbody",D0],["tfoot",D0]]),ap=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),R0=new Set(["math","svg"]),q0=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),ip=/\s|\//,Ci=class{constructor(e,r={}){var n,u,a,i,s,l;this.options=r,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=e??{},this.htmlMode=!this.options.xmlMode,this.lowerCaseTagNames=(n=r.lowerCaseTags)!==null&&n!==void 0?n:this.htmlMode,this.lowerCaseAttributeNames=(u=r.lowerCaseAttributeNames)!==null&&u!==void 0?u:this.htmlMode,this.recognizeSelfClosing=(a=r.recognizeSelfClosing)!==null&&a!==void 0?a:!this.htmlMode,this.tokenizer=new((i=r.Tokenizer)!==null&&i!==void 0?i:Ai)(this.options,this),this.foreignContext=[!this.htmlMode],(l=(s=this.cbs).onparserinit)===null||l===void 0||l.call(s,this)}ontext(e,r){var n,u;let a=this.getSlice(e,r);this.endIndex=r-1,(u=(n=this.cbs).ontext)===null||u===void 0||u.call(n,a),this.startIndex=r}ontextentity(e,r){var n,u;this.endIndex=r-1,(u=(n=this.cbs).ontext)===null||u===void 0||u.call(n,M0(e)),this.startIndex=r}isVoidElement(e){return this.htmlMode&&ap.has(e)}onopentagname(e,r){this.endIndex=r;let n=this.getSlice(e,r);this.lowerCaseTagNames&&(n=n.toLowerCase()),this.emitOpenTag(n)}emitOpenTag(e){var r,n,u,a;this.openTagStart=this.startIndex,this.tagname=e;let i=this.htmlMode&&np.get(e);if(i)for(;this.stack.length>0&&i.has(this.stack[0]);){let s=this.stack.shift();(n=(r=this.cbs).onclosetag)===null||n===void 0||n.call(r,s,!0)}this.isVoidElement(e)||(this.stack.unshift(e),this.htmlMode&&(R0.has(e)?this.foreignContext.unshift(!0):q0.has(e)&&this.foreignContext.unshift(!1))),(a=(u=this.cbs).onopentagname)===null||a===void 0||a.call(u,e),this.cbs.onopentag&&(this.attribs={})}endOpenTag(e){var r,n;this.startIndex=this.openTagStart,this.attribs&&((n=(r=this.cbs).onopentag)===null||n===void 0||n.call(r,this.tagname,this.attribs,e),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""}onopentagend(e){this.endIndex=e,this.endOpenTag(!1),this.startIndex=e+1}onclosetag(e,r){var n,u,a,i,s,l,o,c;this.endIndex=r;let d=this.getSlice(e,r);if(this.lowerCaseTagNames&&(d=d.toLowerCase()),this.htmlMode&&(R0.has(d)||q0.has(d))&&this.foreignContext.shift(),this.isVoidElement(d))this.htmlMode&&d==="br"&&((i=(a=this.cbs).onopentagname)===null||i===void 0||i.call(a,"br"),(l=(s=this.cbs).onopentag)===null||l===void 0||l.call(s,"br",{},!0),(c=(o=this.cbs).onclosetag)===null||c===void 0||c.call(o,"br",!1));else{let f=this.stack.indexOf(d);if(f!==-1)for(let y=0;y<=f;y++){let v=this.stack.shift();(u=(n=this.cbs).onclosetag)===null||u===void 0||u.call(n,v,y!==f)}else this.htmlMode&&d==="p"&&(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=r+1}onselfclosingtag(e){this.endIndex=e,this.recognizeSelfClosing||this.foreignContext[0]?(this.closeCurrentTag(!1),this.startIndex=e+1):this.onopentagend(e)}closeCurrentTag(e){var r,n;let u=this.tagname;this.endOpenTag(e),this.stack[0]===u&&((n=(r=this.cbs).onclosetag)===null||n===void 0||n.call(r,u,!e),this.stack.shift())}onattribname(e,r){this.startIndex=e;let n=this.getSlice(e,r);this.attribname=this.lowerCaseAttributeNames?n.toLowerCase():n}onattribdata(e,r){this.attribvalue+=this.getSlice(e,r)}onattribentity(e){this.attribvalue+=M0(e)}onattribend(e,r){var n,u;this.endIndex=r,(u=(n=this.cbs).onattribute)===null||u===void 0||u.call(n,this.attribname,this.attribvalue,e===Rt.Double?'"':e===Rt.Single?"'":e===Rt.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""}getInstructionName(e){let r=e.search(ip),n=r<0?e:e.substr(0,r);return this.lowerCaseTagNames&&(n=n.toLowerCase()),n}ondeclaration(e,r){this.endIndex=r;let n=this.getSlice(e,r);if(this.cbs.onprocessinginstruction){let u=this.getInstructionName(n);this.cbs.onprocessinginstruction(`!${u}`,`!${n}`)}this.startIndex=r+1}onprocessinginstruction(e,r){this.endIndex=r;let n=this.getSlice(e,r);if(this.cbs.onprocessinginstruction){let u=this.getInstructionName(n);this.cbs.onprocessinginstruction(`?${u}`,`?${n}`)}this.startIndex=r+1}oncomment(e,r,n){var u,a,i,s;this.endIndex=r,(a=(u=this.cbs).oncomment)===null||a===void 0||a.call(u,this.getSlice(e,r-n)),(s=(i=this.cbs).oncommentend)===null||s===void 0||s.call(i),this.startIndex=r+1}oncdata(e,r,n){var u,a,i,s,l,o,c,d,f,y;this.endIndex=r;let v=this.getSlice(e,r-n);!this.htmlMode||this.options.recognizeCDATA?((a=(u=this.cbs).oncdatastart)===null||a===void 0||a.call(u),(s=(i=this.cbs).ontext)===null||s===void 0||s.call(i,v),(o=(l=this.cbs).oncdataend)===null||o===void 0||o.call(l)):((d=(c=this.cbs).oncomment)===null||d===void 0||d.call(c,`[CDATA[${v}]]`),(y=(f=this.cbs).oncommentend)===null||y===void 0||y.call(f)),this.startIndex=r+1}onend(){var e,r;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(let n=0;n<this.stack.length;n++)this.cbs.onclosetag(this.stack[n],!0)}(r=(e=this.cbs).onend)===null||r===void 0||r.call(e)}reset(){var e,r,n,u;(r=(e=this.cbs).onreset)===null||r===void 0||r.call(e),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,(u=(n=this.cbs).onparserinit)===null||u===void 0||u.call(n,this),this.buffers.length=0,this.foreignContext.length=0,this.foreignContext.unshift(!this.htmlMode),this.bufferOffset=0,this.writeIndex=0,this.ended=!1}parseComplete(e){this.reset(),this.end(e)}getSlice(e,r){for(;e-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();let n=this.buffers[0].slice(e-this.bufferOffset,r-this.bufferOffset);for(;r-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),n+=this.buffers[0].slice(0,r-this.bufferOffset);return n}shiftBuffer(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()}write(e){var r,n;if(this.ended){(n=(r=this.cbs).onerror)===null||n===void 0||n.call(r,new Error(".write() after done!"));return}this.buffers.push(e),this.tokenizer.running&&(this.tokenizer.write(e),this.writeIndex++)}end(e){var r,n;if(this.ended){(n=(r=this.cbs).onerror)===null||n===void 0||n.call(r,new Error(".end() after done!"));return}e&&this.write(e),this.ended=!0,this.tokenizer.end()}pause(){this.tokenizer.pause()}resume(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()}parseChunk(e){this.write(e)}done(e){this.end(e)}};function sp(t,e){let r=new wi(void 0,e);return new Ci(r,e).end(t),r.root}var op=K0(),cu=Cn(op);function P0(t){return d1(t)}function H0(t,e){let r=[];return l1(t,(n,u,a)=>{if(!(u?.type==="decl"&&u.value.includes("</style>"))){if(!e.compress){r.push(n);return}if(u?.type!=="comment"){if(u?.type==="decl"){let i=u.prop+u.raws.between;r.push(n.replace(i,i.trim()));return}if(a==="start"){u?.type==="rule"&&u.selectors?u.selectors.length===1?r.push(u.selectors[0]??"","{"):r.push(u.selectors.join(","),"{"):r.push(n.trim());return}if(a==="end"&&n==="}"&&u?.raws?.semicolon){let i=r.length-2;i>=0&&r[i]&&(r[i]=r[i].slice(0,-1))}r.push(n.trim())}}}),r.join("")}function cp(t){return e=>{let r="selectors"in e?e.selectors:void 0;t(e)===!1&&(e.$$remove=!0),"selectors"in e&&(e.$$markedSelectors=e.selectors,e.selectors=r),e._other&&(e._other.$$markedSelectors=e._other.selectors)}}function Ml(t){t.$$markedSelectors&&(t.selectors=t.$$markedSelectors),t._other&&Ml(t._other)}function Xi(t,e){"nodes"in t&&(t.nodes=t.nodes?.filter(r=>(Il(r)&&Xi(r,e),r._other=void 0,r.filterSelectors=Rl,e(r)!==!1)))}function Dl(t,e,r){if(!e)return Xi(t,r);[t.nodes,e.nodes]=Ol(t.nodes,e.nodes,(n,u,a,i)=>{let s=i?.[u];return Il(n)&&Dl(n,s,r),n._other=s,n.filterSelectors=Rl,r(n)!==!1})}function Il(t){return"nodes"in t&&!!t.nodes?.length&&(!("name"in t)||t.name!=="keyframes"&&t.name!=="-webkit-keyframes")&&t.nodes.some(e=>e.type==="rule"||e.type==="atrule")}function Ol(t,e,r){let n=[],u=[];for(let a=0;a<t.length;a++){let i=t[a];r(i,a,t,e)?n.push(i):u.push(i)}return[n,u]}function Rl(t){if(this._other){let[e,r]=Ol(this.selectors,this._other.selectors,t);this.selectors=e,this._other.selectors=r}else this.selectors=this.selectors.filter(t)}var lp=new Set(["all","print","screen","speech"]),dp=new Set(["and","not",","]),fp=new Set(["width","aspect-ratio","color","color-index","grid","height","monochrome","orientation","resolution","scan"].flatMap(t=>[t,`min-${t}`,`max-${t}`]));function hp(t){let{type:e,value:r}=t;if(e==="media-type")return lp.has(r);if(e==="keyword")return dp.has(r);if(e==="media-feature")return fp.has(r)}function pp(t){let r=("default"in ci?ci.default:ci)(t),n=new Set(["media-type","keyword","media-feature"]),u=[r];for(;u.length>0;){let a=u.pop();if(n.has(a.type)&&!hp(a))return!1;a.nodes&&u.push(...a.nodes)}return!0}var ki=null,Li=null;function mp(t){ki=new Set,Li=new Set;let e=[t];for(;e.length;){let r=e.shift();if(r.hasAttribute?.("class")&&r.getAttribute("class").trim().split(" ").forEach(u=>{ki.add(u)}),r.hasAttribute?.("id")){let n=r.getAttribute("id").trim();Li.add(n)}"children"in r&&e.push(...r.children.filter(n=>n.type==="tag"))}}function bp(t){let e=sp(t,{decodeEntities:!1});yp(e),vp(Fr.prototype);let r=e.querySelector("[data-beasties-container]");return r||(e.documentElement?.setAttribute("data-beasties-container",""),r=e.documentElement||e),e.beastiesContainer=r,mp(r),e}function gp(t){return Rn(t,{decodeEntities:!1})}var B0=!1;function vp(t){B0||(B0=!0,Object.defineProperties(t,{nodeName:{get(){return this.tagName.toUpperCase()}},id:{get(){return this.getAttribute("id")},set(e){this.setAttribute("id",e)}},className:{get(){return this.getAttribute("class")},set(e){this.setAttribute("class",e)}},insertBefore:{value(e,r){return r?(pl(r,e),e):this.appendChild(e)}},appendChild:{value(e){return _i(this,e),e}},removeChild:{value(e){Cr(e)}},remove:{value(){Cr(this)}},textContent:{get(){return lu(this)},set(e){this.children=[],_i(this,new Nr(e))}},setAttribute:{value(e,r){this.attribs==null&&(this.attribs={}),r==null&&(r=""),this.attribs[e]=r}},removeAttribute:{value(e){this.attribs!=null&&delete this.attribs[e]}},getAttribute:{value(e){return this.attribs!=null&&this.attribs[e]}},hasAttribute:{value(e){return this.attribs!=null&&this.attribs[e]!=null}},getAttributeNode:{value(e){let r=this.getAttribute(e);if(r!=null)return{specified:!0,value:r}}},exists:{value(e){return ql(e,this)}},querySelector:{value(e){return Wi(e,this)}},querySelectorAll:{value(e){return Ll(e,this)}}}))}function yp(t){Object.defineProperties(t,{nodeType:{get(){return 9}},contentType:{get(){return"text/html"}},nodeName:{get(){return"#document"}},documentElement:{get(){return this.children.find(e=>"tagName"in e&&String(e.tagName).toLowerCase()==="html")}},head:{get(){return this.querySelector("head")}},body:{get(){return this.querySelector("body")}},createElement:{value(e){return new Fr(e,{})}},createTextNode:{value(e){return new Nr(e)}},exists:{value(e){return ql(e,this)}},querySelector:{value(e){return Wi(e,this)}},querySelectorAll:{value(e){return e===":root"?this:Ll(e,this)}}})}var F0=new Map;function ql(t,e){let r=F0.get(t);if(r===void 0&&(r=wp(t),F0.set(t,r)),r)for(let n of r){if(n.name==="class")return ki.has(n.value);if(n.name==="id")return Li.has(n.value)}return!!Wi(t,e)}function wp(t){let e=Gi(t),r=[];for(let n=0;n<e.length;n++){let u=e[n];if(u?.length!==1)continue;let a=u[0];a?.type==="attribute"&&(a.name==="class"||a.name==="id")&&r.push(a)}return r.length>0?r:null}var U0=["trace","debug","info","warn","error","silent"],V0={trace(t){console.trace(t)},debug(t){console.debug(t)},warn(t){console.warn(cu.yellow(t))},error(t){console.error(cu.bold(cu.red(t)))},info(t){console.info(cu.bold(cu.blue(t)))},silent(){}};function xp(t){let e=U0.indexOf(t);return U0.reduce((r,n,u)=>(u>=e?r[n]=V0[n]:r[n]=V0.silent,r),{})}function _p(t,e){return!Br.relative(t,e).startsWith("..")}var Ep=/(?<!\\)::?[a-z-]+(?:\(.+\))?/gi,Sp=/>\s*(?=>|$)/g,Tp=/\(\s*,|,\s*\)/g,Nn=class{#e=new Map;options;logger;fs;constructor(e={}){this.options=Object.assign({logLevel:"info",path:"",publicPath:"",reduceInlineStyles:!0,pruneSource:!1,additionalStylesheets:[],allowRules:[]},e),this.logger=this.options.logger||xp(this.options.logLevel)}readFile(e){let r=this.fs;return new Promise((n,u)=>{let a=(i,s)=>{i?u(i):n(s.toString())};r&&r.readFile?r.readFile(e,a):Gf(e,"utf-8",a)})}process(e){return ze(this,null,function*(){let r=Date.now(),n=bp(e);if(this.options.additionalStylesheets.length>0&&(yield this.embedAdditionalStylesheet(n)),this.options.external!==!1){let s=[...n.querySelectorAll('link[rel="stylesheet"]')];yield Promise.all(s.map(l=>this.embedLinkedStylesheet(l,n)))}let u=this.getAffectedStyleTags(n);for(let s of u)this.processStyle(s,n);this.options.mergeStylesheets!==!1&&u.length!==0&&this.mergeStylesheets(n);let a=gp(n),i=Date.now();return this.logger.info?.(`Time ${i-r}ms`),a})}getAffectedStyleTags(e){let r=[...e.querySelectorAll("style")];return this.options.reduceInlineStyles===!1?r.filter(n=>n.$$external):r}mergeStylesheets(e){let r=this.getAffectedStyleTags(e);if(r.length===0){this.logger.warn?.("Merging inline stylesheets into a single <style> tag skipped, no inline stylesheets to merge");return}let n=r[0],u=n.textContent;for(let a=1;a<r.length;a++){let i=r[a];u+=i.textContent,i.remove()}n.textContent=u}getCssAsset(e,r){return ze(this,null,function*(){let n=this.options.path,u=this.options.publicPath,a=e.replace(/^\//,""),i=`${(u||"").replace(/(^\/|\/$)/g,"")}/`;if(a.startsWith(i)&&(a=a.substring(i.length).replace(/^\//,"")),/^https?:\/\//.test(a)||e.startsWith("//"))return;let s=Br.resolve(n,a);if(!_p(n,s))return;let l;try{l=yield this.readFile(s)}catch{this.logger.warn?.(`Unable to locate stylesheet: ${s}`)}return l})}checkInlineThreshold(e,r,n){if(this.options.inlineThreshold&&n.length<this.options.inlineThreshold){let u=r.$$name;return r.$$reduce=!1,this.logger.info?.(`\x1B[32mInlined all of ${u} (${n.length} was below the threshold of ${this.options.inlineThreshold})\x1B[39m`),e.remove(),!0}return!1}embedAdditionalStylesheet(e){return ze(this,null,function*(){let r=[],n=yield Promise.all(this.options.additionalStylesheets.map(u=>{if(r.includes(u))return[];r.push(u);let a=e.createElement("style");return a.$$external=!0,this.getCssAsset(u,a).then(i=>[i,a])}));for(let[u,a]of n)u&&(a.textContent=u,e.head.appendChild(a))})}embedLinkedStylesheet(e,r){return ze(this,null,function*(){let n=e.getAttribute("href");if(!n?.endsWith(".css"))return;let u=r.createElement("style");u.$$external=!0;let a=yield this.getCssAsset(n,u);if(!a||(u.textContent=a,u.$$name=n,u.$$links=[e],e.parentNode?.insertBefore(u,e),this.checkInlineThreshold(e,u,a)))return;let i=e.getAttribute("media");i&&!pp(i)&&(i=void 0);let s=this.options.preload,l="function $loadcss(u,m,l){(l=document.createElement('link')).rel='stylesheet';l.href=u;document.head.appendChild(l)}";if(s==="js-lazy"&&(l=l.replace("l.href","l.media='print';l.onload=function(){l.media=m};l.href")),s===!1)return;let c=!1,d=!1,f=e.cloneNode(!1);if(s==="body")r.body.appendChild(e);else if(s==="js"||s==="js-lazy"){let y=r.createElement("script");y.setAttribute("data-href",n),y.setAttribute("data-media",i||"all");let v=`${l}$loadcss(document.currentScript.dataset.href,document.currentScript.dataset.media)`;y.textContent=v,e.parentNode.insertBefore(y,e.nextSibling),u.$$links.push(y),l="",c=!0,d=!0}else if(s==="media")e.setAttribute("media","print"),e.setAttribute("onload",`this.media='${i||"all"}'`),c=!0;else if(s==="swap-high")e.setAttribute("rel","alternate stylesheet preload"),e.setAttribute("title","styles"),e.setAttribute("onload","this.title='';this.rel='stylesheet'"),c=!0;else if(s==="swap-low")e.setAttribute("rel","alternate stylesheet"),e.setAttribute("title","styles"),e.setAttribute("onload","this.title='';this.rel='stylesheet'"),c=!0;else if(s==="swap")e.setAttribute("onload","this.rel='stylesheet'"),d=!0,c=!0;else{let y=e.cloneNode(!1);y.removeAttribute("id"),r.body.appendChild(y),u.$$links.push(y),d=!0}if(this.options.noscriptFallback!==!1&&c&&!n.includes("</noscript>")){let y=r.createElement("noscript");f.removeAttribute("id"),y.appendChild(f),e.parentNode.insertBefore(y,e.nextSibling),u.$$links.push(y)}d&&(e.setAttribute("rel","preload"),e.setAttribute("as","style"))})}pruneSource(e,r,n){let u=this.options.minimumExternalSize,a=e.$$name,i=u&&n.length<u;if(i&&this.logger.info?.(`\x1B[32mInlined all of ${a} (non-critical external stylesheet would have been ${n.length}b, which was below the threshold of ${u})\x1B[39m`),(i||!n)&&(e.textContent=r,e.$$links))for(let s of e.$$links)s.parentNode?.removeChild(s);return!!i}processStyle(e,r){if(e.$$reduce===!1)return;let n=e.$$name?e.$$name.replace(/^\//,""):"inline CSS",u=this.options,a=r.beastiesContainer,i=u.keyframes??"critical";i===!0&&(i="all"),i===!1&&(i="none");let s=e.textContent,l=s;if(!s)return;let o=P0(s),c=u.pruneSource?P0(s):null,d="",f=[],y=new Set,v=!1,w=!1,A=!1,S=!1,_=u.fonts===!0||u.preloadFonts===!0,x=u.fonts!==!1&&u.inlineFonts===!0;Xi(o,cp(F=>{if(F.type==="comment"){let C=F.text.match(/^(?<!! )beasties:(.*)/),P=C&&C[1];if(P)switch(P){case"include":v=!0;break;case"exclude":A=!0;break;case"include start":w=!0;break;case"include end":w=!1;break;case"exclude start":S=!0;break;case"exclude end":S=!1;break}}if(F.type==="rule"){if(v)return v=!1,!0;if(A)return A=!1,!1;if(w)return!0;if(S||(F.filterSelectors?.(C=>{if(u.allowRules.some(W=>W instanceof RegExp?W.test(C):W===C)||C===":root"||C==="html"||C==="body"||C[0]===":"&&/^::?(?:before|after)$/.test(C))return!0;if(C=this.normalizeCssSelector(C),!C)return!1;try{return a.exists(C)}catch(W){return f.push(`${C} -> ${W.message||W.toString()}`),!1}}),!F.selector))return!1;if(F.nodes){for(let C of F.nodes)if("prop"in C&&(x&&/\bfont(?:-family)?\b/i.test(C.prop)&&(d+=` ${C.value}`),C.prop==="animation"||C.prop==="animation-name"))for(let P of C.value.split(/\s+/)){let W=P.trim();W&&y.add(W)}}}return F.type==="atrule"&&(F.name==="font-face"||F.name==="layer")?void 0:("nodes"in F&&F.nodes?.some(C=>!C.$$remove))??!0})),f.length!==0&&this.logger.warn?.(`${f.length} rules skipped due to selector errors:
  ${f.join(`
  `)}`);let T=new Set;if(Dl(o,c,F=>{if(F.$$remove===!0)return!1;if("selectors"in F&&Ml(F),F.type==="atrule"&&F.name==="keyframes")return i==="none"?!1:i==="all"?!0:y.has(F.params);if(F.type==="atrule"&&F.name==="font-face"){let q,C;if(F.nodes){for(let P of F.nodes)"prop"in P&&(P.prop==="src"?C=(P.value.match(/url\s*\(\s*(['"]?)(.+?)\1\s*\)/)||[])[2]:P.prop==="font-family"&&(q=P.value));if(C&&_&&!T.has(C)){T.add(C);let P=r.createElement("link");P.setAttribute("rel","preload"),P.setAttribute("as","font"),P.setAttribute("crossorigin","anonymous"),P.setAttribute("href",C.trim()),r.head.appendChild(P)}}if(!x||!q||!C||!d.includes(q))return!1}}),s=H0(o,{compress:this.options.compress!==!1}),s.trim().length===0){e.parentNode&&e.remove();return}let E="",O=!1;if(u.pruneSource){let F=H0(c,{compress:this.options.compress!==!1});O=this.pruneSource(e,l,F),O&&(E=`, reducing non-inlined size ${F.length/l.length*100|0}% to ${yi(F.length)}`)}O||(e.textContent=s);let B=s.length/l.length*100|0;this.logger.info?.(`\x1B[32mInlined ${yi(s.length)} (${B}% of original ${yi(l.length)}) of ${n}${E}.\x1B[39m`)}normalizeCssSelector(e){let r=this.#e.get(e);return r!==void 0||(r=e.replace(Ep,"").replace(Tp,n=>n.includes("(")?"(":")").replace(Sp,"> *").trim(),this.#e.set(e,r)),r}};function yi(t){if(t<=0)return"0 bytes";let e=["bytes","kB","MB","GB"],r=Math.floor(Math.log(t)/Math.log(1024)),n=t/1024**r,u=r===0?0:2;return`${n.toFixed(u)} ${e[r]}`}var qn=class{manifest;constructor(e){this.manifest=e}getServerAsset(e){let r=this.manifest.assets[e];if(!r)throw new Error(`Server asset '${e}' does not exist.`);return r}hasServerAsset(e){return!!this.manifest.assets[e]}getIndexServerHtml(){return this.getServerAsset("index.server.html")}},Np=new Set(["Angular is running in development mode."]),Pn=class extends Zu{log(e){Np.has(e)||super.log(e)}},Qi;function zm(t){Qi=t}function zl(){if(!Qi)throw new Error("Angular app manifest is not set. Please ensure you are using the '@angular/build:application' builder to build your server application.");return Qi}function Lr(t){return t.length>1&&t[0]==="/"?t.slice(1):t}function Gl(t){return t[0]==="/"?t:`/${t}`}function Ap(t){return t[t.length-1]==="/"?t:`${t}/`}function Mr(...t){let e=[];for(let r of t){if(r==="")continue;let n=r;r[0]==="/"&&(n=n.slice(1)),r[r.length-1]==="/"&&(n=n.slice(0,-1)),n!==""&&e.push(n)}return Gl(e.join("/"))}function $l(t){if(t.pathname.endsWith("/index.html")){let e=new URL(t);return e.pathname=e.pathname.slice(0,-11),e}return t}function Cp(t,e){if(t[0]!=="/")throw new Error(`Invalid toPath: The string must start with a '/'. Received: '${t}'`);if(e[0]!=="/")throw new Error(`Invalid fromPath: The string must start with a '/'. Received: '${e}'`);if(!t.includes("/*"))return t;let r=e.split("/"),n=t.split("/"),u=n.map((a,i)=>n[i]==="*"?r[i]:a);return Mr(...u)}function kp(t,e,r,n,u){let a=[{provide:Ra,useValue:u},{provide:Zu,useFactory:()=>new Pn},...n],i=$l(r).toString();return Wl(e)?Bc(e,{url:i,document:t,extraProviders:a}):Fc(e,{url:i,document:t,platformProviders:a})}function Wl(t){return"\u0275mod"in t}function Xl(t,e,r){return new Promise((n,u)=>{let a=()=>{u(new DOMException(`${r} was aborted.
${e.reason}`,"AbortError"))};if(e.aborted){a();return}e.addEventListener("abort",a,{once:!0}),t.then(n).catch(u).finally(()=>{e.removeEventListener("abort",a)})})}var Xe=function(t){return t[t.Server=0]="Server",t[t.Client=1]="Client",t[t.Prerender=2]="Prerender",t}(Xe||{}),Zi=function(t){return t[t.Server=0]="Server",t[t.Client=1]="Client",t[t.None=2]="None",t}(Zi||{}),Lp=new St("SERVER_ROUTES_CONFIG");var gu=class t{root=this.createEmptyRouteTreeNode();insert(e,r){let n=this.root,u=this.getPathSegments(e),a=[];for(let i of u){let s=i[0]===":"?"*":i,l=n.children.get(s);l||(l=this.createEmptyRouteTreeNode(),n.children.set(s,l)),n=l,a.push(s)}n.metadata=je(ye({},r),{route:Gl(a.join("/"))})}match(e){let r=this.getPathSegments(e);return this.traverseBySegments(r)?.metadata}toObject(){return Array.from(this.traverse())}static fromObject(e){let r=new t;for(let n of e){let u=n,{route:a}=u,i=Yt(u,["route"]);r.insert(a,i)}return r}*traverse(e=this.root){e.metadata&&(yield e.metadata);for(let r of e.children.values())yield*gr(this.traverse(r))}getPathSegments(e){return e.split("/").filter(Boolean)}traverseBySegments(e,r=this.root,n=0){if(n>=e.length)return r.metadata?r:r.children.get("**");if(!r.children.size)return;let u=e[n],a=r.children.get(u);if(a){let s=this.traverseBySegments(e,a,n+1);if(s)return s}let i=r.children.get("*");if(i){let s=this.traverseBySegments(e,i,n+1);if(s)return s}return r.children.get("**")}createEmptyRouteTreeNode(){return{children:new Map}}},Pl=10,Hl=/\/(\*\*)$/,Ki=/(?<!\\):([^/]+)/g,Bl=new Set([301,302,303,307,308]);function Fl(t){return Gu(this,null,function*(){try{let{metadata:e,currentRoutePath:r,route:n,compiler:u,parentInjector:a,serverConfigRouteTree:i,entryPointToBrowserMapping:s,invokeGetPrerenderParams:l,includePrerenderFallbackRoutes:o}=t,{redirectTo:c,loadChildren:d,loadComponent:f,children:y,\u0275entryName:v}=n;if(v&&f&&Ul(v,s,e,!0),e.renderMode===Xe.Prerender?yield*gr(Mp(i,typeof c=="string"?c:void 0,e,a,l,o)):typeof c=="string"?e.status&&!Bl.has(e.status)?yield{error:`The '${e.status}' status code is not a valid redirect response code. Please use one of the following redirect response codes: ${[...Bl.values()].join(", ")}.`}:yield je(ye({},e),{redirectTo:Ji(e.route,c)}):yield e,y?.length&&(yield*gr(Yi(je(ye({},t),{routes:y,parentRoute:r,parentPreloads:e.preload})))),d){v&&Ul(v,s,e,!1);let w=yield new ha(Vo(n,u,a).toPromise());if(w){let{routes:A,injector:S=a}=w;yield*gr(Yi(je(ye({},t),{routes:A,parentInjector:S,parentRoute:r,parentPreloads:e.preload})))}}}catch(e){yield{error:`Error in handleRoute for '${t.currentRoutePath}': ${e.message}`}}})}function Yi(t){return Gu(this,null,function*(){let{routes:e,parentPreloads:r,parentRoute:n,serverConfigRouteTree:u}=t;for(let a of e){let{matcher:i,path:s=i?"**":""}=a,l=Mr(n,s);if(i&&u){let c=!1;for(let d of u.traverse())if(d.route.startsWith(l)){if(c=!0,d.presentInClientRouter=!0,d.renderMode===Xe.Prerender){yield{error:`The route '${Lr(l)}' is set for prerendering but has a defined matcher. Routes with matchers cannot use prerendering. Please specify a different 'renderMode'.`};continue}yield*gr(Fl(je(ye({},t),{currentRoutePath:l,route:a,metadata:je(ye({},d),{preload:r,route:d.route,presentInClientRouter:void 0})})))}c||(yield{error:`The route '${Lr(l)}' has a defined matcher but does not match any route in the server routing configuration. Please ensure this route is added to the server routing configuration.`});continue}let o;if(u){if(o=u.match(l),!o){yield{error:`The '${Lr(l)}' route does not match any route defined in the server routing configuration. Please ensure this route is added to the server routing configuration.`};continue}o.presentInClientRouter=!0}yield*gr(Fl(je(ye({},t),{metadata:je(ye({renderMode:Xe.Prerender},o),{preload:r,route:s===""?Ap(l):l,presentInClientRouter:void 0}),currentRoutePath:l,route:a})))}})}function Ul(t,e,r,n){let u=r.preload??[];if(!e||u.length>=Pl)return;let a=e[t];if(!a?.length)return;let i=new Set(u);for(let{dynamicImport:s,path:l}of a)if(!(s&&!n)&&(i.add(l),i.size===Pl))break;r.preload=Array.from(i)}function Mp(t,e,r,n,u,a){return Gu(this,null,function*(){if(r.renderMode!==Xe.Prerender)throw new Error("'handleSSGRoute' was called for a route which rendering mode is not prerender.");let d=r,{route:i,fallback:s}=d,l=Yt(d,["route","fallback"]),o="getPrerenderParams"in l?l.getPrerenderParams:void 0;"getPrerenderParams"in l&&delete l.getPrerenderParams,e!==void 0&&(l.redirectTo=Ji(i,e));let c=Hl.test(i);if(c&&!o||!c&&!Ki.test(i)){yield je(ye({},l),{route:i});return}if(u){if(!o){yield{error:`The '${Lr(i)}' route uses prerendering and includes parameters, but 'getPrerenderParams' is missing. Please define 'getPrerenderParams' function for this route in your server routing configuration or specify a different 'renderMode'.`};return}if(t){let y=c?i:Mr(i,"**"),v=t.match(y);v&&v.renderMode===Xe.Prerender&&!("getPrerenderParams"in v)&&t.insert(y,je(ye({},v),{presentInClientRouter:!0,getPrerenderParams:o}))}let f=yield new ha(Bs(n,()=>o()));try{for(let y of f){let v=Dp(y,i),w=i.replace(Ki,v).replace(Hl,v);yield je(ye({},l),{route:w,redirectTo:e===void 0?void 0:Ji(w,e)})}}catch(y){yield{error:`${y.message}`};return}}a&&(s!==Zi.None||!u)&&(yield je(ye({},l),{route:i,renderMode:s===Zi.Client?Xe.Client:Xe.Server}))})}function Dp(t,e){return r=>{let n=r.slice(1),u=t[n];if(typeof u!="string")throw new Error(`The 'getPrerenderParams' function defined for the '${Lr(e)}' route returned a non-string value for parameter '${n}'. Please make sure the 'getPrerenderParams' function returns values for all parameters specified in this route.`);return n==="**"?`/${u}`:u}}function Ji(t,e){if(e[0]==="/")return e;let r=t.replace(Ki,"*").split("/");return r.pop(),Mr(...r,e)}function Ip({routes:t,appShellRoute:e}){let r=[...t];e!==void 0&&r.unshift({path:e,renderMode:Xe.Prerender});let n=new gu,u=[];for(let a of r){let i=a,{path:s}=i,l=Yt(i,["path"]);if(s[0]==="/"){u.push(`Invalid '${s}' route configuration: the path cannot start with a slash.`);continue}if("getPrerenderParams"in l&&(s.includes("/*/")||s.endsWith("/*"))){u.push(`Invalid '${s}' route configuration: 'getPrerenderParams' cannot be used with a '*' route.`);continue}n.insert(s,l)}return{serverConfigRouteTree:n,errors:u}}function Op(t,e,r,n=!1,u=!0,a=void 0){return ze(this,null,function*(){let{protocol:i,host:s}=r,l=ln([{provide:_r,useValue:{document:e,url:`${i}//${s}/`}},{provide:Zu,useFactory:()=>new Pn},{provide:bo,useValue:!1}]);try{let y;Wl(t)?y=(yield l.bootstrapModule(t)).injector.get(Ku):y=yield t();let v=y.injector,w=v.get(rr);w.navigationTransitions.afterPreactivation()?.next?.(),yield y.whenStable();let A=[],S=v.get(No,null,{optional:!0})??v.get(au).getBaseHrefFromDOM(),{pathname:_}=new URL(S,"http://localhost"),x=v.get(po),T=v.get(Lp,null,{optional:!0}),E;if(T){let B=Ip(T);E=B.serverConfigRouteTree,A.push(...B.errors)}if(A.length)return{baseHref:_,routes:[],errors:A};let O=[];if(w.config.length){let B=Yi({routes:w.config,compiler:x,parentInjector:v,parentRoute:"",serverConfigRouteTree:E,invokeGetPrerenderParams:n,includePrerenderFallbackRoutes:u,entryPointToBrowserMapping:a}),F=new Set;try{for(var o=Rs(B),c,d,f;c=!(d=yield o.next()).done;c=!1){let q=d.value;if("error"in q){A.push(q.error);continue}let C=q.route;F.has(C)||(O.push(q),F.add(C))}}catch{f=[d]}finally{try{c&&(d=o.return)&&(yield d.call(o))}finally{if(f)throw f[0]}}if(yield new Promise(q=>setTimeout(q,0)),E)for(let{route:q,presentInClientRouter:C}of E.traverse())C||q.endsWith("/**")||A.push(`The '${Lr(q)}' server route does not match any routes defined in the Angular routing configuration (typically provided as a part of the 'provideRouter' call). Please make sure that the mentioned server route is present in the Angular routing configuration.`)}else{let B=E?.match("")??{route:"",renderMode:Xe.Prerender};O.push(je(ye({},B),{route:""}))}return{baseHref:_,routes:O,errors:A,appShellRoute:T?.appShellRoute}}finally{l.destroy()}})}function Rp(t){let{url:e,manifest:r=zl(),invokeGetPrerenderParams:n=!1,includePrerenderFallbackRoutes:u=!0,signal:a}=t;function i(){return ze(this,null,function*(){let s=new gu,l=yield new qn(r).getIndexServerHtml().text(),o=yield r.bootstrap(),{baseHref:c,appShellRoute:d,routes:f,errors:y}=yield Op(o,l,e,n,u,r.entryPointToBrowserMapping);for(let v of f){let w=v,{route:A}=w,S=Yt(w,["route"]);S.redirectTo!==void 0&&(S.redirectTo=Mr(c,S.redirectTo));for(let[x,T]of Object.entries(S))T===void 0&&delete S[x];let _=Mr(c,A);s.insert(_,S)}return{appShellRoute:d,routeTree:s,errors:y}})}return a?Xl(i(),a,"Routes extraction"):i()}var es=class{store=new Map;run(e,r){return ze(this,null,function*(){let n=this.store.get(e);switch(e){case"html:transform:pre":{if(!n)return r.html;let u=ye({},r);for(let a of n)u.html=yield a(u);return u.html}default:throw new Error(`Running hook "${e}" is not supported.`)}})}on(e,r){let n=this.store.get(e);n?n.push(r):this.store.set(e,[r])}has(e){return!!this.store.get(e)?.length}},ts=class t{routeTree;constructor(e){this.routeTree=e}static#e;static from(e,r){if(e.routes){let n=gu.fromObject(e.routes);return Promise.resolve(new t(n))}return t.#e??=Rp({url:r,manifest:e}).then(({routeTree:n,errors:u})=>{if(u.length>0)throw new Error(`Error(s) occurred while extracting routes:
`+u.map(a=>`- ${a}`).join(`
`));return new t(n)}).finally(()=>{t.#e=void 0}),t.#e}match(e){let{pathname:r}=$l(e);return this.routeTree.match(decodeURIComponent(r))}};function qp(t){return ze(this,null,function*(){let e=new TextEncoder().encode(t),r=yield crypto.subtle.digest("SHA-256",e),n=[];for(let u of new Uint8Array(r))n.push(u.toString(16).padStart(2,"0"));return n.join("")})}var Vl=/^this\.media=["'](.*)["'];?$/,Ql="ngCspMedia",jl=`(() => {
  const CSP_MEDIA_ATTR = '${Ql}';
  const documentElement = document.documentElement;

  // Listener for load events on link tags.
  const listener = (e) => {
    const target = e.target;
    if (
      !target ||
      target.tagName !== 'LINK' ||
      !target.hasAttribute(CSP_MEDIA_ATTR)
    ) {
      return;
    }

    target.media = target.getAttribute(CSP_MEDIA_ATTR);
    target.removeAttribute(CSP_MEDIA_ATTR);

    if (!document.head.querySelector(\`link[\${CSP_MEDIA_ATTR}]\`)) {
      documentElement.removeEventListener('load', listener);
    }
  };

  documentElement.addEventListener('load', listener, true);
})();`,rs=class extends Nn{},us=class t extends rs{readFile;outputPath;addedCspScriptsDocuments=new WeakSet;documentNonces=new WeakMap;constructor(e,r){super({logger:{warn:n=>console.warn(n),error:n=>console.error(n),info:()=>{}},logLevel:"warn",path:r,publicPath:void 0,compress:!1,pruneSource:!1,reduceInlineStyles:!1,mergeStylesheets:!1,preload:"media",noscriptFallback:!0,inlineFonts:!0}),this.readFile=e,this.outputPath=r}embedLinkedStylesheet(e,r){return ze(this,null,function*(){if(e.getAttribute("media")==="print"&&e.next?.name==="noscript"){let a=e.getAttribute("onload")?.match(Vl);a&&(e.removeAttribute("onload"),e.setAttribute("media",a[1]),e?.next?.remove())}let n=yield Os(t.prototype,this,"embedLinkedStylesheet").call(this,e,r),u=this.findCspNonce(r);if(u){let a=e.getAttribute("onload")?.match(Vl);a&&(e.removeAttribute("onload"),e.setAttribute(Ql,a[1]),this.conditionallyInsertCspLoadingScript(r,u,e)),r.head.children.forEach(i=>{i.tagName==="style"&&!i.hasAttribute("nonce")&&i.setAttribute("nonce",u)})}return n})}findCspNonce(e){if(this.documentNonces.has(e))return this.documentNonces.get(e);let r=e.querySelector("[ngCspNonce], [ngcspnonce]"),n=r?.getAttribute("ngCspNonce")||r?.getAttribute("ngcspnonce")||null;return this.documentNonces.set(e,n),n}conditionallyInsertCspLoadingScript(e,r,n){if(this.addedCspScriptsDocuments.has(e))return;if(e.head.textContent.includes(jl)){this.addedCspScriptsDocuments.add(e);return}let u=e.createElement("script");u.setAttribute("nonce",r),u.textContent=jl,e.head.insertBefore(u,n),this.addedCspScriptsDocuments.add(e)}},ns=class{capacity;cache=new Map;head;tail;constructor(e){this.capacity=e}get(e){let r=this.cache.get(e);if(r)return this.moveToHead(r),r.value}put(e,r){let n=this.cache.get(e);if(n){n.value=r,this.moveToHead(n);return}let u={key:e,value:r,prev:void 0,next:void 0};if(this.cache.set(e,u),this.addToHead(u),this.cache.size>this.capacity){let a=this.removeTail();a&&this.cache.delete(a.key)}}addToHead(e){e.next=this.head,e.prev=void 0,this.head&&(this.head.prev=e),this.head=e,this.tail||(this.tail=e)}removeNode(e){e.prev?e.prev.next=e.next:this.head=e.next,e.next?e.next.prev=e.prev:this.tail=e.prev}moveToHead(e){this.removeNode(e),this.addToHead(e)}removeTail(){let e=this.tail;return e&&this.removeNode(e),e}},Pp=50,Hp={[Xe.Prerender]:"ssg",[Xe.Server]:"ssr",[Xe.Client]:""},as=class{options;allowStaticRouteRender;hooks;constructor(e={}){this.options=e,this.allowStaticRouteRender=this.options.allowStaticRouteRender??!1,this.hooks=e.hooks??new es}manifest=zl();assets=new qn(this.manifest);router;inlineCriticalCssProcessor;boostrap;criticalCssLRUCache=new ns(Pp);handle(e,r){return ze(this,null,function*(){let n=new URL(e.url);this.router??=yield ts.from(this.manifest,n);let u=this.router.match(n);if(!u)return null;let{redirectTo:a,status:i,renderMode:s}=u;if(a!==void 0)return new Response(null,{status:i??302,headers:{Location:Cp(a,n.pathname)}});if(s===Xe.Prerender){let l=yield this.handleServe(e,u);if(l)return l}return Xl(this.handleRendering(e,u,r),e.signal,`Request for: ${e.url}`)})}handleServe(e,r){return ze(this,null,function*(){let{headers:n,renderMode:u}=r;if(u!==Xe.Prerender)return null;let{method:a}=e;if(a!=="GET"&&a!=="HEAD")return null;let i=this.buildServerAssetPathFromRequest(e),{manifest:{locale:s},assets:l}=this;if(!l.hasServerAsset(i))return null;let{text:o,hash:c,size:d}=l.getServerAsset(i),f=`"${c}"`;return e.headers.get("if-none-match")===f?new Response(void 0,{status:304,statusText:"Not Modified"}):new Response(yield o(),{headers:ye(ye({"Content-Length":d.toString(),ETag:f,"Content-Type":"text/html;charset=UTF-8"},s!==void 0?{"Content-Language":s}:{}),n)})})}handleRendering(e,r,n){return ze(this,null,function*(){let{renderMode:u,headers:a,status:i,preload:s}=r;if(!this.allowStaticRouteRender&&u===Xe.Prerender)return null;let l=new URL(e.url),o=[],{manifest:{bootstrap:c,inlineCriticalCss:d,locale:f},assets:y}=this,v={status:i,headers:new Headers(ye(ye({"Content-Type":"text/html;charset=UTF-8"},f!==void 0?{"Content-Language":f}:{}),a))};if(u===Xe.Server)o.push({provide:_o,useValue:e},{provide:So,useValue:n},{provide:Eo,useValue:v});else if(u===Xe.Client){let A=yield this.assets.getServerAsset("index.csr.html").text();return A=yield this.runTransformsOnHtml(A,l,s),new Response(A,v)}f!==void 0&&o.push({provide:mo,useValue:f}),this.boostrap??=yield c();let w=yield y.getIndexServerHtml().text();if(w=yield this.runTransformsOnHtml(w,l,s),w=yield kp(w,this.boostrap,l,o,Hp[u]),d)if(this.inlineCriticalCssProcessor??=new us(A=>{let S=A.split("/").pop()??A;return this.assets.getServerAsset(S).text()}),u===Xe.Server&&typeof crypto>"u"&&console.error("The global 'crypto' module is unavailable. If you are running on Node.js, please ensure you are using version 20 or later, which includes built-in support for the Web Crypto module."),u===Xe.Server&&typeof crypto<"u"){let A=yield qp(w),S=this.criticalCssLRUCache.get(A);S===void 0&&(S=yield this.inlineCriticalCssProcessor.process(w),this.criticalCssLRUCache.put(A,S)),w=S}else w=yield this.inlineCriticalCssProcessor.process(w);return new Response(w,v)})}buildServerAssetPathFromRequest(e){let{pathname:r}=new URL(e.url);r.endsWith("/index.html")||(r=Mr(r,"index.html"));let{baseHref:n}=this.manifest;return n.length>1&&r.startsWith(n)&&(r=r.slice(n.length)),Lr(r)}runTransformsOnHtml(e,r,n){return ze(this,null,function*(){return this.hooks.has("html:transform:pre")&&(e=yield this.hooks.run("html:transform:pre",{html:e,url:r})),n?.length&&(e=Bp(e,n)),e})}},Zl;function Gm(t){return Zl??=new as(t)}function $m(){Zl=void 0}function Bp(t,e){let r=t.lastIndexOf("</body>");return r===-1?t:[t.slice(0,r),...e.map(n=>`<link rel="modulepreload" href="${n}">`),t.slice(r)].join(`
`)}var Vr=class t{title="DoorCompanyAPP";mobileMenuOpen=!1;sidebarCollapsed=!1;isMobile=!1;userMenuOpen=!1;constructor(){}onResize(){this.checkScreenSize()}onDocumentClick(e){!e.target.closest(".user-info")&&!e.target.closest(".user-menu")&&(this.userMenuOpen=!1)}checkScreenSize(){this.isMobile=window.innerWidth<=768,this.isMobile||(this.mobileMenuOpen=!1)}toggleSidebar(){this.isMobile?this.mobileMenuOpen=!this.mobileMenuOpen:this.sidebarCollapsed=!this.sidebarCollapsed}closeMobileMenu(){this.mobileMenuOpen=!1}toggleUserMenu(){this.userMenuOpen=!this.userMenuOpen}onNavItemClick(){this.isMobile&&(this.mobileMenuOpen=!1)}static \u0275fac=function(r){return new(r||t)};static \u0275cmp=Jt({type:t,selectors:[["app-root"]],hostBindings:function(r,n){r&1&&Te("resize",function(){return n.onResize()},!1,Xu)("click",function(a){return n.onDocumentClick(a)},!1,Qu)},standalone:!1,decls:1,vars:0,template:function(r,n){r&1&&Ke(0,"router-outlet")},dependencies:[un],encapsulation:2})};var Fp=()=>["Permissions.Partner.View"];function Up(t,e){t&1&&(X(0,"h2"),se(1,"\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0634\u0631\u0643\u0629"),Z())}function Vp(t,e){t&1&&(X(0,"span",42),se(1,"\u0644\u0648\u062D\u0629 \u0627\u0644\u062A\u062D\u0643\u0645"),Z())}function jp(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u0634\u0631\u0643\u0627\u0621"),Z())}function zp(t,e){if(t&1){let r=yr();X(0,"div",47)(1,"a",48),Te("click",function(){Me(r);let u=er(2);return De(u.onNavItemClick())}),X(2,"span",10),se(3,"\u{1F4C4}"),Z(),X(4,"span",42),se(5,"\u0627\u0644\u0634\u0631\u0643\u0627\u0621"),Z()(),X(6,"a",49),Te("click",function(){Me(r);let u=er(2);return De(u.onNavItemClick())}),X(7,"span",10),se(8,"\u{1F4DD}"),Z(),X(9,"span",42),se(10,"\u0645\u0639\u0627\u0645\u0644\u0627\u062A \u0627\u0644\u0634\u0631\u0643\u0627\u0621"),Z()(),X(11,"a",50),Te("click",function(){Me(r);let u=er(2);return De(u.onNavItemClick())}),X(12,"span",10),uu(),X(13,"svg",51),Ke(14,"path",52),Z()(),pa(),X(15,"span",42),se(16,"\u0645\u0639\u0627\u0645\u0644\u0627\u062A \u0627\u0644\u0627\u0633\u0647\u0645"),Z()()()}}function Gp(t,e){if(t&1){let r=yr();X(0,"div")(1,"a",43),Te("click",function(){Me(r);let u=er();return De(u.toggleMenu("partners"))}),X(2,"span",10),uu(),X(3,"svg",44),Ke(4,"path",45),Z()(),Pe(5,jp,2,0,"span",11),pa(),X(6,"span",39),se(7," \u25BC "),Z()(),Pe(8,zp,17,0,"div",46),Z()}if(t&2){let r=er();xe(5),Le("ngIf",!r.sidebarCollapsed||r.isMobile),xe(),vr("open",r.isPartnerMenuOpen),xe(2),Le("ngIf",r.isPartnerMenuOpen&&(!r.sidebarCollapsed||r.isMobile))}}function $p(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u0639\u0645\u0644\u0627\u0621"),Z())}function Wp(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u0645\u0648\u0631\u062F\u064A\u0646"),Z())}function Xp(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u0645\u0646\u062A\u062C\u0627\u062A"),Z())}function Qp(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u062A\u0635\u0646\u064A\u0641\u0627\u062A"),Z())}function Zp(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u0648\u062D\u062F\u0627\u062A"),Z())}function Kp(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u0641\u0648\u0627\u062A\u064A\u0631"),Z())}function Yp(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u062D\u0633\u0627\u0628\u0627\u062A"),Z())}function Jp(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u0645\u062E\u0627\u0632\u0646"),Z())}function em(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u062E\u0632\u0627\u0626\u0646"),Z())}function tm(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u0642\u064A\u0648\u062F \u0627\u0644\u0645\u062D\u0627\u0633\u0628\u064A\u0629"),Z())}function rm(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631"),Z())}function um(t,e){t&1&&(X(0,"h3",53),se(1,"\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0646\u0638\u0627\u0645"),Z())}function nm(t,e){t&1&&(X(0,"span",42),se(1,"\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645\u064A\u0646"),Z())}function am(t,e){t&1&&(X(0,"span",42),se(1,"\u0627\u0644\u0623\u062F\u0648\u0627\u0631 \u0648\u0627\u0644\u0635\u0644\u0627\u062D\u064A\u0627\u062A"),Z())}function im(t,e){t&1&&(X(0,"span",42),se(1,"\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0646\u0638\u0627\u0645"),Z())}function sm(t,e){if(t&1&&(X(0,"div",54),Ke(1,"img",55),Z()),t&2){let r=er();xe(),Le("src",r.currentUser==null?null:r.currentUser.profileImage,Ks)}}function om(t,e){t&1&&(X(0,"span",54),se(1,"\u{1F464}"),Z())}function cm(t,e){if(t&1){let r=yr();X(0,"div",56)(1,"a",57),se(2,"\u0627\u0644\u0645\u0644\u0641 \u0627\u0644\u0634\u062E\u0635\u064A"),Z(),X(3,"a",58),se(4,"\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A"),Z(),Ke(5,"div",59),X(6,"a",60),Te("click",function(){Me(r);let u=er();return De(u.logout())}),se(7,"\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062E\u0631\u0648\u062C"),Z()()}}var Hn=class t{constructor(e,r,n,u,a){this.platformId=e;this.authService=r;this.userService=n;this.cdr=u;this.zone=a;this.checkScreenSize()}mobileMenuOpen=!1;sidebarCollapsed=!1;isMobile=!1;userMenuOpen=!1;currentUser=null;avatarTimestamp=Date.now();cachedAvatarUrl="";isPartnerMenuOpen=!1;ngOnInit(){this.userService.currentUser$.subscribe(e=>{let r=this.currentUser?.id,n=e?.id;this.currentUser=e,r!==n||this.currentUser?.profileImage})}onResize(){this.checkScreenSize()}onDocumentClick(e){!e.target.closest(".user-info")&&!e.target.closest(".user-menu")&&(this.userMenuOpen=!1)}checkScreenSize(){ko(this.platformId)&&(this.isMobile=window.innerWidth<768,this.isMobile||(this.mobileMenuOpen=!1))}getAvatarUrl(){let e=this.currentUser?.profileImage,r=Date.now();return e?`${e}?t=${r}`:"/assets/images/default-avatar.png"}toggleSidebar(){this.isMobile?this.mobileMenuOpen=!this.mobileMenuOpen:this.sidebarCollapsed=!this.sidebarCollapsed}closeMobileMenu(){this.mobileMenuOpen=!1}toggleUserMenu(){this.userMenuOpen=!this.userMenuOpen}onNavItemClick(){this.isMobile&&(this.mobileMenuOpen=!1)}toggleMenu(e){switch(e){case"partners":this.isPartnerMenuOpen=!this.isPartnerMenuOpen;break}}logout(){this.userMenuOpen=!1,this.authService.forceLogout(),this.userService.clearLocalStorage(),this.authService.logout().subscribe()}static \u0275fac=function(r){return new(r||t)(Rr(Wu),Rr(ur),Rr(Ko),Rr(en),Rr(Vs))};static \u0275cmp=Jt({type:t,selectors:[["app-layout"]],hostBindings:function(r,n){r&1&&Te("resize",function(){return n.onResize()},!1,Xu)("click",function(a){return n.onDocumentClick(a)},!1,Qu)},standalone:!1,decls:97,vars:29,consts:[["noAvatar",""],["dir","rtl",1,"layout-container"],[1,"mobile-overlay",3,"click"],[1,"sidebar"],[1,"sidebar-header"],[4,"ngIf"],[1,"toggle-btn",3,"click"],[1,"hamburger-icon"],[1,"nav-list"],["routerLink","/app/dashboard","routerLinkActive","active",1,"nav-item",3,"click"],[1,"nav-icon"],["class","nav-text",4,"ngIf"],[4,"appHasPermission"],["routerLink","/app/customers","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/suppliers","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/products","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/categories","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/units","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/invoices","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/accounts","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/warehouses","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/treasuries","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/journal-entries","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/reports","routerLinkActive","active",1,"nav-item",3,"click"],[1,"nav-divider"],[1,"admin-section"],["class","section-title",4,"ngIf"],["routerLink","/app/users","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/roles","routerLinkActive","active",1,"nav-item",3,"click"],["routerLink","/app/settings","routerLinkActive","active",1,"nav-item",3,"click"],[1,"main-container"],[1,"top-bar"],[1,"top-bar-left"],[1,"menu-btn",3,"click"],[1,"page-title"],[1,"top-bar-right"],[1,"user-info",3,"click"],[1,"user-name"],["class","user-avatar",4,"ngIf","ngIfElse"],[1,"dropdown-arrow"],["class","user-menu",4,"ngIf"],[1,"page-content"],[1,"nav-text"],["href","javascript:void(0)",1,"nav-item","dropdown-toggle",3,"click"],["xmlns","http://www.w3.org/2000/svg","height","24px","viewBox","0 -960 960 960","width","24px","fill","#FBE6A3"],["d","M40-160v-160q0-34 23.5-57t56.5-23h131q20 0 38 10t29 27q29 39 71.5 61t90.5 22q49 0 91.5-22t70.5-61q13-17 30.5-27t36.5-10h131q34 0 57 23t23 57v160H640v-91q-35 25-75.5 38T480-200q-43 0-84-13.5T320-252v92H40Zm440-160q-38 0-72-17.5T351-386q-17-25-42.5-39.5T253-440q22-37 93-58.5T480-520q63 0 134 21.5t93 58.5q-29 0-55 14.5T609-386q-22 32-56 49t-73 17ZM160-440q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T280-560q0 50-34.5 85T160-440Zm640 0q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T920-560q0 50-34.5 85T800-440ZM480-560q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T600-680q0 50-34.5 85T480-560Z"],["class","dropdown-menu",4,"ngIf"],[1,"dropdown-menu"],["routerLink","/partners","routerLinkActiveOptions","{exact: true}",1,"nav-item","nested",3,"click"],["routerLink","/partners/transactions-list","routerLinkActiveOptions","{exact: true}",1,"nav-item","nested",3,"click"],["routerLink","/partners/share-list","routerLinkActive","active",1,"nav-item","nested",3,"click"],["xmlns","http://www.w3.org/2000/svg","height","24px","viewBox","0 -960 960 960","width","24px","fill","#255290"],["d","M480 0q-94 0-177.5-33.5t-148-93Q90-186 49-266.5T0-440h80q8 72 38.5 134.5t79 110.5Q246-147 309-117.5T444-82l-62-62 56-56L618-20Q584-9 549.5-4.5T480 0Zm120-200v-80H360q-33 0-56.5-23.5T280-360v-240h-80v-80h80v-80h80v400h400v80h-80v80h-80Zm0-240v-160H440v-80h160q33 0 56.5 23.5T680-600v160h-80Zm280-80q-7-72-38-134.5T762.5-765Q714-813 651-842.5T516-878l62 62-56 56-180-180q34-11 68.5-15.5T480-960q94 0 177.5 33.5t148 93Q870-774 911-693.5T960-520h-80Z"],[1,"section-title"],[1,"user-avatar"],["alt","User Avatar",1,"user-cover",3,"src"],[1,"user-menu"],["href","#","routerLink","/profile",1,"menu-item"],["href","#",1,"menu-item"],[1,"menu-divider"],["href","#",1,"menu-item","logout-button",3,"click"]],template:function(r,n){if(r&1){let u=yr();X(0,"div",1)(1,"div",2),Te("click",function(){return Me(u),De(n.closeMobileMenu())}),Z(),X(2,"div",3)(3,"div",4),Pe(4,Up,2,0,"h2",5),X(5,"button",6),Te("click",function(){return Me(u),De(n.toggleSidebar())}),X(6,"span",7),Ke(7,"span")(8,"span")(9,"span"),Z()()(),X(10,"nav",8)(11,"a",9),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(12,"span",10),se(13,"\u{1F4CA}"),Z(),Pe(14,Vp,2,0,"span",11),Z(),Pe(15,Gp,9,4,"div",12),X(16,"a",13),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(17,"span",10),se(18,"\u{1F465}"),Z(),Pe(19,$p,2,0,"span",11),Z(),X(20,"a",14),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(21,"span",10),se(22,"\u{1F3E2}"),Z(),Pe(23,Wp,2,0,"span",11),Z(),X(24,"a",15),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(25,"span",10),se(26,"\u{1F4E6}"),Z(),Pe(27,Xp,2,0,"span",11),Z(),X(28,"a",16),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(29,"span",10),se(30,"\u{1F3F7}\uFE0F"),Z(),Pe(31,Qp,2,0,"span",11),Z(),X(32,"a",17),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(33,"span",10),se(34,"\u{1F4CF}"),Z(),Pe(35,Zp,2,0,"span",11),Z(),X(36,"a",18),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(37,"span",10),se(38,"\u{1F4C4}"),Z(),Pe(39,Kp,2,0,"span",11),Z(),X(40,"a",19),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(41,"span",10),se(42,"\u{1F4B0}"),Z(),Pe(43,Yp,2,0,"span",11),Z(),X(44,"a",20),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(45,"span",10),se(46,"\u{1F3EA}"),Z(),Pe(47,Jp,2,0,"span",11),Z(),X(48,"a",21),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(49,"span",10),se(50,"\u{1F3E6}"),Z(),Pe(51,em,2,0,"span",11),Z(),X(52,"a",22),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(53,"span",10),se(54,"\u{1F4DA}"),Z(),Pe(55,tm,2,0,"span",11),Z(),X(56,"a",23),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(57,"span",10),se(58,"\u{1F4C8}"),Z(),Pe(59,rm,2,0,"span",11),Z(),Ke(60,"div",24),X(61,"div",25),Pe(62,um,2,0,"h3",26),X(63,"a",27),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(64,"span",10),se(65,"\u{1F464}"),Z(),Pe(66,nm,2,0,"span",11),Z(),X(67,"a",28),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(68,"span",10),se(69,"\u{1F510}"),Z(),Pe(70,am,2,0,"span",11),Z(),X(71,"a",29),Te("click",function(){return Me(u),De(n.onNavItemClick())}),X(72,"span",10),se(73,"\u2699\uFE0F"),Z(),Pe(74,im,2,0,"span",11),Z()()()(),X(75,"div",30)(76,"div",31)(77,"div",32)(78,"button",33),Te("click",function(){return Me(u),De(n.toggleSidebar())}),X(79,"span",7),Ke(80,"span")(81,"span")(82,"span"),Z()(),X(83,"h1",34),se(84,"\u0646\u0638\u0627\u0645 \u0627\u062F\u0627\u0631\u0629 \u0627\u0644\u0627\u0628\u0648\u0627\u0628 \u0627\u0644\u0645\u0635\u0641\u062D\u0629"),Z()(),X(85,"div",35)(86,"div",36),Te("click",function(){return Me(u),De(n.toggleUserMenu())}),X(87,"span",37),se(88),Z(),Pe(89,sm,2,1,"div",38)(90,om,2,0,"ng-template",null,0,ho),X(92,"span",39),se(93,"\u25BC"),Z()(),Pe(94,cm,8,0,"div",40),Z()(),X(95,"div",41),Ke(96,"router-outlet"),Z()()()}if(r&2){let u=Ju(91);xe(),vr("active",n.mobileMenuOpen),xe(),vr("collapsed",n.sidebarCollapsed)("mobile-open",n.mobileMenuOpen),xe(2),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(10),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(),Le("appHasPermission",fo(28,Fp)),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(3),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(4),Le("ngIf",!n.sidebarCollapsed||n.isMobile),xe(14),co(n.currentUser==null?null:n.currentUser.fullName),xe(),Le("ngIf",n.currentUser==null?null:n.currentUser.profileImage)("ngIfElse",u),xe(5),Le("ngIf",n.userMenuOpen)}},dependencies:[Ao,un,nn,jo,Qo,Yo],styles:[".layout-container[_ngcontent-%COMP%]{display:flex;height:100vh;direction:rtl;font-family:Cairo,Arial,sans-serif;position:relative}.mobile-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#00000080;z-index:99;opacity:0;visibility:hidden;transition:all .3s ease}.mobile-overlay.active[_ngcontent-%COMP%]{opacity:1;visibility:visible}.sidebar[_ngcontent-%COMP%]{width:280px;background:#1e293b;color:#fff;transition:all .3s ease;overflow-y:Scroll;position:relative;z-index:100;box-shadow:-2px 0 10px #0000001a}.sidebar.collapsed[_ngcontent-%COMP%]{width:70px}.sidebar-header[_ngcontent-%COMP%]{background:#0f172a;padding:16px;display:flex;align-items:center;justify-content:space-between;min-height:64px;box-sizing:border-box;border-bottom:1px solid #334155}.sidebar-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:18px;font-weight:600;white-space:nowrap;transition:opacity .3s ease}.toggle-btn[_ngcontent-%COMP%]{background:none;border:none;color:#fff;cursor:pointer;padding:8px;border-radius:6px;transition:all .3s ease;display:flex;align-items:center;justify-content:center}.toggle-btn[_ngcontent-%COMP%]:hover{background:#ffffff1a}.hamburger-icon[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:20px;height:16px;position:relative}.hamburger-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:block;height:2px;width:100%;background:#fff;border-radius:1px;transition:all .3s ease;transform-origin:center}.hamburger-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){margin-bottom:5px}.hamburger-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){margin-bottom:5px}.nav-list[_ngcontent-%COMP%]{padding:16px 0;overflow-y:auto;flex:1}.nav-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 20px;color:#cbd5e1;text-decoration:none;transition:all .3s ease;margin:2px 8px;border-radius:8px;position:relative;min-height:44px;box-sizing:border-box}.nav-item[_ngcontent-%COMP%]:hover{background:#334155;color:#fff;transform:translate(2px)}.nav-item.active[_ngcontent-%COMP%]{background:#3b82f6;color:#fff;box-shadow:0 2px 8px #3b82f64d}.nav-icon[_ngcontent-%COMP%]{font-size:20px;margin-left:12px;min-width:24px;text-align:center;transition:all .3s ease}.nav-text[_ngcontent-%COMP%]{white-space:nowrap;font-weight:700;transition:all .3s ease;font-family:cairo,Arial,sans-serif;font-size:20px}.sidebar.collapsed[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{padding:12px;justify-content:center;margin:2px 4px}.sidebar.collapsed[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{margin-left:0;font-size:22px}.nav-divider[_ngcontent-%COMP%]{height:1px;background:#334155;margin:16px 20px}.admin-section[_ngcontent-%COMP%]{margin-top:16px}.section-title[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#64748b;margin:16px 20px 8px;text-transform:uppercase;letter-spacing:.5px}.main-container[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;background:#f8fafc}.top-bar[_ngcontent-%COMP%]{background:#fff;padding:0 24px;display:flex;align-items:center;justify-content:space-between;min-height:64px;box-shadow:0 2px 4px #0000001a;z-index:10}.top-bar-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.menu-btn[_ngcontent-%COMP%]{background:green;border:none;font-size:20px;cursor:pointer;padding:8px;border-radius:4px;transition:background .3s ease}.menu-btn[_ngcontent-%COMP%]:hover{background:#f1f5f9}.page-title[_ngcontent-%COMP%]{margin:0;font-size:20px;font-weight:600;color:#1e293b}.top-bar-right[_ngcontent-%COMP%]{position:relative}.user-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px 16px;background:#f8fafc;border-radius:8px;cursor:pointer;transition:background .3s ease}.user-cover[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;border:2px solid #3b82f6}.user-info[_ngcontent-%COMP%]:hover{background:#e2e8f0}.user-name[_ngcontent-%COMP%]{font-weight:500;color:#1e293b}.user-avatar[_ngcontent-%COMP%]{font-size:20px}.dropdown-arrow[_ngcontent-%COMP%]{font-size:12px;color:#64748b}.user-menu[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;background:#fff;border:1px solid #e2e8f0;border-radius:8px;box-shadow:0 4px 12px #0000001a;min-width:200px;z-index:20}.menu-item[_ngcontent-%COMP%]{display:block;padding:12px 16px;color:#1e293b;text-decoration:none;transition:background .3s ease}.menu-item[_ngcontent-%COMP%]:hover{background:#f8fafc}.menu-divider[_ngcontent-%COMP%]{height:1px;background:#e2e8f0;margin:8px 0}.page-content[_ngcontent-%COMP%]{flex:1;overflow-y:auto}@media (max-width: 1024px){.sidebar[_ngcontent-%COMP%]{width:260px}.sidebar.collapsed[_ngcontent-%COMP%]{width:60px}}@media (max-width: 768px){.layout-container[_ngcontent-%COMP%]{overflow:hidden}.sidebar[_ngcontent-%COMP%]{position:fixed;z-index:100;height:100vh;width:280px!important;right:0;top:0;transform:translate(100%);box-shadow:-4px 0 20px #0000004d}.sidebar.mobile-open[_ngcontent-%COMP%]{transform:translate(0)}.sidebar.collapsed[_ngcontent-%COMP%]{width:280px!important;transform:translate(100%)}.sidebar.collapsed.mobile-open[_ngcontent-%COMP%]{transform:translate(0)}.main-container[_ngcontent-%COMP%]{width:100%;margin-right:0}.top-bar[_ngcontent-%COMP%]{padding:0 16px}.page-title[_ngcontent-%COMP%]{font-size:18px}.user-name[_ngcontent-%COMP%]{display:none}.nav-item[_ngcontent-%COMP%]{padding:14px 20px;margin:2px 8px}.nav-icon[_ngcontent-%COMP%]{font-size:22px;margin-left:16px}.nav-text[_ngcontent-%COMP%]{font-size:16px}.user-menu[_ngcontent-%COMP%]{right:0;margin-right:auto}}@media (max-width: 480px){.top-bar[_ngcontent-%COMP%]{padding:0 12px}.page-title[_ngcontent-%COMP%]{font-size:16px}.sidebar[_ngcontent-%COMP%]{width:260px!important}.nav-item[_ngcontent-%COMP%], .sidebar-header[_ngcontent-%COMP%]{padding:12px 16px}.sidebar-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px}}@media (max-width: 768px) and (orientation: portrait){.sidebar[_ngcontent-%COMP%]{width:300px!important}}@media (max-width: 1024px) and (orientation: landscape){.sidebar[_ngcontent-%COMP%]{width:240px}.sidebar.collapsed[_ngcontent-%COMP%]{width:60px}}.nav-item.dropdown-toggle[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;cursor:pointer}.dropdown-arrow[_ngcontent-%COMP%]{transition:transform .3s ease;font-size:.7em}.dropdown-arrow.open[_ngcontent-%COMP%]{transform:rotate(180deg)}.dropdown-menu[_ngcontent-%COMP%]{overflow:hidden;animation:_ngcontent-%COMP%_slideDown .2s ease}@keyframes _ngcontent-%COMP%_slideDown{0%{max-height:0;opacity:0;padding-top:0}to{max-height:200px;opacity:1;padding-top:5px}}.nav-item.nested[_ngcontent-%COMP%]{padding:8px 20px;font-size:.95em;color:#ee1b1b}.nav-item.nested[_ngcontent-%COMP%]:hover, .nav-item.nested.active[_ngcontent-%COMP%]{background-color:#1e88e5;color:#7aeca0}"]})};var Jl=(t,e)=>{let r=qe(ur),n=qe(rr);return r.tokenValue&&!r.isTokenExpired()?(n.navigate(["/"]),!1):!0};var Bn=(t,e)=>{let r=qe(ur),n=qe(rr);return r.tokenValue&&!r.isTokenExpired()?!0:(n.navigate(["/auth/login"]),!1)};var Fn=class t{static \u0275fac=function(r){return new(r||t)};static \u0275cmp=Jt({type:t,selectors:[["app-unauthorized"]],standalone:!1,decls:7,vars:0,consts:[[1,"unauthorized-container"],["routerLink","/dashboard",1,"btn","btn-primary"]],template:function(r,n){r&1&&(X(0,"div",0)(1,"h1"),se(2,"\u063A\u064A\u0631 \u0645\u0635\u0631\u062D"),Z(),X(3,"p"),se(4,"\u0644\u064A\u0633 \u0644\u062F\u064A\u0643 \u0627\u0644\u0635\u0644\u0627\u062D\u064A\u0629 \u0644\u0644\u0648\u0635\u0648\u0644 \u0625\u0644\u0649 \u0647\u0630\u0647 \u0627\u0644\u0635\u0641\u062D\u0629."),Z(),X(5,"a",1),se(6,"\u0627\u0644\u0639\u0648\u062F\u0629 \u0625\u0644\u0649 \u0627\u0644\u0635\u0641\u062D\u0629 \u0627\u0644\u0631\u0626\u064A\u0633\u064A\u0629"),Z()())},dependencies:[nn],styles:[".unauthorized-container[_ngcontent-%COMP%]{text-align:center;margin-top:100px}.unauthorized-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:48px;color:#d9534f}.unauthorized-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:18px;margin:20px 0}.btn[_ngcontent-%COMP%]{padding:10px 20px;text-decoration:none;color:#fff;background-color:#0275d8;border-radius:5px}"]})};var ed=(t,e)=>{let r=qe(ur),n=qe(rr),u=t.data.permissions;if(!u)return!0;let a=r.viewUserPermissions().permissions;return u.some(s=>a.includes(s))?!0:(n.navigate(["/unauthorized"]),!1)};var lm=[{path:"auth",canActivate:[Jl],loadChildren:()=>import("./chunk-K4M745NY.mjs").then(t=>t.AuthModule),\u0275entryName:"src/app/features/auth/auth.module.ts"},{path:"",component:Hn,children:[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"dashboard",canActivate:[Bn],loadChildren:()=>import("./chunk-4ILPT5VM.mjs").then(t=>t.DashboardModule),\u0275entryName:"src/app/features/dashboard/dashboard.module.ts"},{path:"users",canActivate:[Bn],loadChildren:()=>import("./chunk-QD3LIJ7Q.mjs").then(t=>t.UsersModule),\u0275entryName:"src/app/features/users/users.module.ts"},{path:"profile",loadChildren:()=>import("./chunk-TRSHSL3W.mjs").then(t=>t.ProfileModule),\u0275entryName:"src/app/features/profile/profile.module.ts"},{path:"partners",canActivate:[Bn,ed],data:{permissions:["Permissions.Partner.View"]},loadChildren:()=>import("./chunk-O7YEHCAM.mjs").then(t=>t.PartnerModule),\u0275entryName:"src/app/features/partner/partner.module.ts"}]},{path:"unauthorized",component:Fn},{path:"**",redirectTo:"auth/login"}],Un=class t{static \u0275fac=function(r){return new(r||t)};static \u0275mod=Tt({type:t});static \u0275inj=Et({imports:[ga.forRoot(lm),ga]})};var dm=["switch"],fm=["*"];function hm(t,e){t&1&&(X(0,"span",10),uu(),X(1,"svg",12),Ke(2,"path",13),Z(),X(3,"svg",14),Ke(4,"path",15),Z()())}var pm=new St("mat-slide-toggle-default-options",{providedIn:"root",factory:()=>({disableToggleValue:!1,hideIcon:!1,disabledInteractive:!1})}),mm={provide:Jo,useExisting:Ps(()=>td),multi:!0},Vn=class{source;checked;constructor(e,r){this.source=e,this.checked=r}},td=(()=>{class t{_elementRef=qe(js);_focusMonitor=qe(zo);_changeDetectorRef=qe(en);defaults=qe(pm);_onChange=r=>{};_onTouched=()=>{};_validatorOnChange=()=>{};_uniqueId;_checked=!1;_createChangeEvent(r){return new Vn(this,r)}_labelId;get buttonId(){return`${this.id||this._uniqueId}-button`}_switchElement;focus(){this._switchElement.nativeElement.focus()}_noopAnimations;_focused;name=null;id;labelPosition="after";ariaLabel=null;ariaLabelledby=null;ariaDescribedby;required;color;disabled=!1;disableRipple=!1;tabIndex=0;get checked(){return this._checked}set checked(r){this._checked=r,this._changeDetectorRef.markForCheck()}hideIcon;disabledInteractive;change=new ma;toggleChange=new ma;get inputId(){return`${this.id||this._uniqueId}-input`}constructor(){qe(Go).load(Xo);let r=qe(new Us("tabindex"),{optional:!0}),n=this.defaults,u=qe($s,{optional:!0});this.tabIndex=r==null?0:parseInt(r)||0,this.color=n.color||"accent",this._noopAnimations=u==="NoopAnimations",this.id=this._uniqueId=qe($o).getId("mat-mdc-slide-toggle-"),this.hideIcon=n.hideIcon??!1,this.disabledInteractive=n.disabledInteractive??!1,this._labelId=this._uniqueId+"-label"}ngAfterContentInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(r=>{r==="keyboard"||r==="program"?(this._focused=!0,this._changeDetectorRef.markForCheck()):r||Promise.resolve().then(()=>{this._focused=!1,this._onTouched(),this._changeDetectorRef.markForCheck()})})}ngOnChanges(r){r.required&&this._validatorOnChange()}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef)}writeValue(r){this.checked=!!r}registerOnChange(r){this._onChange=r}registerOnTouched(r){this._onTouched=r}validate(r){return this.required&&r.value!==!0?{required:!0}:null}registerOnValidatorChange(r){this._validatorOnChange=r}setDisabledState(r){this.disabled=r,this._changeDetectorRef.markForCheck()}toggle(){this.checked=!this.checked,this._onChange(this.checked)}_emitChangeEvent(){this._onChange(this.checked),this.change.emit(this._createChangeEvent(this.checked))}_handleClick(){this.disabled||(this.toggleChange.emit(),this.defaults.disableToggleValue||(this.checked=!this.checked,this._onChange(this.checked),this.change.emit(new Vn(this,this.checked))))}_getAriaLabelledBy(){return this.ariaLabelledby?this.ariaLabelledby:this.ariaLabel?null:this._labelId}static \u0275fac=function(n){return new(n||t)};static \u0275cmp=Jt({type:t,selectors:[["mat-slide-toggle"]],viewQuery:function(n,u){if(n&1&&io(dm,5),n&2){let a;so(a=oo())&&(u._switchElement=a.first)}},hostAttrs:[1,"mat-mdc-slide-toggle"],hostVars:13,hostBindings:function(n,u){n&2&&(uo("id",u.id),Yu("tabindex",null)("aria-label",null)("name",null)("aria-labelledby",null),to(u.color?"mat-"+u.color:""),vr("mat-mdc-slide-toggle-focused",u._focused)("mat-mdc-slide-toggle-checked",u.checked)("_mat-animation-noopable",u._noopAnimations))},inputs:{name:"name",id:"id",labelPosition:"labelPosition",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],required:[2,"required","required",wr],color:"color",disabled:[2,"disabled","disabled",wr],disableRipple:[2,"disableRipple","disableRipple",wr],tabIndex:[2,"tabIndex","tabIndex",r=>r==null?0:xo(r)],checked:[2,"checked","checked",wr],hideIcon:[2,"hideIcon","hideIcon",wr],disabledInteractive:[2,"disabledInteractive","disabledInteractive",wr]},outputs:{change:"change",toggleChange:"toggleChange"},exportAs:["matSlideToggle"],features:[lo([mm,{provide:ec,useExisting:t,multi:!0}]),Fs],ngContentSelectors:fm,decls:13,vars:27,consts:[["switch",""],["mat-internal-form-field","",3,"labelPosition"],["role","switch","type","button",1,"mdc-switch",3,"click","tabIndex","disabled"],[1,"mdc-switch__track"],[1,"mdc-switch__handle-track"],[1,"mdc-switch__handle"],[1,"mdc-switch__shadow"],[1,"mdc-elevation-overlay"],[1,"mdc-switch__ripple"],["mat-ripple","",1,"mat-mdc-slide-toggle-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mdc-switch__icons"],[1,"mdc-label",3,"click","for"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--on"],["d","M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--off"],["d","M20 13H4v-2h16v2z"]],template:function(n,u){if(n&1){let a=yr();no(),X(0,"div",1)(1,"button",2,0),Te("click",function(){return Me(a),De(u._handleClick())}),Ke(3,"span",3),X(4,"span",4)(5,"span",5)(6,"span",6),Ke(7,"span",7),Z(),X(8,"span",8),Ke(9,"span",9),Z(),Pe(10,hm,5,0,"span",10),Z()()(),X(11,"label",11),Te("click",function(s){return Me(a),De(s.stopPropagation())}),ao(12),Z()()}if(n&2){let a=Ju(2);Le("labelPosition",u.labelPosition),xe(),vr("mdc-switch--selected",u.checked)("mdc-switch--unselected",!u.checked)("mdc-switch--checked",u.checked)("mdc-switch--disabled",u.disabled)("mat-mdc-slide-toggle-disabled-interactive",u.disabledInteractive),Le("tabIndex",u.disabled&&!u.disabledInteractive?-1:u.tabIndex)("disabled",u.disabled&&!u.disabledInteractive),Yu("id",u.buttonId)("name",u.name)("aria-label",u.ariaLabel)("aria-labelledby",u._getAriaLabelledBy())("aria-describedby",u.ariaDescribedby)("aria-required",u.required||null)("aria-checked",u.checked)("aria-disabled",u.disabled&&u.disabledInteractive?"true":null),xe(8),Le("matRippleTrigger",a)("matRippleDisabled",u.disableRipple||u.disabled)("matRippleCentered",!0),xe(),ro(u.hideIcon?-1:10),xe(),Le("for",u.buttonId),Yu("id",u._labelId)}},dependencies:[Wo,rc],styles:[`.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative;width:var(--mdc-switch-track-width, 52px)}.mdc-switch.mdc-switch--disabled{cursor:default;pointer-events:none}.mdc-switch.mat-mdc-slide-toggle-disabled-interactive{pointer-events:auto}.mdc-switch__track{overflow:hidden;position:relative;width:100%;height:var(--mdc-switch-track-height, 32px);border-radius:var(--mdc-switch-track-shape, var(--mat-sys-corner-full))}.mdc-switch--disabled.mdc-switch .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";height:100%;left:0;position:absolute;width:100%;border-width:var(--mat-switch-track-outline-width, 2px);border-color:var(--mat-switch-track-outline-color, var(--mat-sys-outline))}.mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track::after{border-width:var(--mat-switch-selected-track-outline-width, 2px);border-color:var(--mat-switch-selected-track-outline-color, transparent)}.mdc-switch--disabled .mdc-switch__track::before,.mdc-switch--disabled .mdc-switch__track::after{border-width:var(--mat-switch-disabled-unselected-track-outline-width, 2px);border-color:var(--mat-switch-disabled-unselected-track-outline-color, var(--mat-sys-on-surface))}@media(forced-colors: active){.mdc-switch__track{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0);background:var(--mdc-switch-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch--selected .mdc-switch__track::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, var(--mat-sys-surface-variant))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::before,.mdc-switch.mdc-switch--disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch__track::after{transform:translateX(-100%);background:var(--mdc-switch-selected-track-color, var(--mat-sys-primary))}[dir=rtl] .mdc-switch__track::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::after{transform:translateX(0)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, var(--mat-sys-primary))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::after,.mdc-switch.mdc-switch--disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, var(--mat-sys-on-surface))}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0);width:calc(100% - var(--mdc-switch-handle-width))}[dir=rtl] .mdc-switch__handle-track{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto;transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1);width:var(--mdc-switch-handle-width);height:var(--mdc-switch-handle-height);border-radius:var(--mdc-switch-handle-shape, var(--mat-sys-corner-full))}[dir=rtl] .mdc-switch__handle{left:auto;right:0}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size, 16px);height:var(--mat-switch-unselected-handle-size, 16px);margin:var(--mat-switch-unselected-handle-horizontal-margin, 0 8px)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin, 0 4px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size, 24px);height:var(--mat-switch-selected-handle-size, 24px);margin:var(--mat-switch-selected-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size, 24px);height:var(--mat-switch-with-icon-handle-size, 24px)}.mat-mdc-slide-toggle .mdc-switch:active:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size, 28px);height:var(--mat-switch-pressed-handle-size, 28px)}.mat-mdc-slide-toggle .mdc-switch--selected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin, 0 22px)}.mat-mdc-slide-toggle .mdc-switch--unselected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin, 0 2px)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity, 1)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity, 0.38)}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media(forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mat-sys-on-primary))}.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, var(--mat-sys-primary-container))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:hover:not(:focus):not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:focus:not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:active .mdc-switch__handle::after,.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, var(--mat-sys-surface))}.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, var(--mat-sys-outline))}.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, var(--mat-sys-on-surface))}.mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__shadow,.mdc-switch.mdc-switch--disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1;width:var(--mdc-switch-state-layer-size, 40px);height:var(--mdc-switch-state-layer-size, 40px)}.mdc-switch__ripple::after{content:"";opacity:0}.mdc-switch--disabled .mdc-switch__ripple::after{display:none}.mat-mdc-slide-toggle-disabled-interactive .mdc-switch__ripple::after{display:block}.mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:75ms opacity cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:focus .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:active .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:hover:not(:focus) .mdc-switch__ripple::after,.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-pressed-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-selected-pressed-state-layer-color, var(--mat-sys-primary));opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch__icons{position:relative;height:100%;width:100%;z-index:1;transform:translateZ(0)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 16px);height:var(--mdc-switch-unselected-icon-size, 16px);fill:var(--mdc-switch-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 16px);height:var(--mdc-switch-selected-icon-size, 16px);fill:var(--mdc-switch-selected-icon-color, var(--mat-sys-on-primary-container))}.mdc-switch--selected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mat-sys-on-surface))}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{-webkit-user-select:none;user-select:none;display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-focus-indicator::before{content:""}.mat-mdc-slide-toggle .mat-internal-form-field{color:var(--mat-switch-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-switch-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-switch-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-switch-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-switch-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-switch-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}
`],encapsulation:2,changeDetection:0})}return t})();var rd=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275mod=Tt({type:t});static \u0275inj=Et({imports:[td,va,va]})}return t})();var jn=class t{static \u0275fac=function(r){return new(r||t)};static \u0275mod=Tt({type:t,bootstrap:[Vr]});static \u0275inj=Et({providers:[Uo(Fo()),qo(Ho(),Po([Zo]))],imports:[rn,Un,rd,Bo,tc,uc]})};var is=class t{static \u0275fac=function(r){return new(r||t)};static \u0275mod=Tt({type:t,bootstrap:[Vr]});static \u0275inj=Et({imports:[jn,Oa]})};export{Ra as a,Bc as b,Fc as c,zm as d,Rp as e,us as f,Gm as g,$m as h,is as i};
