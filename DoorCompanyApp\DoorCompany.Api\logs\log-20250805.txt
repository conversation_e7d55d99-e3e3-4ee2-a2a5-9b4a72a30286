2025-08-05 11:51:39.328 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-05 11:51:39.495 +03:00 [INF] Executed DbCommand (106ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-05 11:51:39.971 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 11:51:40.000 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 11:51:40.187 +03:00 [INF] Executed DbCommand (50ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-05 11:51:40.303 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-05 11:51:40.357 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-05 11:51:40.373 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 11:51:40.384 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 11:51:40.398 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 11:51:40.411 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 11:51:40.577 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-05 11:51:40.771 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-05 11:51:40.886 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-05 11:51:40.893 +03:00 [INF] Hosting environment: Development
2025-08-05 11:51:40.894 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-05 11:51:41.544 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-05 11:51:41.775 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 239.5307ms
2025-08-05 11:51:41.808 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-05 11:51:41.808 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-05 11:51:41.818 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 10.3153ms
2025-08-05 11:51:41.875 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 67.1197ms
2025-08-05 11:51:42.203 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-05 11:51:42.258 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 55.4972ms
2025-08-05 11:52:03.103 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Share - null null
2025-08-05 11:52:03.112 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-05 11:52:03.305 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.ShareController.GetAllShares (DoorCompany.Api)'
2025-08-05 11:52:03.334 +03:00 [INF] Route matched with {action = "GetAllShares", controller = "Share"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllShares() on controller DoorCompany.Api.Controllers.ShareController (DoorCompany.Api).
2025-08-05 11:52:03.554 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[DistributionDate], [s].[IsActive], [s].[IsDeleted], [s].[PartnerId], [s].[ShareValue], [s].[SharesCount], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareDistributions] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-05 11:52:03.570 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.ShareDto.ShareResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 11:52:03.605 +03:00 [INF] Executed action DoorCompany.Api.Controllers.ShareController.GetAllShares (DoorCompany.Api) in 263.4055ms
2025-08-05 11:52:03.608 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.ShareController.GetAllShares (DoorCompany.Api)'
2025-08-05 11:52:03.612 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Share - 404 null application/json; charset=utf-8 509.3269ms
2025-08-05 15:26:00.536 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-05 15:26:01.420 +03:00 [INF] Executed DbCommand (792ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-05 15:26:01.839 +03:00 [INF] Executed DbCommand (94ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 15:26:01.893 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 15:26:02.122 +03:00 [INF] Executed DbCommand (97ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-05 15:26:02.219 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-05 15:26:02.266 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-05 15:26:02.282 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 15:26:02.292 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 15:26:02.328 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 15:26:02.400 +03:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 15:26:02.414 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 15:26:02.429 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]
        WHERE [c].[Id] = 1) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 15:26:02.670 +03:00 [INF] Executed DbCommand (52ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (Size = 4000), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (Size = 4000), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (Size = 4000), @p10='?' (Size = 4000), @p11='?' (Size = 4000), @p12='?' (Size = 4000), @p13='?' (DbType = Int32), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Companies] ([Code], [CommercialRegister], [CreatedAt], [CreatedBy], [Description], [EstablishmentDate], [IndustrialRegister], [IsActive], [IsDeleted], [LogoPath], [Name], [Symbol], [TaxRegister], [TotalShares], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-08-05 15:26:02.849 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-05 15:26:03.112 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-05 15:26:03.226 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-05 15:26:03.228 +03:00 [INF] Hosting environment: Development
2025-08-05 15:26:03.229 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-05 15:26:03.531 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-05 15:26:03.730 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 207.979ms
2025-08-05 15:26:03.780 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-05 15:26:03.787 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 6.4052ms
2025-08-05 15:26:03.976 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-05 15:26:04.016 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 40.0012ms
2025-08-05 15:26:04.371 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-05 15:26:04.405 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 34.2439ms
2025-08-05 16:53:53.736 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-05 16:53:53.940 +03:00 [INF] Executed DbCommand (107ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-05 16:53:54.276 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 16:53:54.304 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 16:53:54.464 +03:00 [INF] Executed DbCommand (37ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-05 16:53:54.562 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-05 16:53:54.608 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-05 16:53:54.622 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 16:53:54.633 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 16:53:54.644 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 16:53:54.663 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 16:53:54.678 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 16:53:54.862 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-05 16:53:55.060 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-05 16:53:55.209 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-05 16:53:55.211 +03:00 [INF] Hosting environment: Development
2025-08-05 16:53:55.212 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-05 16:53:55.406 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-05 16:53:55.620 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 225.444ms
2025-08-05 16:53:55.644 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-05 16:53:55.649 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-05 16:53:55.652 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.5043ms
2025-08-05 16:53:55.685 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 35.6159ms
2025-08-05 16:53:55.873 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-05 16:53:55.911 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 38.8594ms
2025-08-05 16:55:15.008 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/login - null null
2025-08-05 16:55:15.017 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-05 16:55:15.055 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:15.058 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/login - 204 null null 50.6615ms
2025-08-05 16:55:15.068 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 43
2025-08-05 16:55:15.075 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:16.488 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-08-05 16:55:16.520 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-08-05 16:55:16.890 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-05 16:55:16.974 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-08-05 16:55:17.188 +03:00 [INF] Access token generated for user 1
2025-08-05 16:55:17.387 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-08-05 16:55:17.415 +03:00 [INF] User logged in successfully: admin
2025-08-05 16:55:17.427 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:55:17.490 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 962.6226ms
2025-08-05 16:55:17.493 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-08-05 16:55:17.497 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 2429.2977ms
2025-08-05 16:55:17.503 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-05 16:55:17.507 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:17.508 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 4.7297ms
2025-08-05 16:55:17.511 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-05 16:55:17.517 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:17.576 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '04/08/2025 03:35:22 م', Current time (UTC): '05/08/2025 01:55:17 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 16:55:17.618 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>
2025-08-05 16:55:17.641 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '04/08/2025 03:35:22 م', Current time (UTC): '05/08/2025 01:55:17 م'.
2025-08-05 16:55:17.644 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '04/08/2025 03:35:22 م', Current time (UTC): '05/08/2025 01:55:17 م'.
2025-08-05 16:55:17.678 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>
2025-08-05 16:55:17.682 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-05 16:55:17.685 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/Uploads/Users/<USER>
2025-08-05 16:55:17.691 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-05 16:55:17.693 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 181.4741ms
2025-08-05 16:55:17.696 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-05 16:55:17.699 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - null null
2025-08-05 16:55:17.700 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:17.704 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:17.706 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - 204 null null 7.2603ms
2025-08-05 16:55:17.709 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - application/json 107
2025-08-05 16:55:17.712 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:17.722 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-05 16:55:17.724 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-05 16:55:17.727 +03:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] RefreshToken(DoorCompany.Service.Dtos.UserDto.RefreshTokenDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-08-05 16:55:17.727 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-05 16:55:17.844 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-05 16:55:17.872 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-05 16:55:17.893 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:55:17.910 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 177.7047ms
2025-08-05 16:55:17.912 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-05 16:55:17.914 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 217.1607ms
2025-08-05 16:55:17.930 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[@__refreshTokenDto_RefreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshTokenDto_RefreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-05 16:55:17.964 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-05 16:55:18.045 +03:00 [INF] Executed DbCommand (35ms) [Parameters=[@__refreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-05 16:55:18.121 +03:00 [INF] Access token generated for user 1
2025-08-05 16:55:18.123 +03:00 [INF] Access token refreshed for user 1
2025-08-05 16:55:18.124 +03:00 [INF] Token refreshed successfully for user: 1
2025-08-05 16:55:18.125 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:55:18.126 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api) in 396.4607ms
2025-08-05 16:55:18.128 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-05 16:55:18.129 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - 200 null application/json; charset=utf-8 420.0818ms
2025-08-05 16:55:18.133 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-05 16:55:18.136 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:18.140 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-05 16:55:18.142 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-05 16:55:18.156 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-05 16:55:18.160 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:55:18.162 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 17.8995ms
2025-08-05 16:55:18.164 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-05 16:55:18.165 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 31.6425ms
2025-08-05 16:55:35.838 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 16:55:35.842 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:35.843 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 5.3705ms
2025-08-05 16:55:35.848 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 16:55:35.877 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:55:35.883 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 16:55:35.891 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:55:35.925 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:55:35.937 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 16:55:35.952 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 59.4266ms
2025-08-05 16:55:35.954 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 16:55:35.956 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 107.951ms
2025-08-05 16:56:10.601 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 16:56:10.626 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:56:10.628 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 26.3828ms
2025-08-05 16:56:10.638 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 113
2025-08-05 16:56:10.642 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:56:10.643 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:10.647 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:56:10.679 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:56:10.753 +03:00 [INF] Executed DbCommand (27ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialCapital], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-05 16:56:10.907 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-05 16:56:10.931 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:56:11.125 +03:00 [ERR] Error creating partner: أحمد عبد اللطيف
System.InvalidCastException: Unable to cast object of type 'AsyncStateMachineBox`1[DoorCompany.Service.Dtos.ApiResponse`1[System.Int32],DoorCompany.Service.Repositories.Implementations.PartnerService+<GetCountShareCompanyAsync>d__24]' to type 'System.IConvertible'.
   at System.Convert.ToInt32(Object value)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.CreatePartnerAsycn(CreatePartnerDto createDto) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 140
2025-08-05 16:56:11.178 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:56:11.188 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 537.6777ms
2025-08-05 16:56:11.192 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:11.194 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 556.4993ms
2025-08-05 16:56:11.206 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 113
2025-08-05 16:56:11.210 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:56:11.212 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:11.213 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:56:11.222 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:56:11.225 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:56:11.227 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 11.0665ms
2025-08-05 16:56:11.228 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:11.230 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 25.3112ms
2025-08-05 16:56:11.233 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 113
2025-08-05 16:56:11.236 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:56:11.238 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:11.239 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:56:11.244 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:56:11.247 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:56:11.249 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 8.0422ms
2025-08-05 16:56:11.250 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:11.251 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 17.8638ms
2025-08-05 16:56:36.074 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 16:56:36.081 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:56:36.083 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 8.8248ms
2025-08-05 16:56:36.092 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 104
2025-08-05 16:56:36.095 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:56:36.096 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:36.097 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:56:36.106 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:56:36.141 +03:00 [INF] Executed DbCommand (26ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialCapital], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-05 16:56:36.147 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-05 16:56:36.186 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-08-05 16:56:36.281 +03:00 [ERR] Error creating partner: احمد عبد اللطيف
System.InvalidCastException: Unable to cast object of type 'System.Threading.Tasks.Task`1[DoorCompany.Service.Dtos.ApiResponse`1[System.Int32]]' to type 'System.IConvertible'.
   at System.Convert.ToInt32(Object value)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.CreatePartnerAsycn(CreatePartnerDto createDto) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 140
2025-08-05 16:56:36.283 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:56:36.285 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 185.3383ms
2025-08-05 16:56:36.287 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:36.288 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 196.5733ms
2025-08-05 16:56:36.293 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 104
2025-08-05 16:56:36.296 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:56:36.297 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:36.298 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:56:36.304 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:56:36.307 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:56:36.309 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 8.7411ms
2025-08-05 16:56:36.310 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:36.312 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 18.1904ms
2025-08-05 16:56:36.315 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 104
2025-08-05 16:56:36.317 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:56:36.318 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:36.320 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:56:36.326 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:56:36.328 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:56:36.330 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 7.8148ms
2025-08-05 16:56:36.331 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:56:36.333 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 17.7907ms
2025-08-05 16:59:58.992 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 16:59:59.003 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:59:59.004 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 11.557ms
2025-08-05 16:59:59.014 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 86
2025-08-05 16:59:59.018 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:59:59.019 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:59:59.020 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:59:59.028 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:59:59.047 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialCapital], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-05 16:59:59.052 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-08-05 16:59:59.053 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-05 16:59:59.151 +03:00 [ERR] Error creating partner: حازم
System.InvalidCastException: Unable to cast object of type 'System.Threading.Tasks.Task`1[DoorCompany.Service.Dtos.ApiResponse`1[System.Int32]]' to type 'System.IConvertible'.
   at System.Convert.ToInt32(Object value)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.CreatePartnerAsycn(CreatePartnerDto createDto) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 140
2025-08-05 16:59:59.154 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:59:59.156 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 133.8215ms
2025-08-05 16:59:59.158 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:59:59.160 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 145.4631ms
2025-08-05 16:59:59.165 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 86
2025-08-05 16:59:59.171 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:59:59.173 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:59:59.174 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:59:59.181 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:59:59.186 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:59:59.188 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 12.4971ms
2025-08-05 16:59:59.190 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:59:59.191 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 26.3341ms
2025-08-05 16:59:59.196 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 86
2025-08-05 16:59:59.203 +03:00 [INF] CORS policy execution successful.
2025-08-05 16:59:59.204 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:59:59.205 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 16:59:59.212 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 16:59:59.217 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 16:59:59.219 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 11.4982ms
2025-08-05 16:59:59.221 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 16:59:59.222 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 25.8703ms
2025-08-05 17:01:00.782 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - null null
2025-08-05 17:01:00.788 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-05 17:01:00.786 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:01:00.798 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:00.800 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:00.802 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:00.803 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - 204 null null 21.1334ms
2025-08-05 17:01:00.804 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - 204 null null 16.5406ms
2025-08-05 17:01:00.805 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 19.8269ms
2025-08-05 17:01:00.813 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:01:00.817 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - null null
2025-08-05 17:01:00.821 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-05 17:01:00.824 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:00.827 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:00.830 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:00.831 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:01:00.832 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-05 17:01:00.836 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-05 17:01:00.838 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:01:00.844 +03:00 [INF] Route matched with {action = "GetAllPartnerBandss", controller = "PartnerBand"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerBandss(Boolean) on controller DoorCompany.Api.Controllers.PartnerBandController (DoorCompany.Api).
2025-08-05 17:01:00.846 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:01:00.848 +03:00 [INF] Route matched with {action = "GetAllPartnerTransaction", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerTransaction(DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionRequestDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:01:00.851 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:01:00.854 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 12.1704ms
2025-08-05 17:01:00.856 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:01:00.858 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 44.9809ms
2025-08-05 17:01:00.877 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerBands] AS [p]
2025-08-05 17:01:00.883 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerBandResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:01:00.888 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api) in 40.2931ms
2025-08-05 17:01:00.890 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-05 17:01:00.893 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - 200 null application/json; charset=utf-8 72.6079ms
2025-08-05 17:01:00.893 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
2025-08-05 17:01:00.967 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[ActionDetailId], [p1].[Amount], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[ImagePath], [p1].[IsActive], [p1].[IsDeleted], [p1].[Notes], [p1].[PartnerBandId], [p1].[PartnerId], [p1].[TransactionDate], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy], [p2].[Id], [p2].[CreatedAt], [p2].[CreatedBy], [p2].[Description], [p2].[IsActive], [p2].[IsDeleted], [p2].[Name], [p2].[UpdatedAt], [p2].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[Name], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy]
FROM (
    SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [PartnerTransations] AS [p]
    WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
    ORDER BY [p].[TransactionDate]
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [p1]
INNER JOIN [Partners] AS [p0] ON [p1].[PartnerId] = [p0].[Id]
INNER JOIN [PartnerBands] AS [p2] ON [p1].[PartnerBandId] = [p2].[Id]
INNER JOIN [MainActions] AS [m] ON [p1].[ActionDetailId] = [m].[Id]
ORDER BY [p1].[TransactionDate]
2025-08-05 17:01:00.977 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PagedResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:01:00.988 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api) in 134.9053ms
2025-08-05 17:01:00.990 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-05 17:01:00.991 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - 200 null application/json; charset=utf-8 174.7114ms
2025-08-05 17:01:01.606 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:01:01.611 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:01.612 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:01:01.614 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:01:01.620 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:01:01.622 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:01:01.624 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 7.7504ms
2025-08-05 17:01:01.625 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:01:01.627 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 20.3595ms
2025-08-05 17:01:34.324 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:01:34.334 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:34.336 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 11.9275ms
2025-08-05 17:01:34.347 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 81
2025-08-05 17:01:34.352 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:34.353 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:01:34.355 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:01:34.361 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:01:34.867 +03:00 [INF] Executed DbCommand (496ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialCapital], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-05 17:01:34.874 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-05 17:01:34.874 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'DoorCompany.Data.Context.ApplicationDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-08-05 17:01:34.982 +03:00 [ERR] Error creating partner: يبلي
System.InvalidCastException: Unable to cast object of type 'System.Threading.Tasks.Task`1[DoorCompany.Service.Dtos.ApiResponse`1[System.Int32]]' to type 'System.IConvertible'.
   at System.Convert.ToInt32(Object value)
   at DoorCompany.Service.Repositories.Implementations.PartnerService.CreatePartnerAsycn(CreatePartnerDto createDto) in G:\DoorAPP\DoorCompanyApp\DoorCompany.Service\Repositories\Implementations\PartnerService.cs:line 140
2025-08-05 17:01:34.984 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:01:34.986 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 629.2472ms
2025-08-05 17:01:34.988 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:01:34.990 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 642.795ms
2025-08-05 17:01:34.996 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 81
2025-08-05 17:01:34.999 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:35.005 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:01:35.007 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:01:35.013 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:01:35.019 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:01:35.021 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 12.0719ms
2025-08-05 17:01:35.023 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:01:35.025 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 28.8633ms
2025-08-05 17:01:35.030 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 81
2025-08-05 17:01:35.038 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:01:35.040 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:01:35.041 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:01:35.050 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:01:35.054 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:01:35.056 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 12.6001ms
2025-08-05 17:01:35.059 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:01:35.060 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 400 null application/json; charset=utf-8 30.0308ms
2025-08-05 17:02:19.921 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-05 17:02:20.003 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-05 17:02:20.311 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:02:20.335 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:02:20.488 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-05 17:02:20.599 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-05 17:02:20.644 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-05 17:02:20.658 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:02:20.666 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:02:20.676 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:02:20.689 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:02:20.706 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:02:20.873 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-05 17:02:21.035 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-05 17:02:21.202 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-05 17:02:21.233 +03:00 [INF] Hosting environment: Development
2025-08-05 17:02:21.235 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-05 17:02:21.276 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-05 17:02:21.479 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 215.1432ms
2025-08-05 17:02:21.496 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-05 17:02:21.505 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.8427ms
2025-08-05 17:02:21.513 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-05 17:02:21.550 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 37.7436ms
2025-08-05 17:02:21.738 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-05 17:02:21.776 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 38.1431ms
2025-08-05 17:02:34.990 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-05 17:02:35.000 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-05 17:02:35.052 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:35.062 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:02:35.063 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 73.4132ms
2025-08-05 17:02:35.091 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:35.095 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-05 17:02:35.098 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 36.1464ms
2025-08-05 17:02:35.112 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:35.115 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:02:35.118 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>
2025-08-05 17:02:35.123 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:35.287 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/Uploads/Users/<USER>
2025-08-05 17:02:35.294 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5184/Uploads/Users/<USER>
2025-08-05 17:02:35.360 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 01:57:18 م', Current time (UTC): '05/08/2025 02:02:35 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:02:35.360 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 01:57:18 م', Current time (UTC): '05/08/2025 02:02:35 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:02:35.389 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 01:57:18 م', Current time (UTC): '05/08/2025 02:02:35 م'.
2025-08-05 17:02:35.388 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 01:57:18 م', Current time (UTC): '05/08/2025 02:02:35 م'.
2025-08-05 17:02:35.395 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 01:57:18 م', Current time (UTC): '05/08/2025 02:02:35 م'.
2025-08-05 17:02:35.395 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 01:57:18 م', Current time (UTC): '05/08/2025 02:02:35 م'.
2025-08-05 17:02:35.397 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:02:35.406 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-05 17:02:35.414 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-05 17:02:35.418 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 323.5204ms
2025-08-05 17:02:35.431 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - null null
2025-08-05 17:02:35.436 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:35.437 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:02:35.438 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - 204 null null 7.1828ms
2025-08-05 17:02:35.443 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - application/json 107
2025-08-05 17:02:35.449 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:35.451 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 01:57:18 م', Current time (UTC): '05/08/2025 02:02:35 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:02:35.454 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 01:57:18 م', Current time (UTC): '05/08/2025 02:02:35 م'.
2025-08-05 17:02:35.455 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 01:57:18 م', Current time (UTC): '05/08/2025 02:02:35 م'.
2025-08-05 17:02:35.456 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-05 17:02:35.469 +03:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] RefreshToken(DoorCompany.Service.Dtos.UserDto.RefreshTokenDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-08-05 17:02:35.671 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:02:35.704 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:02:35.749 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 304.9871ms
2025-08-05 17:02:35.752 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:02:35.755 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 641.1508ms
2025-08-05 17:02:35.847 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-05 17:02:35.917 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__refreshTokenDto_RefreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshTokenDto_RefreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-05 17:02:35.962 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-05 17:02:36.016 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__refreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-05 17:02:36.124 +03:00 [INF] Access token generated for user 1
2025-08-05 17:02:36.126 +03:00 [INF] Access token refreshed for user 1
2025-08-05 17:02:36.128 +03:00 [INF] Token refreshed successfully for user: 1
2025-08-05 17:02:36.130 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:02:36.144 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api) in 672.3038ms
2025-08-05 17:02:36.149 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-05 17:02:36.151 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - 200 null application/json; charset=utf-8 707.3795ms
2025-08-05 17:02:36.156 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-05 17:02:36.160 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:36.178 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-05 17:02:36.184 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-05 17:02:36.233 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-05 17:02:36.246 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:02:36.253 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 66.0664ms
2025-08-05 17:02:36.255 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-05 17:02:36.259 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-05 17:02:36.262 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:36.263 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 106.5568ms
2025-08-05 17:02:36.268 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-05 17:02:36.269 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-05 17:02:36.282 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-05 17:02:36.287 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:02:36.292 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 17.9271ms
2025-08-05 17:02:36.296 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-05 17:02:36.298 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 43.811ms
2025-08-05 17:02:59.422 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:02:59.433 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:59.435 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 13.4186ms
2025-08-05 17:02:59.447 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 85
2025-08-05 17:02:59.451 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:02:59.453 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:02:59.460 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:03:04.069 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:03:14.465 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialCapital], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-05 17:03:22.373 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-05 17:03:32.078 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:05:22.016 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-05 17:05:22.098 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-05 17:05:22.397 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:05:22.419 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:05:22.573 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-05 17:05:22.688 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-05 17:05:22.732 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-05 17:05:22.745 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:05:22.753 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:05:22.764 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:05:22.772 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:05:22.780 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:05:22.925 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-05 17:05:23.100 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-05 17:05:23.210 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-05 17:05:23.212 +03:00 [INF] Hosting environment: Development
2025-08-05 17:05:23.214 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-05 17:05:23.472 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-05 17:05:23.666 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 203.0664ms
2025-08-05 17:05:23.689 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-05 17:05:23.695 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-05 17:05:23.697 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 7.8516ms
2025-08-05 17:05:23.741 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 45.6746ms
2025-08-05 17:05:23.906 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-05 17:05:23.947 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 41.0003ms
2025-08-05 17:05:33.108 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - null null
2025-08-05 17:05:33.110 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:05:33.114 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-05 17:05:33.129 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-05 17:05:33.202 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:05:33.202 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:05:33.205 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:05:33.210 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - 204 null null 96.2747ms
2025-08-05 17:05:33.212 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 101.8912ms
2025-08-05 17:05:33.212 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - 204 null null 104.3351ms
2025-08-05 17:05:33.226 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-05 17:05:33.229 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - null null
2025-08-05 17:05:33.236 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:05:33.244 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:05:33.244 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:05:33.246 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:05:33.400 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-05 17:05:33.400 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:05:33.400 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-05 17:05:33.426 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:05:33.426 +03:00 [INF] Route matched with {action = "GetAllPartnerBandss", controller = "PartnerBand"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerBandss(Boolean) on controller DoorCompany.Api.Controllers.PartnerBandController (DoorCompany.Api).
2025-08-05 17:05:33.427 +03:00 [INF] Route matched with {action = "GetAllPartnerTransaction", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerTransaction(DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionRequestDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:05:33.615 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerBands] AS [p]
2025-08-05 17:05:33.635 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:05:33.658 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:05:33.658 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerBandResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:05:33.695 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
2025-08-05 17:05:33.706 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api) in 271.1738ms
2025-08-05 17:05:33.706 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 273.0374ms
2025-08-05 17:05:33.709 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-05 17:05:33.710 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:05:33.714 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 478.0955ms
2025-08-05 17:05:33.714 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - 200 null application/json; charset=utf-8 488.5273ms
2025-08-05 17:05:33.819 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[ActionDetailId], [p1].[Amount], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[ImagePath], [p1].[IsActive], [p1].[IsDeleted], [p1].[Notes], [p1].[PartnerBandId], [p1].[PartnerId], [p1].[TransactionDate], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy], [p2].[Id], [p2].[CreatedAt], [p2].[CreatedBy], [p2].[Description], [p2].[IsActive], [p2].[IsDeleted], [p2].[Name], [p2].[UpdatedAt], [p2].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[Name], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy]
FROM (
    SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [PartnerTransations] AS [p]
    WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
    ORDER BY [p].[TransactionDate]
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [p1]
INNER JOIN [Partners] AS [p0] ON [p1].[PartnerId] = [p0].[Id]
INNER JOIN [PartnerBands] AS [p2] ON [p1].[PartnerBandId] = [p2].[Id]
INNER JOIN [MainActions] AS [m] ON [p1].[ActionDetailId] = [m].[Id]
ORDER BY [p1].[TransactionDate]
2025-08-05 17:05:33.831 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PagedResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:05:33.841 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api) in 408.7353ms
2025-08-05 17:05:33.847 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-05 17:05:33.848 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - 200 null application/json; charset=utf-8 618.8681ms
2025-08-05 17:05:34.107 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:05:34.113 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:05:34.115 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:05:34.117 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:05:34.152 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:05:34.157 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:05:34.160 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 40.5554ms
2025-08-05 17:05:34.162 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:05:34.163 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 55.97ms
2025-08-05 17:05:53.984 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:05:53.989 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:05:53.991 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 6.9537ms
2025-08-05 17:05:53.996 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 88
2025-08-05 17:05:54.000 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:05:54.002 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:05:54.011 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:05:56.600 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:05:58.961 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialCapital], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-05 17:05:59.935 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-05 17:06:06.040 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:10:13.747 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-05 17:10:13.853 +03:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-05 17:10:14.189 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:10:14.213 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:10:14.406 +03:00 [INF] Executed DbCommand (47ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-05 17:10:14.511 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-05 17:10:14.559 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-05 17:10:14.573 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:10:14.584 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:10:14.595 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:10:14.607 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:10:14.621 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:10:14.770 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-05 17:10:14.927 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-05 17:10:15.050 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-05 17:10:15.052 +03:00 [INF] Hosting environment: Development
2025-08-05 17:10:15.053 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-05 17:10:15.283 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-05 17:10:15.494 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 220.4619ms
2025-08-05 17:10:15.517 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-05 17:10:15.523 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-05 17:10:15.525 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 8.1659ms
2025-08-05 17:10:15.565 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.3224ms
2025-08-05 17:10:15.743 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-05 17:10:15.787 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 43.6687ms
2025-08-05 17:10:39.688 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:10:39.699 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-05 17:10:39.735 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:10:39.739 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 51.815ms
2025-08-05 17:10:39.753 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Partner - application/json 83
2025-08-05 17:10:39.757 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:10:39.892 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:10:39 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:10:39.919 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:10:39 م'.
2025-08-05 17:10:39.925 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:10:39 م'.
2025-08-05 17:10:39.930 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:10:39.963 +03:00 [INF] Route matched with {action = "CreatePartnerAsycn", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerAsycn(DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:10:43.365 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__createDto_Name_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Name] = @__createDto_Name_0 AND [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:10:44.693 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialCapital], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-05 17:10:45.428 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-05 17:10:50.376 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:10:51.965 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Int32), @p7='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime2), @p10='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [ShareDistributions] ([CreatedAt], [CreatedBy], [Description], [DistributionDate], [IsActive], [IsDeleted], [PartnerId], [ShareValue], [SharesCount], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-08-05 17:10:53.468 +03:00 [INF] Partner created successfully: كريم
2025-08-05 17:10:54.183 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[InitialCapital], [p1].[IsActive], [p1].[IsDeleted], [p1].[Name], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [Partners] AS [p]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [p1]
LEFT JOIN [PartnerTransations] AS [p0] ON [p1].[Id] = [p0].[PartnerId]
ORDER BY [p1].[Id]
2025-08-05 17:10:54.194 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerBandId], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-08-05 17:10:55.932 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:10:55.970 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api) in 15999.047ms
2025-08-05 17:10:55.973 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.CreatePartnerAsycn (DoorCompany.Api)'
2025-08-05 17:10:55.978 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 16225.8802ms
2025-08-05 17:10:56.550 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:10:56.555 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:10:56.557 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 6.5451ms
2025-08-05 17:10:56.561 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:10:56.567 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:10:56.571 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:10:56 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:10:56.574 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:10:56 م'.
2025-08-05 17:10:56.575 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:10:56 م'.
2025-08-05 17:10:56.576 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:10:56.581 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:10:56.668 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:10:56.678 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:10:56.684 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 99.5447ms
2025-08-05 17:10:56.686 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:10:56.687 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 126.3236ms
2025-08-05 17:11:24.447 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/10 - null null
2025-08-05 17:11:24.454 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:24.455 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/10 - 204 null null 8.4994ms
2025-08-05 17:11:24.468 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/10 - null null
2025-08-05 17:11:24.475 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:24.478 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:24 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:24.481 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:24 م'.
2025-08-05 17:11:24.483 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:24 م'.
2025-08-05 17:11:24.484 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:24.493 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:24.526 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:24.546 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:24.675 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@p7='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialCapital] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [Name] = @p4, [UpdatedAt] = @p5, [UpdatedBy] = @p6
OUTPUT 1
WHERE [Id] = @p7;
2025-08-05 17:11:24.684 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:24.695 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 198.6044ms
2025-08-05 17:11:24.701 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:24.702 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/10 - 200 null application/json; charset=utf-8 234.2699ms
2025-08-05 17:11:24.709 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:11:24.712 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:24.713 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 3.9379ms
2025-08-05 17:11:24.720 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:11:24.723 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:24.725 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:24 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:24.729 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:24 م'.
2025-08-05 17:11:24.730 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:24 م'.
2025-08-05 17:11:24.736 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:24.738 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:24.744 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:11:24.750 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:24.753 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 12.5373ms
2025-08-05 17:11:24.755 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:24.757 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 36.6074ms
2025-08-05 17:11:26.424 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/8 - null null
2025-08-05 17:11:26.428 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:26.430 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/8 - 204 null null 6.242ms
2025-08-05 17:11:26.437 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/8 - null null
2025-08-05 17:11:26.440 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:26.442 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:26 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:26.446 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:26 م'.
2025-08-05 17:11:26.451 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:26 م'.
2025-08-05 17:11:26.453 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:26.455 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:26.462 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:26.471 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:26.486 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@p7='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialCapital] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [Name] = @p4, [UpdatedAt] = @p5, [UpdatedBy] = @p6
OUTPUT 1
WHERE [Id] = @p7;
2025-08-05 17:11:26.489 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:26.491 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 33.7062ms
2025-08-05 17:11:26.492 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:26.494 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/8 - 200 null application/json; charset=utf-8 57.4782ms
2025-08-05 17:11:26.502 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:11:26.505 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:26.507 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:26 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:26.510 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:26 م'.
2025-08-05 17:11:26.515 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:26 م'.
2025-08-05 17:11:26.542 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:26.544 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:26.550 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:11:26.553 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:26.555 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 8.7643ms
2025-08-05 17:11:26.561 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:26.563 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 61.0175ms
2025-08-05 17:11:28.727 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/7 - null null
2025-08-05 17:11:28.732 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:28.734 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/7 - 204 null null 6.2892ms
2025-08-05 17:11:28.739 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/7 - null null
2025-08-05 17:11:28.742 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:28.744 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:28 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:28.752 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:28 م'.
2025-08-05 17:11:28.753 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:28 م'.
2025-08-05 17:11:28.755 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:28.756 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:28.764 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:28.771 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:28.786 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@p7='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialCapital] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [Name] = @p4, [UpdatedAt] = @p5, [UpdatedBy] = @p6
OUTPUT 1
WHERE [Id] = @p7;
2025-08-05 17:11:28.789 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:28.791 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 31.6167ms
2025-08-05 17:11:28.795 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:28.797 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/7 - 200 null application/json; charset=utf-8 58.1547ms
2025-08-05 17:11:28.803 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:11:28.807 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:28.811 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:28 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:28.814 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:28 م'.
2025-08-05 17:11:28.816 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:28 م'.
2025-08-05 17:11:28.818 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:28.820 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:28.827 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:11:28.830 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:28.831 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 8.9421ms
2025-08-05 17:11:28.833 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:28.835 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 31.1713ms
2025-08-05 17:11:30.788 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/9 - null null
2025-08-05 17:11:30.792 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:30.793 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/9 - 204 null null 5.5675ms
2025-08-05 17:11:30.798 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/9 - null null
2025-08-05 17:11:30.802 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:30.804 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:30 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:30.810 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:30 م'.
2025-08-05 17:11:30.812 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:30 م'.
2025-08-05 17:11:30.813 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:30.815 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:30.823 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:30.828 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:30.842 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@p7='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialCapital] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [Name] = @p4, [UpdatedAt] = @p5, [UpdatedBy] = @p6
OUTPUT 1
WHERE [Id] = @p7;
2025-08-05 17:11:30.845 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:30.847 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 28.9582ms
2025-08-05 17:11:30.849 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:30.850 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/9 - 200 null application/json; charset=utf-8 52.4603ms
2025-08-05 17:11:30.858 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:11:30.861 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:30.862 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 4.0454ms
2025-08-05 17:11:30.869 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:11:30.873 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:30.875 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:30 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:30.877 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:30 م'.
2025-08-05 17:11:30.878 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:30 م'.
2025-08-05 17:11:30.880 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:30.881 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:30.893 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:11:30.896 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:30.900 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 11.3971ms
2025-08-05 17:11:30.902 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:30.904 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 35.174ms
2025-08-05 17:11:32.687 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/6 - null null
2025-08-05 17:11:32.691 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:32.693 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/6 - 204 null null 5.9825ms
2025-08-05 17:11:32.697 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/6 - null null
2025-08-05 17:11:32.701 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:32.702 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:32 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:32.709 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:32 م'.
2025-08-05 17:11:32.711 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:32 م'.
2025-08-05 17:11:32.712 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:32.714 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:32.720 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:32.728 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:32.742 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@p7='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialCapital] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [Name] = @p4, [UpdatedAt] = @p5, [UpdatedBy] = @p6
OUTPUT 1
WHERE [Id] = @p7;
2025-08-05 17:11:32.745 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:32.747 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 31.1968ms
2025-08-05 17:11:32.749 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:32.751 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/6 - 200 null application/json; charset=utf-8 53.5922ms
2025-08-05 17:11:32.761 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:11:32.764 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:32.766 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:32 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:32.771 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:32 م'.
2025-08-05 17:11:32.772 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:32 م'.
2025-08-05 17:11:32.774 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:32.775 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:32.784 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:11:32.791 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:32.793 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 14.679ms
2025-08-05 17:11:32.794 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:32.796 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 34.6699ms
2025-08-05 17:11:34.687 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/5 - null null
2025-08-05 17:11:34.691 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:34.693 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/5 - 204 null null 6.0042ms
2025-08-05 17:11:34.697 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/5 - null null
2025-08-05 17:11:34.701 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:34.703 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:34 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:34.706 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:34 م'.
2025-08-05 17:11:34.712 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:34 م'.
2025-08-05 17:11:34.714 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:34.716 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:34.723 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:34.731 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:34.742 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p7='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialCapital] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [Name] = @p4, [UpdatedAt] = @p5, [UpdatedBy] = @p6
OUTPUT 1
WHERE [Id] = @p7;
2025-08-05 17:11:34.746 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:34.748 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 29.0972ms
2025-08-05 17:11:34.750 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:34.751 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/5 - 200 null application/json; charset=utf-8 53.9836ms
2025-08-05 17:11:34.762 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:11:34.765 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:34.767 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:34 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:34.774 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:34 م'.
2025-08-05 17:11:34.776 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:34 م'.
2025-08-05 17:11:34.777 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:34.779 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:34.786 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:11:34.794 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:34.796 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 14.5967ms
2025-08-05 17:11:34.799 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:34.800 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 38.581ms
2025-08-05 17:11:36.455 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/4 - null null
2025-08-05 17:11:36.459 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:36.461 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/4 - 204 null null 6.0839ms
2025-08-05 17:11:36.466 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/4 - null null
2025-08-05 17:11:36.470 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:36.472 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:36 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:36.477 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:36 م'.
2025-08-05 17:11:36.479 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:36 م'.
2025-08-05 17:11:36.480 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:36.482 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:36.490 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:36.497 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:36.508 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p7='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialCapital] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [Name] = @p4, [UpdatedAt] = @p5, [UpdatedBy] = @p6
OUTPUT 1
WHERE [Id] = @p7;
2025-08-05 17:11:36.512 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:36.514 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 28.928ms
2025-08-05 17:11:36.516 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:36.517 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/4 - 200 null application/json; charset=utf-8 51.7832ms
2025-08-05 17:11:36.525 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:11:36.528 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:36.530 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 4.1979ms
2025-08-05 17:11:36.534 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:11:36.540 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:36.542 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:36 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:36.544 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:36 م'.
2025-08-05 17:11:36.546 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:36 م'.
2025-08-05 17:11:36.547 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:36.548 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:36.559 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:11:36.561 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:36.563 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 7.8053ms
2025-08-05 17:11:36.564 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:36.570 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 35.724ms
2025-08-05 17:11:38.166 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/2 - null null
2025-08-05 17:11:38.170 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:38.172 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/2 - 204 null null 6.058ms
2025-08-05 17:11:38.177 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/2 - null null
2025-08-05 17:11:38.180 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:38.182 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:38 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:38.188 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:38 م'.
2025-08-05 17:11:38.189 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:38 م'.
2025-08-05 17:11:38.190 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:38.192 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:38.198 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:38.208 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:38.211 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:38.213 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 18.5335ms
2025-08-05 17:11:38.219 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:38.221 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/2 - 400 null application/json; charset=utf-8 43.7628ms
2025-08-05 17:11:38.226 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/2 - null null
2025-08-05 17:11:38.231 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:38.233 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:38 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:38.236 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:38 م'.
2025-08-05 17:11:38.238 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:38 م'.
2025-08-05 17:11:38.239 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:38.240 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:38.246 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:38.254 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:38.256 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:38.258 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 15.7015ms
2025-08-05 17:11:38.260 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:38.264 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/2 - 400 null application/json; charset=utf-8 38.261ms
2025-08-05 17:11:38.270 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/2 - null null
2025-08-05 17:11:38.272 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:38.274 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:38 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:38.277 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:38 م'.
2025-08-05 17:11:38.281 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:38 م'.
2025-08-05 17:11:38.282 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:38.284 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:38.290 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:38.294 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:38.297 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:38.298 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 12.2917ms
2025-08-05 17:11:38.300 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:38.302 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/2 - 400 null application/json; charset=utf-8 32.3036ms
2025-08-05 17:11:46.014 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/1 - null null
2025-08-05 17:11:46.019 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:46.020 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/1 - 204 null null 6.1895ms
2025-08-05 17:11:46.025 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/1 - null null
2025-08-05 17:11:46.028 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:46.029 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:46 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:46.032 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:46 م'.
2025-08-05 17:11:46.037 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:46 م'.
2025-08-05 17:11:46.039 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:46.040 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:46.047 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:46.055 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:46.059 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:46.060 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 17.3698ms
2025-08-05 17:11:46.062 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:46.069 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/1 - 400 null application/json; charset=utf-8 43.7839ms
2025-08-05 17:11:46.074 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/1 - null null
2025-08-05 17:11:46.078 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:46.081 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:46 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:46.085 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:46 م'.
2025-08-05 17:11:46.087 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:46 م'.
2025-08-05 17:11:46.089 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:46.090 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:46.097 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:46.103 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:46.105 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:46.106 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 13.4842ms
2025-08-05 17:11:46.108 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:46.113 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/1 - 400 null application/json; charset=utf-8 38.8462ms
2025-08-05 17:11:46.119 +03:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:5184/api/Partner/1 - null null
2025-08-05 17:11:46.123 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:46.124 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:46 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:46.129 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:46 م'.
2025-08-05 17:11:46.131 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:46 م'.
2025-08-05 17:11:46.134 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:46.135 +03:00 [INF] Route matched with {action = "DeletePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DeletePartnerAsync(Int32) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:46.142 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-08-05 17:11:46.151 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [PartnerTransations] AS [p]
        WHERE [p].[PartnerId] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-05 17:11:46.154 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:46.155 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api) in 16.623ms
2025-08-05 17:11:46.160 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.DeletePartnerAsync (DoorCompany.Api)'
2025-08-05 17:11:46.161 +03:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:5184/api/Partner/1 - 400 null application/json; charset=utf-8 41.6128ms
2025-08-05 17:11:50.741 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - null null
2025-08-05 17:11:50.743 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - null null
2025-08-05 17:11:50.752 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-05 17:11:50.758 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:50.764 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:50.769 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:50.770 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - 204 null null 28.6178ms
2025-08-05 17:11:50.771 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner - 204 null null 27.8079ms
2025-08-05 17:11:50.772 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - 204 null null 19.3227ms
2025-08-05 17:11:50.777 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-05 17:11:50.783 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - null null
2025-08-05 17:11:50.787 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:11:50.790 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:50.794 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:50.800 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:50.802 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:50 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:50.803 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:50 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:50.805 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:50 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:50.807 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:50 م'.
2025-08-05 17:11:50.810 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:50 م'.
2025-08-05 17:11:50.818 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:50 م'.
2025-08-05 17:11:50.819 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:50 م'.
2025-08-05 17:11:50.820 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:50 م'.
2025-08-05 17:11:50.822 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:50 م'.
2025-08-05 17:11:50.823 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-05 17:11:50.825 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-05 17:11:50.830 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:50.835 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:50.837 +03:00 [INF] Route matched with {action = "GetAllPartnerBandss", controller = "PartnerBand"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerBandss(Boolean) on controller DoorCompany.Api.Controllers.PartnerBandController (DoorCompany.Api).
2025-08-05 17:11:50.841 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:11:50.843 +03:00 [INF] Route matched with {action = "GetAllPartnerTransaction", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerTransaction(DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionRequestDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:50.850 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:50.853 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 15.9446ms
2025-08-05 17:11:50.856 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:50.862 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 75.1589ms
2025-08-05 17:11:50.876 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerBands] AS [p]
2025-08-05 17:11:50.904 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerBandResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:50.913 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api) in 72.1784ms
2025-08-05 17:11:50.918 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-05 17:11:50.923 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - 200 null application/json; charset=utf-8 146.731ms
2025-08-05 17:11:50.934 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
2025-08-05 17:11:51.010 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[ActionDetailId], [p1].[Amount], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[ImagePath], [p1].[IsActive], [p1].[IsDeleted], [p1].[Notes], [p1].[PartnerBandId], [p1].[PartnerId], [p1].[TransactionDate], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy], [p2].[Id], [p2].[CreatedAt], [p2].[CreatedBy], [p2].[Description], [p2].[IsActive], [p2].[IsDeleted], [p2].[Name], [p2].[UpdatedAt], [p2].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[Name], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy]
FROM (
    SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [PartnerTransations] AS [p]
    WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
    ORDER BY [p].[TransactionDate]
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [p1]
INNER JOIN [Partners] AS [p0] ON [p1].[PartnerId] = [p0].[Id]
INNER JOIN [PartnerBands] AS [p2] ON [p1].[PartnerBandId] = [p2].[Id]
INNER JOIN [MainActions] AS [m] ON [p1].[ActionDetailId] = [m].[Id]
ORDER BY [p1].[TransactionDate]
2025-08-05 17:11:51.021 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PagedResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-05 17:11:51.030 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api) in 178.5145ms
2025-08-05 17:11:51.033 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-05 17:11:51.035 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-05&toDate=2025-08-05 - 200 null application/json; charset=utf-8 252.5442ms
2025-08-05 17:11:51.534 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner - null null
2025-08-05 17:11:51.539 +03:00 [INF] CORS policy execution successful.
2025-08-05 17:11:51.541 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:51 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-08-05 17:11:51.550 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:51 م'.
2025-08-05 17:11:51.551 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/08/2025 02:04:36 م', Current time (UTC): '05/08/2025 02:11:51 م'.
2025-08-05 17:11:51.553 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:51.554 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-05 17:11:51.562 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-05 17:11:51.565 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-05 17:11:51.568 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 10.7481ms
2025-08-05 17:11:51.569 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-05 17:11:51.571 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner - 200 null application/json; charset=utf-8 36.7805ms
