﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos.RoleDto;
using DoorCompany.Service.Dtos.UserDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
   // [Authorize]
    public class UserController : AppControllerBase
    {
        public UserController(IUnitOfWork work) : base(work)
        {
        }

        #region User Management

        /// <summary>
        /// Get all users
        /// </summary>
        [HttpGet("all-users")]
        public async Task<IActionResult> GetAllUsers()
        {
            var response = await _work.UserRepository.GetAllUsersAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        [HttpGet("{id}")]     
        public async Task<IActionResult> GetUserById(int id)
        {
            var response = await _work.UserRepository.GetUserByIdAsync(id);
            return NewResult(response);
        }


        /// <summary>
        /// Create new user
        /// </summary>
        [HttpPost]
        //[AllowAnonymous]
        public async Task<IActionResult> CreateUser(CreateUserDto userDto)
        {
            var response = await _work.UserRepository.CreateUserAsync(userDto);
            return NewResult(response);
        }

        /// <summary>
        /// Update user
        /// </summary>
        [HttpPut]
        public async Task<IActionResult> UpdateUser(UpdateUserDto userDto)
        {
            var response = await _work.UserRepository.UpdateUserAsync(userDto);
            return NewResult(response);
        }
        /// <summary>
        /// Delete user
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var response = await _work.UserRepository.DeleteUserAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// Change password
        /// </summary>
        [HttpPost("change-password")]
        [Authorize]
        public async Task<IActionResult> ChangePassword(ChangePasswordDto changePasswordDto)
        {
            var response = await _work.UserRepository.ChangePasswordAsync(changePasswordDto);
            return NewResult(response);
        }

        /// <summary>
        /// Reset password (Admin only)
        /// </summary>
        [HttpPost("reset-password")]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest request)
        {
            var response = await _work.UserRepository.ResetPasswordAsync(request.UserId, request.NewPassword);
            return NewResult(response);
        }

        // Helper classes for request models
        public class ResetPasswordRequest
        {
            public int UserId { get; set; }
            public string NewPassword { get; set; } = string.Empty;
        }



        /// <summary>
        /// Get  UserRoles bY UserId
        /// </summary>

        [HttpGet("All-UserRoles")]
        public async Task<IActionResult> GetAllUserRolesAsync()
        {
            var response = await _work.UserRepository.GetAllUserRoleAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get  UserRoles bY UserId
        /// </summary>

        [HttpGet("{userId}/user-Roles")]
        public async Task<IActionResult> GetUserRoleById(int userId)
        {
            var response = await _work.UserRepository.GetUserRoleByIdAsync(userId);
            return NewResult(response);
        }
        /// <summary>
        /// Create new UserRoles
        /// </summary>
        [HttpPost("add-Remove-user-Roles")]
        //[AllowAnonymous]
        public async Task<IActionResult> AddAndRemoveUserRole(UpdateUserRolesDto updateUserRolesDto)
        {
            var response = await _work.UserRepository.UpdateUserRoleAsync(updateUserRolesDto);
            return NewResult(response);
        }



        #endregion

        /// <summary>
        /// Get current user profile
        /// </summary>
        [HttpGet("profile")]
        [Authorize]
        public async Task<IActionResult> GetProfile()
        {
            var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
            var response = await _work.UserRepository.GetUserByIdAsync(userId);
            return NewResult(response);
        }

        /// <summary>
        /// Update current user profile
        /// </summary>
        [HttpPut("profile")]
        [Authorize]
        public async Task<IActionResult> UpdateProfile([FromForm] UpdateUserProfileDto updateDto)
        {
            var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
            updateDto.Id = userId;

            var response = await _work.UserRepository.UpdateUserProfileAsync(updateDto);
            return NewResult(response);
        } 
        /// <summary>
        /// Update current user profile
        /// </summary>
        [HttpPut("profile-image")]
        [Authorize]
        public async Task<IActionResult> UpdateProfileImage([FromForm] ChangeProfileImageDto updateDto)
        {
            var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
            updateDto.Id = userId;

            var response = await _work.UserRepository.UpdateProfileImage(updateDto);
            return NewResult(response);
        }


    }
}
