using AutoMapper;
using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.InventoryDto;
using DoorCompany.Service.Dtos.ProductDto;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.EntityFrameworkCore;

namespace DoorCompany.Service.Repositories.Implementations
{
    public class InvoiceService :ResponseHandler, IInvoiceRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IInventoryRepository _inventoryService;
        private readonly IFinancialRepository _financialService;

        public InvoiceService(IUnitOfWorkOfService unitOfWork, IMapper mapper, 
            IInventoryRepository inventoryService, IFinancialRepository financialService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _inventoryService = inventoryService;
            _financialService = financialService;
        }

        public async Task<ApiResponse<InvoiceResponseDto>> CreateInvoiceAsync(CreateInvoiceMasterDto createDto)
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // التحقق من توفر المنتجات (للمبيعات)
                if (createDto.InvoiceType == InvoiceType.Sale)
                {
                    var productsToCheck = createDto.Items.Select(i => new ProductQuantityDto
                    {
                        ProductId = i.ProductId,
                        Quantity = i.Quantity
                    }).ToList();

                    var availabilityCheck = await _inventoryService.CheckProductsAvailabilityAsync(productsToCheck);
                    if (!availabilityCheck.Succeeded)
                        return BadRequest<InvoiceResponseDto>(availabilityCheck.Message);

                    var unavailableProducts = availabilityCheck.Data.Where(p => !p.IsAvailable).ToList();
                    if (unavailableProducts.Any())
                    {
                        var errorMessage = "المنتجات التالية غير متوفرة بالكمية المطلوبة:\n" +
                            string.Join("\n", unavailableProducts.Select(p => $"{p.ProductName}: مطلوب {p.RequiredQuantity}, متوفر {p.AvailableQuantity}"));
                        return BadRequest<InvoiceResponseDto>(errorMessage);
                    }
                }

                // إنشاء الفاتورة
                var invoice = _mapper.Map<InvoiceMaster>(createDto);
                
                // توليد رقم الفاتورة إذا لم يتم تحديده
                if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                {
                    invoice.InvoiceNumber = await GenerateInvoiceNumberAsync(createDto.InvoiceType);
                }

                await _unitOfWork.InvoiceMasters.AddAsync(invoice);
                await _unitOfWork.SaveChangesAsync();

                // إضافة أصناف الفاتورة
                foreach (var itemDto in createDto.Items)
                {
                    var item = _mapper.Map<InvoiceItem>(itemDto);
                    item.InvoiceMasterId = invoice.Id;
                    await _unitOfWork.InvoiceItems.AddAsync(item);
                }

                await _unitOfWork.SaveChangesAsync();

                // تحديث المخزون
                await UpdateInventoryForInvoice(invoice);

                // تسجيل المعاملة المالية إذا كان هناك دفع
                if (createDto.PaidAmount > 0)
                {
                    await _financialService.RecordInvoicePaymentAsync(invoice.Id, createDto.PaidAmount, createDto.PaymentType);
                }

                await _unitOfWork.CommitTransactionAsync();

                // إرجاع الفاتورة مع التفاصيل
                var result = await GetInvoiceByIdAsync(invoice.Id);
                return Success(result.Data, "تم إنشاء الفاتورة بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest<InvoiceResponseDto>($"خطأ في إنشاء الفاتورة: {ex.Message}");
            }
        }

        public async Task<ApiResponse<InvoiceResponseDto>> UpdateInvoiceAsync(int invoiceId, UpdateInvoiceMasterDto updateDto)
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync();

                var invoice = await _unitOfWork.InvoiceMasters
                    .GetTableAsTracking()
                    .Include(i => i.Items)
                    .FirstOrDefaultAsync(i => i.Id == invoiceId);

                if (invoice == null)
                    return NotFound<InvoiceResponseDto>("الفاتورة غير موجودة");

                // حفظ البيانات القديمة لإلغاء تأثيرها على المخزون
                var oldItems = invoice.Items.ToList();
                var oldInvoiceType = invoice.InvoiceType;

                // إلغاء تأثير الفاتورة القديمة على المخزون
                await ReverseInventoryForInvoice(invoice, oldItems);

                // تحديث بيانات الفاتورة
                _mapper.Map(updateDto, invoice);

                // حذف الأصناف القديمة
                foreach (var oldItem in oldItems)
                {
                  await  _unitOfWork.InvoiceItems.DeleteAsync(oldItem);
                }

                // إضافة الأصناف الجديدة
                foreach (var itemDto in updateDto.Items)
                {
                    var item = _mapper.Map<InvoiceItem>(itemDto);
                    item.InvoiceMasterId = invoice.Id;
                    await _unitOfWork.InvoiceItems.AddAsync(item);
                }

                await _unitOfWork.SaveChangesAsync();

                // التحقق من توفر المنتجات (للمبيعات)
                if (updateDto.InvoiceType == InvoiceType.Sale)
                {
                    var productsToCheck = updateDto.Items.Select(i => new ProductQuantityDto
                    {
                        ProductId = i.ProductId,
                        Quantity = i.Quantity
                    }).ToList();

                    var availabilityCheck = await _inventoryService.CheckProductsAvailabilityAsync(productsToCheck);
                    if (!availabilityCheck.Succeeded)
                    {
                        await _unitOfWork.RollbackTransactionAsync();
                        return BadRequest<InvoiceResponseDto>(availabilityCheck.Message);
                    }

                    var unavailableProducts = availabilityCheck.Data.Where(p => !p.IsAvailable).ToList();
                    if (unavailableProducts.Any())
                    {
                        await _unitOfWork.RollbackTransactionAsync();
                        var errorMessage = "المنتجات التالية غير متوفرة بالكمية المطلوبة:\n" +
                            string.Join("\n", unavailableProducts.Select(p => $"{p.ProductName}: مطلوب {p.RequiredQuantity}, متوفر {p.AvailableQuantity}"));
                        return BadRequest<InvoiceResponseDto>(errorMessage);
                    }
                }

                // تطبيق التأثير الجديد على المخزون
                await UpdateInventoryForInvoice(invoice);

                await _unitOfWork.CommitTransactionAsync();

                // إرجاع الفاتورة المحدثة
                var result = await GetInvoiceByIdAsync(invoice.Id);
                return Success(result.Data, "تم تحديث الفاتورة بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest<InvoiceResponseDto>($"خطأ في تحديث الفاتورة: {ex.Message}");
            }
        }

        public async Task<ApiResponse<bool>> DeleteInvoiceAsync(int invoiceId)
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync();

                var invoice = await _unitOfWork.InvoiceMasters
                    .GetTableAsTracking()
                    .Include(i => i.Items)
                    .FirstOrDefaultAsync(i => i.Id == invoiceId);

                if (invoice == null)
                    return NotFound<bool>("الفاتورة غير موجودة");

                // إلغاء تأثير الفاتورة على المخزون
                await ReverseInventoryForInvoice(invoice, invoice.Items.ToList());

                // حذف المعاملات المالية المرتبطة
                var financialTransactions = await _unitOfWork.FinancialTransactions
                    .GetTableAsTracking()
                    .Where(t => t.ReferenceType == (invoice.InvoiceType == InvoiceType.Sale ? ReferenceType.Sale : ReferenceType.Purchase) 
                               && t.ReferenceId == invoiceId)
                    .ToListAsync();

                foreach (var transaction in financialTransactions)
                {
                  await  _unitOfWork.FinancialTransactions.DeleteAsync(transaction);
                }

                // حذف أصناف الفاتورة
                foreach (var item in invoice.Items)
                {
                await    _unitOfWork.InvoiceItems.DeleteAsync(item);
                }

                // حذف الفاتورة
              await  _unitOfWork.InvoiceMasters.DeleteAsync(invoice);

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return Success(true, "تم حذف الفاتورة بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest<bool>($"خطأ في حذف الفاتورة: {ex.Message}");
            }
        }

        public async Task<ApiResponse<InvoiceResponseDto>> GetInvoiceByIdAsync(int invoiceId)
        {
            try
            {
                var invoice = await _unitOfWork.InvoiceMasters
                    .GetTableNoTracking()
                    .Include(i => i.Party)
                    .Include(i => i.Items)
                        .ThenInclude(item => item.Product)
                    .FirstOrDefaultAsync(i => i.Id == invoiceId);

                if (invoice == null)
                    return NotFound<InvoiceResponseDto>("الفاتورة غير موجودة");

                var result = _mapper.Map<InvoiceResponseDto>(invoice);
                result.PartyName = invoice.Party.Name;

                return Success(result);
            }
            catch (Exception ex)
            {
                return BadRequest<InvoiceResponseDto>($"خطأ في الحصول على الفاتورة: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResponse<InvoiceResponseDto>>> GetInvoicesAsync(InvoiceFilterDto filterDto)
        {
            try
            {
                var query = _unitOfWork.InvoiceMasters
                    .GetTableNoTracking();


                if (!string.IsNullOrEmpty(filterDto.InvoiceNumber))
                    query = query.Where(i => i.InvoiceNumber.Contains(filterDto.InvoiceNumber));

                if (filterDto.InvoiceType.HasValue)
                    query = query.Where(i => i.InvoiceType == filterDto.InvoiceType.Value);

                if (filterDto.FromDate.HasValue)
                    query = query.Where(i => i.InvoiceDate >= filterDto.FromDate.Value);

                if (filterDto.ToDate.HasValue)
                    query = query.Where(i => i.InvoiceDate <= filterDto.ToDate.Value);

                if (filterDto.PartyId.HasValue)
                    query = query.Where(i => i.PartyId == filterDto.PartyId.Value);

                if (filterDto.IsPaid.HasValue)
                    query = query.Where(i => i.IsPaid == filterDto.IsPaid.Value);

                if (filterDto.PaymentType.HasValue)
                    query = query.Where(i => i.PaymentType == filterDto.PaymentType.Value);

                query.Include(i => i.Party);

                if (!string.IsNullOrEmpty(filterDto.SearchTerm))
                    query = query.Where(i => i.Party.Name.Contains(filterDto.SearchTerm) ||
                                           i.InvoiceNumber.Contains(filterDto.SearchTerm) ||
                                           i.Notes != null && i.Notes.Contains(filterDto.SearchTerm));

                query = query.OrderByDescending(i => i.InvoiceDate);

                var totalCount = await query.CountAsync();
                var items = await query
                    .Skip((filterDto.PageNumber - 1) * filterDto.PageSize)
                    .Take(filterDto.PageSize)
                    .Select(i => new InvoiceResponseDto
                    {
                        Id = i.Id,
                        InvoiceNumber = i.InvoiceNumber,
                        InvoiceType = i.InvoiceType,
                        InvoiceDate = i.InvoiceDate,
                        PartyId = i.PartyId,
                        PartyName = i.Party.Name,
                        SubTotal = i.SubTotal,
                        ItemDiscountAmount = i.ItemDiscountAmount,
                        InvoiceDiscountAmount = i.InvoiceDiscountAmount,
                        TaxAmount = i.TaxAmount,
                        TotalAmount = i.TotalAmount,
                        PaidAmount = i.PaidAmount,
                        RemainingAmount = i.RemainingAmount,
                        PaymentType = i.PaymentType,
                        IsPaid = i.IsPaid,
                        Notes = i.Notes
                    })
                    .ToListAsync();

                var data = new PagedResponse<InvoiceResponseDto> { Items = items, PageNumber = filterDto.PageNumber, PageSize = filterDto.PageSize, TotalCount = totalCount };
                return Success(data);
            }
            catch (Exception ex)
            {
                return NotFound<PagedResponse<InvoiceResponseDto>>($"خطأ في الحصول على الفواتير: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResponse<InvoiceResponseDto>>> GetCustomerInvoicesAsync(int customerId, InvoiceFilterDto filterDto)
        {
            try
            {
                var customer = await _unitOfWork.Customers
                    .GetTableNoTracking()
                    .FirstOrDefaultAsync(c => c.Id == customerId);

                if (customer == null)
                    return NotFound<PagedResponse<InvoiceResponseDto>>("العميل غير موجود");

                filterDto.PartyId = customer.PartyId;
                filterDto.InvoiceType = InvoiceType.Sale;

                return await GetInvoicesAsync(filterDto);
            }
            catch (Exception ex)
            {
                return NotFound<PagedResponse<InvoiceResponseDto>>($"خطأ في الحصول على فواتير العميل: {ex.Message}");
            }
        }

        public async Task<ApiResponse<PagedResponse<InvoiceResponseDto>>> GetSupplierInvoicesAsync(int supplierId, InvoiceFilterDto filterDto)
        {
            try
            {
                var supplier = await _unitOfWork.Suppliers
                    .GetTableNoTracking()
                    .FirstOrDefaultAsync(s => s.Id == supplierId);

                if (supplier == null)
                    return NotFound<PagedResponse<InvoiceResponseDto>>("المورد غير موجود");

                filterDto.PartyId = supplier.PartyId;
                filterDto.InvoiceType = InvoiceType.Purchase;

                return await GetInvoicesAsync(filterDto);
            }
            catch (Exception ex)
            {
                return NotFound<PagedResponse<InvoiceResponseDto>>($"خطأ في الحصول على فواتير المورد: {ex.Message}");
            }
        }

        public async Task<ApiResponse<InvoiceResponseDto>> PayInvoiceAsync(int invoiceId, PayInvoiceDto paymentDto)
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync();

                var invoice = await _unitOfWork.InvoiceMasters
                    .GetTableAsTracking()
                    .FirstOrDefaultAsync(i => i.Id == invoiceId);

                if (invoice == null)
                    return NotFound<InvoiceResponseDto>("الفاتورة غير موجودة");

                if (invoice.IsPaid)
                    return BadRequest<InvoiceResponseDto>("الفاتورة مسددة بالكامل");

                if (paymentDto.Amount <= 0)
                    return BadRequest<InvoiceResponseDto>("مبلغ الدفع يجب أن يكون أكبر من صفر");

                if (paymentDto.Amount > invoice.RemainingAmount)
                    return BadRequest<InvoiceResponseDto>("مبلغ الدفع أكبر من المبلغ المتبقي");

                // تحديث بيانات الفاتورة
                invoice.PaidAmount += paymentDto.Amount;
                invoice.RemainingAmount -= paymentDto.Amount;
                invoice.IsPaid = invoice.RemainingAmount <= 0;
                invoice.PaymentType = paymentDto.PaymentType;

              await _unitOfWork.InvoiceMasters.UpdateAsync(invoice);

                // تسجيل المعاملة المالية
                await _financialService.RecordInvoicePaymentAsync(invoiceId, paymentDto.Amount, paymentDto.PaymentType, paymentDto.Notes);

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                var result = await GetInvoiceByIdAsync(invoiceId);
                return Success(result.Data, "تم تسديد الفاتورة بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest<InvoiceResponseDto>($"خطأ في تسديد الفاتورة: {ex.Message}");
            }
        }

        public async Task<ApiResponse<InvoiceResponseDto>> CancelPaymentAsync(int invoiceId)
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync();

                var invoice = await _unitOfWork.InvoiceMasters
                    .GetTableAsTracking()
                    .FirstOrDefaultAsync(i => i.Id == invoiceId);

                if (invoice == null)
                    return NotFound<InvoiceResponseDto>("الفاتورة غير موجودة");

                if (invoice.PaidAmount <= 0)
                    return BadRequest<InvoiceResponseDto>("لا توجد مدفوعات لإلغائها");

                // حذف المعاملات المالية المرتبطة بالفاتورة
                var financialTransactions = await _unitOfWork.FinancialTransactions
                    .GetTableAsTracking()
                    .Where(t => t.ReferenceType == (invoice.InvoiceType == InvoiceType.Sale ? ReferenceType.Sale : ReferenceType.Purchase)
                               && t.ReferenceId == invoiceId)
                    .ToListAsync();

                foreach (var transaction in financialTransactions)
                {
                 await  _unitOfWork.FinancialTransactions.DeleteAsync(transaction);
                }

                // إعادة تعيين بيانات الدفع
                invoice.PaidAmount = 0;
                invoice.RemainingAmount = invoice.TotalAmount;
                invoice.IsPaid = false;

              await  _unitOfWork.InvoiceMasters.UpdateAsync(invoice);

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                var result = await GetInvoiceByIdAsync(invoiceId);
                return Success(result.Data, "تم إلغاء تسديد الفاتورة بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest<InvoiceResponseDto>($"خطأ في إلغاء تسديد الفاتورة: {ex.Message}");
            }
        }

        public async Task<ApiResponse<InvoiceReportDto>> GetInvoiceReportAsync(InvoiceReportFilterDto filterDto)
        {
            try
            {
                var query = _unitOfWork.InvoiceMasters.GetTableNoTracking();

                if (filterDto.FromDate.HasValue)
                    query = query.Where(i => i.InvoiceDate >= filterDto.FromDate.Value);

                if (filterDto.ToDate.HasValue)
                    query = query.Where(i => i.InvoiceDate <= filterDto.ToDate.Value);

                if (filterDto.InvoiceType.HasValue)
                    query = query.Where(i => i.InvoiceType == filterDto.InvoiceType.Value);

                if (filterDto.PartyId.HasValue)
                    query = query.Where(i => i.PartyId == filterDto.PartyId.Value);

                var invoices = await query.ToListAsync();

                var totalSales = invoices.Where(i => i.InvoiceType == InvoiceType.Sale).Sum(i => i.TotalAmount);
                var totalPurchases = invoices.Where(i => i.InvoiceType == InvoiceType.Purchase).Sum(i => i.TotalAmount);
                var totalProfit = totalSales - totalPurchases; // تقدير بسيط للربح

                var result = new InvoiceReportDto
                {
                    TotalSales = totalSales,
                    TotalPurchases = totalPurchases,
                    TotalProfit = totalProfit,
                    TotalInvoicesCount = invoices.Count,
                    TotalPaidAmount = invoices.Sum(i => i.PaidAmount),
                    TotalRemainingAmount = invoices.Sum(i => i.RemainingAmount)
                };

                if (filterDto.IncludeDetails)
                {
                    result.Invoices = _mapper.Map<List<InvoiceResponseDto>>(invoices);
                }

                return Success(result);
            }
            catch (Exception ex)
            {
                return BadRequest<InvoiceReportDto>($"خطأ في الحصول على تقرير الفواتير: {ex.Message}");
            }
        }

        public async Task<ApiResponse<CustomerAccountDto>> GetCustomerAccountAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            return await _financialService.GetCustomerAccountAsync(customerId, fromDate, toDate);
        }

        public async Task<ApiResponse<SupplierAccountDto>> GetSupplierAccountAsync(int supplierId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            return await _financialService.GetSupplierAccountAsync(supplierId, fromDate, toDate);
        }

        // الطرق المساعدة
        private async Task<string> GenerateInvoiceNumberAsync(InvoiceType invoiceType)
        {
            var prefix = invoiceType == InvoiceType.Sale ? "S" : "P";
            var year = DateTime.Now.Year.ToString().Substring(2);
            var month = DateTime.Now.Month.ToString("00");

            var lastInvoice = await _unitOfWork.InvoiceMasters
                .GetTableNoTracking()
                .Where(i => i.InvoiceType == invoiceType && i.InvoiceNumber.StartsWith($"{prefix}{year}{month}"))
                .OrderByDescending(i => i.InvoiceNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastInvoice != null)
            {
                var lastNumberPart = lastInvoice.InvoiceNumber.Substring(5);
                if (int.TryParse(lastNumberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{year}{month}{nextNumber:0000}";
        }

        private async Task UpdateInventoryForInvoice(InvoiceMaster invoice)
        {
            var inventoryTransactions = new List<InventoryTransaction>();

            foreach (var item in invoice.Items)
            {
                var transactionType = invoice.InvoiceType == InvoiceType.Sale
                    ? InventoryTransactionType.Out
                    : InventoryTransactionType.In;

                var transaction = new InventoryTransaction
                {
                    Type = transactionType,
                    TransactionDate = invoice.InvoiceDate,
                    ProductId = item.ProductId,
                    Quantity = item.Quantity,
                    UnitCost = item.UnitPrice,
                    TotalCost = item.TotalPrice,
                    ReferenceNumber = invoice.InvoiceNumber,
                    Description = $"فاتورة {(invoice.InvoiceType == InvoiceType.Sale ? "بيع" : "شراء")} رقم {invoice.InvoiceNumber}"
                };

                inventoryTransactions.Add(transaction);
            }

            await _inventoryService.AddInventoryTransactionsAsync(inventoryTransactions);
        }

        private async Task ReverseInventoryForInvoice(InvoiceMaster invoice, List<InvoiceItem> items)
        {
            var inventoryTransactions = new List<InventoryTransaction>();

            foreach (var item in items)
            {
                // عكس نوع الحركة
                var transactionType = invoice.InvoiceType == InvoiceType.Sale
                    ? InventoryTransactionType.In
                    : InventoryTransactionType.Out;

                var transaction = new InventoryTransaction
                {
                    Type = transactionType,
                    TransactionDate = DateTime.Now,
                    ProductId = item.ProductId,
                    Quantity = item.Quantity,
                    UnitCost = item.UnitPrice,
                    TotalCost = item.TotalPrice,
                    ReferenceNumber = invoice.InvoiceNumber,
                    Description = $"إلغاء فاتورة {(invoice.InvoiceType == InvoiceType.Sale ? "بيع" : "شراء")} رقم {invoice.InvoiceNumber}"
                };

                inventoryTransactions.Add(transaction);
            }

            await _inventoryService.AddInventoryTransactionsAsync(inventoryTransactions);
        }
    }
}
