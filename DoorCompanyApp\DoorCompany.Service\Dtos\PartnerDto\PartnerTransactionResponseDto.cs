﻿using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos.BasicDto;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos.PartnerDto
{
    public class PartnerTransactionResponseDto : BaseDto
    {
        public DateTime TransactionDate { get; set; }
        public int ActionDetailId { get; set; }  //Investment  //Withdrawal 
        public string ActionDetailName { get; set; } = string.Empty;
        public int PartnerId { get; set; }
        public string PartnerName { get; set; } = string.Empty;
        public int PartnerBandId { get; set; }
        public string PartnerBandName { get;set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public string? ImagePath { get; set; }

    }


    public class PartnerTransactionRequestDto : PagedSearchRequset
    {
        public int? partnerId { get; set; }
        public DateTime? fromDate { get; set; } 
        public DateTime? toDate { get; set; } 
        public int? bandId { get; set; }
    }

    public class CreatePartnerTransactionDto : BaseCreateDto
    {
     
        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactionDate { get; set; }
      
        [Required]
        public int ActionDetailId { get; set; }  //Investment  //Withdrawal 
      
        [Required]
        public int PartnerId { get; set; }
    
        [Required]
        public int PartnerBandId { get; set; }
       
        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
     
        [Required]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string? Notes { get; set; }
        public IFormFile? ImagePath { get; set; }
    } 
    public class UpdatePartnerTransactionDto : BaseUpdateDto
    {
        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactionDate { get; set; }

        [Required]
        public int ActionDetailId { get; set; }  //Investment  //Withdrawal 

        [Required]
        public int PartnerId { get; set; }

        [Required]
        public int PartnerBandId { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string? Notes { get; set; }
        public IFormFile? ImagePath { get; set; }
    }

    
}
