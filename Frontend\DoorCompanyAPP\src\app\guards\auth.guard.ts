import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { inject } from '@angular/core';


export const authGuard: CanActivateFn = (route, state) => {


 const authService = inject(AuthService);
  const router = inject(Router);

 const token = authService.tokenValue;
  

  // ✅ تحقق من صلاحية التوكن
  if (token && !authService.isTokenExpired()) {
    return true;
  }

  // ⛔ لا يوجد توكن أو منتهي → أعد التوجيه إلى login
  router.navigate(['/auth/login']);
  return false;




}


