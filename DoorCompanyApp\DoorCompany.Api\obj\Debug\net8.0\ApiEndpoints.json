[{"ContainingType": "DoorCompany.Api.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "DoorCompany.Service.Dtos.UserDto.LoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "DoorCompany.Service.Dtos.UserDto.RefreshTokenDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.AuthController", "Method": "ValidateToken", "RelativePath": "api/Auth/validate-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.CategoryController", "Method": "GetAllCategoryAsync", "RelativePath": "api/Category", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.CategoryController", "Method": "CreateCategoryAsync", "RelativePath": "api/Category", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ParentCategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "Symbol", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryTypeId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ImageFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "SortOrder", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "CreatedAt", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.CategoryController", "Method": "GetCategoryById", "RelativePath": "api/Category/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ImageController", "Method": "GetImage", "RelativePath": "api/Image/{imagePath}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "imagePath", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ImageController", "Method": "GetFullImage", "RelativePath": "api/Image/GetFullPath{imagePath}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "imagePath", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ImageController", "Method": "GetFullImageUrl", "RelativePath": "api/Image/GetFullPathUrl", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pathstring", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ImageController", "Method": "CreatePermissionAsync", "RelativePath": "api/Image/UploadImage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "folder", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.MainactionController", "Method": "GetAlMainactions", "RelativePath": "api/Mainaction", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.MainactionController", "Method": "GetMainactionById", "RelativePath": "api/Mainaction/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.MainactionController", "Method": "GetMainActionByActionAsync", "RelativePath": "api/Mainaction/MainActionByAction/{parentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "GetAllPartners", "RelativePath": "api/Partner", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "isDelete", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "CreatePartnerAsycn", "RelativePath": "api/Partner", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "DoorCompany.Service.Dtos.PartnerDto.CreatePartnerDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "UpdatePartnerAsycn", "RelativePath": "api/Partner", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "Dto", "Type": "DoorCompany.Service.Dtos.PartnerDto.UpdatePartnerDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "GetPartnerById", "RelativePath": "api/Partner/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "DeletePartnerAsync", "RelativePath": "api/Partner/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "GetAllShareTransactionAsync", "RelativePath": "api/Partner/get-all-sharetransfer", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "partnerId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "GetAllPartnerTransaction", "RelativePath": "api/Partner/get-all-transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "partnerId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "bandId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "GetAllPartnerDataSummer", "RelativePath": "api/Partner/partners-summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "CreateShareTransferAsync", "RelativePath": "api/Partner/share-transfer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "DoorCompany.Service.Dtos.PartnerDto.CreateShareTransferDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "UpdateShareTransferAsync", "RelativePath": "api/Partner/share-transfer", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "DoorCompany.Service.Dtos.PartnerDto.UpdateShareTransferDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "GetShareTransferByIdAsync", "RelativePath": "api/Partner/share-transfer/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "GetPartnerTransactionById", "RelativePath": "api/Partner/transaction/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "CreatePartnerTansactionAsycn", "RelativePath": "api/Partner/transactions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "TransactionDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ActionDetailId", "Type": "System.Int32", "IsRequired": false}, {"Name": "PartnerId", "Type": "System.Int32", "IsRequired": false}, {"Name": "PartnerBandId", "Type": "System.Int32", "IsRequired": false}, {"Name": "Amount", "Type": "System.Decimal", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "ImagePath", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "CreatedAt", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "UpdatePartnerTransactionAsycn", "RelativePath": "api/Partner/transactions", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "TransactionDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ActionDetailId", "Type": "System.Int32", "IsRequired": false}, {"Name": "PartnerId", "Type": "System.Int32", "IsRequired": false}, {"Name": "PartnerBandId", "Type": "System.Int32", "IsRequired": false}, {"Name": "Amount", "Type": "System.Decimal", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "ImagePath", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "UpdatedAt", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsDeleted", "Type": "System.Boolean", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "DeletePartnerTransationAsync", "RelativePath": "api/Partner/transactions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerController", "Method": "DelGetReportPartnerTransactionAsync", "RelativePath": "api/Partner/transactions/report", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "partnerId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "bandId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerBandController", "Method": "GetAllPartnerBandss", "RelativePath": "api/PartnerBand", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "isDelete", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerBandController", "Method": "CreatePartnerBandsAsycn", "RelativePath": "api/PartnerBand", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "DoorCompany.Service.Dtos.PartnerDto.CreatePartnerBandDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerBandController", "Method": "UpdatePartnerAsycn", "RelativePath": "api/PartnerBand", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "Dto", "Type": "DoorCompany.Service.Dtos.PartnerDto.UpdatePartnerBandDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerBandController", "Method": "GetPartnerBandsById", "RelativePath": "api/PartnerBand/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PartnerBandController", "Method": "DeletePartnerBandsAsync", "RelativePath": "api/PartnerBand/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PermissionController", "Method": "CreatePermissionAsync", "RelativePath": "api/Permission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "DoorCompany.Service.Dtos.PermissionDto.CreatePermissionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.PermissionController", "Method": "GetAllPermissons", "RelativePath": "api/Permission/all-Permission", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ProductController", "Method": "CreateProductAsync", "RelativePath": "api/Product", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "Barcode", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Int32", "IsRequired": false}, {"Name": "UnitId", "Type": "System.Int32", "IsRequired": false}, {"Name": "StandardCost", "Type": "System.Decimal", "IsRequired": false}, {"Name": "MinimumStock", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaximumStock", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OpeningBalance", "Type": "System.Decimal", "IsRequired": false}, {"Name": "SortOrder", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ItemType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ImagePath", "Type": "Microsoft.AspNetCore.Http.FormFile", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "CreatedAt", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ProductController", "Method": "GetProductById", "RelativePath": "api/Product/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ProductController", "Method": "CreateInvoiceAsync", "RelativePath": "api/Product/invoice", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "DoorCompany.Service.Dtos.ProductDto.CreateInvoiceMasterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.RoleController", "Method": "CreateRoleAsync", "RelativePath": "api/Role", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "DoorCompany.Service.Dtos.RoleDto.CreateRoleDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.RoleController", "Method": "UpdateRoleAsync", "RelativePath": "api/Role", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "DoorCompany.Service.Dtos.RoleDto.UpdateRoleDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.RoleController", "Method": "GetRoleById", "RelativePath": "api/Role/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.RoleController", "Method": "AddAndRemoveRolePermission", "RelativePath": "api/Role/add-Remove-Role-Permmision", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateRolePermissionsDto", "Type": "DoorCompany.Service.Dtos.RoleDto.UpdateRolePermissionsDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.RoleController", "Method": "GetAllRoles", "RelativePath": "api/Role/All-Roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ShareController", "Method": "GetAllShares", "RelativePath": "api/Share", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ShareController", "Method": "CreateShareAsycn", "RelativePath": "api/Share", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "DoorCompany.Service.Dtos.ShareDto.CreateShareDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ShareController", "Method": "UpdateShareAsycn", "RelativePath": "api/Share", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "Dto", "Type": "DoorCompany.Service.Dtos.ShareDto.UpdateShareDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ShareController", "Method": "GetShareById", "RelativePath": "api/Share/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.ShareController", "Method": "DeleteShareAsync", "RelativePath": "api/Share/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "CreateUser", "RelativePath": "api/User", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userDto", "Type": "DoorCompany.Service.Dtos.UserDto.CreateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "api/User", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userDto", "Type": "DoorCompany.Service.Dtos.UserDto.UpdateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "GetUserById", "RelativePath": "api/User/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "DeleteUser", "RelativePath": "api/User/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "GetUserRoleById", "RelativePath": "api/User/{userId}/user-Roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "AddAndRemoveUserRole", "RelativePath": "api/User/add-Remove-user-Roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateUserRolesDto", "Type": "DoorCompany.Service.Dtos.RoleDto.UpdateUserRolesDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "GetAllUserRolesAsync", "RelativePath": "api/User/All-UserRoles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "GetAllUsers", "RelativePath": "api/User/all-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "ChangePassword", "RelativePath": "api/User/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordDto", "Type": "DoorCompany.Service.Dtos.UserDto.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "GetProfile", "RelativePath": "api/User/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "UpdateProfile", "RelativePath": "api/User/profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "Phone", "Type": "System.String", "IsRequired": false}, {"Name": "ProfileImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "UpdateProfileImage", "RelativePath": "api/User/profile-image", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "ProfileImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "DoorCompany.Api.Controllers.UserController", "Method": "ResetPassword", "RelativePath": "api/User/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DoorCompany.Api.Controllers.UserController+ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}]