import{fa as u,ga as p}from"./chunk-5FTQYYYZ.js";import{K as i,N as l,Q as s,h as o,u as c,z as n}from"./chunk-KFKFGDWN.js";var d=class a{constructor(e,r){this.apiService=e;this.authService=r;this.initializeUserFromStorage(),this.authService.token$.pipe(c(t=>!!t&&!this.authService.isTokenExpired()),n()).subscribe(()=>{this.loadUser()})}currentUserSubject=new o(null);currentUser$=this.currentUserSubject.asObservable();loaded=!1;loadUser(){this.loaded||this.getCurrentUser().subscribe({next:e=>{e.succeeded&&e.data&&(this.loaded=!0)},error:e=>{console.error("Failed to load user",e),this.loaded=!1}})}getCurrentUser(){return this.apiService.get("User/profile").pipe(i(e=>{e.succeeded&&e.data&&(typeof localStorage<"u"&&localStorage.setItem("currentUser",JSON.stringify(e.data)),this.currentUserSubject.next(e.data))}))}initializeUserFromStorage(){if(typeof localStorage>"u")return;let e=localStorage.getItem("currentUser");if(e)try{let r=JSON.parse(e);this.currentUserSubject.next(r)}catch{console.warn("Failed to parse currentUser from localStorage")}}get currentUserValue(){return this.currentUserSubject.value}uploadProfileImage(e){let r=new FormData;return r.append("ProfileImage",e),this.apiService.put("User/profile-image",r).pipe(i(t=>{t.succeeded&&t.data&&(typeof localStorage<"u"&&localStorage.setItem("currentUser",JSON.stringify(t.data)),this.currentUserSubject.next(t.data))}))}updateProfile(e){let r=new FormData;return r.append("fullName",e.fullName),r.append("phone",e.phone),e.profileImage&&r.append("profileImage",e.profileImage),this.apiService.put("User/profile",r).pipe(i(t=>{t.succeeded&&t.data&&(typeof localStorage<"u"&&localStorage.setItem("currentUser",JSON.stringify(t.data)),this.currentUserSubject.next(t.data))}))}changePassword(e){return e.userId||(e.userId=this.currentUserValue?.id||0),this.apiService.post("User/change-password",e).pipe(i(r=>{r.succeeded}))}clearLocalStorage(){typeof localStorage<"u"&&localStorage.removeItem("currentUser"),this.currentUserSubject.next(null)}getUserPermissions(){let e=this.currentUserValue;return console.log("User Permissions:",e),e?e.permissions.map(r=>r.userRole):[]}static \u0275fac=function(r){return new(r||a)(s(u),s(p))};static \u0275prov=l({token:a,factory:a.\u0275fac,providedIn:"root"})};export{d as a};
