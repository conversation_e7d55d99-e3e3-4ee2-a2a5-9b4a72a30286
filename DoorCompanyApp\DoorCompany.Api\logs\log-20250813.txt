2025-08-13 11:46:50.209 +03:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-13 11:46:50.376 +03:00 [INF] Executed DbCommand (104ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-13 11:46:50.796 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-13 11:46:50.826 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-13 11:46:51.164 +03:00 [INF] Executed DbCommand (113ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-08-13 11:46:51.287 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
2025-08-13 11:46:51.378 +03:00 [INF] Executed DbCommand (42ms) [Parameters=[@__adminUser_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__adminUser_Id_0
2025-08-13 11:46:51.400 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-13 11:46:51.415 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-13 11:46:51.438 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [ActionTypes] AS [a]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-13 11:46:51.457 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [MainActions] AS [m]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-13 11:46:51.473 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Companies] AS [c]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-13 11:46:51.487 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Units] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-13 11:46:51.671 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-13 11:46:51.913 +03:00 [INF] Now listening on: http://localhost:5184
2025-08-13 11:46:52.051 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-13 11:46:52.054 +03:00 [INF] Hosting environment: Development
2025-08-13 11:46:52.055 +03:00 [INF] Content root path: G:\DoorAPP\DoorCompanyApp\DoorCompany.Api
2025-08-13 11:46:55.127 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/index.html - null null
2025-08-13 11:46:55.465 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/index.html - 200 null text/html;charset=utf-8 349.3204ms
2025-08-13 11:46:55.509 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - null null
2025-08-13 11:46:55.514 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/_vs/browserLink - null null
2025-08-13 11:46:55.540 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_framework/aspnetcore-browser-refresh.js - 200 16515 application/javascript; charset=utf-8 32.2396ms
2025-08-13 11:46:55.592 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/_vs/browserLink - 200 null text/javascript; charset=UTF-8 77.8909ms
2025-08-13 11:46:55.753 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - null null
2025-08-13 11:46:55.831 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 77.2002ms
2025-08-13 11:47:09.636 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/login - null null
2025-08-13 11:47:09.644 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-13 11:47:09.693 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:09.697 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/login - 204 null null 61.1497ms
2025-08-13 11:47:09.708 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/login - application/json 43
2025-08-13 11:47:09.713 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:11.001 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-08-13 11:47:11.032 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(DoorCompany.Service.Dtos.UserDto.LoginDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-08-13 11:47:11.381 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-13 11:47:11.459 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s0].[Id], [s0].[RoleId], [s0].[UserId], [s0].[Id0], [s0].[CreatedAt], [s0].[CreatedBy], [s0].[Description], [s0].[IsActive], [s0].[IsDeleted], [s0].[Name], [s0].[UpdatedAt], [s0].[UpdatedBy], [s0].[Id1], [s0].[PermissionId], [s0].[RoleId0], [s0].[Id00], [s0].[CreatedAt0], [s0].[CreatedBy0], [s0].[Description0], [s0].[IsActive0], [s0].[IsDeleted0], [s0].[Name0], [s0].[UpdatedAt0], [s0].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy], [s].[Id] AS [Id1], [s].[PermissionId], [s].[RoleId] AS [RoleId0], [s].[Id0] AS [Id00], [s].[CreatedAt] AS [CreatedAt0], [s].[CreatedBy] AS [CreatedBy0], [s].[Description] AS [Description0], [s].[IsActive] AS [IsActive0], [s].[IsDeleted] AS [IsDeleted0], [s].[Name] AS [Name0], [s].[UpdatedAt] AS [UpdatedAt0], [s].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
    LEFT JOIN (
        SELECT [r0].[Id], [r0].[PermissionId], [r0].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r0]
        INNER JOIN [Permissions] AS [p] ON [r0].[PermissionId] = [p].[Id]
    ) AS [s] ON [r].[Id] = [s].[RoleId]
) AS [s0] ON [u1].[Id] = [s0].[UserId]
ORDER BY [u1].[Id], [s0].[Id], [s0].[Id0], [s0].[Id1]
2025-08-13 11:47:11.664 +03:00 [INF] Access token generated for user 1
2025-08-13 11:47:11.956 +03:00 [INF] Executed DbCommand (88ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-08-13 11:47:11.988 +03:00 [INF] User logged in successfully: admin
2025-08-13 11:47:11.999 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:47:12.047 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api) in 1009.1155ms
2025-08-13 11:47:12.049 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.Login (DoorCompany.Api)'
2025-08-13 11:47:12.052 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/login - 200 null application/json; charset=utf-8 2343.7392ms
2025-08-13 11:47:12.057 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-13 11:47:12.062 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:12.070 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-13 11:47:12.078 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-13 11:47:12.080 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 401 0 null 22.7973ms
2025-08-13 11:47:12.088 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - null null
2025-08-13 11:47:12.093 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:12.094 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Auth/refresh-token - 204 null null 6.1524ms
2025-08-13 11:47:12.099 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - application/json 107
2025-08-13 11:47:12.102 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:12.131 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-13 11:47:12.137 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:47:12.142 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:12.143 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:12.144 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 12.8047ms
2025-08-13 11:47:12.145 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 8.0884ms
2025-08-13 11:47:12.152 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:47:12.157 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-13 11:47:12.162 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:12.163 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:12.197 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:47:12.197 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-13 11:47:12.199 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:47:12.203 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:47:12.203 +03:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] RefreshToken(DoorCompany.Service.Dtos.UserDto.RefreshTokenDto) on controller DoorCompany.Api.Controllers.AuthController (DoorCompany.Api).
2025-08-13 11:47:12.204 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-13 11:47:12.306 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:47:12.307 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-13 11:47:12.325 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-13 11:47:12.474 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:47:12.479 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:47:12.496 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 273.6788ms
2025-08-13 11:47:12.498 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:47:12.504 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 347.2472ms
2025-08-13 11:47:12.529 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:47:12.530 +03:00 [INF] Executed DbCommand (39ms) [Parameters=[@__refreshTokenDto_RefreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshTokenDto_RefreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-13 11:47:12.554 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:47:12.560 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:47:12.561 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-08-13 11:47:12.575 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 368.9978ms
2025-08-13 11:47:12.577 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:47:12.579 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 426.5745ms
2025-08-13 11:47:12.639 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[@__refreshToken_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[ExpiryDate], [s].[IpAddress], [s].[IsActive], [s].[IsDeleted], [s].[IsRevoked], [s].[RevokedAt], [s].[RevokedReason], [s].[Token], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserAgent], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[FullName], [s].[IsActive0], [s].[IsDeleted0], [s].[Password], [s].[Phone], [s].[ProfileImage], [s].[UpdatedAt0], [s].[UpdatedBy0], [s].[UserName], [s1].[Id], [s1].[RoleId], [s1].[UserId], [s1].[Id0], [s1].[CreatedAt], [s1].[CreatedBy], [s1].[Description], [s1].[IsActive], [s1].[IsDeleted], [s1].[Name], [s1].[UpdatedAt], [s1].[UpdatedBy], [s1].[Id1], [s1].[PermissionId], [s1].[RoleId0], [s1].[Id00], [s1].[CreatedAt0], [s1].[CreatedBy0], [s1].[Description0], [s1].[IsActive0], [s1].[IsDeleted0], [s1].[Name0], [s1].[UpdatedAt0], [s1].[UpdatedBy0]
FROM (
    SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[ExpiryDate], [r].[IpAddress], [r].[IsActive], [r].[IsDeleted], [r].[IsRevoked], [r].[RevokedAt], [r].[RevokedReason], [r].[Token], [r].[UpdatedAt], [r].[UpdatedBy], [r].[UserAgent], [r].[UserId], [u].[Id] AS [Id0], [u].[CreatedAt] AS [CreatedAt0], [u].[CreatedBy] AS [CreatedBy0], [u].[FullName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted0], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt] AS [UpdatedAt0], [u].[UpdatedBy] AS [UpdatedBy0], [u].[UserName]
    FROM [RefreshTokens] AS [r]
    INNER JOIN [Users] AS [u] ON [r].[UserId] = [u].[Id]
    WHERE [r].[Token] = @__refreshToken_0 AND [r].[IsRevoked] = CAST(0 AS bit) AND [r].[ExpiryDate] > GETUTCDATE()
) AS [s]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r0].[Id] AS [Id0], [r0].[CreatedAt], [r0].[CreatedBy], [r0].[Description], [r0].[IsActive], [r0].[IsDeleted], [r0].[Name], [r0].[UpdatedAt], [r0].[UpdatedBy], [s0].[Id] AS [Id1], [s0].[PermissionId], [s0].[RoleId] AS [RoleId0], [s0].[Id0] AS [Id00], [s0].[CreatedAt] AS [CreatedAt0], [s0].[CreatedBy] AS [CreatedBy0], [s0].[Description] AS [Description0], [s0].[IsActive] AS [IsActive0], [s0].[IsDeleted] AS [IsDeleted0], [s0].[Name] AS [Name0], [s0].[UpdatedAt] AS [UpdatedAt0], [s0].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r0] ON [u0].[RoleId] = [r0].[Id]
    LEFT JOIN (
        SELECT [r1].[Id], [r1].[PermissionId], [r1].[RoleId], [p].[Id] AS [Id0], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
        FROM [RolePermissions] AS [r1]
        INNER JOIN [Permissions] AS [p] ON [r1].[PermissionId] = [p].[Id]
    ) AS [s0] ON [r0].[Id] = [s0].[RoleId]
) AS [s1] ON [s].[Id0] = [s1].[UserId]
ORDER BY [s].[Id], [s].[Id0], [s1].[Id], [s1].[Id0], [s1].[Id1]
2025-08-13 11:47:12.689 +03:00 [INF] Access token generated for user 1
2025-08-13 11:47:12.691 +03:00 [INF] Access token refreshed for user 1
2025-08-13 11:47:12.693 +03:00 [INF] Token refreshed successfully for user: 1
2025-08-13 11:47:12.694 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.LoginResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:47:12.696 +03:00 [INF] Executed action DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api) in 478.375ms
2025-08-13 11:47:12.698 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.AuthController.RefreshToken (DoorCompany.Api)'
2025-08-13 11:47:12.699 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5184/api/Auth/refresh-token - 200 null application/json; charset=utf-8 600.4541ms
2025-08-13 11:47:12.702 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-13 11:47:12.705 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:47:12.709 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:47:12.710 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-13 11:47:12.719 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-13 11:47:12.723 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:47:12.726 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 13.4142ms
2025-08-13 11:47:12.727 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:47:12.729 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 26.432ms
2025-08-13 11:49:26.664 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=true - null null
2025-08-13 11:49:26.667 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:26.668 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=true - 204 null null 4.8123ms
2025-08-13 11:49:26.681 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - null null
2025-08-13 11:49:26.689 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:26.714 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:26.740 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:26.768 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
2025-08-13 11:49:26.781 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:26.788 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 44.4639ms
2025-08-13 11:49:26.790 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:26.791 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - 200 null application/json; charset=utf-8 109.9502ms
2025-08-13 11:49:27.938 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:49:27.941 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:27.943 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 4.6703ms
2025-08-13 11:49:27.953 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:49:27.956 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:27.957 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:49:27.958 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:27.968 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:49:27.972 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:49:27.976 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:49:27.983 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:49:27.985 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:27.987 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 23.8001ms
2025-08-13 11:49:27.989 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:49:27.990 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 37.121ms
2025-08-13 11:49:29.822 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - null null
2025-08-13 11:49:29.827 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:29.828 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:29.830 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:29.836 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
2025-08-13 11:49:29.838 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:29.843 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 11.6288ms
2025-08-13 11:49:29.846 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:29.848 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - 200 null application/json; charset=utf-8 25.5559ms
2025-08-13 11:49:37.220 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - null null
2025-08-13 11:49:37.221 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=false - null null
2025-08-13 11:49:37.221 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-13 11:49:37.224 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:37.227 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:37.230 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:37.235 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=false - 204 null null 13.4685ms
2025-08-13 11:49:37.245 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - 204 null null 23.4348ms
2025-08-13 11:49:37.232 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - 204 null null 11.916ms
2025-08-13 11:49:37.256 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - null null
2025-08-13 11:49:37.261 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-13 11:49:37.264 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - null null
2025-08-13 11:49:37.266 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:37.280 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:37.283 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:37.285 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:37.287 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-13 11:49:37.288 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-13 11:49:37.288 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:37.295 +03:00 [INF] Route matched with {action = "GetAllPartnerBandss", controller = "PartnerBand"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerBandss(Boolean) on controller DoorCompany.Api.Controllers.PartnerBandController (DoorCompany.Api).
2025-08-13 11:49:37.299 +03:00 [INF] Route matched with {action = "GetAllPartnerTransaction", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerTransaction(DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionRequestDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:37.309 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:49:37.313 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:37.315 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 21.4034ms
2025-08-13 11:49:37.316 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:37.321 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerBands] AS [p]
2025-08-13 11:49:37.321 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - 200 null application/json; charset=utf-8 65.3607ms
2025-08-13 11:49:37.329 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerBandResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:37.333 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api) in 36.0005ms
2025-08-13 11:49:37.338 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-13 11:49:37.340 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - 200 null application/json; charset=utf-8 79.089ms
2025-08-13 11:49:37.380 +03:00 [INF] Executed DbCommand (41ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
2025-08-13 11:49:37.442 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[ActionDetailId], [p1].[Amount], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[ImagePath], [p1].[IsActive], [p1].[IsDeleted], [p1].[Notes], [p1].[PartnerBandId], [p1].[PartnerId], [p1].[TransactionDate], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy], [p2].[Id], [p2].[CreatedAt], [p2].[CreatedBy], [p2].[Description], [p2].[IsActive], [p2].[IsDeleted], [p2].[Name], [p2].[UpdatedAt], [p2].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[Name], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy]
FROM (
    SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [PartnerTransations] AS [p]
    WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
    ORDER BY [p].[TransactionDate]
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [p1]
INNER JOIN [Partners] AS [p0] ON [p1].[PartnerId] = [p0].[Id]
INNER JOIN [PartnerBands] AS [p2] ON [p1].[PartnerBandId] = [p2].[Id]
INNER JOIN [MainActions] AS [m] ON [p1].[ActionDetailId] = [m].[Id]
ORDER BY [p1].[TransactionDate]
2025-08-13 11:49:37.453 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PagedResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:49:37.460 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api) in 157.907ms
2025-08-13 11:49:37.467 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-13 11:49:37.469 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - 200 null application/json; charset=utf-8 205.0819ms
2025-08-13 11:49:39.121 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-sharetransfer?fromDate=2025-08-13&toDate=2025-08-13 - null null
2025-08-13 11:49:39.122 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - null null
2025-08-13 11:49:39.127 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:39.130 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:39.131 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-sharetransfer?fromDate=2025-08-13&toDate=2025-08-13 - 204 null null 9.7924ms
2025-08-13 11:49:39.132 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:39.135 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-sharetransfer?fromDate=2025-08-13&toDate=2025-08-13 - null null
2025-08-13 11:49:39.136 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:39.138 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:39.145 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllShareTransactionAsync (DoorCompany.Api)'
2025-08-13 11:49:39.149 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:49:39.152 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:39.153 +03:00 [INF] Route matched with {action = "GetAllShareTransaction", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllShareTransactionAsync(DoorCompany.Service.Dtos.PartnerDto.ShareTransferRequestDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:39.154 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 10.5221ms
2025-08-13 11:49:39.162 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:39.164 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - 200 null application/json; charset=utf-8 42.1251ms
2025-08-13 11:49:39.209 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy], [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [ShareTransfers] AS [s]
INNER JOIN [Partners] AS [p] ON [s].[BuyerId] = [p].[Id]
INNER JOIN [Partners] AS [p0] ON [s].[SellerId] = [p0].[Id]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [s].[TransfersDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [s].[TransfersDate]) <= @__req_toDate_Value_Date_1
ORDER BY [s].[TransfersDate]
2025-08-13 11:49:39.218 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.ShareTransferResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:39.221 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllShareTransactionAsync (DoorCompany.Api) in 61.2283ms
2025-08-13 11:49:39.223 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllShareTransactionAsync (DoorCompany.Api)'
2025-08-13 11:49:39.225 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-sharetransfer?fromDate=2025-08-13&toDate=2025-08-13 - 200 null application/json; charset=utf-8 89.6327ms
2025-08-13 11:49:40.150 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - null null
2025-08-13 11:49:40.150 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - null null
2025-08-13 11:49:40.151 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-13 11:49:40.155 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:40.158 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:40.162 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:40.163 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-13 11:49:40.164 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:40.165 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-13 11:49:40.166 +03:00 [INF] Route matched with {action = "GetAllPartnerTransaction", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerTransaction(DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionRequestDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:40.167 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:40.169 +03:00 [INF] Route matched with {action = "GetAllPartnerBandss", controller = "PartnerBand"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerBandss(Boolean) on controller DoorCompany.Api.Controllers.PartnerBandController (DoorCompany.Api).
2025-08-13 11:49:40.182 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
2025-08-13 11:49:40.185 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:49:40.199 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:40.199 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[ActionDetailId], [p1].[Amount], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[ImagePath], [p1].[IsActive], [p1].[IsDeleted], [p1].[Notes], [p1].[PartnerBandId], [p1].[PartnerId], [p1].[TransactionDate], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy], [p2].[Id], [p2].[CreatedAt], [p2].[CreatedBy], [p2].[Description], [p2].[IsActive], [p2].[IsDeleted], [p2].[Name], [p2].[UpdatedAt], [p2].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[Name], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy]
FROM (
    SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [PartnerTransations] AS [p]
    WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
    ORDER BY [p].[TransactionDate]
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [p1]
INNER JOIN [Partners] AS [p0] ON [p1].[PartnerId] = [p0].[Id]
INNER JOIN [PartnerBands] AS [p2] ON [p1].[PartnerBandId] = [p2].[Id]
INNER JOIN [MainActions] AS [m] ON [p1].[ActionDetailId] = [m].[Id]
ORDER BY [p1].[TransactionDate]
2025-08-13 11:49:40.206 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerBands] AS [p]
2025-08-13 11:49:40.206 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 25.1694ms
2025-08-13 11:49:40.210 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PagedResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:49:40.213 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerBandResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:40.214 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:40.215 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api) in 39.0998ms
2025-08-13 11:49:40.222 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api) in 38.3061ms
2025-08-13 11:49:40.223 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - 200 null application/json; charset=utf-8 73.0913ms
2025-08-13 11:49:40.225 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-13 11:49:40.227 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-13 11:49:40.235 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - 200 null application/json; charset=utf-8 84.732ms
2025-08-13 11:49:40.236 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - 200 null application/json; charset=utf-8 85.5974ms
2025-08-13 11:49:42.494 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=true - null null
2025-08-13 11:49:42.498 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:42.500 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=true - 204 null null 5.3895ms
2025-08-13 11:49:42.505 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - null null
2025-08-13 11:49:42.508 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:42.509 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:42.511 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:42.517 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
2025-08-13 11:49:42.521 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:42.523 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 9.0643ms
2025-08-13 11:49:42.524 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:42.526 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - 200 null application/json; charset=utf-8 20.9867ms
2025-08-13 11:49:43.622 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=false - null null
2025-08-13 11:49:43.622 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - null null
2025-08-13 11:49:43.622 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-13 11:49:43.627 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:43.629 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:43.631 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:43.632 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner?isDelete=false - 204 null null 9.8669ms
2025-08-13 11:49:43.633 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - 204 null null 10.797ms
2025-08-13 11:49:43.634 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/PartnerBand?isDelete=true - 204 null null 11.6482ms
2025-08-13 11:49:43.642 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - null null
2025-08-13 11:49:43.646 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - null null
2025-08-13 11:49:43.649 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - null null
2025-08-13 11:49:43.651 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:43.656 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:43.658 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:43.660 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-13 11:49:43.661 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:43.662 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-13 11:49:43.663 +03:00 [INF] Route matched with {action = "GetAllPartnerBandss", controller = "PartnerBand"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerBandss(Boolean) on controller DoorCompany.Api.Controllers.PartnerBandController (DoorCompany.Api).
2025-08-13 11:49:43.664 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:43.665 +03:00 [INF] Route matched with {action = "GetAllPartnerTransaction", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerTransaction(DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionRequestDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:43.670 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerBands] AS [p]
2025-08-13 11:49:43.677 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:49:43.681 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
2025-08-13 11:49:43.681 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerBandResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:43.683 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:43.691 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api) in 24.1796ms
2025-08-13 11:49:43.691 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2), @__p_2='?' (DbType = Int32), @__p_3='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[ActionDetailId], [p1].[Amount], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[ImagePath], [p1].[IsActive], [p1].[IsDeleted], [p1].[Notes], [p1].[PartnerBandId], [p1].[PartnerId], [p1].[TransactionDate], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy], [p2].[Id], [p2].[CreatedAt], [p2].[CreatedBy], [p2].[Description], [p2].[IsActive], [p2].[IsDeleted], [p2].[Name], [p2].[UpdatedAt], [p2].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[Name], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy]
FROM (
    SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [PartnerTransations] AS [p]
    WHERE [p].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [p].[TransactionDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [p].[TransactionDate]) <= @__req_toDate_Value_Date_1
    ORDER BY [p].[TransactionDate]
    OFFSET @__p_2 ROWS FETCH NEXT @__p_3 ROWS ONLY
) AS [p1]
INNER JOIN [Partners] AS [p0] ON [p1].[PartnerId] = [p0].[Id]
INNER JOIN [PartnerBands] AS [p2] ON [p1].[PartnerBandId] = [p2].[Id]
INNER JOIN [MainActions] AS [m] ON [p1].[ActionDetailId] = [m].[Id]
ORDER BY [p1].[TransactionDate]
2025-08-13 11:49:43.693 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 19.8811ms
2025-08-13 11:49:43.695 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerBandController.GetAllPartnerBandss (DoorCompany.Api)'
2025-08-13 11:49:43.699 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.PagedResponse`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:49:43.706 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:43.707 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/PartnerBand?isDelete=true - 200 null application/json; charset=utf-8 65.1361ms
2025-08-13 11:49:43.709 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api) in 32.5653ms
2025-08-13 11:49:43.710 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - 200 null application/json; charset=utf-8 64.7029ms
2025-08-13 11:49:43.719 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerTransaction (DoorCompany.Api)'
2025-08-13 11:49:43.725 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-transactions?pageNumber=1&pageSize=10&fromDate=2025-08-13&toDate=2025-08-13 - 200 null application/json; charset=utf-8 75.7469ms
2025-08-13 11:49:44.310 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-sharetransfer?fromDate=2025-08-13&toDate=2025-08-13 - null null
2025-08-13 11:49:44.310 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - null null
2025-08-13 11:49:44.315 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:44.317 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:44.318 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/get-all-sharetransfer?fromDate=2025-08-13&toDate=2025-08-13 - 204 null null 7.9291ms
2025-08-13 11:49:44.320 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:44.336 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-sharetransfer?fromDate=2025-08-13&toDate=2025-08-13 - null null
2025-08-13 11:49:44.337 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:44.340 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:44.343 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllShareTransactionAsync (DoorCompany.Api)'
2025-08-13 11:49:44.346 +03:00 [INF] Route matched with {action = "GetAllShareTransaction", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllShareTransactionAsync(DoorCompany.Service.Dtos.PartnerDto.ShareTransferRequestDto) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:44.347 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:49:44.354 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__req_fromDate_Value_Date_0='?' (DbType = DateTime2), @__req_toDate_Value_Date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy], [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[InitialCapital], [p0].[IsActive], [p0].[IsDeleted], [p0].[Name], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [ShareTransfers] AS [s]
INNER JOIN [Partners] AS [p] ON [s].[BuyerId] = [p].[Id]
INNER JOIN [Partners] AS [p0] ON [s].[SellerId] = [p0].[Id]
WHERE [s].[IsDeleted] = CAST(0 AS bit) AND CONVERT(date, [s].[TransfersDate]) >= @__req_fromDate_Value_Date_0 AND CONVERT(date, [s].[TransfersDate]) <= @__req_toDate_Value_Date_1
ORDER BY [s].[TransfersDate]
2025-08-13 11:49:44.355 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:44.357 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.ShareTransferResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:44.358 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 16.4526ms
2025-08-13 11:49:44.361 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllShareTransactionAsync (DoorCompany.Api) in 11.718ms
2025-08-13 11:49:44.362 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:44.364 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllShareTransactionAsync (DoorCompany.Api)'
2025-08-13 11:49:44.366 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=false - 200 null application/json; charset=utf-8 55.6275ms
2025-08-13 11:49:44.373 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/get-all-sharetransfer?fromDate=2025-08-13&toDate=2025-08-13 - 200 null application/json; charset=utf-8 37.016ms
2025-08-13 11:49:45.006 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - null null
2025-08-13 11:49:45.009 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:49:45.011 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:45.012 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners(Boolean) on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:49:45.018 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialCapital], [p].[IsActive], [p].[IsDeleted], [p].[Name], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
2025-08-13 11:49:45.023 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:49:45.025 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api) in 10.557ms
2025-08-13 11:49:45.028 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartners (DoorCompany.Api)'
2025-08-13 11:49:45.030 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner?isDelete=true - 200 null application/json; charset=utf-8 24.4027ms
2025-08-13 11:50:04.096 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:50:04.100 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:50:04.101 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 5.6927ms
2025-08-13 11:50:04.106 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:50:04.109 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:50:04.111 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:50:04.113 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:50:04.119 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:50:04.127 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:50:04.131 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:50:04.136 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:50:04.138 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:50:04.140 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 24.3765ms
2025-08-13 11:50:04.141 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:50:04.148 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 42.0395ms
2025-08-13 11:50:39.327 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-13 11:50:39.343 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:50:39.343 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:50:39.356 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:50:39.357 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 29.4595ms
2025-08-13 11:50:39.358 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 15.5437ms
2025-08-13 11:50:39.375 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:50:39.382 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-13 11:50:39.385 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:50:39.387 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:50:39.388 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:50:39.389 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:50:39.390 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:50:39.391 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-13 11:50:39.400 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:50:39.404 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-13 11:50:39.407 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:50:39.408 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:50:39.427 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:50:39.428 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 29.2274ms
2025-08-13 11:50:39.433 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:50:39.434 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:50:39.435 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 52.9084ms
2025-08-13 11:50:39.437 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:50:39.445 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 49.5529ms
2025-08-13 11:50:39.447 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:50:39.448 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 72.6312ms
2025-08-13 11:51:24.443 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-13 11:51:24.456 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:51:24.458 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 15.3163ms
2025-08-13 11:51:24.472 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-13 11:51:24.472 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:51:24.482 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:51:24.485 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:51:24.486 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:51:24.487 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 14.8709ms
2025-08-13 11:51:24.488 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-13 11:51:24.511 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:51:24.524 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-13 11:51:24.525 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:51:24.528 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:51:24.529 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:51:24.530 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 11.7338ms
2025-08-13 11:51:24.531 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:51:24.533 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:51:24.535 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 65.3774ms
2025-08-13 11:51:24.538 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:51:24.549 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:51:24.553 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:51:24.557 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:51:24.559 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:51:24.560 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 25.8761ms
2025-08-13 11:51:24.567 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:51:24.568 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 57.075ms
2025-08-13 11:53:55.040 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - null null
2025-08-13 11:53:55.273 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:53:55.425 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:53:55.806 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:53:55.890 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/User/profile - 204 null null 849.6046ms
2025-08-13 11:53:55.891 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5184/api/Partner/partners-summary - 204 null null 618.3162ms
2025-08-13 11:53:56.183 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - null null
2025-08-13 11:53:56.434 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5184/api/User/profile - null null
2025-08-13 11:53:56.553 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:53:56.774 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:53:56.773 +03:00 [INF] CORS policy execution successful.
2025-08-13 11:53:56.796 +03:00 [INF] Route matched with {action = "GetAllPartnerDataSummer", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartnerDataSummer() on controller DoorCompany.Api.Controllers.PartnerController (DoorCompany.Api).
2025-08-13 11:53:56.798 +03:00 [INF] Executing endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:53:57.120 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller DoorCompany.Api.Controllers.UserController (DoorCompany.Api).
2025-08-13 11:53:57.311 +03:00 [INF] Executed DbCommand (187ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Name], [p].[InitialCapital]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:53:57.404 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[RoleId], [s].[UserId], [s].[Id0], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[Name], [s].[UpdatedAt], [s].[UpdatedBy]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[RoleId], [u0].[UserId], [r].[Id] AS [Id0], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-08-13 11:53:57.444 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerBandId], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:53:57.468 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[DoorCompany.Service.Dtos.UserDto.UserResponseDto, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-13 11:53:57.612 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[BuyerId], [s].[CreatedAt], [s].[CreatedBy], [s].[Description], [s].[IsActive], [s].[IsDeleted], [s].[SellerId], [s].[SharesCount], [s].[TransferAmount], [s].[TransfersDate], [s].[UpdatedAt], [s].[UpdatedBy]
FROM [ShareTransfers] AS [s]
WHERE [s].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:53:57.618 +03:00 [INF] Executed action DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api) in 232.9411ms
2025-08-13 11:53:57.698 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.UserController.GetProfile (DoorCompany.Api)'
2025-08-13 11:53:57.698 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[Code], [c].[CommercialRegister], [c].[CreatedAt], [c].[CreatedBy], [c].[Description], [c].[EstablishmentDate], [c].[IndustrialRegister], [c].[IsActive], [c].[IsDeleted], [c].[LogoPath], [c].[Name], [c].[Symbol], [c].[TaxRegister], [c].[TotalShares], [c].[UpdatedAt], [c].[UpdatedBy]
FROM [Companies] AS [c]
WHERE [c].[IsDeleted] = CAST(0 AS bit)
2025-08-13 11:53:57.699 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/User/profile - 200 null application/json; charset=utf-8 1264.8301ms
2025-08-13 11:53:57.714 +03:00 [INF] Executing OkObjectResult, writing value of type 'DoorCompany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[DoorCompany.Service.Dtos.PartnerDto.PartnerDataSummary, DoorCompany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-13 11:53:57.742 +03:00 [INF] Executed action DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api) in 622.3868ms
2025-08-13 11:53:57.743 +03:00 [INF] Executed endpoint 'DoorCompany.Api.Controllers.PartnerController.GetAllPartnerDataSummer (DoorCompany.Api)'
2025-08-13 11:53:57.745 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5184/api/Partner/partners-summary - 200 null application/json; charset=utf-8 1561.5262ms
