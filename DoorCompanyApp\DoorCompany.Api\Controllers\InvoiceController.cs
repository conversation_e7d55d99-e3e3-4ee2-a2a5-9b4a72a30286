using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos.ProductDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using DoorCompany.Service.Services;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InvoiceController : AppControllerBase
    {
        public InvoiceController(IUnitOfWork work) : base(work)
        {
        }

        /// <summary>
        /// إنشاء فاتورة جديدة
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CreateInvoice([FromBody] CreateInvoiceMasterDto createDto)
        {
            var result = await _work.InvoiceRepository.CreateInvoiceAsync(createDto);
            return NewResult(result);
        }

        /// <summary>
        /// تحديث فاتورة موجودة
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateInvoice(int id, [FromBody] UpdateInvoiceMasterDto updateDto)
        {
            var result = await _work.InvoiceRepository.UpdateInvoiceAsync(id, updateDto);
            return NewResult(result);
        }

        /// <summary>
        /// حذف فاتورة
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteInvoice(int id)
        {
            var result = await _work.InvoiceRepository.DeleteInvoiceAsync(id);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على فاتورة بالمعرف
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetInvoiceById(int id)
        {
            var result = await _work.InvoiceRepository.GetInvoiceByIdAsync(id);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على قائمة الفواتير مع التصفية والترقيم
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetInvoices([FromQuery] InvoiceFilterDto filterDto)
        {
            var result = await _work.InvoiceRepository.GetInvoicesAsync(filterDto);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على فواتير عميل معين
        /// </summary>
        [HttpGet("customer/{customerId}")]
        public async Task<IActionResult> GetCustomerInvoices(int customerId, [FromQuery] InvoiceFilterDto filterDto)
        {
            var result = await _work.InvoiceRepository.GetCustomerInvoicesAsync(customerId, filterDto);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على فواتير مورد معين
        /// </summary>
        [HttpGet("supplier/{supplierId}")]
        public async Task<IActionResult> GetSupplierInvoices(int supplierId, [FromQuery] InvoiceFilterDto filterDto)
        {
            var result = await _work.InvoiceRepository.GetSupplierInvoicesAsync(supplierId, filterDto);
            return NewResult(result);
        }

        /// <summary>
        /// تسديد فاتورة (كامل أو جزئي)
        /// </summary>
        [HttpPost("{id}/pay")]
        public async Task<IActionResult> PayInvoice(int id, [FromBody] PayInvoiceDto paymentDto)
        {
            var result = await _work.InvoiceRepository.PayInvoiceAsync(id, paymentDto);
            return NewResult(result);
        }

        /// <summary>
        /// إلغاء تسديد فاتورة
        /// </summary>
        [HttpPost("{id}/cancel-payment")]
        public async Task<IActionResult> CancelPayment(int id)
        {
            var result = await _work.InvoiceRepository.CancelPaymentAsync(id);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على تقرير مبيعات/مشتريات
        /// </summary>
        [HttpGet("report")]
        public async Task<IActionResult> GetInvoiceReport([FromQuery] InvoiceReportFilterDto filterDto)
        {
            var result = await _work.InvoiceRepository.GetInvoiceReportAsync(filterDto);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على حساب عميل
        /// </summary>
        [HttpGet("customer-account/{customerId}")]
        public async Task<IActionResult> GetCustomerAccount(int customerId, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var result = await _work.InvoiceRepository.GetCustomerAccountAsync(customerId, fromDate, toDate);
            return NewResult(result);
        }

        /// <summary>
        /// الحصول على حساب مورد
        /// </summary>
        [HttpGet("supplier-account/{supplierId}")]
        public async Task<IActionResult> GetSupplierAccount(int supplierId, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var result = await _work.InvoiceRepository.GetSupplierAccountAsync(supplierId, fromDate, toDate);
            return NewResult(result);
        }
    }
}
