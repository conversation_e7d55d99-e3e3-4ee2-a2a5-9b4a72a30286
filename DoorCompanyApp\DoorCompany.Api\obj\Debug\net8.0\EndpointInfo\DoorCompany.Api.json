{"openapi": "3.0.1", "info": {"title": "Door Company App API", "version": "v1"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/validate-token": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Category": {"get": {"tags": ["Category"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Category"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Description", "Name"], "type": "object", "properties": {"ParentCategoryId": {"type": "integer", "format": "int32"}, "Code": {"type": "string"}, "Symbol": {"type": "string"}, "CategoryTypeId": {"type": "integer", "format": "int32"}, "ImageFile": {"type": "string", "format": "binary"}, "SortOrder": {"type": "integer", "format": "int32"}, "Name": {"type": "string"}, "Description": {"type": "string"}, "Id": {"type": "integer", "format": "int32"}, "CreatedAt": {"type": "string", "format": "date-time"}, "CreatedBy": {"type": "integer", "format": "int32"}, "IsActive": {"type": "boolean"}}}, "encoding": {"ParentCategoryId": {"style": "form"}, "Code": {"style": "form"}, "Symbol": {"style": "form"}, "CategoryTypeId": {"style": "form"}, "ImageFile": {"style": "form"}, "SortOrder": {"style": "form"}, "Name": {"style": "form"}, "Description": {"style": "form"}, "Id": {"style": "form"}, "CreatedAt": {"style": "form"}, "CreatedBy": {"style": "form"}, "IsActive": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Category/{id}": {"get": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Image/UploadImage": {"post": {"tags": ["Image"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "folder": {"type": "string", "default": "general"}}}, "encoding": {"file": {"style": "form"}, "folder": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Image/{imagePath}": {"get": {"tags": ["Image"], "parameters": [{"name": "imagePath", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Image/GetFullPath{imagePath}": {"get": {"tags": ["Image"], "parameters": [{"name": "imagePath", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Image/GetFullPathUrl": {"get": {"tags": ["Image"], "parameters": [{"name": "pathstring", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Mainaction": {"get": {"tags": ["Mainaction"], "responses": {"200": {"description": "OK"}}}}, "/api/Mainaction/{id}": {"get": {"tags": ["Mainaction"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Mainaction/MainActionByAction/{parentId}": {"get": {"tags": ["Mainaction"], "parameters": [{"name": "parentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Partner": {"get": {"tags": ["Partner"], "parameters": [{"name": "isDelete", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Partner"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePartnerDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePartnerDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePartnerDto"}}}}, "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Partner"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePartnerDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePartnerDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePartnerDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Partner/{id}": {"get": {"tags": ["Partner"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Partner"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Partner/get-all-transactions": {"get": {"tags": ["Partner"], "parameters": [{"name": "partnerId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "bandId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Partner/transaction/{id}": {"get": {"tags": ["Partner"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Partner/transactions": {"post": {"tags": ["Partner"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["ActionDetailId", "Amount", "Description", "PartnerBandId", "PartnerId", "TransactionDate"], "type": "object", "properties": {"TransactionDate": {"type": "string", "format": "date-time"}, "ActionDetailId": {"type": "integer", "format": "int32"}, "PartnerId": {"type": "integer", "format": "int32"}, "PartnerBandId": {"type": "integer", "format": "int32"}, "Amount": {"minimum": 0.01, "type": "number", "format": "double"}, "Description": {"maxLength": 500, "minLength": 0, "type": "string"}, "Notes": {"maxLength": 1000, "minLength": 0, "type": "string"}, "ImagePath": {"type": "string", "format": "binary"}, "Id": {"type": "integer", "format": "int32"}, "CreatedAt": {"type": "string", "format": "date-time"}, "CreatedBy": {"type": "integer", "format": "int32"}, "IsActive": {"type": "boolean"}}}, "encoding": {"TransactionDate": {"style": "form"}, "ActionDetailId": {"style": "form"}, "PartnerId": {"style": "form"}, "PartnerBandId": {"style": "form"}, "Amount": {"style": "form"}, "Description": {"style": "form"}, "Notes": {"style": "form"}, "ImagePath": {"style": "form"}, "Id": {"style": "form"}, "CreatedAt": {"style": "form"}, "CreatedBy": {"style": "form"}, "IsActive": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Partner"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["ActionDetailId", "Amount", "Description", "PartnerBandId", "PartnerId", "TransactionDate"], "type": "object", "properties": {"TransactionDate": {"type": "string", "format": "date-time"}, "ActionDetailId": {"type": "integer", "format": "int32"}, "PartnerId": {"type": "integer", "format": "int32"}, "PartnerBandId": {"type": "integer", "format": "int32"}, "Amount": {"minimum": 0.01, "type": "number", "format": "double"}, "Description": {"maxLength": 500, "minLength": 0, "type": "string"}, "Notes": {"maxLength": 1000, "minLength": 0, "type": "string"}, "ImagePath": {"type": "string", "format": "binary"}, "Id": {"type": "integer", "format": "int32"}, "UpdatedAt": {"type": "string", "format": "date-time"}, "UpdatedBy": {"type": "integer", "format": "int32"}, "IsDeleted": {"type": "boolean"}, "IsActive": {"type": "boolean"}}}, "encoding": {"TransactionDate": {"style": "form"}, "ActionDetailId": {"style": "form"}, "PartnerId": {"style": "form"}, "PartnerBandId": {"style": "form"}, "Amount": {"style": "form"}, "Description": {"style": "form"}, "Notes": {"style": "form"}, "ImagePath": {"style": "form"}, "Id": {"style": "form"}, "UpdatedAt": {"style": "form"}, "UpdatedBy": {"style": "form"}, "IsDeleted": {"style": "form"}, "IsActive": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Partner/transactions/{id}": {"delete": {"tags": ["Partner"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Partner/transactions/report": {"get": {"tags": ["Partner"], "parameters": [{"name": "partnerId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "bandId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Partner/get-all-sharetransfer": {"get": {"tags": ["Partner"], "parameters": [{"name": "partnerId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Partner/share-transfer/{id}": {"get": {"tags": ["Partner"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Partner/share-transfer": {"post": {"tags": ["Partner"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateShareTransferDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateShareTransferDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateShareTransferDto"}}}}, "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Partner"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateShareTransferDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateShareTransferDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateShareTransferDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Partner/partners-summary": {"get": {"tags": ["Partner"], "responses": {"200": {"description": "OK"}}}}, "/api/PartnerBand": {"get": {"tags": ["PartnerBand"], "parameters": [{"name": "isDelete", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["PartnerBand"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePartnerBandDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePartnerBandDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePartnerBandDto"}}}}, "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["PartnerBand"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePartnerBandDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePartnerBandDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePartnerBandDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PartnerBand/{id}": {"get": {"tags": ["PartnerBand"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["PartnerBand"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Permission/all-Permission": {"get": {"tags": ["Permission"], "responses": {"200": {"description": "OK"}}}}, "/api/Permission": {"post": {"tags": ["Permission"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePermissionDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Product/{id}": {"get": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Product": {"post": {"tags": ["Product"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Barcode", "CategoryId", "Code", "Description", "Name", "StandardCost", "UnitId"], "type": "object", "properties": {"Code": {"type": "string"}, "Barcode": {"type": "string"}, "CategoryId": {"type": "integer", "format": "int32"}, "UnitId": {"type": "integer", "format": "int32"}, "StandardCost": {"minimum": 0.01, "type": "number", "format": "double"}, "MinimumStock": {"type": "number", "format": "double"}, "MaximumStock": {"type": "number", "format": "double"}, "OpeningBalance": {"type": "number", "format": "double"}, "SortOrder": {"type": "integer", "format": "int32"}, "ItemType": {"type": "integer", "format": "int32"}, "ImagePath": {"type": "string", "format": "binary"}, "Name": {"type": "string"}, "Description": {"type": "string"}, "Id": {"type": "integer", "format": "int32"}, "CreatedAt": {"type": "string", "format": "date-time"}, "CreatedBy": {"type": "integer", "format": "int32"}, "IsActive": {"type": "boolean"}}}, "encoding": {"Code": {"style": "form"}, "Barcode": {"style": "form"}, "CategoryId": {"style": "form"}, "UnitId": {"style": "form"}, "StandardCost": {"style": "form"}, "MinimumStock": {"style": "form"}, "MaximumStock": {"style": "form"}, "OpeningBalance": {"style": "form"}, "SortOrder": {"style": "form"}, "ItemType": {"style": "form"}, "ImagePath": {"style": "form"}, "Name": {"style": "form"}, "Description": {"style": "form"}, "Id": {"style": "form"}, "CreatedAt": {"style": "form"}, "CreatedBy": {"style": "form"}, "IsActive": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Product/invoice": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceMasterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceMasterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceMasterDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Role/All-Roles": {"get": {"tags": ["Role"], "responses": {"200": {"description": "OK"}}}}, "/api/Role/{id}": {"get": {"tags": ["Role"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Role": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}}}, "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Role/add-Remove-Role-Permmision": {"post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRolePermissionsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRolePermissionsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRolePermissionsDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Share": {"get": {"tags": ["Share"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Share"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateShareDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateShareDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateShareDto"}}}}, "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Share"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateShareDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateShareDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateShareDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Share/{id}": {"get": {"tags": ["Share"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Share"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User/all-users": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}, "/api/User/{id}": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/change-password": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/reset-password": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/All-UserRoles": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}, "/api/User/{userId}/user-Roles": {"get": {"tags": ["User"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User/add-Remove-user-Roles": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRolesDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRolesDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRolesDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/profile": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["User"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["FullName", "Id"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "FullName": {"maxLength": 100, "minLength": 0, "type": "string"}, "Phone": {"maxLength": 20, "minLength": 0, "type": "string"}, "ProfileImage": {"type": "string", "format": "binary"}}}, "encoding": {"Id": {"style": "form"}, "FullName": {"style": "form"}, "Phone": {"style": "form"}, "ProfileImage": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/profile-image": {"put": {"tags": ["User"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Id"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "ProfileImage": {"type": "string", "format": "binary"}}}, "encoding": {"Id": {"style": "form"}, "ProfileImage": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"ChangePasswordDto": {"required": ["confirmPassword", "currentPassword", "newPassword", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "currentPassword": {"minLength": 1, "type": "string"}, "newPassword": {"maxLength": 100, "minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CheckBoxViewModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "displayValue": {"type": "string", "nullable": true}, "isSelected": {"type": "boolean"}}, "additionalProperties": false}, "CreateInvoiceItemDto": {"required": ["productId", "quantity", "unitPrice"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "updatedBy": {"type": "integer", "format": "int32", "nullable": true}, "isDeleted": {"type": "boolean"}, "isActive": {"type": "boolean"}, "invoiceMasterId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double"}, "discountPercentage": {"type": "number", "format": "double"}, "discountAmount": {"type": "number", "format": "double"}, "totalPrice": {"type": "number", "format": "double"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateInvoiceMasterDto": {"required": ["invoiceType", "partyId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "invoiceNumber": {"type": "string", "nullable": true}, "invoiceType": {"$ref": "#/components/schemas/InvoiceType"}, "invoiceDate": {"type": "string", "format": "date-time"}, "partyId": {"type": "integer", "format": "int32"}, "subTotal": {"type": "number", "format": "double"}, "itemDiscountAmount": {"type": "number", "format": "double"}, "invoiceDiscountAmount": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "paidAmount": {"type": "number", "format": "double"}, "remainingAmount": {"type": "number", "format": "double"}, "paymentType": {"$ref": "#/components/schemas/PaymentType"}, "isPaid": {"type": "boolean"}, "notes": {"type": "string", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CreateInvoiceItemDto"}, "nullable": true}}, "additionalProperties": false}, "CreatePartnerBandDto": {"required": ["description", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "name": {"minLength": 1, "type": "string"}, "description": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CreatePartnerDto": {"required": ["description", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "name": {"minLength": 1, "type": "string"}, "description": {"minLength": 1, "type": "string"}, "initialCapital": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "CreatePermissionDto": {"required": ["description", "name"], "type": "object", "properties": {"name": {"minLength": 1, "type": "string"}, "description": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CreateRoleDto": {"required": ["<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"roleName": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CreateShareDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int32"}, "sharesCount": {"type": "integer", "format": "int32"}, "distributionDate": {"type": "string", "format": "date-time"}, "shareValue": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateShareTransferDto": {"required": ["buyerId", "sellerId", "sharesCount", "transferAmount", "transfersDate"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "transfersDate": {"type": "string", "format": "date-time"}, "buyerId": {"type": "integer", "format": "int32"}, "sellerId": {"type": "integer", "format": "int32"}, "sharesCount": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "transferAmount": {"minimum": 0.01, "type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateUserDto": {"required": ["fullName", "password", "userName"], "type": "object", "properties": {"userName": {"maxLength": 50, "minLength": 0, "type": "string"}, "fullName": {"maxLength": 100, "minLength": 0, "type": "string"}, "password": {"maxLength": 100, "minLength": 6, "type": "string"}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "profileImage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InvoiceType": {"enum": [1, 2, 3, 4, 5, 6, 7], "type": "integer", "format": "int32"}, "LoginDto": {"required": ["password", "userName"], "type": "object", "properties": {"userName": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "PaymentType": {"enum": [1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "RefreshTokenDto": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdatePartnerBandDto": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"type": "integer", "format": "int32", "nullable": true}, "isDeleted": {"type": "boolean"}, "isActive": {"type": "boolean"}, "name": {"minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdatePartnerDto": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"type": "integer", "format": "int32", "nullable": true}, "isDeleted": {"type": "boolean"}, "isActive": {"type": "boolean"}, "name": {"minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "initialCapital": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "UpdateRoleDto": {"required": ["id", "<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "roleName": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "UpdateRolePermissionsDto": {"type": "object", "properties": {"roleId": {"type": "integer", "format": "int32"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/CheckBoxViewModel"}, "nullable": true}}, "additionalProperties": false}, "UpdateShareDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"type": "integer", "format": "int32", "nullable": true}, "isDeleted": {"type": "boolean"}, "isActive": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int32"}, "sharesCount": {"type": "integer", "format": "int32"}, "distributionDate": {"type": "string", "format": "date-time"}, "shareValue": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateShareTransferDto": {"required": ["buyerId", "sellerId", "sharesCount", "transferAmount", "transfersDate"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"type": "integer", "format": "int32", "nullable": true}, "isDeleted": {"type": "boolean"}, "isActive": {"type": "boolean"}, "transfersDate": {"type": "string", "format": "date-time"}, "buyerId": {"type": "integer", "format": "int32"}, "sellerId": {"type": "integer", "format": "int32"}, "sharesCount": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "transferAmount": {"minimum": 0.01, "type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserDto": {"required": ["fullName", "id", "userName"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userName": {"maxLength": 50, "minLength": 0, "type": "string"}, "fullName": {"maxLength": 100, "minLength": 0, "type": "string"}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "profileImage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserRolesDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/CheckBoxViewModel"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}