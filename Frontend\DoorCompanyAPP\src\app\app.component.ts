import { Component, HostListener } from '@angular/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  styleUrl: './app.component.css'
})
export class AppComponent  {
  title = 'DoorCompanyAPP';

  mobileMenuOpen = false;
  sidebarCollapsed = false;
  isMobile = false;
  userMenuOpen = false;

  constructor() {
   
  }
  @HostListener('window:resize')
  onResize(): void {
    this.checkScreenSize();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: any): void {
    if (!event.target.closest('.user-info') && !event.target.closest('.user-menu')) {
      this.userMenuOpen = false;
    }
  }

 
private checkScreenSize(): void {
    this.isMobile = window.innerWidth <= 768;
    if (!this.isMobile) {
      this.mobileMenuOpen = false;
    }
  }


toggleSidebar(): void {
    if (this.isMobile) {
      this.mobileMenuOpen = !this.mobileMenuOpen;
    } else {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    }
  }

  closeMobileMenu(): void {
    this.mobileMenuOpen = false;
  }

toggleUserMenu(): void {
    this.userMenuOpen = !this.userMenuOpen;
  }

 onNavItemClick(): void {
    if (this.isMobile) {
      this.mobileMenuOpen = false;
    }
  }

}
