﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using DoorCompany.Service.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ImageController : AppControllerBase
    {
        private readonly IFileUploadService _fileUploadService;
        public ImageController(IUnitOfWork work, IFileUploadService fileUploadService) : base(work)
        {
            _fileUploadService = fileUploadService;
        }


        [HttpPost("UploadImage")]
        public async Task<IActionResult> CreatePermissionAsync(IFormFile file, [FromForm] string folder = "general")
        {
            if (file == null || file.Length == 0)
                return BadRequest("لم يتم تحديد ملف");

            var response = await _fileUploadService.UploadImageAsync(file, folder);
            return Ok(new
            {
                success = true,
                message = "تم رفع الصورة بنجاح",
                imagePath = response
            });
        }

        /// <summary>
        /// Get image
        /// </summary>
        [HttpGet("{*imagePath}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetImage(string imagePath)
        {
            try
            {
                var imageBytes = await _fileUploadService.GetImageAsync(imagePath);
                if (imageBytes == null)
                    return NotFound("الصورة غير موجودة");

                var contentType = GetContentType(imagePath);
                return File(imageBytes, contentType);
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء جلب الصورة: {ex.Message}");
            }
        }
        /// <summary>
        /// Get full PathIMAGE
        /// </summary>
        [HttpGet("GetFullPath{imagePath}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetFullImage(string imagePath)
        {
            try
            {
                string imageBytes = await _fileUploadService.GetFullImagePath(imagePath);
                if (imageBytes == null)
                    return NotFound("الصورة غير موجودة");

                return Ok(imageBytes);
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء جلب الصورة: {ex.Message}");
            }
        }
        /// <summary>
        /// Get full PathIMAGEURL
        /// </summary>
        [HttpGet("GetFullPathUrl")]
        [AllowAnonymous]
        public async Task<IActionResult> GetFullImageUrl([FromQuery] string pathstring )
        {
            try
            {
                string url = await _fileUploadService.GetFullImagePathUrl(Request, pathstring);
                if (url == null)
                    return NotFound("الصورة غير موجودة");

                return Ok(new { imageUrl = url });
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء جلب الصورة: {ex.Message}");
            }
        }
        
        private string GetContentType(string imagePath)
        {
            var extension = Path.GetExtension(imagePath).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".webp" => "image/webp",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                _ => "application/octet-stream"
            };
        }
    }
}
