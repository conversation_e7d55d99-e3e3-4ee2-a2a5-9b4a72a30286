﻿using DoorCompany.Service.Repositories.Implementations;
using DoorCompany.Service.Repositories.Implementations.Basic;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using DoorCompany.Service.Services;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.ModuleDependencies
{
    public static class ModuleRepositoriesDependencies
    {
        public static IServiceCollection AddRepositoriesDependencies(this IServiceCollection services)
        {
            // Generic Repository
            services.AddTransient(typeof(IGenericRepository<>), typeof(GenericRepository<>));

            // Unit of Work
            services.AddScoped<IUnitOfWorkOfService, UnitOfWorkOfService>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            //// Individual Repository Services
            //services.AddScoped<IItemRepository, ItemService>();
            services.AddScoped<IPartnerRepository, PartnerService>();
            services.AddScoped<IUserRepository, UserService>();
            services.AddScoped<IMainActionRepository, MainactionService>();
            services.AddScoped<IShareRepository, ShareService>();
            services.AddScoped<IProductRepository, ProductService>();

            //services.AddScoped<IInventoryRepository, InventoryService>();
            //services.AddScoped<IFinancialRepository, FinancialService>();
            //services.AddScoped<IInvoiceRepository, InvoiceService>();

            //// Financial Integration Service
            //services.AddScoped<IFinancialTransactionIntegrationService, FinancialTransactionIntegrationService>();

            // TODO: Add other repository services when implemented
            // services.AddScoped<ISupplierCustomerRepository, SupplierCustomerService>();


            //Image service 
            services.AddScoped<IFileUploadService, FileUploadService>();


            return services;
        }
    }
}
