using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Dtos.ProductDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProductController : AppControllerBase
    {
        public ProductController(IUnitOfWork work) : base(work)
        {
        }
        /// <summary>
        /// Get all partners
        /// </summary>
        //[HttpGet]
        //public async Task<IActionResult> GetAllPartners([FromQuery] bool isDelete = false)
        //{
        //    var response = await _work.PartnerRepository.GetAllPartnersAsync(isDelete);
        //    return NewResult(response);
        //}

        /// <summary>
        /// Get Product by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetProductById(int id)
        {
            var response = await _work.ProductRepository.GetProductByIdAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// create PartnerTransaction
        /// </summary>
        /// <param name="createDto"></param>
        [HttpPost]
        public async Task<IActionResult> CreateProductAsync([FromForm] CreateProductDto createDto)
        {
            if (createDto == null)
            {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.ProductRepository.CreateProductAsync(createDto);
            return NewResult(response);
        }
        /// <summary>
        /// create PartnerTransaction
        /// </summary>
        /// <param name="createDto"></param>
        [HttpPost("invoice")]
        public async Task<IActionResult> CreateInvoiceAsync(CreateInvoiceMasterDto createDto)
        {
            if (createDto == null)
            {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.ProductRepository.CreateInvoiceAsync(createDto);
            return NewResult(response);
        }

        
    }
}
