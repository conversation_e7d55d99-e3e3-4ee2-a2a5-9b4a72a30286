﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Data.Models
{
    public class Partner : BaseName
    {
        /// <summary>
        /// رأس المال الذي قدّمه الشريك عند التأسيس.
        /// </summary>
       [Column(TypeName = "decimal(18,2)")]
       public decimal? InitialCapital { get; set; }
       public virtual ICollection<PartnerTransation> PartnerTransations { get; set; } = new List<PartnerTransation>();
        public virtual ICollection<ShareTransfer> BuyTransactions { get; set; } = new List<ShareTransfer>();
        public virtual ICollection<ShareTransfer> SellTransactions { get; set; } = new List<ShareTransfer>();

    }

    public class PartnerBand : BaseName
    {     
    }
    public class PartnerTransation : BaseEntity
    {
        public DateTime TransactionDate { get; set; }
        public int ActionDetailId { get; set; }  //Investment  //Withdrawal 
        public int PartnerId { get; set; }
        public int PartnerBandId { get; set; }
     
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public string? ImagePath { get; set; }

        [ForeignKey(nameof(PartnerBandId))]
        public virtual PartnerBand PartnerBand { get; set; } = null!;

        [ForeignKey(nameof(PartnerId))]
        public virtual Partner Partners { get; set; } = null!;

        [ForeignKey(nameof(ActionDetailId))]
        public virtual MainAction MainAction { get; set; } = null!;
    }
}
