﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos.UserDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : AppControllerBase
    {
        public AuthController(IUnitOfWork work) : base(work)
        {
        }


        /// <summary>
        /// User login
        /// </summary>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login(LoginDto loginDto)
        {
            var response = await _work.UserRepository.LoginAsync(loginDto);
            return NewResult(response);
        }

        /// <summary>
        /// Refresh access token
        /// </summary>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken(RefreshTokenDto refreshTokenDto)
        {
            var response = await _work.UserRepository.RefreshTokenAsync(refreshTokenDto);
            return NewResult(response);
        }

        /// <summary>
        /// User logout
        /// </summary>
        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
            var response = await _work.UserRepository.LogoutAsync(userId);
            return NewResult(response);
        }

        /// <summary>
        /// Validate token
        /// </summary>
        [HttpPost("validate-token")]
        [Authorize]
        public async Task<IActionResult> ValidateToken()
        {
            var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
            var response = await _work.UserRepository.ValidateTokenAsync(token);
            return NewResult(response);
        }

    }
}
