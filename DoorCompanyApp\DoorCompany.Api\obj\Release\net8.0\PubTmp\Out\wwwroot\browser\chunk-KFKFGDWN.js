var gw=Object.defineProperty,mw=Object.defineProperties;var vw=Object.getOwnPropertyDescriptors;var wi=Object.getOwnPropertySymbols;var Eh=Object.prototype.hasOwnProperty,wh=Object.prototype.propertyIsEnumerable;var Dh=(e,t,n)=>t in e?gw(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,E=(e,t)=>{for(var n in t||={})Eh.call(t,n)&&Dh(e,n,t[n]);if(wi)for(var n of wi(t))wh.call(t,n)&&Dh(e,n,t[n]);return e},U=(e,t)=>mw(e,vw(t));var Ih=(e,t)=>{var n={};for(var r in e)Eh.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&wi)for(var r of wi(e))t.indexOf(r)<0&&wh.call(e,r)&&(n[r]=e[r]);return n};var ut=(e,t,n)=>new Promise((r,o)=>{var i=c=>{try{a(n.next(c))}catch(u){o(u)}},s=c=>{try{a(n.throw(c))}catch(u){o(u)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);a((n=n.apply(e,t)).next())});function Gc(e,t){return Object.is(e,t)}var se=null,Ii=!1,Wc=1,Me=Symbol("SIGNAL");function k(e){let t=se;return se=e,t}function Zc(){return se}var tr={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function so(e){if(Ii)throw new Error("");if(se===null)return;se.consumerOnSignalRead(e);let t=se.nextProducerIndex++;if(Mi(se),t<se.producerNode.length&&se.producerNode[t]!==e&&io(se)){let n=se.producerNode[t];Si(n,se.producerIndexOfThis[t])}se.producerNode[t]!==e&&(se.producerNode[t]=e,se.producerIndexOfThis[t]=io(se)?bh(e,se,t):0),se.producerLastReadVersion[t]=e.version}function Ch(){Wc++}function Yc(e){if(!(io(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Wc)){if(!e.producerMustRecompute(e)&&!Ti(e)){qc(e);return}e.producerRecomputeValue(e),qc(e)}}function Qc(e){if(e.liveConsumerNode===void 0)return;let t=Ii;Ii=!0;try{for(let n of e.liveConsumerNode)n.dirty||yw(n)}finally{Ii=t}}function Kc(){return se?.consumerAllowSignalWrites!==!1}function yw(e){e.dirty=!0,Qc(e),e.consumerMarkedDirty?.(e)}function qc(e){e.dirty=!1,e.lastCleanEpoch=Wc}function ao(e){return e&&(e.nextProducerIndex=0),k(e)}function bi(e,t){if(k(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(io(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Si(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Ti(e){Mi(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Yc(n),r!==n.version))return!0}return!1}function co(e){if(Mi(e),io(e))for(let t=0;t<e.producerNode.length;t++)Si(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function bh(e,t,n){if(Th(e),e.liveConsumerNode.length===0&&Sh(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=bh(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Si(e,t){if(Th(e),e.liveConsumerNode.length===1&&Sh(e))for(let r=0;r<e.producerNode.length;r++)Si(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Mi(o),o.producerIndexOfThis[r]=t}}function io(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Mi(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Th(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Sh(e){return e.producerNode!==void 0}function _i(e,t){let n=Object.create(Dw);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Yc(n),so(n),n.value===Ci)throw n.error;return n.value};return r[Me]=n,r}var Hc=Symbol("UNSET"),zc=Symbol("COMPUTING"),Ci=Symbol("ERRORED"),Dw=U(E({},tr),{value:Hc,dirty:!0,error:null,equal:Gc,kind:"computed",producerMustRecompute(e){return e.value===Hc||e.value===zc},producerRecomputeValue(e){if(e.value===zc)throw new Error("Detected cycle in computations.");let t=e.value;e.value=zc;let n=ao(e),r,o=!1;try{r=e.computation(),k(null),o=t!==Hc&&t!==Ci&&r!==Ci&&e.equal(t,r)}catch(i){r=Ci,e.error=i}finally{bi(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Ew(){throw new Error}var Mh=Ew;function _h(e){Mh(e)}function Xc(e){Mh=e}var ww=null;function Jc(e,t){let n=Object.create(Ni);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(so(n),n.value);return r[Me]=n,r}function uo(e,t){Kc()||_h(e),e.equal(e.value,t)||(e.value=t,Iw(e))}function eu(e,t){Kc()||_h(e),uo(e,t(e.value))}var Ni=U(E({},tr),{equal:Gc,value:void 0,kind:"signal"});function Iw(e){e.version++,Ch(),Qc(e),ww?.()}function tu(e){let t=k(null);try{return e()}finally{k(t)}}var nu;function lo(){return nu}function Pt(e){let t=nu;return nu=e,t}var Ri=Symbol("NotFound");function A(e){return typeof e=="function"}function nr(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Ai=nr(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function In(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var te=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(A(r))try{r()}catch(i){t=i instanceof Ai?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Nh(i)}catch(s){t=t??[],s instanceof Ai?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Ai(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Nh(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&In(n,t)}remove(t){let{_finalizers:n}=this;n&&In(n,t),t instanceof e&&t._removeParent(this)}};te.EMPTY=(()=>{let e=new te;return e.closed=!0,e})();var ru=te.EMPTY;function xi(e){return e instanceof te||e&&"closed"in e&&A(e.remove)&&A(e.add)&&A(e.unsubscribe)}function Nh(e){A(e)?e():e.unsubscribe()}var Ze={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var rr={setTimeout(e,t,...n){let{delegate:r}=rr;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=rr;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Oi(e){rr.setTimeout(()=>{let{onUnhandledError:t}=Ze;if(t)t(e);else throw e})}function fo(){}var Rh=ou("C",void 0,void 0);function Ah(e){return ou("E",void 0,e)}function xh(e){return ou("N",e,void 0)}function ou(e,t,n){return{kind:e,value:t,error:n}}var Cn=null;function or(e){if(Ze.useDeprecatedSynchronousErrorHandling){let t=!Cn;if(t&&(Cn={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Cn;if(Cn=null,n)throw r}}else e()}function Oh(e){Ze.useDeprecatedSynchronousErrorHandling&&Cn&&(Cn.errorThrown=!0,Cn.error=e)}var bn=class extends te{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,xi(t)&&t.add(this)):this.destination=_w}static create(t,n,r){return new kt(t,n,r)}next(t){this.isStopped?su(xh(t),this):this._next(t)}error(t){this.isStopped?su(Ah(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?su(Rh,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Sw=Function.prototype.bind;function iu(e,t){return Sw.call(e,t)}var au=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Pi(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Pi(r)}else Pi(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Pi(n)}}},kt=class extends bn{constructor(t,n,r){super();let o;if(A(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Ze.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&iu(t.next,i),error:t.error&&iu(t.error,i),complete:t.complete&&iu(t.complete,i)}):o=t}this.destination=new au(o)}};function Pi(e){Ze.useDeprecatedSynchronousErrorHandling?Oh(e):Oi(e)}function Mw(e){throw e}function su(e,t){let{onStoppedNotification:n}=Ze;n&&rr.setTimeout(()=>n(e,t))}var _w={closed:!0,next:fo,error:Mw,complete:fo};var ir=typeof Symbol=="function"&&Symbol.observable||"@@observable";function he(e){return e}function cu(...e){return uu(e)}function uu(e){return e.length===0?he:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var L=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Rw(n)?n:new kt(n,r,o);return or(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Ph(r),new r((o,i)=>{let s=new kt({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[ir](){return this}pipe(...n){return uu(n)(this)}toPromise(n){return n=Ph(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Ph(e){var t;return(t=e??Ze.Promise)!==null&&t!==void 0?t:Promise}function Nw(e){return e&&A(e.next)&&A(e.error)&&A(e.complete)}function Rw(e){return e&&e instanceof bn||Nw(e)&&xi(e)}function lu(e){return A(e?.lift)}function _(e){return t=>{if(lu(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function M(e,t,n,r,o){return new du(e,t,n,r,o)}var du=class extends bn{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function sr(){return _((e,t)=>{let n=null;e._refCount++;let r=M(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var ar=class extends L{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,lu(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new te;let n=this.getSubject();t.add(this.source.subscribe(M(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=te.EMPTY)}return t}refCount(){return sr()(this)}};var kh=nr(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var J=(()=>{class e extends L{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new ki(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new kh}next(n){or(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){or(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){or(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ru:(this.currentObservers=null,i.push(n),new te(()=>{this.currentObservers=null,In(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new L;return n.source=this,n}}return e.create=(t,n)=>new ki(t,n),e})(),ki=class extends J{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:ru}};var pe=class extends J{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var ho={now(){return(ho.delegate||Date).now()},delegate:void 0};var po=class extends J{constructor(t=1/0,n=1/0,r=ho){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Fi=class extends te{constructor(t,n){super()}schedule(t,n=0){return this}};var go={setInterval(e,t,...n){let{delegate:r}=go;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=go;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Li=class extends Fi{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return go.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&go.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,In(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var cr=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};cr.now=ho.now;var ji=class extends cr{constructor(t,n=cr.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var mo=new ji(Li),Fh=mo;var De=new L(e=>e.complete());function Vi(e){return e&&A(e.schedule)}function fu(e){return e[e.length-1]}function Ui(e){return A(fu(e))?e.pop():void 0}function lt(e){return Vi(fu(e))?e.pop():void 0}function Lh(e,t){return typeof fu(e)=="number"?e.pop():t}function Vh(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function jh(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Tn(e){return this instanceof Tn?(this.v=e,this):new Tn(e)}function Uh(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(p){return Promise.resolve(p).then(f,d)}}function a(f,p){r[f]&&(o[f]=function(m){return new Promise(function(D,b){i.push([f,m,D,b])>1||c(f,m)})},p&&(o[f]=p(o[f])))}function c(f,p){try{u(r[f](p))}catch(m){h(i[0][3],m)}}function u(f){f.value instanceof Tn?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,p){f(p),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Bh(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof jh=="function"?jh(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var Bi=e=>e&&typeof e.length=="number"&&typeof e!="function";function $i(e){return A(e?.then)}function Hi(e){return A(e[ir])}function zi(e){return Symbol.asyncIterator&&A(e?.[Symbol.asyncIterator])}function qi(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Aw(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Gi=Aw();function Wi(e){return A(e?.[Gi])}function Zi(e){return Uh(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Tn(n.read());if(o)return yield Tn(void 0);yield yield Tn(r)}}finally{n.releaseLock()}})}function Yi(e){return A(e?.getReader)}function B(e){if(e instanceof L)return e;if(e!=null){if(Hi(e))return xw(e);if(Bi(e))return Ow(e);if($i(e))return Pw(e);if(zi(e))return $h(e);if(Wi(e))return kw(e);if(Yi(e))return Fw(e)}throw qi(e)}function xw(e){return new L(t=>{let n=e[ir]();if(A(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Ow(e){return new L(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Pw(e){return new L(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Oi)})}function kw(e){return new L(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function $h(e){return new L(t=>{Lw(e,t).catch(n=>t.error(n))})}function Fw(e){return $h(Zi(e))}function Lw(e,t){var n,r,o,i;return Vh(this,void 0,void 0,function*(){try{for(n=Bh(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function _e(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Qi(e,t=0){return _((n,r)=>{n.subscribe(M(r,o=>_e(r,e,()=>r.next(o),t),()=>_e(r,e,()=>r.complete(),t),o=>_e(r,e,()=>r.error(o),t)))})}function Ki(e,t=0){return _((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Hh(e,t){return B(e).pipe(Ki(t),Qi(t))}function zh(e,t){return B(e).pipe(Ki(t),Qi(t))}function qh(e,t){return new L(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Gh(e,t){return new L(n=>{let r;return _e(n,t,()=>{r=e[Gi](),_e(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>A(r?.return)&&r.return()})}function Xi(e,t){if(!e)throw new Error("Iterable cannot be null");return new L(n=>{_e(n,t,()=>{let r=e[Symbol.asyncIterator]();_e(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Wh(e,t){return Xi(Zi(e),t)}function Zh(e,t){if(e!=null){if(Hi(e))return Hh(e,t);if(Bi(e))return qh(e,t);if($i(e))return zh(e,t);if(zi(e))return Xi(e,t);if(Wi(e))return Gh(e,t);if(Yi(e))return Wh(e,t)}throw qi(e)}function Y(e,t){return t?Zh(e,t):B(e)}function C(...e){let t=lt(e);return Y(e,t)}function ur(e,t){let n=A(e)?e:()=>e,r=o=>o.error(n());return new L(t?o=>t.schedule(r,0,o):r)}function hu(e){return!!e&&(e instanceof L||A(e.lift)&&A(e.subscribe))}var Ft=nr(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Yh(e){return e instanceof Date&&!isNaN(e)}function F(e,t){return _((n,r)=>{let o=0;n.subscribe(M(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:jw}=Array;function Vw(e,t){return jw(t)?e(...t):e(t)}function Ji(e){return F(t=>Vw(e,t))}var{isArray:Uw}=Array,{getPrototypeOf:Bw,prototype:$w,keys:Hw}=Object;function es(e){if(e.length===1){let t=e[0];if(Uw(t))return{args:t,keys:null};if(zw(t)){let n=Hw(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function zw(e){return e&&typeof e=="object"&&Bw(e)===$w}function ts(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function vo(...e){let t=lt(e),n=Ui(e),{args:r,keys:o}=es(e);if(r.length===0)return Y([],t);let i=new L(qw(r,t,o?s=>ts(o,s):he));return n?i.pipe(Ji(n)):i}function qw(e,t,n=he){return r=>{Qh(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Qh(t,()=>{let u=Y(e[c],t),l=!1;u.subscribe(M(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Qh(e,t,n){e?_e(n,e,t):t()}function Kh(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&t.complete()},f=m=>u<r?p(m):c.push(m),p=m=>{i&&t.next(m),u++;let D=!1;B(n(m,l++)).subscribe(M(t,b=>{o?.(b),i?f(b):t.next(b)},()=>{D=!0},void 0,()=>{if(D)try{for(u--;c.length&&u<r;){let b=c.shift();s?_e(t,s,()=>p(b)):p(b)}h()}catch(b){t.error(b)}}))};return e.subscribe(M(t,f,()=>{d=!0,h()})),()=>{a?.()}}function ne(e,t,n=1/0){return A(t)?ne((r,o)=>F((i,s)=>t(r,i,o,s))(B(e(r,o))),n):(typeof t=="number"&&(n=t),_((r,o)=>Kh(r,o,e,n)))}function dt(e=1/0){return ne(he,e)}function Xh(){return dt(1)}function lr(...e){return Xh()(Y(e,lt(e)))}function ns(e){return new L(t=>{B(e()).subscribe(t)})}function Gw(...e){let t=Ui(e),{args:n,keys:r}=es(e),o=new L(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;B(n[l]).subscribe(M(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?ts(r,a):a),i.complete())}))}});return t?o.pipe(Ji(t)):o}function rs(e=0,t,n=Fh){let r=-1;return t!=null&&(Vi(t)?n=t:r=t),new L(o=>{let i=Yh(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Ww(...e){let t=lt(e),n=Lh(e,1/0),r=e;return r.length?r.length===1?B(r[0]):dt(n)(Y(r,t)):De}function de(e,t){return _((n,r)=>{let o=0;n.subscribe(M(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Jh(e){return _((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let u=o;o=null,n.next(u)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(M(n,u=>{r=!0,o=u,i||B(e(u)).subscribe(i=M(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Zw(e,t=mo){return Jh(()=>rs(e,t))}function ft(e){return _((t,n)=>{let r=null,o=!1,i;r=t.subscribe(M(n,void 0,void 0,s=>{i=B(e(s,ft(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function ep(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(M(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function ht(e,t){return A(t)?ne(e,t,1):ne(e,1)}function Yw(e,t=mo){return _((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function c(){let u=s+e,l=t.now();if(l<u){o=this.schedule(void 0,u-l),r.add(o);return}a()}n.subscribe(M(r,u=>{i=u,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function nn(e){return _((t,n)=>{let r=!1;t.subscribe(M(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Lt(e){return e<=0?()=>De:_((t,n)=>{let r=0;t.subscribe(M(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function tp(e,t=he){return e=e??Qw,_((n,r)=>{let o,i=!0;n.subscribe(M(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function Qw(e,t){return e===t}function os(e=Kw){return _((t,n)=>{let r=!1;t.subscribe(M(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Kw(){return new Ft}function rn(e){return _((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function jt(e,t){let n=arguments.length>=2;return r=>r.pipe(e?de((o,i)=>e(o,i,r)):he,Lt(1),n?nn(t):os(()=>new Ft))}function dr(e){return e<=0?()=>De:_((t,n)=>{let r=[];t.subscribe(M(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function pu(e,t){let n=arguments.length>=2;return r=>r.pipe(e?de((o,i)=>e(o,i,r)):he,dr(1),n?nn(t):os(()=>new Ft))}function Xw(){return _((e,t)=>{let n,r=!1;e.subscribe(M(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function Jw(e=1/0){let t;e&&typeof e=="object"?t=e:t={count:e};let{count:n=1/0,delay:r,resetOnSuccess:o=!1}=t;return n<=0?he:_((i,s)=>{let a=0,c,u=()=>{let l=!1;c=i.subscribe(M(s,d=>{o&&(a=0),s.next(d)},void 0,d=>{if(a++<n){let h=()=>{c?(c.unsubscribe(),c=null,u()):l=!0};if(r!=null){let f=typeof r=="number"?rs(r):B(r(d,a)),p=M(s,()=>{p.unsubscribe(),h()},()=>{s.complete()});f.subscribe(p)}else h()}else s.error(d)})),l&&(c.unsubscribe(),c=null,u())};u()})}function gu(e,t){return _(ep(e,t,arguments.length>=2,!0))}function vu(e={}){let{connector:t=()=>new J,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,u=0,l=!1,d=!1,h=()=>{a?.unsubscribe(),a=void 0},f=()=>{h(),s=c=void 0,l=d=!1},p=()=>{let m=s;f(),m?.unsubscribe()};return _((m,D)=>{u++,!d&&!l&&h();let b=c=c??t();D.add(()=>{u--,u===0&&!d&&!l&&(a=mu(p,o))}),b.subscribe(D),!s&&u>0&&(s=new kt({next:H=>b.next(H),error:H=>{d=!0,h(),a=mu(f,n,H),b.error(H)},complete:()=>{l=!0,h(),a=mu(f,r),b.complete()}}),B(m).subscribe(s))})(i)}}function mu(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new kt({next:()=>{r.unsubscribe(),e()}});return B(t(...n)).subscribe(r)}function eI(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,vu({connector:()=>new po(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function tI(e){return de((t,n)=>e<=n)}function yu(...e){let t=lt(e);return _((n,r)=>{(t?lr(e,n,t):lr(e,n)).subscribe(r)})}function Ie(e,t){return _((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(M(r,c=>{o?.unsubscribe();let u=0,l=i++;B(e(c,l)).subscribe(o=M(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Du(e){return _((t,n)=>{B(e).subscribe(M(n,()=>n.complete(),fo)),!n.closed&&t.subscribe(n)})}function nI(e,t=!1){return _((n,r)=>{let o=0;n.subscribe(M(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function oe(e,t,n){let r=A(e)||t||n?{next:e,error:t,complete:n}:e;return r?_((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(M(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):he}var bu={JSACTION:"jsaction"},Tu={JSACTION:"__jsaction",OWNER:"__owner"},ip={};function rI(e){return e[Tu.JSACTION]}function np(e,t){e[Tu.JSACTION]=t}function oI(e){return ip[e]}function iI(e,t){ip[e]=t}var T={CLICK:"click",CLICKMOD:"clickmod",DBLCLICK:"dblclick",FOCUS:"focus",FOCUSIN:"focusin",BLUR:"blur",FOCUSOUT:"focusout",SUBMIT:"submit",KEYDOWN:"keydown",KEYPRESS:"keypress",KEYUP:"keyup",MOUSEOVER:"mouseover",MOUSEOUT:"mouseout",MOUSEENTER:"mouseenter",MOUSELEAVE:"mouseleave",POINTEROVER:"pointerover",POINTEROUT:"pointerout",POINTERENTER:"pointerenter",POINTERLEAVE:"pointerleave",ERROR:"error",LOAD:"load",TOUCHSTART:"touchstart",TOUCHEND:"touchend",TOUCHMOVE:"touchmove",TOGGLE:"toggle"},sI=[T.MOUSEENTER,T.MOUSELEAVE,"pointerenter","pointerleave"],sV=[T.CLICK,T.DBLCLICK,T.FOCUSIN,T.FOCUSOUT,T.KEYDOWN,T.KEYUP,T.KEYPRESS,T.MOUSEOVER,T.MOUSEOUT,T.SUBMIT,T.TOUCHSTART,T.TOUCHEND,T.TOUCHMOVE,"touchcancel","auxclick","change","compositionstart","compositionupdate","compositionend","beforeinput","input","select","copy","cut","paste","mousedown","mouseup","wheel","contextmenu","dragover","dragenter","dragleave","drop","dragstart","dragend","pointerdown","pointermove","pointerup","pointercancel","pointerover","pointerout","gotpointercapture","lostpointercapture","ended","loadedmetadata","pagehide","pageshow","visibilitychange","beforematch"],aI=[T.FOCUS,T.BLUR,T.ERROR,T.LOAD,T.TOGGLE],Su=e=>aI.indexOf(e)>=0;function cI(e){return e===T.MOUSEENTER?T.MOUSEOVER:e===T.MOUSELEAVE?T.MOUSEOUT:e===T.POINTERENTER?T.POINTEROVER:e===T.POINTERLEAVE?T.POINTEROUT:e}function uI(e,t,n,r){let o=!1;Su(t)&&(o=!0);let i=typeof r=="boolean"?{capture:o,passive:r}:o;return e.addEventListener(t,n,i),{eventType:t,handler:n,capture:o,passive:r}}function lI(e,t){if(e.removeEventListener){let n=typeof t.passive=="boolean"?{capture:t.capture}:t.capture;e.removeEventListener(t.eventType,t.handler,n)}else e.detachEvent&&e.detachEvent(`on${t.eventType}`,t.handler)}function dI(e){e.preventDefault?e.preventDefault():e.returnValue=!1}var rp=typeof navigator<"u"&&/Macintosh/.test(navigator.userAgent);function fI(e){return e.which===2||e.which==null&&e.button===4}function hI(e){return rp&&e.metaKey||!rp&&e.ctrlKey||fI(e)||e.shiftKey}function pI(e,t,n){let r=e.relatedTarget;return(e.type===T.MOUSEOVER&&t===T.MOUSEENTER||e.type===T.MOUSEOUT&&t===T.MOUSELEAVE||e.type===T.POINTEROVER&&t===T.POINTERENTER||e.type===T.POINTEROUT&&t===T.POINTERLEAVE)&&(!r||r!==n&&!n.contains(r))}function gI(e,t){let n={};for(let r in e){if(r==="srcElement"||r==="target")continue;let o=r,i=e[o];typeof i!="function"&&(n[o]=i)}return e.type===T.MOUSEOVER?n.type=T.MOUSEENTER:e.type===T.MOUSEOUT?n.type=T.MOUSELEAVE:e.type===T.POINTEROVER?n.type=T.POINTERENTER:n.type=T.POINTERLEAVE,n.target=n.srcElement=t,n.bubbles=!1,n._originalEvent=e,n}var mI=typeof navigator<"u"&&/iPhone|iPad|iPod/.test(navigator.userAgent),cs=class{element;handlerInfos=[];constructor(t){this.element=t}addEventListener(t,n,r){mI&&(this.element.style.cursor="pointer"),this.handlerInfos.push(uI(this.element,t,n(this.element),r))}cleanUp(){for(let t=0;t<this.handlerInfos.length;t++)lI(this.element,this.handlerInfos[t]);this.handlerInfos=[]}},vI={EVENT_ACTION_SEPARATOR:":"};function on(e){return e.eventType}function Mu(e,t){e.eventType=t}function ss(e){return e.event}function sp(e,t){e.event=t}function ap(e){return e.targetElement}function cp(e,t){e.targetElement=t}function up(e){return e.eic}function yI(e,t){e.eic=t}function DI(e){return e.timeStamp}function EI(e,t){e.timeStamp=t}function as(e){return e.eia}function lp(e,t,n){e.eia=[t,n]}function Eu(e){e.eia=void 0}function is(e){return e[1]}function wI(e){return e.eirp}function dp(e,t){e.eirp=t}function fp(e){return e.eir}function hp(e,t){e.eir=t}function pp(e){return{eventType:e.eventType,event:e.event,targetElement:e.targetElement,eic:e.eic,eia:e.eia,timeStamp:e.timeStamp,eirp:e.eirp,eiack:e.eiack,eir:e.eir}}function II(e,t,n,r,o,i,s,a){return{eventType:e,event:t,targetElement:n,eic:r,timeStamp:o,eia:i,eirp:s,eiack:a}}var wu=class e{eventInfo;constructor(t){this.eventInfo=t}getEventType(){return on(this.eventInfo)}setEventType(t){Mu(this.eventInfo,t)}getEvent(){return ss(this.eventInfo)}setEvent(t){sp(this.eventInfo,t)}getTargetElement(){return ap(this.eventInfo)}setTargetElement(t){cp(this.eventInfo,t)}getContainer(){return up(this.eventInfo)}setContainer(t){yI(this.eventInfo,t)}getTimestamp(){return DI(this.eventInfo)}setTimestamp(t){EI(this.eventInfo,t)}getAction(){let t=as(this.eventInfo);if(t)return{name:t[0],element:t[1]}}setAction(t){if(!t){Eu(this.eventInfo);return}lp(this.eventInfo,t.name,t.element)}getIsReplay(){return wI(this.eventInfo)}setIsReplay(t){dp(this.eventInfo,t)}getResolved(){return fp(this.eventInfo)}setResolved(t){hp(this.eventInfo,t)}clone(){return new e(pp(this.eventInfo))}},CI={},bI=/\s*;\s*/,TI=T.CLICK,Iu=class{a11yClickSupport=!1;clickModSupport=!0;syntheticMouseEventSupport;updateEventInfoForA11yClick=void 0;preventDefaultForA11yClick=void 0;populateClickOnlyAction=void 0;constructor({syntheticMouseEventSupport:t=!1,clickModSupport:n=!0}={}){this.syntheticMouseEventSupport=t,this.clickModSupport=n}resolveEventType(t){this.clickModSupport&&on(t)===T.CLICK&&hI(ss(t))?Mu(t,T.CLICKMOD):this.a11yClickSupport&&this.updateEventInfoForA11yClick(t)}resolveAction(t){fp(t)||(this.populateAction(t,ap(t)),hp(t,!0))}resolveParentAction(t){let n=as(t),r=n&&is(n);Eu(t);let o=r&&this.getParentNode(r);o&&this.populateAction(t,o)}populateAction(t,n){let r=n;for(;r&&r!==up(t)&&(r.nodeType===Node.ELEMENT_NODE&&this.populateActionOnElement(r,t),!as(t));)r=this.getParentNode(r);let o=as(t);if(o&&(this.a11yClickSupport&&this.preventDefaultForA11yClick(t),this.syntheticMouseEventSupport&&(on(t)===T.MOUSEENTER||on(t)===T.MOUSELEAVE||on(t)===T.POINTERENTER||on(t)===T.POINTERLEAVE)))if(pI(ss(t),on(t),is(o))){let i=gI(ss(t),is(o));sp(t,i),cp(t,is(o))}else Eu(t)}getParentNode(t){let n=t[Tu.OWNER];if(n)return n;let r=t.parentNode;return r?.nodeName==="#document-fragment"?r?.host??null:r}populateActionOnElement(t,n){let r=this.parseActions(t),o=r[on(n)];o!==void 0&&lp(n,o,t),this.a11yClickSupport&&this.populateClickOnlyAction(t,n,r)}parseActions(t){let n=rI(t);if(!n){let r=t.getAttribute(bu.JSACTION);if(!r)n=CI,np(t,n);else{if(n=oI(r),!n){n={};let o=r.split(bI);for(let i=0;i<o.length;i++){let s=o[i];if(!s)continue;let a=s.indexOf(vI.EVENT_ACTION_SEPARATOR),c=a!==-1,u=c?s.substr(0,a).trim():TI,l=c?s.substr(a+1).trim():s;n[u]=l}iI(r,n)}np(t,n)}}return n}addA11yClickSupport(t,n,r){this.a11yClickSupport=!0,this.updateEventInfoForA11yClick=t,this.preventDefaultForA11yClick=n,this.populateClickOnlyAction=r}},gp=function(e){return e[e.I_AM_THE_JSACTION_FRAMEWORK=0]="I_AM_THE_JSACTION_FRAMEWORK",e}(gp||{}),Cu=class{dispatchDelegate;actionResolver;eventReplayer;eventReplayScheduled=!1;replayEventInfoWrappers=[];constructor(t,{actionResolver:n,eventReplayer:r}={}){this.dispatchDelegate=t,this.actionResolver=n,this.eventReplayer=r}dispatch(t){let n=new wu(t);this.actionResolver?.resolveEventType(t),this.actionResolver?.resolveAction(t);let r=n.getAction();if(r&&SI(r.element,n)&&dI(n.getEvent()),this.eventReplayer&&n.getIsReplay()){this.scheduleEventInfoWrapperReplay(n);return}this.dispatchDelegate(n)}scheduleEventInfoWrapperReplay(t){this.replayEventInfoWrappers.push(t),!this.eventReplayScheduled&&(this.eventReplayScheduled=!0,Promise.resolve().then(()=>{this.eventReplayScheduled=!1,this.eventReplayer(this.replayEventInfoWrappers)}))}};function SI(e,t){return e.tagName==="A"&&(t.getEventType()===T.CLICK||t.getEventType()===T.CLICKMOD)}var mp=Symbol.for("propagationStopped"),_u={REPLAY:101};var MI="`preventDefault` called during event replay.";var _I="`composedPath` called during event replay.",us=class{dispatchDelegate;clickModSupport;actionResolver;dispatcher;constructor(t,n=!0){this.dispatchDelegate=t,this.clickModSupport=n,this.actionResolver=new Iu({clickModSupport:n}),this.dispatcher=new Cu(r=>{this.dispatchToDelegate(r)},{actionResolver:this.actionResolver})}dispatch(t){this.dispatcher.dispatch(t)}dispatchToDelegate(t){for(t.getIsReplay()&&AI(t),NI(t);t.getAction();){if(xI(t),Su(t.getEventType())&&t.getAction().element!==t.getTargetElement()||(this.dispatchDelegate(t.getEvent(),t.getAction().name),RI(t)))return;this.actionResolver.resolveParentAction(t.eventInfo)}}};function NI(e){let t=e.getEvent(),n=e.getEvent().stopPropagation.bind(t),r=()=>{t[mp]=!0,n()};Sn(t,"stopPropagation",r),Sn(t,"stopImmediatePropagation",r)}function RI(e){return!!e.getEvent()[mp]}function AI(e){let t=e.getEvent(),n=e.getTargetElement(),r=t.preventDefault.bind(t);Sn(t,"target",n),Sn(t,"eventPhase",_u.REPLAY),Sn(t,"preventDefault",()=>{throw r(),new Error(MI+"")}),Sn(t,"composedPath",()=>{throw new Error(_I+"")})}function xI(e){let t=e.getEvent(),n=e.getAction()?.element;n&&Sn(t,"currentTarget",n,{configurable:!0})}function Sn(e,t,n,{configurable:r=!1}={}){Object.defineProperty(e,t,{value:n,configurable:r})}function vp(e,t){e.ecrd(n=>{t.dispatch(n)},gp.I_AM_THE_JSACTION_FRAMEWORK)}function OI(e){return e?.q??[]}function PI(e){e&&(op(e.c,e.et,e.h),op(e.c,e.etc,e.h,!0))}function op(e,t,n,r){for(let o=0;o<t.length;o++)e.removeEventListener(t[o],n,r)}var kI=!1,yp=(()=>{class e{static MOUSE_SPECIAL_SUPPORT=kI;containerManager;eventHandlers={};browserEventTypeToExtraEventTypes={};dispatcher=null;queuedEventInfos=[];constructor(n){this.containerManager=n}handleEvent(n,r,o){let i=II(n,r,r.target,o,Date.now());this.handleEventInfo(i)}handleEventInfo(n){if(!this.dispatcher){dp(n,!0),this.queuedEventInfos?.push(n);return}this.dispatcher(n)}addEvent(n,r,o){if(n in this.eventHandlers||!this.containerManager||!e.MOUSE_SPECIAL_SUPPORT&&sI.indexOf(n)>=0)return;let i=(a,c,u)=>{this.handleEvent(a,c,u)};this.eventHandlers[n]=i;let s=cI(r||n);if(s!==n){let a=this.browserEventTypeToExtraEventTypes[s]||[];a.push(n),this.browserEventTypeToExtraEventTypes[s]=a}this.containerManager.addEventListener(s,a=>c=>{i(n,c,a)},o)}replayEarlyEvents(n=window._ejsa){n&&(this.replayEarlyEventInfos(n.q),PI(n),delete window._ejsa)}replayEarlyEventInfos(n){for(let r=0;r<n.length;r++){let o=n[r],i=this.getEventTypesForBrowserEventType(o.eventType);for(let s=0;s<i.length;s++){let a=pp(o);Mu(a,i[s]),this.handleEventInfo(a)}}}getEventTypesForBrowserEventType(n){let r=[];return this.eventHandlers[n]&&r.push(n),this.browserEventTypeToExtraEventTypes[n]&&r.push(...this.browserEventTypeToExtraEventTypes[n]),r}handler(n){return this.eventHandlers[n]}cleanUp(){this.containerManager?.cleanUp(),this.containerManager=null,this.eventHandlers={},this.browserEventTypeToExtraEventTypes={},this.dispatcher=null,this.queuedEventInfos=[]}registerDispatcher(n,r){this.ecrd(n,r)}ecrd(n,r){if(this.dispatcher=n,this.queuedEventInfos?.length){for(let o=0;o<this.queuedEventInfos.length;o++)this.handleEventInfo(this.queuedEventInfos[o]);this.queuedEventInfos=null}}}return e})();function Dp(e,t=window){return OI(t._ejsas?.[e])}function Nu(e,t=window){t._ejsas&&(t._ejsas[e]=void 0)}var Sg="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",v=class extends Error{code;constructor(t,n){super(oa(t,n)),this.code=t}};function FI(e){return`NG0${Math.abs(e)}`}function oa(e,t){return`${FI(e)}${t?": "+t:""}`}var Mg=Symbol("InputSignalNode#UNSET"),LI=U(E({},Ni),{transformFn:void 0,applyValueToInputSignal(e,t){uo(e,t)}});function _g(e,t){let n=Object.create(LI);n.value=e,n.transformFn=t?.transform;function r(){if(so(n),n.value===Mg){let o=null;throw new v(-950,o)}return n.value}return r[Me]=n,r}function xo(e){return{toString:e}.toString()}var ls="__parameters__";function jI(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Ng(e,t,n){return xo(()=>{let r=jI(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(ls)?c[ls]:Object.defineProperty(c,ls,{value:[]})[ls];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var fe=globalThis;function Q(e){for(let t in e)if(e[t]===Q)return t;throw Error("Could not find renamed property on target object.")}function VI(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Re(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Re).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Yu(e,t){return e?t?`${e} ${t}`:e:t||""}var UI=Q({__forward_ref__:Q});function Rg(e){return e.__forward_ref__=Rg,e.toString=function(){return Re(this())},e}function ge(e){return Ag(e)?e():e}function Ag(e){return typeof e=="function"&&e.hasOwnProperty(UI)&&e.__forward_ref__===Rg}function w(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function wt(e){return{providers:e.providers||[],imports:e.imports||[]}}function ia(e){return Ep(e,Og)||Ep(e,Pg)}function xg(e){return ia(e)!==null}function Ep(e,t){return e.hasOwnProperty(t)?e[t]:null}function BI(e){let t=e&&(e[Og]||e[Pg]);return t||null}function wp(e){return e&&(e.hasOwnProperty(Ip)||e.hasOwnProperty($I))?e[Ip]:null}var Og=Q({\u0275prov:Q}),Ip=Q({\u0275inj:Q}),Pg=Q({ngInjectableDef:Q}),$I=Q({ngInjectorDef:Q}),y=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=w({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function kg(e){return e&&!!e.\u0275providers}var HI=Q({\u0275cmp:Q}),zI=Q({\u0275dir:Q}),qI=Q({\u0275pipe:Q}),GI=Q({\u0275mod:Q}),Ts=Q({\u0275fac:Q}),wo=Q({__NG_ELEMENT_ID__:Q}),Cp=Q({__NG_ENV_ID__:Q});function wr(e){return typeof e=="string"?e:e==null?"":String(e)}function WI(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():wr(e)}function Fg(e,t){throw new v(-200,e)}function rd(e,t){throw new v(-201,!1)}var P=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(P||{}),Qu;function Lg(){return Qu}function Ne(e){let t=Qu;return Qu=e,t}function jg(e,t,n){let r=ia(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&P.Optional)return null;if(t!==void 0)return t;rd(e,"Injector")}var ZI={},_n=ZI,Ku="__NG_DI_FLAG__",Ss=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?Ri:_n,r)}},Ms="ngTempTokenPath",YI="ngTokenPath",QI=/\n/gm,KI="\u0275",bp="__source";function XI(e,t=P.Default){if(lo()===void 0)throw new v(-203,!1);if(lo()===null)return jg(e,void 0,t);{let n=lo(),r;return n instanceof Ss?r=n.injector:r=n,r.get(e,t&P.Optional?null:void 0,t)}}function I(e,t=P.Default){return(Lg()||XI)(ge(e),t)}function g(e,t=P.Default){return I(e,sa(t))}function sa(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Xu(e){let t=[];for(let n=0;n<e.length;n++){let r=ge(e[n]);if(Array.isArray(r)){if(r.length===0)throw new v(900,!1);let o,i=P.Default;for(let s=0;s<r.length;s++){let a=r[s],c=JI(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(I(o,i))}else t.push(I(r))}return t}function Vg(e,t){return e[Ku]=t,e.prototype[Ku]=t,e}function JI(e){return e[Ku]}function eC(e,t,n,r){let o=e[Ms];throw t[bp]&&o.unshift(t[bp]),e.message=tC(`
`+e.message,o,n,r),e[YI]=o,e[Ms]=null,e}function tC(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==KI?e.slice(2):e;let o=Re(t);if(Array.isArray(t))o=t.map(Re).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Re(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(QI,`
  `)}`}var Ug=Vg(Ng("Optional"),8);var nC=Vg(Ng("SkipSelf"),4);function Rn(e,t){let n=e.hasOwnProperty(Ts);return n?e[Ts]:null}function rC(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function oC(e){return e.flat(Number.POSITIVE_INFINITY)}function od(e,t){e.forEach(n=>Array.isArray(n)?od(n,t):t(n))}function Bg(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function _s(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function iC(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function sC(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function aa(e,t,n){let r=Oo(e,t);return r>=0?e[r|1]=n:(r=~r,sC(e,r,t,n)),r}function Ru(e,t){let n=Oo(e,t);if(n>=0)return e[n|1]}function Oo(e,t){return aC(e,t,1)}function aC(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var gt={},Ce=[],an=new y(""),$g=new y("",-1),Hg=new y(""),Ns=class{get(t,n=_n){if(n===_n){let r=new Error(`NullInjectorError: No provider for ${Re(t)}!`);throw r.name="NullInjectorError",r}return n}};function zg(e,t){let n=e[GI]||null;if(!n&&t===!0)throw new Error(`Type ${Re(e)} does not have '\u0275mod' property.`);return n}function Ut(e){return e[HI]||null}function id(e){return e[zI]||null}function qg(e){return e[qI]||null}function Un(e){return{\u0275providers:e}}function cC(...e){return{\u0275providers:sd(!0,e),\u0275fromNgModule:!0}}function sd(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return od(t,s=>{let a=s;Ju(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Gg(o,i),n}function Gg(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];ad(o,i=>{t(i,r)})}}function Ju(e,t,n,r){if(e=ge(e),!e)return!1;let o=null,i=wp(e),s=!i&&Ut(e);if(!i&&!s){let c=e.ngModule;if(i=wp(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)Ju(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{od(i.imports,l=>{Ju(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Gg(u,t)}if(!a){let u=Rn(o)||(()=>new o);t({provide:o,useFactory:u,deps:Ce},o),t({provide:Hg,useValue:o,multi:!0},o),t({provide:an,useValue:()=>I(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;ad(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function ad(e,t){for(let n of e)kg(n)&&(n=n.\u0275providers),Array.isArray(n)?ad(n,t):t(n)}var uC=Q({provide:String,useValue:Q});function Wg(e){return e!==null&&typeof e=="object"&&uC in e}function lC(e){return!!(e&&e.useExisting)}function dC(e){return!!(e&&e.useFactory)}function Ir(e){return typeof e=="function"}function fC(e){return!!e.useClass}var ca=new y(""),ys={},Tp={},Au;function ua(){return Au===void 0&&(Au=new Ns),Au}var ve=class{},Co=class extends ve{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,tl(t,s=>this.processProvider(s)),this.records.set($g,fr(void 0,this)),o.has("environment")&&this.records.set(ve,fr(void 0,this));let i=this.records.get(ca);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Hg,Ce,P.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?Ri:_n,r)}destroy(){Do(this),this._destroyed=!0;let t=k(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),k(t)}}onDestroy(t){return Do(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Do(this);let n=Pt(this),r=Ne(void 0),o;try{return t()}finally{Pt(n),Ne(r)}}get(t,n=_n,r=P.Default){if(Do(this),t.hasOwnProperty(Cp))return t[Cp](this);r=sa(r);let o,i=Pt(this),s=Ne(void 0);try{if(!(r&P.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=vC(t)&&ia(t);u&&this.injectableDefInScope(u)?c=fr(el(t),ys):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&P.Self?ua():this.parent;return n=r&P.Optional&&n===_n?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Ms]=a[Ms]||[]).unshift(Re(t)),i)throw a;return eC(a,t,"R3InjectorError",this.source)}else throw a}finally{Ne(s),Pt(i)}}resolveInjectorInitializers(){let t=k(null),n=Pt(this),r=Ne(void 0),o;try{let i=this.get(an,Ce,P.Self);for(let s of i)s()}finally{Pt(n),Ne(r),k(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Re(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=ge(t);let n=Ir(t)?t:ge(t&&t.provide),r=pC(t);if(!Ir(t)&&t.multi===!0){let o=this.records.get(n);o||(o=fr(void 0,ys,!0),o.factory=()=>Xu(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=k(null);try{return n.value===Tp?Fg(Re(t)):n.value===ys&&(n.value=Tp,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&mC(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{k(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ge(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function el(e){let t=ia(e),n=t!==null?t.factory:Rn(e);if(n!==null)return n;if(e instanceof y)throw new v(204,!1);if(e instanceof Function)return hC(e);throw new v(204,!1)}function hC(e){if(e.length>0)throw new v(204,!1);let n=BI(e);return n!==null?()=>n.factory(e):()=>new e}function pC(e){if(Wg(e))return fr(void 0,e.useValue);{let t=Zg(e);return fr(t,ys)}}function Zg(e,t,n){let r;if(Ir(e)){let o=ge(e);return Rn(o)||el(o)}else if(Wg(e))r=()=>ge(e.useValue);else if(dC(e))r=()=>e.useFactory(...Xu(e.deps||[]));else if(lC(e))r=(o,i)=>I(ge(e.useExisting),i!==void 0&&i&P.Optional?P.Optional:void 0);else{let o=ge(e&&(e.useClass||e.provide));if(gC(e))r=()=>new o(...Xu(e.deps));else return Rn(o)||el(o)}return r}function Do(e){if(e.destroyed)throw new v(205,!1)}function fr(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function gC(e){return!!e.deps}function mC(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function vC(e){return typeof e=="function"||typeof e=="object"&&e instanceof y}function tl(e,t){for(let n of e)Array.isArray(n)?tl(n,t):n&&kg(n)?tl(n.\u0275providers,t):t(n)}function Ae(e,t){let n;e instanceof Co?(Do(e),n=e):n=new Ss(e);let r,o=Pt(n),i=Ne(void 0);try{return t()}finally{Pt(o),Ne(i)}}function cd(){return Lg()!==void 0||lo()!=null}function la(e){if(!cd())throw new v(-203,!1)}function yC(e){let t=fe.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function DC(e){return typeof e=="function"}var je=0,S=1,N=2,ue=3,Ke=4,xe=5,qe=6,Rs=7,ce=8,mt=9,Bt=10,G=11,bo=12,Sp=13,Nr=14,Te=15,An=16,hr=17,$t=18,da=19,Yg=20,sn=21,xu=22,xn=23,ze=24,vr=25,W=26,Qg=1,Ht=6,zt=7,As=8,Cr=9,me=10;function Xe(e){return Array.isArray(e)&&typeof e[Qg]=="object"}function It(e){return Array.isArray(e)&&e[Qg]===!0}function ud(e){return(e.flags&4)!==0}function Bn(e){return e.componentOffset>-1}function fa(e){return(e.flags&1)===1}function vt(e){return!!e.template}function To(e){return(e[N]&512)!==0}function $n(e){return(e[N]&256)===256}var nl=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Kg(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var Hn=(()=>{let e=()=>Xg;return e.ngInherit=!0,e})();function Xg(e){return e.type.prototype.ngOnChanges&&(e.setInput=wC),EC}function EC(){let e=em(this),t=e?.current;if(t){let n=e.previous;if(n===gt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function wC(e,t,n,r,o){let i=this.declaredInputs[r],s=em(e)||IC(e,{previous:gt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new nl(u&&u.currentValue,n,c===gt),Kg(e,t,o,n)}var Jg="__ngSimpleChanges__";function em(e){return e[Jg]||null}function IC(e,t){return e[Jg]=t}var Mp=null;var z=function(e,t=null,n){Mp?.(e,t,n)},tm="svg",CC="math";function Je(e){for(;Array.isArray(e);)e=e[je];return e}function nm(e,t){return Je(t[e])}function tt(e,t){return Je(t[e.index])}function Po(e,t){return e.data[t]}function ld(e,t){return e[t]}function rm(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function yt(e,t){let n=t[e];return Xe(n)?n:n[je]}function bC(e){return(e[N]&4)===4}function dd(e){return(e[N]&128)===128}function TC(e){return It(e[ue])}function cn(e,t){return t==null?null:e[t]}function om(e){e[hr]=0}function im(e){e[N]&1024||(e[N]|=1024,dd(e)&&Rr(e))}function SC(e,t){for(;e>0;)t=t[Nr],e--;return t}function ha(e){return!!(e[N]&9216||e[ze]?.dirty)}function rl(e){e[Bt].changeDetectionScheduler?.notify(8),e[N]&64&&(e[N]|=1024),ha(e)&&Rr(e)}function Rr(e){e[Bt].changeDetectionScheduler?.notify(0);let t=On(e);for(;t!==null&&!(t[N]&8192||(t[N]|=8192,!dd(t)));)t=On(t)}function sm(e,t){if($n(e))throw new v(911,!1);e[sn]===null&&(e[sn]=[]),e[sn].push(t)}function MC(e,t){if(e[sn]===null)return;let n=e[sn].indexOf(t);n!==-1&&e[sn].splice(n,1)}function On(e){let t=e[ue];return It(t)?t[ue]:t}function fd(e){return e[Rs]??=[]}function hd(e){return e.cleanup??=[]}function _C(e,t,n,r){let o=fd(t);o.push(n),e.firstCreatePass&&hd(e).push(r,o.length-1)}var x={lFrame:dm(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var ol=!1;function NC(){return x.lFrame.elementDepthCount}function RC(){x.lFrame.elementDepthCount++}function AC(){x.lFrame.elementDepthCount--}function pd(){return x.bindingsEnabled}function Ar(){return x.skipHydrationRootTNode!==null}function xC(e){return x.skipHydrationRootTNode===e}function OC(e){x.skipHydrationRootTNode=e}function PC(){x.skipHydrationRootTNode=null}function R(){return x.lFrame.lView}function X(){return x.lFrame.tView}function qV(e){return x.lFrame.contextLView=e,e[ce]}function GV(e){return x.lFrame.contextLView=null,e}function Ee(){let e=am();for(;e!==null&&e.type===64;)e=e.parent;return e}function am(){return x.lFrame.currentTNode}function kC(){let e=x.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function ln(e,t){let n=x.lFrame;n.currentTNode=e,n.isParent=t}function gd(){return x.lFrame.isParent}function md(){x.lFrame.isParent=!1}function FC(){return x.lFrame.contextLView}function cm(){return ol}function xs(e){let t=ol;return ol=e,t}function vd(){let e=x.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function LC(){return x.lFrame.bindingIndex}function jC(e){return x.lFrame.bindingIndex=e}function zn(){return x.lFrame.bindingIndex++}function yd(e){let t=x.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function VC(){return x.lFrame.inI18n}function UC(e,t){let n=x.lFrame;n.bindingIndex=n.bindingRootIndex=e,il(t)}function BC(){return x.lFrame.currentDirectiveIndex}function il(e){x.lFrame.currentDirectiveIndex=e}function $C(e){let t=x.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Dd(){return x.lFrame.currentQueryIndex}function pa(e){x.lFrame.currentQueryIndex=e}function HC(e){let t=e[S];return t.type===2?t.declTNode:t.type===1?e[xe]:null}function um(e,t,n){if(n&P.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&P.Host);)if(o=HC(i),o===null||(i=i[Nr],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=x.lFrame=lm();return r.currentTNode=t,r.lView=e,!0}function Ed(e){let t=lm(),n=e[S];x.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function lm(){let e=x.lFrame,t=e===null?null:e.child;return t===null?dm(e):t}function dm(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function fm(){let e=x.lFrame;return x.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var hm=fm;function wd(){let e=fm();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function zC(e){return(x.lFrame.contextLView=SC(e,x.lFrame.contextLView))[ce]}function Wt(){return x.lFrame.selectedIndex}function Pn(e){x.lFrame.selectedIndex=e}function ga(){let e=x.lFrame;return Po(e.tView,e.selectedIndex)}function WV(){x.lFrame.currentNamespace=tm}function ZV(){qC()}function qC(){x.lFrame.currentNamespace=null}function pm(){return x.lFrame.currentNamespace}var gm=!0;function ma(){return gm}function dn(e){gm=e}function GC(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Xg(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function Id(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Ds(e,t,n){mm(e,t,3,n)}function Es(e,t,n,r){(e[N]&3)===n&&mm(e,t,n,r)}function Ou(e,t){let n=e[N];(n&3)===t&&(n&=16383,n+=1,e[N]=n)}function mm(e,t,n,r){let o=r!==void 0?e[hr]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[hr]+=65536),(a<i||i==-1)&&(WC(e,n,t,c),e[hr]=(e[hr]&**********)+c+2),c++}function _p(e,t){z(4,e,t);let n=k(null);try{t.call(e)}finally{k(n),z(5,e,t)}}function WC(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[N]>>14<e[hr]>>16&&(e[N]&3)===t&&(e[N]+=16384,_p(a,i)):_p(a,i)}var yr=-1,kn=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function ZC(e){return(e.flags&8)!==0}function YC(e){return(e.flags&16)!==0}function QC(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];KC(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function vm(e){return e===3||e===4||e===6}function KC(e){return e.charCodeAt(0)===64}function br(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Np(e,n,o,null,t[++r]):Np(e,n,o,null,null))}}return e}function Np(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function ym(e){return e!==yr}function Os(e){return e&32767}function XC(e){return e>>16}function Ps(e,t){let n=XC(e),r=t;for(;n>0;)r=r[Nr],n--;return r}var sl=!0;function ks(e){let t=sl;return sl=e,t}var JC=256,Dm=JC-1,Em=5,eb=0,pt={};function tb(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(wo)&&(r=n[wo]),r==null&&(r=n[wo]=eb++);let o=r&Dm,i=1<<o;t.data[e+(o>>Em)]|=i}function Fs(e,t){let n=wm(e,t);if(n!==-1)return n;let r=t[S];r.firstCreatePass&&(e.injectorIndex=t.length,Pu(r.data,e),Pu(t,null),Pu(r.blueprint,null));let o=Cd(e,t),i=e.injectorIndex;if(ym(o)){let s=Os(o),a=Ps(o,t),c=a[S].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function Pu(e,t){e.push(0,0,0,0,0,0,0,0,t)}function wm(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Cd(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Sm(o),r===null)return yr;if(n++,o=o[Nr],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return yr}function al(e,t,n){tb(e,t,n)}function nb(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(vm(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Im(e,t,n){if(n&P.Optional||e!==void 0)return e;rd(t,"NodeInjector")}function Cm(e,t,n,r){if(n&P.Optional&&r===void 0&&(r=null),(n&(P.Self|P.Host))===0){let o=e[mt],i=Ne(void 0);try{return o?o.get(t,r,n&P.Optional):jg(t,r,n&P.Optional)}finally{Ne(i)}}return Im(r,t,n)}function bm(e,t,n,r=P.Default,o){if(e!==null){if(t[N]&2048&&!(r&P.Self)){let s=sb(e,t,n,r,pt);if(s!==pt)return s}let i=Tm(e,t,n,r,pt);if(i!==pt)return i}return Cm(t,n,r,o)}function Tm(e,t,n,r,o){let i=ob(n);if(typeof i=="function"){if(!um(t,e,r))return r&P.Host?Im(o,n,r):Cm(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&P.Optional))rd(n);else return s}finally{hm()}}else if(typeof i=="number"){let s=null,a=wm(e,t),c=yr,u=r&P.Host?t[Te][xe]:null;for((a===-1||r&P.SkipSelf)&&(c=a===-1?Cd(e,t):t[a+8],c===yr||!Ap(r,!1)?a=-1:(s=t[S],a=Os(c),t=Ps(c,t)));a!==-1;){let l=t[S];if(Rp(i,a,l.data)){let d=rb(a,t,n,s,r,u);if(d!==pt)return d}c=t[a+8],c!==yr&&Ap(r,t[S].data[a+8]===u)&&Rp(i,a,t)?(s=l,a=Os(c),t=Ps(c,t)):a=-1}}return o}function rb(e,t,n,r,o,i){let s=t[S],a=s.data[e+8],c=r==null?Bn(a)&&sl:r!=s&&(a.type&3)!==0,u=o&P.Host&&i===a,l=ws(a,s,n,c,u);return l!==null?So(t,s,l,a,o):pt}function ws(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let p=s[f];if(f<c&&n===p||f>=c&&p.type===n)return f}if(o){let f=s[c];if(f&&vt(f)&&f.type===n)return c}return null}function So(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof kn){let a=i;a.resolving&&Fg(WI(s[n]));let c=ks(a.canSeeViewProviders);a.resolving=!0;let u,l=a.injectImpl?Ne(a.injectImpl):null,d=um(e,r,P.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&GC(n,s[n],t)}finally{l!==null&&Ne(l),ks(c),a.resolving=!1,hm()}}return i}function ob(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(wo)?e[wo]:void 0;return typeof t=="number"?t>=0?t&Dm:ib:t}function Rp(e,t,n){let r=1<<e;return!!(n[t+(e>>Em)]&r)}function Ap(e,t){return!(e&P.Self)&&!(e&P.Host&&t)}var Nn=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return bm(this._tNode,this._lView,t,sa(r),n)}};function ib(){return new Nn(Ee(),R())}function bd(e){return xo(()=>{let t=e.prototype.constructor,n=t[Ts]||cl(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Ts]||cl(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function cl(e){return Ag(e)?()=>{let t=cl(ge(e));return t&&t()}:Rn(e)}function sb(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[N]&2048&&!To(s);){let a=Tm(i,s,n,r|P.Self,pt);if(a!==pt)return a;let c=i.parent;if(!c){let u=s[Yg];if(u){let l=u.get(n,pt,r);if(l!==pt)return l}c=Sm(s),s=s[Nr]}i=c}return o}function Sm(e){let t=e[S],n=t.type;return n===2?t.declTNode:n===1?e[xe]:null}function va(e){return nb(Ee(),e)}function xp(e,t=null,n=null,r){let o=Mm(e,t,n,r);return o.resolveInjectorInitializers(),o}function Mm(e,t=null,n=null,r,o=new Set){let i=[n||Ce,cC(e)];return r=r||(typeof e=="object"?void 0:Re(e)),new Co(i,t||ua(),r||null,o)}var ye=class e{static THROW_IF_NOT_FOUND=_n;static NULL=new Ns;static create(t,n){if(Array.isArray(t))return xp({name:""},n,t,"");{let r=t.name??"";return xp({name:r},t.parent,t.providers,r)}}static \u0275prov=w({token:e,providedIn:"any",factory:()=>I($g)});static __NG_ELEMENT_ID__=-1};var Op=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>va(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},ab=new y("");ab.__NG_ELEMENT_ID__=e=>{let t=Ee();if(t===null)throw new v(204,!1);if(t.type&2)return t.value;if(e&P.Optional)return null;throw new v(204,!1)};var _m=!1,Zt=(()=>{class e{static __NG_ELEMENT_ID__=cb;static __NG_ENV_ID__=n=>n}return e})(),Ls=class extends Zt{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return $n(n)?(t(),()=>{}):(sm(n,t),()=>MC(n,t))}};function cb(){return new Ls(R())}var un=class{},Td=new y("",{providedIn:"root",factory:()=>!1});var Nm=new y(""),Rm=new y(""),nt=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new pe(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),ub=(()=>{class e{internalPendingTasks=g(nt);scheduler=g(un);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){return ut(this,null,function*(){let r=this.add();try{return yield n()}finally{r()}})}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),ul=class extends J{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,cd()&&(this.destroyRef=g(Zt,{optional:!0})??void 0,this.pendingTasks=g(nt,{optional:!0})??void 0)}emit(t){let n=k(null);try{super.next(t)}finally{k(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof te&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},be=ul;function Mo(...e){}function Am(e){let t,n;function r(){e=Mo;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Pp(e){return queueMicrotask(()=>e()),()=>{e=Mo}}var Sd="isAngularZone",js=Sd+"_ID",lb=0,q=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new be(!1);onMicrotaskEmpty=new be(!1);onStable=new be(!1);onError=new be(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=_m}=t;if(typeof Zone>"u")throw new v(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,hb(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Sd)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new v(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new v(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,db,Mo,Mo);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},db={};function Md(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function fb(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Am(()=>{e.callbackScheduled=!1,ll(e),e.isCheckStableRunning=!0,Md(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),ll(e)}function hb(e){let t=()=>{fb(e)},n=lb++;e._inner=e._inner.fork({name:"angular",properties:{[Sd]:!0,[js]:n,[js+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(pb(c))return r.invokeTask(i,s,a,c);try{return kp(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Fp(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return kp(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!gb(c)&&t(),Fp(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,ll(e),Md(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function ll(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function kp(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Fp(e){e._nesting--,Md(e)}var Vs=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new be;onMicrotaskEmpty=new be;onStable=new be;onError=new be;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function pb(e){return xm(e,"__ignore_ng_zone__")}function gb(e){return xm(e,"__scheduler_tick__")}function xm(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function mb(e="zone.js",t){return e==="noop"?new Vs:e==="zone.js"?new q(t):e}var Dt=class{_console=console;handleError(t){this._console.error("ERROR",t)}},vb=new y("",{providedIn:"root",factory:()=>{let e=g(q),t=g(Dt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Lp(e,t){return _g(e,t)}function yb(e){return _g(Mg,e)}var Om=(Lp.required=yb,Lp);function Db(){return xr(Ee(),R())}function xr(e,t){return new Ct(tt(e,t))}var Ct=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Db}return e})();function Pm(e){return e instanceof Ct?e.nativeElement:e}function Eb(e){return typeof e=="function"&&e[Me]!==void 0}function km(e,t){let n=Jc(e,t?.equal),r=n[Me];return n.set=o=>uo(r,o),n.update=o=>eu(r,o),n.asReadonly=wb.bind(n),n}function wb(){let e=this[Me];if(e.readonlyFn===void 0){let t=()=>this();t[Me]=e,e.readonlyFn=t}return e.readonlyFn}function Fm(e){return Eb(e)&&typeof e.set=="function"}function Ib(){return this._results[Symbol.iterator]()}var dl=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new J}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=oC(t);(this._changesDetected=!rC(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Ib},Cb="ngSkipHydration",bb="ngskiphydration";function Lm(e){let t=e.mergedAttrs;if(t===null)return!1;for(let n=0;n<t.length;n+=2){let r=t[n];if(typeof r=="number")return!1;if(typeof r=="string"&&r.toLowerCase()===bb)return!0}return!1}function jm(e){return e.hasAttribute(Cb)}function Us(e){return(e.flags&128)===128}function Tb(e){if(Us(e))return!0;let t=e.parent;for(;t;){if(Us(e)||Lm(t))return!0;t=t.parent}return!1}var Vm=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Vm||{}),Um=new Map,Sb=0;function Mb(){return Sb++}function _b(e){Um.set(e[da],e)}function fl(e){Um.delete(e[da])}var jp="__ngContext__";function Or(e,t){Xe(t)?(e[jp]=t[da],_b(t)):e[jp]=t}function Bm(e){return Hm(e[bo])}function $m(e){return Hm(e[Ke])}function Hm(e){for(;e!==null&&!It(e);)e=e[Ke];return e}var hl;function zm(e){hl=e}function ya(){if(hl!==void 0)return hl;if(typeof document<"u")return document;throw new v(210,!1)}var Vt=new y("",{providedIn:"root",factory:()=>Nb}),Nb="ng",_d=new y(""),Pr=new y("",{providedIn:"platform",factory:()=>"unknown"});var YV=new y(""),Nd=new y("",{providedIn:"root",factory:()=>ya().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});function Rb(){let e=new kr;return e.store=Ab(ya(),g(Vt)),e}var kr=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:Rb});store={};onSerializeCallbacks={};get(n,r){return this.store[n]!==void 0?this.store[n]:r}set(n,r){this.store[n]=r}remove(n){delete this.store[n]}hasKey(n){return this.store.hasOwnProperty(n)}get isEmpty(){return Object.keys(this.store).length===0}onSerialize(n,r){this.onSerializeCallbacks[n]=r}toJson(){for(let n in this.onSerializeCallbacks)if(this.onSerializeCallbacks.hasOwnProperty(n))try{this.store[n]=this.onSerializeCallbacks[n]()}catch(r){console.warn("Exception in onSerialize callback: ",r)}return JSON.stringify(this.store).replace(/</g,"\\u003C")}}return e})();function Ab(e,t){let n=e.getElementById(t+"-state");if(n?.textContent)try{return JSON.parse(n.textContent)}catch(r){console.warn("Exception while restoring TransferState for app "+t,r)}return{}}var qm="h",Gm="b",xb="f",Ob="n",Pb="e",kb="t",Rd="c",Wm="x",Bs="r",Fb="i",Lb="n",Zm="d";var jb="di",Vb="s",Ub="p";var ds=new y(""),Ym=!1,Qm=new y("",{providedIn:"root",factory:()=>Ym});var Km=new y(""),Bb=!1,$b=new y(""),Vp=new y("",{providedIn:"root",factory:()=>new Map}),Ad=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Ad||{}),Fr=new y(""),Up=new Set;function Ge(e){Up.has(e)||(Up.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var xd=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Hb}return e})();function Hb(){return new xd(R(),Ee())}var pr=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(pr||{}),Xm=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),zb=[pr.EarlyRead,pr.Write,pr.MixedReadWrite,pr.Read],qb=(()=>{class e{ngZone=g(q);scheduler=g(un);errorHandler=g(Dt,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){g(Fr,{optional:!0})}execute(){let n=this.sequences.size>0;n&&z(16),this.executing=!0;for(let r of zb)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&z(17)}register(n){let{view:r}=n;r!==void 0?((r[vr]??=[]).push(n),Rr(r),r[N]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Ad.AFTER_NEXT_RENDER,n):n()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),pl=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[vr];t&&(this.view[vr]=t.filter(n=>n!==this))}};function Gb(e,t){!t?.injector&&la(Gb);let n=t?.injector??g(ye);return Ge("NgAfterRender"),Jm(e,n,t,!1)}function Da(e,t){!t?.injector&&la(Da);let n=t?.injector??g(ye);return Ge("NgAfterNextRender"),Jm(e,n,t,!0)}function Wb(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Jm(e,t,n,r){let o=t.get(Xm);o.impl??=t.get(qb);let i=t.get(Fr,null,{optional:!0}),s=n?.phase??pr.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Zt):null,c=t.get(xd,null,{optional:!0}),u=new pl(o.impl,Wb(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(u),u}var Le=function(e){return e[e.NOT_STARTED=0]="NOT_STARTED",e[e.IN_PROGRESS=1]="IN_PROGRESS",e[e.COMPLETE=2]="COMPLETE",e[e.FAILED=3]="FAILED",e}(Le||{}),Bp=0,Zb=1,ae=function(e){return e[e.Placeholder=0]="Placeholder",e[e.Loading=1]="Loading",e[e.Complete=2]="Complete",e[e.Error=3]="Error",e}(ae||{});var Yb=0,Ea=1;var Qb=4,Kb=5;var Xb=7,Dr=8,Jb=9,ev=function(e){return e[e.Manual=0]="Manual",e[e.Playthrough=1]="Playthrough",e}(ev||{});function Is(e,t){let n=tT(e),r=t[n];if(r!==null){for(let o of r)o();t[n]=null}}function eT(e){Is(1,e),Is(0,e),Is(2,e)}function tT(e){let t=Qb;return e===1?t=Kb:e===2&&(t=Jb),t}function tv(e){return e+1}function ko(e,t){let n=e[S],r=tv(t.index);return e[r]}function wa(e,t){let n=tv(t.index);return e.data[n]}function nT(e,t,n){let r=t[S],o=wa(r,n);switch(e){case ae.Complete:return o.primaryTmplIndex;case ae.Loading:return o.loadingTmplIndex;case ae.Error:return o.errorTmplIndex;case ae.Placeholder:return o.placeholderTmplIndex;default:return null}}function $p(e,t){return t===ae.Placeholder?e.placeholderBlockConfig?.[Bp]??null:t===ae.Loading?e.loadingBlockConfig?.[Bp]??null:null}function rT(e){return e.loadingBlockConfig?.[Zb]??null}function Hp(e,t){if(!e||e.length===0)return t;let n=new Set(e);for(let r of t)n.add(r);return e.length===n.size?e:Array.from(n)}function oT(e,t){let n=t.primaryTmplIndex+W;return Po(e,n)}var Ia="ngb";var iT=(e,t,n)=>{let r=e,o=r.__jsaction_fns??new Map,i=o.get(t)??[];i.push(n),o.set(t,i),r.__jsaction_fns=o},sT=(e,t)=>{let n=e,r=n.getAttribute(Ia)??"",o=t.get(r)??new Set;o.has(n)||o.add(n),t.set(r,o)};var aT=e=>{e.removeAttribute(bu.JSACTION),e.removeAttribute(Ia),e.__jsaction_fns=void 0},cT=new y("",{providedIn:"root",factory:()=>({})});function nv(e,t){let n=t?.__jsaction_fns?.get(e.type);if(!(!n||!t?.isConnected))for(let r of n)r(e)}var gl=new Map;function uT(e,t){return gl.set(e,t),()=>gl.delete(e)}var zp=!1,rv=(e,t,n,r)=>{};function lT(e,t,n,r){rv(e,t,n,r)}function dT(){zp||(rv=(e,t,n,r)=>{let o=e[mt].get(Vt);gl.get(o)?.(t,n,r)},zp=!0)}var Od=new y("");var fT="__nghData__",ov=fT,hT="__nghDeferData__",pT=hT,ku="ngh",gT="nghm",iv=()=>null;function mT(e,t,n=!1){let r=e.getAttribute(ku);if(r==null)return null;let[o,i]=r.split("|");if(r=n?i:o,!r)return null;let s=i?`|${i}`:"",a=n?o:s,c={};if(r!==""){let l=t.get(kr,null,{optional:!0});l!==null&&(c=l.get(ov,[])[Number(r)])}let u={data:c,firstChild:e.firstChild??null};return n&&(u.firstChild=e,Ca(u,0,e.nextSibling)),a?e.setAttribute(ku,a):e.removeAttribute(ku),u}function vT(){iv=mT}function sv(e,t,n=!1){return iv(e,t,n)}function yT(e){let t=e._lView;return t[S].type===2?null:(To(t)&&(t=t[W]),t)}function DT(e){return e.textContent?.replace(/\s/gm,"")}function ET(e){let t=ya(),n=t.createNodeIterator(e,NodeFilter.SHOW_COMMENT,{acceptNode(i){let s=DT(i);return s==="ngetn"||s==="ngtns"?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}}),r,o=[];for(;r=n.nextNode();)o.push(r);for(let i of o)i.textContent==="ngetn"?i.replaceWith(t.createTextNode("")):i.remove()}function Ca(e,t,n){e.segmentHeads??={},e.segmentHeads[t]=n}function ml(e,t){return e.segmentHeads?.[t]??null}function wT(e){return e.get($b,!1,{optional:!0})}function IT(e,t){let n=e.data,r=n[Pb]?.[t]??null;return r===null&&n[Rd]?.[t]&&(r=Pd(e,t)),r}function av(e,t){return e.data[Rd]?.[t]??null}function Pd(e,t){let n=av(e,t)??[],r=0;for(let o of n)r+=o[Bs]*(o[Wm]??1);return r}function CT(e){if(typeof e.disconnectedNodes>"u"){let t=e.data[Zm];e.disconnectedNodes=t?new Set(t):null}return e.disconnectedNodes}function Fo(e,t){if(typeof e.disconnectedNodes>"u"){let n=e.data[Zm];e.disconnectedNodes=n?new Set(n):null}return!!CT(e)?.has(t)}function bT(e,t){let n=t.get(Od),o=t.get(kr).get(pT,{}),i=!1,s=e,a=null,c=[];for(;!i&&s;){i=n.has(s);let u=n.hydrating.get(s);if(a===null&&u!=null){a=u.promise;break}c.unshift(s),s=o[s][Ub]}return{parentBlockPromise:a,hydrationQueue:c}}function Fu(e){return!!e&&e.nodeType===Node.COMMENT_NODE&&e.textContent?.trim()===gT}function qp(e){for(;e&&e.nodeType===Node.TEXT_NODE;)e=e.previousSibling;return e}function TT(e){for(let r of e.body.childNodes)if(Fu(r))return;let t=qp(e.body.previousSibling);if(Fu(t))return;let n=qp(e.head.lastChild);if(!Fu(n))throw new v(-507,!1)}function cv(e,t){let n=e.contentQueries;if(n!==null){let r=k(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];pa(i),a.contentQueries(2,t[s],s)}}}finally{k(r)}}}function vl(e,t,n){pa(0);let r=k(null);try{t(e,n)}finally{k(r)}}function kd(e,t,n){if(ud(t)){let r=k(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{k(r)}}}var Et=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Et||{}),fs;function ST(){if(fs===void 0&&(fs=null,fe.trustedTypes))try{fs=fe.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return fs}function ba(e){return ST()?.createHTML(e)||e}var hs;function MT(){if(hs===void 0&&(hs=null,fe.trustedTypes))try{hs=fe.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return hs}function Gp(e){return MT()?.createScriptURL(e)||e}var qt=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Sg})`}},yl=class extends qt{getTypeName(){return"HTML"}},Dl=class extends qt{getTypeName(){return"Style"}},El=class extends qt{getTypeName(){return"Script"}},wl=class extends qt{getTypeName(){return"URL"}},Il=class extends qt{getTypeName(){return"ResourceURL"}};function rt(e){return e instanceof qt?e.changingThisBreaksApplicationSecurity:e}function fn(e,t){let n=_T(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Sg})`)}return n===t}function _T(e){return e instanceof qt&&e.getTypeName()||null}function uv(e){return new yl(e)}function lv(e){return new Dl(e)}function dv(e){return new El(e)}function fv(e){return new wl(e)}function hv(e){return new Il(e)}function NT(e){let t=new bl(e);return RT()?new Cl(t):t}var Cl=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(ba(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},bl=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=ba(t),n}};function RT(){try{return!!new window.DOMParser().parseFromString(ba(""),"text/html")}catch{return!1}}var AT=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Ta(e){return e=String(e),e.match(AT)?e:"unsafe:"+e}function Yt(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Lo(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var pv=Yt("area,br,col,hr,img,wbr"),gv=Yt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),mv=Yt("rp,rt"),xT=Lo(mv,gv),OT=Lo(gv,Yt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),PT=Lo(mv,Yt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Wp=Lo(pv,OT,PT,xT),vv=Yt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),kT=Yt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),FT=Yt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),LT=Lo(vv,kT,FT),jT=Yt("script,style,template"),Tl=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=BT(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=UT(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Zp(t).toLowerCase();if(!Wp.hasOwnProperty(n))return this.sanitizedSomething=!0,!jT.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!LT.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;vv[a]&&(c=Ta(c)),this.buf.push(" ",s,'="',Yp(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=Zp(t).toLowerCase();Wp.hasOwnProperty(n)&&!pv.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Yp(t))}};function VT(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function UT(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw yv(t);return t}function BT(e){let t=e.firstChild;if(t&&VT(e,t))throw yv(t);return t}function Zp(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function yv(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var $T=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,HT=/([^\#-~ |!])/g;function Yp(e){return e.replace(/&/g,"&amp;").replace($T,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(HT,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var ps;function Dv(e,t){let n=null;try{ps=ps||NT(e);let r=t?String(t):"";n=ps.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=ps.getInertBodyElement(r)}while(r!==i);let a=new Tl().sanitizeChildren(Qp(n)||n);return ba(a)}finally{if(n){let r=Qp(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function Qp(e){return"content"in e&&zT(e)?e.content:null}function zT(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var bt=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(bt||{});function qT(e){let t=wv();return t?t.sanitize(bt.URL,e)||"":fn(e,"URL")?rt(e):Ta(wr(e))}function GT(e){let t=wv();if(t)return Gp(t.sanitize(bt.RESOURCE_URL,e)||"");if(fn(e,"ResourceURL"))return Gp(rt(e));throw new v(904,!1)}function WT(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?GT:qT}function Ev(e,t,n){return WT(t,n)(e)}function wv(){let e=R();return e&&e[Bt].sanitizer}var ZT=/^>|^->|<!--|-->|--!>|<!-$/g,YT=/(<|>)/g,QT="\u200B$1\u200B";function KT(e){return e.replace(ZT,t=>t.replace(YT,QT))}function QV(e){return e.ownerDocument.defaultView}function KV(e){return e.ownerDocument}function XT(e){return e.ownerDocument.body}function Iv(e){return e instanceof Function?e():e}function JT(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Cv="ng-template";function eS(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&JT(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Fd(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Fd(e){return e.type===4&&e.value!==Cv}function tS(e,t,n){let r=e.type===4&&!n?Cv:e.value;return t===r}function nS(e,t,n){let r=4,o=e.attrs,i=o!==null?iS(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Ye(r)&&!Ye(c))return!1;if(s&&Ye(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!tS(e,c,n)||c===""&&t.length===1){if(Ye(r))return!1;s=!0}}else if(r&8){if(o===null||!eS(e,o,c,n)){if(Ye(r))return!1;s=!0}}else{let u=t[++a],l=rS(c,o,Fd(e),n);if(l===-1){if(Ye(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(Ye(r))return!1;s=!0}}}}return Ye(r)||s}function Ye(e){return(e&1)===0}function rS(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return sS(t,e)}function bv(e,t,n=!1){for(let r=0;r<t.length;r++)if(nS(e,t[r],n))return!0;return!1}function oS(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function iS(e){for(let t=0;t<e.length;t++){let n=e[t];if(vm(n))return t}return e.length}function sS(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function aS(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Kp(e,t){return e?":not("+t.trim()+")":t}function cS(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ye(s)&&(t+=Kp(i,o),o=""),r=s,i=i||!Ye(r);n++}return o!==""&&(t+=Kp(i,o)),t}function uS(e){return e.map(cS).join(",")}function lS(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Ye(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Ve={};function Tv(e,t){return e.createText(t)}function dS(e,t,n){e.setValue(t,n)}function Sv(e,t){return e.createComment(KT(t))}function Ld(e,t,n){return e.createElement(t,n)}function $s(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Mv(e,t,n){e.appendChild(t,n)}function Xp(e,t,n,r,o){r!==null?$s(e,t,n,r,o):Mv(e,t,n)}function jd(e,t,n){e.removeChild(null,t,n)}function _v(e){e.textContent=""}function fS(e,t,n){e.setAttribute(t,"style",n)}function hS(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Nv(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&QC(e,t,r),o!==null&&hS(e,t,o),i!==null&&fS(e,t,i)}function Vd(e,t,n,r,o,i,s,a,c,u,l){let d=W+r,h=d+o,f=pS(d,h),p=typeof u=="function"?u():u;return f[S]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:p,incompleteFirstPass:!1,ssrId:l}}function pS(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Ve);return n}function gS(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Vd(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Ud(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[je]=o,d[N]=r|4|128|8|64|1024,(u!==null||e&&e[N]&2048)&&(d[N]|=2048),om(d),d[ue]=d[Nr]=e,d[ce]=n,d[Bt]=s||e&&e[Bt],d[G]=a||e&&e[G],d[mt]=c||e&&e[mt]||null,d[xe]=i,d[da]=Mb(),d[qe]=l,d[Yg]=u,d[Te]=t.type==2?e[Te]:d,d}function mS(e,t,n){let r=tt(t,e),o=gS(n),i=e[Bt].rendererFactory,s=Bd(e,Ud(e,o,null,Rv(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Rv(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Av(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Bd(e,t){return e[bo]?e[Sp][Ke]=t:e[bo]=t,e[Sp]=t,t}function XV(e=1){xv(X(),R(),Wt()+e,!1)}function xv(e,t,n,r){if(!r)if((t[N]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Ds(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Es(t,i,0,n)}Pn(n)}var Sa=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Sa||{});function Sl(e,t,n,r){let o=k(null);try{let[i,s,a]=e.inputs[n],c=null;(s&Sa.SignalBased)!==0&&(c=t[i][Me]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Kg(t,c,i,r)}finally{k(o)}}function Ov(e,t,n,r,o){let i=Wt(),s=r&2;try{Pn(-1),s&&t.length>W&&xv(e,t,W,!1),z(s?2:0,o),n(r,o)}finally{Pn(i),z(s?3:1,o)}}function Ma(e,t,n){CS(e,t,n),(n.flags&64)===64&&bS(e,t,n)}function $d(e,t,n=tt){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function vS(e,t,n,r){let i=r.get(Qm,Ym)||n===Et.ShadowDom,s=e.selectRootElement(t,i);return yS(s),s}function yS(e){Pv(e)}var Pv=()=>null;function DS(e){jm(e)?_v(e):ET(e)}function ES(){Pv=DS}function wS(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Hd(e,t,n,r,o,i,s,a){if(!a&&Gd(t,e,n,r,o)){Bn(t)&&IS(n,t.index);return}if(t.type&3){let c=tt(t,n);r=wS(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function IS(e,t){let n=yt(t,e);n[N]&16||(n[N]|=64)}function CS(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Bn(n)&&mS(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Fs(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=So(t,e,s,n);if(Or(c,t),i!==null&&_S(t,s-r,c,a,n,i),vt(a)){let u=yt(n.index,t);u[ce]=So(t,e,s,n)}}}function bS(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=BC();try{Pn(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];il(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&TS(c,u)}}finally{Pn(-1),il(s)}}function TS(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function zd(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];bv(t,i.selectors,!1)&&(r??=[],vt(i)?r.unshift(i):r.push(i))}return r}function SS(e,t,n,r,o,i){let s=tt(e,t);MS(t[G],s,i,e.value,n,r,o)}function MS(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?wr(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function _S(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Sl(r,n,c,u)}}function qd(e,t){let n=e[mt],r=n?n.get(Dt,null):null;r&&r.handleError(t)}function Gd(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=t.data[u];Sl(d,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];Sl(l,u,r,o),a=!0}return a}function NS(e,t){let n=yt(t,e),r=n[S];RS(r,n);let o=n[je];o!==null&&n[qe]===null&&(n[qe]=sv(o,n[mt])),z(18),Wd(r,n,n[ce]),z(19,n[ce])}function RS(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Wd(e,t,n){Ed(t);try{let r=e.viewQuery;r!==null&&vl(1,r,n);let o=e.template;o!==null&&Ov(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[$t]?.finishViewCreation(e),e.staticContentQueries&&cv(e,t),e.staticViewQueries&&vl(2,e.viewQuery,n);let i=e.components;i!==null&&AS(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[N]&=-5,wd()}}function AS(e,t){for(let n=0;n<t.length;n++)NS(e,t[n])}function Lr(e,t,n,r){let o=k(null);try{let i=t.tView,a=e[N]&4096?4096:16,c=Ud(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[An]=u;let l=e[$t];return l!==null&&(c[$t]=l.createEmbeddedView(i)),Wd(i,c,n),c}finally{k(o)}}function Fn(e,t){return!t||t.firstChild===null||Us(e)}var xS;function Zd(e,t){return xS(e,t)}var Gt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Gt||{});function qn(e){return(e.flags&32)===32}function gr(e,t,n,r,o){if(r!=null){let i,s=!1;It(r)?i=r:Xe(r)&&(s=!0,r=r[je]);let a=Je(r);e===0&&n!==null?o==null?Mv(t,n,a):$s(t,n,a,o||null,!0):e===1&&n!==null?$s(t,n,a,o||null,!0):e===2?jd(t,a,s):e===3&&t.destroyNode(a),i!=null&&$S(t,e,i,n,o)}}function OS(e,t){kv(e,t),t[je]=null,t[xe]=null}function PS(e,t,n,r,o,i){r[je]=o,r[xe]=t,Ra(e,r,n,1,o,i)}function kv(e,t){t[Bt].changeDetectionScheduler?.notify(9),Ra(e,t,t[G],2,null,null)}function kS(e){let t=e[bo];if(!t)return Lu(e[S],e);for(;t;){let n=null;if(Xe(t))n=t[bo];else{let r=t[me];r&&(n=r)}if(!n){for(;t&&!t[Ke]&&t!==e;)Xe(t)&&Lu(t[S],t),t=t[ue];t===null&&(t=e),Xe(t)&&Lu(t[S],t),n=t&&t[Ke]}t=n}}function Yd(e,t){let n=e[Cr],r=n.indexOf(t);n.splice(r,1)}function _a(e,t){if($n(t))return;let n=t[G];n.destroyNode&&Ra(e,t,n,3,null,null),kS(t)}function Lu(e,t){if($n(t))return;let n=k(null);try{t[N]&=-129,t[N]|=256,t[ze]&&co(t[ze]),LS(e,t),FS(e,t),t[S].type===1&&t[G].destroy();let r=t[An];if(r!==null&&It(t[ue])){r!==t[ue]&&Yd(r,t);let o=t[$t];o!==null&&o.detachView(e)}fl(t)}finally{k(n)}}function FS(e,t){let n=e.cleanup,r=t[Rs];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Rs]=null);let o=t[sn];if(o!==null){t[sn]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[xn];if(i!==null){t[xn]=null;for(let s of i)s.destroy()}}function LS(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof kn)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];z(4,a,c);try{c.call(a)}finally{z(5,a,c)}}else{z(4,o,i);try{i.call(o)}finally{z(5,o,i)}}}}}function Fv(e,t,n){return jS(e,t.parent,n)}function jS(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[je];if(Bn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Et.None||o===Et.Emulated)return null}return tt(r,n)}function Lv(e,t,n){return US(e,t,n)}function VS(e,t,n){return e.type&40?tt(e,n):null}var US=VS,Jp;function Na(e,t,n,r){let o=Fv(e,r,t),i=t[G],s=r.parent||t[xe],a=Lv(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Xp(i,o,n[c],a,!1);else Xp(i,o,n,a,!1);Jp!==void 0&&Jp(i,r,t,n,o)}function Eo(e,t){if(t!==null){let n=t.type;if(n&3)return tt(t,e);if(n&4)return Ml(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Eo(e,r);{let o=e[t.index];return It(o)?Ml(-1,o):Je(o)}}else{if(n&128)return Eo(e,t.next);if(n&32)return Zd(t,e)()||Je(e[t.index]);{let r=jv(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=On(e[Te]);return Eo(o,r)}else return Eo(e,t.next)}}}return null}function jv(e,t){if(t!==null){let r=e[Te][xe],o=t.projection;return r.projection[o]}return null}function Ml(e,t){let n=me+e+1;if(n<t.length){let r=t[n],o=r[S].firstChild;if(o!==null)return Eo(r,o)}return t[zt]}function Qd(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Or(Je(a),r),n.flags|=2),!qn(n))if(c&8)Qd(e,t,n.child,r,o,i,!1),gr(t,e,o,a,i);else if(c&32){let u=Zd(n,r),l;for(;l=u();)gr(t,e,o,l,i);gr(t,e,o,a,i)}else c&16?Vv(e,t,r,n,o,i):gr(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Ra(e,t,n,r,o,i){Qd(n,r,e.firstChild,t,o,i,!1)}function BS(e,t,n){let r=t[G],o=Fv(e,n,t),i=n.parent||t[xe],s=Lv(i,n,t);Vv(r,0,t,n,o,s)}function Vv(e,t,n,r,o,i){let s=n[Te],c=s[xe].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];gr(t,e,o,l,i)}else{let u=c,l=s[ue];Us(r)&&(u.flags|=128),Qd(e,t,u,l,o,i,!0)}}function $S(e,t,n,r,o){let i=n[zt],s=Je(n);i!==s&&gr(t,e,r,i,o);for(let a=me;a<n.length;a++){let c=n[a];Ra(c[S],c,e,t,r,i)}}function HS(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Gt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Gt.Important),e.setStyle(n,r,o,i))}}function Hs(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(Je(i)),It(i)&&zS(i,r);let s=n.type;if(s&8)Hs(e,t,n.child,r);else if(s&32){let a=Zd(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=jv(t,n);if(Array.isArray(a))r.push(...a);else{let c=On(t[Te]);Hs(c[S],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function zS(e,t){for(let n=me;n<e.length;n++){let r=e[n],o=r[S].firstChild;o!==null&&Hs(r[S],r,o,t)}e[zt]!==e[je]&&t.push(e[zt])}function Uv(e){if(e[vr]!==null){for(let t of e[vr])t.impl.addSequence(t);e[vr].length=0}}var Bv=[];function qS(e){return e[ze]??GS(e)}function GS(e){let t=Bv.pop()??Object.create(ZS);return t.lView=e,t}function WS(e){e.lView[ze]!==e&&(e.lView=null,Bv.push(e))}var ZS=U(E({},tr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Rr(e.lView)},consumerOnSignalRead(){this.lView[ze]=this}});function YS(e){let t=e[ze]??Object.create(QS);return t.lView=e,t}var QS=U(E({},tr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=On(e.lView);for(;t&&!$v(t[S]);)t=On(t);t&&im(t)},consumerOnSignalRead(){this.lView[ze]=this}});function $v(e){return e.type!==2}function Hv(e){if(e[xn]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[xn])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[N]&8192)}}var KS=100;function zv(e,t=!0,n=0){let o=e[Bt].rendererFactory,i=!1;i||o.begin?.();try{XS(e,n)}catch(s){throw t&&qd(e,s),s}finally{i||o.end?.()}}function XS(e,t){let n=cm();try{xs(!0),_l(e,t);let r=0;for(;ha(e);){if(r===KS)throw new v(103,!1);r++,_l(e,1)}}finally{xs(n)}}function JS(e,t,n,r){if($n(t))return;let o=t[N],i=!1,s=!1;Ed(t);let a=!0,c=null,u=null;i||($v(e)?(u=qS(t),c=ao(u)):Zc()===null?(a=!1,u=YS(t),c=ao(u)):t[ze]&&(co(t[ze]),t[ze]=null));try{om(t),jC(e.bindingStartIndex),n!==null&&Ov(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&Ds(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Es(t,f,0,null),Ou(t,0)}if(s||eM(t),Hv(t),qv(t,0),e.contentQueries!==null&&cv(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&Ds(t,f)}else{let f=e.contentHooks;f!==null&&Es(t,f,1),Ou(t,1)}nM(e,t);let d=e.components;d!==null&&Wv(t,d,0);let h=e.viewQuery;if(h!==null&&vl(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&Ds(t,f)}else{let f=e.viewHooks;f!==null&&Es(t,f,2),Ou(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[xu]){for(let f of t[xu])f();t[xu]=null}i||(Uv(t),t[N]&=-73)}catch(l){throw i||Rr(t),l}finally{u!==null&&(bi(u,c),a&&WS(u)),wd()}}function qv(e,t){for(let n=Bm(e);n!==null;n=$m(n))for(let r=me;r<n.length;r++){let o=n[r];Gv(o,t)}}function eM(e){for(let t=Bm(e);t!==null;t=$m(t)){if(!(t[N]&2))continue;let n=t[Cr];for(let r=0;r<n.length;r++){let o=n[r];im(o)}}}function tM(e,t,n){z(18);let r=yt(t,e);Gv(r,n),z(19,r[ce])}function Gv(e,t){dd(e)&&_l(e,t)}function _l(e,t){let r=e[S],o=e[N],i=e[ze],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Ti(i)),s||=!1,i&&(i.dirty=!1),e[N]&=-9217,s)JS(r,e,r.template,e[ce]);else if(o&8192){Hv(e),qv(e,1);let a=r.components;a!==null&&Wv(e,a,1),Uv(e)}}function Wv(e,t,n){for(let r=0;r<t.length;r++)tM(e,t[r],n)}function nM(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Pn(~o);else{let i=o,s=n[++r],a=n[++r];UC(s,i);let c=t[i];z(24,c),a(2,c),z(25,c)}}}finally{Pn(-1)}}function Aa(e,t){let n=cm()?64:1088;for(e[Bt].changeDetectionScheduler?.notify(t);e;){e[N]|=n;let r=On(e);if(To(e)&&!r)return e;e=r}return null}function Zv(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Yv(e,t){let n=me+t;if(n<e.length)return e[n]}function jr(e,t,n,r=!0){let o=t[S];if(rM(o,t,e,n),r){let s=Ml(n,e),a=t[G],c=a.parentNode(e[zt]);c!==null&&PS(o,e[xe],a,t,c,s)}let i=t[qe];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Kd(e,t){let n=_o(e,t);return n!==void 0&&_a(n[S],n),n}function _o(e,t){if(e.length<=me)return;let n=me+t,r=e[n];if(r){let o=r[An];o!==null&&o!==e&&Yd(o,r),t>0&&(e[n-1][Ke]=r[Ke]);let i=_s(e,me+t);OS(r[S],r);let s=i[$t];s!==null&&s.detachView(i[S]),r[ue]=null,r[Ke]=null,r[N]&=-129}return r}function rM(e,t,n,r){let o=me+r,i=n.length;r>0&&(n[o-1][Ke]=t),r<i-me?(t[Ke]=n[o],Bg(n,me+r,t)):(n.push(t),t[Ke]=null),t[ue]=n;let s=t[An];s!==null&&n!==s&&Qv(s,t);let a=t[$t];a!==null&&a.insertView(e),rl(t),t[N]|=128}function Qv(e,t){let n=e[Cr],r=t[ue];if(Xe(r))e[N]|=2;else{let o=r[ue][Te];t[Te]!==o&&(e[N]|=2)}n===null?e[Cr]=[t]:n.push(t)}var No=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[S];return Hs(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[ce]}set context(t){this._lView[ce]=t}get destroyed(){return $n(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[ue];if(It(t)){let n=t[As],r=n?n.indexOf(this):-1;r>-1&&(_o(t,r),_s(n,r))}this._attachedToViewContainer=!1}_a(this._lView[S],this._lView)}onDestroy(t){sm(this._lView,t)}markForCheck(){Aa(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[N]&=-129}reattach(){rl(this._lView),this._lView[N]|=128}detectChanges(){this._lView[N]|=1024,zv(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new v(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=To(this._lView),n=this._lView[An];n!==null&&!t&&Yd(n,this._lView),kv(this._lView[S],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new v(902,!1);this._appRef=t;let n=To(this._lView),r=this._lView[An];r!==null&&!n&&Qv(r,this._lView),rl(this._lView)}};var Ln=(()=>{class e{static __NG_ELEMENT_ID__=sM}return e})(),oM=Ln,iM=class extends oM{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Lr(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new No(o)}};function sM(){return xa(Ee(),R())}function xa(e,t){return e.type&4?new iM(t,e,xr(e,t)):null}function Vr(e,t,n,r,o){let i=e.data[t];if(i===null)i=aM(e,t,n,r,o),VC()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=kC();i.injectorIndex=s===null?-1:s.injectorIndex}return ln(i,!0),i}function aM(e,t,n,r,o){let i=am(),s=gd(),a=s?i:i&&i.parent,c=e.data[t]=uM(e,a,n,t,r,o);return cM(e,c,i,s),c}function cM(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function uM(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Ar()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var lM=new RegExp(`^(\\d+)*(${Gm}|${qm})*(.*)`);function dM(e){let t=e.match(lM),[n,r,o,i]=t,s=r?parseInt(r,10):o,a=[];for(let[c,u,l]of i.matchAll(/(f|n)(\d*)/g)){let d=parseInt(l,10)||1;a.push(u,d)}return[s,...a]}function fM(e){return!e.prev&&e.parent?.type===8}function ju(e){return e.index-W}function hM(e,t){let n=e.i18nNodes;if(n)return n.get(t)}function Oa(e,t,n,r){let o=ju(r),i=hM(e,o);if(i===void 0){let s=e.data[Lb];if(s?.[o])i=gM(s[o],n);else if(t.firstChild===r)i=e.firstChild;else{let a=r.prev===null,c=r.prev??r.parent;if(fM(r)){let u=ju(r.parent);i=ml(e,u)}else{let u=tt(c,n);if(a)i=u.firstChild;else{let l=ju(c),d=ml(e,l);if(c.type===2&&d){let f=Pd(e,l)+1;i=Pa(f,d)}else i=u.nextSibling}}}}return i}function Pa(e,t){let n=t;for(let r=0;r<e;r++)n=n.nextSibling;return n}function pM(e,t){let n=e;for(let r=0;r<t.length;r+=2){let o=t[r],i=t[r+1];for(let s=0;s<i;s++)switch(o){case xb:n=n.firstChild;break;case Ob:n=n.nextSibling;break}}return n}function gM(e,t){let[n,...r]=dM(e),o;if(n===qm)o=t[Te][je];else if(n===Gm)o=XT(t[Te][je]);else{let i=Number(n);o=Je(t[i+W])}return pM(o,r)}var mM=!1;function vM(e){mM=e}function yM(e){let t=e[qe];if(t){let{i18nNodes:n,dehydratedIcuData:r}=t;if(n&&r){let o=e[G];for(let i of r.values())DM(o,n,i)}t.i18nNodes=void 0,t.dehydratedIcuData=void 0}}function DM(e,t,n){for(let r of n.node.cases[n.case]){let o=t.get(r.index-W);o&&jd(e,o,!1)}}function Kv(e){let t=e[Ht]??[],r=e[ue][G],o=[];for(let i of t)i.data[jb]!==void 0?o.push(i):Xv(i,r);e[Ht]=o}function EM(e){let{lContainer:t}=e,n=t[Ht];if(n===null)return;let o=t[ue][G];for(let i of n)Xv(i,o)}function Xv(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[Bs];for(;n<o;){let i=r.nextSibling;jd(t,r,!1),r=i,n++}}}function ka(e){Kv(e);let t=e[je];Xe(t)&&zs(t);for(let n=me;n<e.length;n++)zs(e[n])}function zs(e){yM(e);let t=e[S];for(let n=W;n<t.bindingStartIndex;n++)if(It(e[n])){let r=e[n];ka(r)}else Xe(e[n])&&zs(e[n])}function Jv(e){let t=e._views;for(let n of t){let r=yT(n);r!==null&&r[je]!==null&&(Xe(r)?zs(r):ka(r))}}function wM(e,t,n,r){e!==null&&(n.cleanup(t),ka(e.lContainer),Jv(r))}function IM(e,t){let n=[];for(let r of t)for(let o=0;o<(r[Wm]??1);o++){let i={data:r,firstChild:null};r[Bs]>0&&(i.firstChild=e,e=Pa(r[Bs],e)),n.push(i)}return[e,n]}var ey=()=>null;function CM(e,t){let n=e[Ht];return!t||n===null||n.length===0?null:n[0].data[Fb]===t?n.shift():(Kv(e),null)}function bM(){ey=CM}function Tr(e,t){return ey(e,t)}var TM=class{},ty=class{},Nl=class{resolveComponentFactory(t){throw Error(`No component factory found for ${Re(t)}.`)}},Fa=class{static NULL=new Nl},Sr=class{},Ur=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>SM()}return e})();function SM(){let e=R(),t=Ee(),n=yt(t.index,e);return(Xe(n)?n:e)[G]}var MM=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>null})}return e})();var Vu={},Er=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=sa(r);let o=this.injector.get(t,Vu,r);return o!==Vu||n===Vu?o:this.parentInjector.get(t,n,r)}};function Rl(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Yu(o,a);else if(i==2){let c=a,u=t[++s];r=Yu(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function $(e,t=P.Default){let n=R();if(n===null)return I(e,t);let r=Ee();return bm(r,n,ge(e),t)}function ny(){let e="invalid";throw new Error(e)}function Xd(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,u=null,l=NM(s);l===null?a=s:[a,c,u]=l,xM(e,t,n,a,i,c,u)}i!==null&&r!==null&&_M(n,r,i)}function _M(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new v(-301,!1);r.push(t[o],i)}}function NM(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&vt(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,RM(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function RM(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function AM(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function xM(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&vt(f)&&(c=!0,AM(e,n,h)),al(Fs(n,t),e,f.type)}jM(n,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=Av(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(n.mergedAttrs=br(n.mergedAttrs,f.hostAttrs),PM(e,n,t,d,f),LM(d,f,o),s!==null&&s.has(f)){let[m,D]=s.get(f);n.directiveToIndex.set(f.type,[d,m+n.directiveStart,D+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let p=f.type.prototype;!u&&(p.ngOnChanges||p.ngOnInit||p.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(p.ngOnChanges||p.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}OM(e,n,i)}function OM(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))eg(0,t,o,r),eg(1,t,o,r),ng(t,r,!1);else{let i=n.get(o);tg(0,t,i,r),tg(1,t,i,r),ng(t,r,!0)}}}function eg(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),ry(t,i)}}function tg(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),ry(t,s)}}function ry(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function ng(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Fd(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function PM(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Rn(o.type,!0)),s=new kn(i,vt(o),$);e.blueprint[r]=s,n[r]=s,kM(e,t,r,Av(e,n,o.hostVars,Ve),o)}function kM(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;FM(s)!=a&&s.push(a),s.push(n,r,i)}}function FM(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function LM(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;vt(t)&&(n[""]=e)}}function jM(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function oy(e,t,n,r,o,i,s,a){let c=t.consts,u=cn(c,s),l=Vr(t,e,2,r,u);return i&&Xd(t,n,l,cn(c,a),o),l.mergedAttrs=br(l.mergedAttrs,l.attrs),l.attrs!==null&&Rl(l,l.attrs,!1),l.mergedAttrs!==null&&Rl(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function iy(e,t){Id(e,t),ud(t)&&e.queries.elementEnd(t)}var qs=class extends Fa{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Ut(t);return new jn(n,this.ngModule)}};function VM(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&Sa.SignalBased)!==0};return o&&(i.transform=o),i})}function UM(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function BM(e,t,n){let r=t instanceof ve?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Er(n,r):n}function $M(e){let t=e.get(Sr,null);if(t===null)throw new v(407,!1);let n=e.get(MM,null),r=e.get(un,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function HM(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Ld(t,n,n==="svg"?tm:n==="math"?CC:null)}var jn=class extends ty{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=VM(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=UM(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=uS(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){z(22);let i=k(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:lS(this.componentDef.selectors[0]),c=Vd(0,null,null,1,0,null,null,null,null,[a],null),u=BM(s,o||this.ngModule,t),l=$M(u),d=l.rendererFactory.createRenderer(null,s),h=r?vS(d,r,s.encapsulation,u):HM(s,d),f=Ud(null,c,null,512|Rv(s),null,null,l,d,u,null,sv(h,u,!0));f[W]=h,Ed(f);let p=null;try{let m=oy(W,c,f,"#host",()=>[this.componentDef],!0,0);h&&(Nv(d,h,m),Or(h,f)),Ma(c,f,m),kd(c,m,f),iy(c,m),n!==void 0&&zM(m,this.ngContentSelectors,n),p=yt(m.index,f),f[ce]=p[ce],Wd(c,f,null)}catch(m){throw p!==null&&fl(p),fl(f),m}finally{z(23),wd()}return new Al(this.componentType,f)}finally{k(i)}}},Al=class extends TM{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=Po(n[S],W),this.location=xr(this._tNode,n),this.instance=yt(this._tNode.index,n)[ce],this.hostView=this.changeDetectorRef=new No(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Gd(r,o[S],o,t,n);this.previousInputValues.set(t,n);let s=yt(r.index,o);Aa(s,1)}get injector(){return new Nn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function zM(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Qt=(()=>{class e{static __NG_ELEMENT_ID__=qM}return e})();function qM(){let e=Ee();return ay(e,R())}var GM=Qt,sy=class extends GM{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return xr(this._hostTNode,this._hostLView)}get injector(){return new Nn(this._hostTNode,this._hostLView)}get parentInjector(){let t=Cd(this._hostTNode,this._hostLView);if(ym(t)){let n=Ps(t,this._hostLView),r=Os(t),o=n[S].data[r+8];return new Nn(o,n)}else return new Nn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=rg(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-me}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Tr(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Fn(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!DC(t),a;if(s)a=n;else{let p=n||{};a=p.index,r=p.injector,o=p.projectableNodes,i=p.environmentInjector||p.ngModuleRef}let c=s?t:new jn(Ut(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let m=(s?u:this.parentInjector).get(ve,null);m&&(i=m)}let l=Ut(c.componentType??{}),d=Tr(this._lContainer,l?.id??null),h=d?.firstChild??null,f=c.create(u,o,h,i);return this.insertImpl(f.hostView,a,Fn(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(TC(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[ue],u=new sy(c,c[xe],c[ue]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return jr(s,o,i,r),t.attachToViewContainerRef(),Bg(Uu(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=rg(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=_o(this._lContainer,n);r&&(_s(Uu(this._lContainer),n),_a(r[S],r))}detach(t){let n=this._adjustIndex(t,-1),r=_o(this._lContainer,n);return r&&_s(Uu(this._lContainer),n)!=null?new No(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function rg(e){return e[As]}function Uu(e){return e[As]||(e[As]=[])}function ay(e,t){let n,r=t[e.index];return It(r)?n=r:(n=Zv(r,t,null,e),t[e.index]=n,Bd(t,n)),cy(n,t,e,r),new sy(n,e,t)}function WM(e,t){let n=e[G],r=n.createComment(""),o=tt(t,e),i=n.parentNode(o);return $s(n,i,r,n.nextSibling(o),!1),r}var cy=uy,Jd=()=>!1;function ZM(e,t,n){return Jd(e,t,n)}function uy(e,t,n,r){if(e[zt])return;let o;n.type&8?o=Je(r):o=WM(t,n),e[zt]=o}function YM(e,t,n){if(e[zt]&&e[Ht])return!0;let r=n[qe],o=t.index-W;if(!r||Tb(t)||Fo(r,o))return!1;let s=ml(r,o),a=r.data[Rd]?.[o],[c,u]=IM(s,a);return e[zt]=c,e[Ht]=u,!0}function QM(e,t,n,r){Jd(e,n,t)||uy(e,t,n,r)}function KM(){cy=QM,Jd=YM}var xl=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Ol=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)tf(t,n).matches!==null&&this.queries[n].setDirty()}},Gs=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=r_(t):this.predicate=t}},Pl=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},kl=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,XM(n,i)),this.matchTNodeWithReadOption(t,n,ws(n,t,i,!1,!1))}else r===Ln?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,ws(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Ct||o===Qt||o===Ln&&n.type&4)this.addMatch(n.index,-2);else{let i=ws(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function XM(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function JM(e,t){return e.type&11?xr(e,t):e.type&4?xa(e,t):null}function e_(e,t,n,r){return n===-1?JM(t,e):n===-2?t_(e,t,r):So(e,e[S],n,t)}function t_(e,t,n){if(n===Ct)return xr(t,e);if(n===Ln)return xa(t,e);if(n===Qt)return ay(t,e)}function ly(e,t,n,r){let o=t[$t].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(e_(t,l,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Fl(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=ly(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=t[-c];for(let d=me;d<l.length;d++){let h=l[d];h[An]===h[ue]&&Fl(h[S],h,u,r)}if(l[Cr]!==null){let d=l[Cr];for(let h=0;h<d.length;h++){let f=d[h];Fl(f[S],f,u,r)}}}}}return r}function ef(e,t){return e[$t].queries[t].queryList}function dy(e,t,n){let r=new dl((n&4)===4);return _C(e,t,r,r.destroy),(t[$t]??=new Ol).queries.push(new xl(r))-1}function n_(e,t,n){let r=X();return r.firstCreatePass&&(hy(r,new Gs(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),dy(r,R(),t)}function fy(e,t,n,r){let o=X();if(o.firstCreatePass){let i=Ee();hy(o,new Gs(t,n,r),i.index),o_(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return dy(o,R(),n)}function r_(e){return e.split(",").map(t=>t.trim())}function hy(e,t,n){e.queries===null&&(e.queries=new Pl),e.queries.track(new kl(t,n))}function o_(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function tf(e,t){return e.queries.getByIndex(t)}function py(e,t){let n=e[S],r=tf(n,t);return r.crossesNgTemplate?Fl(n,e,t,[]):ly(n,e,r,t)}function gy(e,t,n){let r,o=_i(()=>{r._dirtyCounter();let i=c_(r,e);if(t&&i===void 0)throw new v(-951,!1);return i});return r=o[Me],r._dirtyCounter=km(0),r._flatValue=void 0,o}function i_(e){return gy(!0,!1,e)}function s_(e){return gy(!0,!0,e)}function a_(e,t){let n=e[Me];n._lView=R(),n._queryIndex=t,n._queryList=ef(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function c_(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[N]&4)return t?void 0:Ce;let o=ef(n,r),i=py(n,r);return o.reset(i,Pm),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function og(e,t){return i_(t)}function u_(e,t){return s_(t)}var iU=(og.required=u_,og);function l_(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(p_))}return i}return Ws.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(u=>{o.template=u}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let u=o.styles.length,l=o.styleUrls;o.styleUrls.forEach((d,h)=>{a.push(""),s.push(r(d).then(f=>{a[u+h]=f,l.splice(l.indexOf(d),1),l.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(u=>{a.push(u),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>g_(i));t.push(c)}),f_(),Promise.all(t).then(()=>{})}var Ws=new Map,d_=new Set;function f_(){let e=Ws;return Ws=new Map,e}function h_(){return Ws.size===0}function p_(e){return typeof e=="string"?e:e.text()}function g_(e){d_.delete(e)}var Mr=class{},nf=class{};var Zs=class extends Mr{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new qs(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=zg(t);this._bootstrapComponents=Iv(i.bootstrap),this._r3Injector=Mm(t,n,[{provide:Mr,useValue:this},{provide:Fa,useValue:this.componentFactoryResolver},...r],Re(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Ys=class extends nf{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Zs(this.moduleType,t,[])}};function m_(e,t,n){return new Zs(e,t,n,!1)}var Ll=class extends Mr{injector;componentFactoryResolver=new qs(this);instance=null;constructor(t){super();let n=new Co([...t.providers,{provide:Mr,useValue:this},{provide:Fa,useValue:this.componentFactoryResolver}],t.parent||ua(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Br(e,t,n=null){return new Ll({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var v_=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=sd(!1,n.type),o=r.length>0?Br([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=w({token:e,providedIn:"environment",factory:()=>new e(I(ve))})}return e})();function my(e){return xo(()=>{let t=vy(e),n=U(E({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Vm.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(v_).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Et.Emulated,styles:e.styles||Ce,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Ge("NgStandalone"),yy(n);let r=e.dependencies;return n.directiveDefs=ig(r,!1),n.pipeDefs=ig(r,!0),n.id=I_(n),n})}function y_(e){return Ut(e)||id(e)}function D_(e){return e!==null}function Tt(e){return xo(()=>({type:e.type,bootstrap:e.bootstrap||Ce,declarations:e.declarations||Ce,imports:e.imports||Ce,exports:e.exports||Ce,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function E_(e,t){if(e==null)return gt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Sa.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function w_(e){if(e==null)return gt;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function St(e){return xo(()=>{let t=vy(e);return yy(t),t})}function rf(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function vy(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||gt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ce,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:E_(e.inputs,t),outputs:w_(e.outputs),debugInfo:null}}function yy(e){e.features?.forEach(t=>t(e))}function ig(e,t){if(!e)return null;let n=t?qg:y_;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(D_)}function I_(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function C_(e){return Object.getPrototypeOf(e.prototype).constructor}function b_(e){let t=C_(e.type),n=!0,r=[e];for(;t;){let o;if(vt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new v(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Bu(e.inputs),s.declaredInputs=Bu(e.declaredInputs),s.outputs=Bu(e.outputs);let a=o.hostBindings;a&&N_(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&M_(e,c),u&&__(e,u),T_(e,o),VI(e.outputs,o.outputs),vt(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===b_&&(n=!1)}}t=Object.getPrototypeOf(t)}S_(r)}function T_(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function S_(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=br(o.hostAttrs,n=br(n,o.hostAttrs))}}function Bu(e){return e===gt?{}:e===Ce?[]:e}function M_(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function __(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function N_(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function cU(e){let t=n=>{let r=Array.isArray(e);n.hostDirectives===null?(n.findHostDirectiveDefs=Dy,n.hostDirectives=r?e.map(jl):[e]):r?n.hostDirectives.unshift(...e.map(jl)):n.hostDirectives.unshift(e)};return t.ngInherit=!0,t}function Dy(e,t,n){if(e.hostDirectives!==null)for(let r of e.hostDirectives)if(typeof r=="function"){let o=r();for(let i of o)sg(jl(i),t,n)}else sg(r,t,n)}function sg(e,t,n){let r=id(e.directive);R_(r.declaredInputs,e.inputs),Dy(r,t,n),n.set(r,e),t.push(r)}function jl(e){return typeof e=="function"?{directive:ge(e),inputs:gt,outputs:gt}:{directive:ge(e.directive),inputs:ag(e.inputs),outputs:ag(e.outputs)}}function ag(e){if(e===void 0||e.length===0)return gt;let t={};for(let n=0;n<e.length;n+=2)t[e[n]]=e[n+1];return t}function R_(e,t){for(let n in t)if(t.hasOwnProperty(n)){let r=t[n],o=e[n];e[r]=o}}function Ey(e){return x_(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function A_(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function x_(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function of(e,t,n){return e[t]=n}function O_(e,t){return e[t]}function et(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Qs(e,t,n,r){let o=et(e,t,n);return et(e,t+1,r)||o}function P_(e,t,n,r,o,i){let s=Qs(e,t,n,r);return Qs(e,t+2,o,i)||s}function k_(e,t,n,r,o,i,s,a,c){let u=t.consts,l=Vr(t,e,4,s||null,a||null);pd()&&Xd(t,n,l,cn(u,c),zd),l.mergedAttrs=br(l.mergedAttrs,l.attrs),Id(t,l);let d=l.tView=Vd(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function Ks(e,t,n,r,o,i,s,a,c,u){let l=n+W,d=t.firstCreatePass?k_(l,t,e,r,o,i,s,a,c):t.data[l];ln(d,!1);let h=wy(t,e,d,n);ma()&&Na(t,e,h,d),Or(h,e);let f=Zv(h,e,h,d);return e[l]=f,Bd(e,f),ZM(f,d,e),fa(d)&&Ma(t,e,d),c!=null&&$d(e,d,u),d}function F_(e,t,n,r,o,i,s,a){let c=R(),u=X(),l=cn(u.consts,i);return Ks(c,u,e,t,n,r,o,l,s,a),F_}var wy=Iy;function Iy(e,t,n,r){return dn(!0),t[G].createComment("")}function L_(e,t,n,r){let o=t[qe],i=!o||Ar()||qn(n)||Fo(o,r);if(dn(i),i)return Iy(e,t);let s=o.data[kb]?.[r]??null;s!==null&&n.tView!==null&&n.tView.ssrId===null&&(n.tView.ssrId=s);let a=Oa(o,e,t,n);Ca(o,r,a);let c=Pd(o,r);return Pa(c,a)}function j_(){wy=L_}var V_=(()=>{class e{cachedInjectors=new Map;getOrCreateInjector(n,r,o,i){if(!this.cachedInjectors.has(n)){let s=o.length>0?Br(o,r,i):null;this.cachedInjectors.set(n,s)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=w({token:e,providedIn:"environment",factory:()=>new e})}return e})();var U_=new y("");function $u(e,t,n){return e.get(V_).getOrCreateInjector(t,e,n,"")}function B_(e,t,n){if(e instanceof Er){let o=e.injector,i=e.parentInjector,s=$u(i,t,n);return new Er(o,s)}let r=e.get(ve);if(r!==e){let o=$u(r,t,n);return new Er(e,o)}return $u(e,t,n)}function mr(e,t,n,r=!1){let o=n[ue],i=o[S];if($n(o))return;let s=ko(o,t),a=s[Ea],c=s[Xb];if(!(c!==null&&e<c)&&cg(a,e)&&cg(s[Yb]??-1,e)){let u=wa(i,t),d=!r&&!0&&(rT(u)!==null||$p(u,ae.Loading)!==null||$p(u,ae.Placeholder))?z_:H_;try{d(e,s,n,t,o)}catch(h){qd(o,h)}}}function $_(e,t){let n=e[Ht]?.findIndex(o=>o.data[Vb]===t[Ea])??-1;return{dehydratedView:n>-1?e[Ht][n]:null,dehydratedViewIx:n}}function H_(e,t,n,r,o){z(20);let i=nT(e,o,r);if(i!==null){t[Ea]=e;let s=o[S],a=i+W,c=Po(s,a),u=0;Kd(n,u);let l;if(e===ae.Complete){let p=wa(s,r),m=p.providers;m&&m.length>0&&(l=B_(o[mt],p,m))}let{dehydratedView:d,dehydratedViewIx:h}=$_(n,t),f=Lr(o,c,null,{injector:l,dehydratedView:d});if(jr(n,f,u,Fn(c,d)),Aa(f,2),h>-1&&n[Ht]?.splice(h,1),(e===ae.Complete||e===ae.Error)&&Array.isArray(t[Dr])){for(let p of t[Dr])p();t[Dr]=null}}z(21)}function cg(e,t){return e<t}function ug(e,t,n){e.loadingPromise.then(()=>{e.loadingState===Le.COMPLETE?mr(ae.Complete,t,n):e.loadingState===Le.FAILED&&mr(ae.Error,t,n)})}var z_=null;var sf=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var af=new y(""),jo=new y(""),La=(()=>{class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];_taskTrackingZone=null;_destroyRef;constructor(n,r,o){this._ngZone=n,this.registry=r,cd()&&(this._destroyRef=g(Zt,{optional:!0})??void 0),cf||(q_(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this._taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){let n=this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),r=this._ngZone.runOutsideAngular(()=>this._ngZone.onStable.subscribe({next:()=>{q.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}}));this._destroyRef?.onDestroy(()=>{n.unsubscribe(),r.unsubscribe()})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this._taskTrackingZone?this._taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n()},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this._taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static \u0275fac=function(r){return new(r||e)(I(q),I(ja),I(jo))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),ja=(()=>{class e{_applications=new Map;registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return cf?.findTestabilityInTree(this,n,r)??null}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();function q_(e){cf=e}var cf,Cy=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>new Vl})}return e})(),Vl=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function Vo(e){return!!e&&typeof e.then=="function"}function by(e){return!!e&&typeof e.subscribe=="function"}var Ty=new y("");function uf(e){return Un([{provide:Ty,multi:!0,useValue:e}])}var Sy=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=g(Ty,{optional:!0})??[];injector=g(ye);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Ae(this.injector,o);if(Vo(i))n.push(i);else if(by(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Gn=new y("");function G_(){Xc(()=>{throw new v(600,!1)})}function W_(e){return e.isBoundToModule}var Z_=10;function My(e,t){return Array.isArray(t)?t.reduce(My,e):E(E({},e),t)}var Se=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=g(vb);afterRenderManager=g(Xm);zonelessEnabled=g(Td);rootEffectScheduler=g(Cy);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new J;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=g(nt).hasPendingTasks.pipe(F(n=>!n));constructor(){g(Fr,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=g(ve);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=ye.NULL){z(10);let i=n instanceof ty;if(!this._injector.get(Sy).done){let f="";throw new v(405,f)}let a;i?a=n:a=this._injector.get(Fa).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=W_(a)?void 0:this._injector.get(Mr),u=r||a.selector,l=a.create(o,[],u,c),d=l.location.nativeElement,h=l.injector.get(af,null);return h?.registerApplication(d),l.onDestroy(()=>{this.detachView(l.hostView),Cs(this.components,l),h?.unregisterApplication(d)}),this._loadComponent(l),z(11,l),l}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){z(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Ad.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new v(101,!1);let n=k(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,k(n),this.afterTick.next(),z(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Sr,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<Z_;)z(14),this.synchronizeOnce(),z(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)Y_(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ha(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Cs(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Gn,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Cs(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new v(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Cs(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Y_(e,t,n,r){if(!n&&!ha(e))return;zv(e,t,n&&!r?0:1)}function Q_(e,t,n){let r=t[mt],o=t[S];if(e.loadingState!==Le.NOT_STARTED)return e.loadingPromise??Promise.resolve();let i=ko(t,n),s=oT(o,e);e.loadingState=Le.IN_PROGRESS,Is(1,i);let a=e.dependencyResolverFn,c=r.get(ub).add();return a?(e.loadingPromise=Promise.allSettled(a()).then(u=>{let l=!1,d=[],h=[];for(let f of u)if(f.status==="fulfilled"){let p=f.value,m=Ut(p)||id(p);if(m)d.push(m);else{let D=qg(p);D&&h.push(D)}}else{l=!0;break}if(l){if(e.loadingState=Le.FAILED,e.errorTmplIndex===null){let f="",p=new v(-750,!1);qd(t,p)}}else{e.loadingState=Le.COMPLETE;let f=s.tView;if(d.length>0){f.directiveRegistry=Hp(f.directiveRegistry,d);let p=d.map(D=>D.type),m=sd(!1,...p);e.providers=m}h.length>0&&(f.pipeRegistry=Hp(f.pipeRegistry,h))}}),e.loadingPromise.finally(()=>{e.loadingPromise=null,c()})):(e.loadingPromise=Promise.resolve().then(()=>{e.loadingPromise=null,e.loadingState=Le.COMPLETE,c()}),e.loadingPromise)}function K_(e,t){return t[mt].get(U_,null,{optional:!0})?.behavior!==ev.Manual}function X_(e,t,n){let r=t[S],o=t[n.index];if(!K_(e,t))return;let i=ko(t,n),s=wa(r,n);switch(eT(i),s.loadingState){case Le.NOT_STARTED:mr(ae.Loading,n,o),Q_(s,t,n),s.loadingState===Le.IN_PROGRESS&&ug(s,n,o);break;case Le.IN_PROGRESS:mr(ae.Loading,n,o),ug(s,n,o);break;case Le.COMPLETE:mr(ae.Complete,n,o);break;case Le.FAILED:mr(ae.Error,n,o);break;default:}}function J_(e,t,n){return ut(this,null,function*(){let r=e.get(Od);if(r.hydrating.has(t))return;let{parentBlockPromise:i,hydrationQueue:s}=bT(t,e);if(s.length===0)return;i!==null&&s.shift(),n0(r,s),i!==null&&(yield i);let a=s[0];r.has(a)?yield lg(e,s,n):r.awaitParentBlock(a,()=>ut(null,null,function*(){return yield lg(e,s,n)}))})}function lg(e,t,n){return ut(this,null,function*(){let r=e.get(Od),o=r.hydrating,i=e.get(nt),s=i.add();for(let c=0;c<t.length;c++){let u=t[c],l=r.get(u);if(l!=null){if(yield o0(l),yield r0(e),e0(l)){EM(l),dg(t.slice(c),r);break}o.get(u).resolve()}else{t0(c,t,r),dg(t.slice(c),r);break}}let a=t[t.length-1];yield o.get(a)?.promise,i.remove(s),n&&n(t),wM(r.get(a),t,r,e.get(Se))})}function e0(e){return ko(e.lView,e.tNode)[Ea]===ae.Error}function t0(e,t,n){let r=e-1,o=r>-1?n.get(t[r]):null;o&&ka(o.lContainer)}function dg(e,t){let n=t.hydrating;for(let r in e)n.get(r)?.reject();t.cleanup(e)}function n0(e,t){for(let n of t)e.hydrating.set(n,Promise.withResolvers())}function r0(e){return new Promise(t=>Da(t,{injector:e}))}function o0(e){return ut(this,null,function*(){let{tNode:t,lView:n}=e,r=ko(n,t);return new Promise(o=>{i0(r,o),X_(2,n,t)})})}function i0(e,t){Array.isArray(e[Dr])||(e[Dr]=[]),e[Dr].push(t)}function lf(e,t,n,r){let o=R(),i=zn();if(et(o,i,t)){let s=X(),a=ga();SS(a,o,e,t,n,r)}return lf}function _y(e,t,n,r){return et(e,zn(),n)?t+wr(n)+r:Ve}function s0(e,t,n,r,o,i){let s=LC(),a=Qs(e,s,n,o);return yd(2),a?t+wr(n)+r+wr(o)+i:Ve}function gs(e,t){return e<<17|t<<2}function Vn(e){return e>>17&32767}function a0(e){return(e&2)==2}function c0(e,t){return e&131071|t<<17}function Ul(e){return e|2}function _r(e){return(e&131068)>>2}function Hu(e,t){return e&-131069|t<<2}function u0(e){return(e&1)===1}function Bl(e){return e|1}function l0(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Vn(s),c=_r(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||Oo(d,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let h=Vn(e[a+1]);e[r+1]=gs(h,a),h!==0&&(e[h+1]=Hu(e[h+1],r)),e[a+1]=c0(e[a+1],r)}else e[r+1]=gs(a,0),a!==0&&(e[a+1]=Hu(e[a+1],r)),a=r;else e[r+1]=gs(c,0),a===0?a=r:e[c+1]=Hu(e[c+1],r),c=r;u&&(e[r+1]=Ul(e[r+1])),fg(e,l,r,!0),fg(e,l,r,!1),d0(t,l,e,r,i),s=gs(a,c),i?t.classBindings=s:t.styleBindings=s}function d0(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Oo(i,t)>=0&&(n[r+1]=Bl(n[r+1]))}function fg(e,t,n,r){let o=e[n+1],i=t===null,s=r?Vn(o):_r(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];f0(c,t)&&(a=!0,e[s+1]=r?Bl(u):Ul(u)),s=r?Vn(u):_r(u)}a&&(e[n+1]=r?Ul(o):Bl(o))}function f0(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Oo(e,t)>=0:!1}var Qe={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function h0(e){return e.substring(Qe.key,Qe.keyEnd)}function p0(e){return g0(e),Ny(e,Ry(e,0,Qe.textEnd))}function Ny(e,t){let n=Qe.textEnd;return n===t?-1:(t=Qe.keyEnd=m0(e,Qe.key=t,n),Ry(e,t,n))}function g0(e){Qe.key=0,Qe.keyEnd=0,Qe.value=0,Qe.valueEnd=0,Qe.textEnd=e.length}function Ry(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function m0(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function v0(e,t,n){let r=R(),o=zn();if(et(r,o,t)){let i=X(),s=ga();Hd(i,s,r,e,t,r[G],n,!1)}return v0}function $l(e,t,n,r,o){Gd(t,e,n,o?"class":"style",r)}function y0(e,t,n){return xy(e,t,n,!1),y0}function D0(e,t){return xy(e,t,null,!0),D0}function uU(e){Oy(T0,Ay,e,!0)}function Ay(e,t){for(let n=p0(t);n>=0;n=Ny(t,n))aa(e,h0(t),!0)}function xy(e,t,n,r){let o=R(),i=X(),s=yd(2);if(i.firstUpdatePass&&ky(i,e,s,r),t!==Ve&&et(o,s,t)){let a=i.data[Wt()];Fy(i,a,o,o[G],e,o[s+1]=M0(t,n),r,s)}}function Oy(e,t,n,r){let o=X(),i=yd(2);o.firstUpdatePass&&ky(o,null,i,r);let s=R();if(n!==Ve&&et(s,i,n)){let a=o.data[Wt()];if(Ly(a,r)&&!Py(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Yu(c,n||"")),$l(o,a,s,n,r)}else S0(o,a,s,s[G],s[i+1],s[i+1]=b0(e,t,n),r,i)}}function Py(e,t){return t>=e.expandoStartIndex}function ky(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Wt()],s=Py(e,n);Ly(i,r)&&t===null&&!s&&(t=!1),t=E0(o,i,t,r),l0(o,i,t,n,s,r)}}function E0(e,t,n,r){let o=$C(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=zu(null,e,t,n,r),n=Ro(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=zu(o,e,t,n,r),i===null){let c=w0(e,t,r);c!==void 0&&Array.isArray(c)&&(c=zu(null,e,t,c[1],r),c=Ro(c,t.attrs,r),I0(e,t,r,c))}else i=C0(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function w0(e,t,n){let r=n?t.classBindings:t.styleBindings;if(_r(r)!==0)return e[Vn(r)]}function I0(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Vn(o)]=r}function C0(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Ro(r,s,n)}return Ro(r,t.attrs,n)}function zu(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Ro(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Ro(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),aa(e,s,n?!0:t[++i]))}return e===void 0?null:e}function b0(e,t,n){if(n==null||n==="")return Ce;let r=[],o=rt(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function T0(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&aa(e,r,n)}function S0(e,t,n,r,o,i,s,a){o===Ve&&(o=Ce);let c=0,u=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let h=c<o.length?o[c+1]:void 0,f=u<i.length?i[u+1]:void 0,p=null,m;l===d?(c+=2,u+=2,h!==f&&(p=d,m=f)):d===null||l!==null&&l<d?(c+=2,p=l):(u+=2,p=d,m=f),p!==null&&Fy(e,t,n,r,p,m,s,a),l=c<o.length?o[c]:null,d=u<i.length?i[u]:null}}function Fy(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=u0(u)?hg(c,t,n,o,_r(u),s):void 0;if(!Xs(l)){Xs(i)||a0(u)&&(i=hg(c,null,n,o,a,s));let d=nm(Wt(),n);HS(r,s,d,o,i)}}function hg(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=n[o+1];h===Ve&&(h=d?Ce:void 0);let f=d?Ru(h,r):l===r?h:void 0;if(u&&!Xs(f)&&(f=Ru(c,r)),Xs(f)&&(a=f,s))return a;let p=e[o+1];o=s?Vn(p):_r(p)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Ru(c,r))}return a}function Xs(e){return e!==void 0}function M0(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Re(rt(e)))),e}function Ly(e,t){return(e.flags&(t?8:16))!==0}function lU(e,t,n){let r=R(),o=_y(r,e,t,n);Oy(aa,Ay,o,!0)}var Hl=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function qu(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function _0(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let u=e.at(i),l=t[i],d=qu(i,u,i,l,n);if(d!==0){d<0&&e.updateValue(i,l),i++;continue}let h=e.at(s),f=t[c],p=qu(s,h,c,f,n);if(p!==0){p<0&&e.updateValue(s,f),s--,c--;continue}let m=n(i,u),D=n(s,h),b=n(i,l);if(Object.is(b,D)){let H=n(c,f);Object.is(H,m)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,l),i++;continue}if(r??=new Js,o??=gg(e,i,s,n),zl(e,r,i,b))e.updateValue(i,l),i++,s++;else if(o.has(b))r.set(m,e.detach(i)),s--;else{let H=e.create(i,t[i]);e.attach(i,H),i++,s++}}for(;i<=c;)pg(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),u=c.next();for(;!u.done&&i<=s;){let l=e.at(i),d=u.value,h=qu(i,l,i,d,n);if(h!==0)h<0&&e.updateValue(i,d),i++,u=c.next();else{r??=new Js,o??=gg(e,i,s,n);let f=n(i,d);if(zl(e,r,i,f))e.updateValue(i,d),i++,s++,u=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,u=c.next();else{let p=n(i,l);r.set(p,e.detach(i)),s--}}}for(;!u.done;)pg(e,r,n,e.length,u.value),u=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function zl(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function pg(e,t,n,r,o){if(zl(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function gg(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Js=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function dU(e,t){Ge("NgControlFlow");let n=R(),r=zn(),o=n[r]!==Ve?n[r]:-1,i=o!==-1?ea(n,W+o):void 0,s=0;if(et(n,r,e)){let a=k(null);try{if(i!==void 0&&Kd(i,s),e!==-1){let c=W+e,u=ea(n,c),l=Zl(n[S],c),d=Tr(u,l.tView.ssrId),h=Lr(n,l,t,{dehydratedView:d});jr(u,h,s,Fn(l,d))}}finally{k(a)}}else if(i!==void 0){let a=Yv(i,s);a!==void 0&&(a[ce]=t)}}var ql=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-me}};function fU(e,t){return t}var Gl=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function hU(e,t,n,r,o,i,s,a,c,u,l,d,h){Ge("NgControlFlow");let f=R(),p=X(),m=c!==void 0,D=R(),b=a?s.bind(D[Te][ce]):s,H=new Gl(m,b);D[W+e]=H,Ks(f,p,e+1,t,n,r,o,cn(p.consts,i)),m&&Ks(f,p,e+2,c,u,l,d,cn(p.consts,h))}var Wl=class extends Hl{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-me}at(t){return this.getLView(t)[ce].$implicit}attach(t,n){let r=n[qe];this.needsIndexUpdate||=t!==this.length,jr(this.lContainer,n,t,Fn(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,N0(this.lContainer,t)}create(t,n){let r=Tr(this.lContainer,this.templateTNode.tView.ssrId),o=Lr(this.hostLView,this.templateTNode,new ql(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){_a(t[S],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[ce].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[ce].$index=t}getLView(t){return R0(this.lContainer,t)}};function pU(e){let t=k(null),n=Wt();try{let r=R(),o=r[S],i=r[n],s=n+1,a=ea(r,s);if(i.liveCollection===void 0){let u=Zl(o,s);i.liveCollection=new Wl(a,r,u)}else i.liveCollection.reset();let c=i.liveCollection;if(_0(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let u=zn(),l=c.length===0;if(et(r,u,l)){let d=n+2,h=ea(r,d);if(l){let f=Zl(o,d),p=Tr(h,f.tView.ssrId),m=Lr(r,f,void 0,{dehydratedView:p});jr(h,m,0,Fn(f,p))}else Kd(h,0)}}}finally{k(t)}}function ea(e,t){return e[t]}function N0(e,t){return _o(e,t)}function R0(e,t){return Yv(e,t)}function Zl(e,t){return Po(e,t)}function jy(e,t,n,r){let o=R(),i=X(),s=W+e,a=o[G],c=i.firstCreatePass?oy(s,i,o,t,zd,pd(),n,r):i.data[s],u=Uy(i,o,c,a,t,e);o[s]=u;let l=fa(c);return ln(c,!0),Nv(a,u,c),!qn(c)&&ma()&&Na(i,o,u,c),(NC()===0||l)&&Or(u,o),RC(),l&&(Ma(i,o,c),kd(i,c,o)),r!==null&&$d(o,c),jy}function Vy(){let e=Ee();gd()?md():(e=e.parent,ln(e,!1));let t=e;xC(t)&&PC(),AC();let n=X();return n.firstCreatePass&&iy(n,t),t.classesWithoutHost!=null&&ZC(t)&&$l(n,t,R(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&YC(t)&&$l(n,t,R(),t.stylesWithoutHost,!1),Vy}function df(e,t,n,r){return jy(e,t,n,r),Vy(),df}var Uy=(e,t,n,r,o,i)=>(dn(!0),Ld(r,o,pm()));function A0(e,t,n,r,o,i){let s=t[qe],a=!s||Ar()||qn(n)||Fo(s,i);if(dn(a),a)return Ld(r,o,pm());let c=Oa(s,e,t,n);return av(s,i)&&Ca(s,i,c.nextSibling),s&&(Lm(n)||jm(c))&&Bn(n)&&(OC(n),_v(c)),c}function x0(){Uy=A0}function O0(e,t,n,r,o){let i=t.consts,s=cn(i,r),a=Vr(t,e,8,"ng-container",s);s!==null&&Rl(a,s,!0);let c=cn(i,o);return pd()&&Xd(t,n,a,c,zd),a.mergedAttrs=br(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function By(e,t,n){let r=R(),o=X(),i=e+W,s=o.firstCreatePass?O0(i,o,r,t,n):o.data[i];ln(s,!0);let a=Hy(o,r,s,e);return r[i]=a,ma()&&Na(o,r,a,s),Or(a,r),fa(s)&&(Ma(o,r,s),kd(o,s,r)),n!=null&&$d(r,s),By}function $y(){let e=Ee(),t=X();return gd()?md():(e=e.parent,ln(e,!1)),t.firstCreatePass&&(Id(t,e),ud(e)&&t.queries.elementEnd(e)),$y}function P0(e,t,n){return By(e,t,n),$y(),P0}var Hy=(e,t,n,r)=>(dn(!0),Sv(t[G],""));function k0(e,t,n,r){let o,i=t[qe],s=!i||Ar()||Fo(i,r)||qn(n);if(dn(s),s)return Sv(t[G],"");let a=Oa(i,e,t,n),c=IT(i,r);return Ca(i,r,a),o=Pa(c,a),o}function F0(){Hy=k0}function gU(){return R()}function L0(e,t,n){let r=R(),o=zn();if(et(r,o,t)){let i=X(),s=ga();Hd(i,s,r,e,t,r[G],n,!0)}return L0}var Mn=void 0;function j0(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var V0=["en",[["a","p"],["AM","PM"],Mn],[["AM","PM"],Mn,Mn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Mn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Mn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Mn,"{1} 'at' {0}",Mn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",j0],Gu={};function Oe(e){let t=U0(e),n=mg(t);if(n)return n;let r=t.split("-")[0];if(n=mg(r),n)return n;if(r==="en")return V0;throw new v(701,!1)}function mg(e){return e in Gu||(Gu[e]=fe.ng&&fe.ng.common&&fe.ng.common.locales&&fe.ng.common.locales[e]),Gu[e]}var ee=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ee||{});function U0(e){return e.toLowerCase().replace(/_/g,"-")}var ta="en-US",B0="USD";var $0=ta;function H0(e){typeof e=="string"&&($0=e.toLowerCase().replace(/_/g,"-"))}function vg(e,t,n){return function r(o){if(o===Function)return n;let i=Bn(e)?yt(e.index,t):t;Aa(i,5);let s=t[ce],a=yg(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=yg(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function yg(e,t,n,r){let o=k(null);try{return z(6,t,n),n(r)!==!1}catch(i){return z0(e,i),!1}finally{z(7,t,n),k(o)}}function z0(e,t){let n=e[mt],r=n?n.get(Dt,null):null;r&&r.handleError(t)}function Dg(e,t,n,r,o,i){let s=t[n],a=t[S],u=a.data[n].outputs[r],l=s[u],d=a.firstCreatePass?hd(a):null,h=fd(t),f=l.subscribe(i),p=h.length;h.push(i,f),d&&d.push(o,e.index,p,-(p+1))}function ff(e,t,n,r){let o=R(),i=X(),s=Ee();return zy(i,o,o[G],s,e,t,r),ff}function q0(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Rs],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function zy(e,t,n,r,o,i,s){let a=fa(r),u=e.firstCreatePass?hd(e):null,l=fd(t),d=!0;if(r.type&3||s){let h=tt(r,t),f=s?s(h):h,p=l.length,m=s?b=>s(Je(b[r.index])):r.index,D=null;if(!s&&a&&(D=q0(e,t,o,r.index)),D!==null){let b=D.__ngLastListenerFn__||D;b.__ngNextListenerFn__=i,D.__ngLastListenerFn__=i,d=!1}else{i=vg(r,t,i),lT(t,f,o,i);let b=n.listen(f,o,i);l.push(i,b),u&&u.push(o,m,p,p+1)}}else i=vg(r,t,i);if(d){let h=r.outputs?.[o],f=r.hostDirectiveOutputs?.[o];if(f&&f.length)for(let p=0;p<f.length;p+=2){let m=f[p],D=f[p+1];Dg(r,t,m,D,o,i)}if(h&&h.length)for(let p of h)Dg(r,t,p,o,o,i)}}function mU(e=1){return zC(e)}function G0(e,t){let n=null,r=oS(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?bv(e,i,!0):aS(r,i))return o}return n}function vU(e){let t=R()[Te][xe];if(!t.projection){let n=e?e.length:1,r=t.projection=iC(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?G0(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function yU(e,t=0,n,r,o,i){let s=R(),a=X(),c=r?e+1:null;c!==null&&Ks(s,a,c,r,o,i,null,n);let u=Vr(a,W+e,16,null,n||null);u.projection===null&&(u.projection=t),md();let d=!s[qe]||Ar();s[Te][xe].projection[u.projection]===null&&c!==null?W0(s,a,c):d&&!qn(u)&&BS(a,s,u)}function W0(e,t,n){let r=W+n,o=t.data[r],i=e[r],s=Tr(i,o.tView.ssrId),a=Lr(e,o,void 0,{dehydratedView:s});jr(i,a,0,Fn(o,s))}function qy(e,t,n,r){fy(e,t,n,r)}function DU(e,t,n){n_(e,t,n)}function Gy(e){let t=R(),n=X(),r=Dd();pa(r+1);let o=tf(n,r);if(e.dirty&&bC(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=py(t,r);e.reset(i,Pm),e.notifyOnChanges()}return!0}return!1}function Wy(){return ef(R(),Dd())}function EU(e,t,n,r,o){a_(t,fy(e,n,r,o))}function wU(e=1){pa(Dd()+e)}function IU(e){let t=FC();return ld(t,W+e)}function CU(e,t=""){let n=R(),r=X(),o=e+W,i=r.firstCreatePass?Vr(r,o,1,t,null):r.data[o],s=Zy(r,n,i,t,e);n[o]=s,ma()&&Na(r,n,s,i),ln(i,!1)}var Zy=(e,t,n,r,o)=>(dn(!0),Tv(t[G],r));function Z0(e,t,n,r,o){let i=t[qe],s=!i||Ar()||qn(n)||Fo(i,o);return dn(s),s?Tv(t[G],r):Oa(i,e,t,n)}function Y0(){Zy=Z0}function Q0(e){return Yy("",e,""),Q0}function Yy(e,t,n){let r=R(),o=_y(r,e,t,n);return o!==Ve&&Qy(r,Wt(),o),Yy}function K0(e,t,n,r,o){let i=R(),s=s0(i,e,t,n,r,o);return s!==Ve&&Qy(i,Wt(),s),K0}function Qy(e,t,n){let r=nm(t,e);dS(e[G],r,n)}function X0(e,t,n){Fm(t)&&(t=t());let r=R(),o=zn();if(et(r,o,t)){let i=X(),s=ga();Hd(i,s,r,e,t,r[G],n,!1)}return X0}function bU(e,t){let n=Fm(e);return n&&e.set(t),n}function J0(e,t){let n=R(),r=X(),o=Ee();return zy(r,n,n[G],o,e,t),J0}var eN={};function tN(e){let t=X(),n=R(),r=e+W,o=Vr(t,r,128,null,null);return ln(o,!1),rm(t,n,r,eN),tN}function nN(e,t,n){let r=X();if(r.firstCreatePass){let o=vt(e);Yl(n,r.data,r.blueprint,o,!0),Yl(t,r.data,r.blueprint,o,!1)}}function Yl(e,t,n,r,o){if(e=ge(e),Array.isArray(e))for(let i=0;i<e.length;i++)Yl(e[i],t,n,r,o);else{let i=X(),s=R(),a=Ee(),c=Ir(e)?e:ge(e.provide),u=Zg(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(Ir(e)||!e.multi){let f=new kn(u,o,$),p=Zu(c,t,o?l:l+h,d);p===-1?(al(Fs(a,s),i,c),Wu(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[p]=f,s[p]=f)}else{let f=Zu(c,t,l+h,d),p=Zu(c,t,l,l+h),m=f>=0&&n[f],D=p>=0&&n[p];if(o&&!D||!o&&!m){al(Fs(a,s),i,c);let b=iN(o?oN:rN,n.length,o,r,u);!o&&D&&(n[p].providerFactory=b),Wu(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(b),s.push(b)}else{let b=Ky(n[o?p:f],u,!o&&r);Wu(i,e,f>-1?f:p,b)}!o&&r&&D&&n[p].componentProviders++}}}function Wu(e,t,n,r){let o=Ir(t),i=fC(t);if(o||i){let c=(i?ge(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function Ky(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Zu(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function rN(e,t,n,r,o){return Ql(this.multi,[])}function oN(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=So(r,r[S],this.providerFactory.index,o);s=c.slice(0,a),Ql(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],Ql(i,s);return s}function Ql(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function iN(e,t,n,r,o){let i=new kn(e,n,$);return i.multi=[],i.index=t,i.componentProviders=0,Ky(i,o,r&&!n),i}function TU(e,t=[]){return n=>{n.providersResolver=(r,o)=>nN(r,o?o(e):e,t)}}function SU(e,t,n){let r=vd()+e,o=R();return o[r]===Ve?of(o,r,n?t.call(n):t()):O_(o,r)}function Xy(e,t){let n=e[t];return n===Ve?void 0:n}function sN(e,t,n,r,o,i,s){let a=t+n;return Qs(e,a,o,i)?of(e,a+2,s?r.call(s,o,i):r(o,i)):Xy(e,a+2)}function aN(e,t,n,r,o,i,s,a,c){let u=t+n;return P_(e,u,o,i,s,a)?of(e,u+4,c?r.call(c,o,i,s,a):r(o,i,s,a)):Xy(e,u+4)}function MU(e,t){let n=X(),r,o=e+W;n.firstCreatePass?(r=cN(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Rn(r.type,!0)),s,a=Ne($);try{let c=ks(!1),u=i();return ks(c),rm(n,R(),o,u),u}finally{Ne(a)}}function cN(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function _U(e,t,n,r){let o=e+W,i=R(),s=ld(i,o);return Jy(i,o)?sN(i,vd(),t,s.transform,n,r,s):s.transform(n,r)}function NU(e,t,n,r,o,i){let s=e+W,a=R(),c=ld(a,s);return Jy(a,s)?aN(a,vd(),t,c.transform,n,r,o,i,c):c.transform(n,r,o,i)}function Jy(e,t){return e[S].data[t].pure}function RU(e,t){return xa(e,t)}var ms=null;function uN(e){ms!==null&&(e.defaultEncapsulation!==ms.defaultEncapsulation||e.preserveWhitespaces!==ms.preserveWhitespaces)||(ms=e)}var Ao=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},AU=new Ao("19.2.14"),Kl=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},eD=(()=>{class e{compileModuleSync(n){return new Ys(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=zg(n),i=Iv(o.declarations).reduce((s,a)=>{let c=Ut(a);return c&&s.push(new jn(c)),s},[]);return new Kl(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),lN=new y("");function dN(e,t,n){let r=new Ys(n);return Promise.resolve(r)}function Eg(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var fN=(()=>{class e{zone=g(q);changeDetectionScheduler=g(un);applicationRef=g(Se);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function hN({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new q(U(E({},tD()),{scheduleInRootZone:n})),[{provide:q,useFactory:e},{provide:an,multi:!0,useFactory:()=>{let r=g(fN,{optional:!0});return()=>r.initialize()}},{provide:an,multi:!0,useFactory:()=>{let r=g(pN);return()=>{r.initialize()}}},t===!0?{provide:Nm,useValue:!0}:[],{provide:Rm,useValue:n??_m}]}function tD(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var pN=(()=>{class e{subscription=new te;initialized=!1;zone=g(q);pendingTasks=g(nt);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{q.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{q.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var gN=(()=>{class e{appRef=g(Se);taskService=g(nt);ngZone=g(q);zonelessEnabled=g(Td);tracing=g(Fr,{optional:!0});disableScheduling=g(Nm,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new te;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(js):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(g(Rm,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Vs||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Pp:Am;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(js+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Pp(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function mN(){return typeof $localize<"u"&&$localize.locale||ta}var Uo=new y("",{providedIn:"root",factory:()=>g(Uo,P.Optional|P.SkipSelf)||mN()}),nD=new y("",{providedIn:"root",factory:()=>B0});var na=new y(""),vN=new y("");function yo(e){return!e.moduleRef}function yN(e){let t=yo(e)?e.r3Injector:e.moduleRef.injector,n=t.get(q);return n.run(()=>{yo(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Dt,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),yo(e)){let i=()=>t.destroy(),s=e.platformInjector.get(na);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(na);s.add(i),e.moduleRef.onDestroy(()=>{Cs(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return EN(r,n,()=>{let i=t.get(Sy);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Uo,ta);if(H0(s||ta),!t.get(vN,!0))return yo(e)?t.get(Se):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(yo(e)){let c=t.get(Se);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return DN(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function DN(e,t){let n=e.injector.get(Se);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new v(-403,!1);t.push(e)}function EN(e,t,n){try{let r=n();return Vo(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var rD=(()=>{class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(n){this._injector=n}bootstrapModuleFactory(n,r){let o=r?.scheduleInRootZone,i=()=>mb(r?.ngZone,U(E({},tD({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing})),{scheduleInRootZone:o})),s=r?.ignoreChangesOutsideZone,a=[hN({ngZoneFactory:i,ignoreChangesOutsideZone:s}),{provide:un,useExisting:gN}],c=m_(n.moduleType,this.injector,a);return yN({moduleRef:c,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(n,r=[]){let o=My({},r);return dN(this.injector,o,n).then(i=>this.bootstrapModuleFactory(i,o))}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new v(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(na,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static \u0275fac=function(r){return new(r||e)(I(ye))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),Io=null,oD=new y("");function wN(e){if(Io&&!Io.get(oD,!1))throw new v(400,!1);G_(),Io=e;let t=e.get(rD);return bN(e),t}function hf(e,t,n=[]){let r=`Platform: ${t}`,o=new y(r);return(i=[])=>{let s=iD();if(!s||s.injector.get(oD,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):wN(IN(a,r))}return CN(o)}}function IN(e=[],t){return ye.create({name:t,providers:[{provide:ca,useValue:"platform"},{provide:na,useValue:new Set([()=>Io=null])},...e]})}function CN(e){let t=iD();if(!t)throw new v(401,!1);return t}function iD(){return Io?.get(rD)??null}function bN(e){let t=e.get(_d,null);Ae(e,()=>{t?.forEach(n=>n())})}var $r=(()=>{class e{static __NG_ELEMENT_ID__=TN}return e})();function TN(e){return SN(Ee(),R(),(e&16)===16)}function SN(e,t,n){if(Bn(e)&&!n){let r=yt(e.index,t);return new No(r,r)}else if(e.type&175){let r=t[Te];return new No(r,t)}return null}var Xl=class{constructor(){}supports(t){return Ey(t)}create(t){return new Jl(t)}},MN=(e,t)=>t,Jl=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||MN}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<wg(r,o,i)?n:r,a=wg(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,p=f+h;l<=p&&p<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Ey(t))throw new v(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,A_(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new ed(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new ra),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new ra),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},ed=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},td=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},ra=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new td,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function wg(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function Ig(){return new pf([new Xl])}var pf=(()=>{class e{factories;static \u0275prov=w({token:e,providedIn:"root",factory:Ig});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Ig()),deps:[[e,new nC,new Ug]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new v(901,!1)}}return e})();var sD=hf(null,"core",[]),aD=(()=>{class e{constructor(n){}static \u0275fac=function(r){return new(r||e)(I(Se))};static \u0275mod=Tt({type:e});static \u0275inj=wt({})}return e})();var vs=new WeakSet,Cg="",bs=[];function bg(e){return e.get(Km,Bb)}function cD(){let e=[{provide:Km,useFactory:()=>{let t=!0;{let n=g(Vt);t=!!window._ejsas?.[n]}return t&&Ge("NgEventReplay"),t}}];return e.push({provide:an,useValue:()=>{let t=g(Se),{injector:n}=t;if(!vs.has(t)){let r=g(Vp);if(bg(n)){dT();let o=n.get(Vt),i=uT(o,(s,a,c)=>{s.nodeType===Node.ELEMENT_NODE&&(iT(s,a,c),sT(s,r))});t.onDestroy(i)}}},multi:!0},{provide:Gn,useFactory:()=>{let t=g(Se),{injector:n}=t;return()=>{!bg(n)||vs.has(t)||(vs.add(t),t.onDestroy(()=>{vs.delete(t);{let r=n.get(Vt);Nu(r)}}),t.whenStable().then(()=>{if(t.destroyed)return;let r=n.get(cT);_N(r,n);let o=n.get(Vp);o.get(Cg)?.forEach(aT),o.delete(Cg);let i=r.instance;wT(n)?t.onDestroy(()=>i.cleanUp()):i.cleanUp()}))}},multi:!0}),e}var _N=(e,t)=>{let n=t.get(Vt),r=window._ejsas[n],o=e.instance=new yp(new cs(r.c));for(let a of r.et)o.addEvent(a);for(let a of r.etc)o.addEvent(a);let i=Dp(n);o.replayEarlyEventInfos(i),Nu(n);let s=new us(a=>{NN(t,a,a.currentTarget)});vp(o,s)};function NN(e,t,n){let r=(n&&n.getAttribute(Ia))??"";/d\d+/.test(r)?RN(r,e,t,n):t.eventPhase===_u.REPLAY&&nv(t,n)}function RN(e,t,n,r){bs.push({event:n,currentTarget:r}),J_(t,e,AN)}function AN(e){let t=[...bs],n=new Set(e);bs=[];for(let{event:r,currentTarget:o}of t){let i=o.getAttribute(Ia);n.has(i)?nv(r,o):bs.push({event:r,currentTarget:o})}}var Tg=!1;function xN(){Tg||(Tg=!0,vT(),x0(),Y0(),F0(),j_(),KM(),bM(),ES())}function ON(e){return e.whenStable()}function uD(){let e=[{provide:ds,useFactory:()=>{let t=!0;return t=!!g(kr,{optional:!0})?.get(ov,null),t&&Ge("NgHydration"),t}},{provide:an,useValue:()=>{vM(!1),g(ds)&&(TT(ya()),xN())},multi:!0}];return e.push({provide:Qm,useFactory:()=>g(ds)},{provide:Gn,useFactory:()=>{if(g(ds)){let t=g(Se);return()=>{ON(t).then(()=>{t.destroyed||Jv(t)})}}return()=>{}},multi:!0}),Un(e)}function Bo(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function PN(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function kN(e){return tu(e)}function FN(e,t){return _i(e,t?.equal)}var nd=class{[Me];constructor(t){this[Me]=t}destroy(){this[Me].destroy()}};function LN(e,t){!t?.injector&&la(LN);let n=t?.injector??g(ye),r=t?.manualCleanup!==!0?n.get(Zt):null,o,i=n.get(xd,null,{optional:!0}),s=n.get(un);return i!==null&&!t?.forceRoot?(o=UN(i.view,s,e),r instanceof Ls&&r._lView===i.view&&(r=null)):o=BN(e,n.get(Cy),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new nd(o)}var lD=U(E({},tr),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:Mo,run(){if(this.dirty=!1,this.hasRun&&!Ti(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=ao(this),n=xs(!1);try{this.maybeCleanup(),this.fn(e)}finally{xs(n),bi(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),jN=U(E({},lD),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){co(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),VN=U(E({},lD),{consumerMarkedDirty(){this.view[N]|=8192,Rr(this.view),this.notifier.notify(13)},destroy(){co(this),this.onDestroyFn(),this.maybeCleanup(),this.view[xn]?.delete(this)}});function UN(e,t,n){let r=Object.create(VN);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[xn]??=new Set,e[xn].add(r),r.consumerMarkedDirty(r),r}function BN(e,t,n){let r=Object.create(jN);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function xU(e,t){let n=Ut(e),r=t.elementInjector||ua();return new jn(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function dD(e){let t=Ut(e);if(!t)return null;let n=new jn(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var re=new y("");var pD=null;function Mt(){return pD}function gf(e){pD??=e}var $o=class{},Ho=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(gD),providedIn:"platform"})}return e})(),mf=new y(""),gD=(()=>{class e extends Ho{_location;_history;_doc=g(re);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Mt().getBaseHref(this._doc)}onPopState(n){let r=Mt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Mt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Va(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function fD(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function ot(e){return e&&e[0]!=="?"?`?${e}`:e}var it=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(Ba),providedIn:"root"})}return e})(),Ua=new y(""),Ba=(()=>{class e extends it{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??g(re).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Va(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+ot(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+ot(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+ot(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(I(Ho),I(Ua,8))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),pn=(()=>{class e{_subject=new J;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=zN(fD(hD(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+ot(r))}normalize(n){return e.stripTrailingSlash(HN(this._basePath,hD(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+ot(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+ot(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=ot;static joinWithSlash=Va;static stripTrailingSlash=fD;static \u0275fac=function(r){return new(r||e)(I(it))};static \u0275prov=w({token:e,factory:()=>$N(),providedIn:"root"})}return e})();function $N(){return new pn(I(it))}function HN(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function hD(e){return e.replace(/\/index.html$/,"")}function zN(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Cf=(()=>{class e extends it{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Va(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+ot(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+ot(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(I(Ho),I(Ua,8))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),ID={ADP:[void 0,void 0,0],AFN:[void 0,"\u060B",0],ALL:[void 0,void 0,0],AMD:[void 0,"\u058F",2],AOA:[void 0,"Kz"],ARS:[void 0,"$"],AUD:["A$","$"],AZN:[void 0,"\u20BC"],BAM:[void 0,"KM"],BBD:[void 0,"$"],BDT:[void 0,"\u09F3"],BHD:[void 0,void 0,3],BIF:[void 0,void 0,0],BMD:[void 0,"$"],BND:[void 0,"$"],BOB:[void 0,"Bs"],BRL:["R$"],BSD:[void 0,"$"],BWP:[void 0,"P"],BYN:[void 0,void 0,2],BYR:[void 0,void 0,0],BZD:[void 0,"$"],CAD:["CA$","$",2],CHF:[void 0,void 0,2],CLF:[void 0,void 0,4],CLP:[void 0,"$",0],CNY:["CN\xA5","\xA5"],COP:[void 0,"$",2],CRC:[void 0,"\u20A1",2],CUC:[void 0,"$"],CUP:[void 0,"$"],CZK:[void 0,"K\u010D",2],DJF:[void 0,void 0,0],DKK:[void 0,"kr",2],DOP:[void 0,"$"],EGP:[void 0,"E\xA3"],ESP:[void 0,"\u20A7",0],EUR:["\u20AC"],FJD:[void 0,"$"],FKP:[void 0,"\xA3"],GBP:["\xA3"],GEL:[void 0,"\u20BE"],GHS:[void 0,"GH\u20B5"],GIP:[void 0,"\xA3"],GNF:[void 0,"FG",0],GTQ:[void 0,"Q"],GYD:[void 0,"$",2],HKD:["HK$","$"],HNL:[void 0,"L"],HRK:[void 0,"kn"],HUF:[void 0,"Ft",2],IDR:[void 0,"Rp",2],ILS:["\u20AA"],INR:["\u20B9"],IQD:[void 0,void 0,0],IRR:[void 0,void 0,0],ISK:[void 0,"kr",0],ITL:[void 0,void 0,0],JMD:[void 0,"$"],JOD:[void 0,void 0,3],JPY:["\xA5",void 0,0],KHR:[void 0,"\u17DB"],KMF:[void 0,"CF",0],KPW:[void 0,"\u20A9",0],KRW:["\u20A9",void 0,0],KWD:[void 0,void 0,3],KYD:[void 0,"$"],KZT:[void 0,"\u20B8"],LAK:[void 0,"\u20AD",0],LBP:[void 0,"L\xA3",0],LKR:[void 0,"Rs"],LRD:[void 0,"$"],LTL:[void 0,"Lt"],LUF:[void 0,void 0,0],LVL:[void 0,"Ls"],LYD:[void 0,void 0,3],MGA:[void 0,"Ar",0],MGF:[void 0,void 0,0],MMK:[void 0,"K",0],MNT:[void 0,"\u20AE",2],MRO:[void 0,void 0,0],MUR:[void 0,"Rs",2],MXN:["MX$","$"],MYR:[void 0,"RM"],NAD:[void 0,"$"],NGN:[void 0,"\u20A6"],NIO:[void 0,"C$"],NOK:[void 0,"kr",2],NPR:[void 0,"Rs"],NZD:["NZ$","$"],OMR:[void 0,void 0,3],PHP:["\u20B1"],PKR:[void 0,"Rs",2],PLN:[void 0,"z\u0142"],PYG:[void 0,"\u20B2",0],RON:[void 0,"lei"],RSD:[void 0,void 0,0],RUB:[void 0,"\u20BD"],RWF:[void 0,"RF",0],SBD:[void 0,"$"],SEK:[void 0,"kr",2],SGD:[void 0,"$"],SHP:[void 0,"\xA3"],SLE:[void 0,void 0,2],SLL:[void 0,void 0,0],SOS:[void 0,void 0,0],SRD:[void 0,"$"],SSP:[void 0,"\xA3"],STD:[void 0,void 0,0],STN:[void 0,"Db"],SYP:[void 0,"\xA3",0],THB:[void 0,"\u0E3F"],TMM:[void 0,void 0,0],TND:[void 0,void 0,3],TOP:[void 0,"T$"],TRL:[void 0,void 0,0],TRY:[void 0,"\u20BA"],TTD:[void 0,"$"],TWD:["NT$","$",2],TZS:[void 0,void 0,2],UAH:[void 0,"\u20B4"],UGX:[void 0,void 0,0],USD:["$"],UYI:[void 0,void 0,0],UYU:[void 0,"$"],UYW:[void 0,void 0,4],UZS:[void 0,void 0,2],VEF:[void 0,"Bs",2],VND:["\u20AB",void 0,0],VUV:[void 0,void 0,0],XAF:["FCFA",void 0,0],XCD:["EC$","$"],XOF:["F\u202FCFA",void 0,0],XPF:["CFPF",void 0,0],XXX:["\xA4"],YER:[void 0,void 0,0],ZAR:[void 0,"R"],ZMK:[void 0,void 0,0],ZMW:[void 0,"ZK"],ZWD:[void 0,void 0,0]},bf=function(e){return e[e.Decimal=0]="Decimal",e[e.Percent=1]="Percent",e[e.Currency=2]="Currency",e[e.Scientific=3]="Scientific",e}(bf||{});var we=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(we||{}),Z=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(Z||{}),Pe=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Pe||{}),ke={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function CD(e){return Oe(e)[ee.LocaleId]}function bD(e,t,n){let r=Oe(e),o=[r[ee.DayPeriodsFormat],r[ee.DayPeriodsStandalone]],i=We(o,t);return We(i,n)}function TD(e,t,n){let r=Oe(e),o=[r[ee.DaysFormat],r[ee.DaysStandalone]],i=We(o,t);return We(i,n)}function SD(e,t,n){let r=Oe(e),o=[r[ee.MonthsFormat],r[ee.MonthsStandalone]],i=We(o,t);return We(i,n)}function MD(e,t){let r=Oe(e)[ee.Eras];return We(r,t)}function zo(e,t){let n=Oe(e);return We(n[ee.DateFormat],t)}function qo(e,t){let n=Oe(e);return We(n[ee.TimeFormat],t)}function Go(e,t){let r=Oe(e)[ee.DateTimeFormat];return We(r,t)}function _t(e,t){let n=Oe(e),r=n[ee.NumberSymbols][t];if(typeof r>"u"){if(t===ke.CurrencyDecimal)return n[ee.NumberSymbols][ke.Decimal];if(t===ke.CurrencyGroup)return n[ee.NumberSymbols][ke.Group]}return r}function _D(e,t){return Oe(e)[ee.NumberFormats][t]}function qN(e){return Oe(e)[ee.Currencies]}function ND(e){if(!e[ee.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[ee.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function RD(e){let t=Oe(e);return ND(t),(t[ee.ExtraData][2]||[]).map(r=>typeof r=="string"?vf(r):[vf(r[0]),vf(r[1])])}function AD(e,t,n){let r=Oe(e);ND(r);let o=[r[ee.ExtraData][0],r[ee.ExtraData][1]],i=We(o,t)||[];return We(i,n)||[]}function We(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function vf(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}function xD(e,t,n="en"){let r=qN(n)[e]||ID[e]||[],o=r[1];return t==="narrow"&&typeof o=="string"?o:r[0]||e}var GN=2;function OD(e){let t,n=ID[e];return n&&(t=n[2]),typeof t=="number"?t:GN}var WN=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,$a={},ZN=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function PD(e,t,n,r){let o=rR(e);t=Kt(n,t)||t;let s=[],a;for(;t;)if(a=ZN.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let c=o.getTimezoneOffset();r&&(c=FD(r,c),o=nR(o,r));let u="";return s.forEach(l=>{let d=eR(l);u+=d?d(o,n,c):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}function Wa(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Kt(e,t){let n=CD(e);if($a[n]??={},$a[n][t])return $a[n][t];let r="";switch(t){case"shortDate":r=zo(e,Pe.Short);break;case"mediumDate":r=zo(e,Pe.Medium);break;case"longDate":r=zo(e,Pe.Long);break;case"fullDate":r=zo(e,Pe.Full);break;case"shortTime":r=qo(e,Pe.Short);break;case"mediumTime":r=qo(e,Pe.Medium);break;case"longTime":r=qo(e,Pe.Long);break;case"fullTime":r=qo(e,Pe.Full);break;case"short":let o=Kt(e,"shortTime"),i=Kt(e,"shortDate");r=Ha(Go(e,Pe.Short),[o,i]);break;case"medium":let s=Kt(e,"mediumTime"),a=Kt(e,"mediumDate");r=Ha(Go(e,Pe.Medium),[s,a]);break;case"long":let c=Kt(e,"longTime"),u=Kt(e,"longDate");r=Ha(Go(e,Pe.Long),[c,u]);break;case"full":let l=Kt(e,"fullTime"),d=Kt(e,"fullDate");r=Ha(Go(e,Pe.Full),[l,d]);break}return r&&($a[n][t]=r),r}function Ha(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function st(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function YN(e,t){return st(e,3).substring(0,t)}function ie(e,t,n=0,r=!1,o=!1){return function(i,s){let a=QN(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return YN(a,t);let c=_t(s,ke.MinusSign);return st(a,t,c,r,o)}}function QN(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function K(e,t,n=we.Format,r=!1){return function(o,i){return KN(o,i,e,t,n,r)}}function KN(e,t,n,r,o,i){switch(n){case 2:return SD(t,o,r)[e.getMonth()];case 1:return TD(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let u=RD(t),l=AD(t,o,r),d=u.findIndex(h=>{if(Array.isArray(h)){let[f,p]=h,m=s>=f.hours&&a>=f.minutes,D=s<p.hours||s===p.hours&&a<p.minutes;if(f.hours<p.hours){if(m&&D)return!0}else if(m||D)return!0}else if(h.hours===s&&h.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return bD(t,o,r)[s<12?0:1];case 3:return MD(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new Error(`unexpected translation type ${c}`)}}function za(e){return function(t,n,r){let o=-1*r,i=_t(n,ke.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+st(s,2,i)+st(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+st(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+st(s,2,i)+":"+st(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+st(s,2,i)+":"+st(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var XN=0,Ga=4;function JN(e){let t=Wa(e,XN,1).getDay();return Wa(e,0,1+(t<=Ga?Ga:Ga+7)-t)}function kD(e){let t=e.getDay(),n=t===0?-3:Ga-t;return Wa(e.getFullYear(),e.getMonth(),e.getDate()+n)}function yf(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=kD(n),s=JN(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return st(o,e,_t(r,ke.MinusSign))}}function qa(e,t=!1){return function(n,r){let i=kD(n).getFullYear();return st(i,e,_t(r,ke.MinusSign),t)}}var Df={};function eR(e){if(Df[e])return Df[e];let t;switch(e){case"G":case"GG":case"GGG":t=K(3,Z.Abbreviated);break;case"GGGG":t=K(3,Z.Wide);break;case"GGGGG":t=K(3,Z.Narrow);break;case"y":t=ie(0,1,0,!1,!0);break;case"yy":t=ie(0,2,0,!0,!0);break;case"yyy":t=ie(0,3,0,!1,!0);break;case"yyyy":t=ie(0,4,0,!1,!0);break;case"Y":t=qa(1);break;case"YY":t=qa(2,!0);break;case"YYY":t=qa(3);break;case"YYYY":t=qa(4);break;case"M":case"L":t=ie(1,1,1);break;case"MM":case"LL":t=ie(1,2,1);break;case"MMM":t=K(2,Z.Abbreviated);break;case"MMMM":t=K(2,Z.Wide);break;case"MMMMM":t=K(2,Z.Narrow);break;case"LLL":t=K(2,Z.Abbreviated,we.Standalone);break;case"LLLL":t=K(2,Z.Wide,we.Standalone);break;case"LLLLL":t=K(2,Z.Narrow,we.Standalone);break;case"w":t=yf(1);break;case"ww":t=yf(2);break;case"W":t=yf(1,!0);break;case"d":t=ie(2,1);break;case"dd":t=ie(2,2);break;case"c":case"cc":t=ie(7,1);break;case"ccc":t=K(1,Z.Abbreviated,we.Standalone);break;case"cccc":t=K(1,Z.Wide,we.Standalone);break;case"ccccc":t=K(1,Z.Narrow,we.Standalone);break;case"cccccc":t=K(1,Z.Short,we.Standalone);break;case"E":case"EE":case"EEE":t=K(1,Z.Abbreviated);break;case"EEEE":t=K(1,Z.Wide);break;case"EEEEE":t=K(1,Z.Narrow);break;case"EEEEEE":t=K(1,Z.Short);break;case"a":case"aa":case"aaa":t=K(0,Z.Abbreviated);break;case"aaaa":t=K(0,Z.Wide);break;case"aaaaa":t=K(0,Z.Narrow);break;case"b":case"bb":case"bbb":t=K(0,Z.Abbreviated,we.Standalone,!0);break;case"bbbb":t=K(0,Z.Wide,we.Standalone,!0);break;case"bbbbb":t=K(0,Z.Narrow,we.Standalone,!0);break;case"B":case"BB":case"BBB":t=K(0,Z.Abbreviated,we.Format,!0);break;case"BBBB":t=K(0,Z.Wide,we.Format,!0);break;case"BBBBB":t=K(0,Z.Narrow,we.Format,!0);break;case"h":t=ie(3,1,-12);break;case"hh":t=ie(3,2,-12);break;case"H":t=ie(3,1);break;case"HH":t=ie(3,2);break;case"m":t=ie(4,1);break;case"mm":t=ie(4,2);break;case"s":t=ie(5,1);break;case"ss":t=ie(5,2);break;case"S":t=ie(6,1);break;case"SS":t=ie(6,2);break;case"SSS":t=ie(6,3);break;case"Z":case"ZZ":case"ZZZ":t=za(0);break;case"ZZZZZ":t=za(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=za(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=za(2);break;default:return null}return Df[e]=t,t}function FD(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function tR(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function nR(e,t,n){let o=e.getTimezoneOffset(),i=FD(t,o);return tR(e,-1*(i-o))}function rR(e){if(mD(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return Wa(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(WN))return oR(r)}let t=new Date(e);if(!mD(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function oR(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,c=Number(e[6]||0),u=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,c,u),t}function mD(e){return e instanceof Date&&!isNaN(e.valueOf())}var iR=/^(\d+)?\.((\d+)(-(\d+))?)?$/,vD=22,Za=".",Wo="0",sR=";",aR=",",Ef="#",yD="\xA4";function cR(e,t,n,r,o,i,s=!1){let a="",c=!1;if(!isFinite(e))a=_t(n,ke.Infinity);else{let u=dR(e);s&&(u=lR(u));let l=t.minInt,d=t.minFrac,h=t.maxFrac;if(i){let H=i.match(iR);if(H===null)throw new Error(`${i} is not a valid digit info`);let V=H[1],He=H[3],er=H[5];V!=null&&(l=wf(V)),He!=null&&(d=wf(He)),er!=null?h=wf(er):He!=null&&d>h&&(h=d)}fR(u,d,h);let f=u.digits,p=u.integerLen,m=u.exponent,D=[];for(c=f.every(H=>!H);p<l;p++)f.unshift(0);for(;p<0;p++)f.unshift(0);p>0?D=f.splice(p,f.length):(D=f,f=[0]);let b=[];for(f.length>=t.lgSize&&b.unshift(f.splice(-t.lgSize,f.length).join(""));f.length>t.gSize;)b.unshift(f.splice(-t.gSize,f.length).join(""));f.length&&b.unshift(f.join("")),a=b.join(_t(n,r)),D.length&&(a+=_t(n,o)+D.join("")),m&&(a+=_t(n,ke.Exponential)+"+"+m)}return e<0&&!c?a=t.negPre+a+t.negSuf:a=t.posPre+a+t.posSuf,a}function LD(e,t,n,r,o){let i=_D(t,bf.Currency),s=uR(i,_t(t,ke.MinusSign));return s.minFrac=OD(r),s.maxFrac=s.minFrac,cR(e,s,t,ke.CurrencyGroup,ke.CurrencyDecimal,o).replace(yD,n).replace(yD,"").trim()}function uR(e,t="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=e.split(sR),o=r[0],i=r[1],s=o.indexOf(Za)!==-1?o.split(Za):[o.substring(0,o.lastIndexOf(Wo)+1),o.substring(o.lastIndexOf(Wo)+1)],a=s[0],c=s[1]||"";n.posPre=a.substring(0,a.indexOf(Ef));for(let l=0;l<c.length;l++){let d=c.charAt(l);d===Wo?n.minFrac=n.maxFrac=l+1:d===Ef?n.maxFrac=l+1:n.posSuf+=d}let u=a.split(aR);if(n.gSize=u[1]?u[1].length:0,n.lgSize=u[2]||u[1]?(u[2]||u[1]).length:0,i){let l=o.length-n.posPre.length-n.posSuf.length,d=i.indexOf(Ef);n.negPre=i.substring(0,d).replace(/'/g,""),n.negSuf=i.slice(d+l).replace(/'/g,"")}else n.negPre=t+n.posPre,n.negSuf=n.posSuf;return n}function lR(e){if(e.digits[0]===0)return e;let t=e.digits.length-e.integerLen;return e.exponent?e.exponent+=2:(t===0?e.digits.push(0,0):t===1&&e.digits.push(0),e.integerLen+=2),e}function dR(e){let t=Math.abs(e)+"",n=0,r,o,i,s,a;for((o=t.indexOf(Za))>-1&&(t=t.replace(Za,"")),(i=t.search(/e/i))>0?(o<0&&(o=i),o+=+t.slice(i+1),t=t.substring(0,i)):o<0&&(o=t.length),i=0;t.charAt(i)===Wo;i++);if(i===(a=t.length))r=[0],o=1;else{for(a--;t.charAt(a)===Wo;)a--;for(o-=i,r=[],s=0;i<=a;i++,s++)r[s]=Number(t.charAt(i))}return o>vD&&(r=r.splice(0,vD-1),n=o-1,o=1),{digits:r,exponent:n,integerLen:o}}function fR(e,t,n){if(t>n)throw new Error(`The minimum number of digits after fraction (${t}) is higher than the maximum (${n}).`);let r=e.digits,o=r.length-e.integerLen,i=Math.min(Math.max(t,o),n),s=i+e.integerLen,a=r[s];if(s>0){r.splice(Math.max(e.integerLen,s));for(let d=s;d<r.length;d++)r[d]=0}else{o=Math.max(0,o),e.integerLen=1,r.length=Math.max(1,s=i+1),r[0]=0;for(let d=1;d<s;d++)r[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)r.unshift(0),e.integerLen++;r.unshift(1),e.integerLen++}else r[s-1]++;for(;o<Math.max(0,i);o++)r.push(0);let c=i!==0,u=t+e.integerLen,l=r.reduceRight(function(d,h,f,p){return h=h+d,p[f]=h<10?h:h-10,c&&(p[f]===0&&f>=u?p.pop():c=!1),h>=10?1:0},0);l&&(r.unshift(l),e.integerLen++)}function wf(e){let t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}var If=/\s+/,DD=[],hR=(()=>{class e{_ngEl;_renderer;initialClasses=DD;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(If):DD}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(If):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(If).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)($(Ct),$(Ur))};static \u0275dir=St({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Ya=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},jD=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Ya(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),ED(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);ED(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)($(Qt),$(Ln),$(pf))};static \u0275dir=St({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function ED(e,t){e.context.$implicit=t.item}var pR=(()=>{class e{_viewContainer;_context=new Qa;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){wD(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){wD(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)($(Qt),$(Ln))};static \u0275dir=St({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Qa=class{$implicit=null;ngIf=null};function wD(e,t){if(e&&!e.createEmbeddedView)throw new v(2020,!1)}var gR=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)($(Qt))};static \u0275dir=St({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[Hn]})}return e})();function VD(e,t){return new v(2100,!1)}var mR="mediumDate",UD=new y(""),BD=new y(""),vR=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??mR,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return PD(n,s,i||this.locale,a)}catch(s){throw VD(e,s.message)}}static \u0275fac=function(r){return new(r||e)($(Uo,16),$(UD,24),$(BD,24))};static \u0275pipe=rf({name:"date",type:e,pure:!0})}return e})();var yR=(()=>{class e{_locale;_defaultCurrencyCode;constructor(n,r="USD"){this._locale=n,this._defaultCurrencyCode=r}transform(n,r=this._defaultCurrencyCode,o="symbol",i,s){if(!DR(n))return null;s||=this._locale,typeof o=="boolean"&&(o=o?"symbol":"code");let a=r||this._defaultCurrencyCode;o!=="code"&&(o==="symbol"||o==="symbol-narrow"?a=xD(a,o==="symbol"?"wide":"narrow",s):a=o);try{let c=ER(n);return LD(c,s,a,r,i)}catch(c){throw VD(e,c.message)}}static \u0275fac=function(r){return new(r||e)($(Uo,16),$(nD,16))};static \u0275pipe=rf({name:"currency",type:e,pure:!0})}return e})();function DR(e){return!(e==null||e===""||e!==e)}function ER(e){if(typeof e=="string"&&!isNaN(Number(e)-parseFloat(e)))return Number(e);if(typeof e!="number")throw new Error(`${e} is not a number`);return e}var Tf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Tt({type:e});static \u0275inj=wt({})}return e})();function Zo(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Ka="browser",$D="server";function wR(e){return e===Ka}function Xa(e){return e===$D}var Wn=class{};var HD=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>new Sf(g(re),window)})}return e})(),Sf=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=IR(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function IR(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var tc=new y(""),Rf=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new v(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(I(tc),I(q))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Yo=class{_doc;constructor(t){this._doc=t}manager},Ja="ng-app-id";function zD(e){for(let t of e)t.remove()}function qD(e,t){let n=t.createElement("style");return n.textContent=e,n}function bR(e,t,n,r){let o=e.head?.querySelectorAll(`style[${Ja}="${t}"],link[${Ja}="${t}"]`);if(o)for(let i of o)i.removeAttribute(Ja),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function _f(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Af=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Xa(i),bR(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,qD);r?.forEach(o=>this.addUsage(o,this.external,_f))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(zD(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])zD(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,qD(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,_f(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(Ja,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(I(re),I(Vt),I(Nd,8),I(Pr))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Mf={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},xf=/%COMP%/g;var WD="%COMP%",TR=`_nghost-${WD}`,SR=`_ngcontent-${WD}`,MR=!0,_R=new y("",{providedIn:"root",factory:()=>MR});function NR(e){return SR.replace(xf,e)}function RR(e){return TR.replace(xf,e)}function ZD(e,t){return t.map(n=>n.replace(xf,e))}var Of=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=Xa(a),this.defaultRenderer=new Qo(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Et.ShadowDom&&(r=U(E({},r),{encapsulation:Et.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof ec?o.applyToHost(n):o instanceof Ko&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case Et.Emulated:i=new ec(c,u,r,this.appId,l,s,a,d,h);break;case Et.ShadowDom:return new Nf(c,u,n,r,s,a,this.nonce,d,h);default:i=new Ko(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(I(Rf),I(Af),I(Vt),I(_R),I(re),I(Pr),I(q),I(Nd),I(Fr,8))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Qo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Mf[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(GD(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(GD(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new v(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Mf[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Mf[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(Gt.DashCase|Gt.Important)?t.style.setProperty(n,r,o&Gt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&Gt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=Mt().getGlobalEventTarget(this.doc,t),!t))throw new v(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function GD(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Nf=class extends Qo{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=ZD(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=_f(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Ko=class extends Qo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?ZD(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},ec=class extends Ko{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=NR(l),this.hostAttr=RR(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var nc=class e extends $o{supportsDOMEvents=!0;static makeCurrent(){gf(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=AR();return n==null?null:xR(n)}resetBaseElement(){Xo=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Zo(document.cookie,t)}},Xo=null;function AR(){return Xo=Xo||document.head.querySelector("base"),Xo?Xo.getAttribute("href"):null}function xR(e){return new URL(e,document.baseURI).pathname}var rc=class{addToWindow(t){fe.getAngularTestability=(r,o=!0)=>{let i=t.findTestabilityInTree(r,o);if(i==null)throw new v(5103,!1);return i},fe.getAllAngularTestabilities=()=>t.getAllTestabilities(),fe.getAllAngularRootElements=()=>t.getAllRootElements();let n=r=>{let o=fe.getAllAngularTestabilities(),i=o.length,s=function(){i--,i==0&&r()};o.forEach(a=>{a.whenStable(s)})};fe.frameworkStabilizers||(fe.frameworkStabilizers=[]),fe.frameworkStabilizers.push(n)}findTestabilityInTree(t,n,r){if(n==null)return null;let o=t.getTestability(n);return o??(r?Mt().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},OR=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),QD=(()=>{class e extends Yo{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(I(re))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),YD=["alt","control","meta","shift"],PR={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},kR={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},KD=(()=>{class e extends Yo{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Mt().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),YD.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=PR[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),YD.forEach(s=>{if(s!==o){let a=kR[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(I(re))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function FR(){nc.makeCurrent()}function LR(){return new Dt}function jR(){return zm(document),document}var VR=[{provide:Pr,useValue:Ka},{provide:_d,useValue:FR,multi:!0},{provide:re,useFactory:jR}],UR=hf(sD,"browser",VR);var BR=[{provide:jo,useClass:rc},{provide:af,useClass:La,deps:[q,ja,jo]},{provide:La,useClass:La,deps:[q,ja,jo]}],$R=[{provide:ca,useValue:"root"},{provide:Dt,useFactory:LR},{provide:tc,useClass:QD,multi:!0,deps:[re]},{provide:tc,useClass:KD,multi:!0,deps:[re]},Of,Af,Rf,{provide:Sr,useExisting:Of},{provide:Wn,useClass:OR},[]],HR=(()=>{class e{constructor(){}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Tt({type:e});static \u0275inj=wt({providers:[...$R,...BR],imports:[Tf,aD]})}return e})();var zr=class{},qr=class{},at=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var sc=class{encodeKey(t){return XD(t)}encodeValue(t){return XD(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function zR(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var qR=/%(\d[a-f0-9])/gi,GR={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function XD(e){return encodeURIComponent(e).replace(qR,(t,n)=>GR[n]??t)}function oc(e){return`${e}`}var Jt=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new sc,t.fromString){if(t.fromObject)throw new v(2805,!1);this.map=zR(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(oc):[oc(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(oc(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(oc(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var ac=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function WR(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function JD(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function eE(e){return typeof Blob<"u"&&e instanceof Blob}function tE(e){return typeof FormData<"u"&&e instanceof FormData}function ZR(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Jo="Content-Type",cc="Accept",jf="X-Request-URL",oE="text/plain",iE="application/json",sE=`${iE}, ${oE}, */*`,Hr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(WR(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new at,this.context??=new ac,!this.params)this.params=new Jt,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||JD(this.body)||eE(this.body)||tE(this.body)||ZR(this.body)?this.body:this.body instanceof Jt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||tE(this.body)?null:eE(this.body)?this.body.type||null:JD(this.body)?null:typeof this.body=="string"?oE:this.body instanceof Jt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?iE:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,u=t.headers||this.headers,l=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((h,f)=>h.set(f,t.setHeaders[f]),u)),t.setParams&&(l=Object.keys(t.setParams).reduce((h,f)=>h.set(f,t.setParams[f]),l)),new e(n,r,s,{params:l,headers:u,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},en=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(en||{}),Gr=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new at,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},ei=class e extends Gr{constructor(t={}){super(t)}type=en.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},gn=class e extends Gr{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=en.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Xt=class extends Gr{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},aE=200,YR=204;function Pf(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var cE=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Hr)i=n;else{let c;o.headers instanceof at?c=o.headers:c=new at(o.headers);let u;o.params&&(o.params instanceof Jt?u=o.params:u=new Jt({fromObject:o.params})),i=new Hr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=C(i).pipe(ht(c=>this.handler.handle(c)));if(n instanceof Hr||o.observe==="events")return s;let a=s.pipe(de(c=>c instanceof gn));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(F(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new v(2806,!1);return c.body}));case"blob":return a.pipe(F(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new v(2807,!1);return c.body}));case"text":return a.pipe(F(c=>{if(c.body!==null&&typeof c.body!="string")throw new v(2808,!1);return c.body}));case"json":default:return a.pipe(F(c=>c.body))}case"response":return a;default:throw new v(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new Jt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Pf(o,r))}post(n,r,o={}){return this.request("POST",n,Pf(o,r))}put(n,r,o={}){return this.request("PUT",n,Pf(o,r))}static \u0275fac=function(r){return new(r||e)(I(zr))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),QR=/^\)\]\}',?\n/;function nE(e){if(e.url)return e.url;let t=jf.toLocaleLowerCase();return e.headers.get(t)}var uE=new y(""),ic=(()=>{class e{fetchImpl=g(kf,{optional:!0})?.fetch??((...n)=>globalThis.fetch(...n));ngZone=g(q);destroyRef=g(Zt);destroyed=!1;constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0})}handle(n){return new L(r=>{let o=new AbortController;return this.doRequest(n,o.signal,r).then(Ff,i=>r.error(new Xt({error:i}))),()=>o.abort()})}doRequest(n,r,o){return ut(this,null,function*(){let i=this.createRequestInit(n),s;try{let f=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,E({signal:r},i)));KR(f),o.next({type:en.Sent}),s=yield f}catch(f){o.error(new Xt({error:f,status:f.status??0,statusText:f.statusText,url:n.urlWithParams,headers:f.headers}));return}let a=new at(s.headers),c=s.statusText,u=nE(s)??n.urlWithParams,l=s.status,d=null;if(n.reportProgress&&o.next(new ei({headers:a,status:l,statusText:c,url:u})),s.body){let f=s.headers.get("content-length"),p=[],m=s.body.getReader(),D=0,b,H,V=typeof Zone<"u"&&Zone.current,He=!1;if(yield this.ngZone.runOutsideAngular(()=>ut(this,null,function*(){for(;;){if(this.destroyed){yield m.cancel(),He=!0;break}let{done:wn,value:$c}=yield m.read();if(wn)break;if(p.push($c),D+=$c.length,n.reportProgress){H=n.responseType==="text"?(H??"")+(b??=new TextDecoder).decode($c,{stream:!0}):void 0;let yh=()=>o.next({type:en.DownloadProgress,total:f?+f:void 0,loaded:D,partialText:H});V?V.run(yh):yh()}}})),He){o.complete();return}let er=this.concatChunks(p,D);try{let wn=s.headers.get(Jo)??"";d=this.parseBody(n,er,wn)}catch(wn){o.error(new Xt({error:wn,headers:new at(s.headers),status:s.status,statusText:s.statusText,url:nE(s)??n.urlWithParams}));return}}l===0&&(l=d?aE:0),l>=200&&l<300?(o.next(new gn({body:d,headers:a,status:l,statusText:c,url:u})),o.complete()):o.error(new Xt({error:d,headers:a,status:l,statusText:c,url:u}))})}parseBody(n,r,o){switch(n.responseType){case"json":let i=new TextDecoder().decode(r).replace(QR,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o=n.withCredentials?"include":void 0;if(n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has(cc)||(r[cc]=sE),!n.headers.has(Jo)){let i=n.detectContentTypeHeader();i!==null&&(r[Jo]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),kf=class{};function Ff(){}function KR(e){e.then(Ff,Ff)}function lE(e,t){return t(e)}function XR(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function JR(e,t,n){return(r,o)=>Ae(n,()=>t(r,i=>e(i,o)))}var dE=new y(""),lc=new y(""),Vf=new y(""),Uf=new y("",{providedIn:"root",factory:()=>!0});function eA(){let e=null;return(t,n)=>{e===null&&(e=(g(dE,{optional:!0})??[]).reduceRight(XR,lE));let r=g(nt);if(g(Uf)){let i=r.add();return e(t,n).pipe(rn(()=>r.remove(i)))}else return e(t,n)}}var uc=(()=>{class e extends zr{backend;injector;chain=null;pendingTasks=g(nt);contributeToStability=g(Uf);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(lc),...this.injector.get(Vf,[])]));this.chain=r.reduceRight((o,i)=>JR(o,i,this.injector),lE)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(rn(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(I(qr),I(ve))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();var tA=/^\)\]\}',?\n/,nA=RegExp(`^${jf}:`,"m");function rA(e){return"responseURL"in e&&e.responseURL?e.responseURL:nA.test(e.getAllResponseHeaders())?e.getResponseHeader(jf):null}var Lf=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new v(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?Y(r.\u0275loadImpl()):C(null)).pipe(Ie(()=>new L(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((m,D)=>s.setRequestHeader(m,D.join(","))),n.headers.has(cc)||s.setRequestHeader(cc,sE),!n.headers.has(Jo)){let m=n.detectContentTypeHeader();m!==null&&s.setRequestHeader(Jo,m)}if(n.responseType){let m=n.responseType.toLowerCase();s.responseType=m!=="json"?m:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let m=s.statusText||"OK",D=new at(s.getAllResponseHeaders()),b=rA(s)||n.url;return c=new ei({headers:D,status:s.status,statusText:m,url:b}),c},l=()=>{let{headers:m,status:D,statusText:b,url:H}=u(),V=null;D!==YR&&(V=typeof s.response>"u"?s.responseText:s.response),D===0&&(D=V?aE:0);let He=D>=200&&D<300;if(n.responseType==="json"&&typeof V=="string"){let er=V;V=V.replace(tA,"");try{V=V!==""?JSON.parse(V):null}catch(wn){V=er,He&&(He=!1,V={error:wn,text:V})}}He?(i.next(new gn({body:V,headers:m,status:D,statusText:b,url:H||void 0})),i.complete()):i.error(new Xt({error:V,headers:m,status:D,statusText:b,url:H||void 0}))},d=m=>{let{url:D}=u(),b=new Xt({error:m,status:s.status||0,statusText:s.statusText||"Unknown Error",url:D||void 0});i.error(b)},h=!1,f=m=>{h||(i.next(u()),h=!0);let D={type:en.DownloadProgress,loaded:m.loaded};m.lengthComputable&&(D.total=m.total),n.responseType==="text"&&s.responseText&&(D.partialText=s.responseText),i.next(D)},p=m=>{let D={type:en.UploadProgress,loaded:m.loaded};m.lengthComputable&&(D.total=m.total),i.next(D)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",p)),s.send(a),i.next({type:en.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",p)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(I(Wn))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),fE=new y(""),oA="XSRF-TOKEN",iA=new y("",{providedIn:"root",factory:()=>oA}),sA="X-XSRF-TOKEN",aA=new y("",{providedIn:"root",factory:()=>sA}),ti=class{},cA=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Zo(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(I(re),I(iA))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function uA(e,t){let n=e.url.toLowerCase();if(!g(fE)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=g(ti).getToken(),o=g(aA);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var ni=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(ni||{});function Bf(e,t){return{\u0275kind:e,\u0275providers:t}}function hE(...e){let t=[cE,Lf,uc,{provide:zr,useExisting:uc},{provide:qr,useFactory:()=>g(uE,{optional:!0})??g(Lf)},{provide:lc,useValue:uA,multi:!0},{provide:fE,useValue:!0},{provide:ti,useClass:cA}];for(let n of e)t.push(...n.\u0275providers);return Un(t)}function lA(e){return Bf(ni.Interceptors,e.map(t=>({provide:lc,useValue:t,multi:!0})))}var rE=new y("");function pE(){return Bf(ni.LegacyInterceptors,[{provide:rE,useFactory:eA},{provide:lc,useExisting:rE,multi:!0}])}function dA(){return Bf(ni.Fetch,[ic,{provide:uE,useExisting:ic},{provide:qr,useExisting:ic}])}var fA=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Tt({type:e});static \u0275inj=wt({providers:[hE(pE())]})}return e})();var hA=new y(""),pA="b",gA="h",mA="s",vA="st",yA="u",DA="rt",$f=new y(""),EA=["GET","HEAD"];function wA(e,t){let h=g($f),{isCacheActive:n}=h,r=Ih(h,["isCacheActive"]),{transferCache:o,method:i}=e;if(!n||o===!1||i==="POST"&&!r.includePostRequests&&!o||i!=="POST"&&!EA.includes(i)||!r.includeRequestsWithAuthHeaders&&IA(e)||r.filter?.(e)===!1)return t(e);let s=g(kr);if(g(hA,{optional:!0}))throw new v(2803,!1);let c=e.url,u=CA(e,c),l=s.get(u,null),d=r.includeHeaders;if(typeof o=="object"&&o.includeHeaders&&(d=o.includeHeaders),l){let{[pA]:f,[DA]:p,[gA]:m,[mA]:D,[vA]:b,[yA]:H}=l,V=f;switch(p){case"arraybuffer":V=new TextEncoder().encode(f).buffer;break;case"blob":V=new Blob([f]);break}let He=new at(m);return C(new gn({body:V,headers:He,status:D,statusText:b,url:H}))}return t(e).pipe(oe(f=>{f instanceof gn}))}function IA(e){return e.headers.has("authorization")||e.headers.has("proxy-authorization")}function gE(e){return[...e.keys()].sort().map(t=>`${t}=${e.getAll(t)}`).join("&")}function CA(e,t){let{params:n,method:r,responseType:o}=e,i=gE(n),s=e.serializeBody();s instanceof URLSearchParams?s=gE(s):typeof s!="string"&&(s="");let a=[r,o,t,s,i].join("|"),c=bA(a);return c}function bA(e){let t=0;for(let n of e)t=Math.imul(31,t)+n.charCodeAt(0)<<0;return t+=2147483648,t.toString()}function mE(e){return[{provide:$f,useFactory:()=>(Ge("NgHttpTransferCache"),E({isCacheActive:!0},e))},{provide:Vf,useValue:wA,multi:!0},{provide:Gn,multi:!0,useFactory:()=>{let t=g(Se),n=g($f);return()=>{t.whenStable().then(()=>{n.isCacheActive=!1})}}}]}var vE=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(I(re))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var TA=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=I(SA),o},providedIn:"root"})}return e})(),SA=(()=>{class e extends TA{_doc;constructor(n){super(),this._doc=n}sanitize(n,r){if(r==null)return null;switch(n){case bt.NONE:return r;case bt.HTML:return fn(r,"HTML")?rt(r):Dv(this._doc,String(r)).toString();case bt.STYLE:return fn(r,"Style")?rt(r):r;case bt.SCRIPT:if(fn(r,"Script"))return rt(r);throw new v(5200,!1);case bt.URL:return fn(r,"URL")?rt(r):Ta(String(r));case bt.RESOURCE_URL:if(fn(r,"ResourceURL"))return rt(r);throw new v(5201,!1);default:throw new v(5202,!1)}}bypassSecurityTrustHtml(n){return uv(n)}bypassSecurityTrustStyle(n){return lv(n)}bypassSecurityTrustScript(n){return dv(n)}bypassSecurityTrustUrl(n){return fv(n)}bypassSecurityTrustResourceUrl(n){return hv(n)}static \u0275fac=function(r){return new(r||e)(I(re))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),dc=function(e){return e[e.NoHttpTransferCache=0]="NoHttpTransferCache",e[e.HttpTransferCacheOptions=1]="HttpTransferCacheOptions",e[e.I18nSupport=2]="I18nSupport",e[e.EventReplay=3]="EventReplay",e[e.IncrementalHydration=4]="IncrementalHydration",e}(dc||{});function MA(e,t=[],n={}){return{\u0275kind:e,\u0275providers:t}}function f2(){return MA(dc.EventReplay,cD())}function h2(...e){let t=[],n=new Set;for(let{\u0275providers:o,\u0275kind:i}of e)n.add(i),o.length&&t.push(o);let r=n.has(dc.HttpTransferCacheOptions);return Un([[],uD(),n.has(dc.NoHttpTransferCache)||r?[]:mE({}),t])}var O="primary",gi=Symbol("RouteTitle"),Wf=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Qn(e){return new Wf(e)}function TE(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function NA(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Nt(e[n],t[n]))return!1;return!0}function Nt(e,t){let n=e?Zf(e):void 0,r=t?Zf(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!SE(e[o],t[o]))return!1;return!0}function Zf(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function SE(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function ME(e){return e.length>0?e[e.length-1]:null}function En(e){return hu(e)?e:Vo(e)?Y(Promise.resolve(e)):C(e)}var RA={exact:NE,subset:RE},_E={exact:AA,subset:xA,ignored:()=>!0};function yE(e,t,n){return RA[n.paths](e.root,t.root,n.matrixParams)&&_E[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function AA(e,t){return Nt(e,t)}function NE(e,t,n){if(!Zn(e.segments,t.segments)||!pc(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!NE(e.children[r],t.children[r],n))return!1;return!0}function xA(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>SE(e[n],t[n]))}function RE(e,t,n){return AE(e,t,t.segments,n)}function AE(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!Zn(o,n)||t.hasChildren()||!pc(o,n,r))}else if(e.segments.length===n.length){if(!Zn(e.segments,n)||!pc(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!RE(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!Zn(e.segments,o)||!pc(e.segments,o,r)||!e.children[O]?!1:AE(e.children[O],t,i,r)}}function pc(e,t,n){return t.every((r,o)=>_E[n](e[o].parameters,r.parameters))}var At=class{root;queryParams;fragment;_queryParamMap;constructor(t=new j([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Qn(this.queryParams),this._queryParamMap}toString(){return kA.serialize(this)}},j=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return gc(this)}},mn=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Qn(this.parameters),this._parameterMap}toString(){return OE(this)}};function OA(e,t){return Zn(e,t)&&e.every((n,r)=>Nt(n.parameters,t[r].parameters))}function Zn(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function PA(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===O&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==O&&(n=n.concat(t(o,r)))}),n}var Kn=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new vn,providedIn:"root"})}return e})(),vn=class{parse(t){let n=new Qf(t);return new At(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${ri(t.root,!0)}`,r=jA(t.queryParams),o=typeof t.fragment=="string"?`#${FA(t.fragment)}`:"";return`${n}${r}${o}`}},kA=new vn;function gc(e){return e.segments.map(t=>OE(t)).join("/")}function ri(e,t){if(!e.hasChildren())return gc(e);if(t){let n=e.children[O]?ri(e.children[O],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==O&&r.push(`${o}:${ri(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=PA(e,(r,o)=>o===O?[ri(e.children[O],!1)]:[`${o}:${ri(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[O]!=null?`${gc(e)}/${n[0]}`:`${gc(e)}/(${n.join("//")})`}}function xE(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function fc(e){return xE(e).replace(/%3B/gi,";")}function FA(e){return encodeURI(e)}function Yf(e){return xE(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function mc(e){return decodeURIComponent(e)}function DE(e){return mc(e.replace(/\+/g,"%20"))}function OE(e){return`${Yf(e.path)}${LA(e.parameters)}`}function LA(e){return Object.entries(e).map(([t,n])=>`;${Yf(t)}=${Yf(n)}`).join("")}function jA(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${fc(n)}=${fc(o)}`).join("&"):`${fc(n)}=${fc(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var VA=/^[^\/()?;#]+/;function Hf(e){let t=e.match(VA);return t?t[0]:""}var UA=/^[^\/()?;=#]+/;function BA(e){let t=e.match(UA);return t?t[0]:""}var $A=/^[^=?&#]+/;function HA(e){let t=e.match($A);return t?t[0]:""}var zA=/^[^&#]+/;function qA(e){let t=e.match(zA);return t?t[0]:""}var Qf=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new j([],{}):new j([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[O]=new j(t,n)),r}parseSegment(){let t=Hf(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new v(4009,!1);return this.capture(t),new mn(mc(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=BA(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=Hf(this.remaining);o&&(r=o,this.capture(r))}t[mc(n)]=mc(r)}parseQueryParam(t){let n=HA(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=qA(this.remaining);s&&(r=s,this.capture(r))}let o=DE(n),i=DE(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Hf(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new v(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=O);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[O]:new j([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new v(4011,!1)}};function PE(e){return e.segments.length>0?new j([],{[O]:e}):e}function kE(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=kE(o);if(r===O&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new j(e.segments,t);return GA(n)}function GA(e){if(e.numberOfChildren===1&&e.children[O]){let t=e.children[O];return new j(e.segments.concat(t.segments),t.children)}return e}function yn(e){return e instanceof At}function FE(e,t,n=null,r=null){let o=LE(e);return jE(o,t,n,r)}function LE(e){let t;function n(i){let s={};for(let c of i.children){let u=n(c);s[c.outlet]=u}let a=new j(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=PE(r);return t??o}function jE(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return zf(o,o,o,n,r);let i=WA(t);if(i.toRoot())return zf(o,o,new j([],{}),n,r);let s=ZA(i,o,e),a=s.processChildren?ii(s.segmentGroup,s.index,i.commands):UE(s.segmentGroup,s.index,i.commands);return zf(o,s.segmentGroup,a,n,r)}function yc(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function ai(e){return typeof e=="object"&&e!=null&&e.outlets}function zf(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===t?s=n:s=VE(e,t,n);let a=PE(kE(s));return new At(a,i,o)}function VE(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=VE(i,t,n)}),new j(e.segments,r)}var Dc=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&yc(r[0]))throw new v(4003,!1);let o=r.find(ai);if(o&&o!==ME(r))throw new v(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function WA(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Dc(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Dc(n,t,r)}var Yr=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function ZA(e,t,n){if(e.isAbsolute)return new Yr(t,!0,0);if(!n)return new Yr(t,!1,NaN);if(n.parent===null)return new Yr(n,!0,0);let r=yc(e.commands[0])?0:1,o=n.segments.length-1+r;return YA(n,o,e.numberOfDoubleDots)}function YA(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new v(4005,!1);o=r.segments.length}return new Yr(r,!1,o-i)}function QA(e){return ai(e[0])?e[0].outlets:{[O]:e}}function UE(e,t,n){if(e??=new j([],{}),e.segments.length===0&&e.hasChildren())return ii(e,t,n);let r=KA(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new j(e.segments.slice(0,r.pathIndex),{});return i.children[O]=new j(e.segments.slice(r.pathIndex),e.children),ii(i,0,o)}else return r.match&&o.length===0?new j(e.segments,{}):r.match&&!e.hasChildren()?Kf(e,t,n):r.match?ii(e,0,o):Kf(e,t,n)}function ii(e,t,n){if(n.length===0)return new j(e.segments,{});{let r=QA(n),o={};if(Object.keys(r).some(i=>i!==O)&&e.children[O]&&e.numberOfChildren===1&&e.children[O].segments.length===0){let i=ii(e.children[O],t,n);return new j(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=UE(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new j(e.segments,o)}}function KA(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(ai(a))break;let c=`${a}`,u=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!wE(c,u,s))return i;r+=2}else{if(!wE(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Kf(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(ai(i)){let c=XA(i.outlets);return new j(r,c)}if(o===0&&yc(n[0])){let c=e.segments[t];r.push(new mn(c.path,EE(n[0]))),o++;continue}let s=ai(i)?i.outlets[O]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&yc(a)?(r.push(new mn(s,EE(a))),o+=2):(r.push(new mn(s,{})),o++)}return new j(r,{})}function XA(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=Kf(new j([],{}),0,r))}),t}function EE(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function wE(e,t,n){return e==n.path&&Nt(t,n.parameters)}var vc="imperative",le=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(le||{}),Be=class{id;url;constructor(t,n){this.id=t,this.url=n}},Dn=class extends Be{type=le.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},$e=class extends Be{urlAfterRedirects;type=le.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Fe=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(Fe||{}),Kr=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Kr||{}),Rt=class extends Be{reason;code;type=le.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},xt=class extends Be{reason;code;type=le.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},Xr=class extends Be{error;target;type=le.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},ci=class extends Be{urlAfterRedirects;state;type=le.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ec=class extends Be{urlAfterRedirects;state;type=le.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},wc=class extends Be{urlAfterRedirects;state;shouldActivate;type=le.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Ic=class extends Be{urlAfterRedirects;state;type=le.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Cc=class extends Be{urlAfterRedirects;state;type=le.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},bc=class{route;type=le.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Tc=class{route;type=le.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Sc=class{snapshot;type=le.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Mc=class{snapshot;type=le.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},_c=class{snapshot;type=le.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Nc=class{snapshot;type=le.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Jr=class{routerEvent;position;anchor;type=le.Scroll;constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},ui=class{},eo=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function JA(e,t){return e.providers&&!e._injector&&(e._injector=Br(e.providers,t,`Route: ${e.path}`)),e._injector??t}function ct(e){return e.outlet||O}function ex(e,t){let n=e.filter(r=>ct(r)===t);return n.push(...e.filter(r=>ct(r)!==t)),n}function mi(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var Rc=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return mi(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Xn(this.rootInjector)}},Xn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new Rc(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(I(ve))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ac=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Xf(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Xf(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=Jf(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return Jf(t,this._root).map(n=>n.value)}};function Xf(e,t){if(e===t.value)return t;for(let n of t.children){let r=Xf(e,n);if(r)return r}return null}function Jf(e,t){if(e===t.value)return[t];for(let n of t.children){let r=Jf(e,n);if(r.length)return r.unshift(t),r}return[]}var Ue=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function Zr(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var li=class extends Ac{snapshot;constructor(t,n){super(t),this.snapshot=n,ah(this,t)}toString(){return this.snapshot.toString()}};function BE(e){let t=tx(e),n=new pe([new mn("",{})]),r=new pe({}),o=new pe({}),i=new pe({}),s=new pe(""),a=new tn(n,r,i,s,o,O,e,t.root);return a.snapshot=t.root,new li(new Ue(a,[]),t)}function tx(e){let t={},n={},r={},o="",i=new Yn([],t,r,o,n,O,e,null,{});return new di("",new Ue(i,[]))}var tn=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(F(u=>u[gi]))??C(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(F(t=>Qn(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(F(t=>Qn(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function xc(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:E(E({},t.params),e.params),data:E(E({},t.data),e.data),resolve:E(E(E(E({},e.data),t.data),o?.data),e._resolvedData)}:r={params:E({},e.params),data:E({},e.data),resolve:E(E({},e.data),e._resolvedData??{})},o&&HE(o)&&(r.resolve[gi]=o.title),r}var Yn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[gi]}constructor(t,n,r,o,i,s,a,c,u){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Qn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Qn(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},di=class extends Ac{url;constructor(t,n){super(n),this.url=t,ah(this,n)}toString(){return $E(this._root)}};function ah(e,t){t.value._routerState=e,t.children.forEach(n=>ah(e,n))}function $E(e){let t=e.children.length>0?` { ${e.children.map($E).join(", ")} } `:"";return`${e.value}${t}`}function qf(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Nt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Nt(t.params,n.params)||e.paramsSubject.next(n.params),NA(t.url,n.url)||e.urlSubject.next(n.url),Nt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function eh(e,t){let n=Nt(e.params,t.params)&&OA(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||eh(e.parent,t.parent))}function HE(e){return typeof e.title=="string"||e.title===null}var zE=new y(""),ch=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=O;activateEvents=new be;deactivateEvents=new be;attachEvents=new be;detachEvents=new be;routerOutletData=Om(void 0);parentContexts=g(Xn);location=g(Qt);changeDetector=g($r);inputBinder=g(vi,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new v(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new v(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new v(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new v(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new th(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=St({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Hn]})}return e})(),th=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===tn?this.route:t===Xn?this.childContexts:t===zE?this.outletData:this.parent.get(t,n)}},vi=new y(""),uh=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=vo([r.queryParams,r.params,r.data]).pipe(Ie(([i,s,a],c)=>(a=E(E(E({},i),s),a),c===0?C(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=dD(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),lh=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=my({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&df(0,"router-outlet")},dependencies:[ch],encapsulation:2})}return e})();function dh(e){let t=e.children&&e.children.map(dh),n=t?U(E({},e),{children:t}):E({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==O&&(n.component=lh),n}function nx(e,t,n){let r=fi(e,t._root,n?n._root:void 0);return new li(r,t)}function fi(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=rx(e,t,n);return new Ue(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>fi(e,a)),s}}let r=ox(t.value),o=t.children.map(i=>fi(e,i));return new Ue(r,o)}}function rx(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return fi(e,r,o);return fi(e,r)})}function ox(e){return new tn(new pe(e.url),new pe(e.params),new pe(e.queryParams),new pe(e.fragment),new pe(e.data),e.outlet,e.component,e)}var to=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},qE="ngNavigationCancelingError";function Oc(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=yn(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=GE(!1,Fe.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function GE(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[qE]=!0,n.cancellationCode=t,n}function ix(e){return WE(e)&&yn(e.url)}function WE(e){return!!e&&e[qE]}var sx=(e,t,n,r)=>F(o=>(new nh(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),nh=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),qf(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=Zr(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Zr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Zr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=Zr(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Nc(i.value.snapshot))}),t.children.length&&this.forwardEvent(new Mc(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(qf(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),qf(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},Pc=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Qr=class{component;route;constructor(t,n){this.component=t,this.route=n}};function ax(e,t,n){let r=e._root,o=t?t._root:null;return oi(r,o,n,[r.value])}function cx(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function ro(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!xg(e)?e:t.get(e):r}function oi(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Zr(t);return e.children.forEach(s=>{ux(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>si(a,n.getContext(s),o)),o}function ux(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=lx(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Pc(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?oi(e,t,a?a.children:null,r,o):oi(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Qr(a.outlet.component,s))}else s&&si(t,a,o),o.canActivateChecks.push(new Pc(r)),i.component?oi(e,null,a?a.children:null,r,o):oi(e,null,n,r,o);return o}function lx(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!Zn(e.url,t.url);case"pathParamsOrQueryParamsChange":return!Zn(e.url,t.url)||!Nt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!eh(e,t)||!Nt(e.queryParams,t.queryParams);case"paramsChange":default:return!eh(e,t)}}function si(e,t,n){let r=Zr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?si(s,t.children.getContext(i),n):si(s,null,n):si(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new Qr(t.outlet.component,o)):n.canDeactivateChecks.push(new Qr(null,o)):n.canDeactivateChecks.push(new Qr(null,o))}function yi(e){return typeof e=="function"}function dx(e){return typeof e=="boolean"}function fx(e){return e&&yi(e.canLoad)}function hx(e){return e&&yi(e.canActivate)}function px(e){return e&&yi(e.canActivateChild)}function gx(e){return e&&yi(e.canDeactivate)}function mx(e){return e&&yi(e.canMatch)}function ZE(e){return e instanceof Ft||e?.name==="EmptyError"}var hc=Symbol("INITIAL_VALUE");function no(){return Ie(e=>vo(e.map(t=>t.pipe(Lt(1),yu(hc)))).pipe(F(t=>{for(let n of t)if(n!==!0){if(n===hc)return hc;if(n===!1||vx(n))return n}return!0}),de(t=>t!==hc),Lt(1)))}function vx(e){return yn(e)||e instanceof to}function yx(e,t){return ne(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?C(U(E({},n),{guardsResult:!0})):Dx(s,r,o,e).pipe(ne(a=>a&&dx(a)?Ex(r,i,e,t):C(a)),F(a=>U(E({},n),{guardsResult:a})))})}function Dx(e,t,n,r){return Y(e).pipe(ne(o=>Tx(o.component,o.route,n,t,r)),jt(o=>o!==!0,!0))}function Ex(e,t,n,r){return Y(t).pipe(ht(o=>lr(Ix(o.route.parent,r),wx(o.route,r),bx(e,o.path,n),Cx(e,o.route,n))),jt(o=>o!==!0,!0))}function wx(e,t){return e!==null&&t&&t(new _c(e)),C(!0)}function Ix(e,t){return e!==null&&t&&t(new Sc(e)),C(!0)}function Cx(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return C(!0);let o=r.map(i=>ns(()=>{let s=mi(t)??n,a=ro(i,s),c=hx(a)?a.canActivate(t,e):Ae(s,()=>a(t,e));return En(c).pipe(jt())}));return C(o).pipe(no())}function bx(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>cx(s)).filter(s=>s!==null).map(s=>ns(()=>{let a=s.guards.map(c=>{let u=mi(s.node)??n,l=ro(c,u),d=px(l)?l.canActivateChild(r,e):Ae(u,()=>l(r,e));return En(d).pipe(jt())});return C(a).pipe(no())}));return C(i).pipe(no())}function Tx(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return C(!0);let s=i.map(a=>{let c=mi(t)??o,u=ro(a,c),l=gx(u)?u.canDeactivate(e,t,n,r):Ae(c,()=>u(e,t,n,r));return En(l).pipe(jt())});return C(s).pipe(no())}function Sx(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return C(!0);let i=o.map(s=>{let a=ro(s,e),c=fx(a)?a.canLoad(t,n):Ae(e,()=>a(t,n));return En(c)});return C(i).pipe(no(),YE(r))}function YE(e){return cu(oe(t=>{if(typeof t!="boolean")throw Oc(e,t)}),F(t=>t===!0))}function Mx(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return C(!0);let i=o.map(s=>{let a=ro(s,e),c=mx(a)?a.canMatch(t,n):Ae(e,()=>a(t,n));return En(c)});return C(i).pipe(no(),YE(r))}var hi=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},pi=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function Wr(e){return ur(new hi(e))}function _x(e){return ur(new v(4e3,!1))}function Nx(e){return ur(GE(!1,Fe.GuardRejected))}var rh=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return C(r);if(o.numberOfChildren>1||!o.children[O])return _x(`${t.redirectTo}`);o=o.children[O]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:u,routeConfig:l,url:d,outlet:h,params:f,data:p,title:m}=o,D=Ae(i,()=>a({params:f,data:p,queryParams:c,fragment:u,routeConfig:l,url:d,outlet:h,title:m}));if(D instanceof At)throw new pi(D);n=D}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new pi(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new At(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new j(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new v(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},oh={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Rx(e,t,n,r,o){let i=QE(e,t,n);return i.matched?(r=JA(t,r),Mx(r,t,n,o).pipe(F(s=>s===!0?i:E({},oh)))):C(i)}function QE(e,t,n){if(t.path==="**")return Ax(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?E({},oh):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||TE)(n,e,t);if(!o)return E({},oh);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?E(E({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function Ax(e){return{matched:!0,parameters:e.length>0?ME(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function IE(e,t,n,r){return n.length>0&&Px(e,n,r)?{segmentGroup:new j(t,Ox(r,new j(n,e.children))),slicedSegments:[]}:n.length===0&&kx(e,n,r)?{segmentGroup:new j(e.segments,xx(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new j(e.segments,e.children),slicedSegments:n}}function xx(e,t,n,r){let o={};for(let i of n)if(Fc(e,t,i)&&!r[ct(i)]){let s=new j([],{});o[ct(i)]=s}return E(E({},r),o)}function Ox(e,t){let n={};n[O]=t;for(let r of e)if(r.path===""&&ct(r)!==O){let o=new j([],{});n[ct(r)]=o}return n}function Px(e,t,n){return n.some(r=>Fc(e,t,r)&&ct(r)!==O)}function kx(e,t,n){return n.some(r=>Fc(e,t,r))}function Fc(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function Fx(e,t,n){return t.length===0&&!e.children[n]}var ih=class{};function Lx(e,t,n,r,o,i,s="emptyOnly"){return new sh(e,t,n,r,o,s,i).recognize()}var jx=31,sh=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new rh(this.urlSerializer,this.urlTree)}noMatchError(t){return new v(4002,`'${t.segmentGroup}'`)}recognize(){let t=IE(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(F(({children:n,rootSnapshot:r})=>{let o=new Ue(r,n),i=new di("",o),s=FE(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Yn([],Object.freeze({}),Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),O,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,O,n).pipe(F(r=>({children:r,rootSnapshot:n})),ft(r=>{if(r instanceof pi)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof hi?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(F(s=>s instanceof Ue?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return Y(i).pipe(ht(s=>{let a=r.children[s],c=ex(n,s);return this.processSegmentGroup(t,c,a,s,o)}),gu((s,a)=>(s.push(...a),s)),nn(null),pu(),ne(s=>{if(s===null)return Wr(r);let a=KE(s);return Vx(a),C(a)}))}processSegment(t,n,r,o,i,s,a){return Y(n).pipe(ht(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(ft(u=>{if(u instanceof hi)return C(null);throw u}))),jt(c=>!!c),ft(c=>{if(ZE(c))return Fx(r,o,i)?C(new ih):Wr(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return ct(r)!==s&&(s===O||!Fc(o,i,r))?Wr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):Wr(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=QE(n,o,i);if(!c)return Wr(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>jx&&(this.allowRedirects=!1));let f=new Yn(i,u,Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,CE(o),ct(o),o.component??o._loadedComponent??null,o,bE(o)),p=xc(f,a,this.paramsInheritanceStrategy);f.params=Object.freeze(p.params),f.data=Object.freeze(p.data);let m=this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,t);return this.applyRedirects.lineralizeSegments(o,m).pipe(ne(D=>this.processSegment(t,r,n,D.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=Rx(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Ie(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(Ie(({routes:u})=>{let l=r._loadedInjector??t,{parameters:d,consumedSegments:h,remainingSegments:f}=c,p=new Yn(h,d,Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,CE(r),ct(r),r.component??r._loadedComponent??null,r,bE(r)),m=xc(p,s,this.paramsInheritanceStrategy);p.params=Object.freeze(m.params),p.data=Object.freeze(m.data);let{segmentGroup:D,slicedSegments:b}=IE(n,h,f,u);if(b.length===0&&D.hasChildren())return this.processChildren(l,u,D,p).pipe(F(V=>new Ue(p,V)));if(u.length===0&&b.length===0)return C(new Ue(p,[]));let H=ct(r)===i;return this.processSegment(l,u,D,b,H?O:i,!0,p).pipe(F(V=>new Ue(p,V instanceof Ue?[V]:[])))}))):Wr(n)))}getChildConfig(t,n,r){return n.children?C({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?C({routes:n._loadedRoutes,injector:n._loadedInjector}):Sx(t,n,r,this.urlSerializer).pipe(ne(o=>o?this.configLoader.loadChildren(t,n).pipe(oe(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):Nx(n))):C({routes:[],injector:t})}};function Vx(e){e.sort((t,n)=>t.value.outlet===O?-1:n.value.outlet===O?1:t.value.outlet.localeCompare(n.value.outlet))}function Ux(e){let t=e.value.routeConfig;return t&&t.path===""}function KE(e){let t=[],n=new Set;for(let r of e){if(!Ux(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=KE(r.children);t.push(new Ue(r.value,o))}return t.filter(r=>!n.has(r))}function CE(e){return e.data||{}}function bE(e){return e.resolve||{}}function Bx(e,t,n,r,o,i){return ne(s=>Lx(e,t,n,r,s.extractedUrl,o,i).pipe(F(({state:a,tree:c})=>U(E({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function $x(e,t){return ne(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return C(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of XE(c))s.add(u);let a=0;return Y(s).pipe(ht(c=>i.has(c)?Hx(c,r,e,t):(c.data=xc(c,c.parent,e).resolve,C(void 0))),oe(()=>a++),dr(1),ne(c=>a===s.size?C(n):De))})}function XE(e){let t=e.children.map(n=>XE(n)).flat();return[e,...t]}function Hx(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!HE(o)&&(i[gi]=o.title),zx(i,e,t,r).pipe(F(s=>(e._resolvedData=s,e.data=xc(e,e.parent,n).resolve,null)))}function zx(e,t,n,r){let o=Zf(e);if(o.length===0)return C({});let i={};return Y(o).pipe(ne(s=>qx(e[s],t,n,r).pipe(jt(),oe(a=>{if(a instanceof to)throw Oc(new vn,a);i[s]=a}))),dr(1),F(()=>i),ft(s=>ZE(s)?De:ur(s)))}function qx(e,t,n,r){let o=mi(t)??r,i=ro(e,o),s=i.resolve?i.resolve(t,n):Ae(o,()=>i(t,n));return En(s)}function Gf(e){return Ie(t=>{let n=e(t);return n?Y(n).pipe(F(()=>t)):C(t)})}var fh=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===O);return r}getResolvedTitleForRoute(n){return n.data[gi]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(JE),providedIn:"root"})}return e})(),JE=(()=>{class e extends fh{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(I(vE))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Jn=new y("",{providedIn:"root",factory:()=>({})}),oo=new y(""),Lc=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=g(eD);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return C(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=En(n.loadComponent()).pipe(F(tw),oe(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),rn(()=>{this.componentLoaders.delete(n)})),o=new ar(r,()=>new J).pipe(sr());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return C({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=ew(r,this.compiler,n,this.onLoadEndListener).pipe(rn(()=>{this.childrenLoaders.delete(r)})),s=new ar(i,()=>new J).pipe(sr());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ew(e,t,n,r){return En(e.loadChildren()).pipe(F(tw),ne(o=>o instanceof nf||Array.isArray(o)?C(o):Y(t.compileModuleAsync(o))),F(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(oo,[],{optional:!0,self:!0}).flat()),{routes:s.map(dh),injector:i}}))}function Gx(e){return e&&typeof e=="object"&&"default"in e}function tw(e){return Gx(e)?e.default:e}var jc=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(Wx),providedIn:"root"})}return e})(),Wx=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),hh=new y(""),ph=new y("");function nw(e,t,n){let r=e.get(ph),o=e.get(re);return e.get(q).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let i,s=new Promise(u=>{i=u}),a=o.startViewTransition(()=>(i(),Zx(e))),{onViewTransitionCreated:c}=r;return c&&Ae(e,()=>c({transition:a,from:t,to:n})),s})}function Zx(e){return new Promise(t=>{Da({read:()=>setTimeout(t)},{injector:e})})}var gh=new y(""),Vc=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new J;transitionAbortSubject=new J;configLoader=g(Lc);environmentInjector=g(ve);destroyRef=g(Zt);urlSerializer=g(Kn);rootContexts=g(Xn);location=g(pn);inputBindingEnabled=g(vi,{optional:!0})!==null;titleStrategy=g(fh);options=g(Jn,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=g(jc);createViewTransition=g(hh,{optional:!0});navigationErrorHandler=g(gh,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>C(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new bc(o)),r=o=>this.events.next(new Tc(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(U(E({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new pe(null),this.transitions.pipe(de(r=>r!==null),Ie(r=>{let o=!1,i=!1;return C(r).pipe(Ie(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Fe.SupersededByNewNavigation),De;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?U(E({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let u="";return this.events.next(new xt(s.id,this.urlSerializer.serialize(s.rawUrl),u,Kr.IgnoredSameUrlNavigation)),s.resolve(!1),De}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return C(s).pipe(Ie(u=>(this.events.next(new Dn(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?De:Promise.resolve(u))),Bx(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),oe(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=U(E({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let l=new ci(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:u,extractedUrl:l,source:d,restoredState:h,extras:f}=s,p=new Dn(u,this.urlSerializer.serialize(l),d,h);this.events.next(p);let m=BE(this.rootComponentType).snapshot;return this.currentTransition=r=U(E({},s),{targetSnapshot:m,urlAfterRedirects:l,extras:U(E({},f),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,C(r)}else{let u="";return this.events.next(new xt(s.id,this.urlSerializer.serialize(s.extractedUrl),u,Kr.IgnoredByUrlHandlingStrategy)),s.resolve(!1),De}}),oe(s=>{let a=new Ec(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),F(s=>(this.currentTransition=r=U(E({},s),{guards:ax(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),yx(this.environmentInjector,s=>this.events.next(s)),oe(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw Oc(this.urlSerializer,s.guardsResult);let a=new wc(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),de(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",Fe.GuardRejected),!1)),Gf(s=>{if(s.guards.canActivateChecks.length!==0)return C(s).pipe(oe(a=>{let c=new Ic(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),Ie(a=>{let c=!1;return C(a).pipe($x(this.paramsInheritanceStrategy,this.environmentInjector),oe({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",Fe.NoDataFromResolver)}}))}),oe(a=>{let c=new Cc(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),Gf(s=>{let a=c=>{let u=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(c.routeConfig).pipe(oe(l=>{c.component=l}),F(()=>{})));for(let l of c.children)u.push(...a(l));return u};return vo(a(s.targetSnapshot.root)).pipe(nn(null),Lt(1))}),Gf(()=>this.afterPreactivation()),Ie(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?Y(c).pipe(F(()=>r)):C(r)}),F(s=>{let a=nx(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=U(E({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),oe(()=>{this.events.next(new ui)}),sx(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),Lt(1),oe({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new $e(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),Du(this.transitionAbortSubject.pipe(oe(s=>{throw s}))),rn(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",Fe.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),ft(s=>{if(this.destroyed)return r.resolve(!1),De;if(i=!0,WE(s))this.events.next(new Rt(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),ix(s)?this.events.next(new eo(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new Xr(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=Ae(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof to){let{message:u,cancellationCode:l}=Oc(this.urlSerializer,c);this.events.next(new Rt(r.id,this.urlSerializer.serialize(r.extractedUrl),u,l)),this.events.next(new eo(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return De}))}))}cancelNavigationTransition(n,r,o){let i=new Rt(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Yx(e){return e!==vc}var rw=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(Qx),providedIn:"root"})}return e})(),kc=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},Qx=(()=>{class e extends kc{static \u0275fac=(()=>{let n;return function(o){return(n||(n=bd(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ow=(()=>{class e{urlSerializer=g(Kn);options=g(Jn,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=g(pn);urlHandlingStrategy=g(jc);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new At;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof At?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=BE(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(Kx),providedIn:"root"})}return e})(),Kx=(()=>{class e extends ow{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof Dn?this.updateStateMemento():n instanceof xt?this.commitTransition(r):n instanceof ci?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof ui?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Rt&&(n.code===Fe.GuardRejected||n.code===Fe.NoDataFromResolver)?this.restoreHistory(r):n instanceof Xr?this.restoreHistory(r,!0):n instanceof $e&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=E(E({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=E(E({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=bd(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Uc(e,t){e.events.pipe(de(n=>n instanceof $e||n instanceof Rt||n instanceof Xr||n instanceof xt),F(n=>n instanceof $e||n instanceof xt?0:(n instanceof Rt?n.code===Fe.Redirect||n.code===Fe.SupersededByNewNavigation:!1)?2:1),de(n=>n!==2),Lt(1)).subscribe(()=>{t()})}var Xx={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},Jx={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Ot=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=g(sf);stateManager=g(ow);options=g(Jn,{optional:!0})||{};pendingTasks=g(nt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=g(Vc);urlSerializer=g(Kn);location=g(pn);urlHandlingStrategy=g(jc);_events=new J;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=g(rw);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=g(oo,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!g(vi,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new te;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Rt&&r.code!==Fe.Redirect&&r.code!==Fe.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof $e)this.navigated=!0;else if(r instanceof eo){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=E({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||Yx(o.source)},s);this.scheduleNavigation(a,vc,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}tO(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),vc,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=E({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(dh),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=E(E({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=LE(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return jE(d,n,l,u??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=yn(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,vc,null,r)}navigate(n,r={skipLocationChange:!1}){return eO(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=E({},Xx):r===!1?o=E({},Jx):o=r,yn(n))return yE(this.currentUrlTree,n,o);let i=this.parseUrl(n);return yE(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return Uc(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function eO(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new v(4008,!1)}function tO(e){return!(e instanceof ui)&&!(e instanceof eo)}var Bc=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new J;constructor(n,r,o,i,s,a){this.router=n,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=n.events.subscribe(u=>{u instanceof $e&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(yn(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:Ev(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,n,r):o.removeAttribute(i,n)}get urlTree(){return this.routerLinkInput===null?null:yn(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)($(Ot),$(tn),va("tabindex"),$(Ur),$(Ct),$(it))};static \u0275dir=St({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&ff("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&lf("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Bo],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Bo],replaceUrl:[2,"replaceUrl","replaceUrl",Bo],routerLink:"routerLink"},features:[Hn]})}return e})(),nO=(()=>{class e{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new be;constructor(n,r,o,i,s){this.router=n,this.element=r,this.renderer=o,this.cdr=i,this.link=s,this.routerEventsSubscription=n.events.subscribe(a=>{a instanceof $e&&this.update()})}ngAfterContentInit(){C(this.links.changes,C(null)).pipe(dt()).subscribe(n=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let n=[...this.links.toArray(),this.link].filter(r=>!!r).map(r=>r.onChanges);this.linkInputChangesSubscription=Y(n).pipe(dt()).subscribe(r=>{this._isActive!==this.isLinkActive(this.router)(r)&&this.update()})}set routerLinkActive(n){let r=Array.isArray(n)?n:n.split(" ");this.classes=r.filter(o=>!!o)}ngOnChanges(n){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let n=this.hasActiveLinks();this.classes.forEach(r=>{n?this.renderer.addClass(this.element.nativeElement,r):this.renderer.removeClass(this.element.nativeElement,r)}),n&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==n&&(this._isActive=n,this.cdr.markForCheck(),this.isActiveChange.emit(n))})}isLinkActive(n){let r=rO(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return o=>{let i=o.urlTree;return i?n.isActive(i,r):!1}}hasActiveLinks(){let n=this.isLinkActive(this.router);return this.link&&n(this.link)||this.links.some(n)}static \u0275fac=function(r){return new(r||e)($(Ot),$(Ct),$(Ur),$($r),$(Bc,8))};static \u0275dir=St({type:e,selectors:[["","routerLinkActive",""]],contentQueries:function(r,o,i){if(r&1&&qy(i,Bc,5),r&2){let s;Gy(s=Wy())&&(o.links=s)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[Hn]})}return e})();function rO(e){return!!e.paths}var Di=class{};var iw=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,r,o,i){this.router=n,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(de(n=>n instanceof $e),ht(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Br(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return Y(o).pipe(dt())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=C(null);let i=o.pipe(ne(s=>s===null?C(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return Y([i,s]).pipe(dt())}else return i})}static \u0275fac=function(r){return new(r||e)(I(Ot),I(ve),I(Di),I(Lc))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),sw=new y(""),oO=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Dn?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof $e?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof xt&&n.code===Kr.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Jr&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Jr(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){ny()};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function iO(e){return e.routerState.root}function Ei(e,t){return{\u0275kind:e,\u0275providers:t}}function sO(){let e=g(ye);return t=>{let n=e.get(Se);if(t!==n.components[0])return;let r=e.get(Ot),o=e.get(aw);e.get(vh)===1&&r.initialNavigation(),e.get(lw,null,P.Optional)?.setUpPreloading(),e.get(sw,null,P.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var aw=new y("",{factory:()=>new J}),vh=new y("",{providedIn:"root",factory:()=>1});function cw(){let e=[{provide:vh,useValue:0},uf(()=>{let t=g(ye);return t.get(mf,Promise.resolve()).then(()=>new Promise(r=>{let o=t.get(Ot),i=t.get(aw);Uc(o,()=>{r(!0)}),t.get(Vc).afterPreactivation=()=>(r(!0),i.closed?C(void 0):i),o.initialNavigation()}))})];return Ei(2,e)}function uw(){let e=[uf(()=>{g(Ot).setUpLocationChangeListener()}),{provide:vh,useValue:2}];return Ei(3,e)}var lw=new y("");function dw(e){return Ei(0,[{provide:lw,useExisting:iw},{provide:Di,useExisting:e}])}function fw(){return Ei(8,[uh,{provide:vi,useExisting:uh}])}function hw(e){Ge("NgRouterViewTransitions");let t=[{provide:hh,useValue:nw},{provide:ph,useValue:E({skipNextTransition:!!e?.skipInitialTransition},e)}];return Ei(9,t)}var pw=[pn,{provide:Kn,useClass:vn},Ot,Xn,{provide:tn,useFactory:iO,deps:[Ot]},Lc,[]],aO=(()=>{class e{constructor(){}static forRoot(n,r){return{ngModule:e,providers:[pw,[],{provide:oo,multi:!0,useValue:n},[],r?.errorHandler?{provide:gh,useValue:r.errorHandler}:[],{provide:Jn,useValue:r||{}},r?.useHash?uO():lO(),cO(),r?.preloadingStrategy?dw(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?dO(r):[],r?.bindToComponentInputs?fw().\u0275providers:[],r?.enableViewTransitions?hw().\u0275providers:[],fO()]}}static forChild(n){return{ngModule:e,providers:[{provide:oo,multi:!0,useValue:n}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Tt({type:e});static \u0275inj=wt({})}return e})();function cO(){return{provide:sw,useFactory:()=>{let e=g(HD),t=g(q),n=g(Jn),r=g(Vc),o=g(Kn);return n.scrollOffset&&e.setOffset(n.scrollOffset),new oO(o,r,e,t,n)}}}function uO(){return{provide:it,useClass:Cf}}function lO(){return{provide:it,useClass:Ba}}function dO(e){return[e.initialNavigation==="disabled"?uw().\u0275providers:[],e.initialNavigation==="enabledBlocking"?cw().\u0275providers:[]]}var mh=new y("");function fO(){return[{provide:mh,useFactory:sO},{provide:Gn,multi:!0,useExisting:mh}]}export{E as a,U as b,ut as c,te as d,L as e,ar as f,J as g,pe as h,po as i,De as j,Y as k,C as l,ur as m,hu as n,F as o,vo as p,lr as q,ns as r,Gw as s,Ww as t,de as u,Zw as v,ft as w,Yw as x,Lt as y,tp as z,rn as A,Xw as B,Jw as C,vu as D,eI as E,tI as F,yu as G,Ie as H,Du as I,nI as J,oe as K,v as L,Rg as M,w as N,wt as O,y as P,I as Q,g as R,Ug as S,nC as T,ve as U,Hn as V,qV as W,GV as X,WV as Y,ZV as Z,bd as _,ye as $,Op as aa,be as ba,q as ca,Dt as da,Ct as ea,Eb as fa,km as ga,dl as ha,Vt as ia,Pr as ja,YV as ka,Nd as la,Gb as ma,Da as na,bt as oa,qT as pa,QV as qa,KV as ra,XV as sa,Ln as ta,Sr as ua,Ur as va,$ as wa,ny as xa,Qt as ya,iU as za,Mr as Aa,my as Ba,Tt as Ca,St as Da,b_ as Ea,cU as Fa,F_ as Ga,Vo as Ha,Se as Ia,lf as Ja,v0 as Ka,y0 as La,D0 as Ma,uU as Na,lU as Oa,dU as Pa,fU as Qa,hU as Ra,pU as Sa,jy as Ta,Vy as Ua,df as Va,By as Wa,$y as Xa,P0 as Ya,gU as Za,L0 as _a,ff as $a,mU as ab,vU as bb,yU as cb,qy as db,DU as eb,Gy as fb,Wy as gb,EU as hb,wU as ib,IU as jb,CU as kb,Q0 as lb,Yy as mb,K0 as nb,X0 as ob,bU as pb,J0 as qb,tN as rb,TU as sb,SU as tb,MU as ub,_U as vb,NU as wb,RU as xb,AU as yb,Uo as zb,$r as Ab,pf as Bb,Bo as Cb,PN as Db,kN as Eb,FN as Fb,LN as Gb,xU as Hb,re as Ib,Mt as Jb,pn as Kb,hR as Lb,jD as Mb,pR as Nb,gR as Ob,vR as Pb,yR as Qb,Tf as Rb,wR as Sb,UR as Tb,HR as Ub,Jt as Vb,cE as Wb,hE as Xb,lA as Yb,dA as Zb,fA as _b,TA as $b,f2 as ac,h2 as bc,tn as cc,ch as dc,Ot as ec,Bc as fc,nO as gc,aO as hc};
