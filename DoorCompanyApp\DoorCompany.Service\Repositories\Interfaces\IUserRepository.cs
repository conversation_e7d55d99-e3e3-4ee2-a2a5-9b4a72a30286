﻿using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.ImageDto;
using DoorCompany.Service.Dtos.PermissionDto;
using DoorCompany.Service.Dtos.RoleDto;
using DoorCompany.Service.Dtos.UserDto;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Interfaces
{
    public interface IUserRepository
    {
        // User Management
        Task<ApiResponse<List<UserResponseDto>>> GetAllUsersAsync();
        Task<ApiResponse<UserResponseDto>> GetUserByIdAsync(int id);
        Task<ApiResponse<UserResponseDto>> CreateUserAsync(CreateUserDto userDto);
        Task<ApiResponse<UserResponseDto>> UpdateUserAsync(UpdateUserDto userDto);
        Task<ApiResponse<bool>> DeleteUserAsync(int id);
        Task<ApiResponse<bool>> ChangePasswordAsync(ChangePasswordDto changePasswordDto);
        Task<ApiResponse<bool>> ResetPasswordAsync(int userId, string newPassword);
        // Authentication
        Task<ApiResponse<LoginResponseDto>> LoginAsync(LoginDto loginDto);
        Task<ApiResponse<LoginResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshTokenDto);
        Task<ApiResponse<bool>> LogoutAsync(int userId);
        Task<ApiResponse<bool>> ValidateTokenAsync(string token);

        //Authorization UserRole

        Task<ApiResponse<List<UserResponseWithRolesListDto>>> GetAllUserRoleAsync();
        Task<ApiResponse<UserResponseWithRolesDto>> GetUserRoleByIdAsync(int userId);
        Task<ApiResponse<UserResponseWithRolesDto>> UpdateUserRoleAsync(UpdateUserRolesDto updateUserRolesDto);
      

        //Authorization Role
        Task<ApiResponse<List<RoleResponseWithPermissionListDto>>> GetAllRolesAsync();
        Task<ApiResponse<RolePermissionsDto>> GetRolePermissionsByIdAsync(int roleId);

        Task<ApiResponse<RolePermissionsDto>> CreateRoleAsync(CreateRoleDto model);
        Task<ApiResponse<RolePermissionsDto>> UpdateRoleAsync(UpdateRoleDto model);
        Task<ApiResponse<RolePermissionsDto>> UpdateRolePermiissionAsync(UpdateRolePermissionsDto updateRolePermissionsDto);

        //Authorization Permissions
        Task<ApiResponse<List<PermissionResponseDto>>> GetAllPermissionAsync();
        Task<ApiResponse<List<PermissionResponseDto>>> CreatePermissonAsync(CreatePermissionDto dto);

        Task<ApiResponse<UserResponseDto>> UpdateUserProfileAsync(UpdateUserProfileDto userDto);

        Task<ApiResponse<UserResponseDto>> UpdateProfileImage(ChangeProfileImageDto profileImage);


    }
}
