﻿using DoorCompany.Data.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Data.Context
{
    public class ApplicationDbContext : DbContext
    {
        private readonly IHttpContextAccessor? _httpContextAccessor;

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IHttpContextAccessor httpContextAccessor) : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        #region Set Tables

        #region UsersTables
        /// <summary>
        /// Users 
        /// </summary>
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RefreshToken> RefreshTokens { get; set; }
        #endregion

        #region Company
        public DbSet<Company> Companies { get; set; }



        #endregion

        #region PartnerTables
        public DbSet<Partner> Partners { get; set; }
        public DbSet<PartnerBand> PartnerBands  { get; set; }
        public DbSet<PartnerTransation> PartnerTransations { get; set; }


        #endregion

        #region ShareDistribution
        public DbSet<ShareDistribution> ShareDistributions { get; set; }
        public DbSet<ShareTransfer> ShareTransfers { get; set; }
        public DbSet<ShareHistory> ShareHistories { get; set; }

        #endregion

        #region FinancialTransaction
        public DbSet<FinancialTransaction> FinancialTransactions { get; set; }
        #endregion

        #region Action Main Tables
        public DbSet<ActionType> ActionTypes { get; set; }
        public DbSet<MainAction> MainActions { get; set; }
        #endregion

        #region "StoresTables"
        public DbSet<Product> Products { get; set; }
        public DbSet<ItemCategory> ItemCategories { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }
        public DbSet<Party> Parties { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }   
        public DbSet<ProductionUnit> ProductionUnits { get; set; }
        public DbSet<InvoiceMaster> InvoiceMasters { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<ProductInventory> ProductInventories { get; set; }


        #endregion

        #endregion


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<ShareTransfer>()
            .HasIndex(t => t.BuyerId);

            modelBuilder.Entity<ShareTransfer>()
                .HasIndex(t => t.SellerId);


            modelBuilder.Entity<ShareTransfer>()
                  .HasOne(st => st.Buyer)
                  .WithMany(p => p.BuyTransactions) 
                  .HasForeignKey(st => st.BuyerId)
                  .OnDelete(DeleteBehavior.Restrict);

            
            modelBuilder.Entity<ShareTransfer>()
                .HasOne(st => st.Seller)
                .WithMany(p => p.SellTransactions) 
                .HasForeignKey(st => st.SellerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ProductInventory>()
            .HasIndex(pi => new { pi.ProductId })
            .IsUnique();
            // SeedData(modelBuilder);
        }
        private void SeedData(ModelBuilder modelBuilder)
        {
            var seedDate = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            // Seed default admin user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    UserName = "admin",
                    FullName = "مدير النظام",
                    Password = "eEZPEDAT0eLC131RCecDF38MAyaj3SHPezzQ7iuONHTAAvAHZVnmCR4QfKX3EQIY",
                    Phone = "",
                    ProfileImage = "",
                    IsActive = true,
                    CreatedAt = seedDate,
                    CreatedBy =1
                }
            );

            // Seed Roles
            modelBuilder.Entity<Role>().HasData(
                new Role { Id = 1, Name = "مدير المخازن", Description = "إدارة كاملة للمخازن والمخزون", IsActive = true , CreatedAt = seedDate, CreatedBy = 1},
                new Role { Id = 2, Name = "شريك", Description = "إدارة الشريك", IsActive = true, CreatedAt = seedDate, CreatedBy = 1, },
                new Role { Id = 3, Name = "مدير الشركاء", Description = "إدارة كاملة إدارة حركات الشركاء", IsActive = true, CreatedAt = seedDate, CreatedBy = 1 },
                new Role { Id = 4, Name = "مدير النظام", Description = "إدارة كاملة لجميع صلاحيات البرنامج", IsActive = true, CreatedAt = seedDate,CreatedBy = 1},
                new Role { Id = 5, Name = "إدارة المستخدمين", Description = "إدارة كاملة لجميع صلاحيات المستخدمين", IsActive = true, CreatedAt = seedDate,CreatedBy = 1}
            );

            // Seed Permissions
            modelBuilder.Entity<Permission>().HasData(
                new Permission { Id = 1, Name = "VIEW_INVENTORY", Description = "عرض المخزون",  IsActive = true, CreatedAt = seedDate, CreatedBy = 1 },
                new Permission { Id = 2, Name = "MANAGE_INVENTORY", Description = "إدارة المخزون", IsActive = true, CreatedAt = seedDate, CreatedBy = 1 },
                new Permission { Id = 3, Name = "VIEW_PRODUCTION", Description = "عرض الإنتاج", IsActive = true, CreatedAt = seedDate, CreatedBy = 1 },
                new Permission { Id = 4, Name = "MANAGE_PRODUCTION", Description = "إدارة الإنتاج", IsActive = true, CreatedAt = seedDate, CreatedBy = 1 },
                new Permission { Id = 5, Name = "VIEW_REPORTS", Description = "عرض التقارير", IsActive = true, CreatedAt = seedDate, CreatedBy = 1 },
                new Permission { Id = 6, Name = "MANAGE_USERS", Description = "إدارة المستخدمين", IsActive = true, CreatedAt = seedDate, CreatedBy = 1 }
            );
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateProductInventory();
            ApplyAuditTrail();
            return await base.SaveChangesAsync(cancellationToken);
        }

        public override int SaveChanges()
        {
            UpdateProductInventory();
            ApplyAuditTrail();
            return base.SaveChanges();
        }

        private void ApplyAuditTrail()
        {
            var currentUserId = GetCurrentUserId();
            var currentTime = DateTime.Now;

            foreach (var entry in ChangeTracker.Entries<BaseEntity>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = currentTime;
                        if (currentUserId.HasValue)
                            entry.Entity.CreatedBy = currentUserId.Value;
                        break;

                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = currentTime;
                        if (currentUserId.HasValue)
                            entry.Entity.UpdatedBy = currentUserId.Value;
                        // Prevent modification of creation audit fields
                        entry.Property(e => e.CreatedAt).IsModified = false;
                        entry.Property(e => e.CreatedBy).IsModified = false;
                        break;
                }
            }
        }
        private int? GetCurrentUserId()
        {
            try
            {
                var userIdClaim = _httpContextAccessor?.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (int.TryParse(userIdClaim, out int userId))
                {
                    return userId;
                }
            }
            catch
            {
                // If we can't get the user ID, return null (system operation)
            }
            return null;
        }

        
        private void UpdateProductInventory()
        {
            var addedTransactions = ChangeTracker
                .Entries<InventoryTransaction>()
                .Where(e => e.State == EntityState.Added)
                .Select(e => e.Entity)
                .ToList();

            if (!addedTransactions.Any()) return;

            foreach (var transaction in addedTransactions)
            {
                var productId = transaction.ProductId;
                
                // جلب أو إنشاء سجل الأرصدة
                var inventory = ProductInventories
                    .Local
                    .FirstOrDefault(pi => pi.ProductId == productId)
                    ?? Set<ProductInventory>()
                        .FirstOrDefault(pi => pi.ProductId == productId);

                if (inventory == null)
                {
                    inventory = new ProductInventory
                    {
                        ProductId = productId,                     
                        Balance = transaction.Quantity,
                        AverageCost = transaction.AverageCost
                    };
                    ProductInventories.Add(inventory);
                }

                // حساب التأثير على الكمية
                decimal quantityChange = 0;
                decimal costImpact = 0;

                switch (transaction.Type)
                {
                    case InventoryTransactionType.OpeningBalance:
                    case InventoryTransactionType.Purchase:
                    case InventoryTransactionType.ProductionOutput:
                    case InventoryTransactionType.PurchaseReturn:
                    case InventoryTransactionType.Adjustment when transaction.Quantity > 0:
                    //case InventoryTransactionType.Transfer when IsIncomingTransfer(transaction): // داخلي
                    //    quantityChange = transaction.Quantity;
                    //    costImpact = transaction.TotalCost;
                        break;

                    case InventoryTransactionType.Sale:
                    case InventoryTransactionType.ProductionInput:
                    case InventoryTransactionType.SalesReturn:
                    case InventoryTransactionType.Adjustment when transaction.Quantity < 0:
                    //case InventoryTransactionType.Transfer when IsOutgoingTransfer(transaction): // خارجي
                    //    quantityChange = -Math.Abs(transaction.Quantity);
                        // لا يؤثر على التكلفة (يستخدم متوسط التكلفة الحالي)
                        break;

                    default:
                        break;
                }

                // تحديث الكمية
                inventory.Balance += quantityChange;

                // تحديث متوسط التكلفة فقط عند الدخول (شراء، رصيد افتتاحي، إلخ)
                if (quantityChange > 0 && inventory.Balance > 0)
                {
                    var newTotalCost = (inventory.Balance - quantityChange) * inventory.AverageCost + costImpact;
                    inventory.AverageCost = newTotalCost / inventory.Balance;
                }
                // إذا أصبح الرصيد صفرًا، اجعل متوسط التكلفة 0
                else if (inventory.Balance == 0)
                {
                    inventory.AverageCost = 0;
                }
            }
        }


    }

}
