﻿using Azure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos
{
    public class ResponseHandler
    {
        public ApiResponse<T> Deleted<T>(string? Message = null)
        {
            return new ApiResponse<T>()
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                Succeeded = true,
                Message = Message == null ? "تم الحذف بنجاح" : Message
            };
        }
        public ApiResponse<T> Success<T>(T entity, object? Meta = null)
        {
            return new ApiResponse<T>()
            {
                Data = entity,
                StatusCode = System.Net.HttpStatusCode.OK,
                Succeeded = true,
                Message = "تم بنجاح",
                Meta = Meta ?? new object()
            };
        }
        public ApiResponse<T> Unauthorized<T>(string? Message = null)
        {
            return new ApiResponse<T>()
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                Succeeded = true,
                Message = Message == null ? "ليس لديك الصلاحية" : Message
            };
        }
        public ApiResponse<T> BadRequest<T>(string? Message = null)
        {
            return new ApiResponse<T>()
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                Succeeded = false,
                Message = Message == null ? "خطا" : Message
            };
        }

        public ApiResponse<T> UnprocessableEntity<T>(string? Message = null)
        {
            return new ApiResponse<T>()
            {
                StatusCode = System.Net.HttpStatusCode.UnprocessableEntity,
                Succeeded = false,
                Message = Message == null ? "إدخال خطا" : Message
            };
        }


        public ApiResponse<T> NotFound<T>(string? message = null)
        {
            return new ApiResponse<T>()
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                Succeeded = false,
                Message = message == null ? "لا يوجد بيانات" : message
            };
        }

        public ApiResponse<T> Created<T>(T entity, object? Meta = null)
        {
            return new ApiResponse<T>()
            {
                Data = entity,
                StatusCode = System.Net.HttpStatusCode.Created,
                Succeeded = true,
                Message = "تم الاضافة",
                Meta = Meta ?? new object()
            };
        }
    }
}
