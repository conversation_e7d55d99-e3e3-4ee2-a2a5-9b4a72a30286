import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { CreateShareTransactionDto, Partner, ShareTranactionDialogData, ShareTransaction, UpdateShareTransactionDto } from '../../../../models/partner';
import { PartnerService } from '../../../../services/partner.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-share-transaction-dialog',
  standalone: false,
  templateUrl: './share-transaction-dialog.component.html',
  styleUrl: './share-transaction-dialog.component.css'
})
export class ShareTransactionDialogComponent implements OnInit {
 form!: FormGroup;
  isEditMode = false;
  salesId?: number;
  loading = false;
  saving = false;

  partners: Partner[] = [];
  
 constructor(
    private fb: Form<PERSON>uilder,
    private partnerService: PartnerService,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<ShareTranactionDialogData>,
    @Inject(MAT_DIALOG_DATA) public data: ShareTranactionDialogData
  ) {
    this.isEditMode = data.mode === 'edit';
    this.form = this.createForm();
    this.loadData();
  }

 ngOnInit(): void {
     if (this.isEditMode && this.data.sharetransaction) {
      this.populateForm(this.data.sharetransaction);
     }
}

loadData(): void {
  // Load Partners
    this.partnerService.getPartners().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.partners = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading partenrTransaction:', error);
      }
    });
}

private createForm(): FormGroup {
  return this.fb.group({
    transfersDate: [new Date(), Validators.required],
    sellerId: ['', Validators.required],  
    buyerId: ['', Validators.required],  
    sharesCount: ['', [Validators.required, Validators.min(1)]],  
    transferAmount: ['', [Validators.required, Validators.min(0)]]
  }, {
    validators: this.sellerNotEqualBuyerValidator
  });
}

  private populateForm(sharetransaction: ShareTransaction): void {
    this.form.patchValue({  
      transfersDate: sharetransaction.transfersDate,
      sellerId: sharetransaction.sellerId,  
      buyerId: sharetransaction.buyerId,  
      sharesCount: sharetransaction.sharesCount,  
      transferAmount: sharetransaction.transferAmount,  
     });
  }

  onSubmit(): void {
     if (this.form.valid) {
       this.loading = true;
       const formValue = this.form.value;
 
       if (this.isEditMode) {
         const updateDto: UpdateShareTransactionDto = formValue;
         this.partnerService.updateShareTransaction(this.data.sharetransaction!.id, updateDto)
           .subscribe({
             next: (response) => {
               if (response.succeeded && response.data != null) {
                 this.dialogRef.close(true);
               }
               this.loading = false;
             },
             error: (error) => {
                 this.loading = false;
             }
           });
       } else {
         const createDto: CreateShareTransactionDto = formValue;
         this.partnerService.createShareTransaction(createDto)
           .subscribe({
             next: (response) => {
               if (response.succeeded && response.data != null) {
                 this.snackBar.open('تم إنشاء المعاملة بنجاح', 'إغلاق', { duration: 3000 });
                 this.dialogRef.close(true);
               }
               this.loading = false;
             },
             error: (error) => {
              
               this.loading = false;
             }
           });
       }
     }
   }
 

  onCancel(): void {
    this.dialogRef.close(false);
  }

getErrorMessage(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field?.hasError('required')) {
      return 'هذا الحقل مطلوب';
    }

    if (field?.hasError('maxlength')) {
      const maxLength = field.errors?.['maxlength']?.requiredLength;
      return `الحد الأقصى ${maxLength} حرف`;
    }
    if (field?.hasError('min')) {
      return 'يجب أن تكون القيمة أكبر من أو تساوي 0';
    }
    if (field?.hasError('max')) {
      return 'يجب أن تكون القيمة أقل من أو تساوي 100';
    }
    return '';
  }

private sellerNotEqualBuyerValidator(formGroup: FormGroup) {
  const sellerId = formGroup.get('sellerId')?.value;
  const buyerId = formGroup.get('buyerId')?.value;

  if (sellerId && buyerId && sellerId === buyerId) {
    formGroup.get('buyerId')?.setErrors({ sameAsSeller: true });
  } else {
    // تأكد من حذف الخطأ إن لم يكن موجوداً
    const errors = formGroup.get('buyerId')?.errors;
    if (errors) {
      delete errors['sameAsSeller'];
      if (Object.keys(errors).length === 0) {
        formGroup.get('buyerId')?.setErrors(null);
      } else {
        formGroup.get('buyerId')?.setErrors(errors);
      }
    }
  }

  return null;
}




}
