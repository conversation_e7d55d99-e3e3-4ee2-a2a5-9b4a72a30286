{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/element-x4z00URv.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/platform-DmdVEw_C.mjs"], "sourcesContent": ["import { ElementRef } from '@angular/core';\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  if (_isNumberValue(value)) {\n    return Number(value);\n  }\n  return arguments.length === 2 ? fallbackValue : 0;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\nexport { _isNumberValue as _, coerceElement as a, coerceNumberProperty as c };\n", "import * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Injectable } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n  _platformId = inject(PLATFORM_ID);\n  // We want to use the Angular platform check because if the Document is shimmed\n  // without the navigator, the following checks will fail. This is preferred because\n  // sometimes the Document may be shimmed without the user's knowledge or intention\n  /** Whether the Angular application is being rendered in the browser. */\n  isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n  /** Whether the current browser is Microsoft Edge. */\n  EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n  /** Whether the current rendering engine is Microsoft Trident. */\n  TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n  // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n  /** Whether the current rendering engine is Blink. */\n  BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT;\n  // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n  // ensure that Webkit runs standalone and is not used as another engine's base.\n  /** Whether the current rendering engine is WebKit. */\n  WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n  /** Whether the current platform is Apple iOS. */\n  IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n  // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n  // them self as Gecko-like browsers and modify the userAgent's according to that.\n  // Since we only cover one explicit Firefox case, we can simply check for Firefox\n  // instead of having an unstable check for Gecko.\n  /** Whether the current browser is Firefox. */\n  FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n  /** Whether the current platform is Android. */\n  // Trident on mobile adds the android platform to the userAgent to trick detections.\n  ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n  // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n  // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n  // Safari browser should also use Webkit as its layout engine.\n  /** Whether the current browser is Safari. */\n  SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n  constructor() {}\n  static ɵfac = function Platform_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Platform)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { Platform as P };\n"], "mappings": ";;;;;;;;;;;;;AACA,SAAS,qBAAqB,OAAO,gBAAgB,GAAG;AACtD,MAAI,eAAe,KAAK,GAAG;AACzB,WAAO,OAAO,KAAK;AAAA,EACrB;AACA,SAAO,UAAU,WAAW,IAAI,gBAAgB;AAClD;AAKA,SAAS,eAAe,OAAO;AAI7B,SAAO,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AAC1D;AAMA,SAAS,cAAc,cAAc;AACnC,SAAO,wBAAwB,aAAa,aAAa,gBAAgB;AAC3E;;;AClBA,IAAI;AAMJ,IAAI;AACF,uBAAqB,OAAO,SAAS,eAAe,KAAK;AAC3D,QAAQ;AACN,uBAAqB;AACvB;AAKA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,cAAc,OAAO,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,YAAY,KAAK,cAAc,kBAAkB,KAAK,WAAW,IAAI,OAAO,aAAa,YAAY,CAAC,CAAC;AAAA;AAAA,EAEvG,OAAO,KAAK,aAAa,UAAU,KAAK,UAAU,SAAS;AAAA;AAAA,EAE3D,UAAU,KAAK,aAAa,kBAAkB,KAAK,UAAU,SAAS;AAAA;AAAA;AAAA,EAGtE,QAAQ,KAAK,aAAa,CAAC,EAAE,OAAO,UAAU,uBAAuB,OAAO,QAAQ,eAAe,CAAC,KAAK,QAAQ,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA,EAIvH,SAAS,KAAK,aAAa,eAAe,KAAK,UAAU,SAAS,KAAK,CAAC,KAAK,SAAS,CAAC,KAAK,QAAQ,CAAC,KAAK;AAAA;AAAA,EAE1G,MAAM,KAAK,aAAa,mBAAmB,KAAK,UAAU,SAAS,KAAK,EAAE,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxF,UAAU,KAAK,aAAa,uBAAuB,KAAK,UAAU,SAAS;AAAA;AAAA;AAAA,EAG3E,UAAU,KAAK,aAAa,WAAW,KAAK,UAAU,SAAS,KAAK,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1E,SAAS,KAAK,aAAa,UAAU,KAAK,UAAU,SAAS,KAAK,KAAK;AAAA,EACvE,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,UAAS;AAAA,IAClB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;", "names": []}