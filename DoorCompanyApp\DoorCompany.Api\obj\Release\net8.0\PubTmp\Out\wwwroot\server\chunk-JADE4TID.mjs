import './polyfills.server.mjs';
import{a as E,b as B,d as ih,h as Wr}from"./chunk-S6KH3LOX.mjs";function _c(e,t){return Object.is(e,t)}var ae=null,Ci=!1,Nc=1,_e=Symbol("SIGNAL");function P(e){let t=ae;return ae=e,t}function Rc(){return ae}var Wn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Yr(e){if(Ci)throw new Error("");if(ae===null)return;ae.consumerOnSignalRead(e);let t=ae.nextProducerIndex++;if(_i(ae),t<ae.producerNode.length&&ae.producerNode[t]!==e&&Zr(ae)){let n=ae.producerNode[t];Mi(n,ae.producerIndexOfThis[t])}ae.producerNode[t]!==e&&(ae.producerNode[t]=e,ae.producerIndexOfThis[t]=Zr(ae)?ah(e,ae,t):0),ae.producerLastReadVersion[t]=e.version}function sh(){Nc++}function Ac(e){if(!(Zr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Nc)){if(!e.producerMustRecompute(e)&&!Si(e)){Mc(e);return}e.producerRecomputeValue(e),Mc(e)}}function xc(e){if(e.liveConsumerNode===void 0)return;let t=Ci;Ci=!0;try{for(let n of e.liveConsumerNode)n.dirty||GE(n)}finally{Ci=t}}function Oc(){return ae?.consumerAllowSignalWrites!==!1}function GE(e){e.dirty=!0,xc(e),e.consumerMarkedDirty?.(e)}function Mc(e){e.dirty=!1,e.lastCleanEpoch=Nc}function Qr(e){return e&&(e.nextProducerIndex=0),P(e)}function Ti(e,t){if(P(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Zr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Mi(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Si(e){_i(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ac(n),r!==n.version))return!0}return!1}function Kr(e){if(_i(e),Zr(e))for(let t=0;t<e.producerNode.length;t++)Mi(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function ah(e,t,n){if(ch(e),e.liveConsumerNode.length===0&&uh(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=ah(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Mi(e,t){if(ch(e),e.liveConsumerNode.length===1&&uh(e))for(let r=0;r<e.producerNode.length;r++)Mi(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];_i(o),o.producerIndexOfThis[r]=t}}function Zr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function _i(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function ch(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function uh(e){return e.producerNode!==void 0}function Ni(e,t){let n=Object.create(WE);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Ac(n),Yr(n),n.value===bi)throw n.error;return n.value};return r[_e]=n,r}var Tc=Symbol("UNSET"),Sc=Symbol("COMPUTING"),bi=Symbol("ERRORED"),WE=B(E({},Wn),{value:Tc,dirty:!0,error:null,equal:_c,kind:"computed",producerMustRecompute(e){return e.value===Tc||e.value===Sc},producerRecomputeValue(e){if(e.value===Sc)throw new Error("Detected cycle in computations.");let t=e.value;e.value=Sc;let n=Qr(e),r,o=!1;try{r=e.computation(),P(null),o=t!==Tc&&t!==bi&&r!==bi&&e.equal(t,r)}catch(i){r=bi,e.error=i}finally{Ti(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function ZE(){throw new Error}var lh=ZE;function dh(e){lh(e)}function Pc(e){lh=e}var YE=null;function kc(e,t){let n=Object.create(Ri);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(Yr(n),n.value);return r[_e]=n,r}function Jr(e,t){Oc()||dh(e),e.equal(e.value,t)||(e.value=t,QE(e))}function Fc(e,t){Oc()||dh(e),Jr(e,t(e.value))}var Ri=B(E({},Wn),{equal:_c,value:void 0,kind:"signal"});function QE(e){e.version++,sh(),xc(e),YE?.()}function Lc(e){let t=P(null);try{return e()}finally{P(t)}}var jc;function Xr(){return jc}function Nt(e){let t=jc;return jc=e,t}var Ai=Symbol("NotFound");function R(e){return typeof e=="function"}function Zn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var xi=Zn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function mn(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var te=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(R(r))try{r()}catch(i){t=i instanceof xi?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{fh(i)}catch(s){t=t??[],s instanceof xi?t=[...t,...s.errors]:t.push(s)}}if(t)throw new xi(t)}}add(t){var n;if(t&&t!==this)if(this.closed)fh(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&mn(n,t)}remove(t){let{_finalizers:n}=this;n&&mn(n,t),t instanceof e&&t._removeParent(this)}};te.EMPTY=(()=>{let e=new te;return e.closed=!0,e})();var Vc=te.EMPTY;function Oi(e){return e instanceof te||e&&"closed"in e&&R(e.remove)&&R(e.add)&&R(e.unsubscribe)}function fh(e){R(e)?e():e.unsubscribe()}var We={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Yn={setTimeout(e,t,...n){let{delegate:r}=Yn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Yn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Pi(e){Yn.setTimeout(()=>{let{onUnhandledError:t}=We;if(t)t(e);else throw e})}function eo(){}var hh=Uc("C",void 0,void 0);function ph(e){return Uc("E",void 0,e)}function gh(e){return Uc("N",e,void 0)}function Uc(e,t,n){return{kind:e,value:t,error:n}}var vn=null;function Qn(e){if(We.useDeprecatedSynchronousErrorHandling){let t=!vn;if(t&&(vn={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=vn;if(vn=null,n)throw r}}else e()}function mh(e){We.useDeprecatedSynchronousErrorHandling&&vn&&(vn.errorThrown=!0,vn.error=e)}var yn=class extends te{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Oi(t)&&t.add(this)):this.destination=nw}static create(t,n,r){return new Rt(t,n,r)}next(t){this.isStopped?$c(gh(t),this):this._next(t)}error(t){this.isStopped?$c(ph(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?$c(hh,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},ew=Function.prototype.bind;function Bc(e,t){return ew.call(e,t)}var Hc=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){ki(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){ki(r)}else ki(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){ki(n)}}},Rt=class extends yn{constructor(t,n,r){super();let o;if(R(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&We.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Bc(t.next,i),error:t.error&&Bc(t.error,i),complete:t.complete&&Bc(t.complete,i)}):o=t}this.destination=new Hc(o)}};function ki(e){We.useDeprecatedSynchronousErrorHandling?mh(e):Pi(e)}function tw(e){throw e}function $c(e,t){let{onStoppedNotification:n}=We;n&&Yn.setTimeout(()=>n(e,t))}var nw={closed:!0,next:eo,error:tw,complete:eo};var Kn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ge(e){return e}function zc(...e){return qc(e)}function qc(e){return e.length===0?ge:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var F=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=ow(n)?n:new Rt(n,r,o);return Qn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=vh(r),new r((o,i)=>{let s=new Rt({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Kn](){return this}pipe(...n){return qc(n)(this)}toPromise(n){return n=vh(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function vh(e){var t;return(t=e??We.Promise)!==null&&t!==void 0?t:Promise}function rw(e){return e&&R(e.next)&&R(e.error)&&R(e.complete)}function ow(e){return e&&e instanceof yn||rw(e)&&Oi(e)}function Gc(e){return R(e?.lift)}function M(e){return t=>{if(Gc(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function S(e,t,n,r,o){return new Wc(e,t,n,r,o)}var Wc=class extends yn{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Jn(){return M((e,t)=>{let n=null;e._refCount++;let r=S(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Xn=class extends F{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Gc(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new te;let n=this.getSubject();t.add(this.source.subscribe(S(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=te.EMPTY)}return t}refCount(){return Jn()(this)}};var yh=Zn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var X=(()=>{class e extends F{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Fi(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new yh}next(n){Qn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Qn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Qn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Vc:(this.currentObservers=null,i.push(n),new te(()=>{this.currentObservers=null,mn(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new F;return n.source=this,n}}return e.create=(t,n)=>new Fi(t,n),e})(),Fi=class extends X{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Vc}};var me=class extends X{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var to={now(){return(to.delegate||Date).now()},delegate:void 0};var no=class extends X{constructor(t=1/0,n=1/0,r=to){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Li=class extends te{constructor(t,n){super()}schedule(t,n=0){return this}};var ro={setInterval(e,t,...n){let{delegate:r}=ro;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=ro;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var ji=class extends Li{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return ro.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&ro.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,mn(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var er=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};er.now=to.now;var Vi=class extends er{constructor(t,n=er.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var oo=new Vi(ji),Dh=oo;var De=new F(e=>e.complete());function Ui(e){return e&&R(e.schedule)}function Zc(e){return e[e.length-1]}function Bi(e){return R(Zc(e))?e.pop():void 0}function ct(e){return Ui(Zc(e))?e.pop():void 0}function Eh(e,t){return typeof Zc(e)=="number"?e.pop():t}function Ih(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function wh(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Dn(e){return this instanceof Dn?(this.v=e,this):new Dn(e)}function Ch(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(p){return Promise.resolve(p).then(f,d)}}function a(f,p){r[f]&&(o[f]=function(m){return new Promise(function(D,I){i.push([f,m,D,I])>1||c(f,m)})},p&&(o[f]=p(o[f])))}function c(f,p){try{u(r[f](p))}catch(m){h(i[0][3],m)}}function u(f){f.value instanceof Dn?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,p){f(p),i.shift(),i.length&&c(i[0][0],i[0][1])}}function bh(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof wh=="function"?wh(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var $i=e=>e&&typeof e.length=="number"&&typeof e!="function";function Hi(e){return R(e?.then)}function zi(e){return R(e[Kn])}function qi(e){return Symbol.asyncIterator&&R(e?.[Symbol.asyncIterator])}function Gi(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function iw(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Wi=iw();function Zi(e){return R(e?.[Wi])}function Yi(e){return Ch(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Dn(n.read());if(o)return yield Dn(void 0);yield yield Dn(r)}}finally{n.releaseLock()}})}function Qi(e){return R(e?.getReader)}function $(e){if(e instanceof F)return e;if(e!=null){if(zi(e))return sw(e);if($i(e))return aw(e);if(Hi(e))return cw(e);if(qi(e))return Th(e);if(Zi(e))return uw(e);if(Qi(e))return lw(e)}throw Gi(e)}function sw(e){return new F(t=>{let n=e[Kn]();if(R(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function aw(e){return new F(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function cw(e){return new F(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Pi)})}function uw(e){return new F(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Th(e){return new F(t=>{dw(e,t).catch(n=>t.error(n))})}function lw(e){return Th(Yi(e))}function dw(e,t){var n,r,o,i;return Ih(this,void 0,void 0,function*(){try{for(n=bh(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Ne(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Ki(e,t=0){return M((n,r)=>{n.subscribe(S(r,o=>Ne(r,e,()=>r.next(o),t),()=>Ne(r,e,()=>r.complete(),t),o=>Ne(r,e,()=>r.error(o),t)))})}function Ji(e,t=0){return M((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Sh(e,t){return $(e).pipe(Ji(t),Ki(t))}function Mh(e,t){return $(e).pipe(Ji(t),Ki(t))}function _h(e,t){return new F(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Nh(e,t){return new F(n=>{let r;return Ne(n,t,()=>{r=e[Wi](),Ne(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>R(r?.return)&&r.return()})}function Xi(e,t){if(!e)throw new Error("Iterable cannot be null");return new F(n=>{Ne(n,t,()=>{let r=e[Symbol.asyncIterator]();Ne(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Rh(e,t){return Xi(Yi(e),t)}function Ah(e,t){if(e!=null){if(zi(e))return Sh(e,t);if($i(e))return _h(e,t);if(Hi(e))return Mh(e,t);if(qi(e))return Xi(e,t);if(Zi(e))return Nh(e,t);if(Qi(e))return Rh(e,t)}throw Gi(e)}function W(e,t){return t?Ah(e,t):$(e)}function b(...e){let t=ct(e);return W(e,t)}function tr(e,t){let n=R(e)?e:()=>e,r=o=>o.error(n());return new F(t?o=>t.schedule(r,0,o):r)}function Yc(e){return!!e&&(e instanceof F||R(e.lift)&&R(e.subscribe))}var At=Zn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function xh(e){return e instanceof Date&&!isNaN(e)}function k(e,t){return M((n,r)=>{let o=0;n.subscribe(S(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:fw}=Array;function hw(e,t){return fw(t)?e(...t):e(t)}function es(e){return k(t=>hw(e,t))}var{isArray:pw}=Array,{getPrototypeOf:gw,prototype:mw,keys:vw}=Object;function ts(e){if(e.length===1){let t=e[0];if(pw(t))return{args:t,keys:null};if(yw(t)){let n=vw(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function yw(e){return e&&typeof e=="object"&&gw(e)===mw}function ns(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function io(...e){let t=ct(e),n=Bi(e),{args:r,keys:o}=ts(e);if(r.length===0)return W([],t);let i=new F(Dw(r,t,o?s=>ns(o,s):ge));return n?i.pipe(es(n)):i}function Dw(e,t,n=ge){return r=>{Oh(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Oh(t,()=>{let u=W(e[c],t),l=!1;u.subscribe(S(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Oh(e,t,n){e?Ne(n,e,t):t()}function Ph(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&t.complete()},f=m=>u<r?p(m):c.push(m),p=m=>{i&&t.next(m),u++;let D=!1;$(n(m,l++)).subscribe(S(t,I=>{o?.(I),i?f(I):t.next(I)},()=>{D=!0},void 0,()=>{if(D)try{for(u--;c.length&&u<r;){let I=c.shift();s?Ne(t,s,()=>p(I)):p(I)}h()}catch(I){t.error(I)}}))};return e.subscribe(S(t,f,()=>{d=!0,h()})),()=>{a?.()}}function ne(e,t,n=1/0){return R(t)?ne((r,o)=>k((i,s)=>t(r,i,o,s))($(e(r,o))),n):(typeof t=="number"&&(n=t),M((r,o)=>Ph(r,o,e,n)))}function ut(e=1/0){return ne(ge,e)}function kh(){return ut(1)}function nr(...e){return kh()(W(e,ct(e)))}function rs(e){return new F(t=>{$(e()).subscribe(t)})}function Ew(...e){let t=Bi(e),{args:n,keys:r}=ts(e),o=new F(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;$(n[l]).subscribe(S(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?ns(r,a):a),i.complete())}))}});return t?o.pipe(es(t)):o}function os(e=0,t,n=Dh){let r=-1;return t!=null&&(Ui(t)?n=t:r=t),new F(o=>{let i=xh(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function ww(...e){let t=ct(e),n=Eh(e,1/0),r=e;return r.length?r.length===1?$(r[0]):ut(n)(W(r,t)):De}function de(e,t){return M((n,r)=>{let o=0;n.subscribe(S(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Fh(e){return M((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let u=o;o=null,n.next(u)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(S(n,u=>{r=!0,o=u,i||$(e(u)).subscribe(i=S(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Iw(e,t=oo){return Fh(()=>os(e,t))}function lt(e){return M((t,n)=>{let r=null,o=!1,i;r=t.subscribe(S(n,void 0,void 0,s=>{i=$(e(s,lt(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Lh(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(S(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function dt(e,t){return R(t)?ne(e,t,1):ne(e,1)}function Cw(e,t=oo){return M((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function c(){let u=s+e,l=t.now();if(l<u){o=this.schedule(void 0,u-l),r.add(o);return}a()}n.subscribe(S(r,u=>{i=u,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function Qt(e){return M((t,n)=>{let r=!1;t.subscribe(S(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function xt(e){return e<=0?()=>De:M((t,n)=>{let r=0;t.subscribe(S(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function jh(e,t=ge){return e=e??bw,M((n,r)=>{let o,i=!0;n.subscribe(S(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function bw(e,t){return e===t}function is(e=Tw){return M((t,n)=>{let r=!1;t.subscribe(S(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Tw(){return new At}function Kt(e){return M((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Ot(e,t){let n=arguments.length>=2;return r=>r.pipe(e?de((o,i)=>e(o,i,r)):ge,xt(1),n?Qt(t):is(()=>new At))}function rr(e){return e<=0?()=>De:M((t,n)=>{let r=[];t.subscribe(S(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Qc(e,t){let n=arguments.length>=2;return r=>r.pipe(e?de((o,i)=>e(o,i,r)):ge,rr(1),n?Qt(t):is(()=>new At))}function Sw(){return M((e,t)=>{let n,r=!1;e.subscribe(S(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function Mw(e=1/0){let t;e&&typeof e=="object"?t=e:t={count:e};let{count:n=1/0,delay:r,resetOnSuccess:o=!1}=t;return n<=0?ge:M((i,s)=>{let a=0,c,u=()=>{let l=!1;c=i.subscribe(S(s,d=>{o&&(a=0),s.next(d)},void 0,d=>{if(a++<n){let h=()=>{c?(c.unsubscribe(),c=null,u()):l=!0};if(r!=null){let f=typeof r=="number"?os(r):$(r(d,a)),p=S(s,()=>{p.unsubscribe(),h()},()=>{s.complete()});f.subscribe(p)}else h()}else s.error(d)})),l&&(c.unsubscribe(),c=null,u())};u()})}function Kc(e,t){return M(Lh(e,t,arguments.length>=2,!0))}function Xc(e={}){let{connector:t=()=>new X,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,u=0,l=!1,d=!1,h=()=>{a?.unsubscribe(),a=void 0},f=()=>{h(),s=c=void 0,l=d=!1},p=()=>{let m=s;f(),m?.unsubscribe()};return M((m,D)=>{u++,!d&&!l&&h();let I=c=c??t();D.add(()=>{u--,u===0&&!d&&!l&&(a=Jc(p,o))}),I.subscribe(D),!s&&u>0&&(s=new Rt({next:V=>I.next(V),error:V=>{d=!0,h(),a=Jc(f,n,V),I.error(V)},complete:()=>{l=!0,h(),a=Jc(f,r),I.complete()}}),$(m).subscribe(s))})(i)}}function Jc(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Rt({next:()=>{r.unsubscribe(),e()}});return $(t(...n)).subscribe(r)}function _w(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,Xc({connector:()=>new no(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function Nw(e){return de((t,n)=>e<=n)}function eu(...e){let t=ct(e);return M((n,r)=>{(t?nr(e,n,t):nr(e,n)).subscribe(r)})}function Se(e,t){return M((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(S(r,c=>{o?.unsubscribe();let u=0,l=i++;$(e(c,l)).subscribe(o=S(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function tu(e){return M((t,n)=>{$(e).subscribe(S(n,()=>n.complete(),eo)),!n.closed&&t.subscribe(n)})}function Rw(e,t=!1){return M((n,r)=>{let o=0;n.subscribe(S(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function oe(e,t,n){let r=R(e)||t||n?{next:e,error:t,complete:n}:e;return r?M((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(S(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):ge}var nu={JSACTION:"jsaction"};var re={CLICK:"click",CLICKMOD:"clickmod",DBLCLICK:"dblclick",FOCUS:"focus",FOCUSIN:"focusin",BLUR:"blur",FOCUSOUT:"focusout",SUBMIT:"submit",KEYDOWN:"keydown",KEYPRESS:"keypress",KEYUP:"keyup",MOUSEOVER:"mouseover",MOUSEOUT:"mouseout",MOUSEENTER:"mouseenter",MOUSELEAVE:"mouseleave",POINTEROVER:"pointerover",POINTEROUT:"pointerout",POINTERENTER:"pointerenter",POINTERLEAVE:"pointerleave",ERROR:"error",LOAD:"load",TOUCHSTART:"touchstart",TOUCHEND:"touchend",TOUCHMOVE:"touchmove",TOGGLE:"toggle"},Y1=[re.MOUSEENTER,re.MOUSELEAVE,"pointerenter","pointerleave"],Aw=[re.CLICK,re.DBLCLICK,re.FOCUSIN,re.FOCUSOUT,re.KEYDOWN,re.KEYUP,re.KEYPRESS,re.MOUSEOVER,re.MOUSEOUT,re.SUBMIT,re.TOUCHSTART,re.TOUCHEND,re.TOUCHMOVE,"touchcancel","auxclick","change","compositionstart","compositionupdate","compositionend","beforeinput","input","select","copy","cut","paste","mousedown","mouseup","wheel","contextmenu","dragover","dragenter","dragleave","drop","dragstart","dragend","pointerdown","pointermove","pointerup","pointercancel","pointerover","pointerout","gotpointercapture","lostpointercapture","ended","loadedmetadata","pagehide","pageshow","visibilitychange","beforematch"],Vh=[re.FOCUS,re.BLUR,re.ERROR,re.LOAD,re.TOGGLE],Uh=e=>Vh.indexOf(e)>=0,xw=Aw.concat(Vh),Bh=e=>xw.indexOf(e)>=0;var Q1=typeof navigator<"u"&&/Macintosh/.test(navigator.userAgent);var K1=typeof navigator<"u"&&/iPhone|iPad|iPod/.test(navigator.userAgent);var J1=re.CLICK;var Hp="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",v=class extends Error{code;constructor(t,n){super(Ys(t,n)),this.code=t}};function Ow(e){return`NG0${Math.abs(e)}`}function Ys(e,t){return`${Ow(e)}${t?": "+t:""}`}var zp=Symbol("InputSignalNode#UNSET"),Pw=B(E({},Ri),{transformFn:void 0,applyValueToInputSignal(e,t){Jr(e,t)}});function qp(e,t){let n=Object.create(Pw);n.value=e,n.transformFn=t?.transform;function r(){if(Yr(n),n.value===zp){let o=null;throw new v(-950,o)}return n.value}return r[_e]=n,r}function Ro(e){return{toString:e}.toString()}var ss="__parameters__";function kw(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Gp(e,t,n){return Ro(()=>{let r=kw(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(ss)?c[ss]:Object.defineProperty(c,ss,{value:[]})[ss];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var fe=globalThis;function Z(e){for(let t in e)if(e[t]===Z)return t;throw Error("Could not find renamed property on target object.")}function Fw(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Ae(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Ae).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Iu(e,t){return e?t?`${e} ${t}`:e:t||""}var Lw=Z({__forward_ref__:Z});function Wp(e){return e.__forward_ref__=Wp,e.toString=function(){return Ae(this())},e}function ve(e){return Zp(e)?e():e}function Zp(e){return typeof e=="function"&&e.hasOwnProperty(Lw)&&e.__forward_ref__===Wp}function w(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function vt(e){return{providers:e.providers||[],imports:e.imports||[]}}function Qs(e){return $h(e,Qp)||$h(e,Kp)}function Yp(e){return Qs(e)!==null}function $h(e,t){return e.hasOwnProperty(t)?e[t]:null}function jw(e){let t=e&&(e[Qp]||e[Kp]);return t||null}function Hh(e){return e&&(e.hasOwnProperty(zh)||e.hasOwnProperty(Vw))?e[zh]:null}var Qp=Z({\u0275prov:Z}),zh=Z({\u0275inj:Z}),Kp=Z({ngInjectableDef:Z}),Vw=Z({ngInjectorDef:Z}),y=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=w({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Jp(e){return e&&!!e.\u0275providers}var Uw=Z({\u0275cmp:Z}),Bw=Z({\u0275dir:Z}),$w=Z({\u0275pipe:Z}),Hw=Z({\u0275mod:Z}),ys=Z({\u0275fac:Z}),co=Z({__NG_ELEMENT_ID__:Z}),qh=Z({__NG_ENV_ID__:Z});function Tn(e){return typeof e=="string"?e:e==null?"":String(e)}function zw(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Tn(e)}function Xp(e,t){throw new v(-200,e)}function Vl(e,t){throw new v(-201,!1)}var O=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(O||{}),Cu;function eg(){return Cu}function Re(e){let t=Cu;return Cu=e,t}function tg(e,t,n){let r=Qs(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&O.Optional)return null;if(t!==void 0)return t;Vl(e,"Injector")}var qw={},In=qw,bu="__NG_DI_FLAG__",Ds=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?Ai:In,r)}},Es="ngTempTokenPath",Gw="ngTokenPath",Ww=/\n/gm,Zw="\u0275",Gh="__source";function Yw(e,t=O.Default){if(Xr()===void 0)throw new v(-203,!1);if(Xr()===null)return tg(e,void 0,t);{let n=Xr(),r;return n instanceof Ds?r=n.injector:r=n,r.get(e,t&O.Optional?null:void 0,t)}}function C(e,t=O.Default){return(eg()||Yw)(ve(e),t)}function g(e,t=O.Default){return C(e,Ks(t))}function Ks(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Tu(e){let t=[];for(let n=0;n<e.length;n++){let r=ve(e[n]);if(Array.isArray(r)){if(r.length===0)throw new v(900,!1);let o,i=O.Default;for(let s=0;s<r.length;s++){let a=r[s],c=Qw(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(C(o,i))}else t.push(C(r))}return t}function ng(e,t){return e[bu]=t,e.prototype[bu]=t,e}function Qw(e){return e[bu]}function Kw(e,t,n,r){let o=e[Es];throw t[Gh]&&o.unshift(t[Gh]),e.message=Jw(`
`+e.message,o,n,r),e[Gw]=o,e[Es]=null,e}function Jw(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Zw?e.slice(2):e;let o=Ae(t);if(Array.isArray(t))o=t.map(Ae).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Ae(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Ww,`
  `)}`}var rg=ng(Gp("Optional"),8);var Xw=ng(Gp("SkipSelf"),4);function Sn(e,t){let n=e.hasOwnProperty(ys);return n?e[ys]:null}function eI(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function tI(e){return e.flat(Number.POSITIVE_INFINITY)}function Ul(e,t){e.forEach(n=>Array.isArray(n)?Ul(n,t):t(n))}function og(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function ws(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function nI(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function rI(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function Js(e,t,n){let r=Ao(e,t);return r>=0?e[r|1]=n:(r=~r,rI(e,r,t,n)),r}function ru(e,t){let n=Ao(e,t);if(n>=0)return e[n|1]}function Ao(e,t){return oI(e,t,1)}function oI(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var ht={},Ee=[],Mn=new y(""),ig=new y("",-1),sg=new y(""),Is=class{get(t,n=In){if(n===In){let r=new Error(`NullInjectorError: No provider for ${Ae(t)}!`);throw r.name="NullInjectorError",r}return n}};function ag(e,t){let n=e[Hw]||null;if(!n&&t===!0)throw new Error(`Type ${Ae(e)} does not have '\u0275mod' property.`);return n}function Pt(e){return e[Uw]||null}function cg(e){return e[Bw]||null}function iI(e){return e[$w]||null}function Ln(e){return{\u0275providers:e}}function sI(...e){return{\u0275providers:ug(!0,e),\u0275fromNgModule:!0}}function ug(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Ul(t,s=>{let a=s;Su(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&lg(o,i),n}function lg(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Bl(o,i=>{t(i,r)})}}function Su(e,t,n,r){if(e=ve(e),!e)return!1;let o=null,i=Hh(e),s=!i&&Pt(e);if(!i&&!s){let c=e.ngModule;if(i=Hh(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)Su(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Ul(i.imports,l=>{Su(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&lg(u,t)}if(!a){let u=Sn(o)||(()=>new o);t({provide:o,useFactory:u,deps:Ee},o),t({provide:sg,useValue:o,multi:!0},o),t({provide:Mn,useValue:()=>C(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;Bl(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function Bl(e,t){for(let n of e)Jp(n)&&(n=n.\u0275providers),Array.isArray(n)?Bl(n,t):t(n)}var aI=Z({provide:String,useValue:Z});function dg(e){return e!==null&&typeof e=="object"&&aI in e}function cI(e){return!!(e&&e.useExisting)}function uI(e){return!!(e&&e.useFactory)}function lr(e){return typeof e=="function"}function lI(e){return!!e.useClass}var Xs=new y(""),fs={},Wh={},ou;function ea(){return ou===void 0&&(ou=new Is),ou}var we=class{},po=class extends we{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,_u(t,s=>this.processProvider(s)),this.records.set(ig,or(void 0,this)),o.has("environment")&&this.records.set(we,or(void 0,this));let i=this.records.get(Xs);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(sg,Ee,O.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?Ai:In,r)}destroy(){ao(this),this._destroyed=!0;let t=P(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),P(t)}}onDestroy(t){return ao(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){ao(this);let n=Nt(this),r=Re(void 0),o;try{return t()}finally{Nt(n),Re(r)}}get(t,n=In,r=O.Default){if(ao(this),t.hasOwnProperty(qh))return t[qh](this);r=Ks(r);let o,i=Nt(this),s=Re(void 0);try{if(!(r&O.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=gI(t)&&Qs(t);u&&this.injectableDefInScope(u)?c=or(Mu(t),fs):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&O.Self?ea():this.parent;return n=r&O.Optional&&n===In?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Es]=a[Es]||[]).unshift(Ae(t)),i)throw a;return Kw(a,t,"R3InjectorError",this.source)}else throw a}finally{Re(s),Nt(i)}}resolveInjectorInitializers(){let t=P(null),n=Nt(this),r=Re(void 0),o;try{let i=this.get(Mn,Ee,O.Self);for(let s of i)s()}finally{Nt(n),Re(r),P(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Ae(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=ve(t);let n=lr(t)?t:ve(t&&t.provide),r=fI(t);if(!lr(t)&&t.multi===!0){let o=this.records.get(n);o||(o=or(void 0,fs,!0),o.factory=()=>Tu(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=P(null);try{return n.value===Wh?Xp(Ae(t)):n.value===fs&&(n.value=Wh,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&pI(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{P(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ve(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Mu(e){let t=Qs(e),n=t!==null?t.factory:Sn(e);if(n!==null)return n;if(e instanceof y)throw new v(204,!1);if(e instanceof Function)return dI(e);throw new v(204,!1)}function dI(e){if(e.length>0)throw new v(204,!1);let n=jw(e);return n!==null?()=>n.factory(e):()=>new e}function fI(e){if(dg(e))return or(void 0,e.useValue);{let t=fg(e);return or(t,fs)}}function fg(e,t,n){let r;if(lr(e)){let o=ve(e);return Sn(o)||Mu(o)}else if(dg(e))r=()=>ve(e.useValue);else if(uI(e))r=()=>e.useFactory(...Tu(e.deps||[]));else if(cI(e))r=(o,i)=>C(ve(e.useExisting),i!==void 0&&i&O.Optional?O.Optional:void 0);else{let o=ve(e&&(e.useClass||e.provide));if(hI(e))r=()=>new o(...Tu(e.deps));else return Sn(o)||Mu(o)}return r}function ao(e){if(e.destroyed)throw new v(205,!1)}function or(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function hI(e){return!!e.deps}function pI(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function gI(e){return typeof e=="function"||typeof e=="object"&&e instanceof y}function _u(e,t){for(let n of e)Array.isArray(n)?_u(n,t):n&&Jp(n)?_u(n.\u0275providers,t):t(n)}function xe(e,t){let n;e instanceof po?(ao(e),n=e):n=new Ds(e);let r,o=Nt(n),i=Re(void 0);try{return t()}finally{Nt(o),Re(i)}}function $l(){return eg()!==void 0||Xr()!=null}function ta(e){if(!$l())throw new v(-203,!1)}function mI(e){let t=fe.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function vI(e){return typeof e=="function"}var ce=0,T=1,_=2,he=3,Qe=4,Oe=5,Ke=6,go=7,ie=8,dr=9,kt=10,q=11,mo=12,Zh=13,Er=14,Ie=15,_n=16,ir=17,Ft=18,na=19,hg=20,Jt=21,iu=22,Nn=23,$e=24,cr=25,L=26,Hl=1,vo=6,Lt=7,Cs=8,fr=9,ye=10;function Xt(e){return Array.isArray(e)&&typeof e[Hl]=="object"}function He(e){return Array.isArray(e)&&e[Hl]===!0}function zl(e){return(e.flags&4)!==0}function tn(e){return e.componentOffset>-1}function ra(e){return(e.flags&1)===1}function pt(e){return!!e.template}function hr(e){return(e[_]&512)!==0}function pg(e){return(e.type&16)===16}function yI(e){return(e[_]&32)===32}function wr(e){return(e[_]&256)===256}var Nu=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function gg(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var jn=(()=>{let e=()=>mg;return e.ngInherit=!0,e})();function mg(e){return e.type.prototype.ngOnChanges&&(e.setInput=EI),DI}function DI(){let e=yg(this),t=e?.current;if(t){let n=e.previous;if(n===ht)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function EI(e,t,n,r,o){let i=this.declaredInputs[r],s=yg(e)||wI(e,{previous:ht,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Nu(u&&u.currentValue,n,c===ht),gg(e,t,o,n)}var vg="__ngSimpleChanges__";function yg(e){return e[vg]||null}function wI(e,t){return e[vg]=t}var Yh=null;var Y=function(e,t=null,n){Yh?.(e,t,n)},Dg="svg",II="math";function K(e){for(;Array.isArray(e);)e=e[ce];return e}function Eg(e){for(;Array.isArray(e);){if(typeof e[Hl]=="object")return e;e=e[ce]}return null}function wg(e,t){return K(t[e])}function tt(e,t){return K(t[e.index])}function ql(e,t){return e.data[t]}function Gl(e,t){return e[t]}function Ig(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function gt(e,t){let n=t[e];return Xt(n)?n:n[ce]}function CI(e){return(e[_]&4)===4}function Wl(e){return(e[_]&128)===128}function bI(e){return He(e[he])}function en(e,t){return t==null?null:e[t]}function Cg(e){e[ir]=0}function bg(e){e[_]&1024||(e[_]|=1024,Wl(e)&&Ir(e))}function TI(e,t){for(;e>0;)t=t[Er],e--;return t}function oa(e){return!!(e[_]&9216||e[$e]?.dirty)}function Ru(e){e[kt].changeDetectionScheduler?.notify(8),e[_]&64&&(e[_]|=1024),oa(e)&&Ir(e)}function Ir(e){e[kt].changeDetectionScheduler?.notify(0);let t=Rn(e);for(;t!==null&&!(t[_]&8192||(t[_]|=8192,!Wl(t)));)t=Rn(t)}function Tg(e,t){if(wr(e))throw new v(911,!1);e[Jt]===null&&(e[Jt]=[]),e[Jt].push(t)}function SI(e,t){if(e[Jt]===null)return;let n=e[Jt].indexOf(t);n!==-1&&e[Jt].splice(n,1)}function Rn(e){let t=e[he];return He(t)?t[he]:t}function Zl(e){return e[go]??=[]}function Yl(e){return e.cleanup??=[]}function MI(e,t,n,r){let o=Zl(t);o.push(n),e.firstCreatePass&&Yl(e).push(r,o.length-1)}var A={lFrame:Rg(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Au=!1;function _I(){return A.lFrame.elementDepthCount}function NI(){A.lFrame.elementDepthCount++}function RI(){A.lFrame.elementDepthCount--}function Ql(){return A.bindingsEnabled}function Cr(){return A.skipHydrationRootTNode!==null}function AI(e){return A.skipHydrationRootTNode===e}function xI(e){A.skipHydrationRootTNode=e}function OI(){A.skipHydrationRootTNode=null}function N(){return A.lFrame.lView}function J(){return A.lFrame.tView}function Hj(e){return A.lFrame.contextLView=e,e[ie]}function zj(e){return A.lFrame.contextLView=null,e}function Ce(){let e=Sg();for(;e!==null&&e.type===64;)e=e.parent;return e}function Sg(){return A.lFrame.currentTNode}function PI(){let e=A.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function nn(e,t){let n=A.lFrame;n.currentTNode=e,n.isParent=t}function Kl(){return A.lFrame.isParent}function Jl(){A.lFrame.isParent=!1}function kI(){return A.lFrame.contextLView}function Mg(){return Au}function bs(e){let t=Au;return Au=e,t}function Xl(){let e=A.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function FI(){return A.lFrame.bindingIndex}function LI(e){return A.lFrame.bindingIndex=e}function Vn(){return A.lFrame.bindingIndex++}function ed(e){let t=A.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function jI(){return A.lFrame.inI18n}function VI(e,t){let n=A.lFrame;n.bindingIndex=n.bindingRootIndex=e,xu(t)}function UI(){return A.lFrame.currentDirectiveIndex}function xu(e){A.lFrame.currentDirectiveIndex=e}function BI(e){let t=A.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function td(){return A.lFrame.currentQueryIndex}function ia(e){A.lFrame.currentQueryIndex=e}function $I(e){let t=e[T];return t.type===2?t.declTNode:t.type===1?e[Oe]:null}function _g(e,t,n){if(n&O.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&O.Host);)if(o=$I(i),o===null||(i=i[Er],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=A.lFrame=Ng();return r.currentTNode=t,r.lView=e,!0}function nd(e){let t=Ng(),n=e[T];A.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Ng(){let e=A.lFrame,t=e===null?null:e.child;return t===null?Rg(e):t}function Rg(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Ag(){let e=A.lFrame;return A.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var xg=Ag;function rd(){let e=Ag();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function HI(e){return(A.lFrame.contextLView=TI(e,A.lFrame.contextLView))[ie]}function Ut(){return A.lFrame.selectedIndex}function An(e){A.lFrame.selectedIndex=e}function sa(){let e=A.lFrame;return ql(e.tView,e.selectedIndex)}function qj(){A.lFrame.currentNamespace=Dg}function Gj(){zI()}function zI(){A.lFrame.currentNamespace=null}function Og(){return A.lFrame.currentNamespace}var Pg=!0;function aa(){return Pg}function rn(e){Pg=e}function qI(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=mg(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function od(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function hs(e,t,n){kg(e,t,3,n)}function ps(e,t,n,r){(e[_]&3)===n&&kg(e,t,n,r)}function su(e,t){let n=e[_];(n&3)===t&&(n&=16383,n+=1,e[_]=n)}function kg(e,t,n,r){let o=r!==void 0?e[ir]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[ir]+=65536),(a<i||i==-1)&&(GI(e,n,t,c),e[ir]=(e[ir]&**********)+c+2),c++}function Qh(e,t){Y(4,e,t);let n=P(null);try{t.call(e)}finally{P(n),Y(5,e,t)}}function GI(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[_]>>14<e[ir]>>16&&(e[_]&3)===t&&(e[_]+=16384,Qh(a,i)):Qh(a,i)}var ur=-1,xn=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function Fg(e){return e!=null&&typeof e=="object"&&(e.insertBeforeIndex===null||typeof e.insertBeforeIndex=="number"||Array.isArray(e.insertBeforeIndex))}function WI(e){return!!(e.type&128)}function ZI(e){return(e.flags&8)!==0}function YI(e){return(e.flags&16)!==0}function QI(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];KI(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Lg(e){return e===3||e===4||e===6}function KI(e){return e.charCodeAt(0)===64}function pr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Kh(e,n,o,null,t[++r]):Kh(e,n,o,null,null))}}return e}function Kh(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function jg(e){return e!==ur}function Ts(e){return e&32767}function JI(e){return e>>16}function Ss(e,t){let n=JI(e),r=t;for(;n>0;)r=r[Er],n--;return r}var Ou=!0;function Ms(e){let t=Ou;return Ou=e,t}var XI=256,Vg=XI-1,Ug=5,eC=0,ft={};function tC(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(co)&&(r=n[co]),r==null&&(r=n[co]=eC++);let o=r&Vg,i=1<<o;t.data[e+(o>>Ug)]|=i}function _s(e,t){let n=Bg(e,t);if(n!==-1)return n;let r=t[T];r.firstCreatePass&&(e.injectorIndex=t.length,au(r.data,e),au(t,null),au(r.blueprint,null));let o=id(e,t),i=e.injectorIndex;if(jg(o)){let s=Ts(o),a=Ss(o,t),c=a[T].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function au(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Bg(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function id(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Gg(o),r===null)return ur;if(n++,o=o[Er],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return ur}function Pu(e,t,n){tC(e,t,n)}function nC(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Lg(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function $g(e,t,n){if(n&O.Optional||e!==void 0)return e;Vl(t,"NodeInjector")}function Hg(e,t,n,r){if(n&O.Optional&&r===void 0&&(r=null),(n&(O.Self|O.Host))===0){let o=e[dr],i=Re(void 0);try{return o?o.get(t,r,n&O.Optional):tg(t,r,n&O.Optional)}finally{Re(i)}}return $g(r,t,n)}function zg(e,t,n,r=O.Default,o){if(e!==null){if(t[_]&2048&&!(r&O.Self)){let s=sC(e,t,n,r,ft);if(s!==ft)return s}let i=qg(e,t,n,r,ft);if(i!==ft)return i}return Hg(t,n,r,o)}function qg(e,t,n,r,o){let i=oC(n);if(typeof i=="function"){if(!_g(t,e,r))return r&O.Host?$g(o,n,r):Hg(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&O.Optional))Vl(n);else return s}finally{xg()}}else if(typeof i=="number"){let s=null,a=Bg(e,t),c=ur,u=r&O.Host?t[Ie][Oe]:null;for((a===-1||r&O.SkipSelf)&&(c=a===-1?id(e,t):t[a+8],c===ur||!Xh(r,!1)?a=-1:(s=t[T],a=Ts(c),t=Ss(c,t)));a!==-1;){let l=t[T];if(Jh(i,a,l.data)){let d=rC(a,t,n,s,r,u);if(d!==ft)return d}c=t[a+8],c!==ur&&Xh(r,t[T].data[a+8]===u)&&Jh(i,a,t)?(s=l,a=Ts(c),t=Ss(c,t)):a=-1}}return o}function rC(e,t,n,r,o,i){let s=t[T],a=s.data[e+8],c=r==null?tn(a)&&Ou:r!=s&&(a.type&3)!==0,u=o&O.Host&&i===a,l=gs(a,s,n,c,u);return l!==null?yo(t,s,l,a,o):ft}function gs(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let p=s[f];if(f<c&&n===p||f>=c&&p.type===n)return f}if(o){let f=s[c];if(f&&pt(f)&&f.type===n)return c}return null}function yo(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof xn){let a=i;a.resolving&&Xp(zw(s[n]));let c=Ms(a.canSeeViewProviders);a.resolving=!0;let u,l=a.injectImpl?Re(a.injectImpl):null,d=_g(e,r,O.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&qI(n,s[n],t)}finally{l!==null&&Re(l),Ms(c),a.resolving=!1,xg()}}return i}function oC(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(co)?e[co]:void 0;return typeof t=="number"?t>=0?t&Vg:iC:t}function Jh(e,t,n){let r=1<<e;return!!(n[t+(e>>Ug)]&r)}function Xh(e,t){return!(e&O.Self)&&!(e&O.Host&&t)}var Cn=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return zg(this._tNode,this._lView,t,Ks(r),n)}};function iC(){return new Cn(Ce(),N())}function sd(e){return Ro(()=>{let t=e.prototype.constructor,n=t[ys]||ku(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[ys]||ku(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function ku(e){return Zp(e)?()=>{let t=ku(ve(e));return t&&t()}:Sn(e)}function sC(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[_]&2048&&!hr(s);){let a=qg(i,s,n,r|O.Self,ft);if(a!==ft)return a;let c=i.parent;if(!c){let u=s[hg];if(u){let l=u.get(n,ft,r);if(l!==ft)return l}c=Gg(s),s=s[Er]}i=c}return o}function Gg(e){let t=e[T],n=t.type;return n===2?t.declTNode:n===1?e[Oe]:null}function ca(e){return nC(Ce(),e)}function ep(e,t=null,n=null,r){let o=Wg(e,t,n,r);return o.resolveInjectorInitializers(),o}function Wg(e,t=null,n=null,r,o=new Set){let i=[n||Ee,sI(e)];return r=r||(typeof e=="object"?void 0:Ae(e)),new po(i,t||ea(),r||null,o)}var pe=class e{static THROW_IF_NOT_FOUND=In;static NULL=new Is;static create(t,n){if(Array.isArray(t))return ep({name:""},n,t,"");{let r=t.name??"";return ep({name:r},t.parent,t.providers,r)}}static \u0275prov=w({token:e,providedIn:"any",factory:()=>C(ig)});static __NG_ELEMENT_ID__=-1};var tp=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>ca(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},aC=new y("");aC.__NG_ELEMENT_ID__=e=>{let t=Ce();if(t===null)throw new v(204,!1);if(t.type&2)return t.value;if(e&O.Optional)return null;throw new v(204,!1)};var Zg=!1,Bt=(()=>{class e{static __NG_ELEMENT_ID__=cC;static __NG_ENV_ID__=n=>n}return e})(),Ns=class extends Bt{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return wr(n)?(t(),()=>{}):(Tg(n,t),()=>SI(n,t))}};function cC(){return new Ns(N())}var On=class{},ad=new y("",{providedIn:"root",factory:()=>!1});var Yg=new y(""),Qg=new y(""),$t=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new me(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})();var Fu=class extends X{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,$l()&&(this.destroyRef=g(Bt,{optional:!0})??void 0,this.pendingTasks=g($t,{optional:!0})??void 0)}emit(t){let n=P(null);try{super.next(t)}finally{P(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof te&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},Me=Fu;function Do(...e){}function Kg(e){let t,n;function r(){e=Do;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function np(e){return queueMicrotask(()=>e()),()=>{e=Do}}var cd="isAngularZone",Rs=cd+"_ID",uC=0,z=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new Me(!1);onMicrotaskEmpty=new Me(!1);onStable=new Me(!1);onError=new Me(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Zg}=t;if(typeof Zone>"u")throw new v(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,fC(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(cd)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new v(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new v(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,lC,Do,Do);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},lC={};function ud(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function dC(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Kg(()=>{e.callbackScheduled=!1,Lu(e),e.isCheckStableRunning=!0,ud(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Lu(e)}function fC(e){let t=()=>{dC(e)},n=uC++;e._inner=e._inner.fork({name:"angular",properties:{[cd]:!0,[Rs]:n,[Rs+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(hC(c))return r.invokeTask(i,s,a,c);try{return rp(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),op(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return rp(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!pC(c)&&t(),op(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Lu(e),ud(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Lu(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function rp(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function op(e){e._nesting--,ud(e)}var As=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new Me;onMicrotaskEmpty=new Me;onStable=new Me;onError=new Me;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function hC(e){return Jg(e,"__ignore_ng_zone__")}function pC(e){return Jg(e,"__scheduler_tick__")}function Jg(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function gC(e="zone.js",t){return e==="noop"?new As:e==="zone.js"?new z(t):e}var mt=class{_console=console;handleError(t){this._console.error("ERROR",t)}},mC=new y("",{providedIn:"root",factory:()=>{let e=g(z),t=g(mt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function ip(e,t){return qp(e,t)}function vC(e){return qp(zp,e)}var Xg=(ip.required=vC,ip);function yC(){return br(Ce(),N())}function br(e,t){return new yt(tt(e,t))}var yt=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=yC}return e})();function em(e){return e instanceof yt?e.nativeElement:e}function DC(e){return typeof e=="function"&&e[_e]!==void 0}function tm(e,t){let n=kc(e,t?.equal),r=n[_e];return n.set=o=>Jr(r,o),n.update=o=>Fc(r,o),n.asReadonly=EC.bind(n),n}function EC(){let e=this[_e];if(e.readonlyFn===void 0){let t=()=>this();t[_e]=e,e.readonlyFn=t}return e.readonlyFn}function nm(e){return DC(e)&&typeof e.set=="function"}function wC(){return this._results[Symbol.iterator]()}var ju=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new X}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=tI(t);(this._changesDetected=!eI(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=wC},Eo="ngSkipHydration",IC="ngskiphydration";function ld(e){let t=e.mergedAttrs;if(t===null)return!1;for(let n=0;n<t.length;n+=2){let r=t[n];if(typeof r=="number")return!1;if(typeof r=="string"&&r.toLowerCase()===IC)return!0}return!1}function rm(e){return e.hasAttribute(Eo)}function wo(e){return(e.flags&128)===128}function Io(e){if(wo(e))return!0;let t=e.parent;for(;t;){if(wo(e)||ld(t))return!0;t=t.parent}return!1}function CC(e){return wo(e)||ld(e)||Io(e)}var om=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(om||{}),im=new Map,bC=0;function TC(){return bC++}function SC(e){im.set(e[na],e)}function Vu(e){im.delete(e[na])}var sp="__ngContext__";function Tr(e,t){Xt(t)?(e[sp]=t[na],SC(t)):e[sp]=t}function sm(e){return cm(e[mo])}function am(e){return cm(e[Qe])}function cm(e){for(;e!==null&&!He(e);)e=e[Qe];return e}var Uu;function MC(e){Uu=e}function dd(){if(Uu!==void 0)return Uu;if(typeof document<"u")return document;throw new v(210,!1)}var ua=new y("",{providedIn:"root",factory:()=>_C}),_C="ng",um=new y(""),xo=new y("",{providedIn:"platform",factory:()=>"unknown"});var Wj=new y(""),fd=new y("",{providedIn:"root",factory:()=>dd().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});function NC(){return new Oo}var Oo=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:NC});store={};onSerializeCallbacks={};get(n,r){return this.store[n]!==void 0?this.store[n]:r}set(n,r){this.store[n]=r}remove(n){delete this.store[n]}hasKey(n){return this.store.hasOwnProperty(n)}get isEmpty(){return Object.keys(this.store).length===0}onSerialize(n,r){this.onSerializeCallbacks[n]=r}toJson(){for(let n in this.onSerializeCallbacks)if(this.onSerializeCallbacks.hasOwnProperty(n))try{this.store[n]=this.onSerializeCallbacks[n]()}catch(r){console.warn("Exception in onSerialize callback: ",r)}return JSON.stringify(this.store).replace(/</g,"\\u003C")}}return e})();var hd="h",pd="b",lm="f",dm="n",Bu="e",$u="t",Co="c",xs="x",bn="r",Hu="i",zu="n",uo="d",ap="l",fm="di",cp="s",RC="p",AC="t",up=new y(""),hm=!1,xC=new y("",{providedIn:"root",factory:()=>hm}),OC=new y(""),pm=new y(""),PC=!1,kC=new y("");var gd=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(gd||{}),Sr=new y(""),lp=new Set;function ze(e){lp.has(e)||(lp.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var md=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=FC}return e})();function FC(){return new md(N(),Ce())}var sr=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(sr||{}),gm=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),LC=[sr.EarlyRead,sr.Write,sr.MixedReadWrite,sr.Read],jC=(()=>{class e{ngZone=g(z);scheduler=g(On);errorHandler=g(mt,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){g(Sr,{optional:!0})}execute(){let n=this.sequences.size>0;n&&Y(16),this.executing=!0;for(let r of LC)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&Y(17)}register(n){let{view:r}=n;r!==void 0?((r[cr]??=[]).push(n),Ir(r),r[_]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(gd.AFTER_NEXT_RENDER,n):n()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),qu=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[cr];t&&(this.view[cr]=t.filter(n=>n!==this))}};function VC(e,t){!t?.injector&&ta(VC);let n=t?.injector??g(pe);return vm}function vd(e,t){!t?.injector&&ta(vd);let n=t?.injector??g(pe);return vm}function UC(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function mm(e,t,n,r){let o=t.get(gm);o.impl??=t.get(jC);let i=t.get(Sr,null,{optional:!0}),s=n?.phase??sr.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Bt):null,c=t.get(md,null,{optional:!0}),u=new qu(o.impl,UC(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(u),u}var vm={destroy(){}};var dp=1;function yd(e){return e+1}function BC(e,t){let n=e[T],r=yd(t.index);return e[r]}function ym(e,t){let n=yd(t.index);return e.data[n]}function $C(e){return e!==null&&typeof e=="object"&&typeof e.primaryTmplIndex=="number"}function HC(e,t){let n=null,r=yd(t.index);return L<r&&r<e.bindingStartIndex&&(n=ym(e,t)),!!n&&$C(n)}var zC=["click","keydown"],qC=["mouseenter","mouseover","focusin"];var GC="ngb";function Dm(e,t,n=null){if(t.length===0||e.nodeType!==Node.ELEMENT_NODE)return;let r=e.getAttribute(nu.JSACTION),o=t.reduce((s,a)=>(r?.indexOf(a)??-1)===-1?s+a+":;":s,"");e.setAttribute(nu.JSACTION,`${r??""}${o}`);let i=n??"";i!==""&&o.length>0&&e.setAttribute(GC,i)}var WC=(e,t,n,r)=>{};function ZC(e,t,n,r){WC(e,t,n,r)}var YC="__nghData__",Em=YC,QC="__nghDeferData__",KC=QC,lo="ngh",JC="nghm",wm=()=>null;function XC(e,t,n=!1){let r=e.getAttribute(lo);if(r==null)return null;let[o,i]=r.split("|");if(r=n?i:o,!r)return null;let s=i?`|${i}`:"",a=n?o:s,c={};if(r!==""){let l=t.get(Oo,null,{optional:!0});l!==null&&(c=l.get(Em,[])[Number(r)])}let u={data:c,firstChild:e.firstChild??null};return n&&(u.firstChild=e,la(u,0,e.nextSibling)),a?e.setAttribute(lo,a):e.removeAttribute(lo),u}function eb(){wm=XC}function Im(e,t,n=!1){return wm(e,t,n)}function tb(e){let t=e._lView;return t[T].type===2?null:(hr(t)&&(t=t[L]),t)}function nb(e){return e.textContent?.replace(/\s/gm,"")}function rb(e){let t=dd(),n=t.createNodeIterator(e,NodeFilter.SHOW_COMMENT,{acceptNode(i){let s=nb(i);return s==="ngetn"||s==="ngtns"?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}}),r,o=[];for(;r=n.nextNode();)o.push(r);for(let i of o)i.textContent==="ngetn"?i.replaceWith(t.createTextNode("")):i.remove()}function la(e,t,n){e.segmentHeads??={},e.segmentHeads[t]=n}function Gu(e,t){return e.segmentHeads?.[t]??null}function ob(e){return e.get(kC,!1,{optional:!0})}function ib(e,t){let n=e.data,r=n[Bu]?.[t]??null;return r===null&&n[Co]?.[t]&&(r=Dd(e,t)),r}function Cm(e,t){return e.data[Co]?.[t]??null}function Dd(e,t){let n=Cm(e,t)??[],r=0;for(let o of n)r+=o[bn]*(o[xs]??1);return r}function sb(e){if(typeof e.disconnectedNodes>"u"){let t=e.data[uo];e.disconnectedNodes=t?new Set(t):null}return e.disconnectedNodes}function Po(e,t){if(typeof e.disconnectedNodes>"u"){let n=e.data[uo];e.disconnectedNodes=n?new Set(n):null}return!!sb(e)?.has(t)}function bm(e,t){let n=t,r=e.corruptedTextNodes;n.textContent===""?r.set(n,"ngetn"):n.nextSibling?.nodeType===Node.TEXT_NODE&&r.set(n,"ngtns")}function ab(e){let t=[];return e!==null&&(e.has(4)&&t.push(...qC),e.has(3)&&t.push(...zC)),t}function cu(e){return!!e&&e.nodeType===Node.COMMENT_NODE&&e.textContent?.trim()===JC}function fp(e){for(;e&&e.nodeType===Node.TEXT_NODE;)e=e.previousSibling;return e}function cb(e){for(let r of e.body.childNodes)if(cu(r))return;let t=fp(e.body.previousSibling);if(cu(t))return;let n=fp(e.head.lastChild);if(!cu(n))throw new v(-507,!1)}function Tm(e,t){let n=e.contentQueries;if(n!==null){let r=P(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];ia(i),a.contentQueries(2,t[s],s)}}}finally{P(r)}}}function Wu(e,t,n){ia(0);let r=P(null);try{t(e,n)}finally{P(r)}}function Ed(e,t,n){if(zl(t)){let r=P(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{P(r)}}}var Je=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Je||{}),as;function ub(){if(as===void 0&&(as=null,fe.trustedTypes))try{as=fe.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return as}function da(e){return ub()?.createHTML(e)||e}var cs;function lb(){if(cs===void 0&&(cs=null,fe.trustedTypes))try{cs=fe.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return cs}function hp(e){return lb()?.createScriptURL(e)||e}var jt=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Hp})`}},Zu=class extends jt{getTypeName(){return"HTML"}},Yu=class extends jt{getTypeName(){return"Style"}},Qu=class extends jt{getTypeName(){return"Script"}},Ku=class extends jt{getTypeName(){return"URL"}},Ju=class extends jt{getTypeName(){return"ResourceURL"}};function nt(e){return e instanceof jt?e.changingThisBreaksApplicationSecurity:e}function on(e,t){let n=db(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Hp})`)}return n===t}function db(e){return e instanceof jt&&e.getTypeName()||null}function Sm(e){return new Zu(e)}function Mm(e){return new Yu(e)}function _m(e){return new Qu(e)}function Nm(e){return new Ku(e)}function Rm(e){return new Ju(e)}function fb(e){let t=new el(e);return hb()?new Xu(t):t}var Xu=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(da(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},el=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=da(t),n}};function hb(){try{return!!new window.DOMParser().parseFromString(da(""),"text/html")}catch{return!1}}var pb=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function fa(e){return e=String(e),e.match(pb)?e:"unsafe:"+e}function Ht(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function ko(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Am=Ht("area,br,col,hr,img,wbr"),xm=Ht("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Om=Ht("rp,rt"),gb=ko(Om,xm),mb=ko(xm,Ht("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),vb=ko(Om,Ht("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),pp=ko(Am,mb,vb,gb),Pm=Ht("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),yb=Ht("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Db=Ht("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Eb=ko(Pm,yb,Db),wb=Ht("script,style,template"),tl=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=bb(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Cb(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=gp(t).toLowerCase();if(!pp.hasOwnProperty(n))return this.sanitizedSomething=!0,!wb.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Eb.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Pm[a]&&(c=fa(c)),this.buf.push(" ",s,'="',mp(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=gp(t).toLowerCase();pp.hasOwnProperty(n)&&!Am.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(mp(t))}};function Ib(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Cb(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw km(t);return t}function bb(e){let t=e.firstChild;if(t&&Ib(e,t))throw km(t);return t}function gp(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function km(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var Tb=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Sb=/([^\#-~ |!])/g;function mp(e){return e.replace(/&/g,"&amp;").replace(Tb,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(Sb,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var us;function Fm(e,t){let n=null;try{us=us||fb(e);let r=t?String(t):"";n=us.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=us.getInertBodyElement(r)}while(r!==i);let a=new tl().sanitizeChildren(vp(n)||n);return da(a)}finally{if(n){let r=vp(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function vp(e){return"content"in e&&Mb(e)?e.content:null}function Mb(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Dt=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Dt||{});function _b(e){let t=jm();return t?t.sanitize(Dt.URL,e)||"":on(e,"URL")?nt(e):fa(Tn(e))}function Nb(e){let t=jm();if(t)return hp(t.sanitize(Dt.RESOURCE_URL,e)||"");if(on(e,"ResourceURL"))return hp(nt(e));throw new v(904,!1)}function Rb(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?Nb:_b}function Lm(e,t,n){return Rb(t,n)(e)}function jm(){let e=N();return e&&e[kt].sanitizer}var Ab=/^>|^->|<!--|-->|--!>|<!-$/g,xb=/(<|>)/g,Ob="\u200B$1\u200B";function Pb(e){return e.replace(Ab,t=>t.replace(xb,Ob))}function Zj(e){return e.ownerDocument.defaultView}function Yj(e){return e.ownerDocument}function kb(e){return e.ownerDocument.body}function Vm(e){return e instanceof Function?e():e}function Fb(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Um="ng-template";function Lb(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Fb(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(wd(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function wd(e){return e.type===4&&e.value!==Um}function jb(e,t,n){let r=e.type===4&&!n?Um:e.value;return t===r}function Vb(e,t,n){let r=4,o=e.attrs,i=o!==null?$b(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Ze(r)&&!Ze(c))return!1;if(s&&Ze(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!jb(e,c,n)||c===""&&t.length===1){if(Ze(r))return!1;s=!0}}else if(r&8){if(o===null||!Lb(e,o,c,n)){if(Ze(r))return!1;s=!0}}else{let u=t[++a],l=Ub(c,o,wd(e),n);if(l===-1){if(Ze(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(Ze(r))return!1;s=!0}}}}return Ze(r)||s}function Ze(e){return(e&1)===0}function Ub(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Hb(t,e)}function Bm(e,t,n=!1){for(let r=0;r<t.length;r++)if(Vb(e,t[r],n))return!0;return!1}function Bb(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function $b(e){for(let t=0;t<e.length;t++){let n=e[t];if(Lg(n))return t}return e.length}function Hb(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function zb(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function yp(e,t){return e?":not("+t.trim()+")":t}function qb(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ze(s)&&(t+=yp(i,o),o=""),r=s,i=i||!Ze(r);n++}return o!==""&&(t+=yp(i,o)),t}function Gb(e){return e.map(qb).join(",")}function Wb(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Ze(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var je={};function $m(e,t){return e.createText(t)}function Zb(e,t,n){e.setValue(t,n)}function Hm(e,t){return e.createComment(Pb(t))}function Id(e,t,n){return e.createElement(t,n)}function Os(e,t,n,r,o){e.insertBefore(t,n,r,o)}function zm(e,t,n){e.appendChild(t,n)}function Dp(e,t,n,r,o){r!==null?Os(e,t,n,r,o):zm(e,t,n)}function qm(e,t,n){e.removeChild(null,t,n)}function Gm(e){e.textContent=""}function Yb(e,t,n){e.setAttribute(t,"style",n)}function Qb(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Wm(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&QI(e,t,r),o!==null&&Qb(e,t,o),i!==null&&Yb(e,t,i)}function Cd(e,t,n,r,o,i,s,a,c,u,l){let d=L+r,h=d+o,f=Kb(d,h),p=typeof u=="function"?u():u;return f[T]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:p,incompleteFirstPass:!1,ssrId:l}}function Kb(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:je);return n}function Jb(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Cd(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function bd(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[ce]=o,d[_]=r|4|128|8|64|1024,(u!==null||e&&e[_]&2048)&&(d[_]|=2048),Cg(d),d[he]=d[Er]=e,d[ie]=n,d[kt]=s||e&&e[kt],d[q]=a||e&&e[q],d[dr]=c||e&&e[dr]||null,d[Oe]=i,d[na]=TC(),d[Ke]=l,d[hg]=u,d[Ie]=t.type==2?e[Ie]:d,d}function Xb(e,t,n){let r=tt(t,e),o=Jb(n),i=e[kt].rendererFactory,s=Td(e,bd(e,o,null,Zm(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Zm(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Ym(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Td(e,t){return e[mo]?e[Zh][Qe]=t:e[mo]=t,e[Zh]=t,t}function Qj(e=1){Qm(J(),N(),Ut()+e,!1)}function Qm(e,t,n,r){if(!r)if((t[_]&3)===3){let i=e.preOrderCheckHooks;i!==null&&hs(t,i,n)}else{let i=e.preOrderHooks;i!==null&&ps(t,i,0,n)}An(n)}var ha=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(ha||{});function nl(e,t,n,r){let o=P(null);try{let[i,s,a]=e.inputs[n],c=null;(s&ha.SignalBased)!==0&&(c=t[i][_e]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):gg(t,c,i,r)}finally{P(o)}}function Km(e,t,n,r,o){let i=Ut(),s=r&2;try{An(-1),s&&t.length>L&&Qm(e,t,L,!1),Y(s?2:0,o),n(r,o)}finally{An(i),Y(s?3:1,o)}}function pa(e,t,n){sT(e,t,n),(n.flags&64)===64&&aT(e,t,n)}function Sd(e,t,n=tt){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function eT(e,t,n,r){let i=r.get(xC,hm)||n===Je.ShadowDom,s=e.selectRootElement(t,i);return tT(s),s}function tT(e){Jm(e)}var Jm=()=>null;function nT(e){rm(e)?Gm(e):rb(e)}function rT(){Jm=nT}function oT(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Md(e,t,n,r,o,i,s,a){if(!a&&Nd(t,e,n,r,o)){tn(t)&&iT(n,t.index);return}if(t.type&3){let c=tt(t,n);r=oT(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function iT(e,t){let n=gt(t,e);n[_]&16||(n[_]|=64)}function sT(e,t,n){let r=n.directiveStart,o=n.directiveEnd;tn(n)&&Xb(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||_s(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=yo(t,e,s,n);if(Tr(c,t),i!==null&&dT(t,s-r,c,a,n,i),pt(a)){let u=gt(n.index,t);u[ie]=yo(t,e,s,n)}}}function aT(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=UI();try{An(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];xu(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&cT(c,u)}}finally{An(-1),xu(s)}}function cT(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function _d(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];Bm(t,i.selectors,!1)&&(r??=[],pt(i)?r.unshift(i):r.push(i))}return r}function uT(e,t,n,r,o,i){let s=tt(e,t);lT(t[q],s,i,e.value,n,r,o)}function lT(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Tn(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function dT(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];nl(r,n,c,u)}}function fT(e,t){let n=e[dr],r=n?n.get(mt,null):null;r&&r.handleError(t)}function Nd(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=t.data[u];nl(d,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];nl(l,u,r,o),a=!0}return a}function hT(e,t){let n=gt(t,e),r=n[T];pT(r,n);let o=n[ce];o!==null&&n[Ke]===null&&(n[Ke]=Im(o,n[dr])),Y(18),Rd(r,n,n[ie]),Y(19,n[ie])}function pT(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Rd(e,t,n){nd(t);try{let r=e.viewQuery;r!==null&&Wu(1,r,n);let o=e.template;o!==null&&Km(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Ft]?.finishViewCreation(e),e.staticContentQueries&&Tm(e,t),e.staticViewQueries&&Wu(2,e.viewQuery,n);let i=e.components;i!==null&&gT(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[_]&=-5,rd()}}function gT(e,t){for(let n=0;n<t.length;n++)hT(e,t[n])}function Fo(e,t,n,r){let o=P(null);try{let i=t.tView,a=e[_]&4096?4096:16,c=bd(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[_n]=u;let l=e[Ft];return l!==null&&(c[Ft]=l.createEmbeddedView(i)),Rd(i,c,n),c}finally{P(o)}}function gr(e,t){return!t||t.firstChild===null||wo(e)}var mT;function Ad(e,t){return mT(e,t)}var Vt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Vt||{});function sn(e){return(e.flags&32)===32}function ar(e,t,n,r,o){if(r!=null){let i,s=!1;He(r)?i=r:Xt(r)&&(s=!0,r=r[ce]);let a=K(r);e===0&&n!==null?o==null?zm(t,n,a):Os(t,n,a,o||null,!0):e===1&&n!==null?Os(t,n,a,o||null,!0):e===2?qm(t,a,s):e===3&&t.destroyNode(a),i!=null&&ST(t,e,i,n,o)}}function vT(e,t){Xm(e,t),t[ce]=null,t[Oe]=null}function yT(e,t,n,r,o,i){r[ce]=o,r[Oe]=t,va(e,r,n,1,o,i)}function Xm(e,t){t[kt].changeDetectionScheduler?.notify(9),va(e,t,t[q],2,null,null)}function DT(e){let t=e[mo];if(!t)return uu(e[T],e);for(;t;){let n=null;if(Xt(t))n=t[mo];else{let r=t[ye];r&&(n=r)}if(!n){for(;t&&!t[Qe]&&t!==e;)Xt(t)&&uu(t[T],t),t=t[he];t===null&&(t=e),Xt(t)&&uu(t[T],t),n=t&&t[Qe]}t=n}}function xd(e,t){let n=e[fr],r=n.indexOf(t);n.splice(r,1)}function ga(e,t){if(wr(t))return;let n=t[q];n.destroyNode&&va(e,t,n,3,null,null),DT(t)}function uu(e,t){if(wr(t))return;let n=P(null);try{t[_]&=-129,t[_]|=256,t[$e]&&Kr(t[$e]),wT(e,t),ET(e,t),t[T].type===1&&t[q].destroy();let r=t[_n];if(r!==null&&He(t[he])){r!==t[he]&&xd(r,t);let o=t[Ft];o!==null&&o.detachView(e)}Vu(t)}finally{P(n)}}function ET(e,t){let n=e.cleanup,r=t[go];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[go]=null);let o=t[Jt];if(o!==null){t[Jt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Nn];if(i!==null){t[Nn]=null;for(let s of i)s.destroy()}}function wT(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof xn)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];Y(4,a,c);try{c.call(a)}finally{Y(5,a,c)}}else{Y(4,o,i);try{i.call(o)}finally{Y(5,o,i)}}}}}function Od(e,t,n){return IT(e,t.parent,n)}function IT(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[ce];if(tn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Je.None||o===Je.Emulated)return null}return tt(r,n)}function ev(e,t,n){return bT(e,t,n)}function CT(e,t,n){return e.type&40?tt(e,n):null}var bT=CT,Ep;function ma(e,t,n,r){let o=Od(e,r,t),i=t[q],s=r.parent||t[Oe],a=ev(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Dp(i,o,n[c],a,!1);else Dp(i,o,n,a,!1);Ep!==void 0&&Ep(i,r,t,n,o)}function wn(e,t){if(t!==null){let n=t.type;if(n&3)return tt(t,e);if(n&4)return rl(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return wn(e,r);{let o=e[t.index];return He(o)?rl(-1,o):K(o)}}else{if(n&128)return wn(e,t.next);if(n&32)return Ad(t,e)()||K(e[t.index]);{let r=tv(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Rn(e[Ie]);return wn(o,r)}else return wn(e,t.next)}}}return null}function tv(e,t){if(t!==null){let r=e[Ie][Oe],o=t.projection;return r.projection[o]}return null}function rl(e,t){let n=ye+e+1;if(n<t.length){let r=t[n],o=r[T].firstChild;if(o!==null)return wn(r,o)}return t[Lt]}function Pd(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Tr(K(a),r),n.flags|=2),!sn(n))if(c&8)Pd(e,t,n.child,r,o,i,!1),ar(t,e,o,a,i);else if(c&32){let u=Ad(n,r),l;for(;l=u();)ar(t,e,o,l,i);ar(t,e,o,a,i)}else c&16?nv(e,t,r,n,o,i):ar(t,e,o,a,i);n=s?n.projectionNext:n.next}}function va(e,t,n,r,o,i){Pd(n,r,e.firstChild,t,o,i,!1)}function TT(e,t,n){let r=t[q],o=Od(e,n,t),i=n.parent||t[Oe],s=ev(i,n,t);nv(r,0,t,n,o,s)}function nv(e,t,n,r,o,i){let s=n[Ie],c=s[Oe].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];ar(t,e,o,l,i)}else{let u=c,l=s[he];wo(r)&&(u.flags|=128),Pd(e,t,u,l,o,i,!0)}}function ST(e,t,n,r,o){let i=n[Lt],s=K(n);i!==s&&ar(t,e,r,i,o);for(let a=ye;a<n.length;a++){let c=n[a];va(c[T],c,e,t,r,i)}}function MT(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Vt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Vt.Important),e.setStyle(n,r,o,i))}}function bo(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(K(i)),He(i)&&kd(i,r);let s=n.type;if(s&8)bo(e,t,n.child,r);else if(s&32){let a=Ad(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=tv(t,n);if(Array.isArray(a))r.push(...a);else{let c=Rn(t[Ie]);bo(c[T],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function kd(e,t){for(let n=ye;n<e.length;n++){let r=e[n],o=r[T].firstChild;o!==null&&bo(r[T],r,o,t)}e[Lt]!==e[ce]&&t.push(e[Lt])}function rv(e){if(e[cr]!==null){for(let t of e[cr])t.impl.addSequence(t);e[cr].length=0}}var ov=[];function _T(e){return e[$e]??NT(e)}function NT(e){let t=ov.pop()??Object.create(AT);return t.lView=e,t}function RT(e){e.lView[$e]!==e&&(e.lView=null,ov.push(e))}var AT=B(E({},Wn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Ir(e.lView)},consumerOnSignalRead(){this.lView[$e]=this}});function xT(e){let t=e[$e]??Object.create(OT);return t.lView=e,t}var OT=B(E({},Wn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=Rn(e.lView);for(;t&&!iv(t[T]);)t=Rn(t);t&&bg(t)},consumerOnSignalRead(){this.lView[$e]=this}});function iv(e){return e.type!==2}function sv(e){if(e[Nn]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Nn])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[_]&8192)}}var PT=100;function av(e,t=!0,n=0){let o=e[kt].rendererFactory,i=!1;i||o.begin?.();try{kT(e,n)}catch(s){throw t&&fT(e,s),s}finally{i||o.end?.()}}function kT(e,t){let n=Mg();try{bs(!0),ol(e,t);let r=0;for(;oa(e);){if(r===PT)throw new v(103,!1);r++,ol(e,1)}}finally{bs(n)}}function FT(e,t,n,r){if(wr(t))return;let o=t[_],i=!1,s=!1;nd(t);let a=!0,c=null,u=null;i||(iv(e)?(u=_T(t),c=Qr(u)):Rc()===null?(a=!1,u=xT(t),c=Qr(u)):t[$e]&&(Kr(t[$e]),t[$e]=null));try{Cg(t),LI(e.bindingStartIndex),n!==null&&Km(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&hs(t,f,null)}else{let f=e.preOrderHooks;f!==null&&ps(t,f,0,null),su(t,0)}if(s||LT(t),sv(t),cv(t,0),e.contentQueries!==null&&Tm(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&hs(t,f)}else{let f=e.contentHooks;f!==null&&ps(t,f,1),su(t,1)}VT(e,t);let d=e.components;d!==null&&lv(t,d,0);let h=e.viewQuery;if(h!==null&&Wu(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&hs(t,f)}else{let f=e.viewHooks;f!==null&&ps(t,f,2),su(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[iu]){for(let f of t[iu])f();t[iu]=null}i||(rv(t),t[_]&=-73)}catch(l){throw i||Ir(t),l}finally{u!==null&&(Ti(u,c),a&&RT(u)),rd()}}function cv(e,t){for(let n=sm(e);n!==null;n=am(n))for(let r=ye;r<n.length;r++){let o=n[r];uv(o,t)}}function LT(e){for(let t=sm(e);t!==null;t=am(t)){if(!(t[_]&2))continue;let n=t[fr];for(let r=0;r<n.length;r++){let o=n[r];bg(o)}}}function jT(e,t,n){Y(18);let r=gt(t,e);uv(r,n),Y(19,r[ie])}function uv(e,t){Wl(e)&&ol(e,t)}function ol(e,t){let r=e[T],o=e[_],i=e[$e],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Si(i)),s||=!1,i&&(i.dirty=!1),e[_]&=-9217,s)FT(r,e,r.template,e[ie]);else if(o&8192){sv(e),cv(e,1);let a=r.components;a!==null&&lv(e,a,1),rv(e)}}function lv(e,t,n){for(let r=0;r<t.length;r++)jT(e,t[r],n)}function VT(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)An(~o);else{let i=o,s=n[++r],a=n[++r];VI(s,i);let c=t[i];Y(24,c),a(2,c),Y(25,c)}}}finally{An(-1)}}function Fd(e,t){let n=Mg()?64:1088;for(e[kt].changeDetectionScheduler?.notify(t);e;){e[_]|=n;let r=Rn(e);if(hr(e)&&!r)return e;e=r}return null}function dv(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function fv(e,t){let n=ye+t;if(n<e.length)return e[n]}function Lo(e,t,n,r=!0){let o=t[T];if(UT(o,t,e,n),r){let s=rl(n,e),a=t[q],c=a.parentNode(e[Lt]);c!==null&&yT(o,e[Oe],a,t,c,s)}let i=t[Ke];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function hv(e,t){let n=To(e,t);return n!==void 0&&ga(n[T],n),n}function To(e,t){if(e.length<=ye)return;let n=ye+t,r=e[n];if(r){let o=r[_n];o!==null&&o!==e&&xd(o,r),t>0&&(e[n-1][Qe]=r[Qe]);let i=ws(e,ye+t);vT(r[T],r);let s=i[Ft];s!==null&&s.detachView(i[T]),r[he]=null,r[Qe]=null,r[_]&=-129}return r}function UT(e,t,n,r){let o=ye+r,i=n.length;r>0&&(n[o-1][Qe]=t),r<i-ye?(t[Qe]=n[o],og(n,ye+r,t)):(n.push(t),t[Qe]=null),t[he]=n;let s=t[_n];s!==null&&n!==s&&pv(s,t);let a=t[Ft];a!==null&&a.insertView(e),Ru(t),t[_]|=128}function pv(e,t){let n=e[fr],r=t[he];if(Xt(r))e[_]|=2;else{let o=r[he][Ie];t[Ie]!==o&&(e[_]|=2)}n===null?e[fr]=[t]:n.push(t)}var So=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[T];return bo(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[ie]}set context(t){this._lView[ie]=t}get destroyed(){return wr(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[he];if(He(t)){let n=t[Cs],r=n?n.indexOf(this):-1;r>-1&&(To(t,r),ws(n,r))}this._attachedToViewContainer=!1}ga(this._lView[T],this._lView)}onDestroy(t){Tg(this._lView,t)}markForCheck(){Fd(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[_]&=-129}reattach(){Ru(this._lView),this._lView[_]|=128}detectChanges(){this._lView[_]|=1024,av(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new v(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=hr(this._lView),n=this._lView[_n];n!==null&&!t&&xd(n,this._lView),Xm(this._lView[T],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new v(902,!1);this._appRef=t;let n=hr(this._lView),r=this._lView[_n];r!==null&&!n&&pv(r,this._lView),Ru(this._lView)}};var Pn=(()=>{class e{static __NG_ELEMENT_ID__=HT}return e})(),BT=Pn,$T=class extends BT{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Fo(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new So(o)}};function HT(){return ya(Ce(),N())}function ya(e,t){return e.type&4?new $T(t,e,br(e,t)):null}var il="<-- AT THIS LOCATION";function zT(e){switch(e){case 4:return"view container";case 2:return"element";case 8:return"ng-container";case 32:return"icu";case 64:return"i18n";case 16:return"projection";case 1:return"text";case 128:return"@let";default:return"<unknown>"}}function qT(e,t){let n=`During serialization, Angular was unable to find an element in the DOM:

`,r=`${QT(e,t,!1)}

`,o=JT();throw new v(-502,n+r+o)}function GT(e){let t="During serialization, Angular detected DOM nodes that were created outside of Angular context and provided as projectable nodes (likely via `ViewContainerRef.createComponent` or `createComponent` APIs). Hydration is not supported for such cases, consider refactoring the code to avoid this pattern or using `ngSkipHydration` on the host element of the component.\n\n",n=`${KT(e)}

`,r=t+n+XT();return new v(-503,r)}function WT(e){let t=[];if(e.attrs)for(let n=0;n<e.attrs.length;){let r=e.attrs[n++];if(typeof r=="number")break;let o=e.attrs[n++];t.push(`${r}="${Ps(o)}"`)}return t.join(" ")}var ZT=new Set(["ngh","ng-version","ng-server-context"]);function YT(e){let t=[];for(let n=0;n<e.attributes.length;n++){let r=e.attributes[n];ZT.has(r.name)||t.push(`${r.name}="${Ps(r.value)}"`)}return t.join(" ")}function lu(e,t="\u2026"){switch(e.type){case 1:return`#text${e.value?`(${e.value})`:""}`;case 2:let r=WT(e),o=e.value.toLowerCase();return`<${o}${r?" "+r:""}>${t}</${o}>`;case 8:return"<!-- ng-container -->";case 4:return"<!-- container -->";default:return`#node(${zT(e.type)})`}}function ms(e,t="\u2026"){let n=e;switch(n.nodeType){case Node.ELEMENT_NODE:let r=n.tagName.toLowerCase(),o=YT(n);return`<${r}${o?" "+o:""}>${t}</${r}>`;case Node.TEXT_NODE:let i=n.textContent?Ps(n.textContent):"";return`#text${i?`(${i})`:""}`;case Node.COMMENT_NODE:return`<!-- ${Ps(n.textContent??"")} -->`;default:return`#node(${n.nodeType})`}}function QT(e,t,n){let r="  ",o="";t.prev?(o+=r+`\u2026
`,o+=r+lu(t.prev)+`
`):t.type&&t.type&12&&(o+=r+`\u2026
`),n?(o+=r+lu(t)+`
`,o+=r+`<!-- container -->  ${il}
`):o+=r+lu(t)+`  ${il}
`,o+=r+`\u2026
`;let i=t.type?Od(e[T],t,e):null;return i&&(o=ms(i,`
`+o)),o}function KT(e){let t="  ",n="",r=e;return r.previousSibling&&(n+=t+`\u2026
`,n+=t+ms(r.previousSibling)+`
`),n+=t+ms(r)+`  ${il}
`,e.nextSibling&&(n+=t+`\u2026
`),e.parentNode&&(n=ms(r.parentNode,`
`+n)),n}function JT(e){return`To fix this problem:
  * check ${e?`the "${e}"`:"corresponding"} component for hydration-related issues
  * check to see if your template has valid HTML structure
  * or skip hydration by adding the \`ngSkipHydration\` attribute to its host node in a template

`}function XT(){return`Note: attributes are only displayed to better represent the DOM but have no effect on hydration mismatches.

`}function eS(e){return e.replace(/\s+/gm,"")}function Ps(e,t=50){return e?(e=eS(e),e.length>t?`${e.substring(0,t-1)}\u2026`:e):""}function Mr(e,t,n,r,o){let i=e.data[t];if(i===null)i=tS(e,t,n,r,o),jI()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=PI();i.injectorIndex=s===null?-1:s.injectorIndex}return nn(i,!0),i}function tS(e,t,n,r,o){let i=Sg(),s=Kl(),a=s?i:i&&i.parent,c=e.data[t]=rS(e,a,n,t,r,o);return nS(e,c,i,s),c}function nS(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function rS(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Cr()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function oS(e,t){let n=t[e.currentCaseLViewIndex];return n===null?n:n<0?~n:n}function gv(e,t,n){e.index=0;let r=oS(t,n);r!==null?e.removes=t.remove[r]:e.removes=Ee}function sl(e){if(e.index<e.removes.length){let t=e.removes[e.index++];if(t>0)return e.lView[t];{e.stack.push(e.index,e.removes);let n=~t,r=e.lView[T].data[n];return gv(e,r,e.lView),sl(e)}}else return e.stack.length===0?null:(e.removes=e.stack.pop(),e.index=e.stack.pop(),sl(e))}function iS(e,t){let n={stack:[],index:-1,lView:t};return gv(n,e,t),sl.bind(null,n)}var sS=new RegExp(`^(\\d+)*(${pd}|${hd})*(.*)`);function aS(e,t){let n=[e];for(let r of t){let o=n.length-1;if(o>0&&n[o-1]===r){let i=n[o]||1;n[o]=i+1}else n.push(r,"")}return n.join("")}function cS(e){let t=e.match(sS),[n,r,o,i]=t,s=r?parseInt(r,10):o,a=[];for(let[c,u,l]of i.matchAll(/(f|n)(\d*)/g)){let d=parseInt(l,10)||1;a.push(u,d)}return[s,...a]}function uS(e){return!e.prev&&e.parent?.type===8}function du(e){return e.index-L}function Mo(e,t){return!(e.type&144)&&!!t[e.index]&&mv(K(t[e.index]))}function mv(e){return!!e&&!e.isConnected}function lS(e,t){let n=e.i18nNodes;if(n)return n.get(t)}function Da(e,t,n,r){let o=du(r),i=lS(e,o);if(i===void 0){let s=e.data[zu];if(s?.[o])i=fS(s[o],n);else if(t.firstChild===r)i=e.firstChild;else{let a=r.prev===null,c=r.prev??r.parent;if(uS(r)){let u=du(r.parent);i=Gu(e,u)}else{let u=tt(c,n);if(a)i=u.firstChild;else{let l=du(c),d=Gu(e,l);if(c.type===2&&d){let f=Dd(e,l)+1;i=Ea(f,d)}else i=u.nextSibling}}}}return i}function Ea(e,t){let n=t;for(let r=0;r<e;r++)n=n.nextSibling;return n}function dS(e,t){let n=e;for(let r=0;r<t.length;r+=2){let o=t[r],i=t[r+1];for(let s=0;s<i;s++)switch(o){case lm:n=n.firstChild;break;case dm:n=n.nextSibling;break}}return n}function fS(e,t){let[n,...r]=cS(e),o;if(n===hd)o=t[Ie][ce];else if(n===pd)o=kb(t[Ie][ce]);else{let i=Number(n);o=K(t[i+L])}return dS(o,r)}function al(e,t){if(e===t)return[];if(e.parentElement==null||t.parentElement==null)return null;if(e.parentElement===t.parentElement)return hS(e,t);{let n=t.parentElement,r=al(e,n),o=al(n.firstChild,t);return!r||!o?null:[...r,lm,...o]}}function hS(e,t){let n=[],r=null;for(r=e;r!=null&&r!==t;r=r.nextSibling)n.push(dm);return r==null?null:n}function wp(e,t,n){let r=al(e,t);return r===null?null:aS(n,r)}function pS(e,t,n){let r=e.parent,o,i,s;for(;r!==null&&(Mo(r,t)||n?.has(r.index));)r=r.parent;r===null||!(r.type&3)?(o=s=hd,i=t[Ie][ce]):(o=r.index,i=K(t[o]),s=Tn(o-L));let a=K(t[e.index]);if(e.type&44){let u=wn(t,e);u&&(a=u)}let c=wp(i,a,s);if(c===null&&i!==a){let u=i.ownerDocument.body;if(c=wp(u,a,pd),c===null)throw qT(t,e)}return c}var vv=!1;function gS(e){vv=e}function mS(){return vv}function vS(e){return e=e??g(pe),e.get(OC,!1)}function yS(e,t){let n=t.i18nChildren.get(e);return n===void 0&&(n=DS(e),t.i18nChildren.set(e,n)),n}function DS(e){let t=new Set;function n(r){switch(t.add(r.index),r.kind){case 1:case 2:{for(let o of r.children)n(o);break}case 3:{for(let o of r.cases)for(let i of o)n(i);break}}}for(let r=L;r<e.bindingStartIndex;r++){let o=e.data[r];if(!(!o||!o.ast))for(let i of o.ast)n(i)}return t.size===0?null:t}function ES(e,t,n){if(!n.isI18nHydrationEnabled)return null;let r=e[T],o=r.data[t];if(!o||!o.ast)return null;let i=r.data[o.parentTNodeIndex];if(i&&CC(i))return null;let s={caseQueue:[],disconnectedNodes:new Set,disjointNodes:new Set};return cl(e,s,n,o.ast),s.caseQueue.length===0&&s.disconnectedNodes.size===0&&s.disjointNodes.size===0?null:s}function cl(e,t,n,r){let o=null;for(let i of r){let s=IS(e,t,n,i);s&&(wS(o,s)&&t.disjointNodes.add(i.index-L),o=s)}return o}function wS(e,t){return e&&e.nextSibling!==t}function IS(e,t,n,r){let o=K(e[r.index]);if(!o||mv(o))return t.disconnectedNodes.add(r.index-L),null;let i=o;switch(r.kind){case 0:{bm(n,i);break}case 1:case 2:{cl(e,t,n,r.children);break}case 3:{let s=e[r.currentCaseLViewIndex];if(s!=null){let a=s<0?~s:s;t.caseQueue.push(a),cl(e,t,n,r.cases[a])}break}}return CS(e,r)}function CS(e,t){let r=e[T].data[t.index];return Fg(r)?wn(e,r):t.kind===3?iS(r,e)()??K(e[t.index]):K(e[t.index])??null}function bS(e){let t=e[vo]??[],r=e[he][q],o=[];for(let i of t)i.data[fm]!==void 0?o.push(i):TS(i,r);e[vo]=o}function TS(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[bn];for(;n<o;){let i=r.nextSibling;qm(t,r,!1),r=i,n++}}}function SS(e,t){let n=[];for(let r of t)for(let o=0;o<(r[xs]??1);o++){let i={data:r,firstChild:null};r[bn]>0&&(i.firstChild=e,e=Ea(r[bn],e)),n.push(i)}return[e,n]}var yv=()=>null;function MS(e,t){let n=e[vo];return!t||n===null||n.length===0?null:n[0].data[Hu]===t?n.shift():(bS(e),null)}function _S(){yv=MS}function mr(e,t){return yv(e,t)}var NS=class{},Dv=class{},ul=class{resolveComponentFactory(t){throw Error(`No component factory found for ${Ae(t)}.`)}},wa=class{static NULL=new ul},vr=class{},_r=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>RS()}return e})();function RS(){let e=N(),t=Ce(),n=gt(t.index,e);return(Xt(n)?n:e)[q]}var AS=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>null})}return e})();var fu={},ll=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Ks(r);let o=this.injector.get(t,fu,r);return o!==fu||n===fu?o:this.parentInjector.get(t,n,r)}};function dl(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Iu(o,a);else if(i==2){let c=a,u=t[++s];r=Iu(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function H(e,t=O.Default){let n=N();if(n===null)return C(e,t);let r=Ce();return zg(r,n,ve(e),t)}function Ev(){let e="invalid";throw new Error(e)}function Ld(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,u=null,l=OS(s);l===null?a=s:[a,c,u]=l,FS(e,t,n,a,i,c,u)}i!==null&&r!==null&&xS(n,r,i)}function xS(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new v(-301,!1);r.push(t[o],i)}}function OS(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&pt(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,PS(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function PS(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function kS(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function FS(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&pt(f)&&(c=!0,kS(e,n,h)),Pu(_s(n,t),e,f.type)}$S(n,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=Ym(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(n.mergedAttrs=pr(n.mergedAttrs,f.hostAttrs),jS(e,n,t,d,f),BS(d,f,o),s!==null&&s.has(f)){let[m,D]=s.get(f);n.directiveToIndex.set(f.type,[d,m+n.directiveStart,D+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let p=f.type.prototype;!u&&(p.ngOnChanges||p.ngOnInit||p.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(p.ngOnChanges||p.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}LS(e,n,i)}function LS(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Ip(0,t,o,r),Ip(1,t,o,r),bp(t,r,!1);else{let i=n.get(o);Cp(0,t,i,r),Cp(1,t,i,r),bp(t,r,!0)}}}function Ip(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),wv(t,i)}}function Cp(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),wv(t,s)}}function wv(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function bp(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||wd(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function jS(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Sn(o.type,!0)),s=new xn(i,pt(o),H);e.blueprint[r]=s,n[r]=s,VS(e,t,r,Ym(e,n,o.hostVars,je),o)}function VS(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;US(s)!=a&&s.push(a),s.push(n,r,i)}}function US(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function BS(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;pt(t)&&(n[""]=e)}}function $S(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Iv(e,t,n,r,o,i,s,a){let c=t.consts,u=en(c,s),l=Mr(t,e,2,r,u);return i&&Ld(t,n,l,en(c,a),o),l.mergedAttrs=pr(l.mergedAttrs,l.attrs),l.attrs!==null&&dl(l,l.attrs,!1),l.mergedAttrs!==null&&dl(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function Cv(e,t){od(e,t),zl(t)&&e.queries.elementEnd(t)}var ks=class extends wa{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Pt(t);return new kn(n,this.ngModule)}};function HS(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&ha.SignalBased)!==0};return o&&(i.transform=o),i})}function zS(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function qS(e,t,n){let r=t instanceof we?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new ll(n,r):n}function GS(e){let t=e.get(vr,null);if(t===null)throw new v(407,!1);let n=e.get(AS,null),r=e.get(On,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function WS(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Id(t,n,n==="svg"?Dg:n==="math"?II:null)}var kn=class extends Dv{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=HS(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=zS(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Gb(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){Y(22);let i=P(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:Wb(this.componentDef.selectors[0]),c=Cd(0,null,null,1,0,null,null,null,null,[a],null),u=qS(s,o||this.ngModule,t),l=GS(u),d=l.rendererFactory.createRenderer(null,s),h=r?eT(d,r,s.encapsulation,u):WS(s,d),f=bd(null,c,null,512|Zm(s),null,null,l,d,u,null,Im(h,u,!0));f[L]=h,nd(f);let p=null;try{let m=Iv(L,c,f,"#host",()=>[this.componentDef],!0,0);h&&(Wm(d,h,m),Tr(h,f)),pa(c,f,m),Ed(c,m,f),Cv(c,m),n!==void 0&&ZS(m,this.ngContentSelectors,n),p=gt(m.index,f),f[ie]=p[ie],Rd(c,f,null)}catch(m){throw p!==null&&Vu(p),Vu(f),m}finally{Y(23),rd()}return new fl(this.componentType,f)}finally{P(i)}}},fl=class extends NS{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=ql(n[T],L),this.location=br(this._tNode,n),this.instance=gt(this._tNode.index,n)[ie],this.hostView=this.changeDetectorRef=new So(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Nd(r,o[T],o,t,n);this.previousInputValues.set(t,n);let s=gt(r.index,o);Fd(s,1)}get injector(){return new Cn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function ZS(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var zt=(()=>{class e{static __NG_ELEMENT_ID__=YS}return e})();function YS(){let e=Ce();return Tv(e,N())}var QS=zt,bv=class extends QS{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return br(this._hostTNode,this._hostLView)}get injector(){return new Cn(this._hostTNode,this._hostLView)}get parentInjector(){let t=id(this._hostTNode,this._hostLView);if(jg(t)){let n=Ss(t,this._hostLView),r=Ts(t),o=n[T].data[r+8];return new Cn(o,n)}else return new Cn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Tp(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-ye}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=mr(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,gr(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!vI(t),a;if(s)a=n;else{let p=n||{};a=p.index,r=p.injector,o=p.projectableNodes,i=p.environmentInjector||p.ngModuleRef}let c=s?t:new kn(Pt(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let m=(s?u:this.parentInjector).get(we,null);m&&(i=m)}let l=Pt(c.componentType??{}),d=mr(this._lContainer,l?.id??null),h=d?.firstChild??null,f=c.create(u,o,h,i);return this.insertImpl(f.hostView,a,gr(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(bI(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[he],u=new bv(c,c[Oe],c[he]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Lo(s,o,i,r),t.attachToViewContainerRef(),og(hu(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Tp(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=To(this._lContainer,n);r&&(ws(hu(this._lContainer),n),ga(r[T],r))}detach(t){let n=this._adjustIndex(t,-1),r=To(this._lContainer,n);return r&&ws(hu(this._lContainer),n)!=null?new So(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Tp(e){return e[Cs]}function hu(e){return e[Cs]||(e[Cs]=[])}function Tv(e,t){let n,r=t[e.index];return He(r)?n=r:(n=dv(r,t,null,e),t[e.index]=n,Td(t,n)),Sv(n,t,e,r),new bv(n,e,t)}function KS(e,t){let n=e[q],r=n.createComment(""),o=tt(t,e),i=n.parentNode(o);return Os(n,i,r,n.nextSibling(o),!1),r}var Sv=Mv,jd=()=>!1;function JS(e,t,n){return jd(e,t,n)}function Mv(e,t,n,r){if(e[Lt])return;let o;n.type&8?o=K(r):o=KS(t,n),e[Lt]=o}function XS(e,t,n){if(e[Lt]&&e[vo])return!0;let r=n[Ke],o=t.index-L;if(!r||Io(t)||Po(r,o))return!1;let s=Gu(r,o),a=r.data[Co]?.[o],[c,u]=SS(s,a);return e[Lt]=c,e[vo]=u,!0}function eM(e,t,n,r){jd(e,n,t)||Mv(e,t,n,r)}function tM(){Sv=eM,jd=XS}var hl=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},pl=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Ud(t,n).matches!==null&&this.queries[n].setDirty()}},Fs=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=aM(t):this.predicate=t}},gl=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},ml=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,nM(n,i)),this.matchTNodeWithReadOption(t,n,gs(n,t,i,!1,!1))}else r===Pn?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,gs(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===yt||o===zt||o===Pn&&n.type&4)this.addMatch(n.index,-2);else{let i=gs(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function nM(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function rM(e,t){return e.type&11?br(e,t):e.type&4?ya(e,t):null}function oM(e,t,n,r){return n===-1?rM(t,e):n===-2?iM(e,t,r):yo(e,e[T],n,t)}function iM(e,t,n){if(n===yt)return br(t,e);if(n===Pn)return ya(t,e);if(n===zt)return Tv(t,e)}function _v(e,t,n,r){let o=t[Ft].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(oM(t,l,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function vl(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=_v(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=t[-c];for(let d=ye;d<l.length;d++){let h=l[d];h[_n]===h[he]&&vl(h[T],h,u,r)}if(l[fr]!==null){let d=l[fr];for(let h=0;h<d.length;h++){let f=d[h];vl(f[T],f,u,r)}}}}}return r}function Vd(e,t){return e[Ft].queries[t].queryList}function Nv(e,t,n){let r=new ju((n&4)===4);return MI(e,t,r,r.destroy),(t[Ft]??=new pl).queries.push(new hl(r))-1}function sM(e,t,n){let r=J();return r.firstCreatePass&&(Av(r,new Fs(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Nv(r,N(),t)}function Rv(e,t,n,r){let o=J();if(o.firstCreatePass){let i=Ce();Av(o,new Fs(t,n,r),i.index),cM(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Nv(o,N(),n)}function aM(e){return e.split(",").map(t=>t.trim())}function Av(e,t,n){e.queries===null&&(e.queries=new gl),e.queries.track(new ml(t,n))}function cM(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Ud(e,t){return e.queries.getByIndex(t)}function xv(e,t){let n=e[T],r=Ud(n,t);return r.crossesNgTemplate?vl(n,e,t,[]):_v(n,e,r,t)}function Ov(e,t,n){let r,o=Ni(()=>{r._dirtyCounter();let i=fM(r,e);if(t&&i===void 0)throw new v(-951,!1);return i});return r=o[_e],r._dirtyCounter=tm(0),r._flatValue=void 0,o}function uM(e){return Ov(!0,!1,e)}function lM(e){return Ov(!0,!0,e)}function dM(e,t){let n=e[_e];n._lView=N(),n._queryIndex=t,n._queryList=Vd(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function fM(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[_]&4)return t?void 0:Ee;let o=Vd(n,r),i=xv(n,r);return o.reset(i,em),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function Sp(e,t){return uM(t)}function hM(e,t){return lM(t)}var rV=(Sp.required=hM,Sp);function pM(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(yM))}return i}return Ls.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(u=>{o.template=u}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let u=o.styles.length,l=o.styleUrls;o.styleUrls.forEach((d,h)=>{a.push(""),s.push(r(d).then(f=>{a[u+h]=f,l.splice(l.indexOf(d),1),l.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(u=>{a.push(u),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>DM(i));t.push(c)}),mM(),Promise.all(t).then(()=>{})}var Ls=new Map,gM=new Set;function mM(){let e=Ls;return Ls=new Map,e}function vM(){return Ls.size===0}function yM(e){return typeof e=="string"?e:e.text()}function DM(e){gM.delete(e)}var yr=class{},Bd=class{};var js=class extends yr{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new ks(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=ag(t);this._bootstrapComponents=Vm(i.bootstrap),this._r3Injector=Wg(t,n,[{provide:yr,useValue:this},{provide:wa,useValue:this.componentFactoryResolver},...r],Ae(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Vs=class extends Bd{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new js(this.moduleType,t,[])}};function EM(e,t,n){return new js(e,t,n,!1)}var yl=class extends yr{injector;componentFactoryResolver=new ks(this);instance=null;constructor(t){super();let n=new po([...t.providers,{provide:yr,useValue:this},{provide:wa,useValue:this.componentFactoryResolver}],t.parent||ea(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function jo(e,t,n=null){return new yl({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var wM=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=ug(!1,n.type),o=r.length>0?jo([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=w({token:e,providedIn:"environment",factory:()=>new e(C(we))})}return e})();function Pv(e){return Ro(()=>{let t=kv(e),n=B(E({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===om.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(wM).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Je.Emulated,styles:e.styles||Ee,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&ze("NgStandalone"),Fv(n);let r=e.dependencies;return n.directiveDefs=Mp(r,!1),n.pipeDefs=Mp(r,!0),n.id=MM(n),n})}function IM(e){return Pt(e)||cg(e)}function CM(e){return e!==null}function Et(e){return Ro(()=>({type:e.type,bootstrap:e.bootstrap||Ee,declarations:e.declarations||Ee,imports:e.imports||Ee,exports:e.exports||Ee,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function bM(e,t){if(e==null)return ht;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=ha.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function TM(e){if(e==null)return ht;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function wt(e){return Ro(()=>{let t=kv(e);return Fv(t),t})}function $d(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function kv(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||ht,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ee,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:bM(e.inputs,t),outputs:TM(e.outputs),debugInfo:null}}function Fv(e){e.features?.forEach(t=>t(e))}function Mp(e,t){if(!e)return null;let n=t?iI:IM;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(CM)}var SM=new Map;function MM(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function _M(e){return Object.getPrototypeOf(e.prototype).constructor}function NM(e){let t=_M(e.type),n=!0,r=[e];for(;t;){let o;if(pt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new v(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=pu(e.inputs),s.declaredInputs=pu(e.declaredInputs),s.outputs=pu(e.outputs);let a=o.hostBindings;a&&PM(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&xM(e,c),u&&OM(e,u),RM(e,o),Fw(e.outputs,o.outputs),pt(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===NM&&(n=!1)}}t=Object.getPrototypeOf(t)}AM(r)}function RM(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function AM(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=pr(o.hostAttrs,n=pr(n,o.hostAttrs))}}function pu(e){return e===ht?{}:e===Ee?[]:e}function xM(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function OM(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function PM(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function sV(e){let t=n=>{let r=Array.isArray(e);n.hostDirectives===null?(n.findHostDirectiveDefs=Lv,n.hostDirectives=r?e.map(Dl):[e]):r?n.hostDirectives.unshift(...e.map(Dl)):n.hostDirectives.unshift(e)};return t.ngInherit=!0,t}function Lv(e,t,n){if(e.hostDirectives!==null)for(let r of e.hostDirectives)if(typeof r=="function"){let o=r();for(let i of o)_p(Dl(i),t,n)}else _p(r,t,n)}function _p(e,t,n){let r=cg(e.directive);kM(r.declaredInputs,e.inputs),Lv(r,t,n),n.set(r,e),t.push(r)}function Dl(e){return typeof e=="function"?{directive:ve(e),inputs:ht,outputs:ht}:{directive:ve(e.directive),inputs:Np(e.inputs),outputs:Np(e.outputs)}}function Np(e){if(e===void 0||e.length===0)return ht;let t={};for(let n=0;n<e.length;n+=2)t[e[n]]=e[n+1];return t}function kM(e,t){for(let n in t)if(t.hasOwnProperty(n)){let r=t[n],o=e[n];e[r]=o}}function jv(e){return LM(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function FM(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function LM(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Hd(e,t,n){return e[t]=n}function jM(e,t){return e[t]}function Xe(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Us(e,t,n,r){let o=Xe(e,t,n);return Xe(e,t+1,r)||o}function VM(e,t,n,r,o,i){let s=Us(e,t,n,r);return Us(e,t+2,o,i)||s}function UM(e,t,n,r,o,i,s,a,c){let u=t.consts,l=Mr(t,e,4,s||null,a||null);Ql()&&Ld(t,n,l,en(u,c),_d),l.mergedAttrs=pr(l.mergedAttrs,l.attrs),od(t,l);let d=l.tView=Cd(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function Bs(e,t,n,r,o,i,s,a,c,u){let l=n+L,d=t.firstCreatePass?UM(l,t,e,r,o,i,s,a,c):t.data[l];nn(d,!1);let h=Vv(t,e,d,n);aa()&&ma(t,e,h,d),Tr(h,e);let f=dv(h,e,h,d);return e[l]=f,Td(e,f),JS(f,d,e),ra(d)&&pa(t,e,d),c!=null&&Sd(e,d,u),d}function BM(e,t,n,r,o,i,s,a){let c=N(),u=J(),l=en(u.consts,i);return Bs(c,u,e,t,n,r,o,l,s,a),BM}var Vv=Uv;function Uv(e,t,n,r){return rn(!0),t[q].createComment("")}function $M(e,t,n,r){let o=t[Ke],i=!o||Cr()||sn(n)||Po(o,r);if(rn(i),i)return Uv(e,t);let s=o.data[$u]?.[r]??null;s!==null&&n.tView!==null&&n.tView.ssrId===null&&(n.tView.ssrId=s);let a=Da(o,e,t,n);la(o,r,a);let c=Dd(o,r);return Ea(c,a)}function HM(){Vv=$M}var zd=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var qd=new y(""),Vo=new y(""),Ia=(()=>{class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];_taskTrackingZone=null;_destroyRef;constructor(n,r,o){this._ngZone=n,this.registry=r,$l()&&(this._destroyRef=g(Bt,{optional:!0})??void 0),Gd||(zM(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this._taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){let n=this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),r=this._ngZone.runOutsideAngular(()=>this._ngZone.onStable.subscribe({next:()=>{z.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}}));this._destroyRef?.onDestroy(()=>{n.unsubscribe(),r.unsubscribe()})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this._taskTrackingZone?this._taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n()},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this._taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static \u0275fac=function(r){return new(r||e)(C(z),C(Ca),C(Vo))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Ca=(()=>{class e{_applications=new Map;registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Gd?.findTestabilityInTree(this,n,r)??null}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();function zM(e){Gd=e}var Gd,Bv=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>new El})}return e})(),El=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function Uo(e){return!!e&&typeof e.then=="function"}function $v(e){return!!e&&typeof e.subscribe=="function"}var Hv=new y("");function Wd(e){return Ln([{provide:Hv,multi:!0,useValue:e}])}var zv=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=g(Hv,{optional:!0})??[];injector=g(pe);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=xe(this.injector,o);if(Uo(i))n.push(i);else if($v(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Bo=new y("");function qM(){Pc(()=>{throw new v(600,!1)})}function GM(e){return e.isBoundToModule}var WM=10;function qv(e,t){return Array.isArray(t)?t.reduce(qv,e):E(E({},e),t)}var et=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=g(mC);afterRenderManager=g(gm);zonelessEnabled=g(ad);rootEffectScheduler=g(Bv);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new X;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=g($t).hasPendingTasks.pipe(k(n=>!n));constructor(){g(Sr,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=g(we);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=pe.NULL){Y(10);let i=n instanceof Dv;if(!this._injector.get(zv).done){let f="";throw new v(405,f)}let a;i?a=n:a=this._injector.get(wa).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=GM(a)?void 0:this._injector.get(yr),u=r||a.selector,l=a.create(o,[],u,c),d=l.location.nativeElement,h=l.injector.get(qd,null);return h?.registerApplication(d),l.onDestroy(()=>{this.detachView(l.hostView),vs(this.components,l),h?.unregisterApplication(d)}),this._loadComponent(l),Y(11,l),l}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){Y(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(gd.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new v(101,!1);let n=P(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,P(n),this.afterTick.next(),Y(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(vr,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<WM;)Y(14),this.synchronizeOnce(),Y(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)ZM(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>oa(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;vs(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Bo,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>vs(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new v(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function vs(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function ZM(e,t,n,r){if(!n&&!oa(e))return;av(e,t,n&&!r?0:1)}function Zd(e,t,n,r){let o=N(),i=Vn();if(Xe(o,i,t)){let s=J(),a=sa();uT(a,o,e,t,n,r)}return Zd}function Gv(e,t,n,r){return Xe(e,Vn(),n)?t+Tn(n)+r:je}function YM(e,t,n,r,o,i){let s=FI(),a=Us(e,s,n,o);return ed(2),a?t+Tn(n)+r+Tn(o)+i:je}function ls(e,t){return e<<17|t<<2}function Fn(e){return e>>17&32767}function QM(e){return(e&2)==2}function KM(e,t){return e&131071|t<<17}function wl(e){return e|2}function Dr(e){return(e&131068)>>2}function gu(e,t){return e&-131069|t<<2}function JM(e){return(e&1)===1}function Il(e){return e|1}function XM(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Fn(s),c=Dr(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||Ao(d,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let h=Fn(e[a+1]);e[r+1]=ls(h,a),h!==0&&(e[h+1]=gu(e[h+1],r)),e[a+1]=KM(e[a+1],r)}else e[r+1]=ls(a,0),a!==0&&(e[a+1]=gu(e[a+1],r)),a=r;else e[r+1]=ls(c,0),a===0?a=r:e[c+1]=gu(e[c+1],r),c=r;u&&(e[r+1]=wl(e[r+1])),Rp(e,l,r,!0),Rp(e,l,r,!1),e_(t,l,e,r,i),s=ls(a,c),i?t.classBindings=s:t.styleBindings=s}function e_(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Ao(i,t)>=0&&(n[r+1]=Il(n[r+1]))}function Rp(e,t,n,r){let o=e[n+1],i=t===null,s=r?Fn(o):Dr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];t_(c,t)&&(a=!0,e[s+1]=r?Il(u):wl(u)),s=r?Fn(u):Dr(u)}a&&(e[n+1]=r?wl(o):Il(o))}function t_(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Ao(e,t)>=0:!1}var Ye={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function n_(e){return e.substring(Ye.key,Ye.keyEnd)}function r_(e){return o_(e),Wv(e,Zv(e,0,Ye.textEnd))}function Wv(e,t){let n=Ye.textEnd;return n===t?-1:(t=Ye.keyEnd=i_(e,Ye.key=t,n),Zv(e,t,n))}function o_(e){Ye.key=0,Ye.keyEnd=0,Ye.value=0,Ye.valueEnd=0,Ye.textEnd=e.length}function Zv(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function i_(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function s_(e,t,n){let r=N(),o=Vn();if(Xe(r,o,t)){let i=J(),s=sa();Md(i,s,r,e,t,r[q],n,!1)}return s_}function Cl(e,t,n,r,o){Nd(t,e,n,o?"class":"style",r)}function a_(e,t,n){return Qv(e,t,n,!1),a_}function c_(e,t){return Qv(e,t,null,!0),c_}function aV(e){Kv(p_,Yv,e,!0)}function Yv(e,t){for(let n=r_(t);n>=0;n=Wv(t,n))Js(e,n_(t),!0)}function Qv(e,t,n,r){let o=N(),i=J(),s=ed(2);if(i.firstUpdatePass&&Xv(i,e,s,r),t!==je&&Xe(o,s,t)){let a=i.data[Ut()];ey(i,a,o,o[q],e,o[s+1]=m_(t,n),r,s)}}function Kv(e,t,n,r){let o=J(),i=ed(2);o.firstUpdatePass&&Xv(o,null,i,r);let s=N();if(n!==je&&Xe(s,i,n)){let a=o.data[Ut()];if(ty(a,r)&&!Jv(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Iu(c,n||"")),Cl(o,a,s,n,r)}else g_(o,a,s,s[q],s[i+1],s[i+1]=h_(e,t,n),r,i)}}function Jv(e,t){return t>=e.expandoStartIndex}function Xv(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Ut()],s=Jv(e,n);ty(i,r)&&t===null&&!s&&(t=!1),t=u_(o,i,t,r),XM(o,i,t,n,s,r)}}function u_(e,t,n,r){let o=BI(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=mu(null,e,t,n,r),n=_o(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=mu(o,e,t,n,r),i===null){let c=l_(e,t,r);c!==void 0&&Array.isArray(c)&&(c=mu(null,e,t,c[1],r),c=_o(c,t.attrs,r),d_(e,t,r,c))}else i=f_(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function l_(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Dr(r)!==0)return e[Fn(r)]}function d_(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Fn(o)]=r}function f_(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=_o(r,s,n)}return _o(r,t.attrs,n)}function mu(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=_o(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function _o(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Js(e,s,n?!0:t[++i]))}return e===void 0?null:e}function h_(e,t,n){if(n==null||n==="")return Ee;let r=[],o=nt(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function p_(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&Js(e,r,n)}function g_(e,t,n,r,o,i,s,a){o===je&&(o=Ee);let c=0,u=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let h=c<o.length?o[c+1]:void 0,f=u<i.length?i[u+1]:void 0,p=null,m;l===d?(c+=2,u+=2,h!==f&&(p=d,m=f)):d===null||l!==null&&l<d?(c+=2,p=l):(u+=2,p=d,m=f),p!==null&&ey(e,t,n,r,p,m,s,a),l=c<o.length?o[c]:null,d=u<i.length?i[u]:null}}function ey(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=JM(u)?Ap(c,t,n,o,Dr(u),s):void 0;if(!$s(l)){$s(i)||QM(u)&&(i=Ap(c,null,n,o,a,s));let d=wg(Ut(),n);MT(r,s,d,o,i)}}function Ap(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=n[o+1];h===je&&(h=d?Ee:void 0);let f=d?ru(h,r):l===r?h:void 0;if(u&&!$s(f)&&(f=ru(c,r)),$s(f)&&(a=f,s))return a;let p=e[o+1];o=s?Fn(p):Dr(p)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=ru(c,r))}return a}function $s(e){return e!==void 0}function m_(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Ae(nt(e)))),e}function ty(e,t){return(e.flags&(t?8:16))!==0}function cV(e,t,n){let r=N(),o=Gv(r,e,t,n);Kv(Js,Yv,o,!0)}var bl=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function vu(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function v_(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let u=e.at(i),l=t[i],d=vu(i,u,i,l,n);if(d!==0){d<0&&e.updateValue(i,l),i++;continue}let h=e.at(s),f=t[c],p=vu(s,h,c,f,n);if(p!==0){p<0&&e.updateValue(s,f),s--,c--;continue}let m=n(i,u),D=n(s,h),I=n(i,l);if(Object.is(I,D)){let V=n(c,f);Object.is(V,m)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,l),i++;continue}if(r??=new Hs,o??=Op(e,i,s,n),Tl(e,r,i,I))e.updateValue(i,l),i++,s++;else if(o.has(I))r.set(m,e.detach(i)),s--;else{let V=e.create(i,t[i]);e.attach(i,V),i++,s++}}for(;i<=c;)xp(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),u=c.next();for(;!u.done&&i<=s;){let l=e.at(i),d=u.value,h=vu(i,l,i,d,n);if(h!==0)h<0&&e.updateValue(i,d),i++,u=c.next();else{r??=new Hs,o??=Op(e,i,s,n);let f=n(i,d);if(Tl(e,r,i,f))e.updateValue(i,d),i++,s++,u=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,u=c.next();else{let p=n(i,l);r.set(p,e.detach(i)),s--}}}for(;!u.done;)xp(e,r,n,e.length,u.value),u=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function Tl(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function xp(e,t,n,r,o){if(Tl(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Op(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Hs=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function uV(e,t){ze("NgControlFlow");let n=N(),r=Vn(),o=n[r]!==je?n[r]:-1,i=o!==-1?zs(n,L+o):void 0,s=0;if(Xe(n,r,e)){let a=P(null);try{if(i!==void 0&&hv(i,s),e!==-1){let c=L+e,u=zs(n,c),l=Nl(n[T],c),d=mr(u,l.tView.ssrId),h=Fo(n,l,t,{dehydratedView:d});Lo(u,h,s,gr(l,d))}}finally{P(a)}}else if(i!==void 0){let a=fv(i,s);a!==void 0&&(a[ie]=t)}}var Sl=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-ye}};function lV(e,t){return t}var Ml=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function dV(e,t,n,r,o,i,s,a,c,u,l,d,h){ze("NgControlFlow");let f=N(),p=J(),m=c!==void 0,D=N(),I=a?s.bind(D[Ie][ie]):s,V=new Ml(m,I);D[L+e]=V,Bs(f,p,e+1,t,n,r,o,en(p.consts,i)),m&&Bs(f,p,e+2,c,u,l,d,en(p.consts,h))}var _l=class extends bl{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-ye}at(t){return this.getLView(t)[ie].$implicit}attach(t,n){let r=n[Ke];this.needsIndexUpdate||=t!==this.length,Lo(this.lContainer,n,t,gr(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,y_(this.lContainer,t)}create(t,n){let r=mr(this.lContainer,this.templateTNode.tView.ssrId),o=Fo(this.hostLView,this.templateTNode,new Sl(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){ga(t[T],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[ie].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[ie].$index=t}getLView(t){return D_(this.lContainer,t)}};function fV(e){let t=P(null),n=Ut();try{let r=N(),o=r[T],i=r[n],s=n+1,a=zs(r,s);if(i.liveCollection===void 0){let u=Nl(o,s);i.liveCollection=new _l(a,r,u)}else i.liveCollection.reset();let c=i.liveCollection;if(v_(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let u=Vn(),l=c.length===0;if(Xe(r,u,l)){let d=n+2,h=zs(r,d);if(l){let f=Nl(o,d),p=mr(h,f.tView.ssrId),m=Fo(r,f,void 0,{dehydratedView:p});Lo(h,m,0,gr(f,p))}else hv(h,0)}}}finally{P(t)}}function zs(e,t){return e[t]}function y_(e,t){return To(e,t)}function D_(e,t){return fv(e,t)}function Nl(e,t){return ql(e,t)}function ny(e,t,n,r){let o=N(),i=J(),s=L+e,a=o[q],c=i.firstCreatePass?Iv(s,i,o,t,_d,Ql(),n,r):i.data[s],u=oy(i,o,c,a,t,e);o[s]=u;let l=ra(c);return nn(c,!0),Wm(a,u,c),!sn(c)&&aa()&&ma(i,o,u,c),(_I()===0||l)&&Tr(u,o),NI(),l&&(pa(i,o,c),Ed(i,c,o)),r!==null&&Sd(o,c),ny}function ry(){let e=Ce();Kl()?Jl():(e=e.parent,nn(e,!1));let t=e;AI(t)&&OI(),RI();let n=J();return n.firstCreatePass&&Cv(n,t),t.classesWithoutHost!=null&&ZI(t)&&Cl(n,t,N(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&YI(t)&&Cl(n,t,N(),t.stylesWithoutHost,!1),ry}function Yd(e,t,n,r){return ny(e,t,n,r),ry(),Yd}var oy=(e,t,n,r,o,i)=>(rn(!0),Id(r,o,Og()));function E_(e,t,n,r,o,i){let s=t[Ke],a=!s||Cr()||sn(n)||Po(s,i);if(rn(a),a)return Id(r,o,Og());let c=Da(s,e,t,n);return Cm(s,i)&&la(s,i,c.nextSibling),s&&(ld(n)||rm(c))&&tn(n)&&(xI(n),Gm(c)),c}function w_(){oy=E_}function I_(e,t,n,r,o){let i=t.consts,s=en(i,r),a=Mr(t,e,8,"ng-container",s);s!==null&&dl(a,s,!0);let c=en(i,o);return Ql()&&Ld(t,n,a,c,_d),a.mergedAttrs=pr(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function iy(e,t,n){let r=N(),o=J(),i=e+L,s=o.firstCreatePass?I_(i,o,r,t,n):o.data[i];nn(s,!0);let a=ay(o,r,s,e);return r[i]=a,aa()&&ma(o,r,a,s),Tr(a,r),ra(s)&&(pa(o,r,s),Ed(o,s,r)),n!=null&&Sd(r,s),iy}function sy(){let e=Ce(),t=J();return Kl()?Jl():(e=e.parent,nn(e,!1)),t.firstCreatePass&&(od(t,e),zl(e)&&t.queries.elementEnd(e)),sy}function C_(e,t,n){return iy(e,t,n),sy(),C_}var ay=(e,t,n,r)=>(rn(!0),Hm(t[q],""));function b_(e,t,n,r){let o,i=t[Ke],s=!i||Cr()||Po(i,r)||sn(n);if(rn(s),s)return Hm(t[q],"");let a=Da(i,e,t,n),c=ib(i,r);return la(i,r,a),o=Ea(c,a),o}function T_(){ay=b_}function hV(){return N()}function S_(e,t,n){let r=N(),o=Vn();if(Xe(r,o,t)){let i=J(),s=sa();Md(i,s,r,e,t,r[q],n,!0)}return S_}var En=void 0;function M_(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var __=["en",[["a","p"],["AM","PM"],En],[["AM","PM"],En,En],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],En,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],En,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",En,"{1} 'at' {0}",En],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",M_],yu={};function Pe(e){let t=N_(e),n=Pp(t);if(n)return n;let r=t.split("-")[0];if(n=Pp(r),n)return n;if(r==="en")return __;throw new v(701,!1)}function Pp(e){return e in yu||(yu[e]=fe.ng&&fe.ng.common&&fe.ng.common.locales&&fe.ng.common.locales[e]),yu[e]}var ee=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ee||{});function N_(e){return e.toLowerCase().replace(/_/g,"-")}var qs="en-US",R_="USD";var A_=qs;function x_(e){typeof e=="string"&&(A_=e.toLowerCase().replace(/_/g,"-"))}function kp(e,t,n){return function r(o){if(o===Function)return n;let i=tn(e)?gt(e.index,t):t;Fd(i,5);let s=t[ie],a=Fp(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=Fp(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Fp(e,t,n,r){let o=P(null);try{return Y(6,t,n),n(r)!==!1}catch(i){return O_(e,i),!1}finally{Y(7,t,n),P(o)}}function O_(e,t){let n=e[dr],r=n?n.get(mt,null):null;r&&r.handleError(t)}function Lp(e,t,n,r,o,i){let s=t[n],a=t[T],u=a.data[n].outputs[r],l=s[u],d=a.firstCreatePass?Yl(a):null,h=Zl(t),f=l.subscribe(i),p=h.length;h.push(i,f),d&&d.push(o,e.index,p,-(p+1))}function Qd(e,t,n,r){let o=N(),i=J(),s=Ce();return cy(i,o,o[q],s,e,t,r),Qd}function P_(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[go],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function cy(e,t,n,r,o,i,s){let a=ra(r),u=e.firstCreatePass?Yl(e):null,l=Zl(t),d=!0;if(r.type&3||s){let h=tt(r,t),f=s?s(h):h,p=l.length,m=s?I=>s(K(I[r.index])):r.index,D=null;if(!s&&a&&(D=P_(e,t,o,r.index)),D!==null){let I=D.__ngLastListenerFn__||D;I.__ngNextListenerFn__=i,D.__ngLastListenerFn__=i,d=!1}else{i=kp(r,t,i),ZC(t,f,o,i);let I=n.listen(f,o,i);l.push(i,I),u&&u.push(o,m,p,p+1)}}else i=kp(r,t,i);if(d){let h=r.outputs?.[o],f=r.hostDirectiveOutputs?.[o];if(f&&f.length)for(let p=0;p<f.length;p+=2){let m=f[p],D=f[p+1];Lp(r,t,m,D,o,i)}if(h&&h.length)for(let p of h)Lp(r,t,p,o,o,i)}}function pV(e=1){return HI(e)}function k_(e,t){let n=null,r=Bb(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?Bm(e,i,!0):zb(r,i))return o}return n}function gV(e){let t=N()[Ie][Oe];if(!t.projection){let n=e?e.length:1,r=t.projection=nI(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?k_(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function mV(e,t=0,n,r,o,i){let s=N(),a=J(),c=r?e+1:null;c!==null&&Bs(s,a,c,r,o,i,null,n);let u=Mr(a,L+e,16,null,n||null);u.projection===null&&(u.projection=t),Jl();let d=!s[Ke]||Cr();s[Ie][Oe].projection[u.projection]===null&&c!==null?F_(s,a,c):d&&!sn(u)&&TT(a,s,u)}function F_(e,t,n){let r=L+n,o=t.data[r],i=e[r],s=mr(i,o.tView.ssrId),a=Fo(e,o,void 0,{dehydratedView:s});Lo(i,a,0,gr(o,s))}function uy(e,t,n,r){Rv(e,t,n,r)}function vV(e,t,n){sM(e,t,n)}function ly(e){let t=N(),n=J(),r=td();ia(r+1);let o=Ud(n,r);if(e.dirty&&CI(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=xv(t,r);e.reset(i,em),e.notifyOnChanges()}return!0}return!1}function dy(){return Vd(N(),td())}function yV(e,t,n,r,o){dM(t,Rv(e,n,r,o))}function DV(e=1){ia(td()+e)}function EV(e){let t=kI();return Gl(t,L+e)}function wV(e,t=""){let n=N(),r=J(),o=e+L,i=r.firstCreatePass?Mr(r,o,1,t,null):r.data[o],s=fy(r,n,i,t,e);n[o]=s,aa()&&ma(r,n,s,i),nn(i,!1)}var fy=(e,t,n,r,o)=>(rn(!0),$m(t[q],r));function L_(e,t,n,r,o){let i=t[Ke],s=!i||Cr()||sn(n)||Po(i,o);return rn(s),s?$m(t[q],r):Da(i,e,t,n)}function j_(){fy=L_}function V_(e){return hy("",e,""),V_}function hy(e,t,n){let r=N(),o=Gv(r,e,t,n);return o!==je&&py(r,Ut(),o),hy}function U_(e,t,n,r,o){let i=N(),s=YM(i,e,t,n,r,o);return s!==je&&py(i,Ut(),s),U_}function py(e,t,n){let r=wg(t,e);Zb(e[q],r,n)}function B_(e,t,n){nm(t)&&(t=t());let r=N(),o=Vn();if(Xe(r,o,t)){let i=J(),s=sa();Md(i,s,r,e,t,r[q],n,!1)}return B_}function IV(e,t){let n=nm(e);return n&&e.set(t),n}function $_(e,t){let n=N(),r=J(),o=Ce();return cy(r,n,n[q],o,e,t),$_}var H_={};function z_(e){let t=J(),n=N(),r=e+L,o=Mr(t,r,128,null,null);return nn(o,!1),Ig(t,n,r,H_),z_}function q_(e,t,n){let r=J();if(r.firstCreatePass){let o=pt(e);Rl(n,r.data,r.blueprint,o,!0),Rl(t,r.data,r.blueprint,o,!1)}}function Rl(e,t,n,r,o){if(e=ve(e),Array.isArray(e))for(let i=0;i<e.length;i++)Rl(e[i],t,n,r,o);else{let i=J(),s=N(),a=Ce(),c=lr(e)?e:ve(e.provide),u=fg(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(lr(e)||!e.multi){let f=new xn(u,o,H),p=Eu(c,t,o?l:l+h,d);p===-1?(Pu(_s(a,s),i,c),Du(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[p]=f,s[p]=f)}else{let f=Eu(c,t,l+h,d),p=Eu(c,t,l,l+h),m=f>=0&&n[f],D=p>=0&&n[p];if(o&&!D||!o&&!m){Pu(_s(a,s),i,c);let I=Z_(o?W_:G_,n.length,o,r,u);!o&&D&&(n[p].providerFactory=I),Du(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(I),s.push(I)}else{let I=gy(n[o?p:f],u,!o&&r);Du(i,e,f>-1?f:p,I)}!o&&r&&D&&n[p].componentProviders++}}}function Du(e,t,n,r){let o=lr(t),i=lI(t);if(o||i){let c=(i?ve(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function gy(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Eu(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function G_(e,t,n,r,o){return Al(this.multi,[])}function W_(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=yo(r,r[T],this.providerFactory.index,o);s=c.slice(0,a),Al(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],Al(i,s);return s}function Al(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Z_(e,t,n,r,o){let i=new xn(e,n,H);return i.multi=[],i.index=t,i.componentProviders=0,gy(i,o,r&&!n),i}function CV(e,t=[]){return n=>{n.providersResolver=(r,o)=>q_(r,o?o(e):e,t)}}function bV(e,t,n){let r=Xl()+e,o=N();return o[r]===je?Hd(o,r,n?t.call(n):t()):jM(o,r)}function my(e,t){let n=e[t];return n===je?void 0:n}function Y_(e,t,n,r,o,i,s){let a=t+n;return Us(e,a,o,i)?Hd(e,a+2,s?r.call(s,o,i):r(o,i)):my(e,a+2)}function Q_(e,t,n,r,o,i,s,a,c){let u=t+n;return VM(e,u,o,i,s,a)?Hd(e,u+4,c?r.call(c,o,i,s,a):r(o,i,s,a)):my(e,u+4)}function TV(e,t){let n=J(),r,o=e+L;n.firstCreatePass?(r=K_(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Sn(r.type,!0)),s,a=Re(H);try{let c=Ms(!1),u=i();return Ms(c),Ig(n,N(),o,u),u}finally{Re(a)}}function K_(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function SV(e,t,n,r){let o=e+L,i=N(),s=Gl(i,o);return vy(i,o)?Y_(i,Xl(),t,s.transform,n,r,s):s.transform(n,r)}function MV(e,t,n,r,o,i){let s=e+L,a=N(),c=Gl(a,s);return vy(a,s)?Q_(a,Xl(),t,c.transform,n,r,o,i,c):c.transform(n,r,o,i)}function vy(e,t){return e[T].data[t].pure}function _V(e,t){return ya(e,t)}var ds=null;function J_(e){ds!==null&&(e.defaultEncapsulation!==ds.defaultEncapsulation||e.preserveWhitespaces!==ds.preserveWhitespaces)||(ds=e)}var X_=[];var e0=new WeakMap,t0=new WeakMap;function NV(){e0=new WeakMap,t0=new WeakMap,X_.length=0,SM.clear()}var No=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},RV=new No("19.2.14"),xl=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},yy=(()=>{class e{compileModuleSync(n){return new Vs(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=ag(n),i=Vm(o.declarations).reduce((s,a)=>{let c=Pt(a);return c&&s.push(new kn(c)),s},[]);return new xl(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),n0=new y("");function r0(e,t,n){let r=new Vs(n);return Promise.resolve(r)}function jp(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var o0=(()=>{class e{zone=g(z);changeDetectionScheduler=g(On);applicationRef=g(et);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function i0({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new z(B(E({},Dy()),{scheduleInRootZone:n})),[{provide:z,useFactory:e},{provide:Mn,multi:!0,useFactory:()=>{let r=g(o0,{optional:!0});return()=>r.initialize()}},{provide:Mn,multi:!0,useFactory:()=>{let r=g(s0);return()=>{r.initialize()}}},t===!0?{provide:Yg,useValue:!0}:[],{provide:Qg,useValue:n??Zg}]}function Dy(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var s0=(()=>{class e{subscription=new te;initialized=!1;zone=g(z);pendingTasks=g($t);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{z.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{z.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var a0=(()=>{class e{appRef=g(et);taskService=g($t);ngZone=g(z);zonelessEnabled=g(ad);tracing=g(Sr,{optional:!0});disableScheduling=g(Yg,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new te;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Rs):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(g(Qg,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof As||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?np:Kg;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Rs+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,np(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function c0(){return typeof $localize<"u"&&$localize.locale||qs}var $o=new y("",{providedIn:"root",factory:()=>g($o,O.Optional|O.SkipSelf)||c0()}),Ey=new y("",{providedIn:"root",factory:()=>R_});var Gs=new y(""),u0=new y("");function so(e){return!e.moduleRef}function l0(e){let t=so(e)?e.r3Injector:e.moduleRef.injector,n=t.get(z);return n.run(()=>{so(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(mt,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),so(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Gs);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Gs);s.add(i),e.moduleRef.onDestroy(()=>{vs(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return f0(r,n,()=>{let i=t.get(zv);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get($o,qs);if(x_(s||qs),!t.get(u0,!0))return so(e)?t.get(et):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(so(e)){let c=t.get(et);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return d0(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function d0(e,t){let n=e.injector.get(et);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new v(-403,!1);t.push(e)}function f0(e,t,n){try{let r=n();return Uo(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var wy=(()=>{class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(n){this._injector=n}bootstrapModuleFactory(n,r){let o=r?.scheduleInRootZone,i=()=>gC(r?.ngZone,B(E({},Dy({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing})),{scheduleInRootZone:o})),s=r?.ignoreChangesOutsideZone,a=[i0({ngZoneFactory:i,ignoreChangesOutsideZone:s}),{provide:On,useExisting:a0}],c=EM(n.moduleType,this.injector,a);return l0({moduleRef:c,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(n,r=[]){let o=qv({},r);return r0(this.injector,o,n).then(i=>this.bootstrapModuleFactory(i,o))}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new v(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(Gs,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static \u0275fac=function(r){return new(r||e)(C(pe))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),fo=null,Iy=new y("");function h0(e){if(fo&&!fo.get(Iy,!1))throw new v(400,!1);qM(),fo=e;let t=e.get(wy);return m0(e),t}function Cy(e,t,n=[]){let r=`Platform: ${t}`,o=new y(r);return(i=[])=>{let s=by();if(!s||s.injector.get(Iy,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):h0(p0(a,r))}return g0(o)}}function p0(e=[],t){return pe.create({name:t,providers:[{provide:Xs,useValue:"platform"},{provide:Gs,useValue:new Set([()=>fo=null])},...e]})}function g0(e){let t=by();if(!t)throw new v(401,!1);return t}function by(){return fo?.get(wy)??null}function m0(e){let t=e.get(um,null);xe(e,()=>{t?.forEach(n=>n())})}var Nr=(()=>{class e{static __NG_ELEMENT_ID__=v0}return e})();function v0(e){return y0(Ce(),N(),(e&16)===16)}function y0(e,t,n){if(tn(e)&&!n){let r=gt(e.index,t);return new So(r,r)}else if(e.type&175){let r=t[Ie];return new So(r,t)}return null}var Ol=class{constructor(){}supports(t){return jv(t)}create(t){return new Pl(t)}},D0=(e,t)=>t,Pl=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||D0}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Vp(r,o,i)?n:r,a=Vp(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,p=f+h;l<=p&&p<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!jv(t))throw new v(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,FM(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new kl(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Ws),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Ws),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},kl=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Fl=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Ws=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Fl,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Vp(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function Up(){return new Kd([new Ol])}var Kd=(()=>{class e{factories;static \u0275prov=w({token:e,providedIn:"root",factory:Up});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Up()),deps:[[e,new Xw,new rg]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new v(901,!1)}}return e})();var E0=Cy(null,"core",[]),Ty=(()=>{class e{constructor(n){}static \u0275fac=function(r){return new(r||e)(C(et))};static \u0275mod=Et({type:e});static \u0275inj=vt({})}return e})();function Sy(){return[{provide:pm,useFactory:()=>{let t=!0;return t&&ze("NgEventReplay"),t}}]}function w0(e,t,n){let r=new Map,o=t[go],i=e.cleanup;if(!i||!o)return r;for(let s=0;s<i.length;){let a=i[s++],c=i[s++];if(typeof a!="string")continue;let u=a;if(!Bh(u))continue;Uh(u)?n.capture.add(u):n.regular.add(u);let l=K(t[c]);s++;let d=i[s++];(typeof d=="boolean"||d>=0)&&(r.has(l)?r.get(l).push(u):r.set(l,[u]))}return r}var Ll=class{views=[];indexByContent=new Map;add(t){let n=JSON.stringify(t);if(!this.indexByContent.has(n)){let r=this.views.length;return this.views.push(t),this.indexByContent.set(n,r),r}return this.indexByContent.get(n)}getAll(){return this.views}},I0=0;function My(e){return e.ssrId||(e.ssrId=`t${I0++}`),e.ssrId}function _y(e,t,n){let r=[];return bo(e,t,n,r),r.length}function C0(e){let t=[];return kd(e,t),t.length}function Ny(e,t,n){let r=e[ce];return r&&!r.hasAttribute(Eo)?Zs(r,e,null,t):null}function Ry(e,t,n){let r=Eg(e[ce]),o=Ny(r,t);if(o===null)return;let i=K(r[ce]),s=e[he],a=Zs(i,s,null,t),c=r[q],u=`${o}|${a}`;c.setAttribute(i,lo,u)}function AV(e,t){let n=e.injector,r=vS(n),o=ob(n),i=new Ll,s=new Map,a=e._views,c=n.get(pm,PC),u={regular:new Set,capture:new Set},l=new Map;e.injector.get(ua);for(let f of a){let p=tb(f);if(p!==null){let m={serializedViewCollection:i,corruptedTextNodes:s,isI18nHydrationEnabled:r,isIncrementalHydrationEnabled:o,i18nChildren:new Map,eventTypesToReplay:u,shouldReplayEvents:c,deferBlocks:l};He(p)?Ry(p,m):Ny(p,m),_0(s,t)}}let d=i.getAll(),h=n.get(Oo);if(h.set(Em,d),l.size>0){let f={};for(let[p,m]of l.entries())f[p]=m;h.set(KC,f)}return u}function b0(e,t,n,r,o){let i=[],s="";for(let a=ye;a<e.length;a++){let c=e[a],u,l,d;if(hr(c)&&(c=c[L],He(c))){l=C0(c)+1,Ry(c,o);let f=Eg(c[ce]);d={[Hu]:f[T].ssrId,[bn]:l}}if(!d){let f=c[T];f.type===1?(u=f.ssrId,l=1):(u=My(f),l=_y(f,c,f.firstChild)),d={[Hu]:u,[bn]:l};let p=!1;if(HC(n[T],t)){let m=BC(n,t),D=ym(n[T],t);if(o.isIncrementalHydrationEnabled&&D.hydrateTriggers!==null){let I=`d${o.deferBlocks.size}`;D.hydrateTriggers.has(7)&&(p=!0);let V=[];kd(e,V);let j={[bn]:V.length,[cp]:m[dp]},Te=T0(D.hydrateTriggers);Te.length>0&&(j[AC]=Te),r!==null&&(j[RC]=r),o.deferBlocks.set(I,j);let Ge=K(e);Ge!==void 0?Ge.nodeType===Node.COMMENT_NODE&&Bp(Ge,I):Bp(Ge,I),p||R0(D,V,I,o),r=I,d[fm]=I}d[cp]=m[dp]}p||Object.assign(d,Ay(e[a],r,o))}let h=JSON.stringify(d);if(i.length>0&&h===s){let f=i[i.length-1];f[xs]??=1,f[xs]++}else s=h,i.push(d)}return i}function T0(e){let t=new Set([0,1,2,5]),n=[];for(let[r,o]of e)t.has(r)&&(o===null?n.push(r):n.push({trigger:r,delay:o.delay}));return n}function ho(e,t,n,r){let o=t.index-L;e[zu]??={},e[zu][o]??=pS(t,n,r)}function wu(e,t){let n=typeof t=="number"?t:t.index-L;e[uo]??=[],e[uo].includes(n)||e[uo].push(n)}function Ay(e,t=null,n){let r={},o=e[T],i=yS(o,n),s=n.shouldReplayEvents?w0(o,e,n.eventTypesToReplay):null;for(let a=L;a<o.bindingStartIndex;a++){let c=o.data[a],u=a-L,l=ES(e,a,n);if(l){r[ap]??={},r[ap][u]=l.caseQueue;for(let d of l.disconnectedNodes)wu(r,d);for(let d of l.disjointNodes){let h=o.data[d+L];ho(r,h,e,i)}continue}if(Fg(c)&&!sn(c)){if(Mo(c,e)&&N0(c)){wu(r,c);continue}if(Array.isArray(c.projection)){for(let d of c.projection)if(d)if(!Array.isArray(d))!pg(d)&&!Io(d)&&(Mo(d,e)?wu(r,d):ho(r,d,e,i));else throw GT(K(e[a]))}if(S0(r,c,e,i),He(e[a])){let d=c.tView;d!==null&&(r[$u]??={},r[$u][u]=My(d));let h=e[a][ce];if(Array.isArray(h)){let f=K(h);f.hasAttribute(Eo)||Zs(f,h,t,n)}r[Co]??={},r[Co][u]=b0(e[a],c,e,t,n)}else if(Array.isArray(e[a])&&!WI(c)){let d=K(e[a][ce]);d.hasAttribute(Eo)||Zs(d,e[a],t,n)}else if(c.type&8)r[Bu]??={},r[Bu][u]=_y(o,e,c.child);else if(c.type&144){let d=c.next;for(;d!==null&&d.type&144;)d=d.next;d&&!Io(d)&&ho(r,d,e,i)}else if(c.type&1){let d=K(e[a]);bm(n,d)}if(s&&c.type&2){let d=K(e[a]);s.has(d)&&Dm(d,s.get(d),t)}}}return r}function S0(e,t,n,r){pg(t)||(t.projectionNext&&t.projectionNext!==t.next&&!Io(t.projectionNext)&&ho(e,t.projectionNext,n,r),t.prev===null&&t.parent!==null&&Mo(t.parent,n)&&!Mo(t,n)&&ho(e,t,n,r))}function M0(e){let t=e[ie];return t?.constructor?Pt(t.constructor)?.encapsulation===Je.ShadowDom:!1}function Zs(e,t,n,r){let o=t[q];if(yI(t)&&!mS()||M0(t))return o.setAttribute(e,Eo,""),null;{let i=Ay(t,n,r),s=r.serializedViewCollection.add(i);return o.setAttribute(e,lo,s.toString()),s}}function Bp(e,t){e.textContent=`ngh=${t}`}function _0(e,t){for(let[n,r]of e)n.after(t.createComment(r))}function N0(e){let t=e;for(;t!=null;){if(tn(t))return!0;t=t.parent}return!1}function R0(e,t,n,r){let o=ab(e.hydrateTriggers);for(let i of o)r.eventTypesToReplay.regular.add(i);if(o.length>0){let i=t.filter(s=>s.nodeType===Node.ELEMENT_NODE);for(let s of i)Dm(s,o,n)}}var $p=!1;function A0(){$p||($p=!0,eb(),w_(),j_(),T_(),HM(),tM(),_S(),rT())}function xy(){return Ln([{provide:up,useFactory:()=>{let t=!0;return t&&ze("NgHydration"),t}},{provide:Mn,useValue:()=>{gS(!1)},multi:!0}])}function Ho(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function x0(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}var O0="\u{1F170}\uFE0F",Oy=!1;function xV(e){if(!Oy)return;let{startLabel:t}=Py(e);performance.mark(t)}function OV(e){if(!Oy)return;let{startLabel:t,labelName:n,endLabel:r}=Py(e);performance.mark(r),performance.measure(n,t,r),performance.clearMarks(t),performance.clearMarks(r)}function Py(e){let t=`${O0}:${e}`;return{labelName:t,startLabel:`start:${t}`,endLabel:`end:${t}`}}function P0(e){return Lc(e)}function k0(e,t){return Ni(e,t?.equal)}var jl=class{[_e];constructor(t){this[_e]=t}destroy(){this[_e].destroy()}};function F0(e,t){!t?.injector&&ta(F0);let n=t?.injector??g(pe),r=t?.manualCleanup!==!0?n.get(Bt):null,o,i=n.get(md,null,{optional:!0}),s=n.get(On);return i!==null&&!t?.forceRoot?(o=V0(i.view,s,e),r instanceof Ns&&r._lView===i.view&&(r=null)):o=U0(e,n.get(Bv),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new jl(o)}var ky=B(E({},Wn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:Do,run(){if(this.dirty=!1,this.hasRun&&!Si(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Qr(this),n=bs(!1);try{this.maybeCleanup(),this.fn(e)}finally{bs(n),Ti(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),L0=B(E({},ky),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){Kr(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),j0=B(E({},ky),{consumerMarkedDirty(){this.view[_]|=8192,Ir(this.view),this.notifier.notify(13)},destroy(){Kr(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Nn]?.delete(this)}});function V0(e,t,n){let r=Object.create(j0);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Nn]??=new Set,e[Nn].add(r),r.consumerMarkedDirty(r),r}function U0(e,t,n){let r=Object.create(L0);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function PV(e,t){let n=Pt(e),r=t.elementInjector||ea();return new kn(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function Fy(e){let t=Pt(e);if(!t)return null;let n=new kn(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var kV=new y("",{providedIn:"platform",factory:()=>null}),FV=new y("",{providedIn:"platform",factory:()=>null}),LV=new y("",{providedIn:"platform",factory:()=>null});var ue=new y("");var Vy=null;function It(){return Vy}function Jd(e){Vy??=e}var zo=class{},qo=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(Uy),providedIn:"platform"})}return e})(),Xd=new y(""),Uy=(()=>{class e extends qo{_location;_history;_doc=g(ue);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return It().getBaseHref(this._doc)}onPopState(n){let r=It().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=It().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function ba(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Ly(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function rt(e){return e&&e[0]!=="?"?`?${e}`:e}var ot=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(Sa),providedIn:"root"})}return e})(),Ta=new y(""),Sa=(()=>{class e extends ot{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??g(ue).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return ba(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+rt(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+rt(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+rt(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(C(qo),C(Ta,8))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),cn=(()=>{class e{_subject=new X;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=H0(Ly(jy(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+rt(r))}normalize(n){return e.stripTrailingSlash($0(this._basePath,jy(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+rt(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+rt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=rt;static joinWithSlash=ba;static stripTrailingSlash=Ly;static \u0275fac=function(r){return new(r||e)(C(ot))};static \u0275prov=w({token:e,factory:()=>B0(),providedIn:"root"})}return e})();function B0(){return new cn(C(ot))}function $0(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function jy(e){return e.replace(/\/index.html$/,"")}function H0(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var af=(()=>{class e extends ot{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=ba(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+rt(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+rt(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(C(qo),C(Ta,8))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Wy={ADP:[void 0,void 0,0],AFN:[void 0,"\u060B",0],ALL:[void 0,void 0,0],AMD:[void 0,"\u058F",2],AOA:[void 0,"Kz"],ARS:[void 0,"$"],AUD:["A$","$"],AZN:[void 0,"\u20BC"],BAM:[void 0,"KM"],BBD:[void 0,"$"],BDT:[void 0,"\u09F3"],BHD:[void 0,void 0,3],BIF:[void 0,void 0,0],BMD:[void 0,"$"],BND:[void 0,"$"],BOB:[void 0,"Bs"],BRL:["R$"],BSD:[void 0,"$"],BWP:[void 0,"P"],BYN:[void 0,void 0,2],BYR:[void 0,void 0,0],BZD:[void 0,"$"],CAD:["CA$","$",2],CHF:[void 0,void 0,2],CLF:[void 0,void 0,4],CLP:[void 0,"$",0],CNY:["CN\xA5","\xA5"],COP:[void 0,"$",2],CRC:[void 0,"\u20A1",2],CUC:[void 0,"$"],CUP:[void 0,"$"],CZK:[void 0,"K\u010D",2],DJF:[void 0,void 0,0],DKK:[void 0,"kr",2],DOP:[void 0,"$"],EGP:[void 0,"E\xA3"],ESP:[void 0,"\u20A7",0],EUR:["\u20AC"],FJD:[void 0,"$"],FKP:[void 0,"\xA3"],GBP:["\xA3"],GEL:[void 0,"\u20BE"],GHS:[void 0,"GH\u20B5"],GIP:[void 0,"\xA3"],GNF:[void 0,"FG",0],GTQ:[void 0,"Q"],GYD:[void 0,"$",2],HKD:["HK$","$"],HNL:[void 0,"L"],HRK:[void 0,"kn"],HUF:[void 0,"Ft",2],IDR:[void 0,"Rp",2],ILS:["\u20AA"],INR:["\u20B9"],IQD:[void 0,void 0,0],IRR:[void 0,void 0,0],ISK:[void 0,"kr",0],ITL:[void 0,void 0,0],JMD:[void 0,"$"],JOD:[void 0,void 0,3],JPY:["\xA5",void 0,0],KHR:[void 0,"\u17DB"],KMF:[void 0,"CF",0],KPW:[void 0,"\u20A9",0],KRW:["\u20A9",void 0,0],KWD:[void 0,void 0,3],KYD:[void 0,"$"],KZT:[void 0,"\u20B8"],LAK:[void 0,"\u20AD",0],LBP:[void 0,"L\xA3",0],LKR:[void 0,"Rs"],LRD:[void 0,"$"],LTL:[void 0,"Lt"],LUF:[void 0,void 0,0],LVL:[void 0,"Ls"],LYD:[void 0,void 0,3],MGA:[void 0,"Ar",0],MGF:[void 0,void 0,0],MMK:[void 0,"K",0],MNT:[void 0,"\u20AE",2],MRO:[void 0,void 0,0],MUR:[void 0,"Rs",2],MXN:["MX$","$"],MYR:[void 0,"RM"],NAD:[void 0,"$"],NGN:[void 0,"\u20A6"],NIO:[void 0,"C$"],NOK:[void 0,"kr",2],NPR:[void 0,"Rs"],NZD:["NZ$","$"],OMR:[void 0,void 0,3],PHP:["\u20B1"],PKR:[void 0,"Rs",2],PLN:[void 0,"z\u0142"],PYG:[void 0,"\u20B2",0],RON:[void 0,"lei"],RSD:[void 0,void 0,0],RUB:[void 0,"\u20BD"],RWF:[void 0,"RF",0],SBD:[void 0,"$"],SEK:[void 0,"kr",2],SGD:[void 0,"$"],SHP:[void 0,"\xA3"],SLE:[void 0,void 0,2],SLL:[void 0,void 0,0],SOS:[void 0,void 0,0],SRD:[void 0,"$"],SSP:[void 0,"\xA3"],STD:[void 0,void 0,0],STN:[void 0,"Db"],SYP:[void 0,"\xA3",0],THB:[void 0,"\u0E3F"],TMM:[void 0,void 0,0],TND:[void 0,void 0,3],TOP:[void 0,"T$"],TRL:[void 0,void 0,0],TRY:[void 0,"\u20BA"],TTD:[void 0,"$"],TWD:["NT$","$",2],TZS:[void 0,void 0,2],UAH:[void 0,"\u20B4"],UGX:[void 0,void 0,0],USD:["$"],UYI:[void 0,void 0,0],UYU:[void 0,"$"],UYW:[void 0,void 0,4],UZS:[void 0,void 0,2],VEF:[void 0,"Bs",2],VND:["\u20AB",void 0,0],VUV:[void 0,void 0,0],XAF:["FCFA",void 0,0],XCD:["EC$","$"],XOF:["F\u202FCFA",void 0,0],XPF:["CFPF",void 0,0],XXX:["\xA4"],YER:[void 0,void 0,0],ZAR:[void 0,"R"],ZMK:[void 0,void 0,0],ZMW:[void 0,"ZK"],ZWD:[void 0,void 0,0]},cf=function(e){return e[e.Decimal=0]="Decimal",e[e.Percent=1]="Percent",e[e.Currency=2]="Currency",e[e.Scientific=3]="Scientific",e}(cf||{});var be=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(be||{}),G=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(G||{}),ke=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(ke||{}),Fe={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function Zy(e){return Pe(e)[ee.LocaleId]}function Yy(e,t,n){let r=Pe(e),o=[r[ee.DayPeriodsFormat],r[ee.DayPeriodsStandalone]],i=qe(o,t);return qe(i,n)}function Qy(e,t,n){let r=Pe(e),o=[r[ee.DaysFormat],r[ee.DaysStandalone]],i=qe(o,t);return qe(i,n)}function Ky(e,t,n){let r=Pe(e),o=[r[ee.MonthsFormat],r[ee.MonthsStandalone]],i=qe(o,t);return qe(i,n)}function Jy(e,t){let r=Pe(e)[ee.Eras];return qe(r,t)}function Go(e,t){let n=Pe(e);return qe(n[ee.DateFormat],t)}function Wo(e,t){let n=Pe(e);return qe(n[ee.TimeFormat],t)}function Zo(e,t){let r=Pe(e)[ee.DateTimeFormat];return qe(r,t)}function Ct(e,t){let n=Pe(e),r=n[ee.NumberSymbols][t];if(typeof r>"u"){if(t===Fe.CurrencyDecimal)return n[ee.NumberSymbols][Fe.Decimal];if(t===Fe.CurrencyGroup)return n[ee.NumberSymbols][Fe.Group]}return r}function Xy(e,t){return Pe(e)[ee.NumberFormats][t]}function z0(e){return Pe(e)[ee.Currencies]}function eD(e){if(!e[ee.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[ee.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function tD(e){let t=Pe(e);return eD(t),(t[ee.ExtraData][2]||[]).map(r=>typeof r=="string"?ef(r):[ef(r[0]),ef(r[1])])}function nD(e,t,n){let r=Pe(e);eD(r);let o=[r[ee.ExtraData][0],r[ee.ExtraData][1]],i=qe(o,t)||[];return qe(i,n)||[]}function qe(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function ef(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}function rD(e,t,n="en"){let r=z0(n)[e]||Wy[e]||[],o=r[1];return t==="narrow"&&typeof o=="string"?o:r[0]||e}var q0=2;function oD(e){let t,n=Wy[e];return n&&(t=n[2]),typeof t=="number"?t:q0}var G0=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Ma={},W0=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function iD(e,t,n,r){let o=nN(e);t=qt(n,t)||t;let s=[],a;for(;t;)if(a=W0.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let c=o.getTimezoneOffset();r&&(c=aD(r,c),o=tN(o,r));let u="";return s.forEach(l=>{let d=X0(l);u+=d?d(o,n,c):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}function xa(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function qt(e,t){let n=Zy(e);if(Ma[n]??={},Ma[n][t])return Ma[n][t];let r="";switch(t){case"shortDate":r=Go(e,ke.Short);break;case"mediumDate":r=Go(e,ke.Medium);break;case"longDate":r=Go(e,ke.Long);break;case"fullDate":r=Go(e,ke.Full);break;case"shortTime":r=Wo(e,ke.Short);break;case"mediumTime":r=Wo(e,ke.Medium);break;case"longTime":r=Wo(e,ke.Long);break;case"fullTime":r=Wo(e,ke.Full);break;case"short":let o=qt(e,"shortTime"),i=qt(e,"shortDate");r=_a(Zo(e,ke.Short),[o,i]);break;case"medium":let s=qt(e,"mediumTime"),a=qt(e,"mediumDate");r=_a(Zo(e,ke.Medium),[s,a]);break;case"long":let c=qt(e,"longTime"),u=qt(e,"longDate");r=_a(Zo(e,ke.Long),[c,u]);break;case"full":let l=qt(e,"fullTime"),d=qt(e,"fullDate");r=_a(Zo(e,ke.Full),[l,d]);break}return r&&(Ma[n][t]=r),r}function _a(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function it(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function Z0(e,t){return it(e,3).substring(0,t)}function se(e,t,n=0,r=!1,o=!1){return function(i,s){let a=Y0(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return Z0(a,t);let c=Ct(s,Fe.MinusSign);return it(a,t,c,r,o)}}function Y0(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function Q(e,t,n=be.Format,r=!1){return function(o,i){return Q0(o,i,e,t,n,r)}}function Q0(e,t,n,r,o,i){switch(n){case 2:return Ky(t,o,r)[e.getMonth()];case 1:return Qy(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let u=tD(t),l=nD(t,o,r),d=u.findIndex(h=>{if(Array.isArray(h)){let[f,p]=h,m=s>=f.hours&&a>=f.minutes,D=s<p.hours||s===p.hours&&a<p.minutes;if(f.hours<p.hours){if(m&&D)return!0}else if(m||D)return!0}else if(h.hours===s&&h.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return Yy(t,o,r)[s<12?0:1];case 3:return Jy(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new Error(`unexpected translation type ${c}`)}}function Na(e){return function(t,n,r){let o=-1*r,i=Ct(n,Fe.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+it(s,2,i)+it(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+it(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+it(s,2,i)+":"+it(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+it(s,2,i)+":"+it(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var K0=0,Aa=4;function J0(e){let t=xa(e,K0,1).getDay();return xa(e,0,1+(t<=Aa?Aa:Aa+7)-t)}function sD(e){let t=e.getDay(),n=t===0?-3:Aa-t;return xa(e.getFullYear(),e.getMonth(),e.getDate()+n)}function tf(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=sD(n),s=J0(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return it(o,e,Ct(r,Fe.MinusSign))}}function Ra(e,t=!1){return function(n,r){let i=sD(n).getFullYear();return it(i,e,Ct(r,Fe.MinusSign),t)}}var nf={};function X0(e){if(nf[e])return nf[e];let t;switch(e){case"G":case"GG":case"GGG":t=Q(3,G.Abbreviated);break;case"GGGG":t=Q(3,G.Wide);break;case"GGGGG":t=Q(3,G.Narrow);break;case"y":t=se(0,1,0,!1,!0);break;case"yy":t=se(0,2,0,!0,!0);break;case"yyy":t=se(0,3,0,!1,!0);break;case"yyyy":t=se(0,4,0,!1,!0);break;case"Y":t=Ra(1);break;case"YY":t=Ra(2,!0);break;case"YYY":t=Ra(3);break;case"YYYY":t=Ra(4);break;case"M":case"L":t=se(1,1,1);break;case"MM":case"LL":t=se(1,2,1);break;case"MMM":t=Q(2,G.Abbreviated);break;case"MMMM":t=Q(2,G.Wide);break;case"MMMMM":t=Q(2,G.Narrow);break;case"LLL":t=Q(2,G.Abbreviated,be.Standalone);break;case"LLLL":t=Q(2,G.Wide,be.Standalone);break;case"LLLLL":t=Q(2,G.Narrow,be.Standalone);break;case"w":t=tf(1);break;case"ww":t=tf(2);break;case"W":t=tf(1,!0);break;case"d":t=se(2,1);break;case"dd":t=se(2,2);break;case"c":case"cc":t=se(7,1);break;case"ccc":t=Q(1,G.Abbreviated,be.Standalone);break;case"cccc":t=Q(1,G.Wide,be.Standalone);break;case"ccccc":t=Q(1,G.Narrow,be.Standalone);break;case"cccccc":t=Q(1,G.Short,be.Standalone);break;case"E":case"EE":case"EEE":t=Q(1,G.Abbreviated);break;case"EEEE":t=Q(1,G.Wide);break;case"EEEEE":t=Q(1,G.Narrow);break;case"EEEEEE":t=Q(1,G.Short);break;case"a":case"aa":case"aaa":t=Q(0,G.Abbreviated);break;case"aaaa":t=Q(0,G.Wide);break;case"aaaaa":t=Q(0,G.Narrow);break;case"b":case"bb":case"bbb":t=Q(0,G.Abbreviated,be.Standalone,!0);break;case"bbbb":t=Q(0,G.Wide,be.Standalone,!0);break;case"bbbbb":t=Q(0,G.Narrow,be.Standalone,!0);break;case"B":case"BB":case"BBB":t=Q(0,G.Abbreviated,be.Format,!0);break;case"BBBB":t=Q(0,G.Wide,be.Format,!0);break;case"BBBBB":t=Q(0,G.Narrow,be.Format,!0);break;case"h":t=se(3,1,-12);break;case"hh":t=se(3,2,-12);break;case"H":t=se(3,1);break;case"HH":t=se(3,2);break;case"m":t=se(4,1);break;case"mm":t=se(4,2);break;case"s":t=se(5,1);break;case"ss":t=se(5,2);break;case"S":t=se(6,1);break;case"SS":t=se(6,2);break;case"SSS":t=se(6,3);break;case"Z":case"ZZ":case"ZZZ":t=Na(0);break;case"ZZZZZ":t=Na(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Na(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=Na(2);break;default:return null}return nf[e]=t,t}function aD(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function eN(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function tN(e,t,n){let o=e.getTimezoneOffset(),i=aD(t,o);return eN(e,-1*(i-o))}function nN(e){if(By(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return xa(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(G0))return rN(r)}let t=new Date(e);if(!By(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function rN(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,c=Number(e[6]||0),u=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,c,u),t}function By(e){return e instanceof Date&&!isNaN(e.valueOf())}var oN=/^(\d+)?\.((\d+)(-(\d+))?)?$/,$y=22,Oa=".",Yo="0",iN=";",sN=",",rf="#",Hy="\xA4";function aN(e,t,n,r,o,i,s=!1){let a="",c=!1;if(!isFinite(e))a=Ct(n,Fe.Infinity);else{let u=lN(e);s&&(u=uN(u));let l=t.minInt,d=t.minFrac,h=t.maxFrac;if(i){let V=i.match(oN);if(V===null)throw new Error(`${i} is not a valid digit info`);let j=V[1],Te=V[3],Ge=V[5];j!=null&&(l=of(j)),Te!=null&&(d=of(Te)),Ge!=null?h=of(Ge):Te!=null&&d>h&&(h=d)}dN(u,d,h);let f=u.digits,p=u.integerLen,m=u.exponent,D=[];for(c=f.every(V=>!V);p<l;p++)f.unshift(0);for(;p<0;p++)f.unshift(0);p>0?D=f.splice(p,f.length):(D=f,f=[0]);let I=[];for(f.length>=t.lgSize&&I.unshift(f.splice(-t.lgSize,f.length).join(""));f.length>t.gSize;)I.unshift(f.splice(-t.gSize,f.length).join(""));f.length&&I.unshift(f.join("")),a=I.join(Ct(n,r)),D.length&&(a+=Ct(n,o)+D.join("")),m&&(a+=Ct(n,Fe.Exponential)+"+"+m)}return e<0&&!c?a=t.negPre+a+t.negSuf:a=t.posPre+a+t.posSuf,a}function cD(e,t,n,r,o){let i=Xy(t,cf.Currency),s=cN(i,Ct(t,Fe.MinusSign));return s.minFrac=oD(r),s.maxFrac=s.minFrac,aN(e,s,t,Fe.CurrencyGroup,Fe.CurrencyDecimal,o).replace(Hy,n).replace(Hy,"").trim()}function cN(e,t="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=e.split(iN),o=r[0],i=r[1],s=o.indexOf(Oa)!==-1?o.split(Oa):[o.substring(0,o.lastIndexOf(Yo)+1),o.substring(o.lastIndexOf(Yo)+1)],a=s[0],c=s[1]||"";n.posPre=a.substring(0,a.indexOf(rf));for(let l=0;l<c.length;l++){let d=c.charAt(l);d===Yo?n.minFrac=n.maxFrac=l+1:d===rf?n.maxFrac=l+1:n.posSuf+=d}let u=a.split(sN);if(n.gSize=u[1]?u[1].length:0,n.lgSize=u[2]||u[1]?(u[2]||u[1]).length:0,i){let l=o.length-n.posPre.length-n.posSuf.length,d=i.indexOf(rf);n.negPre=i.substring(0,d).replace(/'/g,""),n.negSuf=i.slice(d+l).replace(/'/g,"")}else n.negPre=t+n.posPre,n.negSuf=n.posSuf;return n}function uN(e){if(e.digits[0]===0)return e;let t=e.digits.length-e.integerLen;return e.exponent?e.exponent+=2:(t===0?e.digits.push(0,0):t===1&&e.digits.push(0),e.integerLen+=2),e}function lN(e){let t=Math.abs(e)+"",n=0,r,o,i,s,a;for((o=t.indexOf(Oa))>-1&&(t=t.replace(Oa,"")),(i=t.search(/e/i))>0?(o<0&&(o=i),o+=+t.slice(i+1),t=t.substring(0,i)):o<0&&(o=t.length),i=0;t.charAt(i)===Yo;i++);if(i===(a=t.length))r=[0],o=1;else{for(a--;t.charAt(a)===Yo;)a--;for(o-=i,r=[],s=0;i<=a;i++,s++)r[s]=Number(t.charAt(i))}return o>$y&&(r=r.splice(0,$y-1),n=o-1,o=1),{digits:r,exponent:n,integerLen:o}}function dN(e,t,n){if(t>n)throw new Error(`The minimum number of digits after fraction (${t}) is higher than the maximum (${n}).`);let r=e.digits,o=r.length-e.integerLen,i=Math.min(Math.max(t,o),n),s=i+e.integerLen,a=r[s];if(s>0){r.splice(Math.max(e.integerLen,s));for(let d=s;d<r.length;d++)r[d]=0}else{o=Math.max(0,o),e.integerLen=1,r.length=Math.max(1,s=i+1),r[0]=0;for(let d=1;d<s;d++)r[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)r.unshift(0),e.integerLen++;r.unshift(1),e.integerLen++}else r[s-1]++;for(;o<Math.max(0,i);o++)r.push(0);let c=i!==0,u=t+e.integerLen,l=r.reduceRight(function(d,h,f,p){return h=h+d,p[f]=h<10?h:h-10,c&&(p[f]===0&&f>=u?p.pop():c=!1),h>=10?1:0},0);l&&(r.unshift(l),e.integerLen++)}function of(e){let t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}var sf=/\s+/,zy=[],fN=(()=>{class e{_ngEl;_renderer;initialClasses=zy;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(sf):zy}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(sf):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(sf).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(H(yt),H(_r))};static \u0275dir=wt({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Pa=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},uD=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Pa(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),qy(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);qy(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(H(zt),H(Pn),H(Kd))};static \u0275dir=wt({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function qy(e,t){e.context.$implicit=t.item}var hN=(()=>{class e{_viewContainer;_context=new ka;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Gy(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Gy(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(H(zt),H(Pn))};static \u0275dir=wt({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),ka=class{$implicit=null;ngIf=null};function Gy(e,t){if(e&&!e.createEmbeddedView)throw new v(2020,!1)}var pN=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(H(zt))};static \u0275dir=wt({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[jn]})}return e})();function lD(e,t){return new v(2100,!1)}var gN="mediumDate",dD=new y(""),fD=new y(""),mN=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??gN,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return iD(n,s,i||this.locale,a)}catch(s){throw lD(e,s.message)}}static \u0275fac=function(r){return new(r||e)(H($o,16),H(dD,24),H(fD,24))};static \u0275pipe=$d({name:"date",type:e,pure:!0})}return e})();var vN=(()=>{class e{_locale;_defaultCurrencyCode;constructor(n,r="USD"){this._locale=n,this._defaultCurrencyCode=r}transform(n,r=this._defaultCurrencyCode,o="symbol",i,s){if(!yN(n))return null;s||=this._locale,typeof o=="boolean"&&(o=o?"symbol":"code");let a=r||this._defaultCurrencyCode;o!=="code"&&(o==="symbol"||o==="symbol-narrow"?a=rD(a,o==="symbol"?"wide":"narrow",s):a=o);try{let c=DN(n);return cD(c,s,a,r,i)}catch(c){throw lD(e,c.message)}}static \u0275fac=function(r){return new(r||e)(H($o,16),H(Ey,16))};static \u0275pipe=$d({name:"currency",type:e,pure:!0})}return e})();function yN(e){return!(e==null||e===""||e!==e)}function DN(e){if(typeof e=="string"&&!isNaN(Number(e)-parseFloat(e)))return Number(e);if(typeof e!="number")throw new Error(`${e} is not a number`);return e}var uf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Et({type:e});static \u0275inj=vt({})}return e})();function Qo(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var lf="browser",hD="server";function EN(e){return e===lf}function Fa(e){return e===hD}var Un=class{};var pD=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>new df})}return e})();var df=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}};var Ar=class{},xr=class{},st=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Va=class{encodeKey(t){return gD(t)}encodeValue(t){return gD(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function IN(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var CN=/%(\d[a-f0-9])/gi,bN={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function gD(e){return encodeURIComponent(e).replace(CN,(t,n)=>bN[n]??t)}function La(e){return`${e}`}var Wt=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Va,t.fromString){if(t.fromObject)throw new v(2805,!1);this.map=IN(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(La):[La(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(La(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(La(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Ua=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function TN(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function mD(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function vD(e){return typeof Blob<"u"&&e instanceof Blob}function yD(e){return typeof FormData<"u"&&e instanceof FormData}function SN(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Ko="Content-Type",Ba="Accept",mf="X-Request-URL",wD="text/plain",ID="application/json",CD=`${ID}, ${wD}, */*`,Rr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(TN(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new st,this.context??=new Ua,!this.params)this.params=new Wt,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||mD(this.body)||vD(this.body)||yD(this.body)||SN(this.body)?this.body:this.body instanceof Wt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||yD(this.body)?null:vD(this.body)?this.body.type||null:mD(this.body)?null:typeof this.body=="string"?wD:this.body instanceof Wt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?ID:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,u=t.headers||this.headers,l=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((h,f)=>h.set(f,t.setHeaders[f]),u)),t.setParams&&(l=Object.keys(t.setParams).reduce((h,f)=>h.set(f,t.setParams[f]),l)),new e(n,r,s,{params:l,headers:u,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Zt=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Zt||{}),Or=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new st,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Jo=class e extends Or{constructor(t={}){super(t)}type=Zt.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},un=class e extends Or{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=Zt.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Gt=class extends Or{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},bD=200,MN=204;function ff(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var TD=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Rr)i=n;else{let c;o.headers instanceof st?c=o.headers:c=new st(o.headers);let u;o.params&&(o.params instanceof Wt?u=o.params:u=new Wt({fromObject:o.params})),i=new Rr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=b(i).pipe(dt(c=>this.handler.handle(c)));if(n instanceof Rr||o.observe==="events")return s;let a=s.pipe(de(c=>c instanceof un));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(k(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new v(2806,!1);return c.body}));case"blob":return a.pipe(k(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new v(2807,!1);return c.body}));case"text":return a.pipe(k(c=>{if(c.body!==null&&typeof c.body!="string")throw new v(2808,!1);return c.body}));case"json":default:return a.pipe(k(c=>c.body))}case"response":return a;default:throw new v(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new Wt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,ff(o,r))}post(n,r,o={}){return this.request("POST",n,ff(o,r))}put(n,r,o={}){return this.request("PUT",n,ff(o,r))}static \u0275fac=function(r){return new(r||e)(C(Ar))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),_N=/^\)\]\}',?\n/;function DD(e){if(e.url)return e.url;let t=mf.toLocaleLowerCase();return e.headers.get(t)}var SD=new y(""),ja=(()=>{class e{fetchImpl=g(hf,{optional:!0})?.fetch??((...n)=>globalThis.fetch(...n));ngZone=g(z);destroyRef=g(Bt);destroyed=!1;constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0})}handle(n){return new F(r=>{let o=new AbortController;return this.doRequest(n,o.signal,r).then(pf,i=>r.error(new Gt({error:i}))),()=>o.abort()})}doRequest(n,r,o){return Wr(this,null,function*(){let i=this.createRequestInit(n),s;try{let f=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,E({signal:r},i)));NN(f),o.next({type:Zt.Sent}),s=yield f}catch(f){o.error(new Gt({error:f,status:f.status??0,statusText:f.statusText,url:n.urlWithParams,headers:f.headers}));return}let a=new st(s.headers),c=s.statusText,u=DD(s)??n.urlWithParams,l=s.status,d=null;if(n.reportProgress&&o.next(new Jo({headers:a,status:l,statusText:c,url:u})),s.body){let f=s.headers.get("content-length"),p=[],m=s.body.getReader(),D=0,I,V,j=typeof Zone<"u"&&Zone.current,Te=!1;if(yield this.ngZone.runOutsideAngular(()=>Wr(this,null,function*(){for(;;){if(this.destroyed){yield m.cancel(),Te=!0;break}let{done:gn,value:bc}=yield m.read();if(gn)break;if(p.push(bc),D+=bc.length,n.reportProgress){V=n.responseType==="text"?(V??"")+(I??=new TextDecoder).decode(bc,{stream:!0}):void 0;let oh=()=>o.next({type:Zt.DownloadProgress,total:f?+f:void 0,loaded:D,partialText:V});j?j.run(oh):oh()}}})),Te){o.complete();return}let Ge=this.concatChunks(p,D);try{let gn=s.headers.get(Ko)??"";d=this.parseBody(n,Ge,gn)}catch(gn){o.error(new Gt({error:gn,headers:new st(s.headers),status:s.status,statusText:s.statusText,url:DD(s)??n.urlWithParams}));return}}l===0&&(l=d?bD:0),l>=200&&l<300?(o.next(new un({body:d,headers:a,status:l,statusText:c,url:u})),o.complete()):o.error(new Gt({error:d,headers:a,status:l,statusText:c,url:u}))})}parseBody(n,r,o){switch(n.responseType){case"json":let i=new TextDecoder().decode(r).replace(_N,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o=n.withCredentials?"include":void 0;if(n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has(Ba)||(r[Ba]=CD),!n.headers.has(Ko)){let i=n.detectContentTypeHeader();i!==null&&(r[Ko]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),hf=class{};function pf(){}function NN(e){e.then(pf,pf)}function MD(e,t){return t(e)}function RN(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function AN(e,t,n){return(r,o)=>xe(n,()=>t(r,i=>e(i,o)))}var _D=new y(""),Ha=new y(""),vf=new y(""),yf=new y("",{providedIn:"root",factory:()=>!0});function xN(){let e=null;return(t,n)=>{e===null&&(e=(g(_D,{optional:!0})??[]).reduceRight(RN,MD));let r=g($t);if(g(yf)){let i=r.add();return e(t,n).pipe(Kt(()=>r.remove(i)))}else return e(t,n)}}var $a=(()=>{class e extends Ar{backend;injector;chain=null;pendingTasks=g($t);contributeToStability=g(yf);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Ha),...this.injector.get(vf,[])]));this.chain=r.reduceRight((o,i)=>AN(o,i,this.injector),MD)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Kt(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(C(xr),C(we))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();var ON=/^\)\]\}',?\n/,PN=RegExp(`^${mf}:`,"m");function kN(e){return"responseURL"in e&&e.responseURL?e.responseURL:PN.test(e.getAllResponseHeaders())?e.getResponseHeader(mf):null}var gf=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new v(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?W(r.\u0275loadImpl()):b(null)).pipe(Se(()=>new F(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((m,D)=>s.setRequestHeader(m,D.join(","))),n.headers.has(Ba)||s.setRequestHeader(Ba,CD),!n.headers.has(Ko)){let m=n.detectContentTypeHeader();m!==null&&s.setRequestHeader(Ko,m)}if(n.responseType){let m=n.responseType.toLowerCase();s.responseType=m!=="json"?m:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let m=s.statusText||"OK",D=new st(s.getAllResponseHeaders()),I=kN(s)||n.url;return c=new Jo({headers:D,status:s.status,statusText:m,url:I}),c},l=()=>{let{headers:m,status:D,statusText:I,url:V}=u(),j=null;D!==MN&&(j=typeof s.response>"u"?s.responseText:s.response),D===0&&(D=j?bD:0);let Te=D>=200&&D<300;if(n.responseType==="json"&&typeof j=="string"){let Ge=j;j=j.replace(ON,"");try{j=j!==""?JSON.parse(j):null}catch(gn){j=Ge,Te&&(Te=!1,j={error:gn,text:j})}}Te?(i.next(new un({body:j,headers:m,status:D,statusText:I,url:V||void 0})),i.complete()):i.error(new Gt({error:j,headers:m,status:D,statusText:I,url:V||void 0}))},d=m=>{let{url:D}=u(),I=new Gt({error:m,status:s.status||0,statusText:s.statusText||"Unknown Error",url:D||void 0});i.error(I)},h=!1,f=m=>{h||(i.next(u()),h=!0);let D={type:Zt.DownloadProgress,loaded:m.loaded};m.lengthComputable&&(D.total=m.total),n.responseType==="text"&&s.responseText&&(D.partialText=s.responseText),i.next(D)},p=m=>{let D={type:Zt.UploadProgress,loaded:m.loaded};m.lengthComputable&&(D.total=m.total),i.next(D)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",p)),s.send(a),i.next({type:Zt.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",p)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(C(Un))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),ND=new y(""),FN="XSRF-TOKEN",LN=new y("",{providedIn:"root",factory:()=>FN}),jN="X-XSRF-TOKEN",VN=new y("",{providedIn:"root",factory:()=>jN}),Xo=class{},UN=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){return null}static \u0275fac=function(r){return new(r||e)(C(ue),C(LN))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function BN(e,t){let n=e.url.toLowerCase();if(!g(ND)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=g(Xo).getToken(),o=g(VN);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var ei=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(ei||{});function Df(e,t){return{\u0275kind:e,\u0275providers:t}}function RD(...e){let t=[TD,gf,$a,{provide:Ar,useExisting:$a},{provide:xr,useFactory:()=>g(SD,{optional:!0})??g(gf)},{provide:Ha,useValue:BN,multi:!0},{provide:ND,useValue:!0},{provide:Xo,useClass:UN}];for(let n of e)t.push(...n.\u0275providers);return Ln(t)}function $N(e){return Df(ei.Interceptors,e.map(t=>({provide:Ha,useValue:t,multi:!0})))}var ED=new y("");function AD(){return Df(ei.LegacyInterceptors,[{provide:ED,useFactory:xN},{provide:Ha,useExisting:ED,multi:!0}])}function HN(){return Df(ei.Fetch,[ja,{provide:SD,useExisting:ja},{provide:xr,useExisting:ja}])}var zN=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Et({type:e});static \u0275inj=vt({providers:[RD(AD())]})}return e})();var qN=new y(""),xD="b",OD="h",PD="s",kD="st",FD="u",LD="rt",Ef=new y(""),GN=["GET","HEAD"];function WN(e,t){let h=g(Ef),{isCacheActive:n}=h,r=ih(h,["isCacheActive"]),{transferCache:o,method:i}=e;if(!n||o===!1||i==="POST"&&!r.includePostRequests&&!o||i!=="POST"&&!GN.includes(i)||!r.includeRequestsWithAuthHeaders&&ZN(e)||r.filter?.(e)===!1)return t(e);let s=g(Oo),a=g(qN,{optional:!0}),c=a?JN(e.url,a):e.url,u=QN(e,c),l=s.get(u,null),d=r.includeHeaders;if(typeof o=="object"&&o.includeHeaders&&(d=o.includeHeaders),l){let{[xD]:f,[LD]:p,[OD]:m,[PD]:D,[kD]:I,[FD]:V}=l,j=f;switch(p){case"arraybuffer":j=new TextEncoder().encode(f).buffer;break;case"blob":j=new Blob([f]);break}let Te=new st(m);return b(new un({body:j,headers:Te,status:D,statusText:I,url:V}))}return t(e).pipe(oe(f=>{f instanceof un&&s.set(u,{[xD]:f.body,[OD]:YN(f.headers,d),[PD]:f.status,[kD]:f.statusText,[FD]:c,[LD]:e.responseType})}))}function ZN(e){return e.headers.has("authorization")||e.headers.has("proxy-authorization")}function YN(e,t){if(!t)return{};let n={};for(let r of t){let o=e.getAll(r);o!==null&&(n[r]=o)}return n}function jD(e){return[...e.keys()].sort().map(t=>`${t}=${e.getAll(t)}`).join("&")}function QN(e,t){let{params:n,method:r,responseType:o}=e,i=jD(n),s=e.serializeBody();s instanceof URLSearchParams?s=jD(s):typeof s!="string"&&(s="");let a=[r,o,t,s,i].join("|"),c=KN(a);return c}function KN(e){let t=0;for(let n of e)t=Math.imul(31,t)+n.charCodeAt(0)<<0;return t+=2147483648,t.toString()}function VD(e){return[{provide:Ef,useFactory:()=>(ze("NgHttpTransferCache"),E({isCacheActive:!0},e))},{provide:vf,useValue:WN,multi:!0},{provide:Bo,multi:!0,useFactory:()=>{let t=g(et),n=g(Ef);return()=>{t.whenStable().then(()=>{n.isCacheActive=!1})}}}]}function JN(e,t){let n=new URL(e,"resolve://").origin,r=t[n];return r?e.replace(n,r):e}var ri=new y(""),bf=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new v(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(C(ri),C(z))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Pr=class{_doc;constructor(t){this._doc=t}manager},za="ng-app-id";function UD(e){for(let t of e)t.remove()}function BD(e,t){let n=t.createElement("style");return n.textContent=e,n}function XN(e,t,n,r){let o=e.head?.querySelectorAll(`style[${za}="${t}"],link[${za}="${t}"]`);if(o)for(let i of o)i.removeAttribute(za),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function If(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Tf=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Fa(i),XN(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,BD);r?.forEach(o=>this.addUsage(o,this.external,If))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(UD(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])UD(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,BD(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,If(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(za,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(C(ue),C(ua),C(fd,8),C(xo))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),wf={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Sf=/%COMP%/g;var HD="%COMP%",eR=`_nghost-${HD}`,tR=`_ngcontent-${HD}`,nR=!0,rR=new y("",{providedIn:"root",factory:()=>nR});function oR(e){return tR.replace(Sf,e)}function iR(e){return eR.replace(Sf,e)}function zD(e,t){return t.map(n=>n.replace(Sf,e))}var Mf=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=Fa(a),this.defaultRenderer=new ti(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Je.ShadowDom&&(r=B(E({},r),{encapsulation:Je.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof qa?o.applyToHost(n):o instanceof ni&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case Je.Emulated:i=new qa(c,u,r,this.appId,l,s,a,d,h);break;case Je.ShadowDom:return new Cf(c,u,n,r,s,a,this.nonce,d,h);default:i=new ni(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(C(bf),C(Tf),C(ua),C(rR),C(ue),C(xo),C(z),C(fd),C(Sr,8))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),ti=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(wf[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){($D(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&($D(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new v(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=wf[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=wf[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(Vt.DashCase|Vt.Important)?t.style.setProperty(n,r,o&Vt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&Vt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=It().getGlobalEventTarget(this.doc,t),!t))throw new v(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function $D(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Cf=class extends ti{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=zD(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=If(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},ni=class extends ti{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?zD(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},qa=class extends ni{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=oR(l),this.hostAttr=iR(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var _f=class e extends zo{supportsDOMEvents=!0;static makeCurrent(){Jd(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=sR();return n==null?null:aR(n)}resetBaseElement(){oi=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Qo(document.cookie,t)}},oi=null;function sR(){return oi=oi||document.head.querySelector("base"),oi?oi.getAttribute("href"):null}function aR(e){return new URL(e,document.baseURI).pathname}var Ga=class{addToWindow(t){fe.getAngularTestability=(r,o=!0)=>{let i=t.findTestabilityInTree(r,o);if(i==null)throw new v(5103,!1);return i},fe.getAllAngularTestabilities=()=>t.getAllTestabilities(),fe.getAllAngularRootElements=()=>t.getAllRootElements();let n=r=>{let o=fe.getAllAngularTestabilities(),i=o.length,s=function(){i--,i==0&&r()};o.forEach(a=>{a.whenStable(s)})};fe.frameworkStabilizers||(fe.frameworkStabilizers=[]),fe.frameworkStabilizers.push(n)}findTestabilityInTree(t,n,r){if(n==null)return null;let o=t.getTestability(n);return o??(r?It().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},cR=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),GD=(()=>{class e extends Pr{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(C(ue))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),qD=["alt","control","meta","shift"],uR={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},lR={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},WD=(()=>{class e extends Pr{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>It().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),qD.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=uR[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),qD.forEach(s=>{if(s!==o){let a=lR[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(C(ue))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function dR(){return new mt}var fR=[{provide:Vo,useClass:Ga},{provide:qd,useClass:Ia,deps:[z,Ca,Vo]},{provide:Ia,useClass:Ia,deps:[z,Ca,Vo]}],hR=[{provide:Xs,useValue:"root"},{provide:mt,useFactory:dR},{provide:ri,useClass:GD,multi:!0,deps:[ue]},{provide:ri,useClass:WD,multi:!0,deps:[ue]},Mf,Tf,bf,{provide:vr,useExisting:Mf},{provide:Un,useClass:cR},[]],pR=(()=>{class e{constructor(){}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Et({type:e});static \u0275inj=vt({providers:[...hR,...fR],imports:[uf,Ty]})}return e})();var ZD=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(C(ue))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var gR=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=C(mR),o},providedIn:"root"})}return e})(),mR=(()=>{class e extends gR{_doc;constructor(n){super(),this._doc=n}sanitize(n,r){if(r==null)return null;switch(n){case Dt.NONE:return r;case Dt.HTML:return on(r,"HTML")?nt(r):Fm(this._doc,String(r)).toString();case Dt.STYLE:return on(r,"Style")?nt(r):r;case Dt.SCRIPT:if(on(r,"Script"))return nt(r);throw new v(5200,!1);case Dt.URL:return on(r,"URL")?nt(r):fa(String(r));case Dt.RESOURCE_URL:if(on(r,"ResourceURL"))return nt(r);throw new v(5201,!1);default:throw new v(5202,!1)}}bypassSecurityTrustHtml(n){return Sm(n)}bypassSecurityTrustStyle(n){return Mm(n)}bypassSecurityTrustScript(n){return _m(n)}bypassSecurityTrustUrl(n){return Nm(n)}bypassSecurityTrustResourceUrl(n){return Rm(n)}static \u0275fac=function(r){return new(r||e)(C(ue))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Wa=function(e){return e[e.NoHttpTransferCache=0]="NoHttpTransferCache",e[e.HttpTransferCacheOptions=1]="HttpTransferCacheOptions",e[e.I18nSupport=2]="I18nSupport",e[e.EventReplay=3]="EventReplay",e[e.IncrementalHydration=4]="IncrementalHydration",e}(Wa||{});function vR(e,t=[],n={}){return{\u0275kind:e,\u0275providers:t}}function D$(){return vR(Wa.EventReplay,Sy())}function E$(...e){let t=[],n=new Set;for(let{\u0275providers:o,\u0275kind:i}of e)n.add(i),o.length&&t.push(o);let r=n.has(Wa.HttpTransferCacheOptions);return Ln([[],xy(),n.has(Wa.NoHttpTransferCache)||r?[]:VD({}),t])}var x="primary",vi=Symbol("RouteTitle"),Of=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Hn(e){return new Of(e)}function nE(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function DR(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!bt(e[n],t[n]))return!1;return!0}function bt(e,t){let n=e?Pf(e):void 0,r=t?Pf(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!rE(e[o],t[o]))return!1;return!0}function Pf(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function rE(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function oE(e){return e.length>0?e[e.length-1]:null}function pn(e){return Yc(e)?e:Uo(e)?W(Promise.resolve(e)):b(e)}var ER={exact:sE,subset:aE},iE={exact:wR,subset:IR,ignored:()=>!0};function YD(e,t,n){return ER[n.paths](e.root,t.root,n.matrixParams)&&iE[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function wR(e,t){return bt(e,t)}function sE(e,t,n){if(!Bn(e.segments,t.segments)||!Qa(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!sE(e.children[r],t.children[r],n))return!1;return!0}function IR(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>rE(e[n],t[n]))}function aE(e,t,n){return cE(e,t,t.segments,n)}function cE(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!Bn(o,n)||t.hasChildren()||!Qa(o,n,r))}else if(e.segments.length===n.length){if(!Bn(e.segments,n)||!Qa(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!aE(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!Bn(e.segments,o)||!Qa(e.segments,o,r)||!e.children[x]?!1:cE(e.children[x],t,i,r)}}function Qa(e,t,n){return t.every((r,o)=>iE[n](e[o].parameters,r.parameters))}var St=class{root;queryParams;fragment;_queryParamMap;constructor(t=new U([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Hn(this.queryParams),this._queryParamMap}toString(){return TR.serialize(this)}},U=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ka(this)}},ln=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Hn(this.parameters),this._parameterMap}toString(){return lE(this)}};function CR(e,t){return Bn(e,t)&&e.every((n,r)=>bt(n.parameters,t[r].parameters))}function Bn(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function bR(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===x&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==x&&(n=n.concat(t(o,r)))}),n}var zn=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new dn,providedIn:"root"})}return e})(),dn=class{parse(t){let n=new Ff(t);return new St(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${ii(t.root,!0)}`,r=_R(t.queryParams),o=typeof t.fragment=="string"?`#${SR(t.fragment)}`:"";return`${n}${r}${o}`}},TR=new dn;function Ka(e){return e.segments.map(t=>lE(t)).join("/")}function ii(e,t){if(!e.hasChildren())return Ka(e);if(t){let n=e.children[x]?ii(e.children[x],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==x&&r.push(`${o}:${ii(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=bR(e,(r,o)=>o===x?[ii(e.children[x],!1)]:[`${o}:${ii(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[x]!=null?`${Ka(e)}/${n[0]}`:`${Ka(e)}/(${n.join("//")})`}}function uE(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Za(e){return uE(e).replace(/%3B/gi,";")}function SR(e){return encodeURI(e)}function kf(e){return uE(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Ja(e){return decodeURIComponent(e)}function QD(e){return Ja(e.replace(/\+/g,"%20"))}function lE(e){return`${kf(e.path)}${MR(e.parameters)}`}function MR(e){return Object.entries(e).map(([t,n])=>`;${kf(t)}=${kf(n)}`).join("")}function _R(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${Za(n)}=${Za(o)}`).join("&"):`${Za(n)}=${Za(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var NR=/^[^\/()?;#]+/;function Nf(e){let t=e.match(NR);return t?t[0]:""}var RR=/^[^\/()?;=#]+/;function AR(e){let t=e.match(RR);return t?t[0]:""}var xR=/^[^=?&#]+/;function OR(e){let t=e.match(xR);return t?t[0]:""}var PR=/^[^&#]+/;function kR(e){let t=e.match(PR);return t?t[0]:""}var Ff=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new U([],{}):new U([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[x]=new U(t,n)),r}parseSegment(){let t=Nf(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new v(4009,!1);return this.capture(t),new ln(Ja(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=AR(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=Nf(this.remaining);o&&(r=o,this.capture(r))}t[Ja(n)]=Ja(r)}parseQueryParam(t){let n=OR(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=kR(this.remaining);s&&(r=s,this.capture(r))}let o=QD(n),i=QD(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Nf(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new v(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=x);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[x]:new U([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new v(4011,!1)}};function dE(e){return e.segments.length>0?new U([],{[x]:e}):e}function fE(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=fE(o);if(r===x&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new U(e.segments,t);return FR(n)}function FR(e){if(e.numberOfChildren===1&&e.children[x]){let t=e.children[x];return new U(e.segments.concat(t.segments),t.children)}return e}function fn(e){return e instanceof St}function hE(e,t,n=null,r=null){let o=pE(e);return gE(o,t,n,r)}function pE(e){let t;function n(i){let s={};for(let c of i.children){let u=n(c);s[c.outlet]=u}let a=new U(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=dE(r);return t??o}function gE(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Rf(o,o,o,n,r);let i=LR(t);if(i.toRoot())return Rf(o,o,new U([],{}),n,r);let s=jR(i,o,e),a=s.processChildren?ai(s.segmentGroup,s.index,i.commands):vE(s.segmentGroup,s.index,i.commands);return Rf(o,s.segmentGroup,a,n,r)}function ec(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function ui(e){return typeof e=="object"&&e!=null&&e.outlets}function Rf(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===t?s=n:s=mE(e,t,n);let a=dE(fE(s));return new St(a,i,o)}function mE(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=mE(i,t,n)}),new U(e.segments,r)}var tc=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&ec(r[0]))throw new v(4003,!1);let o=r.find(ui);if(o&&o!==oE(r))throw new v(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function LR(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new tc(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new tc(n,t,r)}var Lr=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function jR(e,t,n){if(e.isAbsolute)return new Lr(t,!0,0);if(!n)return new Lr(t,!1,NaN);if(n.parent===null)return new Lr(n,!0,0);let r=ec(e.commands[0])?0:1,o=n.segments.length-1+r;return VR(n,o,e.numberOfDoubleDots)}function VR(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new v(4005,!1);o=r.segments.length}return new Lr(r,!1,o-i)}function UR(e){return ui(e[0])?e[0].outlets:{[x]:e}}function vE(e,t,n){if(e??=new U([],{}),e.segments.length===0&&e.hasChildren())return ai(e,t,n);let r=BR(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new U(e.segments.slice(0,r.pathIndex),{});return i.children[x]=new U(e.segments.slice(r.pathIndex),e.children),ai(i,0,o)}else return r.match&&o.length===0?new U(e.segments,{}):r.match&&!e.hasChildren()?Lf(e,t,n):r.match?ai(e,0,o):Lf(e,t,n)}function ai(e,t,n){if(n.length===0)return new U(e.segments,{});{let r=UR(n),o={};if(Object.keys(r).some(i=>i!==x)&&e.children[x]&&e.numberOfChildren===1&&e.children[x].segments.length===0){let i=ai(e.children[x],t,n);return new U(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=vE(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new U(e.segments,o)}}function BR(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(ui(a))break;let c=`${a}`,u=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!JD(c,u,s))return i;r+=2}else{if(!JD(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Lf(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(ui(i)){let c=$R(i.outlets);return new U(r,c)}if(o===0&&ec(n[0])){let c=e.segments[t];r.push(new ln(c.path,KD(n[0]))),o++;continue}let s=ui(i)?i.outlets[x]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&ec(a)?(r.push(new ln(s,KD(a))),o+=2):(r.push(new ln(s,{})),o++)}return new U(r,{})}function $R(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=Lf(new U([],{}),0,r))}),t}function KD(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function JD(e,t,n){return e==n.path&&bt(t,n.parameters)}var Xa="imperative",le=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(le||{}),Ue=class{id;url;constructor(t,n){this.id=t,this.url=n}},hn=class extends Ue{type=le.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Be=class extends Ue{urlAfterRedirects;type=le.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Le=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(Le||{}),Vr=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Vr||{}),Tt=class extends Ue{reason;code;type=le.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Mt=class extends Ue{reason;code;type=le.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},Ur=class extends Ue{error;target;type=le.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},li=class extends Ue{urlAfterRedirects;state;type=le.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},nc=class extends Ue{urlAfterRedirects;state;type=le.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},rc=class extends Ue{urlAfterRedirects;state;shouldActivate;type=le.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},oc=class extends Ue{urlAfterRedirects;state;type=le.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ic=class extends Ue{urlAfterRedirects;state;type=le.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},sc=class{route;type=le.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},ac=class{route;type=le.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},cc=class{snapshot;type=le.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},uc=class{snapshot;type=le.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},lc=class{snapshot;type=le.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},dc=class{snapshot;type=le.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Br=class{routerEvent;position;anchor;type=le.Scroll;constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},di=class{},$r=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function HR(e,t){return e.providers&&!e._injector&&(e._injector=jo(e.providers,t,`Route: ${e.path}`)),e._injector??t}function at(e){return e.outlet||x}function zR(e,t){let n=e.filter(r=>at(r)===t);return n.push(...e.filter(r=>at(r)!==t)),n}function yi(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var fc=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return yi(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new qn(this.rootInjector)}},qn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new fc(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(C(we))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),hc=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=jf(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=jf(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=Vf(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return Vf(t,this._root).map(n=>n.value)}};function jf(e,t){if(e===t.value)return t;for(let n of t.children){let r=jf(e,n);if(r)return r}return null}function Vf(e,t){if(e===t.value)return[t];for(let n of t.children){let r=Vf(e,n);if(r.length)return r.unshift(t),r}return[]}var Ve=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function Fr(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var fi=class extends hc{snapshot;constructor(t,n){super(t),this.snapshot=n,Wf(this,t)}toString(){return this.snapshot.toString()}};function yE(e){let t=qR(e),n=new me([new ln("",{})]),r=new me({}),o=new me({}),i=new me({}),s=new me(""),a=new Yt(n,r,i,s,o,x,e,t.root);return a.snapshot=t.root,new fi(new Ve(a,[]),t)}function qR(e){let t={},n={},r={},o="",i=new $n([],t,r,o,n,x,e,null,{});return new hi("",new Ve(i,[]))}var Yt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(k(u=>u[vi]))??b(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(k(t=>Hn(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(k(t=>Hn(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function pc(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:E(E({},t.params),e.params),data:E(E({},t.data),e.data),resolve:E(E(E(E({},e.data),t.data),o?.data),e._resolvedData)}:r={params:E({},e.params),data:E({},e.data),resolve:E(E({},e.data),e._resolvedData??{})},o&&EE(o)&&(r.resolve[vi]=o.title),r}var $n=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[vi]}constructor(t,n,r,o,i,s,a,c,u){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Hn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Hn(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},hi=class extends hc{url;constructor(t,n){super(n),this.url=t,Wf(this,n)}toString(){return DE(this._root)}};function Wf(e,t){t.value._routerState=e,t.children.forEach(n=>Wf(e,n))}function DE(e){let t=e.children.length>0?` { ${e.children.map(DE).join(", ")} } `:"";return`${e.value}${t}`}function Af(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,bt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),bt(t.params,n.params)||e.paramsSubject.next(n.params),DR(t.url,n.url)||e.urlSubject.next(n.url),bt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Uf(e,t){let n=bt(e.params,t.params)&&CR(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Uf(e.parent,t.parent))}function EE(e){return typeof e.title=="string"||e.title===null}var wE=new y(""),Zf=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=x;activateEvents=new Me;deactivateEvents=new Me;attachEvents=new Me;detachEvents=new Me;routerOutletData=Xg(void 0);parentContexts=g(qn);location=g(zt);changeDetector=g(Nr);inputBinder=g(Di,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new v(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new v(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new v(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new v(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Bf(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=wt({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[jn]})}return e})(),Bf=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===Yt?this.route:t===qn?this.childContexts:t===wE?this.outletData:this.parent.get(t,n)}},Di=new y(""),Yf=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=io([r.queryParams,r.params,r.data]).pipe(Se(([i,s,a],c)=>(a=E(E(E({},i),s),a),c===0?b(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=Fy(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Qf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Pv({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Yd(0,"router-outlet")},dependencies:[Zf],encapsulation:2})}return e})();function Kf(e){let t=e.children&&e.children.map(Kf),n=t?B(E({},e),{children:t}):E({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==x&&(n.component=Qf),n}function GR(e,t,n){let r=pi(e,t._root,n?n._root:void 0);return new fi(r,t)}function pi(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=WR(e,t,n);return new Ve(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>pi(e,a)),s}}let r=ZR(t.value),o=t.children.map(i=>pi(e,i));return new Ve(r,o)}}function WR(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return pi(e,r,o);return pi(e,r)})}function ZR(e){return new Yt(new me(e.url),new me(e.params),new me(e.queryParams),new me(e.fragment),new me(e.data),e.outlet,e.component,e)}var Hr=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},IE="ngNavigationCancelingError";function gc(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=fn(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=CE(!1,Le.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function CE(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[IE]=!0,n.cancellationCode=t,n}function YR(e){return bE(e)&&fn(e.url)}function bE(e){return!!e&&e[IE]}var QR=(e,t,n,r)=>k(o=>(new $f(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),$f=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),Af(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=Fr(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Fr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Fr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=Fr(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new dc(i.value.snapshot))}),t.children.length&&this.forwardEvent(new uc(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(Af(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Af(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},mc=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},jr=class{component;route;constructor(t,n){this.component=t,this.route=n}};function KR(e,t,n){let r=e._root,o=t?t._root:null;return si(r,o,n,[r.value])}function JR(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function qr(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!Yp(e)?e:t.get(e):r}function si(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Fr(t);return e.children.forEach(s=>{XR(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>ci(a,n.getContext(s),o)),o}function XR(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=eA(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new mc(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?si(e,t,a?a.children:null,r,o):si(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new jr(a.outlet.component,s))}else s&&ci(t,a,o),o.canActivateChecks.push(new mc(r)),i.component?si(e,null,a?a.children:null,r,o):si(e,null,n,r,o);return o}function eA(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!Bn(e.url,t.url);case"pathParamsOrQueryParamsChange":return!Bn(e.url,t.url)||!bt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Uf(e,t)||!bt(e.queryParams,t.queryParams);case"paramsChange":default:return!Uf(e,t)}}function ci(e,t,n){let r=Fr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?ci(s,t.children.getContext(i),n):ci(s,null,n):ci(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new jr(t.outlet.component,o)):n.canDeactivateChecks.push(new jr(null,o)):n.canDeactivateChecks.push(new jr(null,o))}function Ei(e){return typeof e=="function"}function tA(e){return typeof e=="boolean"}function nA(e){return e&&Ei(e.canLoad)}function rA(e){return e&&Ei(e.canActivate)}function oA(e){return e&&Ei(e.canActivateChild)}function iA(e){return e&&Ei(e.canDeactivate)}function sA(e){return e&&Ei(e.canMatch)}function TE(e){return e instanceof At||e?.name==="EmptyError"}var Ya=Symbol("INITIAL_VALUE");function zr(){return Se(e=>io(e.map(t=>t.pipe(xt(1),eu(Ya)))).pipe(k(t=>{for(let n of t)if(n!==!0){if(n===Ya)return Ya;if(n===!1||aA(n))return n}return!0}),de(t=>t!==Ya),xt(1)))}function aA(e){return fn(e)||e instanceof Hr}function cA(e,t){return ne(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?b(B(E({},n),{guardsResult:!0})):uA(s,r,o,e).pipe(ne(a=>a&&tA(a)?lA(r,i,e,t):b(a)),k(a=>B(E({},n),{guardsResult:a})))})}function uA(e,t,n,r){return W(e).pipe(ne(o=>gA(o.component,o.route,n,t,r)),Ot(o=>o!==!0,!0))}function lA(e,t,n,r){return W(t).pipe(dt(o=>nr(fA(o.route.parent,r),dA(o.route,r),pA(e,o.path,n),hA(e,o.route,n))),Ot(o=>o!==!0,!0))}function dA(e,t){return e!==null&&t&&t(new lc(e)),b(!0)}function fA(e,t){return e!==null&&t&&t(new cc(e)),b(!0)}function hA(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return b(!0);let o=r.map(i=>rs(()=>{let s=yi(t)??n,a=qr(i,s),c=rA(a)?a.canActivate(t,e):xe(s,()=>a(t,e));return pn(c).pipe(Ot())}));return b(o).pipe(zr())}function pA(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>JR(s)).filter(s=>s!==null).map(s=>rs(()=>{let a=s.guards.map(c=>{let u=yi(s.node)??n,l=qr(c,u),d=oA(l)?l.canActivateChild(r,e):xe(u,()=>l(r,e));return pn(d).pipe(Ot())});return b(a).pipe(zr())}));return b(i).pipe(zr())}function gA(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return b(!0);let s=i.map(a=>{let c=yi(t)??o,u=qr(a,c),l=iA(u)?u.canDeactivate(e,t,n,r):xe(c,()=>u(e,t,n,r));return pn(l).pipe(Ot())});return b(s).pipe(zr())}function mA(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return b(!0);let i=o.map(s=>{let a=qr(s,e),c=nA(a)?a.canLoad(t,n):xe(e,()=>a(t,n));return pn(c)});return b(i).pipe(zr(),SE(r))}function SE(e){return zc(oe(t=>{if(typeof t!="boolean")throw gc(e,t)}),k(t=>t===!0))}function vA(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return b(!0);let i=o.map(s=>{let a=qr(s,e),c=sA(a)?a.canMatch(t,n):xe(e,()=>a(t,n));return pn(c)});return b(i).pipe(zr(),SE(r))}var gi=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},mi=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function kr(e){return tr(new gi(e))}function yA(e){return tr(new v(4e3,!1))}function DA(e){return tr(CE(!1,Le.GuardRejected))}var Hf=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return b(r);if(o.numberOfChildren>1||!o.children[x])return yA(`${t.redirectTo}`);o=o.children[x]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:u,routeConfig:l,url:d,outlet:h,params:f,data:p,title:m}=o,D=xe(i,()=>a({params:f,data:p,queryParams:c,fragment:u,routeConfig:l,url:d,outlet:h,title:m}));if(D instanceof St)throw new mi(D);n=D}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new mi(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new St(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new U(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new v(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},zf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function EA(e,t,n,r,o){let i=ME(e,t,n);return i.matched?(r=HR(t,r),vA(r,t,n,o).pipe(k(s=>s===!0?i:E({},zf)))):b(i)}function ME(e,t,n){if(t.path==="**")return wA(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?E({},zf):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||nE)(n,e,t);if(!o)return E({},zf);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?E(E({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function wA(e){return{matched:!0,parameters:e.length>0?oE(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function XD(e,t,n,r){return n.length>0&&bA(e,n,r)?{segmentGroup:new U(t,CA(r,new U(n,e.children))),slicedSegments:[]}:n.length===0&&TA(e,n,r)?{segmentGroup:new U(e.segments,IA(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new U(e.segments,e.children),slicedSegments:n}}function IA(e,t,n,r){let o={};for(let i of n)if(yc(e,t,i)&&!r[at(i)]){let s=new U([],{});o[at(i)]=s}return E(E({},r),o)}function CA(e,t){let n={};n[x]=t;for(let r of e)if(r.path===""&&at(r)!==x){let o=new U([],{});n[at(r)]=o}return n}function bA(e,t,n){return n.some(r=>yc(e,t,r)&&at(r)!==x)}function TA(e,t,n){return n.some(r=>yc(e,t,r))}function yc(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function SA(e,t,n){return t.length===0&&!e.children[n]}var qf=class{};function MA(e,t,n,r,o,i,s="emptyOnly"){return new Gf(e,t,n,r,o,s,i).recognize()}var _A=31,Gf=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Hf(this.urlSerializer,this.urlTree)}noMatchError(t){return new v(4002,`'${t.segmentGroup}'`)}recognize(){let t=XD(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(k(({children:n,rootSnapshot:r})=>{let o=new Ve(r,n),i=new hi("",o),s=hE(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new $n([],Object.freeze({}),Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),x,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,x,n).pipe(k(r=>({children:r,rootSnapshot:n})),lt(r=>{if(r instanceof mi)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof gi?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(k(s=>s instanceof Ve?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return W(i).pipe(dt(s=>{let a=r.children[s],c=zR(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Kc((s,a)=>(s.push(...a),s)),Qt(null),Qc(),ne(s=>{if(s===null)return kr(r);let a=_E(s);return NA(a),b(a)}))}processSegment(t,n,r,o,i,s,a){return W(n).pipe(dt(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(lt(u=>{if(u instanceof gi)return b(null);throw u}))),Ot(c=>!!c),lt(c=>{if(TE(c))return SA(r,o,i)?b(new qf):kr(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return at(r)!==s&&(s===x||!yc(o,i,r))?kr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):kr(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=ME(n,o,i);if(!c)return kr(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>_A&&(this.allowRedirects=!1));let f=new $n(i,u,Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,eE(o),at(o),o.component??o._loadedComponent??null,o,tE(o)),p=pc(f,a,this.paramsInheritanceStrategy);f.params=Object.freeze(p.params),f.data=Object.freeze(p.data);let m=this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,t);return this.applyRedirects.lineralizeSegments(o,m).pipe(ne(D=>this.processSegment(t,r,n,D.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=EA(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Se(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(Se(({routes:u})=>{let l=r._loadedInjector??t,{parameters:d,consumedSegments:h,remainingSegments:f}=c,p=new $n(h,d,Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,eE(r),at(r),r.component??r._loadedComponent??null,r,tE(r)),m=pc(p,s,this.paramsInheritanceStrategy);p.params=Object.freeze(m.params),p.data=Object.freeze(m.data);let{segmentGroup:D,slicedSegments:I}=XD(n,h,f,u);if(I.length===0&&D.hasChildren())return this.processChildren(l,u,D,p).pipe(k(j=>new Ve(p,j)));if(u.length===0&&I.length===0)return b(new Ve(p,[]));let V=at(r)===i;return this.processSegment(l,u,D,I,V?x:i,!0,p).pipe(k(j=>new Ve(p,j instanceof Ve?[j]:[])))}))):kr(n)))}getChildConfig(t,n,r){return n.children?b({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?b({routes:n._loadedRoutes,injector:n._loadedInjector}):mA(t,n,r,this.urlSerializer).pipe(ne(o=>o?this.configLoader.loadChildren(t,n).pipe(oe(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):DA(n))):b({routes:[],injector:t})}};function NA(e){e.sort((t,n)=>t.value.outlet===x?-1:n.value.outlet===x?1:t.value.outlet.localeCompare(n.value.outlet))}function RA(e){let t=e.value.routeConfig;return t&&t.path===""}function _E(e){let t=[],n=new Set;for(let r of e){if(!RA(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=_E(r.children);t.push(new Ve(r.value,o))}return t.filter(r=>!n.has(r))}function eE(e){return e.data||{}}function tE(e){return e.resolve||{}}function AA(e,t,n,r,o,i){return ne(s=>MA(e,t,n,r,s.extractedUrl,o,i).pipe(k(({state:a,tree:c})=>B(E({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function xA(e,t){return ne(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return b(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of NE(c))s.add(u);let a=0;return W(s).pipe(dt(c=>i.has(c)?OA(c,r,e,t):(c.data=pc(c,c.parent,e).resolve,b(void 0))),oe(()=>a++),rr(1),ne(c=>a===s.size?b(n):De))})}function NE(e){let t=e.children.map(n=>NE(n)).flat();return[e,...t]}function OA(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!EE(o)&&(i[vi]=o.title),PA(i,e,t,r).pipe(k(s=>(e._resolvedData=s,e.data=pc(e,e.parent,n).resolve,null)))}function PA(e,t,n,r){let o=Pf(e);if(o.length===0)return b({});let i={};return W(o).pipe(ne(s=>kA(e[s],t,n,r).pipe(Ot(),oe(a=>{if(a instanceof Hr)throw gc(new dn,a);i[s]=a}))),rr(1),k(()=>i),lt(s=>TE(s)?De:tr(s)))}function kA(e,t,n,r){let o=yi(t)??r,i=qr(e,o),s=i.resolve?i.resolve(t,n):xe(o,()=>i(t,n));return pn(s)}function xf(e){return Se(t=>{let n=e(t);return n?W(n).pipe(k(()=>t)):b(t)})}var Jf=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===x);return r}getResolvedTitleForRoute(n){return n.data[vi]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(RE),providedIn:"root"})}return e})(),RE=(()=>{class e extends Jf{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(C(ZD))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Gn=new y("",{providedIn:"root",factory:()=>({})}),Gr=new y(""),Dc=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=g(yy);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return b(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=pn(n.loadComponent()).pipe(k(xE),oe(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),Kt(()=>{this.componentLoaders.delete(n)})),o=new Xn(r,()=>new X).pipe(Jn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return b({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=AE(r,this.compiler,n,this.onLoadEndListener).pipe(Kt(()=>{this.childrenLoaders.delete(r)})),s=new Xn(i,()=>new X).pipe(Jn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function AE(e,t,n,r){return pn(e.loadChildren()).pipe(k(xE),ne(o=>o instanceof Bd||Array.isArray(o)?b(o):W(t.compileModuleAsync(o))),k(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(Gr,[],{optional:!0,self:!0}).flat()),{routes:s.map(Kf),injector:i}}))}function FA(e){return e&&typeof e=="object"&&"default"in e}function xE(e){return FA(e)?e.default:e}var Ec=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(LA),providedIn:"root"})}return e})(),LA=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Xf=new y(""),eh=new y("");function OE(e,t,n){let r=e.get(eh),o=e.get(ue);return e.get(z).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let i,s=new Promise(u=>{i=u}),a=o.startViewTransition(()=>(i(),jA(e))),{onViewTransitionCreated:c}=r;return c&&xe(e,()=>c({transition:a,from:t,to:n})),s})}function jA(e){return new Promise(t=>{vd({read:()=>setTimeout(t)},{injector:e})})}var th=new y(""),wc=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new X;transitionAbortSubject=new X;configLoader=g(Dc);environmentInjector=g(we);destroyRef=g(Bt);urlSerializer=g(zn);rootContexts=g(qn);location=g(cn);inputBindingEnabled=g(Di,{optional:!0})!==null;titleStrategy=g(Jf);options=g(Gn,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=g(Ec);createViewTransition=g(Xf,{optional:!0});navigationErrorHandler=g(th,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>b(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new sc(o)),r=o=>this.events.next(new ac(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(B(E({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new me(null),this.transitions.pipe(de(r=>r!==null),Se(r=>{let o=!1,i=!1;return b(r).pipe(Se(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Le.SupersededByNewNavigation),De;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?B(E({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let u="";return this.events.next(new Mt(s.id,this.urlSerializer.serialize(s.rawUrl),u,Vr.IgnoredSameUrlNavigation)),s.resolve(!1),De}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return b(s).pipe(Se(u=>(this.events.next(new hn(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?De:Promise.resolve(u))),AA(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),oe(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=B(E({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let l=new li(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:u,extractedUrl:l,source:d,restoredState:h,extras:f}=s,p=new hn(u,this.urlSerializer.serialize(l),d,h);this.events.next(p);let m=yE(this.rootComponentType).snapshot;return this.currentTransition=r=B(E({},s),{targetSnapshot:m,urlAfterRedirects:l,extras:B(E({},f),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,b(r)}else{let u="";return this.events.next(new Mt(s.id,this.urlSerializer.serialize(s.extractedUrl),u,Vr.IgnoredByUrlHandlingStrategy)),s.resolve(!1),De}}),oe(s=>{let a=new nc(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),k(s=>(this.currentTransition=r=B(E({},s),{guards:KR(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),cA(this.environmentInjector,s=>this.events.next(s)),oe(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw gc(this.urlSerializer,s.guardsResult);let a=new rc(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),de(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",Le.GuardRejected),!1)),xf(s=>{if(s.guards.canActivateChecks.length!==0)return b(s).pipe(oe(a=>{let c=new oc(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),Se(a=>{let c=!1;return b(a).pipe(xA(this.paramsInheritanceStrategy,this.environmentInjector),oe({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",Le.NoDataFromResolver)}}))}),oe(a=>{let c=new ic(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),xf(s=>{let a=c=>{let u=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(c.routeConfig).pipe(oe(l=>{c.component=l}),k(()=>{})));for(let l of c.children)u.push(...a(l));return u};return io(a(s.targetSnapshot.root)).pipe(Qt(null),xt(1))}),xf(()=>this.afterPreactivation()),Se(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?W(c).pipe(k(()=>r)):b(r)}),k(s=>{let a=GR(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=B(E({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),oe(()=>{this.events.next(new di)}),QR(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),xt(1),oe({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Be(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),tu(this.transitionAbortSubject.pipe(oe(s=>{throw s}))),Kt(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",Le.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),lt(s=>{if(this.destroyed)return r.resolve(!1),De;if(i=!0,bE(s))this.events.next(new Tt(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),YR(s)?this.events.next(new $r(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new Ur(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=xe(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof Hr){let{message:u,cancellationCode:l}=gc(this.urlSerializer,c);this.events.next(new Tt(r.id,this.urlSerializer.serialize(r.extractedUrl),u,l)),this.events.next(new $r(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return De}))}))}cancelNavigationTransition(n,r,o){let i=new Tt(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function VA(e){return e!==Xa}var PE=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(UA),providedIn:"root"})}return e})(),vc=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},UA=(()=>{class e extends vc{static \u0275fac=(()=>{let n;return function(o){return(n||(n=sd(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),kE=(()=>{class e{urlSerializer=g(zn);options=g(Gn,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=g(cn);urlHandlingStrategy=g(Ec);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new St;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof St?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=yE(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(BA),providedIn:"root"})}return e})(),BA=(()=>{class e extends kE{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof hn?this.updateStateMemento():n instanceof Mt?this.commitTransition(r):n instanceof li?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof di?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Tt&&(n.code===Le.GuardRejected||n.code===Le.NoDataFromResolver)?this.restoreHistory(r):n instanceof Ur?this.restoreHistory(r,!0):n instanceof Be&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=E(E({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=E(E({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=sd(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ic(e,t){e.events.pipe(de(n=>n instanceof Be||n instanceof Tt||n instanceof Ur||n instanceof Mt),k(n=>n instanceof Be||n instanceof Mt?0:(n instanceof Tt?n.code===Le.Redirect||n.code===Le.SupersededByNewNavigation:!1)?2:1),de(n=>n!==2),xt(1)).subscribe(()=>{t()})}var $A={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},HA={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},_t=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=g(zd);stateManager=g(kE);options=g(Gn,{optional:!0})||{};pendingTasks=g($t);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=g(wc);urlSerializer=g(zn);location=g(cn);urlHandlingStrategy=g(Ec);_events=new X;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=g(PE);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=g(Gr,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!g(Di,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new te;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Tt&&r.code!==Le.Redirect&&r.code!==Le.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Be)this.navigated=!0;else if(r instanceof $r){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=E({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||VA(o.source)},s);this.scheduleNavigation(a,Xa,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}qA(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Xa,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=E({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(Kf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=E(E({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=pE(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return gE(d,n,l,u??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=fn(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Xa,null,r)}navigate(n,r={skipLocationChange:!1}){return zA(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=E({},$A):r===!1?o=E({},HA):o=r,fn(n))return YD(this.currentUrlTree,n,o);let i=this.parseUrl(n);return YD(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return Ic(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function zA(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new v(4008,!1)}function qA(e){return!(e instanceof di)&&!(e instanceof $r)}var Cc=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new X;constructor(n,r,o,i,s,a){this.router=n,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=n.events.subscribe(u=>{u instanceof Be&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(fn(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:Lm(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,n,r):o.removeAttribute(i,n)}get urlTree(){return this.routerLinkInput===null?null:fn(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(H(_t),H(Yt),ca("tabindex"),H(_r),H(yt),H(ot))};static \u0275dir=wt({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&Qd("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Zd("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Ho],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Ho],replaceUrl:[2,"replaceUrl","replaceUrl",Ho],routerLink:"routerLink"},features:[jn]})}return e})(),GA=(()=>{class e{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new Me;constructor(n,r,o,i,s){this.router=n,this.element=r,this.renderer=o,this.cdr=i,this.link=s,this.routerEventsSubscription=n.events.subscribe(a=>{a instanceof Be&&this.update()})}ngAfterContentInit(){b(this.links.changes,b(null)).pipe(ut()).subscribe(n=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let n=[...this.links.toArray(),this.link].filter(r=>!!r).map(r=>r.onChanges);this.linkInputChangesSubscription=W(n).pipe(ut()).subscribe(r=>{this._isActive!==this.isLinkActive(this.router)(r)&&this.update()})}set routerLinkActive(n){let r=Array.isArray(n)?n:n.split(" ");this.classes=r.filter(o=>!!o)}ngOnChanges(n){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let n=this.hasActiveLinks();this.classes.forEach(r=>{n?this.renderer.addClass(this.element.nativeElement,r):this.renderer.removeClass(this.element.nativeElement,r)}),n&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==n&&(this._isActive=n,this.cdr.markForCheck(),this.isActiveChange.emit(n))})}isLinkActive(n){let r=WA(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return o=>{let i=o.urlTree;return i?n.isActive(i,r):!1}}hasActiveLinks(){let n=this.isLinkActive(this.router);return this.link&&n(this.link)||this.links.some(n)}static \u0275fac=function(r){return new(r||e)(H(_t),H(yt),H(_r),H(Nr),H(Cc,8))};static \u0275dir=wt({type:e,selectors:[["","routerLinkActive",""]],contentQueries:function(r,o,i){if(r&1&&uy(i,Cc,5),r&2){let s;ly(s=dy())&&(o.links=s)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[jn]})}return e})();function WA(e){return!!e.paths}var wi=class{};var FE=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,r,o,i){this.router=n,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(de(n=>n instanceof Be),dt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=jo(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return W(o).pipe(ut())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=b(null);let i=o.pipe(ne(s=>s===null?b(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return W([i,s]).pipe(ut())}else return i})}static \u0275fac=function(r){return new(r||e)(C(_t),C(we),C(wi),C(Dc))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),LE=new y(""),ZA=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof hn?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof Be?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof Mt&&n.code===Vr.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Br&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Br(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){Ev()};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function YA(e){return e.routerState.root}function Ii(e,t){return{\u0275kind:e,\u0275providers:t}}function QA(){let e=g(pe);return t=>{let n=e.get(et);if(t!==n.components[0])return;let r=e.get(_t),o=e.get(jE);e.get(rh)===1&&r.initialNavigation(),e.get(BE,null,O.Optional)?.setUpPreloading(),e.get(LE,null,O.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var jE=new y("",{factory:()=>new X}),rh=new y("",{providedIn:"root",factory:()=>1});function VE(){let e=[{provide:rh,useValue:0},Wd(()=>{let t=g(pe);return t.get(Xd,Promise.resolve()).then(()=>new Promise(r=>{let o=t.get(_t),i=t.get(jE);Ic(o,()=>{r(!0)}),t.get(wc).afterPreactivation=()=>(r(!0),i.closed?b(void 0):i),o.initialNavigation()}))})];return Ii(2,e)}function UE(){let e=[Wd(()=>{g(_t).setUpLocationChangeListener()}),{provide:rh,useValue:2}];return Ii(3,e)}var BE=new y("");function $E(e){return Ii(0,[{provide:BE,useExisting:FE},{provide:wi,useExisting:e}])}function HE(){return Ii(8,[Yf,{provide:Di,useExisting:Yf}])}function zE(e){ze("NgRouterViewTransitions");let t=[{provide:Xf,useValue:OE},{provide:eh,useValue:E({skipNextTransition:!!e?.skipInitialTransition},e)}];return Ii(9,t)}var qE=[cn,{provide:zn,useClass:dn},_t,qn,{provide:Yt,useFactory:YA,deps:[_t]},Dc,[]],KA=(()=>{class e{constructor(){}static forRoot(n,r){return{ngModule:e,providers:[qE,[],{provide:Gr,multi:!0,useValue:n},[],r?.errorHandler?{provide:th,useValue:r.errorHandler}:[],{provide:Gn,useValue:r||{}},r?.useHash?XA():ex(),JA(),r?.preloadingStrategy?$E(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?tx(r):[],r?.bindToComponentInputs?HE().\u0275providers:[],r?.enableViewTransitions?zE().\u0275providers:[],nx()]}}static forChild(n){return{ngModule:e,providers:[{provide:Gr,multi:!0,useValue:n}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Et({type:e});static \u0275inj=vt({})}return e})();function JA(){return{provide:LE,useFactory:()=>{let e=g(pD),t=g(z),n=g(Gn),r=g(wc),o=g(zn);return n.scrollOffset&&e.setOffset(n.scrollOffset),new ZA(o,r,e,t,n)}}}function XA(){return{provide:ot,useClass:af}}function ex(){return{provide:ot,useClass:Sa}}function tx(e){return[e.initialNavigation==="disabled"?UE().\u0275providers:[],e.initialNavigation==="enabledBlocking"?VE().\u0275providers:[]]}var nh=new y("");function nx(){return[{provide:nh,useFactory:QA},{provide:Bo,multi:!0,useExisting:nh}]}export{te as a,F as b,Xn as c,X as d,me as e,no as f,De as g,W as h,b as i,tr as j,Yc as k,k as l,io as m,nr as n,rs as o,Ew as p,ww as q,de as r,Iw as s,lt as t,Cw as u,xt as v,jh as w,Kt as x,Sw as y,Mw as z,Xc as A,_w as B,Nw as C,eu as D,Se as E,tu as F,Rw as G,oe as H,v as I,Wp as J,w as K,vt as L,y as M,C as N,g as O,rg as P,Xw as Q,we as R,xe as S,jn as T,Hj as U,zj as V,qj as W,Gj as X,sd as Y,pe as Z,tp as _,Me as $,z as aa,mt as ba,yt as ca,DC as da,tm as ea,ju as fa,MC as ga,ua as ha,um as ia,xo as ja,Wj as ka,fd as la,Oo as ma,up as na,VC as oa,vd as pa,JC as qa,Dt as ra,_b as sa,Zj as ta,Yj as ua,Qj as va,Pn as wa,vr as xa,_r as ya,H as za,Ev as Aa,zt as Ba,rV as Ca,yr as Da,Pv as Ea,Et as Fa,wt as Ga,NM as Ha,sV as Ia,BM as Ja,zd as Ka,qd as La,Ia as Ma,Uo as Na,et as Oa,Zd as Pa,s_ as Qa,a_ as Ra,c_ as Sa,aV as Ta,cV as Ua,uV as Va,lV as Wa,dV as Xa,fV as Ya,ny as Za,ry as _a,Yd as $a,iy as ab,sy as bb,C_ as cb,hV as db,S_ as eb,Qd as fb,pV as gb,gV as hb,mV as ib,uy as jb,vV as kb,ly as lb,dy as mb,yV as nb,DV as ob,EV as pb,wV as qb,V_ as rb,hy as sb,U_ as tb,B_ as ub,IV as vb,$_ as wb,z_ as xb,CV as yb,bV as zb,TV as Ab,SV as Bb,MV as Cb,_V as Db,NV as Eb,RV as Fb,yy as Gb,$o as Hb,u0 as Ib,Iy as Jb,Cy as Kb,Nr as Lb,Kd as Mb,E0 as Nb,AV as Ob,Ho as Pb,x0 as Qb,xV as Rb,OV as Sb,P0 as Tb,k0 as Ub,F0 as Vb,PV as Wb,kV as Xb,FV as Yb,LV as Zb,ue as _b,It as $b,Jd as ac,qo as bc,Ta as cc,cn as dc,fN as ec,uD as fc,hN as gc,pN as hc,mN as ic,vN as jc,uf as kc,hD as lc,EN as mc,Un as nc,pD as oc,df as pc,ri as qc,Pr as rc,_f as sc,pR as tc,Wt as uc,TD as vc,vf as wc,RD as xc,$N as yc,HN as zc,zN as Ac,gR as Bc,D$ as Cc,E$ as Dc,Yt as Ec,Zf as Fc,AE as Gc,_t as Hc,Cc as Ic,GA as Jc,KA as Kc};
