using DoorCompany.Service.Dtos.ProductDto;
using DoorCompany.Service.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DoorCompany.Service.Dtos.PartnerDto;

namespace DoorCompany.Service.Repositories.Interfaces
{
    public interface IProductRepository
    {
        //  Task<ApiResponse<ProductResponseDto>> GetAllProductAsync();
  
        Task<ApiResponse<ProductResponseDto>> GetProductByIdAsync(int id);
        Task<ApiResponse<ProductResponseDto>> CreateProductAsync(CreateProductDto createDto);


        //Category 
        Task<ApiResponse<List<CategoryResponseDto>>> GetAllCategoryAsync();
        Task<ApiResponse<CategoryResponseDto>> GetCategoryByIdAsync(int id);
        Task<ApiResponse<CategoryResponseDto>> CreateCategoryAsync(CreateCategoryDto createDto);

        //Invoice 
        Task<ApiResponse<InvoiceResponseDto>> CreateInvoiceAsync(CreateInvoiceMasterDto createDto);

    }
}
