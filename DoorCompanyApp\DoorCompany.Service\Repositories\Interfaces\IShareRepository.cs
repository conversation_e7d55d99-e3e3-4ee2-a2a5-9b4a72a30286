﻿using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DoorCompany.Service.Dtos.ShareDto;

namespace DoorCompany.Service.Repositories.Interfaces
{
    public interface IShareRepository
    {
        Task<ApiResponse<List<ShareResponseDto>>> GetAllShareAsync();
        Task<ApiResponse<ShareResponseDto>> GetShareByIdAsync(int id);
        Task<ApiResponse<ShareResponseDto>> CreateShareAsycn(CreateShareDto createDto);
        Task<ApiResponse<ShareResponseDto>> UpdateShareAsycn(UpdateShareDto Dto);
        Task<ApiResponse<bool>> DeleteShareAsync(int id);

        //-----------------  إجمالي راس مال الشركة  ------------------//
        Task<ApiResponse<decimal>> GetAllInvestmentsAsync();
    
        //----------------  جلب مجموع عدد اسمهم الشركة  ------------------//
        Task<ApiResponse<int>> GetCountShareCompanyAsync();

        // '---------------  GetPartnerShares   ---------------------'
      //  Task<ApiResponse<ShareResponseDto>> CreateShareDistribution(CreateShareDto dto);


    }
}
