export interface User {
  userName: string
  fullName: string
  phone: string
  profileImage: string
  permissions: Permission[]
  id: number
  createdAt: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  isDeleted: boolean
  isActive: boolean
}

export interface Permission {
  id: number
  userId: number
  roleId: number
  fullName: string
  userRole: string
}

export interface UserPermissions{
  
  permissions: string[]
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  userId: number
  userName: string
  fullName: string
  accessToken: string
  refreshToken: string
  tokenExpiry: string
  roles: string[]
  permissions: string[]
}

export interface ChangePasswordRequest {
  userId: number
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface UpdateProfileRequest {
  fullName: string;
  phone: string;
  profileImage?: File | null; // Optional for image upload
}

export interface UloadProfileRequest {
    profileImage?: File | null; // Optional for image upload
}

export interface refreshTokenRequest {
  refreshToken: string;
}