{"Version": 1, "Hash": "uLIjgDof3FUpI0Zov/VBFRg49I2rOhFzFcSQHWfnGUo=", "Source": "DoorCompany.Api", "BasePath": "_content/DoorCompany.Api", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "DoorCompany.Api\\wwwroot", "Source": "DoorCompany.Api", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "Pattern": "**"}], "Assets": [{"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\3rdpartylicenses.txt", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "3rdpartylicenses#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xe542vkvij", "Integrity": "l+2qo5YdANHd3zpv8j0kU0SCM84g1kJcTiqhjdQuOs4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\3rdpartylicenses.txt"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\auth\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/auth/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ocj5keqt7x", "Integrity": "OF1ZGXuoHHtRKOBHAktT8BmZvUa5/4o5HNY1DQt8zYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\auth\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\auth\\login\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/auth/login/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zfoch2jv56", "Integrity": "v+9G4taXXE0yN4B8htZpdeLX6vq/6nRo1t8tlTBtF/Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\auth\\login\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-3WJJNDKD.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-3WJJNDKD#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s9980q68o3", "Integrity": "8OgqkHmV4KplxhqTH+KbWL361P0uNA+hPJYlOOlP21k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-3WJJNDKD.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-5FTQYYYZ.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-5FTQYYYZ#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hkx35hnq9i", "Integrity": "Tzqq5y1bdGSEtbSzt646BI7Wu4VvWhq2DICGUk31GSc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-5FTQYYYZ.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-BKOE74GJ.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-BKOE74GJ#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "voktisbywo", "Integrity": "AYD5R4R6JS+AIXNS5Jskp57yP7fz1ZznJyfqFjkOOWw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-BKOE74GJ.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-BZACXYOM.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-BZACXYOM#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8hoe3pc0pd", "Integrity": "bBqzcKSywpl20ci/gmBiX2nXc1H/XiV8Hyf2q4+nk1o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-BZACXYOM.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-HKHJ3A6U.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-HKHJ3A6U#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ac5hw7kyzi", "Integrity": "tm7of3sZCs387CVgaquYsGZq1Mbz2WDYnbBgPEQ236M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-HKHJ3A6U.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-KFKFGDWN.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-KFKFGDWN#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5vqt6at6t4", "Integrity": "v1SffQ0Pr6SNJwYsoRu1ypeL18d4GQKv4bKRbyYdS0M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-KFKFGDWN.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-N2CFN6CY.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-N2CFN6CY#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jj3jo0rgkz", "Integrity": "r2BvirHd4YY1VqAx0gg5xTqgN1rx3AsflerVud64HVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-N2CFN6CY.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-P4EX7ALU.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-P4EX7ALU#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wilrytg6s8", "Integrity": "uBvynYhcyqwUHsmPE3/sQTBDB3mAYagwOXHx16EmHoo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-P4EX7ALU.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-PAYZX34A.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-PAYZX34A#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "929axmdw67", "Integrity": "nwpD2Ic/9BSwAmgFHp1x/n381YH1zvNej/brpB7goEs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-PAYZX34A.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-SEMAVOI3.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-SEMAVOI3#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ch2n96jrpg", "Integrity": "sCIxlQfMYj2ofqBvJBiApEgqayaxrpgT5f2DWEM9xlE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-SEMAVOI3.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-UMPFDFGF.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-UMPFDFGF#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2f9ruoq9bq", "Integrity": "knlh9ZZL/Q/ffdXgRPFUSF3kAi+r3i51fYiphw0hJXQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-UMPFDFGF.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-VEW7PVF5.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-VEW7PVF5#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "962iddw8yf", "Integrity": "gVz2F41jIiIwFcn8VFCyFTigiChLAi+bNIr46Jsn0wE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-VEW7PVF5.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-YMJA7MAW.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-YMJA7MAW#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m0ifsol94k", "Integrity": "9pQoMfYpfgFnYQEk1QvXlBdBoGMy6KRJK9NQ4dOgEMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-YMJA7MAW.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-YNMORVHL.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-YNMORVHL#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vmlakis5hv", "Integrity": "B6iBE0TaDiBISjb3GVqWu60blQaSWNPAt5XhbqB0P4U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-YNMORVHL.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-ZPTOQNW7.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/chunk-ZPTOQNW7#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rudg9fbz3v", "Integrity": "3aCGsOuJIF+nuqPPXC/VYn0Jd3cOw2/nnvc5HbZCDAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\chunk-ZPTOQNW7.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\dashboard\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/dashboard/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jcgmt2rsci", "Integrity": "vc5ZcSeJtemdSsfOrD7wze2sZ03eRS+rWh/1/lvdtGk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\dashboard\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\favicon.jpg", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/favicon#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "17hl2lf6ga", "Integrity": "TXBR1dMDjqckukmMUUMUa0WvMxvqX/WG9UbStiDjntk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\favicon.jpg"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\index.csr.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/index.csr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k8sqlbzk6u", "Integrity": "dIL8/Z6TDkYIUTfdkLmUOd6TdL5jnAXnL7Ec4Y3qiac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\index.csr.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "91cay4vuna", "Integrity": "bb1G1ryw+4VZPpZc/w9baSRHJnwQQ57TKGKX4KJsWcs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\main-JZR2GOX4.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/main-JZR2GOX4#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "trt54l35ha", "Integrity": "B3KY19TpBqeHWbolIacD/qK9OykOIqOpohV0Zzu07Fo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\main-JZR2GOX4.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\band-list\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/partners/band-list/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zrqzgtrcmv", "Integrity": "sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\partners\\band-list\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/partners/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zrqzgtrcmv", "Integrity": "sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\partners\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\share-list\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/partners/share-list/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "g3d7y09nmg", "Integrity": "9A9uvLR3D/2XIqaYinRWVPlyzDtS8+LeC9axzFcK8Es=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\partners\\share-list\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\transactions-list\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/partners/transactions-list/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xr091y1u95", "Integrity": "LWVB6zx32U5nO0ST+KSRA29vmNntLleZDnxtkJVtEYg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\partners\\transactions-list\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\polyfills-B6TNHZQ6.js", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/polyfills-B6TNHZQ6#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pjm<PERSON><PERSON><PERSON><PERSON>h", "Integrity": "5YQR23G5CGFsXCEtI3Pntat5BVx5h+CDbmSyWdpyUH8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\polyfills-B6TNHZQ6.js"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\profile\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/profile/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h04fg47mve", "Integrity": "761GZAv369ylLxOHEx+meqJEhAE488oh3Aez8qXu8Mo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\profile\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\styles-7WIUEL3Y.css", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/styles-7WIUEL3Y#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7xmrsf0v9s", "Integrity": "NVv82QgJ12q3ExjSC4nOkCi0bhAG0AR32U4DwK4qJnM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\styles-7WIUEL3Y.css"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\unauthorized\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/unauthorized/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6e6f6ak7ln", "Integrity": "HvwwyurcYvA749Bijnv448+DRZc7YE4YXLgOtaOkSwQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\unauthorized\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\users\\index.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "browser/users/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aqprhkwwob", "Integrity": "/sNVIdgq+YzQgj3zsynjyyb6ZYKBPebKN1RGYyY6ueA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\browser\\users\\index.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\prerendered-routes.json", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "prerendered-routes#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7zdjvn0vl1", "Integrity": "g8gPu+xIUF1kOf4szx8rWezVA5KqBt7cvG4g5qDLEKE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\prerendered-routes.json"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\angular-app-engine-manifest.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/angular-app-engine-manifest#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s3d2ds2de9", "Integrity": "2CsSM/JSjtNhiaB5mElLasl80s6V0oAXYI4MDI7F1wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\angular-app-engine-manifest.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\angular-app-manifest.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/angular-app-manifest#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q3kcf4d5yv", "Integrity": "OgDzWhJwNMmtV2HQTul8918xNqIK6q7UaeEHsFm+DQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\angular-app-manifest.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\assets-chunks\\index_csr_html.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/assets-chunks/index_csr_html#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lt7ptprswx", "Integrity": "2eTs/jF/QhlDpPNOMsAZ/lpKv1uSTZbc4LdmW8mevXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\assets-chunks\\index_csr_html.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\assets-chunks\\index_server_html.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/assets-chunks/index_server_html#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7kkpnptia0", "Integrity": "qQBnsnTrsh+aSEE8z8c0KMHcY+RJgV3Et6BwktzTmvo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\assets-chunks\\index_server_html.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\assets-chunks\\styles-7WIUEL3Y_css.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/assets-chunks/styles-7WIUEL3Y_css#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "757zcsgm6c", "Integrity": "6wpHLSMcF05YzShztDImm071lJfx8yucOK6Fwhdq+p8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\assets-chunks\\styles-7WIUEL3Y_css.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-22RKY2J5.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-22RKY2J5#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2bw5xvv0x8", "Integrity": "zr/vjcyc5aJPMwwyQ8byH4DqfOp7tfQ8geY69gkiFzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-22RKY2J5.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-4ILPT5VM.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-4ILPT5VM#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n20b51bfpy", "Integrity": "4fGQlQPDBSK8g8sjqiMrYAfcKBx5k3XylO9mz4XTdQw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-4ILPT5VM.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-5SQR5QFE.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-5SQR5QFE#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rx6vqzobhd", "Integrity": "3zrgWPJYkXJgRsEAow+EwnvyHrUUZGlxnEsbnHI+1zA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-5SQR5QFE.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-FE3ZEMPP.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-FE3ZEMPP#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jngdu8uvnt", "Integrity": "jzOIxWWh39YpTb9I3AGLNE9YS74/nvICxHd9q8s6rjo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-FE3ZEMPP.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-HY3M65TY.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-HY3M65TY#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v1umc9qhfs", "Integrity": "XY6jYUpabImMUprwTJQA4+RVzFBsd0F6NZF0U2FXgbM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-HY3M65TY.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-JADE4TID.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-JADE4TID#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "abek9i5cqr", "Integrity": "ivleniJrtcD+A+ksQRnK2H7fR/4+D1HDOxAmFcISnJc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-JADE4TID.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-JEHCA7E6.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-JEHCA7E6#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yig13rkdi6", "Integrity": "6ritoIXwAGl9Tx03JZRwckHL+qvtypPAh4Z1uV4cArk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-JEHCA7E6.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-K4M745NY.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-K4M745NY#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wiv8k3n4fi", "Integrity": "mEHxTn/BY6Y5tx0g+XEJf1oeCv4KSZUhGDtRxHej9wM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-K4M745NY.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-L4YQHDFK.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-L4YQHDFK#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0hfns7t658", "Integrity": "7On3j8Uv9UegOCzoAt7YJ+NevBs3kWxYB8sdQY+jb3Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-L4YQHDFK.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-LSS2YBGE.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-LSS2YBGE#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tb093ccrlt", "Integrity": "6Xyho2G7YN1PdDdY3PWRg67cpC/25wm4feamEhm+ChY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-LSS2YBGE.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-MQBUCPND.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-MQBUCPND#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qekizb6wmf", "Integrity": "0qYHYOASENwvoVjbkwWnqWBiVXW1mMGdtUV/2YoaWFA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-MQBUCPND.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-NTD6FRHY.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-NTD6FRHY#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1aa8qe9n89", "Integrity": "CYggUM/usQM3ZYhk8fodoSAeAh6fXa31liD3HXy/eXE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-NTD6FRHY.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-O7YEHCAM.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-O7YEHCAM#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0wokg7945o", "Integrity": "ANcoWnTIiUpeDlU2WDgPyLO/mGqjlAAkVCiCor9Ihks=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-O7YEHCAM.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-QD3LIJ7Q.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-QD3LIJ7Q#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "01t1i1bed2", "Integrity": "zJD0AwLc6Mb1SCoetHwOM1FoUspRXuQDsvWfYn6q5us=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-QD3LIJ7Q.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-RBXEQ7AP.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-RBXEQ7AP#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m2pnsz8uev", "Integrity": "ruZQpPRIePFcxvKDveqyYI512+CY0NXiumF85cJtsxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-RBXEQ7AP.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-S6KH3LOX.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-S6KH3LOX#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mp9tv9odxm", "Integrity": "atkqHkVSOygHl0GemnLfIg8QgsTo6mKlo/IIj6XO9wQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-S6KH3LOX.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-TRSHSL3W.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-TRSHSL3W#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rdt6s731km", "Integrity": "wZ2L+chNf9nrsk+SutiIKxg4r0vj8Jnw44+29S6EDi0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-TRSHSL3W.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-UDSHM75V.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/chunk-UDSHM75V#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b1s8mewln8", "Integrity": "700jKVDbBvg7yDjP4TZozGrnXBajVyTuD8soswcDK1k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\chunk-UDSHM75V.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\index.server.html", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/index.server#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t1crhiauiq", "Integrity": "PzFdCtPUYO/TQTJT0sVL1reocUICV350o5XR5Dm5lI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\index.server.html"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\main.server.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/main.server#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9c0132p83u", "Integrity": "B43H+FrHvC7pwSqVxuaa7sawU6O8UxtnxSf4k3o4Yx8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\main.server.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\polyfills.server.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/polyfills.server#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kjiojcr5dc", "Integrity": "IPlcoC2Xd6jYRduLzIsMtT7xaJC3KgyNGhiOAOgJnb8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\polyfills.server.mjs"}, {"Identity": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\server.mjs", "SourceId": "DoorCompany.Api", "SourceType": "Discovered", "ContentRoot": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\", "BasePath": "_content/DoorCompany.Api", "RelativePath": "server/server#[.{fingerprint}]?.mjs", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9c6byiefvl", "Integrity": "J4EFkjY5zdKM+PFCLabS4kjtWYzim48BD0fc6uvb8vw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\server\\server.mjs"}], "Endpoints": [{"Route": "3rdpartylicenses.txt", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\3rdpartylicenses.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109071"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"l+2qo5YdANHd3zpv8j0kU0SCM84g1kJcTiqhjdQuOs4=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l+2qo5YdANHd3zpv8j0kU0SCM84g1kJcTiqhjdQuOs4="}]}, {"Route": "3rdpartylicenses.xe542vkvij.txt", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\3rdpartylicenses.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109071"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"l+2qo5YdANHd3zpv8j0kU0SCM84g1kJcTiqhjdQuOs4=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xe542vkvij"}, {"Name": "label", "Value": "3rdpartylicenses.txt"}, {"Name": "integrity", "Value": "sha256-l+2qo5YdANHd3zpv8j0kU0SCM84g1kJcTiqhjdQuOs4="}]}, {"Route": "browser/auth/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\auth\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51738"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OF1ZGXuoHHtRKOBHAktT8BmZvUa5/4o5HNY1DQt8zYw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OF1ZGXuoHHtRKOBHAktT8BmZvUa5/4o5HNY1DQt8zYw="}]}, {"Route": "browser/auth/index.ocj5keqt7x.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\auth\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51738"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"OF1ZGXuoHHtRKOBHAktT8BmZvUa5/4o5HNY1DQt8zYw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ocj5keqt7x"}, {"Name": "label", "Value": "browser/auth/index.html"}, {"Name": "integrity", "Value": "sha256-OF1ZGXuoHHtRKOBHAktT8BmZvUa5/4o5HNY1DQt8zYw="}]}, {"Route": "browser/auth/login/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\auth\\login\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "106951"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"v+9G4taXXE0yN4B8htZpdeLX6vq/6nRo1t8tlTBtF/Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v+9G4taXXE0yN4B8htZpdeLX6vq/6nRo1t8tlTBtF/Y="}]}, {"Route": "browser/auth/login/index.zfoch2jv56.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\auth\\login\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "106951"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"v+9G4taXXE0yN4B8htZpdeLX6vq/6nRo1t8tlTBtF/Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zfoch2jv56"}, {"Name": "label", "Value": "browser/auth/login/index.html"}, {"Name": "integrity", "Value": "sha256-v+9G4taXXE0yN4B8htZpdeLX6vq/6nRo1t8tlTBtF/Y="}]}, {"Route": "browser/chunk-3WJJNDKD.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-3WJJNDKD.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "341356"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8OgqkHmV4KplxhqTH+KbWL361P0uNA+hPJYlOOlP21k=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8OgqkHmV4KplxhqTH+KbWL361P0uNA+hPJYlOOlP21k="}]}, {"Route": "browser/chunk-3WJJNDKD.s9980q68o3.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-3WJJNDKD.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "341356"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8OgqkHmV4KplxhqTH+KbWL361P0uNA+hPJYlOOlP21k=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s9980q68o3"}, {"Name": "label", "Value": "browser/chunk-3WJJNDKD.js"}, {"Name": "integrity", "Value": "sha256-8OgqkHmV4KplxhqTH+KbWL361P0uNA+hPJYlOOlP21k="}]}, {"Route": "browser/chunk-5FTQYYYZ.hkx35hnq9i.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-5FTQYYYZ.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "151549"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Tzqq5y1bdGSEtbSzt646BI7Wu4VvWhq2DICGUk31GSc=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hkx35hnq9i"}, {"Name": "label", "Value": "browser/chunk-5FTQYYYZ.js"}, {"Name": "integrity", "Value": "sha256-Tzqq5y1bdGSEtbSzt646BI7Wu4VvWhq2DICGUk31GSc="}]}, {"Route": "browser/chunk-5FTQYYYZ.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-5FTQYYYZ.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "151549"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Tzqq5y1bdGSEtbSzt646BI7Wu4VvWhq2DICGUk31GSc=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tzqq5y1bdGSEtbSzt646BI7Wu4VvWhq2DICGUk31GSc="}]}, {"Route": "browser/chunk-BKOE74GJ.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-BKOE74GJ.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81740"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AYD5R4R6JS+AIXNS5Jskp57yP7fz1ZznJyfqFjkOOWw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AYD5R4R6JS+AIXNS5Jskp57yP7fz1ZznJyfqFjkOOWw="}]}, {"Route": "browser/chunk-BKOE74GJ.voktisbywo.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-BKOE74GJ.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81740"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AYD5R4R6JS+AIXNS5Jskp57yP7fz1ZznJyfqFjkOOWw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "voktisbywo"}, {"Name": "label", "Value": "browser/chunk-BKOE74GJ.js"}, {"Name": "integrity", "Value": "sha256-AYD5R4R6JS+AIXNS5Jskp57yP7fz1ZznJyfqFjkOOWw="}]}, {"Route": "browser/chunk-BZACXYOM.8hoe3pc0pd.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-BZACXYOM.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52988"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bBqzcKSywpl20ci/gmBiX2nXc1H/XiV8Hyf2q4+nk1o=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8hoe3pc0pd"}, {"Name": "label", "Value": "browser/chunk-BZACXYOM.js"}, {"Name": "integrity", "Value": "sha256-bBqzcKSywpl20ci/gmBiX2nXc1H/XiV8Hyf2q4+nk1o="}]}, {"Route": "browser/chunk-BZACXYOM.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-BZACXYOM.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52988"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bBqzcKSywpl20ci/gmBiX2nXc1H/XiV8Hyf2q4+nk1o=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bBqzcKSywpl20ci/gmBiX2nXc1H/XiV8Hyf2q4+nk1o="}]}, {"Route": "browser/chunk-HKHJ3A6U.ac5hw7kyzi.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-HKHJ3A6U.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26220"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tm7of3sZCs387CVgaquYsGZq1Mbz2WDYnbBgPEQ236M=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ac5hw7kyzi"}, {"Name": "label", "Value": "browser/chunk-HKHJ3A6U.js"}, {"Name": "integrity", "Value": "sha256-tm7of3sZCs387CVgaquYsGZq1Mbz2WDYnbBgPEQ236M="}]}, {"Route": "browser/chunk-HKHJ3A6U.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-HKHJ3A6U.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26220"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tm7of3sZCs387CVgaquYsGZq1Mbz2WDYnbBgPEQ236M=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tm7of3sZCs387CVgaquYsGZq1Mbz2WDYnbBgPEQ236M="}]}, {"Route": "browser/chunk-KFKFGDWN.5vqt6at6t4.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-KFKFGDWN.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "338284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"v1SffQ0Pr6SNJwYsoRu1ypeL18d4GQKv4bKRbyYdS0M=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5vqt6at6t4"}, {"Name": "label", "Value": "browser/chunk-KFKFGDWN.js"}, {"Name": "integrity", "Value": "sha256-v1SffQ0Pr6SNJwYsoRu1ypeL18d4GQKv4bKRbyYdS0M="}]}, {"Route": "browser/chunk-KFKFGDWN.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-KFKFGDWN.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "338284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"v1SffQ0Pr6SNJwYsoRu1ypeL18d4GQKv4bKRbyYdS0M=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v1SffQ0Pr6SNJwYsoRu1ypeL18d4GQKv4bKRbyYdS0M="}]}, {"Route": "browser/chunk-N2CFN6CY.jj3jo0rgkz.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-N2CFN6CY.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1173"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r2BvirHd4YY1VqAx0gg5xTqgN1rx3AsflerVud64HVM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj3jo0rgkz"}, {"Name": "label", "Value": "browser/chunk-N2CFN6CY.js"}, {"Name": "integrity", "Value": "sha256-r2BvirHd4YY1VqAx0gg5xTqgN1rx3AsflerVud64HVM="}]}, {"Route": "browser/chunk-N2CFN6CY.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-N2CFN6CY.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1173"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r2BvirHd4YY1VqAx0gg5xTqgN1rx3AsflerVud64HVM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r2BvirHd4YY1VqAx0gg5xTqgN1rx3AsflerVud64HVM="}]}, {"Route": "browser/chunk-P4EX7ALU.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-P4EX7ALU.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "876"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uBvynYhcyqwUHsmPE3/sQTBDB3mAYagwOXHx16EmHoo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uBvynYhcyqwUHsmPE3/sQTBDB3mAYagwOXHx16EmHoo="}]}, {"Route": "browser/chunk-P4EX7ALU.wilrytg6s8.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-P4EX7ALU.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "876"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uBvynYhcyqwUHsmPE3/sQTBDB3mAYagwOXHx16EmHoo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wilrytg6s8"}, {"Name": "label", "Value": "browser/chunk-P4EX7ALU.js"}, {"Name": "integrity", "Value": "sha256-uBvynYhcyqwUHsmPE3/sQTBDB3mAYagwOXHx16EmHoo="}]}, {"Route": "browser/chunk-PAYZX34A.929axmdw67.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-PAYZX34A.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nwpD2Ic/9BSwAmgFHp1x/n381YH1zvNej/brpB7goEs=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "929axmdw67"}, {"Name": "label", "Value": "browser/chunk-PAYZX34A.js"}, {"Name": "integrity", "Value": "sha256-nwpD2Ic/9BSwAmgFHp1x/n381YH1zvNej/brpB7goEs="}]}, {"Route": "browser/chunk-PAYZX34A.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-PAYZX34A.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nwpD2Ic/9BSwAmgFHp1x/n381YH1zvNej/brpB7goEs=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nwpD2Ic/9BSwAmgFHp1x/n381YH1zvNej/brpB7goEs="}]}, {"Route": "browser/chunk-SEMAVOI3.ch2n96jrpg.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-SEMAVOI3.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1230"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sCIxlQfMYj2ofqBvJBiApEgqayaxrpgT5f2DWEM9xlE=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ch2n96jrpg"}, {"Name": "label", "Value": "browser/chunk-SEMAVOI3.js"}, {"Name": "integrity", "Value": "sha256-sCIxlQfMYj2ofqBvJBiApEgqayaxrpgT5f2DWEM9xlE="}]}, {"Route": "browser/chunk-SEMAVOI3.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-SEMAVOI3.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1230"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sCIxlQfMYj2ofqBvJBiApEgqayaxrpgT5f2DWEM9xlE=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sCIxlQfMYj2ofqBvJBiApEgqayaxrpgT5f2DWEM9xlE="}]}, {"Route": "browser/chunk-UMPFDFGF.2f9ruoq9bq.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-UMPFDFGF.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22616"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"knlh9ZZL/Q/ffdXgRPFUSF3kAi+r3i51fYiphw0hJXQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2f9ruoq9bq"}, {"Name": "label", "Value": "browser/chunk-UMPFDFGF.js"}, {"Name": "integrity", "Value": "sha256-knlh9ZZL/Q/ffdXgRPFUSF3kAi+r3i51fYiphw0hJXQ="}]}, {"Route": "browser/chunk-UMPFDFGF.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-UMPFDFGF.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22616"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"knlh9ZZL/Q/ffdXgRPFUSF3kAi+r3i51fYiphw0hJXQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-knlh9ZZL/Q/ffdXgRPFUSF3kAi+r3i51fYiphw0hJXQ="}]}, {"Route": "browser/chunk-VEW7PVF5.962iddw8yf.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-VEW7PVF5.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14817"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gVz2F41jIiIwFcn8VFCyFTigiChLAi+bNIr46Jsn0wE=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "962iddw8yf"}, {"Name": "label", "Value": "browser/chunk-VEW7PVF5.js"}, {"Name": "integrity", "Value": "sha256-gVz2F41jIiIwFcn8VFCyFTigiChLAi+bNIr46Jsn0wE="}]}, {"Route": "browser/chunk-VEW7PVF5.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-VEW7PVF5.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14817"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gVz2F41jIiIwFcn8VFCyFTigiChLAi+bNIr46Jsn0wE=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gVz2F41jIiIwFcn8VFCyFTigiChLAi+bNIr46Jsn0wE="}]}, {"Route": "browser/chunk-YMJA7MAW.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-YMJA7MAW.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1095"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9pQoMfYpfgFnYQEk1QvXlBdBoGMy6KRJK9NQ4dOgEMQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9pQoMfYpfgFnYQEk1QvXlBdBoGMy6KRJK9NQ4dOgEMQ="}]}, {"Route": "browser/chunk-YMJA7MAW.m0ifsol94k.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-YMJA7MAW.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1095"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9pQoMfYpfgFnYQEk1QvXlBdBoGMy6KRJK9NQ4dOgEMQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m0ifsol94k"}, {"Name": "label", "Value": "browser/chunk-YMJA7MAW.js"}, {"Name": "integrity", "Value": "sha256-9pQoMfYpfgFnYQEk1QvXlBdBoGMy6KRJK9NQ4dOgEMQ="}]}, {"Route": "browser/chunk-YNMORVHL.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-YNMORVHL.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "640"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B6iBE0TaDiBISjb3GVqWu60blQaSWNPAt5XhbqB0P4U=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B6iBE0TaDiBISjb3GVqWu60blQaSWNPAt5XhbqB0P4U="}]}, {"Route": "browser/chunk-YNMORVHL.vmlakis5hv.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-YNMORVHL.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "640"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B6iBE0TaDiBISjb3GVqWu60blQaSWNPAt5XhbqB0P4U=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vmlakis5hv"}, {"Name": "label", "Value": "browser/chunk-YNMORVHL.js"}, {"Name": "integrity", "Value": "sha256-B6iBE0TaDiBISjb3GVqWu60blQaSWNPAt5XhbqB0P4U="}]}, {"Route": "browser/chunk-ZPTOQNW7.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-ZPTOQNW7.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17509"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3aCGsOuJIF+nuqPPXC/VYn0Jd3cOw2/nnvc5HbZCDAs=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3aCGsOuJIF+nuqPPXC/VYn0Jd3cOw2/nnvc5HbZCDAs="}]}, {"Route": "browser/chunk-ZPTOQNW7.rudg9fbz3v.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\chunk-ZPTOQNW7.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17509"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3aCGsOuJIF+nuqPPXC/VYn0Jd3cOw2/nnvc5HbZCDAs=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rudg9fbz3v"}, {"Name": "label", "Value": "browser/chunk-ZPTOQNW7.js"}, {"Name": "integrity", "Value": "sha256-3aCGsOuJIF+nuqPPXC/VYn0Jd3cOw2/nnvc5HbZCDAs="}]}, {"Route": "browser/dashboard/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\dashboard\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "106847"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"vc5ZcSeJtemdSsfOrD7wze2sZ03eRS+rWh/1/lvdtGk=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vc5ZcSeJtemdSsfOrD7wze2sZ03eRS+rWh/1/lvdtGk="}]}, {"Route": "browser/dashboard/index.jcgmt2rsci.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\dashboard\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "106847"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"vc5ZcSeJtemdSsfOrD7wze2sZ03eRS+rWh/1/lvdtGk=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jcgmt2rsci"}, {"Name": "label", "Value": "browser/dashboard/index.html"}, {"Name": "integrity", "Value": "sha256-vc5ZcSeJtemdSsfOrD7wze2sZ03eRS+rWh/1/lvdtGk="}]}, {"Route": "browser/favicon.17hl2lf6ga.jpg", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\favicon.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"TXBR1dMDjqckukmMUUMUa0WvMxvqX/WG9UbStiDjntk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:12:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "17hl2lf6ga"}, {"Name": "label", "Value": "browser/favicon.jpg"}, {"Name": "integrity", "Value": "sha256-TXBR1dMDjqckukmMUUMUa0WvMxvqX/WG9UbStiDjntk="}]}, {"Route": "browser/favicon.jpg", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\favicon.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"TXBR1dMDjqckukmMUUMUa0WvMxvqX/WG9UbStiDjntk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 13:12:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TXBR1dMDjqckukmMUUMUa0WvMxvqX/WG9UbStiDjntk="}]}, {"Route": "browser/index.91cay4vuna.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "106794"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bb1G1ryw+4VZPpZc/w9baSRHJnwQQ57TKGKX4KJsWcs=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "91cay4vuna"}, {"Name": "label", "Value": "browser/index.html"}, {"Name": "integrity", "Value": "sha256-bb1G1ryw+4VZPpZc/w9baSRHJnwQQ57TKGKX4KJsWcs="}]}, {"Route": "browser/index.csr.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\index.csr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51286"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dIL8/Z6TDkYIUTfdkLmUOd6TdL5jnAXnL7Ec4Y3qiac=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dIL8/Z6TDkYIUTfdkLmUOd6TdL5jnAXnL7Ec4Y3qiac="}]}, {"Route": "browser/index.csr.k8sqlbzk6u.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\index.csr.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51286"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dIL8/Z6TDkYIUTfdkLmUOd6TdL5jnAXnL7Ec4Y3qiac=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8sqlbzk6u"}, {"Name": "label", "Value": "browser/index.csr.html"}, {"Name": "integrity", "Value": "sha256-dIL8/Z6TDkYIUTfdkLmUOd6TdL5jnAXnL7Ec4Y3qiac="}]}, {"Route": "browser/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "106794"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bb1G1ryw+4VZPpZc/w9baSRHJnwQQ57TKGKX4KJsWcs=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bb1G1ryw+4VZPpZc/w9baSRHJnwQQ57TKGKX4KJsWcs="}]}, {"Route": "browser/main-JZR2GOX4.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\main-JZR2GOX4.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46936"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B3KY19TpBqeHWbolIacD/qK9OykOIqOpohV0Zzu07Fo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B3KY19TpBqeHWbolIacD/qK9OykOIqOpohV0Zzu07Fo="}]}, {"Route": "browser/main-JZR2GOX4.trt54l35ha.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\main-JZR2GOX4.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46936"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B3KY19TpBqeHWbolIacD/qK9OykOIqOpohV0Zzu07Fo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "trt54l35ha"}, {"Name": "label", "Value": "browser/main-JZR2GOX4.js"}, {"Name": "integrity", "Value": "sha256-B3KY19TpBqeHWbolIacD/qK9OykOIqOpohV0Zzu07Fo="}]}, {"Route": "browser/partners/band-list/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\band-list\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107003"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA="}]}, {"Route": "browser/partners/band-list/index.zrqzgtrcmv.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\band-list\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107003"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zrqzgtrcmv"}, {"Name": "label", "Value": "browser/partners/band-list/index.html"}, {"Name": "integrity", "Value": "sha256-sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA="}]}, {"Route": "browser/partners/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107003"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA="}]}, {"Route": "browser/partners/index.zrqzgtrcmv.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107003"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zrqzgtrcmv"}, {"Name": "label", "Value": "browser/partners/index.html"}, {"Name": "integrity", "Value": "sha256-sTnU8JE07n+Mcxgh1XXB3SjF0Xn3wNdyRDqfQbkw8lA="}]}, {"Route": "browser/partners/share-list/index.g3d7y09nmg.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\share-list\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107003"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"9A9uvLR3D/2XIqaYinRWVPlyzDtS8+LeC9axzFcK8Es=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g3d7y09nmg"}, {"Name": "label", "Value": "browser/partners/share-list/index.html"}, {"Name": "integrity", "Value": "sha256-9A9uvLR3D/2XIqaYinRWVPlyzDtS8+LeC9axzFcK8Es="}]}, {"Route": "browser/partners/share-list/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\share-list\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107003"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"9A9uvLR3D/2XIqaYinRWVPlyzDtS8+LeC9axzFcK8Es=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9A9uvLR3D/2XIqaYinRWVPlyzDtS8+LeC9axzFcK8Es="}]}, {"Route": "browser/partners/transactions-list/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\transactions-list\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107003"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LWVB6zx32U5nO0ST+KSRA29vmNntLleZDnxtkJVtEYg=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LWVB6zx32U5nO0ST+KSRA29vmNntLleZDnxtkJVtEYg="}]}, {"Route": "browser/partners/transactions-list/index.xr091y1u95.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\partners\\transactions-list\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107003"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LWVB6zx32U5nO0ST+KSRA29vmNntLleZDnxtkJVtEYg=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xr091y1u95"}, {"Name": "label", "Value": "browser/partners/transactions-list/index.html"}, {"Name": "integrity", "Value": "sha256-LWVB6zx32U5nO0ST+KSRA29vmNntLleZDnxtkJVtEYg="}]}, {"Route": "browser/polyfills-B6TNHZQ6.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\polyfills-B6TNHZQ6.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "34579"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5YQR23G5CGFsXCEtI3Pntat5BVx5h+CDbmSyWdpyUH8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5YQR23G5CGFsXCEtI3Pntat5BVx5h+CDbmSyWdpyUH8="}]}, {"Route": "browser/polyfills-B6TNHZQ6.pjm38qobjh.js", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\polyfills-B6TNHZQ6.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "34579"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5YQR23G5CGFsXCEtI3Pntat5BVx5h+CDbmSyWdpyUH8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pjm<PERSON><PERSON><PERSON><PERSON>h"}, {"Name": "label", "Value": "browser/polyfills-B6TNHZQ6.js"}, {"Name": "integrity", "Value": "sha256-5YQR23G5CGFsXCEtI3Pntat5BVx5h+CDbmSyWdpyUH8="}]}, {"Route": "browser/profile/index.h04fg47mve.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\profile\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80362"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"761GZAv369ylLxOHEx+meqJEhAE488oh3Aez8qXu8Mo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h04fg47mve"}, {"Name": "label", "Value": "browser/profile/index.html"}, {"Name": "integrity", "Value": "sha256-761GZAv369ylLxOHEx+meqJEhAE488oh3Aez8qXu8Mo="}]}, {"Route": "browser/profile/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\profile\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80362"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"761GZAv369ylLxOHEx+meqJEhAE488oh3Aez8qXu8Mo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-761GZAv369ylLxOHEx+meqJEhAE488oh3Aez8qXu8Mo="}]}, {"Route": "browser/styles-7WIUEL3Y.7xmrsf0v9s.css", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\styles-7WIUEL3Y.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37077"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NVv82QgJ12q3ExjSC4nOkCi0bhAG0AR32U4DwK4qJnM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xmrsf0v9s"}, {"Name": "label", "Value": "browser/styles-7WIUEL3Y.css"}, {"Name": "integrity", "Value": "sha256-NVv82QgJ12q3ExjSC4nOkCi0bhAG0AR32U4DwK4qJnM="}]}, {"Route": "browser/styles-7WIUEL3Y.css", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\styles-7WIUEL3Y.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37077"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NVv82QgJ12q3ExjSC4nOkCi0bhAG0AR32U4DwK4qJnM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NVv82QgJ12q3ExjSC4nOkCi0bhAG0AR32U4DwK4qJnM="}]}, {"Route": "browser/unauthorized/index.6e6f6ak7ln.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\unauthorized\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53185"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HvwwyurcYvA749Bijnv448+DRZc7YE4YXLgOtaOkSwQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6e6f6ak7ln"}, {"Name": "label", "Value": "browser/unauthorized/index.html"}, {"Name": "integrity", "Value": "sha256-HvwwyurcYvA749Bijnv448+DRZc7YE4YXLgOtaOkSwQ="}]}, {"Route": "browser/unauthorized/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\unauthorized\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53185"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HvwwyurcYvA749Bijnv448+DRZc7YE4YXLgOtaOkSwQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HvwwyurcYvA749Bijnv448+DRZc7YE4YXLgOtaOkSwQ="}]}, {"Route": "browser/users/index.aqprhkwwob.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\users\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "106847"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/sNVIdgq+YzQgj3zsynjyyb6ZYKBPebKN1RGYyY6ueA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aqprhkwwob"}, {"Name": "label", "Value": "browser/users/index.html"}, {"Name": "integrity", "Value": "sha256-/sNVIdgq+YzQgj3zsynjyyb6ZYKBPebKN1RGYyY6ueA="}]}, {"Route": "browser/users/index.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\browser\\users\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "106847"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/sNVIdgq+YzQgj3zsynjyyb6ZYKBPebKN1RGYyY6ueA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/sNVIdgq+YzQgj3zsynjyyb6ZYKBPebKN1RGYyY6ueA="}]}, {"Route": "prerendered-routes.7zdjvn0vl1.json", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\prerendered-routes.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "281"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"g8gPu+xIUF1kOf4szx8rWezVA5KqBt7cvG4g5qDLEKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7zdjvn0vl1"}, {"Name": "label", "Value": "prerendered-routes.json"}, {"Name": "integrity", "Value": "sha256-g8gPu+xIUF1kOf4szx8rWezVA5KqBt7cvG4g5qDLEKE="}]}, {"Route": "prerendered-routes.json", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\prerendered-routes.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "281"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"g8gPu+xIUF1kOf4szx8rWezVA5KqBt7cvG4g5qDLEKE=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g8gPu+xIUF1kOf4szx8rWezVA5KqBt7cvG4g5qDLEKE="}]}, {"Route": "server/angular-app-engine-manifest.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\angular-app-engine-manifest.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "141"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2CsSM/JSjtNhiaB5mElLasl80s6V0oAXYI4MDI7F1wc=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2CsSM/JSjtNhiaB5mElLasl80s6V0oAXYI4MDI7F1wc="}]}, {"Route": "server/angular-app-engine-manifest.s3d2ds2de9.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\angular-app-engine-manifest.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "141"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2CsSM/JSjtNhiaB5mElLasl80s6V0oAXYI4MDI7F1wc=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s3d2ds2de9"}, {"Name": "label", "Value": "server/angular-app-engine-manifest.mjs"}, {"Name": "integrity", "Value": "sha256-2CsSM/JSjtNhiaB5mElLasl80s6V0oAXYI4MDI7F1wc="}]}, {"Route": "server/angular-app-manifest.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\angular-app-manifest.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1947"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OgDzWhJwNMmtV2HQTul8918xNqIK6q7UaeEHsFm+DQA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OgDzWhJwNMmtV2HQTul8918xNqIK6q7UaeEHsFm+DQA="}]}, {"Route": "server/angular-app-manifest.q3kcf4d5yv.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\angular-app-manifest.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1947"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OgDzWhJwNMmtV2HQTul8918xNqIK6q7UaeEHsFm+DQA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q3kcf4d5yv"}, {"Name": "label", "Value": "server/angular-app-manifest.mjs"}, {"Name": "integrity", "Value": "sha256-OgDzWhJwNMmtV2HQTul8918xNqIK6q7UaeEHsFm+DQA="}]}, {"Route": "server/assets-chunks/index_csr_html.lt7ptprswx.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\assets-chunks\\index_csr_html.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51304"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2eTs/jF/QhlDpPNOMsAZ/lpKv1uSTZbc4LdmW8mevXw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lt7ptprswx"}, {"Name": "label", "Value": "server/assets-chunks/index_csr_html.mjs"}, {"Name": "integrity", "Value": "sha256-2eTs/jF/QhlDpPNOMsAZ/lpKv1uSTZbc4LdmW8mevXw="}]}, {"Route": "server/assets-chunks/index_csr_html.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\assets-chunks\\index_csr_html.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51304"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2eTs/jF/QhlDpPNOMsAZ/lpKv1uSTZbc4LdmW8mevXw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2eTs/jF/QhlDpPNOMsAZ/lpKv1uSTZbc4LdmW8mevXw="}]}, {"Route": "server/assets-chunks/index_server_html.7kkpnptia0.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\assets-chunks\\index_server_html.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17690"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qQBnsnTrsh+aSEE8z8c0KMHcY+RJgV3Et6BwktzTmvo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7kkpnptia0"}, {"Name": "label", "Value": "server/assets-chunks/index_server_html.mjs"}, {"Name": "integrity", "Value": "sha256-qQBnsnTrsh+aSEE8z8c0KMHcY+RJgV3Et6BwktzTmvo="}]}, {"Route": "server/assets-chunks/index_server_html.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\assets-chunks\\index_server_html.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17690"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qQBnsnTrsh+aSEE8z8c0KMHcY+RJgV3Et6BwktzTmvo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qQBnsnTrsh+aSEE8z8c0KMHcY+RJgV3Et6BwktzTmvo="}]}, {"Route": "server/assets-chunks/styles-7WIUEL3Y_css.757zcsgm6c.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\assets-chunks\\styles-7WIUEL3Y_css.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37095"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6wpHLSMcF05YzShztDImm071lJfx8yucOK6Fwhdq+p8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "757zcsgm6c"}, {"Name": "label", "Value": "server/assets-chunks/styles-7WIUEL3Y_css.mjs"}, {"Name": "integrity", "Value": "sha256-6wpHLSMcF05YzShztDImm071lJfx8yucOK6Fwhdq+p8="}]}, {"Route": "server/assets-chunks/styles-7WIUEL3Y_css.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\assets-chunks\\styles-7WIUEL3Y_css.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37095"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6wpHLSMcF05YzShztDImm071lJfx8yucOK6Fwhdq+p8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6wpHLSMcF05YzShztDImm071lJfx8yucOK6Fwhdq+p8="}]}, {"Route": "server/chunk-22RKY2J5.2bw5xvv0x8.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-22RKY2J5.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17578"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zr/vjcyc5aJPMwwyQ8byH4DqfOp7tfQ8geY69gkiFzM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2bw5xvv0x8"}, {"Name": "label", "Value": "server/chunk-22RKY2J5.mjs"}, {"Name": "integrity", "Value": "sha256-zr/vjcyc5aJPMwwyQ8byH4DqfOp7tfQ8geY69gkiFzM="}]}, {"Route": "server/chunk-22RKY2J5.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-22RKY2J5.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17578"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zr/vjcyc5aJPMwwyQ8byH4DqfOp7tfQ8geY69gkiFzM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zr/vjcyc5aJPMwwyQ8byH4DqfOp7tfQ8geY69gkiFzM="}]}, {"Route": "server/chunk-4ILPT5VM.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-4ILPT5VM.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1238"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4fGQlQPDBSK8g8sjqiMrYAfcKBx5k3XylO9mz4XTdQw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4fGQlQPDBSK8g8sjqiMrYAfcKBx5k3XylO9mz4XTdQw="}]}, {"Route": "server/chunk-4ILPT5VM.n20b51bfpy.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-4ILPT5VM.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1238"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4fGQlQPDBSK8g8sjqiMrYAfcKBx5k3XylO9mz4XTdQw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n20b51bfpy"}, {"Name": "label", "Value": "server/chunk-4ILPT5VM.mjs"}, {"Name": "integrity", "Value": "sha256-4fGQlQPDBSK8g8sjqiMrYAfcKBx5k3XylO9mz4XTdQw="}]}, {"Route": "server/chunk-5SQR5QFE.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-5SQR5QFE.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53055"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3zrgWPJYkXJgRsEAow+EwnvyHrUUZGlxnEsbnHI+1zA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3zrgWPJYkXJgRsEAow+EwnvyHrUUZGlxnEsbnHI+1zA="}]}, {"Route": "server/chunk-5SQR5QFE.rx6vqzobhd.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-5SQR5QFE.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53055"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3zrgWPJYkXJgRsEAow+EwnvyHrUUZGlxnEsbnHI+1zA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rx6vqzobhd"}, {"Name": "label", "Value": "server/chunk-5SQR5QFE.mjs"}, {"Name": "integrity", "Value": "sha256-3zrgWPJYkXJgRsEAow+EwnvyHrUUZGlxnEsbnHI+1zA="}]}, {"Route": "server/chunk-FE3ZEMPP.jngdu8uvnt.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-FE3ZEMPP.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jzOIxWWh39YpTb9I3AGLNE9YS74/nvICxHd9q8s6rjo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jngdu8uvnt"}, {"Name": "label", "Value": "server/chunk-FE3ZEMPP.mjs"}, {"Name": "integrity", "Value": "sha256-jzOIxWWh39YpTb9I3AGLNE9YS74/nvICxHd9q8s6rjo="}]}, {"Route": "server/chunk-FE3ZEMPP.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-FE3ZEMPP.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jzOIxWWh39YpTb9I3AGLNE9YS74/nvICxHd9q8s6rjo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jzOIxWWh39YpTb9I3AGLNE9YS74/nvICxHd9q8s6rjo="}]}, {"Route": "server/chunk-HY3M65TY.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-HY3M65TY.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1130"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XY6jYUpabImMUprwTJQA4+RVzFBsd0F6NZF0U2FXgbM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XY6jYUpabImMUprwTJQA4+RVzFBsd0F6NZF0U2FXgbM="}]}, {"Route": "server/chunk-HY3M65TY.v1umc9qhfs.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-HY3M65TY.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1130"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XY6jYUpabImMUprwTJQA4+RVzFBsd0F6NZF0U2FXgbM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v1umc9qhfs"}, {"Name": "label", "Value": "server/chunk-HY3M65TY.mjs"}, {"Name": "integrity", "Value": "sha256-XY6jYUpabImMUprwTJQA4+RVzFBsd0F6NZF0U2FXgbM="}]}, {"Route": "server/chunk-JADE4TID.abek9i5cqr.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-JADE4TID.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "330247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ivleniJrtcD+A+ksQRnK2H7fR/4+D1HDOxAmFcISnJc=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "abek9i5cqr"}, {"Name": "label", "Value": "server/chunk-JADE4TID.mjs"}, {"Name": "integrity", "Value": "sha256-ivleniJrtcD+A+ksQRnK2H7fR/4+D1HDOxAmFcISnJc="}]}, {"Route": "server/chunk-JADE4TID.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-JADE4TID.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "330247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ivleniJrtcD+A+ksQRnK2H7fR/4+D1HDOxAmFcISnJc=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ivleniJrtcD+A+ksQRnK2H7fR/4+D1HDOxAmFcISnJc="}]}, {"Route": "server/chunk-JEHCA7E6.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-JEHCA7E6.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81776"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6ritoIXwAGl9Tx03JZRwckHL+qvtypPAh4Z1uV4cArk=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6ritoIXwAGl9Tx03JZRwckHL+qvtypPAh4Z1uV4cArk="}]}, {"Route": "server/chunk-JEHCA7E6.yig13rkdi6.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-JEHCA7E6.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81776"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6ritoIXwAGl9Tx03JZRwckHL+qvtypPAh4Z1uV4cArk=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yig13rkdi6"}, {"Name": "label", "Value": "server/chunk-JEHCA7E6.mjs"}, {"Name": "integrity", "Value": "sha256-6ritoIXwAGl9Tx03JZRwckHL+qvtypPAh4Z1uV4cArk="}]}, {"Route": "server/chunk-K4M745NY.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-K4M745NY.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14886"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mEHxTn/BY6Y5tx0g+XEJf1oeCv4KSZUhGDtRxHej9wM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mEHxTn/BY6Y5tx0g+XEJf1oeCv4KSZUhGDtRxHej9wM="}]}, {"Route": "server/chunk-K4M745NY.wiv8k3n4fi.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-K4M745NY.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14886"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mEHxTn/BY6Y5tx0g+XEJf1oeCv4KSZUhGDtRxHej9wM=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wiv8k3n4fi"}, {"Name": "label", "Value": "server/chunk-K4M745NY.mjs"}, {"Name": "integrity", "Value": "sha256-mEHxTn/BY6Y5tx0g+XEJf1oeCv4KSZUhGDtRxHej9wM="}]}, {"Route": "server/chunk-L4YQHDFK.0hfns7t658.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-L4YQHDFK.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26255"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7On3j8Uv9UegOCzoAt7YJ+NevBs3kWxYB8sdQY+jb3Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0hfns7t658"}, {"Name": "label", "Value": "server/chunk-L4YQHDFK.mjs"}, {"Name": "integrity", "Value": "sha256-7On3j8Uv9UegOCzoAt7YJ+NevBs3kWxYB8sdQY+jb3Q="}]}, {"Route": "server/chunk-L4YQHDFK.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-L4YQHDFK.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26255"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7On3j8Uv9UegOCzoAt7YJ+NevBs3kWxYB8sdQY+jb3Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7On3j8Uv9UegOCzoAt7YJ+NevBs3kWxYB8sdQY+jb3Q="}]}, {"Route": "server/chunk-LSS2YBGE.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-LSS2YBGE.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6Xyho2G7YN1PdDdY3PWRg67cpC/25wm4feamEhm+ChY=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6Xyho2G7YN1PdDdY3PWRg67cpC/25wm4feamEhm+ChY="}]}, {"Route": "server/chunk-LSS2YBGE.tb093ccrlt.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-LSS2YBGE.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6Xyho2G7YN1PdDdY3PWRg67cpC/25wm4feamEhm+ChY=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tb093ccrlt"}, {"Name": "label", "Value": "server/chunk-LSS2YBGE.mjs"}, {"Name": "integrity", "Value": "sha256-6Xyho2G7YN1PdDdY3PWRg67cpC/25wm4feamEhm+ChY="}]}, {"Route": "server/chunk-MQBUCPND.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-MQBUCPND.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2320"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0qYHYOASENwvoVjbkwWnqWBiVXW1mMGdtUV/2YoaWFA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0qYHYOASENwvoVjbkwWnqWBiVXW1mMGdtUV/2YoaWFA="}]}, {"Route": "server/chunk-MQBUCPND.qekizb6wmf.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-MQBUCPND.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2320"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0qYHYOASENwvoVjbkwWnqWBiVXW1mMGdtUV/2YoaWFA=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qekizb6wmf"}, {"Name": "label", "Value": "server/chunk-MQBUCPND.mjs"}, {"Name": "integrity", "Value": "sha256-0qYHYOASENwvoVjbkwWnqWBiVXW1mMGdtUV/2YoaWFA="}]}, {"Route": "server/chunk-NTD6FRHY.1aa8qe9n89.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-NTD6FRHY.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "493651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CYggUM/usQM3ZYhk8fodoSAeAh6fXa31liD3HXy/eXE=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1aa8qe9n89"}, {"Name": "label", "Value": "server/chunk-NTD6FRHY.mjs"}, {"Name": "integrity", "Value": "sha256-CYggUM/usQM3ZYhk8fodoSAeAh6fXa31liD3HXy/eXE="}]}, {"Route": "server/chunk-NTD6FRHY.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-NTD6FRHY.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "493651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CYggUM/usQM3ZYhk8fodoSAeAh6fXa31liD3HXy/eXE=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CYggUM/usQM3ZYhk8fodoSAeAh6fXa31liD3HXy/eXE="}]}, {"Route": "server/chunk-O7YEHCAM.0wokg7945o.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-O7YEHCAM.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "341429"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ANcoWnTIiUpeDlU2WDgPyLO/mGqjlAAkVCiCor9Ihks=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0wokg7945o"}, {"Name": "label", "Value": "server/chunk-O7YEHCAM.mjs"}, {"Name": "integrity", "Value": "sha256-ANcoWnTIiUpeDlU2WDgPyLO/mGqjlAAkVCiCor9Ihks="}]}, {"Route": "server/chunk-O7YEHCAM.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-O7YEHCAM.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "341429"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ANcoWnTIiUpeDlU2WDgPyLO/mGqjlAAkVCiCor9Ihks=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ANcoWnTIiUpeDlU2WDgPyLO/mGqjlAAkVCiCor9Ihks="}]}, {"Route": "server/chunk-QD3LIJ7Q.01t1i1bed2.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-QD3LIJ7Q.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "703"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zJD0AwLc6Mb1SCoetHwOM1FoUspRXuQDsvWfYn6q5us=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "01t1i1bed2"}, {"Name": "label", "Value": "server/chunk-QD3LIJ7Q.mjs"}, {"Name": "integrity", "Value": "sha256-zJD0AwLc6Mb1SCoetHwOM1FoUspRXuQDsvWfYn6q5us="}]}, {"Route": "server/chunk-QD3LIJ7Q.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-QD3LIJ7Q.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "703"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zJD0AwLc6Mb1SCoetHwOM1FoUspRXuQDsvWfYn6q5us=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zJD0AwLc6Mb1SCoetHwOM1FoUspRXuQDsvWfYn6q5us="}]}, {"Route": "server/chunk-RBXEQ7AP.m2pnsz8uev.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-RBXEQ7AP.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "911"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ruZQpPRIePFcxvKDveqyYI512+CY0NXiumF85cJtsxo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m2pnsz8uev"}, {"Name": "label", "Value": "server/chunk-RBXEQ7AP.mjs"}, {"Name": "integrity", "Value": "sha256-ruZQpPRIePFcxvKDveqyYI512+CY0NXiumF85cJtsxo="}]}, {"Route": "server/chunk-RBXEQ7AP.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-RBXEQ7AP.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "911"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ruZQpPRIePFcxvKDveqyYI512+CY0NXiumF85cJtsxo=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ruZQpPRIePFcxvKDveqyYI512+CY0NXiumF85cJtsxo="}]}, {"Route": "server/chunk-S6KH3LOX.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-S6KH3LOX.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2772"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"atkqHkVSOygHl0GemnLfIg8QgsTo6mKlo/IIj6XO9wQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-atkqHkVSOygHl0GemnLfIg8QgsTo6mKlo/IIj6XO9wQ="}]}, {"Route": "server/chunk-S6KH3LOX.mp9tv9odxm.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-S6KH3LOX.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2772"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"atkqHkVSOygHl0GemnLfIg8QgsTo6mKlo/IIj6XO9wQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mp9tv9odxm"}, {"Name": "label", "Value": "server/chunk-S6KH3LOX.mjs"}, {"Name": "integrity", "Value": "sha256-atkqHkVSOygHl0GemnLfIg8QgsTo6mKlo/IIj6XO9wQ="}]}, {"Route": "server/chunk-TRSHSL3W.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-TRSHSL3W.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wZ2L+chNf9nrsk+SutiIKxg4r0vj8Jnw44+29S6EDi0=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wZ2L+chNf9nrsk+SutiIKxg4r0vj8Jnw44+29S6EDi0="}]}, {"Route": "server/chunk-TRSHSL3W.rdt6s731km.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-TRSHSL3W.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wZ2L+chNf9nrsk+SutiIKxg4r0vj8Jnw44+29S6EDi0=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rdt6s731km"}, {"Name": "label", "Value": "server/chunk-TRSHSL3W.mjs"}, {"Name": "integrity", "Value": "sha256-wZ2L+chNf9nrsk+SutiIKxg4r0vj8Jnw44+29S6EDi0="}]}, {"Route": "server/chunk-UDSHM75V.b1s8mewln8.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-UDSHM75V.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "151616"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"700jKVDbBvg7yDjP4TZozGrnXBajVyTuD8soswcDK1k=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b1s8mewln8"}, {"Name": "label", "Value": "server/chunk-UDSHM75V.mjs"}, {"Name": "integrity", "Value": "sha256-700jKVDbBvg7yDjP4TZozGrnXBajVyTuD8soswcDK1k="}]}, {"Route": "server/chunk-UDSHM75V.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\chunk-UDSHM75V.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "151616"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"700jKVDbBvg7yDjP4TZozGrnXBajVyTuD8soswcDK1k=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-700jKVDbBvg7yDjP4TZozGrnXBajVyTuD8soswcDK1k="}]}, {"Route": "server/index.server.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\index.server.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17672"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"PzFdCtPUYO/TQTJT0sVL1reocUICV350o5XR5Dm5lI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PzFdCtPUYO/TQTJT0sVL1reocUICV350o5XR5Dm5lI8="}]}, {"Route": "server/index.server.t1crhiauiq.html", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\index.server.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17672"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"PzFdCtPUYO/TQTJT0sVL1reocUICV350o5XR5Dm5lI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1crhiauiq"}, {"Name": "label", "Value": "server/index.server.html"}, {"Name": "integrity", "Value": "sha256-PzFdCtPUYO/TQTJT0sVL1reocUICV350o5XR5Dm5lI8="}]}, {"Route": "server/main.server.9c0132p83u.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\main.server.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "568"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B43H+FrHvC7pwSqVxuaa7sawU6O8UxtnxSf4k3o4Yx8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9c0132p83u"}, {"Name": "label", "Value": "server/main.server.mjs"}, {"Name": "integrity", "Value": "sha256-B43H+FrHvC7pwSqVxuaa7sawU6O8UxtnxSf4k3o4Yx8="}]}, {"Route": "server/main.server.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\main.server.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "568"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B43H+FrHvC7pwSqVxuaa7sawU6O8UxtnxSf4k3o4Yx8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B43H+FrHvC7pwSqVxuaa7sawU6O8UxtnxSf4k3o4Yx8="}]}, {"Route": "server/polyfills.server.kjiojcr5dc.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\polyfills.server.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "266084"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IPlcoC2Xd6jYRduLzIsMtT7xaJC3KgyNGhiOAOgJnb8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjiojcr5dc"}, {"Name": "label", "Value": "server/polyfills.server.mjs"}, {"Name": "integrity", "Value": "sha256-IPlcoC2Xd6jYRduLzIsMtT7xaJC3KgyNGhiOAOgJnb8="}]}, {"Route": "server/polyfills.server.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\polyfills.server.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "266084"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IPlcoC2Xd6jYRduLzIsMtT7xaJC3KgyNGhiOAOgJnb8=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IPlcoC2Xd6jYRduLzIsMtT7xaJC3KgyNGhiOAOgJnb8="}]}, {"Route": "server/server.9c6byiefvl.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\server.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "843918"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"J4EFkjY5zdKM+PFCLabS4kjtWYzim48BD0fc6uvb8vw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9c6byiefvl"}, {"Name": "label", "Value": "server/server.mjs"}, {"Name": "integrity", "Value": "sha256-J4EFkjY5zdKM+PFCLabS4kjtWYzim48BD0fc6uvb8vw="}]}, {"Route": "server/server.mjs", "AssetFile": "G:\\DoorAPP\\DoorCompanyApp\\DoorCompany.Api\\wwwroot\\server\\server.mjs", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "843918"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"J4EFkjY5zdKM+PFCLabS4kjtWYzim48BD0fc6uvb8vw=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 15:51:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J4EFkjY5zdKM+PFCLabS4kjtWYzim48BD0fc6uvb8vw="}]}]}