import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PartnerRoutingModule } from './partner-routing.module';
import { PartnerListComponent } from './component/partner-list/partner-list.component';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PartnerDialogComponent } from './component/partner-dialog/partner-dialog.component';
import { MatCheckbox } from "@angular/material/checkbox";
import { MatInputModule } from "@angular/material/input";
import { ReactiveFormsModule } from '@angular/forms';
import { MatSortModule } from '@angular/material/sort';
import { PartnerListTransactionComponent } from './component/partner-list-transaction/partner-list-transaction.component';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { PartnerTransationDialogComponent } from './component/partner-transation-dialog/partner-transation-dialog.component';
import {MatRadioModule} from '@angular/material/radio';
import { BandListComponent } from './component/band-list/band-list.component';
import { PartnerBandDialogComponent } from './component/partner-band-dialog/partner-band-dialog.component';
import { ShareTransactionListComponent } from './component/share-transaction-list/share-transaction-list.component';
import { ShareTransactionDialogComponent } from './component/share-transaction-dialog/share-transaction-dialog.component';
import { SharedModule } from '../../shared/shared.module';
@NgModule({
  declarations: [
    PartnerListComponent,
    PartnerDialogComponent,
    PartnerListTransactionComponent,
    PartnerTransationDialogComponent,
    BandListComponent,
    PartnerBandDialogComponent,
    ShareTransactionListComponent,
    ShareTransactionDialogComponent
    
    
  ],
  imports: [
    CommonModule,
    PartnerRoutingModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    MatCheckbox,
    MatInputModule,
    ReactiveFormsModule,
    MatSortModule,
    MatPaginatorModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    MatRadioModule,
    SharedModule
    
   
    
]
})
export class PartnerModule { }
