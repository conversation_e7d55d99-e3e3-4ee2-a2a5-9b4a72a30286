﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PartnerBandController : AppControllerBase
    {
     

        public PartnerBandController(IUnitOfWork work) : base(work)
        {
        }
        /// <summary>
        /// Get all partnerBands
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAllPartnerBandss([FromQuery] bool isDelete = false)
        {
            var response = await _work.PartnerRepository.GetAllPartnersBandAsync(isDelete);
            return NewResult(response);
        }

        /// <summary>
        /// Get partnerBands by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetPartnerBandsById(int id)
        {
            var response = await _work.PartnerRepository.GetPartnerBandByIdAsync(id);
            return NewResult(response);
        }
        /// <summary>
        /// create Partner
        /// </summary>
        /// <param name="createDto"></param>
        /// <returns></returns>
        [HttpPost()]
        public async Task<IActionResult> CreatePartnerBandsAsycn(CreatePartnerBandDto createDto)
        {
            if (createDto == null) {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.PartnerRepository.CreatePartnerBandAsycn(createDto);
            return NewResult(response);
        }

        /// <summary>
        /// Update PartnerBands
        /// </summary>
        /// <param name="Dto"></param>
        /// <returns></returns>
        [HttpPut()]
        public async Task<IActionResult> UpdatePartnerAsycn(UpdatePartnerBandDto Dto)
        {
            if (Dto == null) {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.PartnerRepository.UpdatePartnerBandAsycn(Dto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete PartnerBands
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePartnerBandsAsync(int id)
        {        
            var response = await _work.PartnerRepository.DeletePartnerBandAsync(id);
            return NewResult(response);
        }

    }
}
