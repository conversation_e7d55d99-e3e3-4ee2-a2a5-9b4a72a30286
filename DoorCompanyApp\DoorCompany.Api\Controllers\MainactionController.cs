﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MainactionController : AppControllerBase
    {
        public MainactionController(IUnitOfWork work) : base(work)
        {
        }
        /// <summary>
        /// Get all Mainactions
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAlMainactions()
        {
            var response = await _work.MainActionRepository.GetALLMainActionAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get Mainaction by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetMainactionById(int id)
        {
            var response = await _work.MainActionRepository.GetMainActionByIdAsync(id);
            return NewResult(response);
        }


        /// <summary>
        /// Get Mainaction by Parnate
        /// </summary>
        [HttpGet("MainActionByAction/{parentId}")]
        public async Task<IActionResult> GetMainActionByActionAsync(int parentId)
        {
            var response = await _work.MainActionRepository.GetMainActionByActionAsync(parentId);
            return NewResult(response);
        }
      
    }
}
