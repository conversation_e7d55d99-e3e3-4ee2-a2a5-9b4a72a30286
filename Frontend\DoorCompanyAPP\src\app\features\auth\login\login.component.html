<div class="login-container">
  <!-- Background -->
  <div class="login-background">
    <div class="background-pattern"></div>
    <div class="background-overlay"></div>
  </div>

  <!-- Content -->
  <div class="login-content">
    <!-- Company Logo Section -->
    <div class="company-section">
      <div class="company-logo">
        <mat-icon class="logo-icon">business</mat-icon>
      </div>
      <h1 class="company-name">شركة جنرال للأجهزة الإلكترونية</h1>
      <p class="company-subtitle">نظام إدارة المبيعات</p>
    </div>

    <!-- Login Card -->
    <div class="login-card-container">
      <mat-card class="login-card">
        <mat-card-header class="login-header">
          <div class="header-content">
            <mat-icon class="login-icon">login</mat-icon>
            <div class="header-text">
              <mat-card-title class="login-title">تسجيل الدخول</mat-card-title>
              <mat-card-subtitle class="login-subtitle">أدخل بياناتك للوصول إلى النظام</mat-card-subtitle>
            </div>
          </div>
        </mat-card-header>

        <mat-card-content class="login-form-content">
          <!-- Error Message -->
          <div *ngIf="errorMessage" class="error-message">
            <mat-icon>error</mat-icon>
            <span>{{ errorMessage }}</span>
          </div>

          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>اسم المستخدم</mat-label>
              <input matInput
                     formControlName="username"
                     placeholder="أدخل اسم المستخدم"
                     autocomplete="username">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="f['username'].touched && f['username'].invalid">
                {{ getErrorMessage('username') }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>كلمة المرور</mat-label>
              <input matInput
                     [type]="hidePassword ? 'password' : 'text'"
                     formControlName="password"
                     placeholder="أدخل كلمة المرور"
                     autocomplete="current-password">
              <button class="toggle-password-btn"
                      mat-icon-button 
                      matSuffix
                      type="button"
                      (click)="hidePassword = !hidePassword"
                      [attr.aria-label]="'إظهار/إخفاء كلمة المرور'"
                      [attr.aria-pressed]="hidePassword">
                <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
              </button>
              <mat-error *ngIf="f['password'].touched && f['password'].invalid">
                {{ getErrorMessage('password') }}
              </mat-error>
            </mat-form-field>

            <button mat-raised-button
                    color="primary"
                    type="submit"
                    class="login-button full-width"
                    [disabled]="loginForm.invalid || loading">
              <mat-spinner *ngIf="loading" diameter="20" class="login-spinner"></mat-spinner>
              <mat-icon *ngIf="!loading" class="button-icon">login</mat-icon>
              <span *ngIf="!loading">تسجيل الدخول</span>
              <span *ngIf="loading">جاري تسجيل الدخول...</span>
            </button>
          </form>
        </mat-card-content>
      </mat-card>
    </div>

    
    <!-- Footer -->
    <div class="login-footer">
      <p class="footer-text">© 2025 شركة جنرال للأجهزة الإلكترونية. جميع الحقوق محفوظة.</p>
    </div>
  </div>
</div>
