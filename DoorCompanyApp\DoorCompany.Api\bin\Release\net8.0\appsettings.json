{"ConnectionStrings": {"DefaultConnection": "Data Source=*************\\SQLEXPRESS;Initial Catalog=DoorDb;User ID=***;Password=***;TrustServerCertificate=True;MultipleActiveResultSets=True;Integrated Security=False"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLongForSecurity!@#$%^&*()", "Issuer": "DoorCompanyApp", "Audience": "DoorCompanyApp", "AccessTokenExpirationMinutes": 2, "RefreshTokenExpirationDays": 7, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ClockSkewMinutes": 5}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}