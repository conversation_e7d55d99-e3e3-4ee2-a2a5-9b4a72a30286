﻿using DoorCompany.Data.Context;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace DoorCompany.Api.Filters
{
    public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
    {

        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ApplicationDbContext _context;
       
        public PermissionAuthorizationHandler(IHttpContextAccessor httpContextAccessor, ApplicationDbContext context)
        {
             _httpContextAccessor = httpContextAccessor; 
            _context = context;
        }
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, PermissionRequirement requirement)
        {

            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null || context.User == null)
                return;


            //var canAccess = context.User.Claims.Any(c =>
            //                   c.Type == "permission" &&
            //                   c.Value == requirement.Permission);


            var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return;


            // مثال على قراءة الصلاحيات من قاعدة البيانات
            var user = await _context.Users.Include(u => u.UserRoles!)
                    .ThenInclude(ur => ur.Roles!)
                        .ThenInclude(r => r.RolePermissions!)
                            .ThenInclude(rp => rp.Permissions)
                .FirstOrDefaultAsync(p => p.Id ==Convert.ToInt32(userId) && p.IsActive == true);

            var permissions = user!.UserRoles!
                .SelectMany(ur => ur.Roles.RolePermissions!)
                .Any(a => a.Permissions.Name == requirement.Permission);

         
            if (permissions)
                context.Succeed(requirement);

            return;
        }

      
    }
}
