# دليل اختبار نظام إدارة الفواتير

## خطوات الاختبار

### 1. التحضير
```bash
# تشغيل المشروع
cd DoorCompanyApp/DoorCompany.Api
dotnet run
```

### 2. اختبار إنشاء فاتورة بيع

#### طلب POST إلى `/api/Invoice`
```json
{
  "invoiceNumber": "",
  "invoiceType": 0,
  "invoiceDate": "2025-01-13T00:00:00",
  "partyId": 1,
  "subTotal": 1000.00,
  "itemDiscountAmount": 50.00,
  "invoiceDiscountAmount": 20.00,
  "taxAmount": 150.00,
  "totalAmount": 1080.00,
  "paidAmount": 500.00,
  "remainingAmount": 580.00,
  "paymentType": 0,
  "isPaid": false,
  "notes": "فاتورة بيع تجريبية",
  "items": [
    {
      "productId": 1,
      "quantity": 5.00,
      "unitPrice": 200.00,
      "discountPercentage": 5.00,
      "discountAmount": 50.00,
      "totalPrice": 950.00,
      "notes": "صنف أول"
    }
  ]
}
```

### 3. اختبار إنشاء فاتورة شراء

#### طلب POST إلى `/api/Invoice`
```json
{
  "invoiceNumber": "",
  "invoiceType": 1,
  "invoiceDate": "2025-01-13T00:00:00",
  "partyId": 2,
  "subTotal": 800.00,
  "itemDiscountAmount": 0.00,
  "invoiceDiscountAmount": 0.00,
  "taxAmount": 120.00,
  "totalAmount": 920.00,
  "paidAmount": 920.00,
  "remainingAmount": 0.00,
  "paymentType": 0,
  "isPaid": true,
  "notes": "فاتورة شراء تجريبية",
  "items": [
    {
      "productId": 1,
      "quantity": 10.00,
      "unitPrice": 80.00,
      "discountPercentage": 0.00,
      "discountAmount": 0.00,
      "totalPrice": 800.00,
      "notes": "شراء مخزون"
    }
  ]
}
```

### 4. اختبار تسديد فاتورة

#### طلب POST إلى `/api/Invoice/{invoiceId}/pay`
```json
{
  "amount": 580.00,
  "paymentType": 0,
  "notes": "تسديد المبلغ المتبقي",
  "paymentDate": "2025-01-13T10:00:00"
}
```

### 5. اختبار تقارير المخزون

#### طلب GET إلى `/api/Inventory/report`
```
?pageNumber=1&pageSize=10&searchTerm=&isLowStock=false
```

### 6. اختبار رصيد الخزينة

#### طلب GET إلى `/api/Financial/cash-balance`

### 7. اختبار حساب عميل

#### طلب GET إلى `/api/Financial/customer-account/{customerId}`
```
?fromDate=2025-01-01&toDate=2025-01-31
```

## سيناريوهات الاختبار

### سيناريو 1: دورة بيع كاملة
1. إنشاء فاتورة بيع
2. التحقق من تحديث المخزون
3. تسديد جزئي
4. تسديد المبلغ المتبقي
5. التحقق من رصيد الخزينة

### سيناريو 2: دورة شراء كاملة
1. إنشاء فاتورة شراء
2. التحقق من تحديث المخزون
3. سداد للمورد
4. التحقق من رصيد الخزينة

### سيناريو 3: إدارة المخزون
1. التحقق من الأرصدة الحالية
2. إنشاء فاتورة بيع تتجاوز المخزون المتاح
3. التحقق من رسالة الخطأ
4. إنشاء فاتورة شراء لتعبئة المخزون
5. إعادة محاولة البيع

### سيناريو 4: التقارير المالية
1. إنشاء عدة فواتير بيع وشراء
2. عرض تقرير الخزينة
3. عرض أرصدة العملاء
4. عرض أرصدة الموردين

## نقاط الاختبار المهمة

### التحقق من صحة البيانات
- محاولة إنشاء فاتورة بدون أصناف
- محاولة بيع كمية أكبر من المتوفر
- محاولة تسديد مبلغ أكبر من المتبقي

### التحقق من تماسك البيانات
- التأكد من تطابق أرصدة المخزون مع الحركات
- التأكد من تطابق أرصدة الخزينة مع المعاملات
- التأكد من تطابق أرصدة العملاء مع الفواتير

### اختبار الأداء
- إنشاء عدد كبير من الفواتير
- اختبار سرعة التقارير
- اختبار الاستعلامات المعقدة

## أدوات الاختبار المقترحة
- Postman أو Insomnia لاختبار API
- Swagger UI المدمج في المشروع
- Unit Tests للخدمات
- Integration Tests للكنترولرز

## البيانات التجريبية المطلوبة
- منتجات مع أرصدة مخزون
- عملاء وموردين
- فئات منتجات
- وحدات قياس

## ملاحظات الاختبار
- تأكد من وجود بيانات أساسية قبل الاختبار
- راقب قاعدة البيانات أثناء الاختبار
- احفظ نسخة احتياطية قبل الاختبار المكثف
- اختبر السيناريوهات الاستثنائية
