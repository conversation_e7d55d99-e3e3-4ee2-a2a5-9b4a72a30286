﻿using AutoMapper;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DoorCompany.Service.Dtos.ShareDto;
using DoorCompany.Service.Dtos.PartnerDto;
using Microsoft.EntityFrameworkCore;
using DoorCompany.Data.Models;

namespace DoorCompany.Service.Repositories.Implementations
{
    public class ShareService : ResponseHandler, IShareRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<ShareService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IFileUploadService _fileUploadService;
        public ShareService(IUnitOfWorkOfService unitOfWork, IMapper mapper,
                              ILogger<ShareService> logger, IHttpContextAccessor httpContextAccessor,
                             IFileUploadService fileUploadService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _fileUploadService = fileUploadService;
        }

        public async Task<ApiResponse<List<ShareResponseDto>>> GetAllShareAsync()
        {
            try
            {
                var Shares = await _unitOfWork.ShareDistributions.GetTableNoTracking()
                    .Where(p => !p.IsDeleted)
                    .ToListAsync();

                if (Shares.Count == 0)
                    return NotFound<List<ShareResponseDto>>("لا يوجد بيانات");

                var shareDto = _mapper.Map<List<ShareResponseDto>>(Shares);
                return Success(shareDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all Shares");
                return BadRequest<List<ShareResponseDto>>("حدث خطأ أثناء جلب البيانات ");
            }
        }
        public async Task<ApiResponse<ShareResponseDto>> GetShareByIdAsync(int id)
        {
            try
            {
                var share = await _unitOfWork.ShareDistributions.GetTableNoTracking()
                    .Include(p => p.Partners)
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (share == null)
                    return NotFound<ShareResponseDto>("غير موجود");

            
                var shareDto = _mapper.Map<ShareResponseDto>(share);
          

                return Success(shareDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting share by id: {shareId}", id);
                return BadRequest<ShareResponseDto>("حدث خطأ أثناء جلب بيانات");
            }
            ;
        }
        public async Task<ApiResponse<ShareResponseDto>> CreateShareAsycn(CreateShareDto createDto)
        {
            //اولا التحقق من ان اسم الشريك مش موجود
            try
            {           
                var share = _mapper.Map<ShareDistribution>(createDto);

                await _unitOfWork.ShareDistributions.AddAsync(share);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Share created successfully: {Description}", share.Description);

                var result = await GetShareByIdAsync(share.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Share: {name}", createDto.Description);
                return BadRequest<ShareResponseDto>("حدث خطأ أثناء إنشاء");
            }
        }
        public async Task<ApiResponse<ShareResponseDto>> UpdateShareAsycn(UpdateShareDto Dto)
        {
            try
            {
                var existingShare = await _unitOfWork.ShareDistributions.GetByIdAsync(Dto.Id);
                if (existingShare == null || existingShare.IsDeleted)
                    return NotFound<ShareResponseDto>("غير موجود");

               
                _mapper.Map(Dto, existingShare);
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.ShareDistributions.UpdateAsync(existingShare);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Share updated successfully: {ShareId}", existingShare.Id);

                var result = await GetShareByIdAsync(existingShare.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating Share: {ShareId}", Dto.Id);
                return BadRequest<ShareResponseDto>("حدث خطأ أثناء تحديث ");
            }
        }
        public async Task<ApiResponse<bool>> DeleteShareAsync(int id)
        {
            try
            {
                var partnerBand = await _unitOfWork.ShareDistributions.GetByIdAsync(id);
                if (partnerBand == null || partnerBand.IsDeleted)
                    return NotFound<bool>("غير موجود");

                //// Check if partnerBand has transactions
                //var hasTransactions = await _unitOfWork.ShareDistributions.GetTableNoTracking()
                //    .AnyAsync(t => t.PartnerBandId == id && !t.IsDeleted);

                //if (hasTransactions)
                //{
                //    partnerBand.IsDeleted = true;
                //    partnerBand.UpdatedAt = DateTime.Now;

                //    await _unitOfWork.PartnerBands.UpdateAsync(partnerBand);
                //    await _unitOfWork.SaveChangesAsync();

                //    return Success(true, "تم حذف البيانات بنجاح");
                //}

                await _unitOfWork.ShareDistributions.DeleteAsync(partnerBand);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم حذف البيانات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting ShareDistributions: {ShareDistributionsId}", id);
                return BadRequest<bool>("حدث خطأ أثناء حذف البيانات");
            }
        }

        public async Task<ApiResponse<decimal>> GetAllInvestmentsAsync()
        {

            try
            {     
            var allPartners = await _unitOfWork.Partners.GetTableNoTracking()
                  .Where(p => !p.IsDeleted)
                  .Include(p => p.PartnerTransations)
                  .ToListAsync();

                if (!allPartners.Any()) {

                    _logger.LogError("Error  : {GetAllInvestmentsAsync}", 0);
                    return BadRequest<decimal>("لا يوجد شركاء");
                }



            decimal allInitialCapital = Convert.ToDecimal(allPartners.Sum(p => p.InitialCapital ?? 0));

            // Flatten all transactions from all partners
            var allTransactions = allPartners
                .SelectMany(p => p.PartnerTransations)
                .Where(t => !t.IsDeleted)
                .ToList();

            // Calculate total investments (ActionDetailId == 2) and withdrawals (ActionDetailId == 3)
            decimal totalInvestments = allTransactions
                .Where(t => t.ActionDetailId == 2)
                .Sum(t => t.Amount); // Assuming Amount is nullable

            decimal totalWithdrawals = allTransactions
                .Where(t => t.ActionDetailId == 3)
                .Sum(t => t.Amount);

            // Current capital = initial + investments - withdrawals
            decimal currentCapital = allInitialCapital + totalInvestments - totalWithdrawals;

            
            return Success(currentCapital);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting ShareDistributions: {GetAllInvestmentsAsync}", 0);
                return BadRequest<decimal>("حدث خطاء ما");
            }


        }

        public async Task<ApiResponse<int>> GetCountShareCompanyAsync()
        {
            try
            {
                var company = await _unitOfWork.Companies.GetTableNoTracking()
                    .Where(c => !c.IsDeleted)
                    .FirstOrDefaultAsync();

                if (company == null)
                {
                    return NotFound<int>("لا توجد شركات متاحة.");
                }

                return Success(company.TotalShares);
            }
            catch (Exception ex)
            {
                return NotFound<int>($"حدث خطأ أثناء جلب بيانات الشركة: {ex.Message}");
            }
        }
    }
}
