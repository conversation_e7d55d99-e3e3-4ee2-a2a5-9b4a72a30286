/* .profile-container */
.profile-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  direction: rtl;
}

/* .loading-container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container mat-spinner {
  margin-bottom: 16px;
}

.loading-container p {
  color: #666;
  font-size: 16px;
}

/* .profile-header */
.profile-header {
  text-align: center;
  margin-bottom: 32px;
}

.profile-header .header-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #2196f3;
  margin-bottom: 16px;
}

.profile-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 2rem;
  font-weight: 500;
}

.profile-header p {
  margin: 0;
  color: #666;
  font-size: 1.1rem;
}

/* .profile-grid */
.profile-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
}

@media (max-width: 768px) {
  .profile-grid {
    grid-template-columns: 1fr;
  }
}

/* .image-card */
.image-card {
  grid-row: span 2;
}

@media (max-width: 768px) {
  .image-card {
    grid-row: span 1;
  }
}

/* mat-card */
mat-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

mat-card:hover {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

/* mat-card-header */
mat-card-header {
  margin-bottom: 16px;
}

mat-card-header mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  font-size: 1.3rem;
}

mat-card-header mat-card-title mat-icon {
  color: #2196f3;
}

/* Image Section Styles */
.image-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

/* .image-container */
.image-container {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid #e0e0e0;
  transition: border-color 0.3s ease;
}

.image-container:hover {
  border-color: #2196f3;
}

/* .profile-image */
.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.profile-image.default-image {
  opacity: 0.7;
}

.profile-image:hover {
  transform: scale(1.05);
}

/* .image-overlay */
.image-overlay {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
}

.image-overlay button {
  background: transparent;
  color: white;
}

.image-overlay button:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* .upload-section */
.upload-section {
  text-align: center;
}

/* .upload-btn */
.upload-btn {
  margin-bottom: 12px;
  padding: 12px 24px;
}

.upload-btn mat-icon {
  margin-left: 8px;
}

/* .upload-hint */
.upload-hint {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

/* .preview-section */
.preview-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

/* .preview-container */
.preview-container {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #2196f3;
}

/* .preview-image */
.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* .preview-actions */
.preview-actions {
  display: flex;
  gap: 12px;
}

.preview-actions button {
  min-width: 120px;
}

/* Form Styles */
.form-row {
  margin-bottom: 20px;
}

.full-width {
  width: 100%;
}

mat-form-field .mat-mdc-form-field-subscript-wrapper {
  margin-top: 4px;
}

/* .readonly-section */
.readonly-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin: 20px 0;
}

/* .readonly-field */
.readonly-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.readonly-field:last-child {
  margin-bottom: 0;
}

.readonly-field label {
  font-weight: 500;
  color: #666;
}

.readonly-field span {
  color: #333;
}

/* .role-badge */
.role-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* .form-actions */
.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.form-actions button {
  min-width: 140px;
  padding: 12px 24px;
}

.form-actions button mat-icon {
  margin-left: 8px;
}

.form-actions button mat-spinner {
  margin-left: 8px;
}

/* Password Section Styles */
.password-description {
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .profile-header {
    margin-bottom: 24px;
  }

  .profile-header .header-icon {
    font-size: 36px;
    width: 36px;
    height: 36px;
  }

  .profile-header h1 {
    font-size: 1.5rem;
  }

  .profile-header p {
    font-size: 1rem;
  }

  .image-container {
    width: 150px;
    height: 150px;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }

  .preview-actions {
    flex-direction: column;
    width: 100%;
  }

  .preview-actions button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .readonly-field {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  z-index: 10;
}

/* Error States */
.error-message {
  color: #f44336;
  font-size: 0.9rem;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-message mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}