<div class="dialog-container">
  <div mat-dialog-title class="dialog-header">
    <mat-icon>{{ isEditMode ? 'edit' : 'person_add' }}</mat-icon>
    <h2>{{ isEditMode ? 'تعديل حركة الشريك' : 'إضافة حركة الشريك جديد' }}</h2>
  </div>

  <div mat-dialog-content class="dialog-content">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">

     
        <div class="form-row">
           <mat-form-field class="form-field">
                <mat-label>تاريخ الحركة</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="transactionDate" required>
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                    <mat-error *ngIf="form.get('transactionDate')?.hasError('required')">
                    تاريخ الحركة مطلوب
                    </mat-error>
            </mat-form-field>
           </div>

      <mat-radio-group aria-label="Select an option" formControlName="actionDetailId" required>
      <mat-radio-button *ngFor="let rep of actionDetails" [value]="rep.id">
        {{ rep.name }}
      </mat-radio-button>

      </mat-radio-group>
    


           <div class="form-row">
              <mat-form-field class="form-field">
                <mat-label>الشريك</mat-label>
                <mat-select formControlName="partnerId" required>
                <mat-option *ngFor="let rep of partners" [value]="rep.id">
                    {{ rep.name }}
                </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-error *ngIf="form.get('partnerId')?.hasError('required')">
              الشريك مطلوب
            </mat-error>
           </div>

           <div class="form-row">
              <mat-form-field class="form-field">
                <mat-label>البند</mat-label>
                <mat-select formControlName="partnerBandId" required>
                <mat-option *ngFor="let rep of partnerbands" [value]="rep.id">
                    {{ rep.name }}
                </mat-option>
                </mat-select>
               <mat-error *ngIf="form.get('partnerBandId')?.hasError('required')">
                    البند مطلوب
                 </mat-error> 
             </mat-form-field>
            <mat-form-field class="form-field adjust-width">
                <mat-label>المبلغ</mat-label>
                <input matInput type="number"
                    step="1.00"
                    min="0"  formControlName="amount" type="number" placeholder="أدخل المبلغ">
                <mat-icon matSuffix>ج.م</mat-icon>
                <mat-error *ngIf="form.get('amount')?.invalid && form.get('amount')?.touched">
                    {{ getErrorMessage('amount') }}
                </mat-error>
            </mat-form-field>
            </div>
          
            <div class="form-row">
            <mat-form-field class="full-width">
            <mat-label>الوصف</mat-label>
            <input matInput formControlName="description" placeholder="الوصف">
            <mat-icon matSuffix>description</mat-icon>
            <mat-error *ngIf="form.get('description')?.invalid && form.get('description')?.touched">
                {{ getErrorMessage('description') }}
            </mat-error>
            </mat-form-field>
            </div>

<mat-form-field class="full-width">
          <mat-label>ملاحظات</mat-label>
          <textarea matInput formControlName="notes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
        </mat-form-field>



  <!-- Profile Image Section -->
      <mat-card class="image-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>photo_camera</mat-icon>
            الصورة 
          </mat-card-title>
        </mat-card-header>
          <!--Image Profile -->
        <mat-card-content>
          <div class="image-section">
            <div class="image-container">
              <img [src]="imagePathUrl"
                   alt="الصورة الحالية" 
                   class="profile-image"
                   >
              <!-- [class.default-image]="!hasProfileImage()" -->
              <!-- <div class="image-overlay" *ngIf="hasProfileImage()">
                <button mat-icon-button 
                        color="warn" 
                        (click)="deleteImage()"
                        matTooltip="حذف الصورة">
                  <mat-icon>delete</mat-icon>
                </button>
              </div> -->
            </div>

            <!-- File Upload -->
            <div class="upload-section" *ngIf="!selectedFile">
              <input type="file" 
                     #fileInput 
                     (change)="onFileSelected($event)"
                     accept="image/*"
                     style="display: none;">
              
              <button mat-raised-button 
                      color="primary" 
                      (click)="fileInput.click()"
                      class="upload-btn"
                      type="button"
>
                <mat-icon>cloud_upload</mat-icon>
                اختيار صورة
              </button>
              
              <p class="upload-hint">
                الحد الأقصى: 5 ميجابايت<br>
                الأنواع المدعومة: JPG, PNG, GIF
              </p>
            </div> 

            <!-- Image Preview and Upload -->
            <div class="preview-section" *ngIf="selectedFile">
              <div class="preview-container">
                <img [src]="imagePreview" alt="معاينة الصورة" class="preview-image">
              </div> 
                <!-- <div class="preview-actions">
                        <button mat-raised-button 
                                color="primary" 
                                (click)="uploadImage()"
                                [disabled]="isUploadingImage">
                          <mat-icon *ngIf="!isUploadingImage">save</mat-icon>
                          <mat-spinner *ngIf="isUploadingImage" diameter="20"></mat-spinner>
                          {{ isUploadingImage ? 'جاري الرفع...' : 'حفظ الصورة' }}
                        </button>
                
                <button mat-stroked-button 
                        (click)="cancelImageUpload()"
                        [disabled]="isUploadingImage">
                  إلغاء
                </button>
              </div> -->
            </div>
          </div>
        </mat-card-content>
      </mat-card>




    </form>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button mat-button (click)="onCancel()" [disabled]="loading">
      إلغاء
    </button>
    <button mat-raised-button 
            color="primary" 
            (click)="onSubmit()" 
            [disabled]="form.invalid || loading">
      <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
      <mat-icon *ngIf="!loading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
      {{ isEditMode ? 'حفظ التغييرات' : 'إضافة الشريك' }}
    </button>
  </div>
</div>


