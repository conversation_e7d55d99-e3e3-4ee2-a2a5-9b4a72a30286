﻿using AutoMapper;
using Azure.Core;
using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Dtos.ProductDto;
using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using DoorCompany.Service.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Repositories.Implementations
{
    public class ProductService :ResponseHandler, IProductRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<ProductService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IFileUploadService _fileUploadService;
        public ProductService(IUnitOfWorkOfService unitOfWork, IMapper mapper,
                              ILogger<ProductService> logger, IHttpContextAccessor httpContextAccessor,
                             IFileUploadService fileUploadService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _fileUploadService = fileUploadService;
        }


        #region Product
        public async Task<ApiResponse<ProductResponseDto>> GetProductByIdAsync(int id)
        {
            try
            {
                var product = await _unitOfWork.Products.GetTableNoTracking()
                    .Include(c => c.ItemCategory)
                    .Include(u => u.ItemUnit)
                    .Include(t => t.InventoryTransactions)
                     .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (product == null)
                    return NotFound<ProductResponseDto>("لا يوجد بيانات");

                var productDto = _mapper.Map<ProductResponseDto>(product, opt =>
                {
                    opt.Items["baseUrl"] = $"{_httpContextAccessor.HttpContext!.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}";
                });

                var productBalance = await GetProductBalanceById(id);
                productDto.Balance = productBalance?.Balance;

                return Success(productDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Product by id: {ProductId}", id);
                return BadRequest<ProductResponseDto>("حدث خطأ أثناء جلب بيانات ");
            }
        }
        public async Task<ApiResponse<ProductResponseDto>> CreateProductAsync(CreateProductDto createDto)
        {
            try
            {
                var existingProducts =  _unitOfWork.Products.GetTableNoTracking();      
                
                if (existingProducts.Any(p => p.Name == createDto.Name))
                    return BadRequest<ProductResponseDto>("اسم الصنف موجود بالفعل");

                if (existingProducts.Any(i => i.Code == createDto.Code))
                    return BadRequest<ProductResponseDto>("كود الصنف موجود بالفعل");
            
                if (existingProducts.Any(i => i.Barcode == createDto.Barcode))
                    return BadRequest<ProductResponseDto>("باركود الصنف موجود بالفعل");             

                    string? imagePath = null;
                    // Handle profile image upload if provided
                    if (createDto.ImagePath != null)
                    {
                        // Upload new profile image
                        imagePath = await _fileUploadService.UploadImageAsync(createDto.ImagePath, "Items");
                    }

                    var productEntity = _mapper.Map<Product>(createDto);

                     productEntity.Code = createDto.Code?.Trim().ToUpper()!;
                     productEntity.Barcode = createDto.Barcode?.Trim().ToUpper()!;

                     productEntity.ImagePath = imagePath!;
                    

                    await _unitOfWork.Products.AddAsync(productEntity);
                   
                    await _unitOfWork.SaveChangesAsync();
                  
                    // إذا كان هناك رصيد افتتاحي
                    if (createDto.OpeningBalance > 0)
                    {
                    await AddOpeningBalanceAsync(productEntity.Id, createDto.OpeningBalance, createDto.StandardCost);
                    }


                _logger.LogInformation("Product created successfully: {Product.Name}", productEntity.Name);

                    var result = await GetProductByIdAsync(productEntity.Id);
                    return result;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating Product: {Product.Name}", createDto.Name);
                    return BadRequest<ProductResponseDto>("حدث خطأ أثناء إنشاء الصنف");
                }
            
        }
        private async Task<ProductInventory?> GetProductBalanceById(int productId)
        {
            var product = await _unitOfWork.ProductInventorys.GetTableNoTracking()
                         .FirstOrDefaultAsync(p => p.ProductId == productId && !p.IsDeleted);

            return product;
        }
     
        private async Task AddOpeningBalanceAsync(int productId, decimal quantity, decimal unitCost, DateTime? date = null)
        {
            var transaction = new InventoryTransaction
            {
                ProductId = productId,
                Type = InventoryTransactionType.OpeningBalance,
                TransactionDate = date ?? DateTime.Now,
                Quantity = quantity,
                UnitCost = unitCost,
                TotalCost = quantity * unitCost,
                BalanceAfter = quantity,
                AverageCost = unitCost,
                Description = "رصيد افتتاحي",
            };

            await _unitOfWork.InventoryTransactions.AddAsync(transaction);
            await _unitOfWork.SaveChangesAsync(); // سيُحدث ProductInventory تلقائيًا
        }
        #endregion

        #region Category
        public async Task<ApiResponse<List<CategoryResponseDto>>> GetAllCategoryAsync()
        {
            var categories = await _unitOfWork.ItemCategories.GetTableNoTracking()
                         .Include(c => c.ParentCategory)
                         .Include(c => c.ChildCategories)
                         .AsNoTracking()
                         .ToListAsync();

            var tree = BuildCategoryTree(categories, parentId: null, level: 0);

            return Success(tree);

           
        }
        public async Task<ApiResponse<CategoryResponseDto>> GetCategoryByIdAsync(int id)
        {
            var category = await _unitOfWork.ItemCategories.GetTableNoTracking()
            .Include(c => c.ParentCategory)
            .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);

            if (category == null)
                return NotFound<CategoryResponseDto>("لا يوجد بيانات");

            var dto = _mapper.Map<CategoryResponseDto>(category, opt =>
            {
                opt.Items["baseUrl"] = $"{_httpContextAccessor.HttpContext!.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}";
            });
            dto.ParentCategoryName = category.ParentCategory?.Name;
       
            return Success(dto);
        }
        public async Task<ApiResponse<CategoryResponseDto>> CreateCategoryAsync(CreateCategoryDto createDto)
        {

            try
            {

                var existingCategory = _unitOfWork.ItemCategories.GetTableNoTracking();

                if (existingCategory.Any(p => p.Name == createDto.Name && !p.IsDeleted))
                    return BadRequest<CategoryResponseDto>("اسم الفئة موجود بالفعل");

                if (existingCategory.Any(c => c.Code == createDto.Code && !c.IsDeleted))
                    return BadRequest<CategoryResponseDto>("كود الفئة موجود بالفعل.");

                if (createDto.ParentCategoryId.HasValue)
                {
                    var parent = await _unitOfWork.ItemCategories.GetByIdAsync(createDto.ParentCategoryId.Value);

                    if (parent == null || parent.IsDeleted)
                        return BadRequest<CategoryResponseDto>("الفئة الأم غير موجودة.");

                    // منع الحلقات (Circular Reference)
                    if (await IsAncestorAsync(createDto.ParentCategoryId.Value, 0)) // id=0 لأنه جديد
                    {
                        return BadRequest<CategoryResponseDto>("لا يمكن جعل الفئة فرعًا لواحدة من فئاتها الفرعية (حلقة غير مسموحة).");
                    }
                }

                string? imageUrl = null;
                if (createDto.ImageFile != null)
                {
                    imageUrl = await _fileUploadService.UploadImageAsync(createDto.ImageFile, "Categories");
                }

                var category = _mapper.Map<ItemCategory>(createDto);

                category.ImageUrl = imageUrl;
                category.Code = createDto.Code?.ToUpper(); 

                await _unitOfWork.ItemCategories.AddAsync(category);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Category created successfully: {Category.Name}", category.Name);

                var result = await GetCategoryByIdAsync(category.Id);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Category: {Category.Name}", createDto.Name);
                return BadRequest<CategoryResponseDto>("حدث خطأ أثناء إنشاء الصنف");
            }
        }
        private async Task<bool> IsAncestorAsync(int ancestorId, int childId)
        {
            if (ancestorId == childId) return true;

            var current = await _unitOfWork.ItemCategories.GetTableNoTracking()
                .Where(c => c.Id == childId && !c.IsDeleted)
                .Select(c => new { c.ParentCategoryId })
                .FirstOrDefaultAsync();

            while (current?.ParentCategoryId != null)
            {
                if (current.ParentCategoryId == ancestorId)
                    return true;

                current = await _unitOfWork.ItemCategories.GetTableNoTracking()
                    .Where(c => c.Id == current.ParentCategoryId && !c.IsDeleted)
                    .Select(c => new { c.ParentCategoryId })
                    .FirstOrDefaultAsync();
            }

            return false;
        }
        private List<CategoryResponseDto> BuildCategoryTree(List<ItemCategory> allCategories,int? parentId,int level)
        {
            var mapper = _mapper.ConfigurationProvider.CreateMapper();

            return allCategories
                .Where(c => c.ParentCategoryId == parentId && !c.IsDeleted)
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.Name)
                .Select(c =>
                {
                    var dto = mapper.Map<CategoryResponseDto>(c, opt =>
                    {
                        opt.Items["baseUrl"] = $"{_httpContextAccessor.HttpContext!.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}";
                    });

                    dto.Level = level;
                    dto.Children = BuildCategoryTree(allCategories, c.Id, level + 1);
                    dto.HasChildren = dto.Children.Any();

                    return dto;
                }).ToList();
        }

        #endregion

        #region Invoice
        public async Task<ApiResponse<InvoiceResponseDto>> CreateInvoiceAsync(CreateInvoiceMasterDto createDto)
        {

            // التحقق من صحة الفاتورة
            if (createDto.Items == null || !createDto.Items.Any())
                return BadRequest<InvoiceResponseDto>("يجب إضافة عناصر للفاتورة");

            // التحقق من وجود الطرف (عميل/مورد/إنتاج)
            var party = await _unitOfWork.Parties.GetTableNoTracking()
                .Include(c => c.Customer)
                .FirstOrDefaultAsync(p => p.Id == createDto.PartyId && !p.IsDeleted);

            if (party == null)
                return NotFound<InvoiceResponseDto>("الطرف غير موجود");

            // التحقق من الدور حسب نوع الفاتورة
            switch (createDto.InvoiceType)
            {
                case InvoiceType.Sale:
                case InvoiceType.SalesReturn:
                    if (party.Customer == null)
                        return NotFound<InvoiceResponseDto>("الطرف المحدد ليس عميلًا");
                    break;

                case InvoiceType.Purchase:
                case InvoiceType.PurchaseReturn:
                    if (party.Supplier == null)
                        return NotFound<InvoiceResponseDto>("الطرف المحدد ليس موردًا");
                    break;

                case InvoiceType.ProductionInput:
                case InvoiceType.ProductionOutput:
                case InvoiceType.Return:
                    if (party.ProductionUnit == null)
                        return NotFound<InvoiceResponseDto>("الطرف المحدد ليس قسم إنتاج");
                    break;

                default:
                    return BadRequest<InvoiceResponseDto>("نوع الفاتورة غير مدعوم");
            }


            foreach (var item in createDto.Items)
            {
                var product = await _unitOfWork.Products.GetTableNoTracking()
                    .Include(p => p.ItemUnit)
                    .FirstOrDefaultAsync(p => p.Id == item.ProductId && !p.IsDeleted);

                if (product == null)
                    return NotFound<InvoiceResponseDto>($"المنتج برقم {item.ProductId} غير موجود");

                // التحقق من الكمية فقط في الحركات الناقصة
                if (createDto.InvoiceType == InvoiceType.Sale ||
                    createDto.InvoiceType == InvoiceType.Return ||
                    createDto.InvoiceType == InvoiceType.ProductionOutput)
                {
                    var inventory = await _unitOfWork.ProductInventorys.GetTableNoTracking()
                        .FirstOrDefaultAsync(pi => pi.ProductId == item.ProductId);

                    if (inventory == null || inventory.Balance < item.Quantity)
                    {
                        return BadRequest<InvoiceResponseDto>(
                            $"الكمية غير كافية للمنتج '{product.Name}'. الرصيد: {inventory?.Balance ?? 0}, المطلوب: {item.Quantity}");
                    }
                }
            }

            // إنشاء رقم الفاتورة
            var invoiceNumber = await GenerateInvoiceNumberAsync(createDto.InvoiceType);

            //    decimal subTotal = createDto.Items.Sum(item => ((item.Quantity * item.UnitPrice * item.DiscountPercentage) / 100) - item.DiscountAmount);
            decimal subTotal = createDto.Items.Sum(item =>
            {
                decimal totalPrice = item.Quantity * item.UnitPrice;
                decimal percentageDiscount = (totalPrice * item.DiscountPercentage) / 100;
                return totalPrice - percentageDiscount - item.DiscountAmount;
            });
            //decimal discountAmount = createDto.ItemDiscountAmount;
            //decimal taxableAmount = subTotal - discountAmount;
            //decimal taxAmount = taxableAmount * (invoiceDto.TaxPercentage ?? 0) / 100;
            //decimal totalAmount = taxableAmount + taxAmount;
            var items = new List<InvoiceItemResponseDto>();
       
            // var invoice = _mapper.Map<InvoiceMaster>(createDto);
            foreach (var itemDto in createDto.Items)
            {
                var discountAmount = (itemDto.UnitPrice * itemDto.Quantity * itemDto.DiscountPercentage) / 100;
                var totalPrice = (itemDto.UnitPrice * itemDto.Quantity) - discountAmount;

                var item = new InvoiceItemResponseDto
                {
                    ProductId = itemDto.ProductId,
                    Quantity = itemDto.Quantity,
                    UnitPrice = itemDto.UnitPrice,
                    DiscountPercentage = itemDto.DiscountPercentage,
                    DiscountAmount = discountAmount,
                    TotalPrice = totalPrice,
                    Notes = itemDto.Notes
                };

                items.Add(item);
                
            }



                var invoice = new InvoiceResponseDto()
            {
                InvoiceNumber = invoiceNumber.ToString(),
                InvoiceDate = createDto.InvoiceDate == default ? DateTime.Now : createDto.InvoiceDate,
                IsPaid = createDto.PaidAmount >= createDto.TotalAmount,
                Id = 1,
                PartyName = party.Name + "-" + party.Customer?.SalesRepresentative,
                CreatedAt = DateTime.Now,
                InvoiceDiscountAmount = createDto.InvoiceDiscountAmount,
                ItemDiscountAmount = items.Sum(item => item.DiscountAmount),
                SubTotal = subTotal,
                PaidAmount = createDto.PaidAmount,
                InvoiceType = createDto.InvoiceType, 
                Items = items,
                
            };

        

        }

    
        private async Task<string> GenerateInvoiceNumberAsync(InvoiceType invoiceType)
        {
            string prefix = invoiceType switch
            {
                InvoiceType.Purchase => "INV-PUR",
                InvoiceType.Sale => "INV-SAL",
                InvoiceType.ProductionInput => "INV-PROIN",
                InvoiceType.ProductionOutput => "INV-PROOUT",
                InvoiceType.SalesReturn => "INV-RSAL",
                InvoiceType.PurchaseReturn => "INV-RPUR",
                _ => "INV-UNK"
            };

            var lastInvoice = await _unitOfWork.InvoiceMasters.GetTableNoTracking()
                .Where(i => i.InvoiceNumber!.StartsWith(prefix))
                .OrderByDescending(i => i.Id)
                .Select(i => i.InvoiceNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (!string.IsNullOrEmpty(lastInvoice) && int.TryParse(lastInvoice.Substring(prefix.Length), out int lastNum))
            {
                nextNumber = lastNum + 1;
            }

            return $"{prefix}{nextNumber:D6}";
        }



        #endregion


    }
}
