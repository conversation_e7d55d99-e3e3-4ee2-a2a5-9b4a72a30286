﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos;
using DoorCompany.Service.Dtos.PartnerDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PartnerController : AppControllerBase
    {
        public PartnerController(IUnitOfWork work) : base(work)
        {
        }
        /// <summary>
        /// Get all partners
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAllPartners([FromQuery] bool isDelete = false)
        {
            var response = await _work.PartnerRepository.GetAllPartnersAsync(isDelete);
            return NewResult(response);
        }

        /// <summary>
        /// Get partner by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetPartnerById(int id)
        {
            var response = await _work.PartnerRepository.GetPartnerByIdAsync(id);
            return NewResult(response);
        }
        /// <summary>
        /// create Partner
        /// </summary>
        /// <param name="createDto"></param>
        /// <returns></returns>
        [HttpPost()]
        public async Task<IActionResult> CreatePartnerAsycn(CreatePartnerDto createDto)
        {
            if (createDto == null) {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.PartnerRepository.CreatePartnerAsycn(createDto);
            return NewResult(response);
        }

        /// <summary>
        /// Update Partner
        /// </summary>
        /// <param name="Dto"></param>
        /// <returns></returns>
        [HttpPut()]
        public async Task<IActionResult> UpdatePartnerAsycn(UpdatePartnerDto Dto)
        {
            if (Dto == null) {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.PartnerRepository.UpdatePartnerAsycn(Dto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete Partner
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePartnerAsync(int id)
        {        
            var response = await _work.PartnerRepository.DeletePartnerAsync(id);
            return NewResult(response);
        }

        [HttpGet("get-all-transactions")]
        public async Task<IActionResult> GetAllPartnerTransaction([FromQuery] PartnerTransactionRequestDto req)
        {
            var response = await _work.PartnerRepository.GetAllPartnerTransactionAsync(req);
            return NewResult(response);
        }
        /// <summary>
        /// Get partner by ID
        /// </summary>
        [HttpGet("transaction/{id}")]
        public async Task<IActionResult> GetPartnerTransactionById(int id)
        {
            var response = await _work.PartnerRepository.GetPartnerTransactionByIdAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// create PartnerTransaction
        /// </summary>
        /// <param name="createDto"></param>
        [HttpPost("transactions")]
        public async Task<IActionResult> CreatePartnerTansactionAsycn([FromForm] CreatePartnerTransactionDto createDto)
        {
            if (createDto == null)
            {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.PartnerRepository.CreatePartnerTransactionAsync(createDto);
            return NewResult(response);
        }

        //  UpdatePartnerTransactionAsycn(UpdatePartnerTransactionDto Dto);

        /// <summary>
        /// Update PartnerTransaction
        /// </summary>
        /// <param name="dto"></param>
        [HttpPut("transactions")]
        public async Task<IActionResult> UpdatePartnerTransactionAsycn([FromForm] UpdatePartnerTransactionDto dto)
        {
            if (dto == null)
            {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.PartnerRepository.UpdatePartnerTransactionAsycn(dto);
            return NewResult(response);
        }

        /// <summary>
        /// Delele Transaction
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("transactions/{id}")]
        public async Task<IActionResult> DeletePartnerTransationAsync(int id)
        {
            var response =  await _work.PartnerRepository.DeletePartnerTransationAsync(id);

            return NewResult(response);
        }  
        
        [HttpGet("transactions/report")]
        public async Task<IActionResult> DelGetReportPartnerTransactionAsync([FromQuery] PartnerTransactionRequestDto req)
        {
            var response =  await _work.PartnerRepository.GetReportPartnerTransactionAsync(req);

            return NewResult(response);
        }

      
        [HttpGet("get-all-sharetransfer")]
        public async Task<IActionResult> GetAllShareTransactionAsync([FromQuery] ShareTransferRequestDto req)
        {
            var response = await _work.PartnerRepository.GetAllShareTransactionAsync(req);

            return NewResult(response);
        }

        /// <summary>
        /// Get share-transfer by ID
        /// </summary>
        [HttpGet("share-transfer/{id}")]
        public async Task<IActionResult> GetShareTransferByIdAsync(int id)
        {
            var response = await _work.PartnerRepository.GetShareTransferByIdAsync(id);
            return NewResult(response);
        }
        /// <summary>
        /// create ShareTransfer
        /// </summary>
        /// <param name="createDto"></param>
        [HttpPost("share-transfer")]
        public async Task<IActionResult> CreateShareTransferAsync(CreateShareTransferDto createDto)
        {
            if (createDto == null)
            {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.PartnerRepository.CreateShareTransferAsync(createDto);
            return NewResult(response);
        }

        /// <summary>
        /// Update Partner ShareTrans
        /// </summary>
        /// <param name="dto"></param>
        [HttpPut("share-transfer")]
        public async Task<IActionResult> UpdateShareTransferAsync(UpdateShareTransferDto  dto)
        {
            if (dto == null)
            {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.PartnerRepository.UpdateShareTransferAsync(dto);
            return NewResult(response);
        }

        [HttpGet("partners-summary")]
        public async Task<IActionResult> GetAllPartnerDataSummer()
        {
            var response = await _work.PartnerRepository.GetAllPartnerDataSummer();
            return NewResult(response);
        }

    }
}
