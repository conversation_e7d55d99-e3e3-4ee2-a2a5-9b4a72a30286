import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AngularAppEngine,
  InlineCriticalCssProcessor,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig,
  provideServerRoutesConfig,
  provideServerRouting,
  setAngularAppEngineManifest,
  setAngularAppManifest,
  withAppShell
} from "./chunk-CC2KHWFS.js";
import "./chunk-P2FRHF3L.js";
import "./chunk-7QUZPSXW.js";
import "./chunk-T3735ANT.js";
import "./chunk-6KQMH66P.js";
import "./chunk-R5VTAUEX.js";
import "./chunk-N55ZFUUE.js";
import "./chunk-SBQGIAH2.js";
import "./chunk-MKHVCWTC.js";
import "./chunk-YHCV7DAQ.js";
export {
  <PERSON><PERSON><PERSON><PERSON><PERSON>ng<PERSON>,
  PrerenderFallback,
  RenderMode,
  createRequestHand<PERSON>,
  provideServerRoutesConfig,
  provideServerRouting,
  withAppShell,
  InlineCriticalCssProcessor as ɵInlineCriticalCssProcessor,
  destroyAngularServerApp as ɵdestroyAngularServerApp,
  extractRoutesAndCreateRouteTree as ɵextractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as ɵgetOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig as ɵgetRoutesFromAngularRouterConfig,
  setAngularAppEngineManifest as ɵsetAngularAppEngineManifest,
  setAngularAppManifest as ɵsetAngularAppManifest
};
