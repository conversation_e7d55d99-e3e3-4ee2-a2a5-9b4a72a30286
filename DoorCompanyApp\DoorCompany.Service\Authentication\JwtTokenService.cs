using DoorCompany.Service.Repositories.Interfaces;
using DoorCompany.Data.Models;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using DoorComapany.Service.Authentication;
using DoorCompany.Service.Repositories.Interfaces.Basic;

namespace DoorCompany.Service.Authentication
{
    public class JwtTokenService : IJwtTokenService
    {
        private readonly JwtSettings _jwtSettings;
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly ILogger<JwtTokenService> _logger;

        public JwtTokenService(IOptions<JwtSettings> jwtSettings, IUnitOfWorkOfService unitOfWork, ILogger<JwtTokenService> logger)
        {
            _jwtSettings = jwtSettings.Value;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<string> GenerateAccessTokenAsync(User user, List<string> permissions)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_jwtSettings.SecretKey);

                var claims = new List<Claim>
                {
                    new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new(ClaimTypes.Name, user.UserName),
                    new("FullName", user.FullName),
                    new("UserId", user.Id.ToString()),
                    new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                    new(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
                };

                // Add permissions as claims
                foreach (var permission in permissions)
                {
                    claims.Add(new Claim("permission", permission));
                }

                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(claims),
                    Expires = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes),
                    Issuer = _jwtSettings.Issuer,
                    Audience = _jwtSettings.Audience,
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
                };

                var token = tokenHandler.CreateToken(tokenDescriptor);
                var tokenString = tokenHandler.WriteToken(token);

                _logger.LogInformation("Access token generated for user {UserId}", user.Id);
                return tokenString;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating access token for user {UserId}", user.Id);
                throw;
            }
        }

        public async Task<string> GenerateRefreshTokenAsync()
        {
            var randomBytes = new byte[64];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomBytes);
            return Convert.ToBase64String(randomBytes);
        }

        public async Task<ClaimsPrincipal?> ValidateTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_jwtSettings.SecretKey);

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = _jwtSettings.ValidateIssuerSigningKey,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = _jwtSettings.ValidateIssuer,
                    ValidIssuer = _jwtSettings.Issuer,
                    ValidateAudience = _jwtSettings.ValidateAudience,
                    ValidAudience = _jwtSettings.Audience,
                    ValidateLifetime = _jwtSettings.ValidateLifetime,
                    ClockSkew = TimeSpan.FromMinutes(_jwtSettings.ClockSkewMinutes)
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
                return principal;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Token validation failed");
                return null;
            }
        }

        public async Task<bool> ValidateRefreshTokenAsync(string refreshToken, int userId)
        {
            try
            {
                var storedToken = await _unitOfWork.RefreshTokens.GetTableNoTracking()
                    .FirstOrDefaultAsync(rt => rt.Token == refreshToken && rt.UserId == userId && !rt.IsRevoked && rt.ExpiryDate > DateTime.UtcNow);

                return storedToken != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating refresh token for user {UserId}", userId);
                return false;
            }
        }

        public async Task<string> RefreshAccessTokenAsync(string refreshToken)
        {
            try
            {
                var storedToken = await _unitOfWork.RefreshTokens.GetTableAsTracking()
                    .Include(rt => rt.User)
                     .ThenInclude(u => u.UserRoles!)
                       .ThenInclude(ur => ur.Roles!)
                        .ThenInclude(r => r.RolePermissions!)
                            .ThenInclude(rp => rp.Permissions)
                    .FirstOrDefaultAsync(rt => rt.Token == refreshToken && !rt.IsRevoked && rt.ExpiryDate > DateTime.UtcNow);

                if (storedToken == null)
                {
                    throw new SecurityTokenException("Invalid refresh token");
                }

                //Get User Roles
                var Roles = storedToken.User.UserRoles?.Select(ur => ur.Roles.Name).ToList();


                // Get user permissions
                var permissions = storedToken.User.UserRoles!
                    .SelectMany(ur => ur.Roles.RolePermissions!)
                    .Select(rp => rp.Permissions.Name)
                    .Distinct()
                    .ToList();

                // Generate new access token
                var newAccessToken = await GenerateAccessTokenAsync(storedToken.User, permissions);

                _logger.LogInformation("Access token refreshed for user {UserId}", storedToken.UserId);
                return newAccessToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing access token");
                throw;
            }
        }

        public async Task RevokeRefreshTokenAsync(string refreshToken)
        {
            try
            {
                var storedToken = await _unitOfWork.RefreshTokens.GetTableAsTracking()
                    .FirstOrDefaultAsync(rt => rt.Token == refreshToken);

                if (storedToken != null)
                {
                    storedToken.IsRevoked = true;
                    storedToken.RevokedAt = DateTime.UtcNow;
                    storedToken.RevokedReason = "Manual revocation";
                    await _unitOfWork.RefreshTokens.UpdateAsync(storedToken);
                    await _unitOfWork.SaveChangesAsync();

                    _logger.LogInformation("Refresh token revoked for user {UserId}", storedToken.UserId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking refresh token");
                throw;
            }
        }

        public async Task RevokeAllUserTokensAsync(int userId)
        {
            try
            {
                var userTokens = await _unitOfWork.RefreshTokens.GetTableAsTracking()
                    .Where(rt => rt.UserId == userId && !rt.IsRevoked)
                    .ToListAsync();

                foreach (var token in userTokens)
                {
                    token.IsRevoked = true;
                    token.RevokedAt = DateTime.UtcNow;
                    token.RevokedReason = "All tokens revoked";
                }

                await _unitOfWork.RefreshTokens.UpdateRangeAsync(userTokens);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("All refresh tokens revoked for user {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking all tokens for user {UserId}", userId);
                throw;
            }
        }

        public async Task CleanupExpiredTokensAsync()
        {
            try
            {
                var expiredTokens = await _unitOfWork.RefreshTokens.GetTableAsTracking()
                    .Where(rt => rt.ExpiryDate < DateTime.UtcNow)
                    .ToListAsync();

                await _unitOfWork.RefreshTokens.DeleteRangeAsync(expiredTokens);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Cleaned up {Count} expired refresh tokens", expiredTokens.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up expired tokens");
            }
        }

        public int GetUserIdFromToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jsonToken = tokenHandler.ReadJwtToken(token);
                var userIdClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == "UserId");
                
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                {
                    return userId;
                }
                
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        public List<string> GetPermissionsFromToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jsonToken = tokenHandler.ReadJwtToken(token);
                return jsonToken.Claims.Where(c => c.Type == "permission").Select(c => c.Value).ToList();
            }
            catch
            {
                return new List<string>();
            }
        }
    }
}
