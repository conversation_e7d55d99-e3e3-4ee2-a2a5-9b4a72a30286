import { Component ,OnInit} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { LoginRequest } from '../../../models/user.model';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../../services/auth.service';
import { AuthtestService } from '../../../services/authtest.service';
@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.css' 
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  loading = false;
  hidePassword = true;
  returnUrl: string = '/';
  errorMessage: string = '';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private authtestService: AuthtestService
  ) { }

  ngOnInit(): void {
    this.initializeForm();

    // Get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || 'auth/login';

    // Redirect to home if already logged in
    if (this.authService.isLoggedIn) {
      this.router.navigate([this.returnUrl]);
    }
  }

  private initializeForm(): void {
    this.loginForm = this.formBuilder.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  get f() {
    return this.loginForm.controls;
  }

  // onSubmit(): void {
  //   if (this.loginForm.invalid) {
  //     this.markFormGroupTouched();
  //     return;
  //   }

  //   this.loading = true;
  //   this.errorMessage = '';
  //   const loginRequest: LoginRequest = this.loginForm.value;

  //   this.authService.login(loginRequest).subscribe({
  //     next: (response) => {
  //       if (response.statusCode == 200 && response.data ) {
  //         this.snackBar.open('تم تسجيل الدخول بنجاح', 'إغلاق', {
  //           duration: 3000,
  //           horizontalPosition: 'center',
  //           verticalPosition: 'top',
  //           panelClass: ['success-snackbar']
  //         });
  //         this.router.navigate([this.returnUrl]);
  //       } else if(response.statusCode != 200 && response.data == null)
  //       {
  //         this.showError(response.message);
  //       }
  //       this.loading = false;
  //     },
  //     error: (error) => {
  //       this.showError('حدث خطأ أثناء تسجيل الدخول');
  //       this.loading = false;
  //     }
  //   });
  // }

onSubmit(): void {
if (this.loginForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.loading = true;
    this.errorMessage = '';
    const loginRequest: LoginRequest = this.loginForm.value;

    this.authService.login(loginRequest).subscribe({
      next: (response) => {
        if (response.statusCode == 200 && response.data ) {
          this.snackBar.open('تم تسجيل الدخول بنجاح', 'إغلاق', {
            duration: 3000,
            horizontalPosition: 'center',
            verticalPosition: 'top',
            panelClass: ['success-snackbar']
          });
        //  this.returnUrl
          this.router.navigate(['/']);
            } 
        this.loading = false;
      },
      error: (error) => {
//this.showError('حدث خطأ أثناء تسجيل الدخول');
        this.loading = false;
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  private showError(message: string): void {
    this.errorMessage = message;
  this.snackBar.open(message, 'إغلاق', {
    duration: 5000,
    horizontalPosition: 'center',
    verticalPosition: 'top',
     panelClass: ['error-snackbar']
 }); 
  }

  getErrorMessage(fieldName: string): string {
    const control = this.loginForm.get(fieldName);

    if (control?.hasError('required')) {
      return `${this.getFieldDisplayName(fieldName)} مطلوب`;
    }

    if (control?.hasError('minlength')) {
      const minLength = control.errors?.['minlength'].requiredLength;
      return `${this.getFieldDisplayName(fieldName)} يجب أن يكون ${minLength} أحرف على الأقل`;
    }

    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      'username': 'اسم المستخدم',
      'password': 'كلمة المرور'
    };

    return fieldNames[fieldName] || fieldName;
  }
}
