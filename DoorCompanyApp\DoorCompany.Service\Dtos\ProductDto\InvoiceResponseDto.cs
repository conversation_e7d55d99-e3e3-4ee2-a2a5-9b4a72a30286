﻿using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos.BasicDto;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos.ProductDto
{
    public class InvoiceResponseDto : BaseDto
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public InvoiceType InvoiceType { get; set; }
        public DateTime InvoiceDate { get; set; }
        public int PartyId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public string? PartyName { get; set; }
        public decimal SubTotal { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal ItemDiscountAmount { get; set; } // خصم على مستوى الأصناف
        [Column(TypeName = "decimal(18,2)")]
        public decimal InvoiceDiscountAmount { get; set; } // خصم على مستوى الفاتورة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }  //مبلغ الضريبة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }  //المبلغ الاجمالي
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } // المبلغ المدفوع
        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } //المبلغ المتبقي
        public PaymentType PaymentType { get; set; } = PaymentType.Cash;
        public bool IsPaid { get; set; } = false;
        public string? Notes { get; set; }
        public virtual List<InvoiceItemResponseDto> Items { get; set; } = new();
    }

    public class InvoiceItemResponseDto : BaseDto
    {
        public int InvoiceMasterId { get; set; }    
        public int ProductId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public string? ProductName { get; set; }
        public decimal Quantity { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountPercentage { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }
        public string? Notes { get; set; }
    }

    public class CreateInvoiceMasterDto : BaseCreateDto
    {
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        public InvoiceType InvoiceType { get; set; }
        public DateTime InvoiceDate { get; set; }
        [Required]
        public int PartyId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal ItemDiscountAmount { get; set; } // خصم على مستوى الأصناف
        [Column(TypeName = "decimal(18,2)")]
        public decimal InvoiceDiscountAmount { get; set; } // خصم على مستوى الفاتورة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }  //مبلغ الضريبة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }  //المبلغ الاجمالي
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } // المبلغ المدفوع
        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } //المبلغ المتبقي
        public PaymentType PaymentType { get; set; } = PaymentType.Cash;
        public bool IsPaid { get; set; } = false;
        public string? Notes { get; set; }
        public virtual ICollection<CreateInvoiceItemDto> Items { get; set; } = new List<CreateInvoiceItemDto>();
    }

    public class CreateInvoiceItemDto : BaseDto
    {
        public int InvoiceMasterId { get; set; }
        [Required]
        public int ProductId { get; set; }
        [Column(TypeName = "decimal(18,2)")]

        [Required]
        public decimal Quantity { get; set; }
        [Column(TypeName = "decimal(18,2)")]
       
        [Required]
        public decimal UnitPrice { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountPercentage { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateInvoiceMasterDto : BaseUpdateDto
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public InvoiceType InvoiceType { get; set; }
        public DateTime InvoiceDate { get; set; }
        public int PartyId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal ItemDiscountAmount { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal InvoiceDiscountAmount { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; }
        public PaymentType PaymentType { get; set; } = PaymentType.Cash;
        public bool IsPaid { get; set; } = false;
        public string? Notes { get; set; }
        public virtual ICollection<UpdateInvoiceItemDto> Items { get; set; } = new List<UpdateInvoiceItemDto>();
    }

    public class UpdateInvoiceItemDto : BaseUpdateDto
    {
        public int InvoiceMasterId { get; set; }
        public int ProductId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountPercentage { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }
        public string? Notes { get; set; }
    }

    public class InvoiceFilterDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? InvoiceNumber { get; set; }
        public InvoiceType? InvoiceType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? PartyId { get; set; }
        public bool? IsPaid { get; set; }
        public PaymentType? PaymentType { get; set; }
        public string? SearchTerm { get; set; }
    }

    public class PayInvoiceDto
    {
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        public PaymentType PaymentType { get; set; } = PaymentType.Cash;
        public string? Notes { get; set; }
        public DateTime PaymentDate { get; set; } = DateTime.Now;
    }

    public class InvoiceReportDto
    {
        public decimal TotalSales { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalProfit { get; set; }
        public int TotalInvoicesCount { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public decimal TotalRemainingAmount { get; set; }
        public List<InvoiceResponseDto> Invoices { get; set; } = new();
    }

    public class InvoiceReportFilterDto
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public InvoiceType? InvoiceType { get; set; }
        public int? PartyId { get; set; }
        public bool IncludeDetails { get; set; } = false;
    }

    public class CustomerAccountDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal TotalRemaining { get; set; }
        public decimal CreditLimit { get; set; }
        public List<CustomerTransactionDto> Transactions { get; set; } = new();
    }

    public class SupplierAccountDto
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public decimal TotalPurchases { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal TotalRemaining { get; set; }
        public List<SupplierTransactionDto> Transactions { get; set; } = new();
    }

    public class CustomerTransactionDto
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public string Type { get; set; } = string.Empty; // "فاتورة" أو "تحصيل"
        public string ReferenceNumber { get; set; } = string.Empty;
        public decimal Debit { get; set; } // مدين
        public decimal Credit { get; set; } // دائن
        public decimal Balance { get; set; } // الرصيد
        public string? Notes { get; set; }
    }

    public class SupplierTransactionDto
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public string Type { get; set; } = string.Empty; // "فاتورة" أو "سداد"
        public string ReferenceNumber { get; set; } = string.Empty;
        public decimal Debit { get; set; } // مدين
        public decimal Credit { get; set; } // دائن
        public decimal Balance { get; set; } // الرصيد
        public string? Notes { get; set; }
    }


}
