﻿using DoorCompany.Data.Models;
using DoorCompany.Service.Dtos.BasicDto;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DoorCompany.Service.Dtos.ProductDto
{
    public class InvoiceResponseDto : BaseDto
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public InvoiceType InvoiceType { get; set; }
        public DateTime InvoiceDate { get; set; }
        public int PartyId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public string? PartyName { get; set; }
        public decimal SubTotal { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal ItemDiscountAmount { get; set; } // خصم على مستوى الأصناف
        [Column(TypeName = "decimal(18,2)")]
        public decimal InvoiceDiscountAmount { get; set; } // خصم على مستوى الفاتورة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }  //مبلغ الضريبة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }  //المبلغ الاجمالي
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } // المبلغ المدفوع
        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } //المبلغ المتبقي
        public PaymentType PaymentType { get; set; } = PaymentType.Cash;
        public bool IsPaid { get; set; } = false;
        public string? Notes { get; set; }
        public virtual List<InvoiceItemResponseDto> Items { get; set; } = new();
    }

    public class InvoiceItemResponseDto : BaseDto
    {
        public int InvoiceMasterId { get; set; }    
        public int ProductId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public string? ProductName { get; set; }
        public decimal Quantity { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountPercentage { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }
        public string? Notes { get; set; }
    }

    public class CreateInvoiceMasterDto : BaseCreateDto
    {
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        public InvoiceType InvoiceType { get; set; }
        public DateTime InvoiceDate { get; set; }
        [Required]
        public int PartyId { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal ItemDiscountAmount { get; set; } // خصم على مستوى الأصناف
        [Column(TypeName = "decimal(18,2)")]
        public decimal InvoiceDiscountAmount { get; set; } // خصم على مستوى الفاتورة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }  //مبلغ الضريبة
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }  //المبلغ الاجمالي
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } // المبلغ المدفوع
        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } //المبلغ المتبقي
        public PaymentType PaymentType { get; set; } = PaymentType.Cash;
        public bool IsPaid { get; set; } = false;
        public string? Notes { get; set; }
        public virtual ICollection<CreateInvoiceItemDto> Items { get; set; } = new List<CreateInvoiceItemDto>();
    }

    public class CreateInvoiceItemDto : BaseDto
    {
        public int InvoiceMasterId { get; set; }
        [Required]
        public int ProductId { get; set; }
        [Column(TypeName = "decimal(18,2)")]

        [Required]
        public decimal Quantity { get; set; }
        [Column(TypeName = "decimal(18,2)")]
       
        [Required]
        public decimal UnitPrice { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountPercentage { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }
        public string? Notes { get; set; }
    }


}
