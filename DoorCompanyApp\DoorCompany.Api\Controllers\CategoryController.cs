﻿using DoorCompany.Api.Base;
using DoorCompany.Service.Dtos.ProductDto;
using DoorCompany.Service.Repositories.Interfaces.Basic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace DoorCompany.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CategoryController : AppControllerBase
    {
        public CategoryController(IUnitOfWork work) : base(work)
        {
        }
        /// <summary>
        /// Get all partners
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAllCategoryAsync()
        {
            var response = await _work.ProductRepository.GetAllCategoryAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get Category by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetCategoryById(int id)
        {
            var response = await _work.ProductRepository.GetCategoryByIdAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// create PartnerTransaction
        /// </summary>
        /// <param name="createDto"></param>
        [HttpPost]
        public async Task<IActionResult> CreateCategoryAsync([FromForm] CreateCategoryDto createDto)
        {
            if (createDto == null)
            {
                return BadRequest("لم يتم ملئ البيانات");
            }
            var response = await _work.ProductRepository.CreateCategoryAsync(createDto);
            return NewResult(response);
        }
    }
}
